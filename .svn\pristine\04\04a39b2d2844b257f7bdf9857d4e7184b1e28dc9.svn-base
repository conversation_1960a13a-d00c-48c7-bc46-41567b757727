import { Vec2 } from "cc";
import { BoolOpType, CondOPType, Res<PERSON><PERSON><PERSON>, ResSkillCondition, ResSkillConditionElem, SkillConditionType, TargetType } from "../../../../autogen/luban/schema";

import { MyApp } from "../../../../app/MyApp";

import { PlaneEventType } from "../event/PlaneEventType";
import forEachEntityByTargetType from "./SearchTarget";
import type PlaneBase from "../PlaneBase";
import { ExCondition, ExConditionEvent, ExConditionNum } from "./ExCondition";
import { GameIns } from "../../../GameIns";
import { genEntityID } from "../../base/EntityID";

export class Buff {
    id: number;
    res: ResBuffer
    removeConditionRes: ResSkillCondition|undefined = undefined
    removeConditionElems: ResSkillConditionElem[]|null = null
    forbinConditionRes: ResSkillCondition|undefined = undefined
    forbinConditionElems: ResSkillConditionElem[]|null = null
    triggerConditionRes: ResSkillCondition|undefined = undefined
    triggerConditionElems: ResSkillConditionElem[]|null = null
    time = 0;
    cycleTimes = 0;
    isOutside: boolean;
    forbin:boolean = false;
    stack:number = 0;
    self:PlaneBase;
    constructor(isOutside: boolean, data: ResBuffer, self:PlaneBase) {
        this.id = genEntityID();
        this.res = data;
        this.isOutside = isOutside;
        this.self = self
        if (this.res.removeCondition) {
            this.removeConditionRes = MyApp.lubanTables.TbResSkillCondition.get(this.res.removeCondition);
            this.removeConditionElems = this.conditionRes2Elems(this.removeConditionRes);
        }
        if (this.res.forbinCondition) {
            this.forbinConditionRes = MyApp.lubanTables.TbResSkillCondition.get(this.res.forbinCondition);
            this.forbinConditionElems = this.conditionRes2Elems(this.forbinConditionRes);
        }
        if (this.res.triggerCondition) {
            this.triggerConditionRes = MyApp.lubanTables.TbResSkillCondition.get(this.res.triggerCondition);
            this.triggerConditionElems = this.conditionRes2Elems(this.triggerConditionRes);
        }
    }

    conditionRes2Elems(res: ResSkillCondition|undefined):ResSkillConditionElem[]|null {
        if (!res) {
            return null;
        }
        let target: PlaneBase | null = null;
        forEachEntityByTargetType(this.self, res.target, (entity:PlaneBase) => {
            target = entity;
        });
        if (!target) {
            return null;
        }
        const validTarget: PlaneBase = target;

        const elems = Array.from(res.conditions);
        for (let i = 0; i < elems.length; i++) {
            switch (elems[i].type) {
                case SkillConditionType.FatalInjuryHurt:
                    {
                        let elem = new ExConditionEvent(elems[i], () => {
                            elem.has = true;
                            this.update(0)
                        });
                        elems[i] = elem
                        // NOTE FatalInjuryHurt 只支持self， 要不Unregister不好删除
                        this.self.PlaneEventRegister(PlaneEventType.FatalInjuryHurt, elem.cb)
                    } 
                    break;
                case SkillConditionType.PickDiamond:
                    elems[i] = new ExConditionNum(elems[i], validTarget.pickDiamondNum);
                    break;
                case SkillConditionType.KillEnemyNum:
                    elems[i] = new ExConditionNum(elems[i], validTarget.killEnemyNum);
                    break;
                case SkillConditionType.UseNuclear:
                    elems[i] = new ExConditionNum(elems[i], validTarget.usedNuclearNum);
                    break;
                case SkillConditionType.UserSuper:
                    elems[i] = new ExConditionNum(elems[i], validTarget.usedSuperNum);
                    break;
            }
        }
        return elems;
    }

    static checkCondition(self: PlaneBase, res: ResSkillCondition, elems: ResSkillConditionElem[]|null) {
        let target: PlaneBase | null = null;
        forEachEntityByTargetType(self, res.target, (entity) => {
            target = entity;
        });
        if (!target) {
            return false
        }
        if (res.conditions.length == 0) {
            return true;
        }
        let ret = res.boolType == BoolOpType.AND ? true : false;
        elems?.forEach((condition) => {
            let value = 0;
            switch(condition.type) {
                case SkillConditionType.BuffStack:
                    {
                        if (condition.params.length < 1) {
                            break
                        }
                        value = target!.buffComp!.GetBuff(condition.params[0])?.stack || 0;
                    }
                    break
                case SkillConditionType.CurHPPer:
                    value = target!.curHp / target!.maxHp * 10000;
                    break;
                case SkillConditionType.CurHP:
                    value = target!.curHp;
                    break;
                case SkillConditionType.MaxHP:
                    value = target!.maxHp;
                    break;
                case SkillConditionType.BeAttackTime:
                    value = GameIns.gameDataManager.gameTime - target!.hurtTime;
                    break;
                case SkillConditionType.FatalInjuryHurt:
                    if (condition instanceof ExConditionEvent) {
                        value = condition.has?1:0;
                    }
                    break;
                case SkillConditionType.PickDiamond:
                    if (condition instanceof ExConditionNum) {
                        value = target!.pickDiamondNum - condition.num;
                    }
                case SkillConditionType.KillEnemyNum:
                case SkillConditionType.KillEnemy:
                    if (condition instanceof ExConditionNum) {
                        value = target!.killEnemyNum - condition.num;
                    }
                    break;
                case SkillConditionType.RemainNuclearNum:
                    value = target!.nuclearNum;
                    break;
                case SkillConditionType.UsedNuclearNum:
                    value = target!.usedNuclearNum;
                    break;
                case SkillConditionType.LevelStart:
                    value = GameIns.gameDataManager.gameTime == 0 ? 1 : 0;
                    break;
                case SkillConditionType.BossBeKilled:
                    // TODO ybgg
                    break;
                case SkillConditionType.UseNuclear:
                    if (condition instanceof ExConditionNum) {
                        value = target!.usedNuclearNum - condition.num;
                    }
                case SkillConditionType.UserSuper:
                    if (condition instanceof ExConditionNum) {
                        value = target!.usedSuperNum - condition.num;
                    }
                    break;
                case SkillConditionType.GameTime:
                    value = GameIns.gameDataManager.gameTime;
                    break;
                case SkillConditionType.EnemyCount:
                    if (condition.params.length >= 2) {
                        const radiusSqr = condition.params[1]*condition.params[1];
                        if (target?.enemy) {
                            if (Vec2.squaredDistance(target!.node.position, GameIns.mainPlaneManager.mainPlane!.node.position) <= radiusSqr) {
                                value++;
                            }
                        } else {
                            forEachEntityByTargetType(target!, TargetType.Enemy, (entity) => {
                                if (entity.isDead && Vec2.squaredDistance(entity.node.position, target!.node.position) <= radiusSqr) {
                                    value++;
                                }
                            });
                        }
                    }
                    break;
                case SkillConditionType.WaveNo:
                    // TODO ybgg
                    // value = GameIns.battleManager.waveNo;
                    break;
                case SkillConditionType.Distance:
                    value = Vec2.distance(target!.node.position, GameIns.mainPlaneManager.mainPlane!.node.position);
                    break;
                case SkillConditionType.KillEnemy:
                    if (condition instanceof ExConditionNum) {
                        value = target!.killEnemyNum - condition.num;
                    }
                    break;

            }
            let ret2 = false;
            switch(condition.op) {
                case CondOPType.EQ:
                    ret2 = value == condition.value;
                    break;
                case CondOPType.GE:
                    ret2 = value >= condition.value;
                    break;
                case CondOPType.GT:
                    ret2 = value > condition.value;
                    break;
                case CondOPType.LE:
                    ret2 = value <= condition.value;
                    break;
                case CondOPType.LT:
                    ret2 = value < condition.value;
                    break;
                case CondOPType.NE:
                    ret2 = value != condition.value;
                    break;
            }
            if (res.boolType == BoolOpType.AND) {
                ret = ret && ret2;
            } else {
                ret = ret || ret2;
            }
        })
        
        return ret;
    }

    checkRemoveCondition() {
        if (this.res.duration != -1 && this.time >= this.res.duration) {
            return true;
        }
        if (!this.removeConditionRes) {
            return false;
        }
        return Buff.checkCondition(this.self, this.removeConditionRes, this.removeConditionElems);
    }
    resetTriggerConditionNum() {
        this.triggerConditionElems?.forEach((condition) => {
            if (condition instanceof ExCondition) {
                condition.reset()
            }
        })
    }
    resetConditionElems(elems: ResSkillConditionElem[]|null) {
        elems?.forEach((condition) => {
            switch(condition.type) {
                case SkillConditionType.FatalInjuryHurt:
                    if (condition instanceof ExConditionEvent) {
                        this.self.PlaneEventUnRegister(PlaneEventType.FatalInjuryHurt, condition.cb)
                    }
                    break;
            }
        })
    }
    onRemove() {
        this.res.effects.forEach((applyEffect) => {
            forEachEntityByTargetType(this.self, applyEffect.target, (entity) => {
                entity.RemoveBuffEffect(this, applyEffect);
            })
        })
        this.resetConditionElems(this.removeConditionElems)
    }
    update(dt:number):void {
        this.time += dt * 1000;
        if (this.checkRemoveCondition()) {
            this.self.buffComp.removeBuff(this, this.res.id);
            return
        }
        if (this.forbinConditionRes && Buff.checkCondition(this.self, this.forbinConditionRes, this.forbinConditionElems)) {
            if (!this.forbin) {
                this.res.effects.forEach((applyEffect) => {
                    forEachEntityByTargetType(this.self, applyEffect.target, (entity) => {
                        entity.RemoveBuffEffect(this, applyEffect);
                    })
                })
            }
            this.forbin = true;
        } else {
            if (this.forbin) {
                this.applyEffect()
            }
            this.forbin = false;
        }
        
        if ((this.res.cycle > 0 &&
                this.time >= (this.cycleTimes + 1) * this.res.cycle &&
                (this.res.cycleTimes == 0 || this.cycleTimes < this.res.cycleTimes))
            || (this.triggerConditionElems && Buff.checkCondition(this.self, this.triggerConditionRes!, this.triggerConditionElems)
            )
        ) {
            this.cycleTimes++;
            this.resetTriggerConditionNum();
            this.applyEffect()
        }
    }
    private applyEffect() {
        this.res.effects.forEach((applyEffect) => {
            forEachEntityByTargetType(this.self, applyEffect.target, (entity) => {
                entity.ApplyBuffEffect(this, applyEffect);
            })
        })
    }
}
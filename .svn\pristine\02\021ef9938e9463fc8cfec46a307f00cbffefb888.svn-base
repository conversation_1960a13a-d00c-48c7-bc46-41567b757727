import { EnemyEventGroupContext } from "../EventGroupCom";
import { EventConditionBase, Comparer } from "db://assets/bundles/common/script/game/eventgroup/IEventCondition";
import { AttributeConst } from "db://assets/bundles/common/script/const/AttributeConst";
import { Vec2, misc } from "cc";
const { degreesToRadians, radiansToDegrees } = misc;

export class EnemyEventConditionBase extends EventConditionBase<EnemyEventGroupContext> {
}

export class EnemyCondition_ElapsedTime extends EnemyEventConditionBase {
    public evaluate(context: EnemyEventGroupContext): boolean {
        if (!context.plane) {
            return false;
        }

        return Comparer.compare(context.plane.lifeTimeMilliseconds, this._targetValue, this.data.compareOp);
    }
}

export class EnemyCondition_Pos_X extends EnemyEventConditionBase {
    public evaluate(context: EnemyEventGroupContext): boolean {
        if (!context.plane) {
            return false;
        }

        return Comparer.compare(context.plane.node.position.x, this._targetValue, this.data.compareOp);
    }
}

export class EnemyCondition_Pos_Y extends EnemyEventConditionBase {
    public evaluate(context: EnemyEventGroupContext): boolean {
        if (!context.plane) {
            return false;
        }

        return Comparer.compare(context.plane.node.position.y, this._targetValue, this.data.compareOp);
    }
}

export class EnemyCondition_CurHP extends EnemyEventConditionBase {
    public evaluate(context: EnemyEventGroupContext): boolean {
        if (!context.plane) {
            return false;
        }

        return Comparer.compare(context.plane.curHp, this._targetValue, this.data.compareOp);
    }
}

export class EnemyCondition_CurHPPercent extends EnemyEventConditionBase {
    public evaluate(context: EnemyEventGroupContext): boolean {
        if (!context.plane) {
            return false;
        }

        // 万分比
        const hpPercent = context.plane.curHp / context.plane.maxHp * 10000;
        return Comparer.compare(hpPercent, this._targetValue, this.data.compareOp);
    }
}

export class EnemyCondition_Speed extends EnemyEventConditionBase {
    public evaluate(context: EnemyEventGroupContext): boolean {
        if (!context.plane) {
            return false;
        }

        const speed = context.plane.moveCom?.speed || 0;
        return Comparer.compare(speed, this._targetValue, this.data.compareOp);
    }
}

export class EnemyCondition_SpeedAngle extends EnemyEventConditionBase {
    public evaluate(context: EnemyEventGroupContext): boolean {
        if (!context.plane) {
            return false;
        }

        const speedAngle = context.plane.speedAngle || 0;
        return Comparer.compare(speedAngle, this._targetValue, this.data.compareOp);
    }
}

export class EnemyCondition_Acceleration extends EnemyEventConditionBase {
    public evaluate(context: EnemyEventGroupContext): boolean {
        if (!context.plane) {
            return false;
        }

        const acceleration = context.plane.acceleration || 0;
        return Comparer.compare(acceleration, this._targetValue, this.data.compareOp);
    }
}

export class EnemyCondition_AccelerationAngle extends EnemyEventConditionBase {
    public evaluate(context: EnemyEventGroupContext): boolean {
        if (!context.plane) {
            return false;
        }

        const accelerationAngle = context.plane.accelerationAngle || 0;
        return Comparer.compare(accelerationAngle, this._targetValue, this.data.compareOp);
    }
}

export class EnemyCondition_DistanceToPlayer extends EnemyEventConditionBase {
    public evaluate(context: EnemyEventGroupContext): boolean {
        if (!context.plane || !context.playerPlane) {
            return false;
        }

        const positionA = context.plane.node.position;
        const positionB = context.playerPlane.node.position;
        const distance = Vec2.distance(positionA, positionB);
        return Comparer.compare(distance, this._targetValue, this.data.compareOp);
    }
}

export class EnemyCondition_AngleToPlayer extends EnemyEventConditionBase {
    public evaluate(context: EnemyEventGroupContext): boolean {
        if (!context.plane || !context.playerPlane) {
            return false;
        }

        const positionA = context.plane.node.position;
        const positionB = context.playerPlane.node.position;
        const angle = radiansToDegrees(Vec2.angle(positionA, positionB));
        return Comparer.compare(angle, this._targetValue, this.data.compareOp);
    }
}

export class EnemyCondition_Player_Pos_X extends EnemyEventConditionBase {
    public evaluate(context: EnemyEventGroupContext): boolean {
        if (!context.playerPlane) {
            return false;
        }

        return Comparer.compare(context.playerPlane.node.position.x, this._targetValue, this.data.compareOp);
    }
}

export class EnemyCondition_Player_Pos_Y extends EnemyEventConditionBase {
    public evaluate(context: EnemyEventGroupContext): boolean {
        if (!context.playerPlane) {
            return false;
        }

        return Comparer.compare(context.playerPlane.node.position.y, this._targetValue, this.data.compareOp);
    }
}

export class EnemyCondition_Player_CurHPPercent extends EnemyEventConditionBase {
    public evaluate(context: EnemyEventGroupContext): boolean {
        if (!context.playerPlane) {
            return false;
        }

        // 万分比
        const hpPercent = context.playerPlane.curHp / context.playerPlane.maxHp * 10000;
        return Comparer.compare(hpPercent, this._targetValue, this.data.compareOp);
    }
}

export class EnemyCondition_Level_ElapsedTime extends EnemyEventConditionBase {
    public evaluate(context: EnemyEventGroupContext): boolean {
        // return Comparer.compare(context.level!.levelElapsedTime, this._targetValue, this.data.compareOp);
        return false;
    }
}

export class EnemyCondition_Level_InfLevel extends EnemyEventConditionBase {
    public evaluate(context: EnemyEventGroupContext): boolean {
        // return Comparer.compare(context.level!.infLevel, this._targetValue, this.data.compareOp);
        return false;
    }
}

export class EnemyCondition_Level_ChallengeLevelSection extends EnemyEventConditionBase {
    public evaluate(context: EnemyEventGroupContext): boolean {
        // return Comparer.compare(context.level!.challengeLevelSection, this._targetValue, this.data.compareOp);
        return false;
    }
}

export class EnemyCondition_SelectLevel extends EnemyEventConditionBase {
    public evaluate(context: EnemyEventGroupContext): boolean {
        if (!context.plane) {
            return false;
        }

        return false;
        // return Comparer.compare(context.plane.enemyData.selectLevel, this._targetValue, this.data.compareOp);
    }
}

export class EnemyCondition_ImmuneBulletDamage extends EnemyEventConditionBase {
    public evaluate(context: EnemyEventGroupContext): boolean {
        if (!context.plane) {
            return false;
        }

        return Comparer.compareBoolean(context.plane.immuneBulletDamage, this._targetValue === 1, this.data.compareOp);
    }
}

export class EnemyCondition_ImmuneCollideDamage extends EnemyEventConditionBase {
    public evaluate(context: EnemyEventGroupContext): boolean {
        if (!context.plane) {
            return false;
        }

        return Comparer.compareBoolean(context.plane.immuneCollideDamage, this._targetValue === 1, this.data.compareOp);
    }
}

export class EnemyCondition_IgnoreBullet extends EnemyEventConditionBase {
    public evaluate(context: EnemyEventGroupContext): boolean {
        if (!context.plane) {
            return false;
        }

        return Comparer.compareBoolean(context.plane.ignoreBullet, this._targetValue === 1, this.data.compareOp);
    }
}

export class EnemyCondition_IgnoreCollide extends EnemyEventConditionBase {
    public evaluate(context: EnemyEventGroupContext): boolean {
        if (!context.plane) {
            return false;
        }

        return Comparer.compareBoolean(context.plane.ignoreCollide, this._targetValue === 1, this.data.compareOp);
    }
}

export class EnemyCondition_ImmuneNuke extends EnemyEventConditionBase {
    public evaluate(context: EnemyEventGroupContext): boolean {
        if (!context.plane) {
            return false;
        }

        return Comparer.compareBoolean(context.plane.immuneNuke, this._targetValue === 1, this.data.compareOp);
    }
}

export class EnemyCondition_ImmuneActiveSkill extends EnemyEventConditionBase {
    public evaluate(context: EnemyEventGroupContext): boolean {
        if (!context.plane) {
            return false;
        }

        return Comparer.compareBoolean(context.plane.immuneActiveSkill, this._targetValue === 1, this.data.compareOp);
    }
}

export class EnemyCondition_Invincible extends EnemyEventConditionBase {
    public evaluate(context: EnemyEventGroupContext): boolean {
        if (!context.plane) {
            return false;
        }

        return Comparer.compareBoolean(context.plane.invincible, this._targetValue === 1, this.data.compareOp);
    }
}
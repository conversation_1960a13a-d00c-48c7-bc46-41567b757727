{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/utils/UITools.ts"], "names": ["UIToolMgr", "Node", "Sprite", "SpriteFrame", "MyApp", "QualityType", "BundleName", "StateSprite", "formatTime", "totalSeconds", "isChineseFormat", "hours", "Math", "floor", "minutes", "seconds", "pad", "num", "toString", "padStart", "formatDate", "date", "Date", "year", "getFullYear", "month", "getMonth", "day", "getDate", "getHours", "getMinutes", "modifyColorTag", "passText", "index", "options", "string", "originalString", "parts", "split", "filteredParts", "filter", "part", "length", "Error", "tagIndex", "valueIndex", "color", "value", "join", "modifyNumber", "txt", "num2", "undefined", "replacement", "match", "replace", "replacement1", "replacement2", "regex", "modifyStateSprite", "target", "idx", "stateSprite", "sprite", "getComponent", "setState", "setAvatarBg", "sp", "avatarbg", "resMgr", "loadAsync", "Common", "then", "frame", "spriteFrame", "setItemQuality", "quality", "COMMON", "MYTHIC", "qualityPng", "UNCOMMON", "RACE", "EPIC", "LEGENDARY", "UITools"], "mappings": ";;;uJAMMA,S;;;;;;;;;;;;;;;;;;;;;;;;;AANUC,MAAAA,I,OAAAA,I;AAAgBC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;;AAC/BC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,W,iBAAAA,W;;;;;;;;;AAEHP,MAAAA,S,GAAN,MAAMA,SAAN,CAAgB;AACZ;AACJ;AACA;AACA;AACA;AACIQ,QAAAA,UAAU,CAACC,YAAD,EAAuBC,eAAwB,GAAG,KAAlD,EAAiE;AACvE,cAAI,OAAOD,YAAP,KAAwB,QAAxB,IAAoCA,YAAY,GAAG,CAAvD,EAA0D;AACtD,mBAAOC,eAAe,GAAG,QAAH,GAAc,UAApC;AACH;;AACD,gBAAMC,KAAK,GAAGC,IAAI,CAACC,KAAL,CAAWJ,YAAY,GAAG,IAA1B,CAAd;AACA,gBAAMK,OAAO,GAAGF,IAAI,CAACC,KAAL,CAAYJ,YAAY,GAAG,IAAhB,GAAwB,EAAnC,CAAhB;AACA,gBAAMM,OAAO,GAAGN,YAAY,GAAG,EAA/B;;AACA,gBAAMO,GAAG,GAAIC,GAAD,IAAiBA,GAAG,CAACC,QAAJ,GAAeC,QAAf,CAAwB,CAAxB,EAA2B,GAA3B,CAA7B;;AACA,cAAIT,eAAJ,EAAqB;AACjB,mBAAQ,GAAEC,KAAM,IAAGK,GAAG,CAACF,OAAD,CAAU,IAAGE,GAAG,CAACD,OAAD,CAAU,GAAhD;AACH,WAFD,MAEO;AACH,mBAAQ,GAAEC,GAAG,CAACL,KAAD,CAAQ,IAAGK,GAAG,CAACF,OAAD,CAAU,IAAGE,GAAG,CAACD,OAAD,CAAU,EAArD;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIK,QAAAA,UAAU,CAACX,YAAD,EAA+B;AACrC,gBAAMY,IAAI,GAAG,IAAIC,IAAJ,CAASb,YAAY,GAAG,IAAxB,CAAb;AACA,gBAAMc,IAAI,GAAGF,IAAI,CAACG,WAAL,EAAb;AACA,gBAAMC,KAAK,GAAG,CAACJ,IAAI,CAACK,QAAL,KAAkB,CAAnB,EAAsBR,QAAtB,GAAiCC,QAAjC,CAA0C,CAA1C,EAA6C,GAA7C,CAAd;AACA,gBAAMQ,GAAG,GAAGN,IAAI,CAACO,OAAL,GAAeV,QAAf,GAA0BC,QAA1B,CAAmC,CAAnC,EAAsC,GAAtC,CAAZ;AACA,gBAAMR,KAAK,GAAGU,IAAI,CAACQ,QAAL,GAAgBX,QAAhB,GAA2BC,QAA3B,CAAoC,CAApC,EAAuC,GAAvC,CAAd;AACA,gBAAML,OAAO,GAAGO,IAAI,CAACS,UAAL,GAAkBZ,QAAlB,GAA6BC,QAA7B,CAAsC,CAAtC,EAAyC,GAAzC,CAAhB;AACA,iBAAQ,GAAEI,IAAK,IAAGE,KAAM,IAAGE,GAAI,IAAGhB,KAAM,IAAGG,OAAQ,EAAnD;AACH;AAID;AACJ;AACA;AACA;AACA;AACA;;;AACIiB,QAAAA,cAAc,CAACC,QAAD,EAAqBC,KAArB,EAAoCC,OAApC,EAAiF;AAC3F,cAAIF,QAAQ,IAAI,IAAZ,IAAoBA,QAAQ,CAACG,MAAT,IAAmB,IAA3C,EAAiD;AACjD,gBAAMC,cAAc,GAAGJ,QAAQ,CAACG,MAAhC;AACA,gBAAME,KAAK,GAAGD,cAAc,CAACE,KAAf,CAAqB,oCAArB,CAAd;AACA,gBAAMC,aAAa,GAAGF,KAAK,CAACG,MAAN,CAAaC,IAAI,IAAIA,IAAI,KAAK,EAA9B,CAAtB;;AACA,cAAIR,KAAK,GAAG,CAAR,IAAaA,KAAK,IAAIM,aAAa,CAACG,MAAd,GAAuB,CAAjD,EAAoD;AAChD,kBAAM,IAAIC,KAAJ,CAAW,kBAAiBV,KAAM,EAAlC,CAAN;AACH;;AACD,gBAAMW,QAAQ,GAAG,IAAIX,KAArB;AACA,gBAAMY,UAAU,GAAGD,QAAQ,GAAG,CAA9B;;AACA,cAAIV,OAAO,CAACY,KAAZ,EAAmB;AACfP,YAAAA,aAAa,CAACK,QAAD,CAAb,GAA2B,UAASV,OAAO,CAACY,KAAM,GAAlD;AACH;;AACD,cAAIZ,OAAO,CAACa,KAAZ,EAAmB;AACfR,YAAAA,aAAa,CAACM,UAAD,CAAb,GAA4BX,OAAO,CAACa,KAApC;AACH;;AACDf,UAAAA,QAAQ,CAACG,MAAT,GAAkBI,aAAa,CAACS,IAAd,CAAmB,EAAnB,CAAlB;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIC,QAAAA,YAAY,CACRC,GADQ,EAERjC,GAFQ,EAGRkC,IAHQ,EAIJ;AACJ,cAAI,CAACD,GAAD,IAAQjC,GAAG,KAAK,IAAhB,IAAwBA,GAAG,KAAKmC,SAApC,EAA+C;;AAC/C,cAAID,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAKC,SAA9B,EAAyC;AACrC,kBAAMC,WAAW,GAAGpC,GAAG,CAACC,QAAJ,EAApB;;AACA,gBAAIgC,GAAG,CAACf,MAAJ,CAAWmB,KAAX,CAAiB,MAAjB,CAAJ,EAA8B;AAC1BJ,cAAAA,GAAG,CAACf,MAAJ,GAAae,GAAG,CAACf,MAAJ,CAAWoB,OAAX,CAAmB,MAAnB,EAA2BF,WAA3B,CAAb;AACH;AACJ,WALD,MAMK;AACD,kBAAMG,YAAY,GAAGvC,GAAG,CAACC,QAAJ,EAArB;AACA,kBAAMuC,YAAY,GAAGN,IAAI,CAACjC,QAAL,EAArB;AACA,kBAAMwC,KAAK,GAAG,eAAd;;AACA,gBAAIR,GAAG,CAACf,MAAJ,CAAWmB,KAAX,CAAiBI,KAAjB,CAAJ,EAA6B;AACzBR,cAAAA,GAAG,CAACf,MAAJ,GAAae,GAAG,CAACf,MAAJ,CAAWoB,OAAX,CAAmBG,KAAnB,EAA2B,GAAEF,YAAa,IAAGC,YAAa,EAA1D,CAAb;AACH;AACJ;AACJ;AAGD;AACJ;AACA;AACA;AACA;;;AACIE,QAAAA,iBAAiB,CAACC,MAAD,EAAwBC,GAAxB,EAAqC;AAClD,cAAID,MAAM,IAAI,IAAd,EAAoB;AACpB,cAAIE,WAA+B,GAAG,IAAtC;AACA,cAAIC,MAAqB,GAAG,IAA5B;;AACA,cAAIH,MAAM,YAAY3D,IAAtB,EAA4B;AACxB6D,YAAAA,WAAW,GAAGF,MAAM,CAACI,YAAP;AAAA;AAAA,2CAAd;AACAD,YAAAA,MAAM,GAAGH,MAAM,CAACI,YAAP,CAAoB9D,MAApB,CAAT;AACH,WAHD,MAGO,IAAI0D,MAAM,YAAY1D,MAAtB,EAA8B;AACjC4D,YAAAA,WAAW,GAAGF,MAAM,CAACI,YAAP;AAAA;AAAA,2CAAd;AACAD,YAAAA,MAAM,GAAGH,MAAT;AACH;;AACD,cAAIE,WAAW,IAAI,IAAf,IAAuBC,MAAM,IAAI,IAArC,EAA2C;AAC3CD,UAAAA,WAAW,CAACG,QAAZ,CAAqBF,MAArB,EAA6BF,GAA7B;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIK,QAAAA,WAAW,CAACC,EAAD,EAAaC,QAAb,EAA+B;AACtC,cAAID,EAAE,IAAI,IAAV,EAAgB;AAChB;AAAA;AAAA,8BAAME,MAAN,CAAaC,SAAb,CAAuB;AAAA;AAAA,wCAAWC,MAAlC,EAA2C,oBAAmBH,QAAS,cAAvE,EAAsFjE,WAAtF,EAAmGqE,IAAnG,CAAyGC,KAAD,IAAwB;AAC5HN,YAAAA,EAAE,CAACO,WAAH,GAAiBD,KAAjB;AACH,WAFD;AAGH;AAED;AACJ;AACA;AACA;AACA;;;AACIE,QAAAA,cAAc,CAACR,EAAD,EAAaS,OAAb,EAAmC;AAC7C,cAAIT,EAAE,IAAI,IAAV,EAAgB;;AAChB,cAAIS,OAAO,IAAI;AAAA;AAAA,0CAAYC,MAAvB,IAAiCD,OAAO,IAAI;AAAA;AAAA,0CAAYE,MAA5D,EAAoE;AAChE,gBAAIC,UAAU,GAAG,EAAjB;;AACA,gBAAIH,OAAO,IAAI;AAAA;AAAA,4CAAYC,MAA3B,EAAmC;AAC/BE,cAAAA,UAAU,GAAG,SAAb;AACH,aAFD,MAEO,IAAIH,OAAO,IAAI;AAAA;AAAA,4CAAYI,QAA3B,EAAqC;AACxCD,cAAAA,UAAU,GAAG,UAAb;AACH,aAFM,MAEA,IAAIH,OAAO,IAAI;AAAA;AAAA,4CAAYK,IAA3B,EAAiC;AACpCF,cAAAA,UAAU,GAAG,SAAb;AACH,aAFM,MAEA,IAAIH,OAAO,IAAI;AAAA;AAAA,4CAAYM,IAA3B,EAAiC;AACpCH,cAAAA,UAAU,GAAG,WAAb;AACH,aAFM,MAEA,IAAIH,OAAO,IAAI;AAAA;AAAA,4CAAYO,SAA3B,EAAsC;AACzCJ,cAAAA,UAAU,GAAG,WAAb;AACH,aAFM,MAEA,IAAIH,OAAO,IAAI;AAAA;AAAA,4CAAYE,MAA3B,EAAmC;AACtCC,cAAAA,UAAU,GAAG,QAAb;AACH;;AACD;AAAA;AAAA,gCAAMV,MAAN,CAAaC,SAAb,CAAuB;AAAA;AAAA,0CAAWC,MAAlC,EAA2C,gBAAeQ,UAAW,cAArE,EAAoF5E,WAApF,EAAiGqE,IAAjG,CAAuGC,KAAD,IAAwB;AAC1HN,cAAAA,EAAE,CAACO,WAAH,GAAiBD,KAAjB;AACH,aAFD;AAGH;AACJ;;AAtJW,O;;yBAyJHW,O,GAAU,IAAIpF,SAAJ,E", "sourcesContent": ["import { Label, Node, RichText, Sprite, SpriteFrame } from 'cc';\r\nimport { MyApp } from '../../app/MyApp';\r\nimport { QualityType } from '../../autogen/luban/schema';\r\nimport { BundleName } from '../../const/BundleConst';\r\nimport { StateSprite } from '../../ui/common/components/base/StateSprite';\r\n\r\nclass UIToolMgr {\r\n    /**\r\n     * 将时间戳格式化为 \"时:分:秒\" 或 \"0时0分0秒\" 的字符串\r\n     * @param totalSeconds 时间戳（秒）\r\n     * @param isChineseFormat 是否使用中文格式（默认 false）\r\n     */\r\n    formatTime(totalSeconds: number, isChineseFormat: boolean = false): string {\r\n        if (typeof totalSeconds !== 'number' || totalSeconds < 0) {\r\n            return isChineseFormat ? '0时0分0秒' : '00:00:00';\r\n        }\r\n        const hours = Math.floor(totalSeconds / 3600);\r\n        const minutes = Math.floor((totalSeconds % 3600) / 60);\r\n        const seconds = totalSeconds % 60;\r\n        const pad = (num: number) => num.toString().padStart(2, '0');\r\n        if (isChineseFormat) {\r\n            return `${hours}时${pad(minutes)}分${pad(seconds)}秒`;\r\n        } else {\r\n            return `${pad(hours)}:${pad(minutes)}:${pad(seconds)}`;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 将时间戳格式化为 \"年/月/日 时:分\" 的字符串\r\n     * @param txt 目标 Label 组件\r\n     * @param totalSeconds 时间戳（秒）\r\n     */\r\n    formatDate(totalSeconds: number): string {\r\n        const date = new Date(totalSeconds * 1000);\r\n        const year = date.getFullYear();\r\n        const month = (date.getMonth() + 1).toString().padStart(2, '0');\r\n        const day = date.getDate().toString().padStart(2, '0');\r\n        const hours = date.getHours().toString().padStart(2, '0');\r\n        const minutes = date.getMinutes().toString().padStart(2, '0');\r\n        return `${year}/${month}/${day} ${hours}:${minutes}`;\r\n    }\r\n\r\n\r\n\r\n    /**\r\n     * 修改富文本原字符串中指定 <color> 标签内的数值\r\n     * @param passText RichText\r\n     * @param index 要修改的 <color> 标签的索引（从 0 开始）\r\n     * @param newValue 新的数值\r\n     */\r\n    modifyColorTag(passText: RichText, index: number, options: { value?: string; color?: string }) {\r\n        if (passText == null || passText.string == null) return;\r\n        const originalString = passText.string;\r\n        const parts = originalString.split(/(<color=[^>]+>)([^<]+)(<\\/color>)/g);\r\n        const filteredParts = parts.filter(part => part !== '');\r\n        if (index < 0 || index >= filteredParts.length / 3) {\r\n            throw new Error(`Invalid index: ${index}`);\r\n        }\r\n        const tagIndex = 3 * index;\r\n        const valueIndex = tagIndex + 1;\r\n        if (options.color) {\r\n            filteredParts[tagIndex] = `<color=${options.color}>`;\r\n        }\r\n        if (options.value) {\r\n            filteredParts[valueIndex] = options.value;\r\n        }\r\n        passText.string = filteredParts.join('');\r\n    }\r\n\r\n    /**\r\n     * 修改 Label 文本中的数字部分\r\n     * @param txt 目标 Label 组件\r\n     * @param num 替换的数字（支持 number 或 string 类型）\r\n     * @param num2 可选，第二个替换的数字（用于替换 \"数字/数字\" 格式）\r\n     */\r\n    modifyNumber(\r\n        txt: Label | null,\r\n        num: number | string | null,\r\n        num2?: number | string | null\r\n    ): void {\r\n        if (!txt || num === null || num === undefined) return;\r\n        if (num2 === null || num2 === undefined) {\r\n            const replacement = num.toString();\r\n            if (txt.string.match(/\\d+/g)) {\r\n                txt.string = txt.string.replace(/\\d+/g, replacement);\r\n            }\r\n        }\r\n        else {\r\n            const replacement1 = num.toString();\r\n            const replacement2 = num2.toString();\r\n            const regex = /(\\d+)\\/(\\d+)/g;\r\n            if (txt.string.match(regex)) {\r\n                txt.string = txt.string.replace(regex, `${replacement1}/${replacement2}`);\r\n            }\r\n        }\r\n    }\r\n\r\n\r\n    /**\r\n     * 修改状态精灵\r\n     * @param target 目标节点或精灵\r\n     * @param idx 状态索引\r\n     */\r\n    modifyStateSprite(target: Node | Sprite, idx: number) {\r\n        if (target == null) return;\r\n        let stateSprite: StateSprite | null = null;\r\n        let sprite: Sprite | null = null;\r\n        if (target instanceof Node) {\r\n            stateSprite = target.getComponent(StateSprite);\r\n            sprite = target.getComponent(Sprite);\r\n        } else if (target instanceof Sprite) {\r\n            stateSprite = target.getComponent(StateSprite);\r\n            sprite = target;\r\n        }\r\n        if (stateSprite == null || sprite == null) return;\r\n        stateSprite.setState(sprite, idx);\r\n    }\r\n\r\n    /**\r\n     * 设置头像\r\n     * @param sp 目标精灵\r\n     * @param avatarbg 头像框\r\n     */\r\n    setAvatarBg(sp: Sprite, avatarbg: string) {\r\n        if (sp == null) return;\r\n        MyApp.resMgr.loadAsync(BundleName.Common, `texture/avatarbg/${avatarbg}/spriteFrame`, SpriteFrame).then((frame: SpriteFrame) => {\r\n            sp.spriteFrame = frame;\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 设置物品品质\r\n     * @param sp 目标精灵\r\n     * @param quality 品质\r\n     */\r\n    setItemQuality(sp: Sprite, quality: QualityType) {\r\n        if (sp == null) return;\r\n        if (quality >= QualityType.COMMON && quality <= QualityType.MYTHIC) {\r\n            let qualityPng = \"\";\r\n            if (quality == QualityType.COMMON) {\r\n                qualityPng = \"grey_bg\"\r\n            } else if (quality == QualityType.UNCOMMON) {\r\n                qualityPng = \"green_bg\"\r\n            } else if (quality == QualityType.RACE) {\r\n                qualityPng = \"blue_bg\"\r\n            } else if (quality == QualityType.EPIC) {\r\n                qualityPng = \"purple_bg\"\r\n            } else if (quality == QualityType.LEGENDARY) {\r\n                qualityPng = \"golden_bg\"\r\n            } else if (quality == QualityType.MYTHIC) {\r\n                qualityPng = \"red_bg\"\r\n            }\r\n            MyApp.resMgr.loadAsync(BundleName.Common, `texture/item/${qualityPng}/spriteFrame`, SpriteFrame).then((frame: SpriteFrame) => {\r\n                sp.spriteFrame = frame;\r\n            });\r\n        }\r\n    }\r\n\r\n}\r\nexport const UITools = new UIToolMgr();"]}
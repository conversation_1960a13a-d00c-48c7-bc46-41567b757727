{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKBuyUI.ts"], "names": ["_decorator", "Label", "Node", "Slide<PERSON>", "BundleName", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "DataMgr", "ButtonPlus", "ccclass", "property", "PKBuyUI", "maxVal", "curVal", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getBundleName", "HomePK", "getUIOption", "isClickBgCloseUI", "onLoad", "btnReduce", "addClick", "onReduceClick", "btnAdd", "onAddClick", "btnMax", "onMaxClick", "btnBuy", "onBuyClick", "slider", "node", "on", "onSliderValueChanged", "Math", "max", "itemName", "string", "toString", "updateSliderProgress", "min", "val", "round", "progress", "pk", "cmdStoreBuy", "closeUI", "onShow", "onHide", "onClose", "onDestroy"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;;AACzBC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;yBAGjBY,O,WADZF,OAAO,CAAC,SAAD,C,UAGHC,QAAQ,CAACR,MAAD,C,UAERQ,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,UAGRA,QAAQ,CAACV,KAAD,C,UAGRU,QAAQ,CAACT,IAAD,C,UAERS,QAAQ,CAACV,KAAD,C,WAGRU,QAAQ,CAACV,KAAD,C,2BAtBb,MACaW,OADb;AAAA;AAAA,4BACoC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAwBhCC,MAxBgC,GAwBf,EAxBe;AAAA,eAyBhCC,MAzBgC,GAyBf,CAzBe;AAAA;;AA2BZ,eAANC,MAAM,GAAW;AAAE,iBAAO,mBAAP;AAA6B;;AACxC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAwB;;AACjC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,MAAlB;AAA2B;;AAC1C,eAAXC,WAAW,GAAU;AAAE,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AAAmC;;AAC9DC,QAAAA,MAAM,GAAS;AACrB,eAAKC,SAAL,CAAgBC,QAAhB,CAAyB,KAAKC,aAA9B,EAA6C,IAA7C;AACA,eAAKC,MAAL,CAAaF,QAAb,CAAsB,KAAKG,UAA3B,EAAuC,IAAvC;AACA,eAAKC,MAAL,CAAaJ,QAAb,CAAsB,KAAKK,UAA3B,EAAuC,IAAvC;AACA,eAAKC,MAAL,CAAaN,QAAb,CAAsB,KAAKO,UAA3B,EAAuC,IAAvC;AAEA,eAAKC,MAAL,CAAaC,IAAb,CAAkBC,EAAlB,CAAqB,OAArB,EAA8B,KAAKC,oBAAnC,EAAyD,IAAzD;AACH;;AACDV,QAAAA,aAAa,GAAG;AACZ,eAAKX,MAAL;AACA,eAAKA,MAAL,GAAcsB,IAAI,CAACC,GAAL,CAAS,CAAT,EAAY,KAAKvB,MAAjB,CAAd;AACA,eAAKwB,QAAL,CAAeC,MAAf,GAAwB,QAAQ,KAAKzB,MAAL,CAAY0B,QAAZ,EAAhC;AACA,eAAKC,oBAAL;AACH;;AACDd,QAAAA,UAAU,GAAG;AACT,eAAKb,MAAL;AACA,eAAKA,MAAL,GAAcsB,IAAI,CAACM,GAAL,CAAS,KAAK7B,MAAd,EAAsB,KAAKC,MAA3B,CAAd;AACA,eAAKwB,QAAL,CAAeC,MAAf,GAAwB,QAAQ,KAAKzB,MAAL,CAAY0B,QAAZ,EAAhC;AACA,eAAKC,oBAAL;AACH;;AACDZ,QAAAA,UAAU,GAAG;AACT,eAAKf,MAAL,GAAc,KAAKD,MAAnB;AACA,eAAKyB,QAAL,CAAeC,MAAf,GAAwB,QAAQ,KAAKzB,MAAL,CAAY0B,QAAZ,EAAhC;AACA,eAAKC,oBAAL;AACH;;AAEDN,QAAAA,oBAAoB,CAACH,MAAD,EAAiB;AACjC,cAAI,KAAKnB,MAAL,IAAe,CAAnB,EAAsB;AAClB,iBAAKC,MAAL,GAAc,CAAd;AACA,iBAAKwB,QAAL,CAAeC,MAAf,GAAwB,QAAQ,KAAKzB,MAAL,CAAY0B,QAAZ,EAAhC;AACA;AACH;;AACD,cAAIG,GAAG,GAAGP,IAAI,CAACQ,KAAL,CAAWZ,MAAM,CAACa,QAAP,IAAmB,KAAKhC,MAAL,GAAc,CAAjC,CAAX,IAAkD,CAA5D;AACA8B,UAAAA,GAAG,GAAGP,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYD,IAAI,CAACM,GAAL,CAAS,KAAK7B,MAAd,EAAsB8B,GAAtB,CAAZ,CAAN;AACA,eAAK7B,MAAL,GAAc6B,GAAd;AACA,eAAKL,QAAL,CAAeC,MAAf,GAAwB,QAAQ,KAAKzB,MAAL,CAAY0B,QAAZ,EAAhC;AACH;;AACOC,QAAAA,oBAAoB,GAAG;AAC3B,cAAI,KAAKT,MAAT,EAAiB;AACb,gBAAI,KAAKnB,MAAL,IAAe,CAAnB,EAAsB;AAClB,mBAAKmB,MAAL,CAAYa,QAAZ,GAAuB,CAAvB;AACA;AACH;;AACD,iBAAKb,MAAL,CAAYa,QAAZ,GAAuB,CAAC,KAAK/B,MAAL,GAAc,CAAf,KAAqB,KAAKD,MAAL,GAAc,CAAnC,CAAvB;AACH;AACJ;;AACDkB,QAAAA,UAAU,GAAG;AACT;AAAA;AAAA,kCAAQe,EAAR,CAAWC,WAAX,CAAuB,CAAvB,EAA0B,KAAKjC,MAA/B,EAAuC,CAAvC,EAA0C,CAA1C,EAA6C,CAA7C,EAAgD,CAAhD;AACH;;AACKkC,QAAAA,OAAO,GAAG;AAAA;AACZ;AAAA;AAAA,gCAAMA,OAAN,CAAcpC,OAAd;AADY;AAEf;;AACKqC,QAAAA,MAAM,GAAkB;AAAA;AAE7B;;AACKC,QAAAA,MAAM,GAAkB;AAAA;AAC7B;;AACKC,QAAAA,OAAO,GAAkB;AAAA;AAC9B;;AACSC,QAAAA,SAAS,GAAS,CAE3B;;AA5F+B,O;;;;;iBAGR,I;;;;;;;iBAEO,I;;;;;;;iBAEH,I;;;;;;;iBAEA,I;;;;;;;iBAGA,I;;;;;;;iBAEH,I;;;;;;;iBAGE,I;;;;;;;iBAEF,I;;;;;;;iBAGA,I", "sourcesContent": ["import { _decorator, Label, Node, Slider } from 'cc';\r\nimport { BundleName } from 'db://assets/bundles/common/script/const/BundleConst';\r\nimport { BaseUI, UILayer, UIMgr, UIOpt } from 'db://assets/scripts/core/base/UIMgr';\r\nimport { DataMgr } from '../../data/DataManager';\r\nimport { ButtonPlus } from '../common/components/button/ButtonPlus';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('PKBuyUI')\r\nexport class PKBuyUI extends BaseUI {\r\n\r\n    @property(Slider)\r\n    slider: Slider | null = null;\r\n    @property(ButtonPlus)\r\n    btnReduce: ButtonPlus | null = null;\r\n    @property(ButtonPlus)\r\n    btnAdd: ButtonPlus | null = null;\r\n    @property(ButtonPlus)\r\n    btnMax: ButtonPlus | null = null;\r\n    @property(ButtonPlus)\r\n\r\n    btnBuy: ButtonPlus | null = null;\r\n    @property(Label)\r\n    lblPrice: Label | null = null;\r\n\r\n    @property(Node)\r\n    itemQuaIcon: Node | null = null;\r\n    @property(Label)\r\n    itemName: Label | null = null;\r\n\r\n    @property(Label)\r\n    buyTimes: Label | null = null;\r\n\r\n    maxVal: number = 10;\r\n    curVal: number = 1;\r\n\r\n    public static getUrl(): string { return \"prefab/ui/PKBuyUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Default }\r\n    public static getBundleName(): string { return BundleName.HomePK; }\r\n    public static getUIOption(): UIOpt { return { isClickBgCloseUI: true } }\r\n    protected onLoad(): void {\r\n        this.btnReduce!.addClick(this.onReduceClick, this);\r\n        this.btnAdd!.addClick(this.onAddClick, this);\r\n        this.btnMax!.addClick(this.onMaxClick, this);\r\n        this.btnBuy!.addClick(this.onBuyClick, this);\r\n\r\n        this.slider!.node.on('slide', this.onSliderValueChanged, this);\r\n    }\r\n    onReduceClick() {\r\n        this.curVal--;\r\n        this.curVal = Math.max(1, this.curVal);\r\n        this.itemName!.string = \"购买：\" + this.curVal.toString();\r\n        this.updateSliderProgress();\r\n    }\r\n    onAddClick() {\r\n        this.curVal++;\r\n        this.curVal = Math.min(this.maxVal, this.curVal);\r\n        this.itemName!.string = \"购买：\" + this.curVal.toString();\r\n        this.updateSliderProgress();\r\n    }\r\n    onMaxClick() {\r\n        this.curVal = this.maxVal;\r\n        this.itemName!.string = \"购买：\" + this.curVal.toString();\r\n        this.updateSliderProgress();\r\n    }\r\n\r\n    onSliderValueChanged(slider: Slider) {\r\n        if (this.maxVal <= 1) {\r\n            this.curVal = 1;\r\n            this.itemName!.string = \"购买：\" + this.curVal.toString();\r\n            return;\r\n        }\r\n        let val = Math.round(slider.progress * (this.maxVal - 1)) + 1;\r\n        val = Math.max(1, Math.min(this.maxVal, val));\r\n        this.curVal = val;\r\n        this.itemName!.string = \"购买：\" + this.curVal.toString();\r\n    }\r\n    private updateSliderProgress() {\r\n        if (this.slider) {\r\n            if (this.maxVal <= 1) {\r\n                this.slider.progress = 1;\r\n                return;\r\n            }\r\n            this.slider.progress = (this.curVal - 1) / (this.maxVal - 1);\r\n        }\r\n    }\r\n    onBuyClick() {\r\n        DataMgr.pk.cmdStoreBuy(1, this.curVal, 1, 1, 1, 1);\r\n    }\r\n    async closeUI() {\r\n        UIMgr.closeUI(PKBuyUI);\r\n    }\r\n    async onShow(): Promise<void> {\r\n\r\n    }\r\n    async onHide(): Promise<void> {\r\n    }\r\n    async onClose(): Promise<void> {\r\n    }\r\n    protected onDestroy(): void {\r\n\r\n    }\r\n}\r\n\r\n\r\n"]}
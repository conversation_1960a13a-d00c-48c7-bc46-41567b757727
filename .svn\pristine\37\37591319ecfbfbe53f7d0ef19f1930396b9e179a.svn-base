import { _decorator, Component, misc, Enum, Node, UITransform, Vec2, Vec3, JsonAsset } from 'cc';
import { BulletSystem } from '../bullet/BulletSystem';
import { MoveBase, eMoveEvent } from './IMovable';
import Entity from '../ui/base/Entity';
import { PathData, PathPoint } from '../data/PathData';
import { DefaultMove } from './DefaultMove';

const { degreesToRadians, radiansToDegrees } = misc;
const { ccclass, property, executeInEditMode } = _decorator;

@ccclass('PathMove')
@executeInEditMode()
export class PathMove extends DefaultMove {
    public _pathAsset: JsonAsset | null = null;
    @property({ type: JsonAsset, displayName: "路径数据(预览用)" })
    public get pathAsset(): JsonAsset | null {
        return this._pathAsset;
    }
    public set pathAsset(value: JsonAsset) {
        this._pathAsset = value;
        if (value) {
            this.setPath(PathData.fromJSON(value.json));
        }
    }

    @property({ displayName: "循环移动" })
    public loop: boolean = false;

    @property({ displayName: "反向移动" })
    public reverse: boolean = false;

    // 路径相关数据
    private _pathData: PathData | null = null;
    private _subdivided: PathPoint[] = []; // 细分后的路径点（包含完整信息）
    // 路径偏移
    private _offsetX: number = 0;
    private _offsetY: number = 0;

    // 移动状态
    private _currentPointIndex: number = 0; // 当前所在的细分点索引
    private _nextPointIndex: number = 0;
    private _remainDistance: number = 0;    // 距离下一个点的剩余距离

    // 停留状态
    private _stayTimer: number = 0; // 停留计时器（秒）

    private _updateInEditor: boolean = false;
    public onFocusInEditor() {
        this._updateInEditor = true;
        this._isMovable = true;
    }

    public onLostFocusInEditor() {
        this._updateInEditor = false;
        this._isMovable = false;
        this.resetToStart();
    }

    public update(dt: number) {
        if (this._updateInEditor) {
            this.tick(dt);
        }
    }

    // 注意调用顺序,先调用setOffset,再调用setPath
    public setOffset(x: number, y: number): PathMove {
        this._offsetX = x;
        this._offsetY = y;
        return this;
    }
    public setPath(pathData: PathData): PathMove {
        this._pathData = pathData;
        // 使用新的细分点方法，直接获取包含完整信息的PathPoint数组
        this._subdivided = this._pathData.getSubdividedPoints();
        this.resetToStart();

        return this;
    }

    /**
     * 主要的移动更新逻辑
     */
    public tick(dt: number): void {
        if (!this._isMovable) return;
        if (!this._pathData) {
            super.tick(dt);
            return;
        }
        
        // 处理停留逻辑
        if (this._stayTimer > 0) {
            this._stayTimer -= dt;
            if (this._stayTimer <= 0) {
                this._stayTimer = 0;
                // 停留结束，继续移动到下一个点
                this.moveToNextPoint();
            }
        } else if (this._nextPointIndex !== this._currentPointIndex) {
            this.tickMovement(dt);
        }

        this.updateTilting(this.speedAngle, dt, this._position);
        
        // 设置节点位置
        this.node.setPosition(this._position);
        this.checkVisibility();
    }

    private tickMovement(dt: number) {
        // 使用匀加速直线运动更新位置
        const v0 = this.speed;
        // s = v0*t + 0.5*a*t^2
        const s = v0 * dt + 0.5 * this.acceleration * dt * dt;
        this.speed += this.acceleration * dt;

        // console.log(`v0: ${v0.toFixed(2)}, a: ${this.acceleration.toFixed(2)}, s: ${s.toFixed(2)}`);

        // 计算移动向量
        const angleRad = this.speedAngle;
        const deltaX = Math.cos(angleRad) * s;
        const deltaY = Math.sin(angleRad) * s;

        // 更新位置
        this._position.x += deltaX;
        this._position.y += deltaY;

        // 检查是否到达目标点
        if (this._remainDistance > 0) {
            this._remainDistance -= s;
            if (this._remainDistance <= 0) {
                this.onReachPoint(this._nextPointIndex);
            }
        }
    }

    private onReachPoint(pointIndex: number) {
        // 更新当前点索引
        this._currentPointIndex = pointIndex;
        // 检查是否需要停留
        const currentPoint = this.getPathPoint(pointIndex);
        if (currentPoint) { 
            this._basePosition.x = currentPoint.x + this._offsetX;
            this._basePosition.y = currentPoint.y + this._offsetY;
            this._position.set(this._basePosition);
            this.node.setPosition(this._position);

            // console.log(`到达点 ${pointIndex}, 停留时间: ${currentPoint.stayDuration}ms`);
            if (currentPoint.stayDuration > 0) {
                this._stayTimer = currentPoint.stayDuration / 1000.0;
                return;
            }
        }

        // 继续移动到下一个点
        this.moveToNextPoint();
    }

    private moveToNextPoint() {
        const nextIndex = this._currentPointIndex + 1;

        if (nextIndex >= this._subdivided.length) {
            // 到达路径终点
            if (this.loop) {
                // 循环模式，回到起点
                this.setNext(0);
            } else {
                // 停止移动
                this._nextPointIndex = this._currentPointIndex;
            }
        } else {
            // 移动到下一个点
            this.setNext(nextIndex);
        }
    }

    private setNext(pathPointIndex: number) {
        this._nextPointIndex = pathPointIndex;

        const currentPoint = this.getPathPoint(this._currentPointIndex);
        const nextPoint = this.getPathPoint(this._nextPointIndex);
        if (currentPoint && nextPoint) {
            const dirX = nextPoint.x - currentPoint.x;
            const dirY = nextPoint.y - currentPoint.y;
            this._remainDistance = Math.sqrt(dirX * dirX + dirY * dirY);

            if (this._remainDistance > 1) {
                // 计算移动角度
                this.speedAngle = Math.atan2(dirY, dirX);

                // 计算加速度：使用匀加速直线运动公式 v1^2 = v0^2 + 2*a*x
                // 解出 a = (v1^2 - v0^2) / (2*x)
                const v0 = currentPoint.speed;
                const v1 = nextPoint.speed;
                this.acceleration = (v1 * v1 - v0 * v0) / (2 * this._remainDistance);

                // 设置初始速度
                this.speed = v0;

                // console.log(`设置下一段: ${this._currentPointIndex} -> ${this._nextPointIndex}, 距离: ${this._remainDistance.toFixed(2)}, 初速度: ${v0}, 末速度: ${v1}, 加速度: ${this.acceleration.toFixed(2)}, 角度: ${radiansToDegrees(this.speedAngle).toFixed(2)}`);
            }
        }
    }

    private getPathPoint(pathPointIndex: number): PathPoint | null {
        if (pathPointIndex < 0 || pathPointIndex >= this._subdivided.length) {
            return null;
        }
        return this._subdivided[pathPointIndex];
    }

    private resetToStart() {
        this._currentPointIndex = 0;
        this._nextPointIndex = 0;
        this._stayTimer = 0;
        this._tiltTime = 0;

        this.onReachPoint(0);
    }

    public isStaying(): boolean {
        return this._stayTimer > 0;
    }

    public getRemainingStayTime(): number {
        return this._stayTimer;
    }
}

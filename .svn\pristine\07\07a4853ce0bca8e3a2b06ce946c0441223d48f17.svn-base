import { _decorator, Label, Node, Sprite, Sprite<PERSON><PERSON><PERSON>, Sprite<PERSON>rame } from 'cc';

import { BaseUI, UILayer, UIMgr, UIOpt } from 'db://assets/scripts/core/base/UIMgr';
import { GameIns } from '../../../game/GameIns';
import { MyApp } from '../../../app/MyApp';

const { ccclass, property } = _decorator;

@ccclass('GamePauseUI')
export class GamePauseUI extends BaseUI {

    @property([SpriteFrame])
    downFrames:SpriteFrame[] = [];

    @property(Node)
    NodePauseUI: Node | null = null;
    @property(Node)
    NodeTimeUI: Node | null = null;
    @property(Sprite)
    spTime: Sprite | null = null;

    @property(Node)
    contentRogue: Node | null = null;

    private _countdown: number = 7; // 倒计时初始值
    private _countdownInterval: ReturnType<typeof setInterval> | null = null; // 用于存储计时器 ID

    public static getUrl(): string { return "prefab/ui/game/GamePauseUI"; }
    public static getLayer(): UILayer { return UILayer.Default }
    public static getUIOption(): UIOpt { return { isClickBgCloseUI: false } }

    protected onLoad(): void {

    }
    async closeUI() {
        UIMgr.closeUI(GamePauseUI);
    }
    async onShow(): Promise<void> {
        this.NodePauseUI!.active = true;
        this.NodeTimeUI!.active = false;
        this.refreshPauseUI();
    }
    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {
        this.stopCountdown(); // 停止倒计时
        GameIns.rogueManager.recycleRogueItems(this.contentRogue!);
    }
    protected onDestroy(): void {
    }

    refreshPauseUI(){
        let data = GameIns.rogueManager.getSeLectedHistory();
        GameIns.rogueManager.recycleRogueItems(this.contentRogue!);
        data.forEach((value,key)=>{
            let config = MyApp.lubanTables.TbResWordGroup.get(key);
            GameIns.rogueManager.setRogueItem(this.contentRogue!,config!.wordId,value);
        })
    }

    onBtnResumeClicked() {
        this.NodePauseUI!.active = false;
        this.NodeTimeUI!.active = true;
        this.startCountdown();
    }

    onBtnExitClicked() {
        this.closeUI();
        GameIns.battleManager.quitBattle();
    }

    // 开始倒计时
    private startCountdown(): void {
        this._countdown = 3; // 初始化倒计时
        this.updateCountdownLabel(); // 更新初始显示

        this._countdownInterval = setInterval(() => {
            this._countdown--;
            this.updateCountdownLabel();

            if (this._countdown <= 0) {
                this.stopCountdown();
                this.onCountdownFinished();
            }
        }, 1000); // 每秒更新一次
    }

    // 停止倒计时
    private stopCountdown(): void {
        if (this._countdownInterval !== null) {
            clearInterval(this._countdownInterval);
            this._countdownInterval = null;
        }
    }

    // 更新倒计时文本
    private updateCountdownLabel(): void {
        if (this.spTime) {
            this.spTime.spriteFrame = this.downFrames[this._countdown];
        }
    }

    // 倒计时结束时的逻辑
    private onCountdownFinished(): void {
        GameIns.gameStateManager.gameResume();
        this.closeUI();
    }
}



import { _decorator, Node, Prefab} from 'cc';
import { GameEnum } from '../../../const/GameEnum';
import PlaneBase from 'db://assets/bundles/common/script/game/ui/plane/PlaneBase';
import FCollider from '../../../collider-system/FCollider';
import { Bullet } from '../../../bullet/Bullet';
import { Plane } from 'db://assets/bundles/common/script/ui/Plane';
import { EnemyData, EnemyPrefab } from '../../../data/EnemyData';
import { AttributeConst } from 'db://assets/bundles/common/script/const/AttributeConst';
import { eMoveEvent, eOrientationType } from 'db://assets/bundles/common/script/game/move/IMovable'
import { PathMove } from 'db://assets/bundles/common/script/game/move/PathMove';
import { PathData } from 'db://assets/bundles/common/script/game/data/PathData';
import { GameIns } from '../../../GameIns';
import { MyApp } from '../../../../app/MyApp';
import { EventGroupComp } from '../event/EventGroupCom';

const { ccclass, property } = _decorator;

@ccclass('EnemyPlaneBase')
export default class EnemyPlaneBase extends PlaneBase {
    @property(Node)
    planeParent: Node | null = null;

    _plane: Plane | null = null;
    _enemyData: EnemyData | null = null;
    public get enemyData() { return this._enemyData; }
    _enemyPrefabData: EnemyPrefab | null = null;
    public get enemyPrefabData() { return this._enemyPrefabData; }

    _moveCom: PathMove | null = null;
    public get moveCom() { return this._moveCom; }
    _eventGroupComp: EventGroupComp | null = null;
    public get eventGroupComp() { return this._eventGroupComp; }

    removeAble:boolean = false;
    bullets: Bullet[] = [];

    reset() {
        // 继承自BaseComp的组件在reset里会执行reset
        super.reset();
        // 继承自Component的组件这里自行reset
        this._plane?.reset();
        this._moveCom?.reset();
    }

    initPlane(data: EnemyData, prefab: Prefab) {
        this.reset();
        this.enemy = true
        this._enemyData = data;

        this.removeAble = false;
        //加载飞机显示
        if (!this._plane) {
            let plane = MyApp.planeMgr.getPlane(data, prefab);
            this._plane = plane.getComponent(Plane)!;
            this.planeParent!.addChild(plane);
            this._enemyPrefabData = plane.getComponent(EnemyPrefab);
            if (this._enemyPrefabData && this._enemyPrefabData.eventGroups) {
                this._eventGroupComp = new EventGroupComp();
                this.addComp('eventGroup', this._eventGroupComp);
            }
        }

        // reset plane property
        this._enemyData?.initProperties(this);

        super.init();

        this._moveCom = this.getComponent(PathMove) || this.addComponent(PathMove);
        this._moveCom!.removeAllListeners();
        this._moveCom!.on(eMoveEvent.onBecomeInvisible, () => this.onBecameInvisible());
        this._moveCom!.on(eMoveEvent.onBecomeVisible, () => this.onBecameVisible());

        this.refreshProperty();
    }

    get target():PlaneBase|null {
        return GameIns.mainPlaneManager.mainPlane;
    }

    initMove(x: number, y: number, angle: number) {
        this.setPos(x, y);
        // 兼容编辑器
        if (!this._moveCom) {        
            this._moveCom = this.getComponent(PathMove) || this.addComponent(PathMove);
        }
        // 速度从表格里读取
        this._moveCom!.speed = this._enemyData?.config?.moveSpeed || 100;
        this._moveCom!.speedAngle = angle;
        this._moveCom!.turnSpeed = this._enemyData?.config?.turnSpeed || 0;
        this._moveCom!.acceleration = 0;
        this._moveCom!.accelerationAngle = 0;
        // this._moveCom!.tiltSpeed = this.tiltSpeed;
        // this._moveCom!.tiltOffset = this.tiltOffset;
        let orientationType = this._enemyData?.config?.orientation as any as eOrientationType || eOrientationType.Path;
        let orientationParam = 0;// this._enemyData?.config?.orientationParam || 0;
        this._moveCom!.setOrientation(orientationType, orientationParam);
        this._moveCom!.setMovable(true);
    }

    initPath(x: number, y: number, pathData: PathData) {
        if (!this._moveCom) {
            return;
        }
        
        this._moveCom.speed = this._enemyData?.config?.moveSpeed || 100;
        this._moveCom.turnSpeed = this._enemyData?.config?.turnSpeed || 0;
        this._moveCom!.acceleration = 0;
        this._moveCom!.accelerationAngle = 0;
        this.setPos(x, y);
        // this._moveCom!.tiltSpeed = this.tiltSpeed;
        // this._moveCom!.tiltOffset = this.tiltOffset;
        this._moveCom!.setOffset(x, y).setPath(pathData).setMovable(true);
    }

    _dieWhenOffScreen() {
        this.toDie(GameEnum.EnemyDestroyType.Leave);
    }

    private onBecameInvisible() {
        // TODO: 从表格里增加延时销毁的配置
        this._dieWhenOffScreen();
        // this.scheduleOnce(this._dieWhenOffScreen, delayDestroy);
    }

    private onBecameVisible() {
        // this.unschedule(this._dieWhenOffScreen);
    }

    refreshProperty() {
        const config = this._enemyData?.config;
        if (!config) {
            return;
        }
        this.attribute.addBaseAttribute(AttributeConst.MaxHPOutAdd, config.baseHp);
        this.attribute.addBaseAttribute(AttributeConst.AttackOutAdd, config.baseAtk);
        this.attribute.addBaseAttribute(AttributeConst.StatusImmuneBulletHurt, config.immuneBulletDamage?1:0);
        this.attribute.addBaseAttribute(AttributeConst.StatusImmuneCollisionHurt, config.immuneCollideDamage?1:0);
        this.attribute.addBaseAttribute(AttributeConst.StatusIgnoreBullet, config.ignoreBullet?1:0);
        this.attribute.addBaseAttribute(AttributeConst.StatusIgnoreCollision, config.ignoreCollide?1:0);
        this.attribute.addBaseAttribute(AttributeConst.StatusImmuneNuclearHurt, config.immuneNuke?1:0);
        this.attribute.addBaseAttribute(AttributeConst.StatusImmuneActiveSkillHurt, config.immuneActiveSkill?1:0);

        this.curHp = this.maxHp
    }

    getAttack():number {
        return this._enemyData!.getAttack();
    }

    toDie(destroyType: GameEnum.EnemyDestroyType = GameEnum.EnemyDestroyType.Die): boolean  {
        if (!super.toDie(destroyType)) {
            return false;
        }
        this.colliderEnabled = false;

        this.onDie(destroyType);
        return true;
    }

    onDie(destroyType: number) {
        this.willRemove();

        switch (destroyType) {
            case GameEnum.EnemyDestroyType.Die:
                this.playDieAnim(()=>{
                    this.removeAble = true;
                });
                break;

            case GameEnum.EnemyDestroyType.Leave:
            case GameEnum.EnemyDestroyType.TrackOver:
            case GameEnum.EnemyDestroyType.TimeOver:
                this.removeAble = true;
                break;
        }
    }

    playDieAnim(callBack: Function) {
        // if (this.plane) {
        //     this.plane.playDieAnim(callBack);   
        // }
        callBack?.();
    }

    get collisionLevel() {
        return this._enemyData!.config?.collideLevel || 0;
    }
    get collisionHurt() {
        return this._enemyData!.config?.collideDamage || 0;
    }

    onCollide(collider: FCollider) {
        if (this.isDead) {
            return
        }
        if (collider.entity instanceof Bullet) {
            if (this.attribute.getFinalAttributeByKey(AttributeConst.StatusImmuneBulletHurt) == 0) {
                const damage = collider.entity.calcDamage(this);
                GameIns.hurtEffectManager.createHurtNumByType(collider.entity.node.getPosition(), damage);
                this.hurt(damage)
                // 计算破甲概率
                const attacker = collider.entity.emitter.getEntity();
                const penetrationRate = attacker?.attribute.getFinalAttributeByKey(AttributeConst.BulletPenetrationRate) || 0;
                if (GameIns.battleManager.random() < penetrationRate/10000) {
                    this.buffComp.ApplyBuff(false, MyApp.lubanTables.TbGlobalAttr.BulletPenetrationFlagBuffID)
                }
            }
        } else if (collider.entity instanceof PlaneBase && !collider.entity.enemy) {
            this.collisionPlane(collider.entity);
        }
    }

    /**
     * 准备移除敌机
     */
    willRemove() {

    }

    addBullet(bullet: Bullet) {
        if (this.bullets) {
            this.bullets.push(bullet);
        }
    }

    /**
     * 从敌人移除子弹
     * @param {Bullet} bullet 子弹对象
     */
    removeBullet(bullet: Bullet) {
        if (this.bullets) {
            const index = this.bullets.indexOf(bullet);
            if (index >= 0) {
                this.bullets.splice(index, 1);
            }
        }
    }

    setPos(x: number, y: number) {
        this.node.setPosition(x, y);
    }
}
System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Node, tween, v3, BaseUI, UILayer, UIMgr, GameIns, MyApp, BundleName, _dec, _dec2, _dec3, _dec4, _class, _class2, _descriptor, _descriptor2, _descriptor3, _crd, ccclass, property, WheelSpinnerUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../../game/GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../../app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "../../../const/BundleConst", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Node = _cc.Node;
      tween = _cc.tween;
      v3 = _cc.v3;
    }, function (_unresolved_2) {
      BaseUI = _unresolved_2.BaseUI;
      UILayer = _unresolved_2.UILayer;
      UIMgr = _unresolved_2.UIMgr;
    }, function (_unresolved_3) {
      GameIns = _unresolved_3.GameIns;
    }, function (_unresolved_4) {
      MyApp = _unresolved_4.MyApp;
    }, function (_unresolved_5) {
      BundleName = _unresolved_5.BundleName;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "60985kNDC1CJbTv5Vdbto5y", "WheelSpinnerUI", undefined);

      __checkObsolete__(['_decorator', 'Node', 'tween', 'v3', 'Vec3', 'UIOpacity']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("WheelSpinnerUI", WheelSpinnerUI = (_dec = ccclass("WheelSpinnerUI"), _dec2 = property(Node), _dec3 = property(Node), _dec4 = property(Node), _dec(_class = (_class2 = class WheelSpinnerUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "NodeRewards", _descriptor, this);

          // 转盘奖励节点容器
          _initializerDefineProperty(this, "NodeArrow", _descriptor2, this);

          // 箭头节点，需要旋转这个
          _initializerDefineProperty(this, "NodeSelect", _descriptor3, this);

          // 转盘配置
          this.REWARD_COUNT = 6;
          // 奖励数量
          // 转盘状态
          this._isSpinning = false;
          // 是否正在旋转
          this._currentRewardIndex = 0;
        }

        // 当前奖励索引
        static getUrl() {
          return "prefab/WheelSpinnerUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Default;
        }

        static getBundleName() {
          return (_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).GameFight;
        }

        onLoad() {}

        async onShow() {
          // 初始化箭头位置
          this.resetWheel();
          this.refreshSelectUI();
        }

        async onHide(...args) {}

        async onClose(...args) {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).rogueManager.recycleRogueItems(this.NodeSelect);
        }

        async closeUI() {
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).closeUI(WheelSpinnerUI);
        }

        refreshSelectUI() {
          let data = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).rogueManager.getSeLectedHistory();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).rogueManager.recycleRogueItems(this.NodeSelect);
          data.forEach((value, key) => {
            let config = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).lubanTables.TbResWordGroup.get(key);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).rogueManager.setRogueItem(this.NodeSelect, config.wordId, value);
          });
        }
        /**
         * 初始化箭头位置
         */


        resetWheel() {
          if (!this.NodeArrow) return;
          this._isSpinning = false;
          this.NodeArrow.angle = 3;
          this._currentRewardIndex = 0;
        }

        onBtnStartClick() {
          if (this._isSpinning) return; // 如果正在旋转，则忽略点击

          this.startSpin();
        }

        startSpin() {
          if (!this.NodeArrow) return;
          this._isSpinning = true;
          const targetRewardIndex = Math.floor(Math.random() * this.REWARD_COUNT);
          let newIndex = targetRewardIndex - this._currentRewardIndex;

          if (newIndex < 0) {
            newIndex += this.REWARD_COUNT;
          }

          const targetAngle = this.NodeArrow.angle - newIndex * 60; // 为了增加动画效果，让箭头多转几圈

          const extraRotations = 3; // 额外旋转圈数

          const totalRotation = -extraRotations * 360 + targetAngle; // 使用缓动动画实现箭头旋转

          tween(this.NodeArrow).to(extraRotations * 0.8, {
            angle: totalRotation
          }, {
            easing: "cubicOut",
            // 缓动函数，先快后慢
            onComplete: () => {
              // 旋转完成
              this.onSpinComplete(targetRewardIndex);
            }
          }).start();
        }

        onSpinComplete(rewardIndex) {
          this._isSpinning = false;
          this._currentRewardIndex = rewardIndex;
          console.log(`转盘抽奖完成，选中奖励索引: ${rewardIndex}`); // 这里可以添加选中效果，比如高亮显示对应的奖励

          this.highlightSelectedReward(rewardIndex); // 发放奖励

          this.giveReward(rewardIndex); // 刷新选择UI

          this.refreshSelectUI();
        }

        highlightSelectedReward(rewardIndex) {
          if (!this.NodeRewards) return;
          const rewardNodes = this.NodeRewards.children;

          if (rewardNodes.length > rewardIndex) {
            // 这里可以添加高亮效果，比如改变颜色、缩放等
            // 示例：简单的缩放效果
            tween(rewardNodes[rewardIndex]).to(0.2, {
              scale: v3(1.2, 1.2, 1)
            }).to(0.2, {
              scale: v3(1, 1, 1)
            }).start();
          }
        }

        giveReward(rewardIndex) {
          console.log(`发放奖励: ${rewardIndex}`);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "NodeRewards", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "NodeArrow", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "NodeSelect", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=97aa86a28235941b87345596a0a90ad8b54a8065.js.map
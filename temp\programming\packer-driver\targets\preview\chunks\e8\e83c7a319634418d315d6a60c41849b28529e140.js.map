{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/FriendFusionCellUI.ts"], "names": ["_decorator", "Component", "Label", "Sprite", "DataMgr", "DataEvent", "EventMgr", "AvatarIcon", "MyApp", "UITools", "ccclass", "property", "FriendFusionCellUI", "selectIdx", "setData", "idx", "lblAdd", "string", "toString", "lblName", "lblScore", "spPlane", "spriteFrame", "updateSelect", "pk", "selectFusionIdx", "modifyStateSprite", "bg", "onClick", "audioMgr", "playSound", "emit", "FusionCellClick", "start", "update", "deltaTime", "onDestroy"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAmBC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAaC,MAAAA,M,OAAAA,M;;AAC3CC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,O,iBAAAA,O;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;oCAGjBY,kB,WADZF,OAAO,CAAC,oBAAD,C,UAGHC,QAAQ,CAACT,KAAD,C,UAERS,QAAQ,CAACR,MAAD,C,UAERQ,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ,CAACT,KAAD,C,UAERS,QAAQ,CAACT,KAAD,C,UAERS,QAAQ,CAACR,MAAD,C,UAGRQ,QAAQ,CAACR,MAAD,C,2BAhBb,MACaS,kBADb,SACwCX,SADxC,CACkD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAkB9CY,SAlB8C,GAkB1B,CAlB0B;AAAA;;AAoB9CC,QAAAA,OAAO,CAACC,GAAD,EAAcF,SAAd,EAAiC;AACpC,eAAKA,SAAL,GAAiBA,SAAjB;AACA,eAAKG,MAAL,CAAaC,MAAb,GAAsBF,GAAG,CAACG,QAAJ,EAAtB;AACA,eAAKC,OAAL,CAAcF,MAAd,GAAuB,MAAvB;AACA,eAAKG,QAAL,CAAeH,MAAf,GAAwB,KAAxB;AACA,eAAKI,OAAL,CAAcC,WAAd,GAA4B,IAA5B;AACA,eAAKC,YAAL;AACH;;AAEDA,QAAAA,YAAY,GAAG;AACX,cAAI,KAAKV,SAAL,IAAkB;AAAA;AAAA,kCAAQW,EAAR,CAAWC,eAAjC,EAAkD;AAC9C;AACA;AAAA;AAAA,oCAAQC,iBAAR,CAA0B,KAAKC,EAA/B,EAAoC,CAApC;AACH,WAHD,MAGO;AACH;AAAA;AAAA,oCAAQD,iBAAR,CAA0B,KAAKC,EAA/B,EAAoC,CAApC,EADG,CAEH;AACH;AACJ;;AAEDC,QAAAA,OAAO,GAAG;AACN;AAAA;AAAA,8BAAMC,QAAN,CAAeC,SAAf,CAAyB,OAAzB,EAAkC,CAAlC;;AACA,cAAI,KAAKjB,SAAL,IAAkB;AAAA;AAAA,kCAAQW,EAAR,CAAWC,eAAjC,EAAkD;AAC9C;AAAA;AAAA,oCAAQD,EAAR,CAAWC,eAAX,GAA6B,CAAC,CAA9B;AACH,WAFD,MAEO;AACH;AAAA;AAAA,oCAAQD,EAAR,CAAWC,eAAX,GAA6B,KAAKZ,SAAlC;AACH;;AACD;AAAA;AAAA,oCAASkB,IAAT,CAAc;AAAA;AAAA,sCAAUC,eAAxB,EAAyC,KAAKnB,SAA9C;AACH;;AAEDoB,QAAAA,KAAK,GAAG,CAEP;;AAEDC,QAAAA,MAAM,CAACC,SAAD,EAAoB,CAEzB;;AAESC,QAAAA,SAAS,GAAS,CAE3B;;AA3D6C,O;;;;;iBAGvB,I;;;;;;;iBAEA,I;;;;;;;iBAES,I;;;;;;;iBAER,I;;;;;;;iBAEC,I;;;;;;;iBAEA,I;;;;;;;iBAGL,I", "sourcesContent": ["import { _decorator, Color, Component, Label, math, Sprite, Node } from 'cc';\r\nimport { DataMgr } from '../../data/DataManager';\r\nimport { DataEvent } from '../../event/DataEvent';\r\nimport { EventMgr } from '../../event/EventManager';\r\nimport { AvatarIcon } from '../common/components/base/AvatarIcon';\r\nimport { MyApp } from '../../app/MyApp';\r\nimport { UITools } from '../../game/utils/UITools';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('FriendFusionCellUI')\r\nexport class FriendFusionCellUI extends Component {\r\n\r\n    @property(Label)\r\n    lblAdd: Label | null = null;\r\n    @property(Sprite)\r\n    spAdd: Sprite | null = null;\r\n    @property(AvatarIcon)\r\n    avatarIcon: AvatarIcon | null = null;\r\n    @property(Label)\r\n    lblName: Label | null = null;\r\n    @property(Label)\r\n    lblScore: Label | null = null;\r\n    @property(Sprite)\r\n    spPlane: Sprite | null = null;\r\n\r\n    @property(Sprite)\r\n    bg: Sprite | null = null;\r\n    \r\n    selectIdx: number = 0;\r\n\r\n    setData(idx: number, selectIdx: number) {\r\n        this.selectIdx = selectIdx;\r\n        this.lblAdd!.string = idx.toString();\r\n        this.lblName!.string = \"name\";\r\n        this.lblScore!.string = \"分数：\";\r\n        this.spPlane!.spriteFrame = null;\r\n        this.updateSelect();\r\n    }\r\n\r\n    updateSelect() {\r\n        if (this.selectIdx == DataMgr.pk.selectFusionIdx) {\r\n            //this.getComponentInChildren(Sprite)!.color = Color.BLUE;\r\n            UITools.modifyStateSprite(this.bg! ,0);\r\n        } else {\r\n            UITools.modifyStateSprite(this.bg! ,1);\r\n            //this.getComponentInChildren(Sprite)!.color = math.color(\"#C3D69F\");\r\n        }\r\n    }\r\n\r\n    onClick() {\r\n        MyApp.audioMgr.playSound(\"click\", 5);\r\n        if (this.selectIdx == DataMgr.pk.selectFusionIdx) {\r\n            DataMgr.pk.selectFusionIdx = -1;\r\n        } else {\r\n            DataMgr.pk.selectFusionIdx = this.selectIdx;\r\n        }\r\n        EventMgr.emit(DataEvent.FusionCellClick, this.selectIdx);\r\n    }\r\n\r\n    start() {\r\n\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n\r\n    }\r\n\r\n    protected onDestroy(): void {\r\n\r\n    }\r\n\r\n}\r\n\r\n\r\n"]}
System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10", "__unresolved_11"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, instantiate, JsonAsset, Node, Prefab, Sprite, SpriteFrame, UITransform, v2, MyApp, LayerSplicingMode, LevelDataTerrain, LevelEventRun, Tools, GameIns, logDebug, logError, logInfo, GameConst, LevelNodeCheckOutScreen, LevelUtils, EmittierStatus, EmittierTerrain, LevelLayer, _dec, _class, _crd, ccclass, SCROLL_POOL_NAME, TERRAIN_POOL_NAME, EMIITER_POOL_NAME, TerrainsNodeName, DynamicNodeName, ScrollsNodeName, EmittiersNodeName, jsonRootPath, LevelLayerUI;

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/bundles/common/script/app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLayerSplicingMode(extras) {
    _reporterNs.report("LayerSplicingMode", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEvent(extras) {
    _reporterNs.report("LevelDataEvent", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataLayer(extras) {
    _reporterNs.report("LevelDataLayer", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataScroll(extras) {
    _reporterNs.report("LevelDataScroll", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataTerrain(extras) {
    _reporterNs.report("LevelDataTerrain", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelEventRun(extras) {
    _reporterNs.report("LevelEventRun", "./LevelEventRun", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "db://assets/bundles/common/script/game/utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "db://assets/bundles/common/script/game/GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogDebug(extras) {
    _reporterNs.report("logDebug", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogError(extras) {
    _reporterNs.report("logError", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogInfo(extras) {
    _reporterNs.report("logInfo", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "db://assets/scripts/core/base/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelNodeCheckOutScreen(extras) {
    _reporterNs.report("LevelNodeCheckOutScreen", "./LevelNodeCheckOutScreen", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelUtils(extras) {
    _reporterNs.report("LevelUtils", "./LevelUtils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmittierStatus(extras) {
    _reporterNs.report("EmittierStatus", "db://assets/bundles/common/script/game/dyncTerrain/EmittierTerrain", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmittierTerrain(extras) {
    _reporterNs.report("EmittierTerrain", "db://assets/bundles/common/script/game/dyncTerrain/EmittierTerrain", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelLayer(extras) {
    _reporterNs.report("LevelLayer", "./LevelLayer", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      instantiate = _cc.instantiate;
      JsonAsset = _cc.JsonAsset;
      Node = _cc.Node;
      Prefab = _cc.Prefab;
      Sprite = _cc.Sprite;
      SpriteFrame = _cc.SpriteFrame;
      UITransform = _cc.UITransform;
      v2 = _cc.v2;
    }, function (_unresolved_2) {
      MyApp = _unresolved_2.MyApp;
    }, function (_unresolved_3) {
      LayerSplicingMode = _unresolved_3.LayerSplicingMode;
      LevelDataTerrain = _unresolved_3.LevelDataTerrain;
    }, function (_unresolved_4) {
      LevelEventRun = _unresolved_4.LevelEventRun;
    }, function (_unresolved_5) {
      Tools = _unresolved_5.Tools;
    }, function (_unresolved_6) {
      GameIns = _unresolved_6.GameIns;
    }, function (_unresolved_7) {
      logDebug = _unresolved_7.logDebug;
      logError = _unresolved_7.logError;
      logInfo = _unresolved_7.logInfo;
    }, function (_unresolved_8) {
      GameConst = _unresolved_8.GameConst;
    }, function (_unresolved_9) {
      LevelNodeCheckOutScreen = _unresolved_9.LevelNodeCheckOutScreen;
    }, function (_unresolved_10) {
      LevelUtils = _unresolved_10.LevelUtils;
    }, function (_unresolved_11) {
      EmittierStatus = _unresolved_11.EmittierStatus;
      EmittierTerrain = _unresolved_11.EmittierTerrain;
    }, function (_unresolved_12) {
      LevelLayer = _unresolved_12.LevelLayer;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "d139a2/Z9NEzIGUXE+qqxb8", "LevelLayerUI", undefined);

      __checkObsolete__(['_decorator', 'Component', 'instantiate', 'js', 'JsonAsset', 'log', 'Node', 'Prefab', 'Sprite', 'SpriteFrame', 'UITransform', 'v2', 'Vec2', 'view']);

      ({
        ccclass
      } = _decorator);
      SCROLL_POOL_NAME = "scroll_pool";
      TERRAIN_POOL_NAME = "terrain_pool";
      EMIITER_POOL_NAME = "emittier_pool";
      TerrainsNodeName = "terrains";
      DynamicNodeName = "dynamic";
      ScrollsNodeName = "scrolls";
      EmittiersNodeName = "emittiers";
      jsonRootPath = 'game/level/background/Prefab/Config/';

      _export("LevelLayerUI", LevelLayerUI = (_dec = ccclass('LevelLayerUI'), _dec(_class = class LevelLayerUI extends (_crd && LevelLayer === void 0 ? (_reportPossibleCrUseOfLevelLayer({
        error: Error()
      }), LevelLayer) : LevelLayer) {
        constructor(...args) {
          super(...args);
          this._offSetY = 0;
          // 当前关卡的偏移量
          this.terrainsNode = null;
          this.dynamicNode = null;
          this.scrollsNode = null;
          this.emittiersNode = null;
          this._scorllData = null;
          this._scorllPrefabs = [];
          // 已经加载的
          this._lastScrollNodeHeight = 0;
          this._emittierInfo = [];
          this._inactiveEmittierNodes = [];
          // 未激活的发射器节点列表
          this._insEmittierLock = false;
          // 当前关卡的地形信息，用于动态实例化
          this._terrainsInfo = [];
          // 地形基础元素，单个terrain根据Y坐标排序（背景、滚动层、发射器单独处理）
          this._insTerrainLock = false;
          this.events = [];
          this.eventRunners = [];
        }

        onLoad() {}

        async initByLevelData(data, offSetY, bFirstLoad) {
          this._offSetY = offSetY;
          this.node.setPosition(0, offSetY, 0);
          this.terrainsNode = (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
            error: Error()
          }), LevelUtils) : LevelUtils).getOrAddNode(this.node, TerrainsNodeName);
          this.dynamicNode = (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
            error: Error()
          }), LevelUtils) : LevelUtils).getOrAddNode(this.node, DynamicNodeName);
          this.scrollsNode = (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
            error: Error()
          }), LevelUtils) : LevelUtils).getOrAddNode(this.node, ScrollsNodeName);
          this.emittiersNode = (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
            error: Error()
          }), LevelUtils) : LevelUtils).getOrAddNode(this.node, EmittiersNodeName);
          this._scorllPrefabs = [];
          this._terrainsInfo = [];
          this._emittierInfo = [];
          this._inactiveEmittierNodes = [];
          this._insTerrainLock = false;
          this._insEmittierLock = false; // 首关基础地形只加载一屏半的资源，剩余的动态加载
          // ------------------------------------------

          await this._initTerrainsByLevelData(data, bFirstLoad);
          await this._initDynamicsByLevelData(data, bFirstLoad);
          await this._initEmittierLevelData(data, bFirstLoad); // ------------------------------------------

          await this._initScorllsByLevelData(data, bFirstLoad);

          this._sortTerrainsInfo();

          this.events = [...data.events];
          this.events.sort((a, b) => a.position.y - b.position.y);
          this.events.forEach(event => {
            this.eventRunners.push(new (_crd && LevelEventRun === void 0 ? (_reportPossibleCrUseOfLevelEventRun({
              error: Error()
            }), LevelEventRun) : LevelEventRun)(event, this));
          });
        }

        async _initTerrainsByLevelData(data, bFirstLoad) {
          var _this$node$parent;

          if (!data || this.terrainsNode === null || data.terrains.length === 0) {
            return;
          }

          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
            error: Error()
          }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent = this.node.parent) == null || (_this$node$parent = _this$node$parent.parent) == null ? void 0 : _this$node$parent.name}:${this.node.name} 固定地形初始化：${data.remark}`);
          let zIndex = 0;

          for (const terrain of data.terrains) {
            if (bFirstLoad) {
              if (terrain.type.length > 0) {
                if (terrain.type.endsWith('.json')) {
                  var _this$node$parent2;

                  const json_path = jsonRootPath + terrain.type.replace('.json', '');
                  (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                    error: Error()
                  }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent2 = this.node.parent) == null || (_this$node$parent2 = _this$node$parent2.parent) == null ? void 0 : _this$node$parent2.name}:${this.node.name}] jsonData : ${(_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
                    error: Error()
                  }), LevelUtils) : LevelUtils).extractPathPart(json_path)}`);
                  const jsonAsset = await (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                    error: Error()
                  }), MyApp) : MyApp).resMgr.loadAsync(json_path, JsonAsset);
                  const jsonData = jsonAsset.json;

                  if (jsonData.terrains && Array.isArray(jsonData.terrains)) {
                    var _this$node$parent3;

                    let subZIndex = 0;
                    const subParentNode = (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
                      error: Error()
                    }), LevelUtils) : LevelUtils).getOrAddNode(this.terrainsNode, `terr_${zIndex}`);
                    (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                      error: Error()
                    }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent3 = this.node.parent) == null || (_this$node$parent3 = _this$node$parent3.parent) == null ? void 0 : _this$node$parent3.name}:${this.node.name}] init terrain 创建节点 subParentNode: ${subParentNode.name} 父节点：${subParentNode.parent.name}`);
                    subParentNode.setSiblingIndex(zIndex);

                    for (const t of jsonData.terrains) {
                      const terrainData = Object.assign(new (_crd && LevelDataTerrain === void 0 ? (_reportPossibleCrUseOfLevelDataTerrain({
                        error: Error()
                      }), LevelDataTerrain) : LevelDataTerrain)(), t);
                      const terrainPosY = terrain.position.y + terrainData.position.y;

                      if (terrainPosY - terrainData.height / 2 + this.node.position.y <= (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                        error: Error()
                      }), GameConst) : GameConst).VIEWPORT_LOAD_POS) {
                        if (terrainData.type.endsWith('.png')) {
                          var _this$node$parent4;

                          const png_path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                            error: Error()
                          }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                            error: Error()
                          }), MyApp) : MyApp).resMgr.defaultBundleName, terrainData.type.replace('.png', ''));
                          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                            error: Error()
                          }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent4 = this.node.parent) == null || (_this$node$parent4 = _this$node$parent4.parent) == null ? void 0 : _this$node$parent4.name}:${this.node.name}] 静态元素(通过json配置) png : ${(_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
                            error: Error()
                          }), LevelUtils) : LevelUtils).extractPathPart(png_path)}`);
                          await this._createPngNode(png_path, v2(terrain.position.x + terrainData.position.x, terrainPosY), v2(terrainData.scale.x, terrainData.scale.y), terrainData.rotation, subParentNode, subZIndex);
                        } else {
                          var _this$node$parent5;

                          const path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                            error: Error()
                          }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                            error: Error()
                          }), MyApp) : MyApp).resMgr.defaultBundleName, terrainData.uuid);
                          const prefabNode = await this._createTerrainPrefabNode(path, v2(terrain.position.x + terrainData.position.x, terrainPosY), v2(terrainData.scale.x, terrainData.scale.y), terrainData.rotation, subParentNode, subZIndex);
                          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                            error: Error()
                          }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent5 = this.node.parent) == null || (_this$node$parent5 = _this$node$parent5.parent) == null ? void 0 : _this$node$parent5.name}:${this.node.name}] 静态元素(通过json配置) prefab : ${prefabNode.name}`);
                        }
                      } else {
                        terrainData.position.y = terrainPosY;
                        terrainData.position.x += terrain.position.x;
                        terrainData.subParentNode = subParentNode;

                        this._addTerrainInfo(terrainData, subParentNode, subZIndex);
                      }

                      subZIndex++;
                    }
                  } else {
                    (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
                      error: Error()
                    }), logError) : logError)("LevelLayerUI", "JSON data has no terrains array");
                  }
                } else if (terrain.type.endsWith('.png')) {
                  if (terrain.position.y - terrain.height / 2 + this.node.position.y <= (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                    error: Error()
                  }), GameConst) : GameConst).VIEWPORT_LOAD_POS) {
                    var _this$node$parent6;

                    const png_path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                      error: Error()
                    }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                      error: Error()
                    }), MyApp) : MyApp).resMgr.defaultBundleName, terrain.type.replace('.png', ''));
                    await this._createPngNode(png_path, v2(terrain.position.x, terrain.position.y), v2(terrain.scale.x, terrain.scale.y), terrain.rotation, this.terrainsNode, zIndex);
                    (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                      error: Error()
                    }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent6 = this.node.parent) == null || (_this$node$parent6 = _this$node$parent6.parent) == null ? void 0 : _this$node$parent6.name}:${this.node.name}] 静态元素 png : ${(_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
                      error: Error()
                    }), LevelUtils) : LevelUtils).extractPathPart(png_path)}`);
                  } else {
                    this._addTerrainInfo(terrain, this.terrainsNode, zIndex);
                  }
                }
              } else {
                if (terrain.position.y - terrain.height / 2 + this.node.position.y <= (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                  error: Error()
                }), GameConst) : GameConst).VIEWPORT_LOAD_POS) {
                  var _this$node$parent7;

                  const path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                    error: Error()
                  }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                    error: Error()
                  }), MyApp) : MyApp).resMgr.defaultBundleName, terrain.uuid);
                  const prefabNode = await this._createTerrainPrefabNode(path, v2(terrain.position.x, terrain.position.y), v2(terrain.scale.x, terrain.scale.y), terrain.rotation, this.terrainsNode, zIndex);
                  (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                    error: Error()
                  }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent7 = this.node.parent) == null || (_this$node$parent7 = _this$node$parent7.parent) == null ? void 0 : _this$node$parent7.name}:${this.node.name}] 静态元素 prefab Node: ${prefabNode.name}`);
                } else {
                  this._addTerrainInfo(terrain, this.terrainsNode, zIndex);
                }
              }
            } else {
              this._addTerrainInfo(terrain, this.terrainsNode, zIndex);
            }

            zIndex++;
          }
        }

        async _initDynamicsByLevelData(data, bFirstLoad) {
          var _this$node$parent8;

          if (!data || this.dynamicNode === null || data.dynamics.length === 0) {
            return;
          }

          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
            error: Error()
          }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent8 = this.node.parent) == null || (_this$node$parent8 = _this$node$parent8.parent) == null ? void 0 : _this$node$parent8.name}:${this.node.name} 随机地形初始化：${data.remark}`);
          let zIndex = 0;

          for (const dynamics of data.dynamics) {
            let weights = [];
            dynamics.group.forEach(dynamic => {
              weights.push(dynamic.weight);
            });
            const dynaIndex = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).getRandomIndexByWeights(weights, (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.random());
            const dynamic = dynamics.group[dynaIndex];
            weights = [];
            dynamic.terrains.forEach(terrain => {
              weights.push(terrain.weight);
            });
            const terrainIndex = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).getRandomIndexByWeights(weights, (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.random());
            const terrain = dynamic.terrains[terrainIndex];
            const randomOffsetX = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.random() * (terrain.offSetX.max - terrain.offSetX.min) + terrain.offSetX.min;
            const randomOffsetY = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.random() * (terrain.offSetY.max - terrain.offSetY.min) + terrain.offSetY.min;

            if (!this.node.parent || !this.node.parent.parent) {
              console.log("LevelLayerUI", "this.node.parent || !this.node.parent.parent");
              return;
            }

            if (bFirstLoad) {
              if (terrain.type.length > 0) {
                var _this$node$parent9;

                (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                  error: Error()
                }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent9 = this.node.parent) == null || (_this$node$parent9 = _this$node$parent9.parent) == null ? void 0 : _this$node$parent9.name}:${this.node.name}] 随机出的地形 : ${(_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
                  error: Error()
                }), LevelUtils) : LevelUtils).extractPathPart((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                  error: Error()
                }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                  error: Error()
                }), MyApp) : MyApp).resMgr.defaultBundleName, terrain.uuid))}`);

                if (terrain.type.endsWith('.json')) {
                  var _this$node$parent10;

                  const json_path = jsonRootPath + terrain.type.replace('.json', '');
                  (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                    error: Error()
                  }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent10 = this.node.parent) == null || (_this$node$parent10 = _this$node$parent10.parent) == null ? void 0 : _this$node$parent10.name}:${this.node.name}] jsonData : ${(_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
                    error: Error()
                  }), LevelUtils) : LevelUtils).extractPathPart(json_path)}`);
                  const jsonAsset = await (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                    error: Error()
                  }), MyApp) : MyApp).resMgr.loadAsync(json_path, JsonAsset);
                  const jsonData = jsonAsset.json;

                  if (jsonData.terrains && Array.isArray(jsonData.terrains)) {
                    var _this$node$parent11;

                    let subZIndex = 0;
                    const subParentNode = (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
                      error: Error()
                    }), LevelUtils) : LevelUtils).getOrAddNode(this.dynamicNode, `dyna_${zIndex}`);
                    (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                      error: Error()
                    }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent11 = this.node.parent) == null || (_this$node$parent11 = _this$node$parent11.parent) == null ? void 0 : _this$node$parent11.name}:${this.node.name}] init dyna 创建节点 subParentNode: ${subParentNode.name} 父节点：${subParentNode.parent.name}`);
                    subParentNode.setSiblingIndex(zIndex);

                    for (const t of jsonData.terrains) {
                      var _this$node$parent12;

                      const terrainData = Object.assign(new (_crd && LevelDataTerrain === void 0 ? (_reportPossibleCrUseOfLevelDataTerrain({
                        error: Error()
                      }), LevelDataTerrain) : LevelDataTerrain)(), t); //logDebug("LevelLayerUI", `jsonData.terrains: ${JSON.stringify(terrainData)}`);

                      const terrainPosY = dynamic.position.y + randomOffsetY + terrainData.position.y;
                      const curOffPosY = terrainPosY - terrainData.height / 2;
                      const curPosY = curOffPosY + this.node.position.y;
                      (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                        error: Error()
                      }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent12 = this.node.parent) == null || (_this$node$parent12 = _this$node$parent12.parent) == null ? void 0 : _this$node$parent12.name}:${this.node.name}] 父节点坐标：${this.node.position.y} 子节偏移坐标：${curOffPosY}`);

                      if (curPosY <= (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                        error: Error()
                      }), GameConst) : GameConst).VIEWPORT_LOAD_POS) {
                        if (terrainData.type.endsWith('.png')) {
                          var _this$node$parent13;

                          const png_path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                            error: Error()
                          }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                            error: Error()
                          }), MyApp) : MyApp).resMgr.defaultBundleName, terrainData.type.replace('.png', ''));
                          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                            error: Error()
                          }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent13 = this.node.parent) == null || (_this$node$parent13 = _this$node$parent13.parent) == null ? void 0 : _this$node$parent13.name}:${this.node.name}] 随机元素(通过json配置) png : ${(_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
                            error: Error()
                          }), LevelUtils) : LevelUtils).extractPathPart(png_path)}`);
                          await this._createPngNode(png_path, v2(dynamic.position.x + randomOffsetX + terrainData.position.x, terrainPosY), v2(terrainData.scale.x, terrainData.scale.y), terrainData.rotation, subParentNode, subZIndex);
                        } else {
                          var _this$node$parent14;

                          const path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                            error: Error()
                          }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                            error: Error()
                          }), MyApp) : MyApp).resMgr.defaultBundleName, terrainData.uuid);
                          const prefabNode = await this._createTerrainPrefabNode(path, v2(dynamic.position.x + randomOffsetX + terrainData.position.x, terrainPosY), v2(terrainData.scale.x, terrainData.scale.y), terrainData.rotation, subParentNode, subZIndex);
                          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                            error: Error()
                          }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent14 = this.node.parent) == null || (_this$node$parent14 = _this$node$parent14.parent) == null ? void 0 : _this$node$parent14.name}:${this.node.name}] 随机元素(通过json配置) prefab : ${prefabNode.name}`);
                        }
                      } else {
                        this._addTerrainInfo({
                          uuid: terrainData.uuid,
                          type: terrainData.type,
                          height: terrainData.height,
                          position: v2(dynamic.position.x + randomOffsetX + terrainData.position.x, terrainPosY),
                          scale: v2(terrainData.scale.x, terrainData.scale.y),
                          rotation: terrainData.rotation
                        }, subParentNode, subZIndex);
                      }

                      subZIndex++;
                    }
                  } else {
                    (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
                      error: Error()
                    }), logError) : logError)("LevelLayerUI", "JSON data has no terrains array");
                  }
                } else if (terrain.type.endsWith('.png')) {
                  if (dynamic.position.y - terrain.height / 2 + randomOffsetY + this.node.position.y <= (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                    error: Error()
                  }), GameConst) : GameConst).VIEWPORT_LOAD_POS) {
                    var _this$node$parent15;

                    const png_path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                      error: Error()
                    }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                      error: Error()
                    }), MyApp) : MyApp).resMgr.defaultBundleName, terrain.type.replace('.png', ''));
                    (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                      error: Error()
                    }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent15 = this.node.parent) == null || (_this$node$parent15 = _this$node$parent15.parent) == null ? void 0 : _this$node$parent15.name}:${this.node.name}] 随机元素 png : ${(_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
                      error: Error()
                    }), LevelUtils) : LevelUtils).extractPathPart(png_path)}`);
                    await this._createPngNode(png_path, v2(dynamic.position.x + randomOffsetX, dynamic.position.y + randomOffsetY), v2(dynamic.scale.x, dynamic.scale.y), dynamic.rotation, this.dynamicNode, zIndex);
                  } else {
                    this._addTerrainInfo({
                      uuid: terrain.uuid,
                      type: terrain.type,
                      height: terrain.height,
                      position: v2(dynamic.position.x + randomOffsetX, dynamic.position.y + randomOffsetY),
                      scale: v2(dynamic.scale.x, dynamic.scale.y),
                      rotation: dynamic.rotation
                    }, this.dynamicNode, zIndex);
                  }
                }
              } else {
                if (dynamic.position.y + randomOffsetX - terrain.height / 2 + this.node.position.y <= (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                  error: Error()
                }), GameConst) : GameConst).VIEWPORT_LOAD_POS) {
                  var _this$node$parent16;

                  const path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                    error: Error()
                  }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                    error: Error()
                  }), MyApp) : MyApp).resMgr.defaultBundleName, terrain.uuid);
                  const prefabNode = await this._createTerrainPrefabNode(path, v2(dynamic.position.x + randomOffsetX, dynamic.position.y + randomOffsetY), v2(dynamic.scale.x, dynamic.scale.y), dynamic.rotation, this.dynamicNode, zIndex);
                  (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                    error: Error()
                  }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent16 = this.node.parent) == null || (_this$node$parent16 = _this$node$parent16.parent) == null ? void 0 : _this$node$parent16.name}:${this.node.name}] 随机元素 prefab : ${prefabNode.name}`);
                } else {
                  this._addTerrainInfo({
                    uuid: terrain.uuid,
                    type: '',
                    height: terrain.height,
                    position: v2(dynamic.position.x + randomOffsetX, dynamic.position.y + randomOffsetY),
                    scale: v2(dynamic.scale.x, dynamic.scale.y),
                    rotation: dynamic.rotation
                  }, this.dynamicNode, zIndex);
                }
              }
            } else {
              this._addTerrainInfo({
                uuid: terrain.uuid,
                type: terrain.type,
                height: terrain.height,
                position: v2(dynamic.position.x + randomOffsetX, dynamic.position.y + randomOffsetY),
                scale: v2(dynamic.scale.x, dynamic.scale.y),
                rotation: dynamic.rotation
              }, this.dynamicNode, zIndex);
            }

            zIndex++;
          }
        }

        async _initEmittierLevelData(data, bFristLevel) {
          if (!data || this.emittiersNode === null || data.emittiers.length === 0) {
            return;
          }

          let index = 0;

          for (const emittier of data.emittiers) {
            if (bFristLevel) {
              if (emittier.position.y - emittier.height / 2 + this.node.position.y < (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                error: Error()
              }), GameConst) : GameConst).VIEWPORT_LOAD_POS) {
                var _this$node$parent17;

                const path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                  error: Error()
                }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                  error: Error()
                }), MyApp) : MyApp).resMgr.defaultBundleName, emittier.uuid);
                const prefab = await this._createEmittierNode(path, v2(emittier.position.x, emittier.position.y), v2(emittier.scale.x, emittier.scale.y), emittier.rotation, index);
                (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                  error: Error()
                }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent17 = this.node.parent) == null || (_this$node$parent17 = _this$node$parent17.parent) == null ? void 0 : _this$node$parent17.name}:${this.node.name}] 发射器 prefab : ${prefab.name}`);
              } else {
                this._addEmittierInfo(emittier);
              }
            } else {
              this._addEmittierInfo(emittier);
            }

            index++;
          }

          this._emittierInfo.sort((a, b) => {
            return a.position.y - b.position.y;
          });
        }

        async _initScorllsByLevelData(data, bFristLevel) {
          if (!data || this.scrollsNode === null || data.scrolls.length === 0) {
            return;
          } // 根据权重随机出一个滚动组


          const weights = [];
          data.scrolls.forEach(element => {
            weights.push(element.weight);
          });
          const srocllIndex = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).getRandomIndexByWeights(weights, (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.random());
          this._scorllData = data.scrolls[srocllIndex];

          for (let i = 0; i < this._scorllData.uuids.length; i++) {
            const path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.defaultBundleName, this._scorllData.uuids[i]);
            const prefab = await (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.loadAsync(path, Prefab);

            this._scorllPrefabs.push(prefab);
          }

          var offSetY = 0;
          var halfHeight = 0;

          while (this._scorllPrefabs.length > 0 && offSetY - halfHeight < (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).VIEWPORT_LOAD_POS) {
            const randomOffsetX = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.random() * (this._scorllData.offSetX.max - this._scorllData.offSetX.min) + this._scorllData.offSetX.min;

            const node = this._addScrollNode(randomOffsetX, offSetY);

            var nodeHeight = 0;

            if (this._scorllData.splicingMode === (_crd && LayerSplicingMode === void 0 ? (_reportPossibleCrUseOfLayerSplicingMode({
              error: Error()
            }), LayerSplicingMode) : LayerSplicingMode).node_height) {
              nodeHeight = node.getComponent(UITransform).contentSize.height;
            } else if (this._scorllData.splicingMode === (_crd && LayerSplicingMode === void 0 ? (_reportPossibleCrUseOfLayerSplicingMode({
              error: Error()
            }), LayerSplicingMode) : LayerSplicingMode).fix_height) {
              nodeHeight = 1334;
            } else if (this._scorllData.splicingMode === (_crd && LayerSplicingMode === void 0 ? (_reportPossibleCrUseOfLayerSplicingMode({
              error: Error()
            }), LayerSplicingMode) : LayerSplicingMode).random_height) {
              nodeHeight = Math.max(this._scorllData.offSetY.min, this._scorllData.offSetY.max) + node.getComponent(UITransform).contentSize.height;
            }

            offSetY += nodeHeight;
            halfHeight = nodeHeight / 2;
            this._lastScrollNodeHeight = nodeHeight;
          }
        }

        tick(deltaTime) {
          let posY = this.node.position.y;
          posY -= deltaTime * this.speed;
          this.node.setPosition(0, posY, 0); // 说明: event的激活，是从进入世界范围开始。
          // 又：因为游戏是从上往下滚动，因为我们用一个固定大小变量定义上边界，一旦进入这个范围，就激活。

          const scrollY = -posY + (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).VIEWPORT_TOP; // console.log('LevelLayerUI tick scrollY', scrollY, -posY, this._offSetY, GameConst.VIEWPORT_TOP);

          for (let i = 0; i < this.eventRunners.length; i++) {
            const eventRunner = this.eventRunners[i];
            eventRunner.tick(scrollY);

            if (eventRunner.isTriggered) {
              // 条件已触发
              this.eventRunners.splice(i, 1);
              i--;
            }
          }

          if (!this._insTerrainLock) {
            this._checkDynaTerrain(posY);
          }

          this._checkScroll();

          if (this._insEmittierLock) {
            this._checkEmittier(posY);
          }
        }

        _sortTerrainsInfo() {
          this._terrainsInfo.sort((a, b) => {
            return a.data.position.y - b.data.position.y;
          });

          (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
            error: Error()
          }), logInfo) : logInfo)("LevelLayerUI", `${this.node.name} 地形元素数量: ${this._terrainsInfo.length}`);
          /*this._terrainsInfo.forEach((terrain, index) => {
              logInfo("LevelLayerUI", `[${index}] Y坐标: ${terrain.data.position.y} 元素类型：${terrain.parentNode} uuid:${terrain.data.uuid} json:${terrain.data.type}`);
          });*/
        }

        _addTerrainInfo(terrainData, parentNode, zIndex) {
          var _this$node$parent18;

          let attr = '';
          let elemName = '';

          if (terrainData.type.length > 0) {
            attr = '图片';
            elemName = (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
              error: Error()
            }), LevelUtils) : LevelUtils).extractPathPart((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.defaultBundleName, terrainData.type.replace('.png', '')));
          } else {
            attr = '预制体';
            elemName = (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
              error: Error()
            }), LevelUtils) : LevelUtils).extractPathPart((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.defaultBundleName, terrainData.uuid), '');
          }

          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
            error: Error()
          }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent18 = this.node.parent) == null || (_this$node$parent18 = _this$node$parent18.parent) == null ? void 0 : _this$node$parent18.name}:${this.node.name}] 添加到预加载地形组 类型: ${attr} 元素: ${elemName}  父节点：${parentNode.name}  zIndex: ${zIndex}`);

          this._terrainsInfo.push({
            data: terrainData,
            parentNode: parentNode,
            zIndex: zIndex
          });
        }

        _addEmittierInfo(terrainData) {
          var _this$node$parent19;

          this._emittierInfo.push(terrainData);

          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
            error: Error()
          }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent19 = this.node.parent) == null || (_this$node$parent19 = _this$node$parent19.parent) == null ? void 0 : _this$node$parent19.name}:${this.node.name}] 添加到预加载发射器组 元素: ${(_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
            error: Error()
          }), LevelUtils) : LevelUtils).extractPathPart((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.defaultBundleName, terrainData.uuid))}`);
        } // 动态实例化场景元素，当元素的位置在一个屏幕以上位置时，就实例化


        async _checkDynaTerrain(curPosY) {
          if (!this.node.parent || !this.node.parent.parent) return;
          if (this._insTerrainLock) return;
          this._insTerrainLock = true;

          try {
            const indicesToRemove = [];
            const newTerrainsInfo = [];

            for (let i = 0; i < this._terrainsInfo.length; i++) {
              const terrainInfo = this._terrainsInfo[i]; // 因为列表排过序，先简单判断一次只要保存的元素第一个不在预判屏幕内，就跳出循环，因为后续的元素也都不会在预判屏幕内
              // 逻辑后面会根据每个节点的高度再仔细判断一次

              if (terrainInfo.data.position.y - terrainInfo.data.height / 2 + curPosY > (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                error: Error()
              }), GameConst) : GameConst).VIEWPORT_LOAD_POS) {
                break;
              }

              if (terrainInfo.data.type.length > 0) {
                if (terrainInfo.data.type.endsWith('.json')) {
                  const json_path = jsonRootPath + terrainInfo.data.type.replace('.json', '');
                  const jsonAsset = await (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                    error: Error()
                  }), MyApp) : MyApp).resMgr.loadAsync(json_path, JsonAsset);
                  const jsonData = jsonAsset.json;

                  if (jsonData.terrains && Array.isArray(jsonData.terrains)) {
                    var _this$node$parent20;

                    let subZIndex = 0;
                    const subParentNode = (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
                      error: Error()
                    }), LevelUtils) : LevelUtils).getOrAddNode(terrainInfo.parentNode, `dyna_${terrainInfo.zIndex}`);
                    (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                      error: Error()
                    }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent20 = this.node.parent) == null || (_this$node$parent20 = _this$node$parent20.parent) == null ? void 0 : _this$node$parent20.name}:${this.node.name}] _instantiate 创建节点 subParentNode: ${subParentNode.name} 父节点：${subParentNode.parent.name}`);
                    subParentNode.setSiblingIndex(terrainInfo.zIndex);

                    for (const t of jsonData.terrains) {
                      const terrainData = Object.assign(new (_crd && LevelDataTerrain === void 0 ? (_reportPossibleCrUseOfLevelDataTerrain({
                        error: Error()
                      }), LevelDataTerrain) : LevelDataTerrain)(), t);

                      if (terrainInfo.data.position.y + terrainData.position.y - terrainData.height / 2 + curPosY <= (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                        error: Error()
                      }), GameConst) : GameConst).VIEWPORT_LOAD_POS) {
                        if (terrainData.type.endsWith('.png')) {
                          var _this$node$parent21;

                          const png_path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                            error: Error()
                          }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                            error: Error()
                          }), MyApp) : MyApp).resMgr.defaultBundleName, terrainData.type.replace('.png', ''));
                          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                            error: Error()
                          }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent21 = this.node.parent) == null || (_this$node$parent21 = _this$node$parent21.parent) == null ? void 0 : _this$node$parent21.name}:${this.node.name}] 随机元素 png 路径: ${png_path} index: ${i}`);
                          await this._createPngNode(png_path, v2(terrainInfo.data.position.x + terrainData.position.x, terrainInfo.data.position.y + terrainData.position.y), v2(terrainData.scale.x, terrainData.scale.y), terrainData.rotation, subParentNode, subZIndex);
                        } else {
                          var _this$node$parent22;

                          const path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                            error: Error()
                          }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                            error: Error()
                          }), MyApp) : MyApp).resMgr.defaultBundleName, terrainData.uuid);
                          const prefabNode = await this._createTerrainPrefabNode(path, v2(terrainInfo.data.position.x + terrainData.position.x, terrainInfo.data.position.y + terrainData.position.y), v2(terrainData.scale.x, terrainData.scale.y), terrainData.rotation, subParentNode, subZIndex);
                          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                            error: Error()
                          }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent22 = this.node.parent) == null || (_this$node$parent22 = _this$node$parent22.parent) == null ? void 0 : _this$node$parent22.name}:${this.node.name}] 实时创建元素(通过json配置) prefab Node: ${prefabNode.name} index: ${i}`);
                        }
                      } else {
                        var _this$node$parent23;

                        const name = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                          error: Error()
                        }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                          error: Error()
                        }), MyApp) : MyApp).resMgr.defaultBundleName, terrainData.uuid);
                        (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                          error: Error()
                        }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent23 = this.node.parent) == null || (_this$node$parent23 = _this$node$parent23.parent) == null ? void 0 : _this$node$parent23.name}:${this.node.name}] push TerrainsInfo name: ${name} parentName: ${subParentNode.name} index: ${i} subZIndex: ${subZIndex}`);
                        newTerrainsInfo.push({
                          data: {
                            uuid: terrainData.uuid,
                            type: terrainData.type,
                            height: terrainData.height,
                            position: v2(terrainInfo.data.position.x + terrainData.position.x, terrainInfo.data.position.y + terrainData.position.y),
                            scale: v2(terrainData.scale.x, terrainData.scale.y),
                            rotation: terrainData.rotation
                          },
                          parentNode: subParentNode,
                          zIndex: subZIndex
                        });
                      }

                      subZIndex++;
                    }

                    indicesToRemove.push(i);
                  }
                } else if (terrainInfo.data.type.endsWith('.png')) {
                  if (terrainInfo.data.position.y - terrainInfo.data.height / 2 + curPosY <= (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                    error: Error()
                  }), GameConst) : GameConst).VIEWPORT_LOAD_POS) {
                    const png_path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                      error: Error()
                    }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                      error: Error()
                    }), MyApp) : MyApp).resMgr.defaultBundleName, terrainInfo.data.type.replace('.png', ''));
                    await this._createPngNode(png_path, v2(terrainInfo.data.position.x, terrainInfo.data.position.y), v2(terrainInfo.data.scale.x, terrainInfo.data.scale.y), terrainInfo.data.rotation, terrainInfo.parentNode, terrainInfo.zIndex);
                    indicesToRemove.push(i);
                  }
                }
              } else {
                if (terrainInfo.data.position.y - terrainInfo.data.height / 2 + curPosY <= (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                  error: Error()
                }), GameConst) : GameConst).VIEWPORT_LOAD_POS) {
                  var _this$node$parent24;

                  const path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                    error: Error()
                  }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                    error: Error()
                  }), MyApp) : MyApp).resMgr.defaultBundleName, terrainInfo.data.uuid);
                  const prefabNode = await this._createTerrainPrefabNode(path, v2(terrainInfo.data.position.x, terrainInfo.data.position.y), v2(terrainInfo.data.scale.x, terrainInfo.data.scale.y), terrainInfo.data.rotation, terrainInfo.parentNode, terrainInfo.zIndex);
                  (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                    error: Error()
                  }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent24 = this.node.parent) == null || (_this$node$parent24 = _this$node$parent24.parent) == null ? void 0 : _this$node$parent24.name}:${this.node.name}] 实时创建元素 prefab Node: ${prefabNode.name} 父节点：${terrainInfo.parentNode.name} 节点顺序：${terrainInfo.zIndex}`);
                  indicesToRemove.push(i);
                }
              }
            }

            for (let i = indicesToRemove.length - 1; i >= 0; i--) {
              this._terrainsInfo.splice(indicesToRemove[i], 1);
            }

            if (newTerrainsInfo.length > 0) {
              this._terrainsInfo.push(...newTerrainsInfo);

              this._sortTerrainsInfo();
            }
          } catch (error) {
            (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
              error: Error()
            }), logError) : logError)("LevelLayerUI", `_instantiate Terrain error: ${error}`);
          } finally {
            this._insTerrainLock = false;
          }
        }

        _checkScroll() {
          if (!this.scrollsNode || this._scorllPrefabs.length === 0) return;
          const lastChild = this.scrollsNode.children[this.scrollsNode.children.length - 1];
          const lastChildTransform = lastChild.getComponent(UITransform);
          if (!lastChildTransform) return;
          const lastChildTop = this.node.position.y + lastChild.position.y + this._lastScrollNodeHeight / 2;

          if (lastChildTop < (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).VIEWPORT_TOP) {
            const newY = lastChild.position.y + this._lastScrollNodeHeight;

            const randomOffsetX = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.random() * (this._scorllData.offSetX.max - this._scorllData.offSetX.min) + this._scorllData.offSetX.min;

            const newNode = this._addScrollNode(randomOffsetX, newY);

            var nodeHeight = 0;

            if (this._scorllData.splicingMode === (_crd && LayerSplicingMode === void 0 ? (_reportPossibleCrUseOfLayerSplicingMode({
              error: Error()
            }), LayerSplicingMode) : LayerSplicingMode).node_height) {
              nodeHeight = newNode.getComponent(UITransform).contentSize.height;
            } else if (this._scorllData.splicingMode === (_crd && LayerSplicingMode === void 0 ? (_reportPossibleCrUseOfLayerSplicingMode({
              error: Error()
            }), LayerSplicingMode) : LayerSplicingMode).fix_height) {
              nodeHeight = 1334;
            } else if (this._scorllData.splicingMode === (_crd && LayerSplicingMode === void 0 ? (_reportPossibleCrUseOfLayerSplicingMode({
              error: Error()
            }), LayerSplicingMode) : LayerSplicingMode).random_height) {
              nodeHeight = Math.max(this._scorllData.offSetY.min, this._scorllData.offSetY.max) + newNode.getComponent(UITransform).contentSize.height;
            }

            this._lastScrollNodeHeight = nodeHeight;
          }
        }

        async _checkEmittier(posY) {
          if (!this.emittiersNode || this._emittierInfo.length === 0) return;
          if (!this._insEmittierLock) return;
          this._insEmittierLock = true;

          try {
            const indicesToRemove = [];

            for (let i = 0; i < this._emittierInfo.length; i++) {
              const emittierInfo = this._emittierInfo[i];

              if (emittierInfo.position.y - emittierInfo.height / 2 + posY > (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                error: Error()
              }), GameConst) : GameConst).VIEWPORT_LOAD_POS) {
                break;
              } else {
                var _this$node$parent25;

                const path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                  error: Error()
                }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                  error: Error()
                }), MyApp) : MyApp).resMgr.defaultBundleName, emittierInfo.uuid);
                const emittierNode = await this._createEmittierNode(path, v2(emittierInfo.position.x, emittierInfo.position.y), v2(emittierInfo.scale.x, emittierInfo.scale.y), emittierInfo.rotation, i);
                (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                  error: Error()
                }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent25 = this.node.parent) == null || (_this$node$parent25 = _this$node$parent25.parent) == null ? void 0 : _this$node$parent25.name}:${this.node.name}] 实时创建发射器 prefab Node: ${emittierNode.name} 父节点：${this.emittiersNode.name} 节点顺序：${i}`);
                indicesToRemove.push(i);
              }
            }

            for (let i = indicesToRemove.length - 1; i >= 0; i--) {
              this._emittierInfo.splice(indicesToRemove[i], 1);
            }
          } catch (error) {
            (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
              error: Error()
            }), logError) : logError)("LevelLayerUI", `_checkEmittier error: ${error}`);
          } finally {
            this._insEmittierLock = false;
          }

          for (let i = this._inactiveEmittierNodes.length - 1; i >= 0; i--) {
            const node = this._inactiveEmittierNodes[i];

            if (!node.isValid) {
              this._inactiveEmittierNodes.splice(i, 1);

              continue;
            }

            const comp = node.getComponent(_crd && EmittierTerrain === void 0 ? (_reportPossibleCrUseOfEmittierTerrain({
              error: Error()
            }), EmittierTerrain) : EmittierTerrain);

            if (!comp) {
              this._inactiveEmittierNodes.splice(i, 1);

              continue;
            }

            if (comp.status === (_crd && EmittierStatus === void 0 ? (_reportPossibleCrUseOfEmittierStatus({
              error: Error()
            }), EmittierStatus) : EmittierStatus).inactive && posY + node.position.y <= (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
              error: Error()
            }), GameConst) : GameConst).VIEWPORT_TOP) {
              comp.startEmittier();

              if (comp.follow === false) {
                this.speed = 0;
              }

              this._inactiveEmittierNodes.splice(i, 1);
            }
          }
        }
        /**
         * 创建并配置 PNG 类型的地形节点
         * @param spriteFramePath - SpriteFrame 资源路径
         * @param position - 节点位置
         * @param scale - 节点缩放
         * @param rotation - 节点旋转角度
         * @param parentNode - 父节点
         * @param zIndex - 在父节点中的层级顺序
         * @returns 返回创建好的地形节点
         */


        async _createPngNode(spriteFramePath, position, scale, rotation, parentNode, zIndex) {
          if (!spriteFramePath.endsWith('/spriteFrame')) {
            var _this$node$parent26;

            (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
              error: Error()
            }), logError) : logError)("LevelLayerUI", `[${(_this$node$parent26 = this.node.parent) == null || (_this$node$parent26 = _this$node$parent26.parent) == null ? void 0 : _this$node$parent26.name}:${this.node.name}] 非标准 png 路径: ${spriteFramePath}`);
            return null;
          }

          const name = (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
            error: Error()
          }), LevelUtils) : LevelUtils).extractPathPart(spriteFramePath);
          let terrainNode = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameMapManager.mapObjectPoolManager.get(TERRAIN_POOL_NAME, name);

          if (terrainNode) {
            var _this$node$parent27;

            // 2. 如果从对象池获取到节点，直接配置属性
            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent27 = this.node.parent) == null || (_this$node$parent27 = _this$node$parent27.parent) == null ? void 0 : _this$node$parent27.name}:${this.node.name}] 从对象池${TERRAIN_POOL_NAME}获取 PNG 元素节点: ${name}`);
          } else {
            var _this$node$parent28;

            const spriteFrame = await (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.loadAsync(spriteFramePath, SpriteFrame);
            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent28 = this.node.parent) == null || (_this$node$parent28 = _this$node$parent28.parent) == null ? void 0 : _this$node$parent28.name}:${this.node.name}] 创建 PNG 元素节点: ${name} 父节点：${parentNode.name} 节点顺序：${zIndex}`);
            terrainNode = new Node();
            terrainNode.name = name;
            const terrainSprite = terrainNode.addComponent(Sprite);
            terrainSprite.spriteFrame = spriteFrame;
            const uiTransform = terrainNode.getComponent(UITransform);

            if (uiTransform && spriteFrame) {
              const size = spriteFrame.originalSize;
              uiTransform.setContentSize(size.width, size.height); //logDebug("LevelLayerUI", `PNG 尺寸: ${size.width}x${size.height}`);
            }

            const checkOut = terrainNode.addComponent(_crd && LevelNodeCheckOutScreen === void 0 ? (_reportPossibleCrUseOfLevelNodeCheckOutScreen({
              error: Error()
            }), LevelNodeCheckOutScreen) : LevelNodeCheckOutScreen);
            checkOut.init(TERRAIN_POOL_NAME);
          }

          terrainNode.setPosition(position.x, position.y, 0);
          terrainNode.setScale(scale.x, scale.y);
          terrainNode.setRotationFromEuler(0, 0, rotation);
          parentNode.addChild(terrainNode);
          terrainNode.setSiblingIndex(zIndex);
          return terrainNode;
        }
        /**
         * 创建并配置 Prefab 类型的地形节点
         * @param prefabPath - Prefab 资源路径
         * @param position - 节点位置
         * @param scale - 节点缩放
         * @param rotation - 节点旋转角度
         * @param parentNode - 父节点
         * @param zIndex - 在父节点中的层级顺序
         * @returns 返回创建好的地形节点
         */


        async _createTerrainPrefabNode(prefabPath, position, scale, rotation, parentNode, zIndex) {
          const name = (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
            error: Error()
          }), LevelUtils) : LevelUtils).extractPathPart(prefabPath, '');
          let terrainNode = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameMapManager.mapObjectPoolManager.get(TERRAIN_POOL_NAME, name);

          if (!terrainNode) {
            var _this$node$parent29;

            const prefab = await (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.loadAsync(prefabPath, Prefab);
            terrainNode = instantiate(prefab);
            const checkOut = terrainNode.addComponent(_crd && LevelNodeCheckOutScreen === void 0 ? (_reportPossibleCrUseOfLevelNodeCheckOutScreen({
              error: Error()
            }), LevelNodeCheckOutScreen) : LevelNodeCheckOutScreen);
            checkOut.init(TERRAIN_POOL_NAME);
            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent29 = this.node.parent) == null || (_this$node$parent29 = _this$node$parent29.parent) == null ? void 0 : _this$node$parent29.name}:${this.node.name}] 创建 Prefab 元素节点: ${name} 父节点：${parentNode.name} 节点顺序：${zIndex}`);
          } else {
            var _this$node$parent30;

            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent30 = this.node.parent) == null || (_this$node$parent30 = _this$node$parent30.parent) == null ? void 0 : _this$node$parent30.name}:${this.node.name}] 从对象池${TERRAIN_POOL_NAME}获取 Prefab 元素节点: ${name}`);
          }

          terrainNode.setPosition(position.x, position.y, 0);
          terrainNode.setScale(scale.x, scale.y);
          terrainNode.setRotationFromEuler(0, 0, rotation);
          parentNode.addChild(terrainNode);
          terrainNode.setSiblingIndex(zIndex);
          return terrainNode;
        }
        /**
         * 创建并配置 Prefab 类型的地形节点
         * @param prefabPath - Prefab 资源路径
         * @param position - 节点位置
         * @param scale - 节点缩放
         * @param rotation - 节点旋转角度
         * @param zIndex - 在父节点中的层级顺序
         * @returns 返回创建好的地形节点
         */


        async _createEmittierNode(prefabPath, position, scale, rotation, zIndex) {
          const name = (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
            error: Error()
          }), LevelUtils) : LevelUtils).extractPathPart(prefabPath, '');
          let emittierNode = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameMapManager.mapObjectPoolManager.get(EMIITER_POOL_NAME, name);

          if (!emittierNode) {
            var _this$node$parent31;

            const prefab = await (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.loadAsync(prefabPath, Prefab);
            emittierNode = instantiate(prefab);
            const emittierTerrain = emittierNode.addComponent(_crd && EmittierTerrain === void 0 ? (_reportPossibleCrUseOfEmittierTerrain({
              error: Error()
            }), EmittierTerrain) : EmittierTerrain);
            emittierTerrain.init(EMIITER_POOL_NAME);
            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent31 = this.node.parent) == null || (_this$node$parent31 = _this$node$parent31.parent) == null ? void 0 : _this$node$parent31.name}:${this.node.name}] 创建发射器节点: ${name} 父节点：${this.emittiersNode.name} 节点顺序：${zIndex}`);
          } else {
            var _this$node$parent32;

            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent32 = this.node.parent) == null || (_this$node$parent32 = _this$node$parent32.parent) == null ? void 0 : _this$node$parent32.name}:${this.node.name}] 从对象池${EMIITER_POOL_NAME}获取发射器节点: ${name}`);
          }

          emittierNode.setPosition(position.x, position.y, 0);
          emittierNode.setScale(scale.x, scale.y);
          emittierNode.setRotationFromEuler(0, 0, rotation);
          this.emittiersNode.addChild(emittierNode);
          emittierNode.setSiblingIndex(zIndex);
          return emittierNode;
        }

        _addScrollNode(xPos, yPos) {
          const index = this.scrollsNode.children.length % this._scorllPrefabs.length;
          const prefab = this._scorllPrefabs[index];
          const prefabName = prefab.name;
          let node = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameMapManager.mapObjectPoolManager.get(SCROLL_POOL_NAME, prefabName);

          if (!node) {
            node = instantiate(this._scorllPrefabs[index]);
            const checkOut = node.addComponent(_crd && LevelNodeCheckOutScreen === void 0 ? (_reportPossibleCrUseOfLevelNodeCheckOutScreen({
              error: Error()
            }), LevelNodeCheckOutScreen) : LevelNodeCheckOutScreen);
            checkOut.init(SCROLL_POOL_NAME);
          } else {
            var _this$node$parent33;

            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)("LevelLayerUI", `[${(_this$node$parent33 = this.node.parent) == null || (_this$node$parent33 = _this$node$parent33.parent) == null ? void 0 : _this$node$parent33.name}:${this.node.name}] 从对象池${SCROLL_POOL_NAME}获取 Prefab 元素节点: ${name}`);
          }

          this.scrollsNode.addChild(node);
          node.setPosition(xPos, yPos, 0);
          return node;
        }

        getEventByElemID(elemID) {
          for (let event of this.events) {
            if (event.elemID == elemID) {
              return event;
            }
          }

          return null;
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=928ab71a9883ce6ff25bdd946042179d19325cc0.js.map
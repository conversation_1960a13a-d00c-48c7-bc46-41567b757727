{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelNodeCheckOutScreen.ts"], "names": ["_decorator", "Component", "UITransform", "Vec3", "GameConst", "logDebug", "logError", "GameIns", "ccclass", "property", "LevelNodeCheckOutScreen", "_remove_thresHold", "_height", "_worldPos", "_poolName", "init", "poolName", "uiTransform", "node", "getComponent", "height", "name", "BATTLE_VIEW_BOTTOM", "update", "deltaTime", "<PERSON><PERSON><PERSON><PERSON>", "active", "getWorldPosition", "topPosition", "y", "_recycleNode", "length", "gameMapManager", "mapObjectPoolManager", "put", "destroy"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;;AAC1CC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,Q,iBAAAA,Q;;AACVC,MAAAA,O,iBAAAA,O;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;yCAGjBU,uB,WADZF,OAAO,CAAC,yBAAD,C,gBAAR,MACaE,uBADb,SAC6CT,SAD7C,CACuD;AAAA;AAAA;AAAA,eAC3CU,iBAD2C,GACf,CADe;AAAA,eAE3CC,OAF2C,GAEzB,CAFyB;AAAA,eAG3CC,SAH2C,GAGzB,IAAIV,IAAJ,EAHyB;AAAA,eAK3CW,SAL2C,GAKvB,EALuB;AAAA;;AAO5CC,QAAAA,IAAI,CAACC,QAAD,EAAyB;AAChC,eAAKF,SAAL,GAAiBE,QAAjB;AAEA,cAAMC,WAAW,GAAG,KAAKC,IAAL,CAAUC,YAAV,CAAuBjB,WAAvB,CAApB;;AACA,cAAIe,WAAJ,EAAiB;AACb,iBAAKL,OAAL,GAAeK,WAAW,CAACG,MAA3B;AACH,WAFD,MAEO;AACH;AAAA;AAAA,sCAAS,yBAAT,mBAAwC,KAAKF,IAAL,CAAUG,IAAlD;AACH;;AAED,eAAKV,iBAAL,GAAyB;AAAA;AAAA,sCAAUW,kBAAnC;AACH;;AAEDC,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtB,cAAI,KAAKN,IAAL,CAAUO,OAAV,KAAsB,KAAtB,IAA+B,KAAKP,IAAL,CAAUQ,MAAV,KAAqB,KAAxD,EAA+D;AAC/D,cAAI,KAAKd,OAAL,KAAiB,CAArB,EAAwB;AAExB,eAAKM,IAAL,CAAUS,gBAAV,CAA2B,KAAKd,SAAhC;AAEA,cAAMe,WAAW,GAAG,KAAKf,SAAL,CAAegB,CAAf,GAAmB,KAAKjB,OAAL,GAAe,CAAtD;;AAEA,cAAIgB,WAAW,GAAG,KAAKjB,iBAAvB,EAA0C;AACtC,iBAAKmB,YAAL;AACH;AACJ;;AAEOA,QAAAA,YAAY,GAAS;AACzB,cAAI,KAAKhB,SAAL,CAAeiB,MAAf,GAAwB,CAA5B,EAA+B;AAC3B;AAAA;AAAA,sCAAS,yBAAT,oBAAyC,KAAKb,IAAL,CAAUG,IAAnD,kDAAiE,KAAKP,SAAtE;AACA;AAAA;AAAA,oCAAQkB,cAAR,CAAuBC,oBAAvB,CAA4CC,GAA5C,CAAgD,KAAKpB,SAArD,EAAgE,KAAKI,IAArE;AACH,WAHD,MAGO;AACH;AACA;AAAA;AAAA,sCAAS,yBAAT,mBAAyC,KAAKA,IAAL,CAAUG,IAAnD;AACA,iBAAKH,IAAL,CAAUiB,OAAV;AACH;AACJ;;AA1CkD,O", "sourcesContent": ["import { _decorator, Component, Node, UITransform, Vec3 } from 'cc';\r\nimport { GameConst } from 'db://assets/scripts/core/base/GameConst';\r\nimport { logDebug, logError } from 'db://assets/scripts/utils/Logger';\r\nimport { GameIns } from 'db://assets/bundles/common/script/game/GameIns';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('LevelNodeCheckOutScreen')\r\nexport class LevelNodeCheckOutScreen extends Component {\r\n    private _remove_thresHold: number = 0;\r\n    private _height: number = 0;\r\n    private _worldPos: Vec3 = new Vec3();\r\n\r\n    private _poolName: string = '';\r\n\r\n    public init(poolName: string): void {\r\n        this._poolName = poolName;\r\n\r\n        const uiTransform = this.node.getComponent(UITransform);\r\n        if (uiTransform) {\r\n            this._height = uiTransform.height;\r\n        } else {\r\n            logError('LevelNodeCheckOutScreen',`节点${this.node.name}缺少UITransform组件`);\r\n        }\r\n\r\n        this._remove_thresHold = GameConst.BATTLE_VIEW_BOTTOM;\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n        if (this.node.isValid === false || this.node.active === false) return;\r\n        if (this._height === 0) return;\r\n\r\n        this.node.getWorldPosition(this._worldPos);\r\n        \r\n        const topPosition = this._worldPos.y + this._height / 2;\r\n\r\n        if (topPosition < this._remove_thresHold) {\r\n            this._recycleNode();\r\n        }\r\n    }\r\n\r\n    private _recycleNode(): void {\r\n        if (this._poolName.length > 0) {\r\n            logDebug('LevelNodeCheckOutScreen',` 节点${this.node.name}已回收到对象池${this._poolName}`);\r\n            GameIns.gameMapManager.mapObjectPoolManager.put(this._poolName, this.node);\r\n        } else {\r\n            // 没有指定对象池时直接销毁\r\n            logDebug('LevelNodeCheckOutScreen', `节点${this.node.name}已销毁`);\r\n            this.node.destroy();\r\n        }\r\n    }\r\n}\r\n\r\n\r\n"]}
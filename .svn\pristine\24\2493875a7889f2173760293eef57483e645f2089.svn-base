import { Component, error, _decorator } from "cc";
const { ccclass, property } = _decorator;

import { ResEnemy } from "../../autogen/luban/schema";

import { MyApp } from "db://assets/bundles/common/script/app/MyApp";

import { AttributeConst } from "db://assets/bundles/common/script/const/AttributeConst";
import { PlaneBaseData } from "../../data/plane/PlaneData";
import { EnemyEventGroupData } from "../ui/plane/event/EventGroupCom";
import { PlaneProperty } from "../ui/plane/enemy/EnemyPlaneProperty";
import EnemyPlaneBase from "../ui/plane/enemy/EnemyPlaneBase";

// 挂在Prefab上的配置
@ccclass("EnemyPrefab")
export class EnemyPrefab extends Component {
    // 这两个属性策划希望放到预制体上
    @property({displayName: '摆动速度', tooltip: '控制摆动的频率'})
    public tiltSpeed: number = 0;                   // 摆动速度
    @property({displayName: '摆动幅度', tooltip: '控制摆动的幅度'})
    public tiltOffset: number = 100;                // 摆动距离

    // 事件组数据
    @property({type: [EnemyEventGroupData], override: true, displayName: '事件组列表'})
    public eventGroups: EnemyEventGroupData[] = [];
}

const PATH_SPINE = "game/prefabs/plane/enemyplane/"
export class EnemyData extends PlaneBaseData {

    config: ResEnemy | null = null;//飞机静态配置
    planeProperty: PlaneProperty | null = null; //飞机表现属性

    constructor(planeId:number) {
        super(planeId);
        this.config = MyApp.lubanTables.TbResEnemy.get(this._planeId)!;
        this.initAttributes();
        this.planeProperty = new PlaneProperty();
    }

    get recoursePrefab() {
        if (!this.config) {
            return "";
        }
        return PATH_SPINE + this.config.prefab
    }

    // 战斗相关的属性(受buffer影响的):放到Attributes
    initAttributes() {
        if (!this.config) {
            error(`enemyPlane ${this._planeId}: config is null, cannot update attributes.`);
            return;
        }
        this.addBaseAttribute(AttributeConst.MaxHPOutAdd, this.config.baseHp);
        this.addBaseAttribute(AttributeConst.AttackOutAdd, this.config.baseAtk);
        this.addBaseAttribute(AttributeConst.MoveSpeed, this.config.moveSpeed);
        this.addBaseAttribute(AttributeConst.TurnSpeed, this.config.turnSpeed);
    }

    initProperties(plane: EnemyPlaneBase) {
        this.planeProperty!.reset(plane);
    }

    update(dt: number) {
        this.planeProperty!.elapsedTime.value += dt * 1000; // 毫秒
        this.planeProperty!.notifyAll();
    }
}
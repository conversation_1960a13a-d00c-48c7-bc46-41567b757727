import { _decorator, Component, Node, Prefab, assetManager, instantiate, Vec2, UITransform, v2 } from 'cc';
const { ccclass, property, executeInEditMode } = _decorator;

import { LayerSplicingMode, LayerType, LevelDataEvent, LevelDataLayer } from 'db://assets/bundles/common/script/leveldata/leveldata';

import { LevelEditorUtils, LevelLayer, LevelScrollLayerUI } from './utils';
import { LevelEditorEventUI } from './LevelEditorEventUI';
import { LevelPrefabParse } from './LevelPrefabParse';

const TerrainsNodeName = "terrains";
const ScrollsNodeName = "scrolls";
const DynamicNodeName = "dynamic";
const EmittiersNodeName = "emittiers";
const EventNodeName = "events"

@ccclass('LevelEditorLayerUI')
@executeInEditMode()
export class LevelEditorLayerUI extends Component {
    public terrainsNode: Node|null = null;
    public scrollsNode: Node|null = null;
    public dynamicNode: Node|null = null;
    public emittiersNode: Node|null = null;
    public eventsNode: Node|null = null;

    private _loadScrollNode: boolean = false;

    onLoad(): void {
        this.terrainsNode = LevelEditorUtils.getOrAddNode(this.node, TerrainsNodeName);
        this.scrollsNode = LevelEditorUtils.getOrAddNode(this.node, ScrollsNodeName);
        this.dynamicNode = LevelEditorUtils.getOrAddNode(this.node, DynamicNodeName);
        this.emittiersNode = LevelEditorUtils.getOrAddNode(this.node, EmittiersNodeName);
        this.eventsNode = LevelEditorUtils.getOrAddNode(this.node, EventNodeName);
    }

    public initByLevelData(data: LevelDataLayer):void {
        console.log("LevelEditorLayerUI initByLevelData");
        if (!data) {
            return;
        }

        if (this.terrainsNode === null) {
            return;
        }
        data.terrains?.forEach((terrain) => {
            assetManager.loadAny({uuid:terrain.uuid}, (err: Error, prefab:Prefab) => {
                if (err) {
                    console.error("LevelEditorLayerUI initByLevelData load terrain prefab err", err);
                    return
                } 
                var terrainNode = instantiate(prefab);
                terrainNode.setPosition(terrain.position.x, terrain.position.y, 0);
                terrainNode.setScale(terrain.scale.x, terrain.scale.y, 1);
                terrainNode.setRotationFromEuler(0, 0, terrain.rotation);
                this.terrainsNode!.addChild(terrainNode);                
            });
        });

        data.dynamics?.forEach((dynamics,index) => {
            var dynaNode = LevelEditorUtils.getOrAddNode(this.dynamicNode!, `dyna_${index}`);
            dynamics.group.forEach((dynamic, dynaIndex) => {
                dynaNode.setPosition(dynamic.position.x, dynamic.position.y, 0);
                dynaNode.setScale(dynamic.scale.x, dynamic.scale.y, 1);
                dynaNode.setRotationFromEuler(0, 0, dynamic.rotation);
                dynamic.terrains.forEach((terrain,terrainIndex)=>{
                    assetManager.loadAny({uuid:terrain.uuid}, (err, prefab:Prefab) => {
                        if (err) {
                            console.error("LevelEditorLayerUI initByLevelData load dynamic prefab err", err);
                            return
                        } 
                        var dynamicNode = instantiate(prefab);
                        dynamicNode.name = `rand_${dynaIndex}_${terrainIndex}`;
                        dynaNode!.addChild(dynamicNode); 
                        const randomOffsetX = Math.random() * (terrain.offSetX.max - terrain.offSetX.min) + terrain.offSetX.min;   
                        const randomOffsetY = Math.random() * (terrain.offSetY.max - terrain.offSetY.min) + terrain.offSetY.min;
                        dynamicNode.setPosition(randomOffsetX, randomOffsetY, 0);
                    });
                });
            });
        });

        data.events?.forEach((event)=>{
            var node = new Node();
            var eventUIComp = node.addComponent(LevelEditorEventUI);
            eventUIComp.initByLevelData(event);
            this.eventsNode!.addChild(node);
        })
    }

    public async initEmittierLevelData(layerData: LevelLayer, data: LevelDataLayer):Promise<void> {
        if (!data || this.emittiersNode === null) {
            return;
        } 

        const loadPromises: Promise<void>[] = [];
        layerData.emittierLayers = [];
        
        data.emittiers?.forEach((emittier, index) => {
            const loadPromise = new Promise<void>((resolve) => {
                assetManager.loadAny({uuid: emittier.uuid}, (err, prefab:Prefab) => {
                    if (err) {
                        resolve();
                        return
                    }           
                    layerData.emittierLayers.push(prefab);  
                    var emittierNode = instantiate(prefab);
                    emittierNode.name = `emittier_${index}`;
                    emittierNode.setPosition(emittier.position.x, emittier.position.y, 0);
                    emittierNode.setScale(emittier.scale.x, emittier.scale.y, 1);
                    emittierNode.setRotationFromEuler(0, 0, emittier.rotation);
                    this.emittiersNode!.addChild(emittierNode); 
                    resolve();
                });
            });
            loadPromises.push(loadPromise);
        });

        await Promise.all(loadPromises);  
    }

    public async initScorllsByLevelData(layerData: LevelLayer, data: LevelDataLayer):Promise<void> {
        if (!data || this.scrollsNode === null || this._loadScrollNode) {
            return;
        } 

        const loadPromises: Promise<void>[] = [];
        layerData.scrollLayers = [];
        this._loadScrollNode = true;
        data.scrolls?.forEach((scroll) => {
            const scrollLayers = new LevelScrollLayerUI(); 
            scrollLayers.weight = scroll.weight;
            const uuids = scroll.uuids || []; 
            scrollLayers.splicingMode = scroll.splicingMode;
            scrollLayers.splicingOffsetX!.min = scroll.offSetX.min;
            scrollLayers.splicingOffsetX!.max = scroll.offSetX.max;
            scrollLayers.splicingOffsetY!.min = scroll.offSetY.min;
            scrollLayers.splicingOffsetY!.max = scroll.offSetY.max;
            scrollLayers.scrollPrefabs = [];
            uuids.forEach((uuid) => {
                const loadPromise = new Promise<void>((resolve) => {
                    assetManager.loadAny({uuid: uuid}, (err: Error, prefab:Prefab) => {
                        if (err) {
                            resolve();
                            return
                        }           
                        scrollLayers.scrollPrefabs.push(prefab);  
                        resolve();
                    });
                });
                loadPromises.push(loadPromise);
            });
            layerData.scrollLayers.push(scrollLayers);
        });

        await Promise.all(loadPromises);  
        this._loadScrollNode = false;
        layerData.scrollLayers.forEach((scroll, index) => { 
            const scrollsNode = LevelEditorUtils.getOrAddNode(this.scrollsNode!, `scroll_${index}`);
            var totalHeight = data.speed * data.totalTime / 1000;
            console.log(`LevelEditorBaseUI _checkScrollNode totalHeight ${totalHeight} totalTime ${data.totalTime} speed ${data.speed}`);
            var posOffsetY = 0;
            var height = 0;
            let prefabIndex = 0; // 当前使用的 prefab 索引
            while (height < totalHeight) {
                // 循环使用 prefab
                const curPrefab = scroll.scrollPrefabs[prefabIndex];
                const child = instantiate(curPrefab);
                const randomOffsetX = Math.random() * (scroll.splicingOffsetX!.max - scroll.splicingOffsetX!.min) + scroll.splicingOffsetX!.min;
                child.setPosition(randomOffsetX, posOffsetY, 0);
                var offY = 0;
                if (scroll.splicingMode === LayerSplicingMode.node_height) {    
                    offY = child.getComponent(UITransform)!.contentSize.height;
                } else if (scroll.splicingMode === LayerSplicingMode.fix_height) {
                    offY = 1334;
                } else if (scroll.splicingMode === LayerSplicingMode.random_height) {
                    offY = Math.max(scroll.splicingOffsetY!.min,scroll.splicingOffsetY!.max) + child.getComponent(UITransform)!.contentSize.height;
                }
                scrollsNode.addChild(child);
                posOffsetY += offY;
                height += offY;
                prefabIndex = (prefabIndex + 1) % scroll.scrollPrefabs.length;
            }
        });
    }

    public fillLevelData(data: LevelDataLayer):void {
        data.terrains = []
        this.terrainsNode!.children.forEach((terrainNode) => {
            // @ts-ignore
            let uuid = terrainNode._prefab.asset._uuid;
            if (terrainNode.getComponent(LevelPrefabParse)) {
                uuid = `${uuid}.json`;
            }
            data.terrains.push({
                uuid: uuid,
                position: new Vec2(terrainNode.position.x, terrainNode.position.y),
                scale: new Vec2(terrainNode.scale.x, terrainNode.scale.y),  
                rotation: terrainNode.rotation.z
            })
        })

        //data.dynamics = [] 在上层有保存其它信息，所以这里不清空
        if (data.type === LayerType.Random) {
            this.dynamicNode!.children.forEach((dynamic, index) => { 
                dynamic.children.forEach((dyna, dynaIndex) => {
                    const match = dyna.name.match(/^rand_(\d+)_(\d+)$/); 
                    let groupIndex = 0;
                    if (match) {
                        groupIndex = parseInt(match[1]);
                    }

                    if (dyna.getComponent(LevelPrefabParse)) {
                        // @ts-ignore
                        data.dynamics[index]!.group[groupIndex]!.terrains[dynaIndex].uuid = `${dyna._prefab.asset._uuid}.json`;
                    }
                    
                    //console.log("LevelEditorLayerUI fillLevelData data.dynamics", data.dynamics.length);
                    data.dynamics[index]!.group[groupIndex]!.position = v2(dynamic.position.x, dynamic.position.y);
                    data.dynamics[index]!.group[groupIndex]!.scale = v2(dynamic.scale.x, dynamic.scale.y);
                    data.dynamics[index]!.group[groupIndex]!.rotation = dynamic.rotation.z;
                }) 
            });
        }

        if (data.type === LayerType.Emittier) {
            data.emittiers = [];
            this.emittiersNode!.children.forEach((emittierNode) => {
                data.emittiers.push({ 
                    // @ts-ignore
                    uuid: emittierNode._prefab.asset._uuid,
                    position: v2(emittierNode.position.x, emittierNode.position.y),
                    scale: v2(emittierNode.scale.x, emittierNode.scale.y),
                    rotation: emittierNode.rotation.z,
                })
            })
        }

        data.events = [];
        this.eventsNode!.children.forEach((eventNode) => {
            var event = new LevelDataEvent();
            var eventUIComp = eventNode.getComponent(LevelEditorEventUI);
            eventUIComp!.fillLevelData(event);
            data.events.push(event);
        })
    }

    public tick(progress: number, totalTime:number, speed:number):void {
        this.node.setPosition(0, - progress * totalTime * speed / 1000, 0);
    }

    // tick 一直在执行，play则是开启预览了执行
    // 这里的progress和上面tick里的progress不同
    public play(bPlay: boolean, progress: number) {
        // tick eventsNode
        this.eventsNode?.children.forEach((eventNode) => {
            var eventUIComp = eventNode.getComponent(LevelEditorEventUI);
            eventUIComp!.play(bPlay, progress);
        })
    }
}
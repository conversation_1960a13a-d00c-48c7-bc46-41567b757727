import { Emitter } from "db://assets/bundles/common/script/game/bullet/Emitter";
import { Bullet } from "db://assets/bundles/common/script/game/bullet/Bullet";
import PlaneBase from "db://assets/bundles/common/script/game/ui/plane/PlaneBase";

export interface IEventGroupContext {
    // 考虑把emitter和bullet移出去，移到子弹和发射器的派生类里
    // 这里只保留：玩家飞机，关卡
    playerPlane: PlaneBase | null;
    // TODO: Level, Wave

    reset(): void;
}

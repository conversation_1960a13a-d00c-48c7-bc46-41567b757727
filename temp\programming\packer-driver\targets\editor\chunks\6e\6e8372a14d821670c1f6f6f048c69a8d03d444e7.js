System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, BaseComp, EventGroupBase, ConditionChain, EnemyConditionData, eEnemyCondition, EnemyActionData, eEnemyAction, enemy_cond, enemy_act, GameIns, EnemyEventGroupContext, EnemyEventInstance, EnemyConditionFactory, EnemyActionFactory, EventGroupComp, _dec, _dec2, _dec3, _dec4, _dec5, _class2, _class3, _descriptor, _descriptor2, _descriptor3, _descriptor4, _crd, ccclass, property, EnemyEventGroupData;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfEntity(extras) {
    _reporterNs.report("Entity", "db://assets/bundles/common/script/game/ui/base/Entity", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseComp(extras) {
    _reporterNs.report("BaseComp", "db://assets/bundles/common/script/game/ui/base/BaseComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyPlaneBase(extras) {
    _reporterNs.report("EnemyPlaneBase", "db://assets/bundles/common/script/game/ui/plane/enemy/EnemyPlaneBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIEventGroupContext(extras) {
    _reporterNs.report("IEventGroupContext", "db://assets/bundles/common/script/game/eventgroup/IEventGroupContext", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIEventGroupData(extras) {
    _reporterNs.report("IEventGroupData", "db://assets/bundles/common/script/game/eventgroup/IEventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventGroupBase(extras) {
    _reporterNs.report("EventGroupBase", "db://assets/bundles/common/script/game/eventgroup/IEventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIEventCondition(extras) {
    _reporterNs.report("IEventCondition", "db://assets/bundles/common/script/game/eventgroup/IEventCondition", _context.meta, extras);
  }

  function _reportPossibleCrUseOfConditionChain(extras) {
    _reporterNs.report("ConditionChain", "db://assets/bundles/common/script/game/eventgroup/IEventCondition", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIEventAction(extras) {
    _reporterNs.report("IEventAction", "db://assets/bundles/common/script/game/eventgroup/IEventAction", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneBase(extras) {
    _reporterNs.report("PlaneBase", "db://assets/bundles/common/script/game/ui/plane/PlaneBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyConditionData(extras) {
    _reporterNs.report("EnemyConditionData", "./conditions/EnemyEventConditionData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEnemyCondition(extras) {
    _reporterNs.report("eEnemyCondition", "./conditions/EnemyEventConditionData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyActionData(extras) {
    _reporterNs.report("EnemyActionData", "./actions/EnemyEventActionData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEnemyAction(extras) {
    _reporterNs.report("eEnemyAction", "./actions/EnemyEventActionData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "db://assets/bundles/common/script/game/GameIns", _context.meta, extras);
  }

  _export({
    EnemyEventGroupContext: void 0,
    EventGroupComp: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      BaseComp = _unresolved_2.default;
    }, function (_unresolved_3) {
      EventGroupBase = _unresolved_3.EventGroupBase;
    }, function (_unresolved_4) {
      ConditionChain = _unresolved_4.ConditionChain;
    }, function (_unresolved_5) {
      EnemyConditionData = _unresolved_5.EnemyConditionData;
      eEnemyCondition = _unresolved_5.eEnemyCondition;
    }, function (_unresolved_6) {
      EnemyActionData = _unresolved_6.EnemyActionData;
      eEnemyAction = _unresolved_6.eEnemyAction;
    }, function (_unresolved_7) {
      enemy_cond = _unresolved_7;
    }, function (_unresolved_8) {
      enemy_act = _unresolved_8;
    }, function (_unresolved_9) {
      GameIns = _unresolved_9.GameIns;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "e0954V0gBVLYI+UJyy+iwWi", "EventGroupCom", undefined);

      __checkObsolete__(['_decorator', 'Enum']);

      ({
        ccclass,
        property
      } = _decorator);

      /**
       * 这个文件主要用于实现敌机的一些简单的 条件->效果 机制，主要适用于敌机表现相关的，如：移动速度，摆动等等。
       * 核心接口：
       *   编辑器配置：
       *   - IEventGroupData: 事件组数据
       *   - IEventConditionData: 事件条件数据
       *   - IEventActionData: 事件效果数据
       *   运行时：
       *   - IEventGroupContext: 事件执行上下文
       *   - IEventCondition: 事件条件
       *   - IEventAction: 事件效果
       *   - EventInstance: 事件运行时的实例
       * 原则上，扩展可以通过扩展条件或者事件的枚举，并提供具体的实现即可。
       */
      _export("EnemyEventGroupContext", EnemyEventGroupContext = class EnemyEventGroupContext {
        constructor() {
          this.playerPlane = null;
          this.plane = null;
        }

        reset() {
          this.playerPlane = null;
          this.plane = null;
        }

      });

      _export("EnemyEventGroupData", EnemyEventGroupData = (_dec = ccclass('EnemyEventGroupData'), _dec2 = property({
        displayName: '事件组名称'
      }), _dec3 = property({
        displayName: '触发次数',
        tooltip: '触发次数(默认1,只能触发一次; -1表示循环触发)'
      }), _dec4 = property({
        type: [_crd && EnemyConditionData === void 0 ? (_reportPossibleCrUseOfEnemyConditionData({
          error: Error()
        }), EnemyConditionData) : EnemyConditionData],
        displayName: '条件列表'
      }), _dec5 = property({
        type: [_crd && EnemyActionData === void 0 ? (_reportPossibleCrUseOfEnemyActionData({
          error: Error()
        }), EnemyActionData) : EnemyActionData],
        displayName: '行为列表'
      }), _dec(_class2 = (_class3 = class EnemyEventGroupData {
        constructor() {
          _initializerDefineProperty(this, "name", _descriptor, this);

          _initializerDefineProperty(this, "triggerCount", _descriptor2, this);

          _initializerDefineProperty(this, "conditions", _descriptor3, this);

          _initializerDefineProperty(this, "actions", _descriptor4, this);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class3.prototype, "name", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return "";
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class3.prototype, "triggerCount", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 1;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class3.prototype, "conditions", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class3.prototype, "actions", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      })), _class3)) || _class2));

      EnemyEventInstance = class EnemyEventInstance extends (_crd && EventGroupBase === void 0 ? (_reportPossibleCrUseOfEventGroupBase({
        error: Error()
      }), EventGroupBase) : EventGroupBase) {
        buildConditions() {
          const chain = new (_crd && ConditionChain === void 0 ? (_reportPossibleCrUseOfConditionChain({
            error: Error()
          }), ConditionChain) : ConditionChain)();
          this.data.conditions.forEach((condData, index) => {
            const condition = EnemyConditionFactory.create(condData);

            if (condition) {
              condition.onLoad(this.context);
              chain.conditions.push(condition);
            }
          });
          return chain;
        }

        buildActions() {
          return this.data.actions.map(actionData => {
            let action = EnemyActionFactory.create(actionData);
            return action;
          });
        }

      };
      EnemyConditionFactory = class EnemyConditionFactory {
        static create(data) {
          switch (data.type) {
            case (_crd && eEnemyCondition === void 0 ? (_reportPossibleCrUseOfeEnemyCondition({
              error: Error()
            }), eEnemyCondition) : eEnemyCondition).ElapsedTime:
              return new enemy_cond.EnemyCondition_ElapsedTime(data);

            case (_crd && eEnemyCondition === void 0 ? (_reportPossibleCrUseOfeEnemyCondition({
              error: Error()
            }), eEnemyCondition) : eEnemyCondition).Pos_X:
              return new enemy_cond.EnemyCondition_Pos_X(data);

            case (_crd && eEnemyCondition === void 0 ? (_reportPossibleCrUseOfeEnemyCondition({
              error: Error()
            }), eEnemyCondition) : eEnemyCondition).Pos_Y:
              return new enemy_cond.EnemyCondition_Pos_Y(data);

            case (_crd && eEnemyCondition === void 0 ? (_reportPossibleCrUseOfeEnemyCondition({
              error: Error()
            }), eEnemyCondition) : eEnemyCondition).CurHP:
              return new enemy_cond.EnemyCondition_CurHP(data);

            case (_crd && eEnemyCondition === void 0 ? (_reportPossibleCrUseOfeEnemyCondition({
              error: Error()
            }), eEnemyCondition) : eEnemyCondition).CurHPPercent:
              return new enemy_cond.EnemyCondition_CurHPPercent(data);

            case (_crd && eEnemyCondition === void 0 ? (_reportPossibleCrUseOfeEnemyCondition({
              error: Error()
            }), eEnemyCondition) : eEnemyCondition).Speed:
              return new enemy_cond.EnemyCondition_Speed(data);

            case (_crd && eEnemyCondition === void 0 ? (_reportPossibleCrUseOfeEnemyCondition({
              error: Error()
            }), eEnemyCondition) : eEnemyCondition).SpeedAngle:
              return new enemy_cond.EnemyCondition_SpeedAngle(data);

            case (_crd && eEnemyCondition === void 0 ? (_reportPossibleCrUseOfeEnemyCondition({
              error: Error()
            }), eEnemyCondition) : eEnemyCondition).Acceleration:
              return new enemy_cond.EnemyCondition_Acceleration(data);

            case (_crd && eEnemyCondition === void 0 ? (_reportPossibleCrUseOfeEnemyCondition({
              error: Error()
            }), eEnemyCondition) : eEnemyCondition).AccelerationAngle:
              return new enemy_cond.EnemyCondition_AccelerationAngle(data);

            case (_crd && eEnemyCondition === void 0 ? (_reportPossibleCrUseOfeEnemyCondition({
              error: Error()
            }), eEnemyCondition) : eEnemyCondition).DistanceToPlayer:
              return new enemy_cond.EnemyCondition_DistanceToPlayer(data);

            case (_crd && eEnemyCondition === void 0 ? (_reportPossibleCrUseOfeEnemyCondition({
              error: Error()
            }), eEnemyCondition) : eEnemyCondition).AngleToPlayer:
              return new enemy_cond.EnemyCondition_AngleToPlayer(data);

            case (_crd && eEnemyCondition === void 0 ? (_reportPossibleCrUseOfeEnemyCondition({
              error: Error()
            }), eEnemyCondition) : eEnemyCondition).Player_Pos_X:
              return new enemy_cond.EnemyCondition_Player_Pos_X(data);

            case (_crd && eEnemyCondition === void 0 ? (_reportPossibleCrUseOfeEnemyCondition({
              error: Error()
            }), eEnemyCondition) : eEnemyCondition).Player_Pos_Y:
              return new enemy_cond.EnemyCondition_Player_Pos_Y(data);

            case (_crd && eEnemyCondition === void 0 ? (_reportPossibleCrUseOfeEnemyCondition({
              error: Error()
            }), eEnemyCondition) : eEnemyCondition).Player_CurHPPercent:
              return new enemy_cond.EnemyCondition_Player_CurHPPercent(data);

            case (_crd && eEnemyCondition === void 0 ? (_reportPossibleCrUseOfeEnemyCondition({
              error: Error()
            }), eEnemyCondition) : eEnemyCondition).Level_ElapsedTime:
              return new enemy_cond.EnemyCondition_Level_ElapsedTime(data);

            case (_crd && eEnemyCondition === void 0 ? (_reportPossibleCrUseOfeEnemyCondition({
              error: Error()
            }), eEnemyCondition) : eEnemyCondition).Level_InfLevel:
              return new enemy_cond.EnemyCondition_Level_InfLevel(data);

            case (_crd && eEnemyCondition === void 0 ? (_reportPossibleCrUseOfeEnemyCondition({
              error: Error()
            }), eEnemyCondition) : eEnemyCondition).Level_ChallengeLevelSection:
              return new enemy_cond.EnemyCondition_Level_ChallengeLevelSection(data);

            case (_crd && eEnemyCondition === void 0 ? (_reportPossibleCrUseOfeEnemyCondition({
              error: Error()
            }), eEnemyCondition) : eEnemyCondition).SelectLevel:
              return new enemy_cond.EnemyCondition_SelectLevel(data);

            case (_crd && eEnemyCondition === void 0 ? (_reportPossibleCrUseOfeEnemyCondition({
              error: Error()
            }), eEnemyCondition) : eEnemyCondition).ImmuneBulletDamage:
              return new enemy_cond.EnemyCondition_ImmuneBulletDamage(data);

            case (_crd && eEnemyCondition === void 0 ? (_reportPossibleCrUseOfeEnemyCondition({
              error: Error()
            }), eEnemyCondition) : eEnemyCondition).ImmuneCollideDamage:
              return new enemy_cond.EnemyCondition_ImmuneCollideDamage(data);

            case (_crd && eEnemyCondition === void 0 ? (_reportPossibleCrUseOfeEnemyCondition({
              error: Error()
            }), eEnemyCondition) : eEnemyCondition).IgnoreBullet:
              return new enemy_cond.EnemyCondition_IgnoreBullet(data);

            case (_crd && eEnemyCondition === void 0 ? (_reportPossibleCrUseOfeEnemyCondition({
              error: Error()
            }), eEnemyCondition) : eEnemyCondition).IgnoreCollide:
              return new enemy_cond.EnemyCondition_IgnoreCollide(data);

            case (_crd && eEnemyCondition === void 0 ? (_reportPossibleCrUseOfeEnemyCondition({
              error: Error()
            }), eEnemyCondition) : eEnemyCondition).ImmuneNuke:
              return new enemy_cond.EnemyCondition_ImmuneNuke(data);

            case (_crd && eEnemyCondition === void 0 ? (_reportPossibleCrUseOfeEnemyCondition({
              error: Error()
            }), eEnemyCondition) : eEnemyCondition).ImmuneActiveSkill:
              return new enemy_cond.EnemyCondition_ImmuneActiveSkill(data);

            case (_crd && eEnemyCondition === void 0 ? (_reportPossibleCrUseOfeEnemyCondition({
              error: Error()
            }), eEnemyCondition) : eEnemyCondition).Invincible:
              return new enemy_cond.EnemyCondition_Invincible(data);

            default:
              throw new Error(`Unknown condition type: ${data.type}`);
          }
        }

      };
      EnemyActionFactory = class EnemyActionFactory {
        static create(data) {
          switch (data.type) {
            case (_crd && eEnemyAction === void 0 ? (_reportPossibleCrUseOfeEnemyAction({
              error: Error()
            }), eEnemyAction) : eEnemyAction).Speed:
              return new enemy_act.EnemyAction_Speed(data);

            case (_crd && eEnemyAction === void 0 ? (_reportPossibleCrUseOfeEnemyAction({
              error: Error()
            }), eEnemyAction) : eEnemyAction).SpeedAngle:
              return new enemy_act.EnemyAction_SpeedAngle(data);

            case (_crd && eEnemyAction === void 0 ? (_reportPossibleCrUseOfeEnemyAction({
              error: Error()
            }), eEnemyAction) : eEnemyAction).Acceleration:
              return new enemy_act.EnemyAction_Acceleration(data);

            case (_crd && eEnemyAction === void 0 ? (_reportPossibleCrUseOfeEnemyAction({
              error: Error()
            }), eEnemyAction) : eEnemyAction).AccelerationAngle:
              return new enemy_act.EnemyAction_AccelerationAngle(data);

            case (_crd && eEnemyAction === void 0 ? (_reportPossibleCrUseOfeEnemyAction({
              error: Error()
            }), eEnemyAction) : eEnemyAction).Color_R:
              return new enemy_act.EnemyAction_Color_R(data);

            case (_crd && eEnemyAction === void 0 ? (_reportPossibleCrUseOfeEnemyAction({
              error: Error()
            }), eEnemyAction) : eEnemyAction).Color_G:
              return new enemy_act.EnemyAction_Color_G(data);

            case (_crd && eEnemyAction === void 0 ? (_reportPossibleCrUseOfeEnemyAction({
              error: Error()
            }), eEnemyAction) : eEnemyAction).Color_B:
              return new enemy_act.EnemyAction_Color_B(data);

            case (_crd && eEnemyAction === void 0 ? (_reportPossibleCrUseOfeEnemyAction({
              error: Error()
            }), eEnemyAction) : eEnemyAction).Scale_X:
              return new enemy_act.EnemyAction_Scale_X(data);

            case (_crd && eEnemyAction === void 0 ? (_reportPossibleCrUseOfeEnemyAction({
              error: Error()
            }), eEnemyAction) : eEnemyAction).Scale_Y:
              return new enemy_act.EnemyAction_Scale_Y(data);

            case (_crd && eEnemyAction === void 0 ? (_reportPossibleCrUseOfeEnemyAction({
              error: Error()
            }), eEnemyAction) : eEnemyAction).TiltSpeed:
              return new enemy_act.EnemyAction_TiltSpeed(data);

            case (_crd && eEnemyAction === void 0 ? (_reportPossibleCrUseOfeEnemyAction({
              error: Error()
            }), eEnemyAction) : eEnemyAction).TiltOffset:
              return new enemy_act.EnemyAction_TiltOffset(data);

            case (_crd && eEnemyAction === void 0 ? (_reportPossibleCrUseOfeEnemyAction({
              error: Error()
            }), eEnemyAction) : eEnemyAction).RotateSpeed:
              return new enemy_act.EnemyAction_RotateSpeed(data);

            case (_crd && eEnemyAction === void 0 ? (_reportPossibleCrUseOfeEnemyAction({
              error: Error()
            }), eEnemyAction) : eEnemyAction).SelectLevel:
              return new enemy_act.EnemyAction_SelectLevel(data);

            case (_crd && eEnemyAction === void 0 ? (_reportPossibleCrUseOfeEnemyAction({
              error: Error()
            }), eEnemyAction) : eEnemyAction).ImmuneBulletDamage:
              return new enemy_act.EnemyAction_ImmuneBulletDamage(data);

            case (_crd && eEnemyAction === void 0 ? (_reportPossibleCrUseOfeEnemyAction({
              error: Error()
            }), eEnemyAction) : eEnemyAction).ImmuneCollideDamage:
              return new enemy_act.EnemyAction_ImmuneCollideDamage(data);

            case (_crd && eEnemyAction === void 0 ? (_reportPossibleCrUseOfeEnemyAction({
              error: Error()
            }), eEnemyAction) : eEnemyAction).IgnoreBullet:
              return new enemy_act.EnemyAction_IgnoreBullet(data);

            case (_crd && eEnemyAction === void 0 ? (_reportPossibleCrUseOfeEnemyAction({
              error: Error()
            }), eEnemyAction) : eEnemyAction).IgnoreCollide:
              return new enemy_act.EnemyAction_IgnoreCollide(data);

            case (_crd && eEnemyAction === void 0 ? (_reportPossibleCrUseOfeEnemyAction({
              error: Error()
            }), eEnemyAction) : eEnemyAction).ImmuneNuke:
              return new enemy_act.EnemyAction_ImmuneNuke(data);

            case (_crd && eEnemyAction === void 0 ? (_reportPossibleCrUseOfeEnemyAction({
              error: Error()
            }), eEnemyAction) : eEnemyAction).ImmuneActiveSkill:
              return new enemy_act.EnemyAction_ImmuneActiveSkill(data);

            case (_crd && eEnemyAction === void 0 ? (_reportPossibleCrUseOfeEnemyAction({
              error: Error()
            }), eEnemyAction) : eEnemyAction).Invincible:
              return new enemy_act.EnemyAction_Invincible(data);

            default:
              throw new Error(`Unknown action type: ${data.type}`);
          }
        }

      }; // 挂在Entity身上，用来执行事件组的组件

      _export("EventGroupComp", EventGroupComp = class EventGroupComp extends (_crd && BaseComp === void 0 ? (_reportPossibleCrUseOfBaseComp({
        error: Error()
      }), BaseComp) : BaseComp) {
        constructor(...args) {
          super(...args);
          this.plane = null;
          this._context = new EnemyEventGroupContext();
          this._instances = [];
        }

        init(entity) {
          super.init(entity);
          this.plane = entity;
        }

        onInit() {
          var _mainPlaneManager;

          this._context.playerPlane = (_mainPlaneManager = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager) == null ? void 0 : _mainPlaneManager.mainPlane;
          this._context.plane = this.plane;

          if (this.plane && this.plane.enemyPrefabData) {
            this.plane.enemyPrefabData.eventGroups.forEach(groupData => {
              let instance = new EnemyEventInstance(this._context, groupData);
              instance.tryStart();

              this._instances.push(instance);
            });
          }
        }

        onReset() {
          this._instances.forEach(instance => instance.tryStop());

          this._instances = [];

          this._context.reset();
        }

        updateGameLogic(dt) {
          if (this._instances.length > 0) {
            this._instances.forEach(instance => {
              instance.tick(dt);
            });
          }
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=6e8372a14d821670c1f6f6f048c69a8d03d444e7.js.map
import { AttributeData } from "../base/AttributeData";

export class PlaneBaseData extends AttributeData {
    id: number = 0;//唯一id
    protected _planeId: number = 0;//飞机id

    get planeId():number {
        return this._planeId;
    }

    set planeId(value:number) {
        if (value != this._planeId) {
            this._planeId = value;
        }
    }
    
    get recoursePrefab():string {
        return ""
    }
}
import { Label, Node, RichText, Sprite, SpriteFrame } from 'cc';
import { MyApp } from '../../app/MyApp';
import { QualityType } from '../../autogen/luban/schema';
import { BundleName } from '../../const/BundleConst';
import { StateSprite } from '../../ui/common/components/base/StateSprite';

class UIToolMgr {
    /**
     * 将时间戳格式化为 "时:分:秒" 或 "0时0分0秒" 的字符串
     * @param totalSeconds 时间戳（秒）
     * @param isChineseFormat 是否使用中文格式（默认 false）
     */
    formatTime(totalSeconds: number, isChineseFormat: boolean = false): string {
        if (typeof totalSeconds !== 'number' || totalSeconds < 0) {
            return isChineseFormat ? '0时0分0秒' : '00:00:00';
        }
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = totalSeconds % 60;
        const pad = (num: number) => num.toString().padStart(2, '0');
        if (isChineseFormat) {
            return `${hours}时${pad(minutes)}分${pad(seconds)}秒`;
        } else {
            return `${pad(hours)}:${pad(minutes)}:${pad(seconds)}`;
        }
    }

    /**
     * 将时间戳格式化为 "年/月/日 时:分" 的字符串
     * @param txt 目标 Label 组件
     * @param totalSeconds 时间戳（秒）
     */
    formatDate(totalSeconds: number): string {
        const date = new Date(totalSeconds * 1000);
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        return `${year}/${month}/${day} ${hours}:${minutes}`;
    }



    /**
     * 修改富文本原字符串中指定 <color> 标签内的数值
     * @param richText RichText
     * @param index 要修改的 <color> 标签的索引（从 0 开始）
     * @param newValue 新的数值
     */
    modifyColorTag(richText: RichText, index: number, options: { value?: string; color?: string }) {
        if (richText == null || richText.string == null) return;
        const originalString = richText.string;
        const parts = originalString.split(/(<color=[^>]+>)([^<]+)(<\/color>)/g);
        const filteredParts = parts.filter(part => part !== '');
        if (index < 0 || index >= filteredParts.length / 3) {
            throw new Error(`Invalid index: ${index}`);
        }
        const tagIndex = 3 * index;
        const valueIndex = tagIndex + 1;
        if (options.color) {
            filteredParts[tagIndex] = `<color=${options.color}>`;
        }
        if (options.value) {
            filteredParts[valueIndex] = options.value;
        }
        richText.string = filteredParts.join('');
    }

    /**
     * 修改 Label 文本中的数字部分
     * @param txt 目标 Label 组件
     * @param num 替换的数字（支持 number 或 string 类型）
     * @param num2 可选，第二个替换的数字（用于替换 "数字/数字" 格式）
     */
    modifyNumber(
        txt: Label | null,
        num: number | string | null,
        num2?: number | string | null
    ): void {
        if (!txt || num === null || num === undefined) return;
        if (num2 === null || num2 === undefined) {
            const replacement = num.toString();
            if (txt.string.match(/\d+/g)) {
                txt.string = txt.string.replace(/\d+/g, replacement);
            }
        }
        else {
            const replacement1 = num.toString();
            const replacement2 = num2.toString();
            const regex = /(\d+)\/(\d+)/g;
            if (txt.string.match(regex)) {
                txt.string = txt.string.replace(regex, `${replacement1}/${replacement2}`);
            }
        }
    }


    /**
     * 修改状态精灵
     * @param target 目标节点或精灵
     * @param idx 状态索引
     */
    modifyStateSprite(target: Node | Sprite, idx: number) {
        if (target == null) return;
        let stateSprite: StateSprite | null = null;
        let sprite: Sprite | null = null;
        if (target instanceof Node) {
            stateSprite = target.getComponent(StateSprite);
            sprite = target.getComponent(Sprite);
        } else if (target instanceof Sprite) {
            stateSprite = target.getComponent(StateSprite);
            sprite = target;
        }
        if (stateSprite == null || sprite == null) return;
        stateSprite.setState(sprite, idx);
    }

    /**
     * 设置头像
     * @param sp 目标精灵
     * @param avatarbg 头像框
     */
    setAvatarBg(sp: Sprite, avatarbg: string) {
        if (sp == null) return;
        MyApp.resMgr.loadAsync(BundleName.Common, `texture/avatarbg/${avatarbg}/spriteFrame`, SpriteFrame).then((frame: SpriteFrame) => {
            sp.spriteFrame = frame;
        });
    }

    /**
     * 设置物品品质
     * @param sp 目标精灵
     * @param quality 品质
     */
    setItemQuality(sp: Sprite, quality: QualityType) {
        if (sp == null) return;
        if (quality >= QualityType.COMMON && quality <= QualityType.MYTHIC) {
            let qualityPng = "";
            if (quality == QualityType.COMMON) {
                qualityPng = "grey_bg"
            } else if (quality == QualityType.UNCOMMON) {
                qualityPng = "green_bg"
            } else if (quality == QualityType.RACE) {
                qualityPng = "blue_bg"
            } else if (quality == QualityType.EPIC) {
                qualityPng = "purple_bg"
            } else if (quality == QualityType.LEGENDARY) {
                qualityPng = "golden_bg"
            } else if (quality == QualityType.MYTHIC) {
                qualityPng = "red_bg"
            }
            MyApp.resMgr.loadAsync(BundleName.Common, `texture/item/${qualityPng}/spriteFrame`, SpriteFrame).then((frame: SpriteFrame) => {
                sp.spriteFrame = frame;
            });
        }
    }

}
export const UITools = new UIToolMgr();
import { _decorator, Component, Node, UITransform, Vec3 } from 'cc';
import { GameConst } from 'db://assets/scripts/core/base/GameConst';
import { logDebug, logError } from 'db://assets/scripts/utils/Logger';
const { ccclass, property } = _decorator;

@ccclass('LevelNodeCheckOutScreen')
export class LevelNodeCheckOutScreen extends Component {
    private _remove_thresHold: number = 0;
    private _height: number = 0;
    private _worldPos: Vec3 = new Vec3();
    
    start() {
        const uiTransform = this.node.getComponent(UITransform);
        if (uiTransform) {
            // 记录节点的高度
            this._height = uiTransform.height;
        } else {
            logError('LevelNodeCheckOutScreen',`节点${this.node.name}缺少UITransform组件`);
        }

        this._remove_thresHold = GameConst.BATTLE_VIEW_BOTTOM;
    }

    update(deltaTime: number) {
        if (this.node.isValid === false || this.node.active === false) return;
        if (this._height === 0) return;

        this.node.getWorldPosition(this._worldPos);
        
        const topPosition = this._worldPos.y + this._height / 2;

        if (topPosition < this._remove_thresHold) {
            logDebug('LevelLayerUI',` 节点${this.node.name}已移除`);
            this.node.removeFromParent();
            this.node.destroy();
        }
    }
}



{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/SettlementResultUI.ts"], "names": ["_decorator", "Label", "Node", "ProgressBar", "tween", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "BundleName", "ButtonPlus", "StatisticsUI", "ccclass", "property", "SettlementResultUI", "_scoreTween", "_expTween", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Top", "getBundleName", "Home", "getUIOption", "isClickBgCloseUI", "onLoad", "onOKClick", "stop", "closeUI", "onNextClick", "openUI", "onShow", "setNodeStar", "nodeStar1", "nodeStar2", "nodeStar3", "btnOK", "addClick", "btnNext", "scoreAdd", "string", "lblScore", "scoreHigh", "gap", "score", "value", "to", "onUpdate", "target", "undefined", "Math", "round", "toString", "start", "exp", "finalExp", "expProBar", "progress", "lblExp", "lblLevel", "onHide", "onClose", "node", "val", "getComponentInChildren"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;AAA6CC,MAAAA,K,OAAAA,K;;AACtEC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OAGH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBZ,U;;oCAIjBa,kB,WADZF,OAAO,CAAC,oBAAD,C,UAGHC,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,UAGRA,QAAQ,CAACX,KAAD,C,UAERW,QAAQ,CAACX,KAAD,C,UAERW,QAAQ,CAACX,KAAD,C,UAGRW,QAAQ,CAACV,IAAD,C,UAERU,QAAQ,CAACV,IAAD,C,UAERU,QAAQ,CAACV,IAAD,C,WAGRU,QAAQ,CAACX,KAAD,C,WAERW,QAAQ,CAACT,WAAD,C,WAERS,QAAQ,CAACX,KAAD,C,2BA1Bb,MACaY,kBADb;AAAA;AAAA,4BAC+C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eA4BnCC,WA5BmC;AA4BjB;AA5BiB,eA6BnCC,SA7BmC;AAAA;;AA6BjB;AAEN,eAANC,MAAM,GAAW;AAAE,iBAAO,8BAAP;AAAwC;;AACnD,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,GAAf;AAAoB;;AAC7B,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,IAAlB;AAAwB;;AACvC,eAAXC,WAAW,GAAU;AAC/B,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AACH;;AAESC,QAAAA,MAAM,GAAS,CAExB;;AAEc,cAATC,SAAS,GAAG;AACd;AACA,cAAI,KAAKV,WAAT,EAAsB,KAAKA,WAAL,CAAiBW,IAAjB;AACtB,cAAI,KAAKV,SAAT,EAAoB,KAAKA,SAAL,CAAeU,IAAf;AACpB;AAAA;AAAA,8BAAMC,OAAN,CAAcb,kBAAd;AACH;;AACgB,cAAXc,WAAW,GAAG;AAChB;AACA,cAAI,KAAKb,WAAT,EAAsB,KAAKA,WAAL,CAAiBW,IAAjB;AACtB,cAAI,KAAKV,SAAT,EAAoB,KAAKA,SAAL,CAAeU,IAAf;AACpB,gBAAM;AAAA;AAAA,8BAAMG,MAAN;AAAA;AAAA,2CAAN;AACA;AAAA;AAAA,8BAAMF,OAAN,CAAcb,kBAAd;AACH;;AAEW,cAANgB,MAAM,GAAkB;AAC1B,eAAKC,WAAL,CAAiB,KAAKC,SAAtB,EAAkC,CAAlC;AACA,eAAKD,WAAL,CAAiB,KAAKE,SAAtB,EAAkC,CAAlC;AACA,eAAKF,WAAL,CAAiB,KAAKG,SAAtB,EAAkC,CAAlC;AAEA,eAAKC,KAAL,CAAYC,QAAZ,CAAqB,KAAKX,SAA1B,EAAqC,IAArC;AACA,eAAKY,OAAL,CAAcD,QAAd,CAAuB,KAAKR,WAA5B,EAAyC,IAAzC;AAEA,eAAKU,QAAL,CAAeC,MAAf,GAAwB,UAAU,GAAV,GAAgB,GAAxC;AACA,eAAKC,QAAL,CAAeD,MAAf,GAAwB,GAAxB;AACA,eAAKE,SAAL,CAAgBF,MAAhB,GAAyB,WAAW,KAApC;AAEA,gBAAMG,GAAG,GAAG,GAAZ,CAZ0B,CAa1B;;AACA,gBAAMC,KAAK,GAAG,IAAd;AACA,eAAK5B,WAAL,GAAmBV,KAAK,CAAC;AAAEuC,YAAAA,KAAK,EAAE;AAAT,WAAD,CAAL,CACdC,EADc,CACXH,GADW,EACN;AAAEE,YAAAA,KAAK,EAAED;AAAT,WADM,EACY;AACvBG,YAAAA,QAAQ,EAAGC,MAAD,IAAY;AAClB,kBAAI,CAAC,KAAKP,QAAN,IAAkBO,MAAM,KAAKC,SAAjC,EAA4C;AAC5C,mBAAKR,QAAL,CAAcD,MAAd,GAAuBU,IAAI,CAACC,KAAL,CAAWH,MAAM,CAACH,KAAlB,EAAyBO,QAAzB,EAAvB;AACH;AAJsB,WADZ,EAOdC,KAPc,EAAnB;AASA,gBAAMC,GAAG,GAAG,GAAZ;AACA,gBAAMC,QAAQ,GAAG,KAAjB,CAzB0B,CAyBF;AACxB;;AACA,eAAKtC,SAAL,GAAiBX,KAAK,CAAC,KAAKkD,SAAN,CAAL,CACZV,EADY,CACTH,GADS,EACJ;AAAEc,YAAAA,QAAQ,EAAEH;AAAZ,WADI,EACe;AACxBP,YAAAA,QAAQ,EAAE,MAAM;AACZ,kBAAI,CAAC,KAAKS,SAAV,EAAqB;AACrB,mBAAKE,MAAL,CAAalB,MAAb,GAAuB,GAAEU,IAAI,CAACC,KAAL,CAAWI,QAAQ,GAAG,KAAKC,SAAL,CAAeC,QAArC,CAA+C,IAAGF,QAAS,EAApF;AACH;AAJuB,WADf,EAOZF,KAPY,EAAjB;AASA,eAAKM,QAAL,CAAenB,MAAf,GAAwB,OAAO,EAA/B;AACH;;AACW,cAANoB,MAAM,GAAkB,CAC7B;;AACY,cAAPC,OAAO,GAAkB;AAC3B;AACA,cAAI,KAAK7C,WAAT,EAAsB,KAAKA,WAAL,CAAiBW,IAAjB;AACtB,cAAI,KAAKV,SAAT,EAAoB,KAAKA,SAAL,CAAeU,IAAf;AACvB;;AACOK,QAAAA,WAAW,CAAC8B,IAAD,EAAaC,GAAb,EAA0B;AACzCD,UAAAA,IAAI,CAAEE,sBAAN,CAA6B7D,KAA7B,EAAqCqC,MAArC,GAA8CuB,GAAG,CAACX,QAAJ,EAA9C;AACH;;AAvG0C,O;;;;;iBAGhB,I;;;;;;;iBAEE,I;;;;;;;iBAGJ,I;;;;;;;iBAEA,I;;;;;;;iBAEC,I;;;;;;;iBAGD,I;;;;;;;iBAEA,I;;;;;;;iBAEA,I;;;;;;;iBAGA,I;;;;;;;iBAEO,I;;;;;;;iBAET,I", "sourcesContent": ["import { _decorator, Label, Node, ProgressBar, resources, Sprite, SpriteFrame, tween } from 'cc';\r\nimport { BaseUI, UILayer, UIMgr, UIOpt } from \"db://assets/scripts/core/base/UIMgr\";\r\nimport { BundleName } from '../../const/BundleConst';\r\nimport { ButtonPlus } from './components/button/ButtonPlus';\r\nimport { StatisticsUI } from './StatisticsUI';\r\nimport { MyApp } from '../../app/MyApp';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n\r\n@ccclass('SettlementResultUI')\r\nexport class SettlementResultUI extends BaseUI {\r\n\r\n    @property(ButtonPlus)\r\n    btnOK: ButtonPlus | null = null;\r\n    @property(ButtonPlus)\r\n    btnNext: ButtonPlus | null = null;\r\n\r\n    @property(Label)\r\n    lblScore: Label | null = null;\r\n    @property(Label)\r\n    scoreAdd: Label | null = null;\r\n    @property(Label)\r\n    scoreHigh: Label | null = null;\r\n\r\n    @property(Node)\r\n    nodeStar1: Node | null = null;\r\n    @property(Node)\r\n    nodeStar2: Node | null = null;\r\n    @property(Node)\r\n    nodeStar3: Node | null = null;\r\n\r\n    @property(Label)\r\n    lblLevel: Label | null = null;\r\n    @property(ProgressBar)\r\n    expProBar: ProgressBar | null = null;\r\n    @property(Label)\r\n    lblExp: Label | null = null;\r\n\r\n    private _scoreTween: any; // 分数动画的 tween 引用\r\n    private _expTween: any;   // 经验条动画的 tween 引用\r\n\r\n    public static getUrl(): string { return \"prefab/ui/SettlementResultUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Top }\r\n    public static getBundleName(): string { return BundleName.Home }\r\n    public static getUIOption(): UIOpt {\r\n        return { isClickBgCloseUI: false }\r\n    }\r\n\r\n    protected onLoad(): void {\r\n\r\n    }\r\n\r\n    async onOKClick() {\r\n        // 停止所有动画\r\n        if (this._scoreTween) this._scoreTween.stop();\r\n        if (this._expTween) this._expTween.stop();\r\n        UIMgr.closeUI(SettlementResultUI);\r\n    }\r\n    async onNextClick() {\r\n        // 停止所有动画\r\n        if (this._scoreTween) this._scoreTween.stop();\r\n        if (this._expTween) this._expTween.stop();\r\n        await UIMgr.openUI(StatisticsUI);\r\n        UIMgr.closeUI(SettlementResultUI);\r\n    }\r\n\r\n    async onShow(): Promise<void> {\r\n        this.setNodeStar(this.nodeStar1!, 1);\r\n        this.setNodeStar(this.nodeStar2!, 2);\r\n        this.setNodeStar(this.nodeStar3!, 3);\r\n\r\n        this.btnOK!.addClick(this.onOKClick, this);\r\n        this.btnNext!.addClick(this.onNextClick, this);\r\n\r\n        this.scoreAdd!.string = \"分数加成 \" + 123 + \"%\";\r\n        this.lblScore!.string = \"0\";\r\n        this.scoreHigh!.string = \"历史最高分 \" + 10000;\r\n\r\n        const gap = 0.5;\r\n        // 分数动画\r\n        const score = 1000;\r\n        this._scoreTween = tween({ value: 0 })\r\n            .to(gap, { value: score }, {\r\n                onUpdate: (target) => {\r\n                    if (!this.lblScore || target === undefined) return;\r\n                    this.lblScore.string = Math.round(target.value).toString();\r\n                }\r\n            })\r\n            .start();\r\n\r\n        const exp = 0.8;\r\n        const finalExp = 10000; // 最终值\r\n        // 经验条动画\r\n        this._expTween = tween(this.expProBar!)\r\n            .to(gap, { progress: exp }, {\r\n                onUpdate: () => {\r\n                    if (!this.expProBar) return;\r\n                    this.lblExp!.string = `${Math.round(finalExp * this.expProBar.progress)}/${finalExp}`;\r\n                }\r\n            })\r\n            .start();\r\n\r\n        this.lblLevel!.string = \"lv\" + 30;\r\n    }\r\n    async onHide(): Promise<void> {\r\n    }\r\n    async onClose(): Promise<void> {\r\n        // 停止所有动画\r\n        if (this._scoreTween) this._scoreTween.stop();\r\n        if (this._expTween) this._expTween.stop();\r\n    }\r\n    private setNodeStar(node: Node, val: number) {\r\n        node!.getComponentInChildren(Label)!.string = val.toString();\r\n    }\r\n}\r\n"]}
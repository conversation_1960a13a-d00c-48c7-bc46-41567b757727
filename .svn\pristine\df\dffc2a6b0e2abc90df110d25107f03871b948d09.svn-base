import { _decorator, color, find, instantiate, Label, Node } from 'cc';
import { BaseUI, UILayer, UIMgr } from 'db://assets/scripts/core/base/UIMgr';
import { BundleName } from '../../../const/BundleConst';
import { ButtonPlus } from '../../common/components/button/ButtonPlus';
import { ResWorldGroup, WordType } from '../../../autogen/luban/schema';
import { GameIns } from '../../../game/GameIns';
import { MyApp } from '../../../app/MyApp';
import { getI18StrByKey } from '../../../../../../../extensions/i18n/assets/LanguageData';
import StringUtils from '../../../../../../scripts/utils/StringUtils';

const TYPE_COLOR = {
    [WordType.None]: "#FFFB80",
    [WordType.Prop]: "#FFFB80",
    [WordType.Missile]: "#80ff86",
    [WordType.Laser]: "#59ffdb",
}

const { ccclass, property } = _decorator;

@ccclass("RogueUI")
export class RogueUI extends BaseUI {

    @property(Node)
    nodeFresh: Node | null = null;

    @property([Node])
    rogueSelectNodes: Node[] = [];
    @property(Label)
    freshTimes: Label | null = null;

    @property(Node)
    NodeSelect: Node | null = null;

    @property(Node)
    itemProgress: Node | null = null;
    @property(Node)
    NodeProgress: Node | null = null;

    _callFunc: Function | null = null;
    _groupId: number = 0;
    _maxSelectCount: number = 0;
    _selectedIds: number[] = [];
    _curRefreshTimes: number = 0;
    _maxRefreshTimes: number = 0;

    public static getUrl(): string { return "prefab/RogueUI"; };
    public static getLayer(): UILayer { return UILayer.Default }
    public static getBundleName(): string { return BundleName.GameFight }
    protected onLoad(): void {
        this.nodeFresh!.getComponentInChildren(ButtonPlus)!.addClick(this.onFreshClick, this);
        this.itemProgress!.active = false;
    }
    async onShow(groupId: number, callFunc: Function | null = null, maxSelectCount: number = 1): Promise<void> {
        this._groupId = groupId;
        this._maxSelectCount = maxSelectCount;
        this._callFunc = callFunc;
        this._selectedIds = [];
        this._maxRefreshTimes = 2;
        this.refreshRogueUI();
        this.refreshSelectUI();
        this.refreshProgressUI();
        this.refreshTimesUI();
    }
    async onHide(...args: any[]): Promise<void> {
        this.unscheduleAllCallbacks();
     }
    async onClose(...args: any[]): Promise<void> {
        GameIns.rogueManager.recycleRogueItems(this.NodeSelect!);
    }

    async closeUI() {
        UIMgr.closeUI(RogueUI)
        this._callFunc?.(GameIns.rogueManager.coverToBuffIds(this._selectedIds));
    }

    refreshRogueUI() {
        let list: ResWorldGroup[] = GameIns.rogueManager.randomWords(this._groupId);
        let len = list.length;
        for (let i = 0; i < len; i++) {
            let wordGroup = list[i];
            let item = this.rogueSelectNodes[i];
            item.active = wordGroup != null;

            if (!item.active) {
                continue;
            }
            let wordConfig = MyApp.lubanTables.TbResWord.get(wordGroup.wordId)

            let NodeItem = find("NodeItem", item)!;
            GameIns.rogueManager.recycleRogueItems(NodeItem);
            GameIns.rogueManager.setRogueItem(NodeItem, wordGroup.wordId);

            let rogueDesc = find("rogueDesc", item);
            let rogueType = find("rogueType", item);

            rogueDesc!.getComponent(Label)!.string = wordConfig!.desc;
            rogueDesc!.getComponent(Label)!.color = color(TYPE_COLOR[wordGroup.type] || "")

            item.getComponent(ButtonPlus)!.addClick(() => {
                this.selectRogue(wordGroup)
            }, this)
        }
    }

    onFreshClick() {
        if (this._selectedIds.length >= this._maxSelectCount) {
            return;
        }
        if (this._curRefreshTimes >= this._maxRefreshTimes) {
            return;
        }
        this._curRefreshTimes++;
        this.refreshRogueUI();
        this.refreshTimesUI();
    }

    selectRogue(wordGroup: ResWorldGroup) {
        if (this._selectedIds.length >= this._maxSelectCount) {
            return;
        }
        GameIns.rogueManager.selectRogue([wordGroup]);
        this._selectedIds.push(wordGroup.ID);

        this.refreshSelectUI();
        this.refreshProgressUI();
        this.refreshTimesUI();

        if (this._selectedIds.length >= this._maxSelectCount) {
            this.scheduleOnce(() => {
                this.closeUI();
            }, 0.5)
        }else{
            this.refreshRogueUI();
        }
    }

    refreshSelectUI() {
        let data = GameIns.rogueManager.getSeLectedHistory();
        GameIns.rogueManager.recycleRogueItems(this.NodeSelect!);
        data.forEach((value, key) => {
            let config = MyApp.lubanTables.TbResWordGroup.get(key);
            GameIns.rogueManager.setRogueItem(this.NodeSelect!, config!.wordId, value);
        })
    }
    refreshProgressUI() {
        this.NodeProgress!.children.forEach(item => {
            item.active = false;
        })
        let curSelectCount = this._selectedIds.length;
        for (let i = 1; i <= this._maxSelectCount; i++) {
            let item = find(`item${i}`, this.NodeProgress!);
            if (!item) {
                item = instantiate(this.itemProgress!);
                item.name = `item${i}`;
                item.setPosition(item.position.x, 0);
                this.NodeProgress!.addChild(item);
            }
            item.active = true;
            let progressBg = find("progressBg", item);
            let progressBg2 = find("progressBg2", item);
            let complete = find("complete", item);
            let LabelIndex = find("LabelIndex", item);
            progressBg!.active = i < this._maxSelectCount;
            progressBg2!.active = i < this._maxSelectCount && i <= curSelectCount;
            complete!.active = i <= curSelectCount;
            LabelIndex!.getComponent(Label)!.string = String(i);
        }
    }

    refreshTimesUI() {
        this.freshTimes!.string = StringUtils.getReplaceStr(getI18StrByKey("ROUGE_LEFT_TIMES"), [this._maxRefreshTimes - this._curRefreshTimes, this._maxRefreshTimes]);
    }
}

System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, AnimationClip, Asset, assetManager, AudioClip, Font, ImageAsset, js, JsonAsset, Material, Mesh, Prefab, resources, sp, SpriteFrame, Texture2D, warn, IMgr, ResManager, _crd;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfIMgr(extras) {
    _reporterNs.report("IMgr", "./IMgr", _context.meta, extras);
  }

  _export("ResManager", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      AnimationClip = _cc.AnimationClip;
      Asset = _cc.Asset;
      assetManager = _cc.assetManager;
      AudioClip = _cc.AudioClip;
      Font = _cc.Font;
      ImageAsset = _cc.ImageAsset;
      js = _cc.js;
      JsonAsset = _cc.JsonAsset;
      Material = _cc.Material;
      Mesh = _cc.Mesh;
      Prefab = _cc.Prefab;
      resources = _cc.resources;
      sp = _cc.sp;
      SpriteFrame = _cc.SpriteFrame;
      Texture2D = _cc.Texture2D;
      warn = _cc.warn;
    }, function (_unresolved_2) {
      IMgr = _unresolved_2.IMgr;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "c55eblgxX5Ac7WDc8hYrt25", "ResManager", undefined);

      __checkObsolete__(['__private', 'AnimationClip', 'Asset', 'AssetManager', 'assetManager', 'Atlas', 'AudioClip', 'Font', 'ImageAsset', 'js', 'JsonAsset', 'Material', 'Mesh', 'Prefab', 'resources', 'sp', 'Sprite', 'SpriteAtlas', 'SpriteFrame', 'Texture2D', 'warn']);

      /** 
       * 游戏资源管理
       * 1、加载默认resources文件夹中资源
       * 2、加载默认bundle远程资源
       * 3、主动传递bundle名时，优先加载传递bundle名资源包中的资源
       */
      _export("ResManager", ResManager = class ResManager extends (_crd && IMgr === void 0 ? (_reportPossibleCrUseOfIMgr({
        error: Error()
      }), IMgr) : IMgr) {
        constructor() {
          super(...arguments);
          //#region 资源配置数据

          /** 全局默认加载的资源包名 */
          this.defaultBundleName = "resources";
        }

        static get instance() {
          if (this._instance) {
            return this._instance;
          }

          this._instance = new ResManager();
          return this._instance;
        }
        /** 下载时的最大并发数 - 项目设置 -> 项目数据 -> 资源下载并发数，设置默认值；初始值为15 */


        get maxConcurrency() {
          return assetManager.downloader.maxConcurrency;
        }

        set maxConcurrency(value) {
          assetManager.downloader.maxConcurrency = value;
        }
        /** 下载时每帧可以启动的最大请求数 - 默认值为15 */


        get maxRequestsPerFrame() {
          return assetManager.downloader.maxRequestsPerFrame;
        }

        set maxRequestsPerFrame(value) {
          assetManager.downloader.maxRequestsPerFrame = value;
        }
        /** 失败重试次数 - 默认值为0 */


        get maxRetryCount() {
          return assetManager.downloader.maxRetryCount;
        }

        set maxRetryCount(value) {
          assetManager.downloader.maxRetryCount = value;
        }
        /** 重试的间隔时间，单位为毫秒 - 默认值为2000毫秒 */


        get retryInterval() {
          return assetManager.downloader.retryInterval;
        }

        set retryInterval(value) {
          assetManager.downloader.retryInterval = value;
        } //#region 加载远程资源

        /**
         * 加载远程资源
         * @param url           资源地址
         * @param options       资源参数，例：{ ext: ".png" }
         * @param onComplete    加载完成回调
         * @example
        var opt: IRemoteOptions = { ext: ".png" };
        var onComplete = (err: Error | null, data: ImageAsset) => {
            const texture = new Texture2D();
            texture.image = data;
            
            const spriteFrame = new SpriteFrame();
            spriteFrame.texture = texture;
            
            var sprite = this.sprite.addComponent(Sprite);
            sprite.spriteFrame = spriteFrame;
        }
        ResManager.loadRemote<ImageAsset>(this.url, opt, onComplete);
         */


        loadRemote(url) {
          var options = null;
          var onComplete = null;

          if ((arguments.length <= 1 ? 0 : arguments.length - 1) == 2) {
            options = arguments.length <= 1 ? undefined : arguments[1];
            onComplete = arguments.length <= 2 ? undefined : arguments[2];
          } else {
            onComplete = arguments.length <= 1 ? undefined : arguments[1];
          }

          assetManager.loadRemote(url, options, onComplete);
        } //#endregion
        //#region 资源包管理

        /**
         * 获取资源包
         * @param name 资源包名
         */


        getBundle(name) {
          return assetManager.bundles.get(name);
        }
        /**
         * 加载资源包
         * @param name       资源地址
         * @example
            await ResManager.loadBundle(name);
         */


        loadBundle(name) {
          return new Promise((resolve, reject) => {
            assetManager.loadBundle(name, (err, bundle) => {
              if (err) {
                resolve(null);
                return;
              }

              resolve(bundle);
            });
          });
        }
        /**
         * 释放资源包与包中所有资源
         * @param bundleName 资源地址
         */


        removeBundle(bundleName) {
          var bundle = assetManager.bundles.get(bundleName);

          if (bundle) {
            bundle.releaseAll();
            assetManager.removeBundle(bundle);
          }
        } //#endregion
        //#region 预加载资源

        /**
         * 加载一个资源
         * @param bundleName    远程包名
         * @param paths         资源路径
         * @param type          资源类型
         * @param onProgress    加载进度回调
         * @param onComplete    加载完成回调
         */


        preload(bundleName, paths, type, onProgress, onComplete) {
          var args = null;

          if (typeof paths === "string" || paths instanceof Array) {
            args = this.parseLoadResArgs(paths, type, onProgress, onComplete);
            args.bundle = bundleName;
          } else {
            args = this.parseLoadResArgs(bundleName, paths, type, onProgress);
            args.bundle = this.defaultBundleName;
          }

          args.preload = true;
          this.loadByArgs(args);
        }
        /**
         * 异步加载一个资源
         * @param bundleName    远程包名
         * @param paths         资源路径
         * @param type          资源类型
         */


        preloadAsync(bundleName, paths, type) {
          return new Promise((resolve, reject) => {
            this.preload(bundleName, paths, type, (err, data) => {
              if (err) {
                warn(err.message);
              }

              resolve(data);
            });
          });
        }
        /**
         * 预加载文件夹中的资源
         * @param bundleName    远程包名
         * @param dir           文件夹名
         * @param type          资源类型
         * @param onProgress    加载进度回调
         * @param onComplete    加载完成回调
         */


        preloadDir(bundleName, dir, type, onProgress, onComplete) {
          var args = null;

          if (typeof dir === "string") {
            args = this.parseLoadResArgs(dir, type, onProgress, onComplete);
            args.bundle = bundleName;
          } else {
            args = this.parseLoadResArgs(bundleName, dir, type, onProgress);
            args.bundle = this.defaultBundleName;
          }

          args.dir = args.paths;
          args.preload = true;
          this.loadByArgs(args);
        } //#endregion
        //#region 资源加载、获取、释放

        /**
         * 加载一个资源
         * @param bundleName    远程包名
         * @param paths         资源路径
         * @param type          资源类型
         * @param onProgress    加载进度回调
         * @param onComplete    加载完成回调
         * @example
        ResManager.load("spine_path", sp.SkeletonData, (err: Error | null, sd: sp.SkeletonData) => {
          });
         */


        load(bundleName, paths, type, onProgress, onComplete) {
          var args = null;

          if (typeof paths === "string" || paths instanceof Array) {
            args = this.parseLoadResArgs(paths, type, onProgress, onComplete);
            args.bundle = bundleName;
          } else {
            args = this.parseLoadResArgs(bundleName, paths, type, onProgress);
            args.bundle = this.defaultBundleName;
          }

          this.loadByArgs(args);
        }
        /**
         * 异步加载一个资源
         * @param bundleName    远程包名
         * @param paths         资源路径
         * @param type          资源类型
         */


        loadAsync(bundleName, paths, type) {
          return new Promise((resolve, reject) => {
            this.load(bundleName, paths, type, (err, asset) => {
              if (err) {
                warn(err.message);
              }

              resolve(asset);
            });
          });
        }
        /**
         * 加载文件夹中的资源
         * @param bundleName    远程包名
         * @param dir           文件夹名
         * @param type          资源类型
         * @param onProgress    加载进度回调
         * @param onComplete    加载完成回调
         * @example
        // 加载进度事件
        var onProgressCallback = (finished: number, total: number, item: any) => {
            console.log("资源加载进度", finished, total);
        }
          // 加载完成事件
        var onCompleteCallback = () => {
            console.log("资源加载完成");
        }
        ResManager.loadDir("game", onProgressCallback, onCompleteCallback);
         */


        loadDir(bundleName, dir, type, onProgress, onComplete) {
          var args = null;

          if (typeof dir === "string") {
            args = this.parseLoadResArgs(dir, type, onProgress, onComplete);
            args.bundle = bundleName;
          } else {
            args = this.parseLoadResArgs(bundleName, dir, type, onProgress);
            args.bundle = this.defaultBundleName;
          }

          args.dir = args.paths;
          this.loadByArgs(args);
        }
        /**
         * 通过资源相对路径释放资源
         * @param path          资源路径
         * @param bundleName    远程资源包名
         */


        release(path, bundleName) {
          if (bundleName == undefined) bundleName = this.defaultBundleName;
          var bundle = assetManager.getBundle(bundleName);

          if (bundle) {
            var asset = bundle.get(path);

            if (asset) {
              this.releasePrefabtDepsRecursively(bundleName, asset);
            }
          }
        }
        /**
         * 通过相对文件夹路径删除所有文件夹中资源
         * @param path          资源文件夹路径
         * @param bundleName    远程资源包名
         */


        releaseDir(path, bundleName) {
          if (bundleName === void 0) {
            bundleName = this.defaultBundleName;
          }

          var bundle = assetManager.getBundle(bundleName);

          if (bundle) {
            var infos = bundle.getDirWithPath(path);

            if (infos) {
              infos.map(info => {
                this.releasePrefabtDepsRecursively(bundleName, info.uuid);
              });
            }

            if (path == "" && bundleName != "resources") {
              assetManager.removeBundle(bundle);
            }
          }
        }
        /**
         * 获取资源路径
         * @param bundleName 资源包名
         * @param uuid       资源唯一编号
         * @returns 
         */


        getAssetPath(bundleName, uuid) {
          var b = this.getBundle(bundleName);
          var info = b.getAssetInfo(uuid);
          if (info === null) return ""; //@ts-ignore

          return info.path;
        }
        /** 释放预制依赖资源 */


        releasePrefabtDepsRecursively(bundleName, uuid) {
          if (uuid instanceof Asset) {
            uuid.decRef(); // assetManager.releaseAsset(uuid);
            // this.debugLogReleasedAsset(bundleName, uuid);
          } else {
            var asset = assetManager.assets.get(uuid);

            if (asset) {
              asset.decRef(); // assetManager.releaseAsset(asset);
              // this.debugLogReleasedAsset(bundleName, asset);
            }
          }
        }

        releaseAssetByForce(asset) {
          assetManager.releaseAsset(asset);
        }

        debugLogReleasedAsset(bundleName, asset) {
          if (asset.refCount == 0) {
            var path = this.getAssetPath(bundleName, asset.uuid);
            var content = "";

            if (asset instanceof JsonAsset) {
              content = "【释放资源】Json【路径】" + path;
            } else if (asset instanceof Prefab) {
              content = "【释放资源】Prefab【路径】" + path;
            } else if (asset instanceof SpriteFrame) {
              content = "【释放资源】SpriteFrame【路径】" + path;
            } else if (asset instanceof Texture2D) {
              content = "【释放资源】Texture2D【路径】" + path;
            } else if (asset instanceof ImageAsset) {
              content = "【释放资源】ImageAsset【路径】" + path;
            } else if (asset instanceof AudioClip) {
              content = "【释放资源】AudioClip【路径】" + path;
            } else if (asset instanceof AnimationClip) {
              content = "【释放资源】AnimationClip【路径】" + path;
            } else if (asset instanceof Font) {
              content = "【释放资源】Font【路径】" + path;
            } else if (asset instanceof Material) {
              content = "【释放资源】Material【路径】" + path;
            } else if (asset instanceof Mesh) {
              content = "【释放资源】Mesh【路径】" + path;
            } else if (asset instanceof sp.SkeletonData) {
              content = "【释放资源】Spine【路径】" + path;
            } else {
              content = "【释放资源】未知【路径】" + path;
            }

            console.log(content);
          }
        }
        /**
         * 获取资源
         * @param path          资源路径
         * @param type          资源类型
         * @param bundleName    远程资源包名
         */


        get(path, type, bundleName) {
          if (bundleName === void 0) {
            bundleName = this.defaultBundleName;
          }

          var bundle = assetManager.getBundle(bundleName);
          return bundle.get(path, type);
        } //#endregion


        parseLoadResArgs(paths, type, onProgress, onComplete) {
          var pathsOut = paths;
          var typeOut = type;
          var onProgressOut = onProgress;
          var onCompleteOut = onComplete;

          if (onComplete === undefined) {
            var isValidType = js.isChildClassOf(type, Asset);

            if (onProgress) {
              onCompleteOut = onProgress;

              if (isValidType) {
                onProgressOut = null;
              }
            } else if (onProgress === undefined && !isValidType) {
              onCompleteOut = type;
              onProgressOut = null;
              typeOut = null;
            }

            if (onProgress !== undefined && !isValidType) {
              onProgressOut = type;
              typeOut = null;
            }
          }

          return {
            paths: pathsOut,
            type: typeOut,
            onProgress: onProgressOut,
            onComplete: onCompleteOut
          };
        }

        loadByBundleAndArgs(bundle, args) {
          if (args.dir) {
            if (args.preload) {
              bundle.preloadDir(args.paths, args.type, args.onProgress, args.onComplete);
            } else {
              bundle.loadDir(args.paths, args.type, args.onProgress, args.onComplete);
            }
          } else {
            if (args.preload) {
              bundle.preload(args.paths, args.type, args.onProgress, args.onComplete);
            } else {
              bundle.load(args.paths, args.type, args.onProgress, args.onComplete);
            }
          }
        }

        loadByArgs(args) {
          var _this = this;

          return _asyncToGenerator(function* () {
            if (args.bundle) {
              var bundle = assetManager.bundles.get(args.bundle); // 获取缓存中的资源包

              if (bundle) {
                _this.loadByBundleAndArgs(bundle, args);
              } // 自动加载资源包
              else {
                bundle = yield _this.loadBundle(args.bundle);
                if (bundle) _this.loadByBundleAndArgs(bundle, args);
              }
            } // 默认资源包
            else {
              _this.loadByBundleAndArgs(resources, args);
            }
          })();
        }
        /** 打印缓存中所有资源信息 */


        dump() {
          assetManager.assets.forEach((value, key) => {
            console.log("\u5F15\u7528\u6570\u91CF:" + value.refCount, assetManager.assets.get(key));
          });
          console.log("\u5F53\u524D\u8D44\u6E90\u603B\u6570:" + assetManager.assets.count);
        }

      });

      ResManager._instance = void 0;

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=f0e2db9880d4139156139b8b97f6146c9ee9541a.js.map
import { _decorator, Node, tween, v3, Vec3, UIOpacity } from 'cc';
import { BaseUI, UILayer, UIMgr } from 'db://assets/scripts/core/base/UIMgr';
import { GameIns } from '../../../game/GameIns';
import { MyApp } from '../../../app/MyApp';
import { BundleName } from '../../../const/BundleConst';

const { ccclass, property } = _decorator;

@ccclass("WheelSpinnerUI")
export class WheelSpinnerUI extends BaseUI {

    @property(Node)
    NodeRewards: Node | null = null; // 转盘奖励节点容器
    @property(Node)
    NodeArrow: Node | null = null; // 箭头节点，需要旋转这个
    @property(Node)
    NodeSelect: Node | null = null;

    // 转盘配置
    private readonly REWARD_COUNT: number = 6; // 奖励数量

    // 转盘状态
    _isSpinning: boolean = false; // 是否正在旋转
    _currentRewardIndex: number = 0; // 当前奖励索引

    public static getUrl(): string { return "prefab/WheelSpinnerUI"; };
    public static getLayer(): UILayer { return UILayer.Default; }
    public static getBundleName(): string { return BundleName.GameFight }

    protected onLoad(): void {

    }

    async onShow(): Promise<void> {
        // 初始化箭头位置
        this.resetWheel();
        this.refreshSelectUI();
    }

    async onHide(...args: any[]): Promise<void> { }

    async onClose(...args: any[]): Promise<void> {
        GameIns.rogueManager.recycleRogueItems(this.NodeSelect!);
    }

    async closeUI() {
        UIMgr.closeUI(WheelSpinnerUI);
    }

    refreshSelectUI() {
        let data = GameIns.rogueManager.getSeLectedHistory();
        GameIns.rogueManager.recycleRogueItems(this.NodeSelect!);
        data.forEach((value, key) => {
            let config = MyApp.lubanTables.TbResWordGroup.get(key);
            GameIns.rogueManager.setRogueItem(this.NodeSelect!, config!.wordId, value);
        });
    }

    /**
     * 初始化箭头位置
     */
    private resetWheel(): void {
        if (!this.NodeArrow) return;
        
        this._isSpinning = false;
        this.NodeArrow.angle = 3;
        this._currentRewardIndex = 0;
    }

    onBtnStartClick(): void {
        if (this._isSpinning) return; // 如果正在旋转，则忽略点击
        
        this.startSpin();
    }

    private startSpin(): void {
        if (!this.NodeArrow) return;
        this._isSpinning = true;

        const targetRewardIndex = Math.floor(Math.random() * this.REWARD_COUNT);
        let newIndex = targetRewardIndex - this._currentRewardIndex;
        if (newIndex < 0){
            newIndex += this.REWARD_COUNT;
        }
        const targetAngle = this.NodeArrow.angle-newIndex * 60;
        
        // 为了增加动画效果，让箭头多转几圈
        const extraRotations = 3; // 额外旋转圈数
        const totalRotation = - extraRotations * 360 + targetAngle;

        // 使用缓动动画实现箭头旋转
        tween(this.NodeArrow)
            .to(extraRotations*0.8, { angle: totalRotation }, {
                easing: "cubicOut", // 缓动函数，先快后慢
                onComplete: () => {
                    // 旋转完成
                    this.onSpinComplete(targetRewardIndex);
                }
            })
            .start();
    }
    private onSpinComplete(rewardIndex: number): void {
        this._isSpinning = false;
        this._currentRewardIndex = rewardIndex;

        console.log(`转盘抽奖完成，选中奖励索引: ${rewardIndex}`);
        
        // 这里可以添加选中效果，比如高亮显示对应的奖励
        this.highlightSelectedReward(rewardIndex);
        
        // 发放奖励
        this.giveReward(rewardIndex);
        
        // 刷新选择UI
        this.refreshSelectUI();
    }

    private highlightSelectedReward(rewardIndex: number): void {
        if (!this.NodeRewards) return;
        
        const rewardNodes = this.NodeRewards.children;
        if (rewardNodes.length > rewardIndex) {
            // 这里可以添加高亮效果，比如改变颜色、缩放等
            // 示例：简单的缩放效果
            tween(rewardNodes[rewardIndex])
                .to(0.2, { scale: v3(1.2, 1.2, 1) })
                .to(0.2, { scale: v3(1, 1, 1) })
                .start();
        }
    }

    private giveReward(rewardIndex: number): void {
        console.log(`发放奖励: ${rewardIndex}`);
    }
}
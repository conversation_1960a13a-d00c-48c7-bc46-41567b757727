import { SingletonBase } from "db://assets/scripts/core/base/SingletonBase";
import { MyApp } from "../../app/MyApp";
import { Asset, assetManager, Prefab, resources } from "cc";
import { logError } from "db://assets/scripts/utils/Logger";
import { GameIns } from "../GameIns";
import { BundleName } from "../../const/BundleConst";

let GameResourceList = {
    MainPlane: "prefabs/mainPlane/MainPlane",
    PrefabBoss: "prefabs/boss/BossPlane",
    Bullet: "prefabs/Bullet",
    EnemyPlane: "prefabs/enemy/EnemyPlane",
    HurtNum: "prefabs/effect/HurtNum",
    Hurt0: "prefabs/effect/Hurt",
    EmitterPrefabPath: "prefabs/emitter/",
    font_hurtNum: "font/hurtNum",
};

class GameResTask {
    name:string;
    deps:Set<string>|null = null;
    uuids:string[]|null = null;
    paths:string[]|null = null;
    cb:(()=>void)|null = null;
    bedeps:string[] = [];
    private finCount = 0;
    constructor(name: string, uuids:string[]|null, paths:string[]|null, deps:Set<string>|null, cb:(()=>void)|null) {
        this.name = name;
        this.uuids = uuids;
        this.paths = paths;
        this.deps = deps;
        this.cb = cb;
    }
    preload(gameRes: GameRes) {
        this.finCount = 1
        if (this.uuids?.length||0 > 0) {
            this.finCount++
            const uuids:{uuid:string}[] = []
            this.uuids!.forEach((uuid) => {
                uuids.push({uuid:uuid})
            })
            assetManager.loadAny(uuids, (err:Error|null, assets:Asset[]) => {
                if (err) {
                    logError("GameRes", `preload ${this.name} err:${err}`);
                    return;
                }
                assets.forEach((asset) => {
                    gameRes.addAsset(asset.uuid, asset);
                })
                this.finOnce()
            })
        }
        if (this.paths?.length||0 > 0) {
            this.finCount++
            assetManager.loadAny(this.paths!, (err:Error|null, assets:Asset[]) => {
                if (err) {
                    logError("GameRes", `preload ${this.name} err:${err}`);
                    return;
                }
                for (let i = 0; i < this.paths!.length; i++) {
                    gameRes.addAsset(this.paths![i], assets[i]);
                }
                this.finOnce()
            })
        }
        this.finOnce();
    }
    finOnce() {
        this.finCount--
        if (this.finCount == 0) {
            if (this.cb) {
                this.cb()
            }
        }
    }
}
class GameRes {
    protected assets = new Map<string, Asset>();
    protected pathMap = new Map<string, Asset>();
    protected tasks = new Map<string, GameResTask>()
    protected bedepTasks = new Map<string, GameResTask[]>();
    addAsset(path:string, asset:Asset) {
        this.pathMap.set(path, asset);
    }
    addTask(name:string, uuids:string[]|null, paths:string[]) {
        this.tasks.set(name, new GameResTask(name, uuids, paths, null, null))
    }
    addTaskByDeps(name:string, deps:string[], cb:()=>void) {
        this.tasks.set(name, new GameResTask(name, null, null, new Set(deps), cb))
        deps.forEach((dep) => {
            let depTask = this.tasks.get(dep);
            if (!depTask) {
                logError("GameRes", `addTaskByDeps ${name} dep ${dep} not found`);
                return;
            }
            depTask.bedeps.push(name)
        })
    }
    get(path:string) {
        return this.assets.get(path);
    }
    preload(onComplete:() => void, onProgress:(progress:number) => void) {
    }
}

export class GameLevelRes extends GameRes {
    preload(onComplete: () => void, onProgress: (progress: number) => void): void {
        MyApp.resMgr.loadAsync(BundleName.Common, "prefab/Plane")
        super.preload(onComplete, onProgress);
    }
}
export class GameCommonRes extends GameRes {
}

export class GameResManager extends SingletonBase<GameResManager> {
    commonRes = new GameCommonRes();
    curLevelRes:GameLevelRes|null = null;
    nextLevelRes:GameLevelRes|null = null;

    // 常用数据
    planePrefab: Prefab|null = null
    mainPlanePrefab: Prefab|null = null
    bossPrefab: Prefab|null = null
    enemyPlane: Prefab|null = null

    constructor() {
        super();
    }
    preloadCommon(onComplete:() => void, onProgress:(progress:number) => void) {
        const startInfo = GameIns._battleManager?.startInfo
    }
    preloadLevel(levelID:number){
    }
    getByPath(path:string) {
        return this.commonRes.get(path) || this.curLevelRes?.get(path)
    }
}
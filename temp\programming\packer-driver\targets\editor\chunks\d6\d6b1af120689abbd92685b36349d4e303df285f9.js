System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Label, BaseUI, UILayer, UIMgr, GameIns, BundleName, _dec, _dec2, _class, _class2, _descriptor, _crd, ccclass, property, GameReviveUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIOpt(extras) {
    _reporterNs.report("UIOpt", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../../game/GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "../../../const/BundleConst", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Label = _cc.Label;
    }, function (_unresolved_2) {
      BaseUI = _unresolved_2.BaseUI;
      UILayer = _unresolved_2.UILayer;
      UIMgr = _unresolved_2.UIMgr;
    }, function (_unresolved_3) {
      GameIns = _unresolved_3.GameIns;
    }, function (_unresolved_4) {
      BundleName = _unresolved_4.BundleName;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "41950R6vQlGa4fuyhFxbtVw", "GameReviveUI", undefined);

      __checkObsolete__(['_decorator', 'Label', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("GameReviveUI", GameReviveUI = (_dec = ccclass('GameReviveUI'), _dec2 = property(Label), _dec(_class = (_class2 = class GameReviveUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "labTime", _descriptor, this);

          this._countdown = 7;
          // 倒计时初始值
          this._countdownInterval = null;
        }

        // 用于存储计时器 ID
        static getUrl() {
          return "prefab/GameReviveUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Default;
        }

        static getUIOption() {
          return {
            isClickBgCloseUI: false
          };
        }

        static getBundleName() {
          return (_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).GameFight;
        }

        onLoad() {}

        async closeUI() {
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).closeUI(GameReviveUI);
        }

        async onShow() {// this.startCountdown(); // 开始倒计时
        }

        async onHide() {
          this.stopCountdown(); // 停止倒计时
        }

        async onClose() {}

        onDestroy() {}

        onBtnReviveClicked() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.relifeBattle();
          this.closeUI();
        }

        onBtnAdClicked() {
          this.closeUI();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.relifeBattle();
        }

        onBtnCloseClicked() {
          this.quitBattle();
        } // 开始倒计时


        startCountdown() {
          this._countdown = 7; // 初始化倒计时

          this.updateCountdownLabel(); // 更新初始显示

          this._countdownInterval = setInterval(() => {
            this._countdown--;
            this.updateCountdownLabel();

            if (this._countdown <= 0) {
              this.stopCountdown();
              this.onCountdownFinished();
            }
          }, 1000); // 每秒更新一次
        } // 停止倒计时


        stopCountdown() {
          if (this._countdownInterval !== null) {
            clearInterval(this._countdownInterval);
            this._countdownInterval = null;
          }
        } // 更新倒计时文本


        updateCountdownLabel() {
          if (this.labTime) {
            this.labTime.string = `${this._countdown}秒`;
          }
        } // 倒计时结束时的逻辑


        onCountdownFinished() {
          this.quitBattle(); // 关闭界面
        }

        quitBattle() {
          this.closeUI();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.quitBattle();
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "labTime", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=d6b1af120689abbd92685b36349d4e303df285f9.js.map
import csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';
import { MessageBox } from 'db://assets/bundles/common/script/ui/common/components/messagebox/MessageBox';
import { logWarn } from 'db://assets/scripts/utils/Logger';
import Long from 'long';
import { MyApp } from "../../app/MyApp";
import { DataEvent } from '../../event/DataEvent';
import { EventMgr } from '../../event/EventManager';
import { IData } from "../DataManager";

export enum ROLE_SETTING_TYPE {
    ROLE_SETTING_UI_DATA = "ROLE_SETTING_UI_DATA",
    ROLE_SETTING_BATTLE_DATA = "ROLE_SETTING_BATTLE_DATA",
}

export class Role implements IData {

    roleBaseAttr: csproto.comm.IRoleBaseAttr = new csproto.comm.RoleBaseAttr();
    roleExtAttr: csproto.comm.IRoleExtAttr = new csproto.comm.RoleExtAttr();

    maxLevel: number = 0;
    curLevel: number = 0;
    curExp: number = 0;
    maxExp: number = 0;
    localSettings: (csproto.comm.IClientSetting[]) = [];
    settingDataUI: csproto.comm.SettingDataUI = new csproto.comm.SettingDataUI();
    settingDataBattle: csproto.comm.SettingDataBattle = new csproto.comm.SettingDataBattle();

    dailyCounter: Map<number, number> = new Map();
    weekCounter: Map<number, number> = new Map();
    monthlytCounter: Map<number, number> = new Map();
    permanentCounter: Map<number, number> = new Map();

    public init(): void {
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GET_ROLE_BASE_ATTR, this.onGetRoleBaseAttr, this);
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_LEVEL_UP, this.onLevelUp, this);
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GET_ROLE_EXT_ATTR, this.onGetRoleExtAttr, this);
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GET_ROLE_SIMPLE, this.onGetRoleSimple, this);
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GET_COUNTER, this.onGetCounter, this);
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GET_CLIENT_SETTING, this.onGetClientSetting, this);
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_SET_CLIENT_SETTING, this.onSetClientSetting, this);

        this.cmdGetClientSettingUI();
        this.cmdGetCounterDaily();
        this.cmdGetCounterWeekly();
        this.cmdGetCounterMonthly();
        this.cmdGetCounterPermanent();
    }
    public update(): void {
    }

    public getCurLevelData(lvl: number) {
        const tbResUpgrade = MyApp.lubanTables.TbResUpgrade;
        const data = tbResUpgrade.get(lvl);
        return data;
    }
    public setRole(data: csproto.cs.IS2CGetRole) {
        if (!data) {
            return;
        }
        this.setRoleBaseAttr(data.base!);
    }
    public setRoleBaseAttr(data: csproto.comm.IRoleBaseAttr) {
        if (!data) {
            return;
        }
        this.roleBaseAttr = data;
        const tbResUpgrade = MyApp.lubanTables.TbResUpgrade;
        const dataList = tbResUpgrade.getDataList();
        if (dataList.length > 0) {
            this.maxLevel = dataList[dataList.length - 1].roleLevel;
        }
        this.curLevel = data.level ?? 0;
        this.curExp = data.xp ?? 0;
        if (this.curLevel >= this.maxLevel) {
            this.maxLevel = this.curLevel;
            this.maxExp = 0;
        } else {
            this.maxExp = MyApp.lubanTables.TbResUpgrade.get(this.curLevel + 1)?.xp ?? 0;
        }
        if (this.curExp > this.maxExp) {
            this.curExp = this.maxExp;
        }
        EventMgr.emit(DataEvent.RoleBaseAttrChange);
        //MessageBox.show("获取成功" + JSON.stringify(this.roleBaseAttr));
    }

    public getSetting(key: string) {
        if (this.localSettings) {
            const setting = this.localSettings.find(s => s.key === key);
            if (setting) {
                return setting.data;
            }
        }
        return null;
    }
    public getEndlessBestScore() {
        return this.permanentCounter.get(csproto.comm.COUNTER_ID.COUNTER_ID_COMMON_GAME_ENDLESS_BEST_SCORE) ?? 0;
    }
    public getEndlessWeekBestScore() {
        return this.weekCounter.get(csproto.comm.COUNTER_ID.COUNTER_ID_COMMON_GAME_ENDLESS_WEEK_BEST_SCORE) ?? 0;
    }
    public getEndlessDoubleRewardCount() {
        return this.dailyCounter.get(csproto.comm.COUNTER_ID.COUNTER_ID_COMMON_GAME_ENDLESS_DOUBLE_REWARD_COUNT) ?? 0;
    }
    public getStoryDoubleRewardCount() {
        return this.dailyCounter.get(csproto.comm.COUNTER_ID.COUNTER_ID_COMMON_GAME_STORY_DOUBLE_REWARD_COUNT) ?? 0;
    }
    //#region 收协议
    onGetRoleBaseAttr(msg: csproto.cs.IS2CMsg): void {
        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {
            logWarn("NetMgr", `onGetRoleBaseAttr failed ${msg.ret_code}`);

        }
        var data = msg.body?.get_role_base_attr;
        if (!data) {
            logWarn("NetMgr", "onGetRoleBaseAttr data is null");
            return;
        }
        this.setRoleBaseAttr(data.base!);
    }
    onLevelUp(msg: csproto.cs.IS2CMsg): void {
        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {
            logWarn("NetMgr", `onLevelUp failed ${msg.ret_code}`);
            return;
        }
        var data = msg.body?.level_up;
        if (!data) {
            logWarn("NetMgr", "onLevelUp data is null");
            return;
        }
        MessageBox.testShow("升级成功");
    }
    onGetRoleExtAttr(msg: csproto.cs.IS2CMsg): void {
        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {
            logWarn("NetMgr", `onGetRoleExtAttr failed ${msg.ret_code}`);
            return;
        }
        var data = msg.body?.get_role_ext_attr;
        if (!data) {
            logWarn("NetMgr", "onGetRoleExtAttr data is null");
            return;
        }
        this.roleExtAttr = data.ext!;
        EventMgr.emit(DataEvent.RoleExtAttrChange);
        //MessageBox.show("获取成功" + JSON.stringify(this.roleExtAttr));
    }
    onGetRoleSimple(msg: csproto.cs.IS2CMsg): void {
        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {
            logWarn("NetMgr", `onGetRoleSimple failed ${msg.ret_code}`);
            return;
        }
        var data = msg.body?.get_role_simple;
        if (!data) {
            logWarn("NetMgr", "onGetRoleSimple data is null");
        }
    }
    onGetClientSetting(msg: csproto.cs.IS2CMsg): void {
        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {
            logWarn("NetMgr", `onGetClientSetting failed ${msg.ret_code}`);
            return;
        }
        var data = msg.body?.get_client_setting;
        if (!data) {
            logWarn("NetMgr", "onGetClientSetting data is null");
            return;
        }
        const settings = data.settings;
        if (settings) {
            settings.forEach(setting => {
                const key = setting.key;
                const data = setting.data;
                if (key === ROLE_SETTING_TYPE.ROLE_SETTING_UI_DATA) {
                    this.settingDataUI = csproto.comm.SettingDataUI.decode(data);
                    //MessageBox.show("获取成功" + JSON.stringify(this.settingDataUI));
                } else if (key === ROLE_SETTING_TYPE.ROLE_SETTING_BATTLE_DATA) {
                    this.settingDataBattle = csproto.comm.SettingDataBattle.decode(data);
                    //MessageBox.show("获取成功" + JSON.stringify(this.settingDataBattle));
                }
            })
        }
    }

    onSetClientSetting(msg: csproto.cs.IS2CMsg): void {
        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {
            logWarn("NetMgr", `onSetClientSetting failed ${msg.ret_code}`);
            return;
        }
        var data = msg.body?.set_client_setting;
        if (!data) {
            logWarn("NetMgr", "onSetClientSetting data is null");
            return;
        }
        //MessageBox.show("设置成功");
        //没有Setting数据返回
    }

    onGetCounter(msg: csproto.cs.IS2CMsg): void {
        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {
            logWarn("NetMgr", `onGetCounter failed ${msg.ret_code}`);
            return;
        }
        var data = msg.body?.get_counter;
        if (!data) {
            logWarn("NetMgr", "onGetCounter data is null");
            return;
        }
        const counters = data.counters;
        if (counters) {
            counters.forEach(counter => {
                const id = counter.id;
                const value = counter.value;
                if (counter.class === csproto.comm.COUNTER_CLASS.COUNTER_CLASS_COMMON_DAILY) {
                    this.dailyCounter.set(id!, value!);
                }
                else if (counter.class === csproto.comm.COUNTER_CLASS.COUNTER_CLASS_COMMON_WEEKLY) {
                    this.weekCounter.set(id!, value!);
                }
                else if (counter.class === csproto.comm.COUNTER_CLASS.COUNTER_CLASS_COMMON_MONTHLY) {
                    this.monthlytCounter.set(id!, value!);
                }
                else if (counter.class === csproto.comm.COUNTER_CLASS.COUNTER_CLASS_COMMON_PERMANENT) {
                    this.permanentCounter.set(id!, value!);
                }
            })
        }
    }
    onDelClientSetting(msg: csproto.cs.IS2CMsg): void {
        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {
            logWarn("NetMgr", `onDelClientSetting failed ${msg.ret_code}`);
            return;
        }
        var data = msg.body?.del_client_setting;
        if (!data) {
            logWarn("NetMgr", "onDelClientSetting data is null");
            return;
        }
    }
    //#endregion
    //#region 发协议
    cmdGetRoleBaseAttr() {
        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GET_ROLE_BASE_ATTR, {
            get_role_base_attr: {
            }
        })
    }
    //获取角色的扩展信息
    cmdGetRoleExtAttr() {
        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GET_ROLE_EXT_ATTR, {
            get_role_ext_attr: {
            }
        })
    }
    CmdGetRoleSimple(uin: Long, area_id: number) {
        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GET_ROLE_SIMPLE, {
            get_role_simple: {
                uin: uin,
                area_id: area_id
            }
        })
    }
    cmdGetClientSettingUI() {
        let getKeys: string[] = [
            ROLE_SETTING_TYPE.ROLE_SETTING_UI_DATA,
        ]
        this.cmdGetClientSetting(getKeys);
    }

    cmdGetClientSettingBattle() {
        let getKeys: string[] = [
            ROLE_SETTING_TYPE.ROLE_SETTING_BATTLE_DATA,
        ]
        this.cmdGetClientSetting(getKeys);
    }

    cmdGetClientSetting(getKeys: string[]) {
        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GET_CLIENT_SETTING, {
            get_client_setting: {
                keys: getKeys
            }
        })
    }

    cmdSetClientSettingUI(settingDataUI: csproto.comm.SettingDataUI) {
        let settingDataUIBytes = csproto.comm.SettingDataUI.encode(settingDataUI).finish();
        this.cmdSetClientSetting([
            {
                key: ROLE_SETTING_TYPE.ROLE_SETTING_UI_DATA,
                data: settingDataUIBytes,
            }
        ]);
    }

    cmdSetClientSettingBattle(settingDataBattle: csproto.comm.SettingDataBattle) {
        let settingDataBattleBytes = csproto.comm.SettingDataBattle.encode(settingDataBattle).finish();
        this.cmdSetClientSetting([
            {
                key: ROLE_SETTING_TYPE.ROLE_SETTING_BATTLE_DATA,
                data: settingDataBattleBytes,
            }
        ]);
    }

    cmdSetClientSetting(newSettings: csproto.comm.IClientSetting[]) {
        if (newSettings.length === 0) {
            return;
        }
        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_SET_CLIENT_SETTING, {
            set_client_setting: {
                settings: newSettings
            }
        })
    }

    cmdDelClientSetting(delKeys: string[]) {
        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_DEL_CLIENT_SETTING, {
            del_client_setting: {
                keys: delKeys
            }
        })
    }
    //每日数据  COUNTER_CLASS_COMMON_DAILY;
    cmdGetCounterDaily(counterID: number = 0) {
        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GET_COUNTER, {
            get_counter: {
                "class": csproto.comm.COUNTER_CLASS.COUNTER_CLASS_COMMON_DAILY,
                id: counterID
            }
        })
    }
    //每周数据  COUNTER_CLASS_COMMON_WEEKLY
    cmdGetCounterWeekly(counterID: number = 0) {
        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GET_COUNTER, {
            get_counter: {
                "class": csproto.comm.COUNTER_CLASS.COUNTER_CLASS_COMMON_WEEKLY,
                id: counterID
            }
        })
    }
    //每月数据  COUNTER_CLASS_COMMON_MONTHLY
    cmdGetCounterMonthly(counterID: number = 0) {
        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GET_COUNTER, {
            get_counter: {
                "class": csproto.comm.COUNTER_CLASS.COUNTER_CLASS_COMMON_MONTHLY,
                id: counterID
            }
        })
    }
    //永久数据  COUNTER_CLASS_COMMON_PERMANENT
    cmdGetCounterPermanent(counterID: number = 0) {
        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GET_COUNTER, {
            get_counter: {
                "class": csproto.comm.COUNTER_CLASS.COUNTER_CLASS_COMMON_PERMANENT,
                id: counterID
            }
        })
    }
    //#endregion
}

{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/data/base/Role.ts"], "names": ["Role", "csproto", "MessageBox", "log<PERSON>arn", "MyApp", "DataEvent", "EventMgr", "ROLE_SETTING_TYPE", "roleBaseAttr", "comm", "RoleBaseAttr", "roleExtAttr", "RoleExtAttr", "maxLevel", "curLevel", "curExp", "maxExp", "localSettings", "settingDataUI", "SettingDataUI", "settingDataBattle", "SettingDataBattle", "dailyCounter", "Map", "weekCounter", "monthlytCounter", "permanentCounter", "init", "netMgr", "registerHandler", "cs", "CS_CMD", "CS_CMD_GET_ROLE_BASE_ATTR", "onGetRoleBaseAttr", "CS_CMD_LEVEL_UP", "onLevelUp", "CS_CMD_GET_ROLE_EXT_ATTR", "onGetRoleExtAttr", "CS_CMD_GET_ROLE_SIMPLE", "onGetRoleSimple", "CS_CMD_GET_COUNTER", "onGetCounter", "CS_CMD_GET_CLIENT_SETTING", "onGetClientSetting", "CS_CMD_SET_CLIENT_SETTING", "onSetClientSetting", "cmdGetClientSettingUI", "cmdGetCounterDaily", "cmdGetCounterWeekly", "cmdGetCounterMonthly", "cmdGetCounterPermanent", "update", "getCurLevelData", "lvl", "tbResUpgrade", "lubanTables", "TbResUpgrade", "data", "get", "setRole", "setRoleBaseAttr", "base", "dataList", "getDataList", "length", "roleLevel", "level", "xp", "emit", "RoleBaseAttrChange", "getSetting", "key", "setting", "find", "s", "getEndlessBestScore", "COUNTER_ID", "COUNTER_ID_COMMON_GAME_ENDLESS_BEST_SCORE", "getEndlessWeekBestScore", "COUNTER_ID_COMMON_GAME_ENDLESS_WEEK_BEST_SCORE", "getEndlessDoubleRewardCount", "COUNTER_ID_COMMON_GAME_ENDLESS_DOUBLE_REWARD_COUNT", "getStoryDoubleRewardCount", "COUNTER_ID_COMMON_GAME_STORY_DOUBLE_REWARD_COUNT", "getSelfUin", "uin", "isSelfUin", "msg", "ret_code", "RET_CODE", "RET_CODE_NO_ERROR", "body", "get_role_base_attr", "level_up", "showLevelUP", "get_role_ext_attr", "ext", "RoleExtAttrChange", "get_role_simple", "get_client_setting", "settings", "for<PERSON>ach", "ROLE_SETTING_UI_DATA", "decode", "ROLE_SETTING_BATTLE_DATA", "set_client_setting", "get_counter", "counters", "counter", "id", "value", "class", "COUNTER_CLASS", "COUNTER_CLASS_COMMON_DAILY", "set", "COUNTER_CLASS_COMMON_WEEKLY", "COUNTER_CLASS_COMMON_MONTHLY", "COUNTER_CLASS_COMMON_PERMANENT", "onDelClientSetting", "del_client_setting", "cmdGetRoleBaseAttr", "sendMessage", "cmdGetRoleExtAttr", "CmdGetRoleSimple", "area_id", "get<PERSON><PERSON><PERSON>", "cmdGetClientSetting", "cmdGetClientSettingBattle", "keys", "cmdSetClientSettingUI", "settingDataUIBytes", "encode", "finish", "cmdSetClientSetting", "cmdSetClientSettingBattle", "settingDataBattleBytes", "newSettings", "cmdDelClientSetting", "<PERSON><PERSON><PERSON><PERSON>", "CS_CMD_DEL_CLIENT_SETTING", "counterID"], "mappings": ";;;wFAcaA,I;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAdNC,MAAAA,O;;AACEC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,O,iBAAAA,O;;AAEAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,Q,iBAAAA,Q;;;;;;;mCAGGC,iB,0BAAAA,iB;AAAAA,QAAAA,iB;AAAAA,QAAAA,iB;eAAAA,iB;;;sBAKCP,I,GAAN,MAAMA,IAAN,CAA4B;AAAA;AAAA,eAE/BQ,YAF+B,GAEY,IAAI;AAAA;AAAA,kCAAQC,IAAR,CAAaC,YAAjB,EAFZ;AAAA,eAG/BC,WAH+B,GAGU,IAAI;AAAA;AAAA,kCAAQF,IAAR,CAAaG,WAAjB,EAHV;AAAA,eAK/BC,QAL+B,GAKZ,CALY;AAAA,eAM/BC,QAN+B,GAMZ,CANY;AAAA,eAO/BC,MAP+B,GAOd,CAPc;AAAA,eAQ/BC,MAR+B,GAQd,CARc;AAAA,eAS/BC,aAT+B,GASkB,EATlB;AAAA,eAU/BC,aAV+B,GAUa,IAAI;AAAA;AAAA,kCAAQT,IAAR,CAAaU,aAAjB,EAVb;AAAA,eAW/BC,iBAX+B,GAWqB,IAAI;AAAA;AAAA,kCAAQX,IAAR,CAAaY,iBAAjB,EAXrB;AAAA,eAa/BC,YAb+B,GAaK,IAAIC,GAAJ,EAbL;AAAA,eAc/BC,WAd+B,GAcI,IAAID,GAAJ,EAdJ;AAAA,eAe/BE,eAf+B,GAeQ,IAAIF,GAAJ,EAfR;AAAA,eAgB/BG,gBAhB+B,GAgBS,IAAIH,GAAJ,EAhBT;AAAA;;AAkBxBI,QAAAA,IAAI,GAAS;AAChB;AAAA;AAAA,8BAAMC,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBC,yBAA/C,EAA0E,KAAKC,iBAA/E,EAAkG,IAAlG;AACA;AAAA;AAAA,8BAAML,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBG,eAA/C,EAAgE,KAAKC,SAArE,EAAgF,IAAhF;AACA;AAAA;AAAA,8BAAMP,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBK,wBAA/C,EAAyE,KAAKC,gBAA9E,EAAgG,IAAhG;AACA;AAAA;AAAA,8BAAMT,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBO,sBAA/C,EAAuE,KAAKC,eAA5E,EAA6F,IAA7F;AACA;AAAA;AAAA,8BAAMX,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBS,kBAA/C,EAAmE,KAAKC,YAAxE,EAAsF,IAAtF;AACA;AAAA;AAAA,8BAAMb,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBW,yBAA/C,EAA0E,KAAKC,kBAA/E,EAAmG,IAAnG;AACA;AAAA;AAAA,8BAAMf,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBa,yBAA/C,EAA0E,KAAKC,kBAA/E,EAAmG,IAAnG;AAEA,eAAKC,qBAAL;AACA,eAAKC,kBAAL;AACA,eAAKC,mBAAL;AACA,eAAKC,oBAAL;AACA,eAAKC,sBAAL;AACH;;AACMC,QAAAA,MAAM,GAAS,CACrB;;AAEMC,QAAAA,eAAe,CAACC,GAAD,EAAc;AAChC,cAAMC,YAAY,GAAG;AAAA;AAAA,8BAAMC,WAAN,CAAkBC,YAAvC;AACA,cAAMC,IAAI,GAAGH,YAAY,CAACI,GAAb,CAAiBL,GAAjB,CAAb;AACA,iBAAOI,IAAP;AACH;;AACME,QAAAA,OAAO,CAACF,IAAD,EAA+B;AACzC,cAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,eAAKG,eAAL,CAAqBH,IAAI,CAACI,IAA1B;AACH;;AACMD,QAAAA,eAAe,CAACH,IAAD,EAAmC;AAAA;;AACrD,cAAI,CAACA,IAAL,EAAW;AACP;AACH;;AACD,eAAKjD,YAAL,GAAoBiD,IAApB;AACA,cAAMH,YAAY,GAAG;AAAA;AAAA,8BAAMC,WAAN,CAAkBC,YAAvC;AACA,cAAMM,QAAQ,GAAGR,YAAY,CAACS,WAAb,EAAjB;;AACA,cAAID,QAAQ,CAACE,MAAT,GAAkB,CAAtB,EAAyB;AACrB,iBAAKnD,QAAL,GAAgBiD,QAAQ,CAACA,QAAQ,CAACE,MAAT,GAAkB,CAAnB,CAAR,CAA8BC,SAA9C;AACH;;AACD,eAAKnD,QAAL,kBAAgB2C,IAAI,CAACS,KAArB,0BAA8B,CAA9B;AACA,eAAKnD,MAAL,eAAc0C,IAAI,CAACU,EAAnB,uBAAyB,CAAzB;;AACA,cAAI,KAAKrD,QAAL,IAAiB,KAAKD,QAA1B,EAAoC;AAChC,iBAAKA,QAAL,GAAgB,KAAKC,QAArB;AACA,iBAAKE,MAAL,GAAc,CAAd;AACH,WAHD,MAGO;AAAA;;AACH,iBAAKA,MAAL,sDAAc;AAAA;AAAA,gCAAMuC,WAAN,CAAkBC,YAAlB,CAA+BE,GAA/B,CAAmC,KAAK5C,QAAL,GAAgB,CAAnD,CAAd,qBAAc,uBAAuDqD,EAArE,oCAA2E,CAA3E;AACH;;AACD,cAAI,KAAKpD,MAAL,GAAc,KAAKC,MAAvB,EAA+B;AAC3B,iBAAKD,MAAL,GAAc,KAAKC,MAAnB;AACH;;AACD;AAAA;AAAA,oCAASoD,IAAT,CAAc;AAAA;AAAA,sCAAUC,kBAAxB,EArBqD,CAsBrD;AACH;;AACMC,QAAAA,UAAU,CAACC,GAAD,EAAc;AAC3B,cAAI,KAAKtD,aAAT,EAAwB;AACpB,gBAAMuD,OAAO,GAAG,KAAKvD,aAAL,CAAmBwD,IAAnB,CAAwBC,CAAC,IAAIA,CAAC,CAACH,GAAF,KAAUA,GAAvC,CAAhB;;AACA,gBAAIC,OAAJ,EAAa;AACT,qBAAOA,OAAO,CAACf,IAAf;AACH;AACJ;;AACD,iBAAO,IAAP;AACH;;AACMkB,QAAAA,mBAAmB,GAAG;AAAA;;AACzB,0CAAO,KAAKjD,gBAAL,CAAsBgC,GAAtB,CAA0B;AAAA;AAAA,kCAAQjD,IAAR,CAAamE,UAAb,CAAwBC,yCAAlD,CAAP,oCAAuG,CAAvG;AACH;;AACMC,QAAAA,uBAAuB,GAAG;AAAA;;AAC7B,0CAAO,KAAKtD,WAAL,CAAiBkC,GAAjB,CAAqB;AAAA;AAAA,kCAAQjD,IAAR,CAAamE,UAAb,CAAwBG,8CAA7C,CAAP,oCAAuG,CAAvG;AACH;;AACMC,QAAAA,2BAA2B,GAAG;AAAA;;AACjC,0CAAO,KAAK1D,YAAL,CAAkBoC,GAAlB,CAAsB;AAAA;AAAA,kCAAQjD,IAAR,CAAamE,UAAb,CAAwBK,kDAA9C,CAAP,oCAA4G,CAA5G;AACH;;AACMC,QAAAA,yBAAyB,GAAG;AAAA;;AAC/B,2CAAO,KAAK5D,YAAL,CAAkBoC,GAAlB,CAAsB;AAAA;AAAA,kCAAQjD,IAAR,CAAamE,UAAb,CAAwBO,gDAA9C,CAAP,qCAA0G,CAA1G;AACH;;AACMC,QAAAA,UAAU,GAAE;AACf,iBAAO,KAAK5E,YAAL,CAAkB6E,GAAzB;AACH;;AACMC,QAAAA,SAAS,CAACD,GAAD,EAAW;AACvB,iBAAO,KAAK7E,YAAL,CAAkB6E,GAAlB,KAA0BA,GAAjC;AACH,SAjG8B,CAkG/B;;;AACApD,QAAAA,iBAAiB,CAACsD,GAAD,EAAgC;AAAA;;AAC7C,cAAIA,GAAG,CAACC,QAAJ,IAAgB;AAAA;AAAA,kCAAQ/E,IAAR,CAAagF,QAAb,CAAsBC,iBAA1C,EAA6D;AACzD;AAAA;AAAA,oCAAQ,QAAR,gCAA8CH,GAAG,CAACC,QAAlD;AAEH;;AACD,cAAI/B,IAAI,gBAAG8B,GAAG,CAACI,IAAP,qBAAG,UAAUC,kBAArB;;AACA,cAAI,CAACnC,IAAL,EAAW;AACP;AAAA;AAAA,oCAAQ,QAAR,EAAkB,gCAAlB;AACA;AACH;;AACD,eAAKG,eAAL,CAAqBH,IAAI,CAACI,IAA1B;AACH;;AACD1B,QAAAA,SAAS,CAACoD,GAAD,EAAgC;AAAA;;AACrC,cAAIA,GAAG,CAACC,QAAJ,IAAgB;AAAA;AAAA,kCAAQ/E,IAAR,CAAagF,QAAb,CAAsBC,iBAA1C,EAA6D;AACzD;AAAA;AAAA,oCAAQ,QAAR,wBAAsCH,GAAG,CAACC,QAA1C;AACA;AACH;;AACD,cAAI/B,IAAI,iBAAG8B,GAAG,CAACI,IAAP,qBAAG,WAAUE,QAArB;;AACA,cAAI,CAACpC,IAAL,EAAW;AACP;AAAA;AAAA,oCAAQ,QAAR,EAAkB,wBAAlB;AACA;AACH;;AACD;AAAA;AAAA,wCAAWqC,WAAX,CAAuBrC,IAAvB;AACH;;AACDpB,QAAAA,gBAAgB,CAACkD,GAAD,EAAgC;AAAA;;AAC5C,cAAIA,GAAG,CAACC,QAAJ,IAAgB;AAAA;AAAA,kCAAQ/E,IAAR,CAAagF,QAAb,CAAsBC,iBAA1C,EAA6D;AACzD;AAAA;AAAA,oCAAQ,QAAR,+BAA6CH,GAAG,CAACC,QAAjD;AACA;AACH;;AACD,cAAI/B,IAAI,iBAAG8B,GAAG,CAACI,IAAP,qBAAG,WAAUI,iBAArB;;AACA,cAAI,CAACtC,IAAL,EAAW;AACP;AAAA;AAAA,oCAAQ,QAAR,EAAkB,+BAAlB;AACA;AACH;;AACD,eAAK9C,WAAL,GAAmB8C,IAAI,CAACuC,GAAxB;AACA;AAAA;AAAA,oCAAS5B,IAAT,CAAc;AAAA;AAAA,sCAAU6B,iBAAxB,EAX4C,CAY5C;AACH;;AACD1D,QAAAA,eAAe,CAACgD,GAAD,EAAgC;AAAA;;AAC3C,cAAIA,GAAG,CAACC,QAAJ,IAAgB;AAAA;AAAA,kCAAQ/E,IAAR,CAAagF,QAAb,CAAsBC,iBAA1C,EAA6D;AACzD;AAAA;AAAA,oCAAQ,QAAR,8BAA4CH,GAAG,CAACC,QAAhD;AACA;AACH;;AACD,cAAI/B,IAAI,iBAAG8B,GAAG,CAACI,IAAP,qBAAG,WAAUO,eAArB;;AACA,cAAI,CAACzC,IAAL,EAAW;AACP;AAAA;AAAA,oCAAQ,QAAR,EAAkB,8BAAlB;AACH;AACJ;;AACDd,QAAAA,kBAAkB,CAAC4C,GAAD,EAAgC;AAAA;;AAC9C,cAAIA,GAAG,CAACC,QAAJ,IAAgB;AAAA;AAAA,kCAAQ/E,IAAR,CAAagF,QAAb,CAAsBC,iBAA1C,EAA6D;AACzD;AAAA;AAAA,oCAAQ,QAAR,iCAA+CH,GAAG,CAACC,QAAnD;AACA;AACH;;AACD,cAAI/B,IAAI,iBAAG8B,GAAG,CAACI,IAAP,qBAAG,WAAUQ,kBAArB;;AACA,cAAI,CAAC1C,IAAL,EAAW;AACP;AAAA;AAAA,oCAAQ,QAAR,EAAkB,iCAAlB;AACA;AACH;;AACD,cAAM2C,QAAQ,GAAG3C,IAAI,CAAC2C,QAAtB;;AACA,cAAIA,QAAJ,EAAc;AACVA,YAAAA,QAAQ,CAACC,OAAT,CAAiB7B,OAAO,IAAI;AACxB,kBAAMD,GAAG,GAAGC,OAAO,CAACD,GAApB;AACA,kBAAMd,IAAI,GAAGe,OAAO,CAACf,IAArB;;AACA,kBAAIc,GAAG,KAAKhE,iBAAiB,CAAC+F,oBAA9B,EAAoD;AAChD,qBAAKpF,aAAL,GAAqB;AAAA;AAAA,wCAAQT,IAAR,CAAaU,aAAb,CAA2BoF,MAA3B,CAAkC9C,IAAlC,CAArB,CADgD,CAEhD;AACH,eAHD,MAGO,IAAIc,GAAG,KAAKhE,iBAAiB,CAACiG,wBAA9B,EAAwD;AAC3D,qBAAKpF,iBAAL,GAAyB;AAAA;AAAA,wCAAQX,IAAR,CAAaY,iBAAb,CAA+BkF,MAA/B,CAAsC9C,IAAtC,CAAzB,CAD2D,CAE3D;AACH;AACJ,aAVD;AAWH;AACJ;;AAEDZ,QAAAA,kBAAkB,CAAC0C,GAAD,EAAgC;AAAA;;AAC9C,cAAIA,GAAG,CAACC,QAAJ,IAAgB;AAAA;AAAA,kCAAQ/E,IAAR,CAAagF,QAAb,CAAsBC,iBAA1C,EAA6D;AACzD;AAAA;AAAA,oCAAQ,QAAR,iCAA+CH,GAAG,CAACC,QAAnD;AACA;AACH;;AACD,cAAI/B,IAAI,iBAAG8B,GAAG,CAACI,IAAP,qBAAG,WAAUc,kBAArB;;AACA,cAAI,CAAChD,IAAL,EAAW;AACP;AAAA;AAAA,oCAAQ,QAAR,EAAkB,iCAAlB;AACA;AACH,WAT6C,CAU9C;AACA;;AACH;;AAEDhB,QAAAA,YAAY,CAAC8C,GAAD,EAAgC;AAAA;;AACxC,cAAIA,GAAG,CAACC,QAAJ,IAAgB;AAAA;AAAA,kCAAQ/E,IAAR,CAAagF,QAAb,CAAsBC,iBAA1C,EAA6D;AACzD;AAAA;AAAA,oCAAQ,QAAR,2BAAyCH,GAAG,CAACC,QAA7C;AACA;AACH;;AACD,cAAI/B,IAAI,iBAAG8B,GAAG,CAACI,IAAP,qBAAG,WAAUe,WAArB;;AACA,cAAI,CAACjD,IAAL,EAAW;AACP;AAAA;AAAA,oCAAQ,QAAR,EAAkB,2BAAlB;AACA;AACH;;AACD,cAAMkD,QAAQ,GAAGlD,IAAI,CAACkD,QAAtB;;AACA,cAAIA,QAAJ,EAAc;AACVA,YAAAA,QAAQ,CAACN,OAAT,CAAiBO,OAAO,IAAI;AACxB,kBAAMC,EAAE,GAAGD,OAAO,CAACC,EAAnB;AACA,kBAAMC,KAAK,GAAGF,OAAO,CAACE,KAAtB;;AACA,kBAAIF,OAAO,CAACG,KAAR,KAAkB;AAAA;AAAA,sCAAQtG,IAAR,CAAauG,aAAb,CAA2BC,0BAAjD,EAA6E;AACzE,qBAAK3F,YAAL,CAAkB4F,GAAlB,CAAsBL,EAAtB,EAA2BC,KAA3B;AACH,eAFD,MAGK,IAAIF,OAAO,CAACG,KAAR,KAAkB;AAAA;AAAA,sCAAQtG,IAAR,CAAauG,aAAb,CAA2BG,2BAAjD,EAA8E;AAC/E,qBAAK3F,WAAL,CAAiB0F,GAAjB,CAAqBL,EAArB,EAA0BC,KAA1B;AACH,eAFI,MAGA,IAAIF,OAAO,CAACG,KAAR,KAAkB;AAAA;AAAA,sCAAQtG,IAAR,CAAauG,aAAb,CAA2BI,4BAAjD,EAA+E;AAChF,qBAAK3F,eAAL,CAAqByF,GAArB,CAAyBL,EAAzB,EAA8BC,KAA9B;AACH,eAFI,MAGA,IAAIF,OAAO,CAACG,KAAR,KAAkB;AAAA;AAAA,sCAAQtG,IAAR,CAAauG,aAAb,CAA2BK,8BAAjD,EAAiF;AAClF,qBAAK3F,gBAAL,CAAsBwF,GAAtB,CAA0BL,EAA1B,EAA+BC,KAA/B;AACH;AACJ,aAfD;AAgBH;AACJ;;AACDQ,QAAAA,kBAAkB,CAAC/B,GAAD,EAAgC;AAAA;;AAC9C,cAAIA,GAAG,CAACC,QAAJ,IAAgB;AAAA;AAAA,kCAAQ/E,IAAR,CAAagF,QAAb,CAAsBC,iBAA1C,EAA6D;AACzD;AAAA;AAAA,oCAAQ,QAAR,iCAA+CH,GAAG,CAACC,QAAnD;AACA;AACH;;AACD,cAAI/B,IAAI,iBAAG8B,GAAG,CAACI,IAAP,qBAAG,WAAU4B,kBAArB;;AACA,cAAI,CAAC9D,IAAL,EAAW;AACP;AAAA;AAAA,oCAAQ,QAAR,EAAkB,iCAAlB;AACA;AACH;AACJ,SAnO8B,CAoO/B;AACA;;;AACA+D,QAAAA,kBAAkB,GAAG;AACjB;AAAA;AAAA,8BAAM5F,MAAN,CAAa6F,WAAb,CAAyB;AAAA;AAAA,kCAAQ3F,EAAR,CAAWC,MAAX,CAAkBC,yBAA3C,EAAsE;AAClE4D,YAAAA,kBAAkB,EAAE;AAD8C,WAAtE;AAIH,SA3O8B,CA4O/B;;;AACA8B,QAAAA,iBAAiB,GAAG;AAChB;AAAA;AAAA,8BAAM9F,MAAN,CAAa6F,WAAb,CAAyB;AAAA;AAAA,kCAAQ3F,EAAR,CAAWC,MAAX,CAAkBK,wBAA3C,EAAqE;AACjE2D,YAAAA,iBAAiB,EAAE;AAD8C,WAArE;AAIH;;AACD4B,QAAAA,gBAAgB,CAACtC,GAAD,EAAYuC,OAAZ,EAA6B;AACzC;AAAA;AAAA,8BAAMhG,MAAN,CAAa6F,WAAb,CAAyB;AAAA;AAAA,kCAAQ3F,EAAR,CAAWC,MAAX,CAAkBO,sBAA3C,EAAmE;AAC/D4D,YAAAA,eAAe,EAAE;AACbb,cAAAA,GAAG,EAAEA,GADQ;AAEbuC,cAAAA,OAAO,EAAEA;AAFI;AAD8C,WAAnE;AAMH;;AACD9E,QAAAA,qBAAqB,GAAG;AACpB,cAAI+E,OAAiB,GAAG,CACpBtH,iBAAiB,CAAC+F,oBADE,CAAxB;AAGA,eAAKwB,mBAAL,CAAyBD,OAAzB;AACH;;AAEDE,QAAAA,yBAAyB,GAAG;AACxB,cAAIF,OAAiB,GAAG,CACpBtH,iBAAiB,CAACiG,wBADE,CAAxB;AAGA,eAAKsB,mBAAL,CAAyBD,OAAzB;AACH;;AAEDC,QAAAA,mBAAmB,CAACD,OAAD,EAAoB;AACnC;AAAA;AAAA,8BAAMjG,MAAN,CAAa6F,WAAb,CAAyB;AAAA;AAAA,kCAAQ3F,EAAR,CAAWC,MAAX,CAAkBW,yBAA3C,EAAsE;AAClEyD,YAAAA,kBAAkB,EAAE;AAChB6B,cAAAA,IAAI,EAAEH;AADU;AAD8C,WAAtE;AAKH;;AAEDI,QAAAA,qBAAqB,CAAC/G,aAAD,EAA4C;AAC7D,cAAIgH,kBAAkB,GAAG;AAAA;AAAA,kCAAQzH,IAAR,CAAaU,aAAb,CAA2BgH,MAA3B,CAAkCjH,aAAlC,EAAiDkH,MAAjD,EAAzB;AACA,eAAKC,mBAAL,CAAyB,CACrB;AACI9D,YAAAA,GAAG,EAAEhE,iBAAiB,CAAC+F,oBAD3B;AAEI7C,YAAAA,IAAI,EAAEyE;AAFV,WADqB,CAAzB;AAMH;;AAEDI,QAAAA,yBAAyB,CAAClH,iBAAD,EAAoD;AACzE,cAAImH,sBAAsB,GAAG;AAAA;AAAA,kCAAQ9H,IAAR,CAAaY,iBAAb,CAA+B8G,MAA/B,CAAsC/G,iBAAtC,EAAyDgH,MAAzD,EAA7B;AACA,eAAKC,mBAAL,CAAyB,CACrB;AACI9D,YAAAA,GAAG,EAAEhE,iBAAiB,CAACiG,wBAD3B;AAEI/C,YAAAA,IAAI,EAAE8E;AAFV,WADqB,CAAzB;AAMH;;AAEDF,QAAAA,mBAAmB,CAACG,WAAD,EAA6C;AAC5D,cAAIA,WAAW,CAACxE,MAAZ,KAAuB,CAA3B,EAA8B;AAC1B;AACH;;AACD;AAAA;AAAA,8BAAMpC,MAAN,CAAa6F,WAAb,CAAyB;AAAA;AAAA,kCAAQ3F,EAAR,CAAWC,MAAX,CAAkBa,yBAA3C,EAAsE;AAClE6D,YAAAA,kBAAkB,EAAE;AAChBL,cAAAA,QAAQ,EAAEoC;AADM;AAD8C,WAAtE;AAKH;;AAEDC,QAAAA,mBAAmB,CAACC,OAAD,EAAoB;AACnC;AAAA;AAAA,8BAAM9G,MAAN,CAAa6F,WAAb,CAAyB;AAAA;AAAA,kCAAQ3F,EAAR,CAAWC,MAAX,CAAkB4G,yBAA3C,EAAsE;AAClEpB,YAAAA,kBAAkB,EAAE;AAChBS,cAAAA,IAAI,EAAEU;AADU;AAD8C,WAAtE;AAKH,SAtT8B,CAuT/B;;;AACA3F,QAAAA,kBAAkB,CAAC6F,SAAD,EAAwB;AAAA,cAAvBA,SAAuB;AAAvBA,YAAAA,SAAuB,GAAH,CAAG;AAAA;;AACtC;AAAA;AAAA,8BAAMhH,MAAN,CAAa6F,WAAb,CAAyB;AAAA;AAAA,kCAAQ3F,EAAR,CAAWC,MAAX,CAAkBS,kBAA3C,EAA+D;AAC3DkE,YAAAA,WAAW,EAAE;AACT,uBAAS;AAAA;AAAA,sCAAQjG,IAAR,CAAauG,aAAb,CAA2BC,0BAD3B;AAETJ,cAAAA,EAAE,EAAE+B;AAFK;AAD8C,WAA/D;AAMH,SA/T8B,CAgU/B;;;AACA5F,QAAAA,mBAAmB,CAAC4F,SAAD,EAAwB;AAAA,cAAvBA,SAAuB;AAAvBA,YAAAA,SAAuB,GAAH,CAAG;AAAA;;AACvC;AAAA;AAAA,8BAAMhH,MAAN,CAAa6F,WAAb,CAAyB;AAAA;AAAA,kCAAQ3F,EAAR,CAAWC,MAAX,CAAkBS,kBAA3C,EAA+D;AAC3DkE,YAAAA,WAAW,EAAE;AACT,uBAAS;AAAA;AAAA,sCAAQjG,IAAR,CAAauG,aAAb,CAA2BG,2BAD3B;AAETN,cAAAA,EAAE,EAAE+B;AAFK;AAD8C,WAA/D;AAMH,SAxU8B,CAyU/B;;;AACA3F,QAAAA,oBAAoB,CAAC2F,SAAD,EAAwB;AAAA,cAAvBA,SAAuB;AAAvBA,YAAAA,SAAuB,GAAH,CAAG;AAAA;;AACxC;AAAA;AAAA,8BAAMhH,MAAN,CAAa6F,WAAb,CAAyB;AAAA;AAAA,kCAAQ3F,EAAR,CAAWC,MAAX,CAAkBS,kBAA3C,EAA+D;AAC3DkE,YAAAA,WAAW,EAAE;AACT,uBAAS;AAAA;AAAA,sCAAQjG,IAAR,CAAauG,aAAb,CAA2BI,4BAD3B;AAETP,cAAAA,EAAE,EAAE+B;AAFK;AAD8C,WAA/D;AAMH,SAjV8B,CAkV/B;;;AACA1F,QAAAA,sBAAsB,CAAC0F,SAAD,EAAwB;AAAA,cAAvBA,SAAuB;AAAvBA,YAAAA,SAAuB,GAAH,CAAG;AAAA;;AAC1C;AAAA;AAAA,8BAAMhH,MAAN,CAAa6F,WAAb,CAAyB;AAAA;AAAA,kCAAQ3F,EAAR,CAAWC,MAAX,CAAkBS,kBAA3C,EAA+D;AAC3DkE,YAAAA,WAAW,EAAE;AACT,uBAAS;AAAA;AAAA,sCAAQjG,IAAR,CAAauG,aAAb,CAA2BK,8BAD3B;AAETR,cAAAA,EAAE,EAAE+B;AAFK;AAD8C,WAA/D;AAMH,SA1V8B,CA2V/B;;;AA3V+B,O", "sourcesContent": ["import csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';\r\nimport { MessageBox } from 'db://assets/bundles/common/script/ui/common/components/messagebox/MessageBox';\r\nimport { logWarn } from 'db://assets/scripts/utils/Logger';\r\nimport Long from 'long';\r\nimport { MyApp } from \"../../app/MyApp\";\r\nimport { DataEvent } from '../../event/DataEvent';\r\nimport { EventMgr } from '../../event/EventManager';\r\nimport { IData } from \"../DataManager\";\r\n\r\nexport enum ROLE_SETTING_TYPE {\r\n    ROLE_SETTING_UI_DATA = \"ROLE_SETTING_UI_DATA\",\r\n    ROLE_SETTING_BATTLE_DATA = \"ROLE_SETTING_BATTLE_DATA\",\r\n}\r\n\r\nexport class Role implements IData {\r\n\r\n    roleBaseAttr: csproto.comm.IRoleBaseAttr = new csproto.comm.RoleBaseAttr();\r\n    roleExtAttr: csproto.comm.IRoleExtAttr = new csproto.comm.RoleExtAttr();\r\n\r\n    maxLevel: number = 0;\r\n    curLevel: number = 0;\r\n    curExp: number = 0;\r\n    maxExp: number = 0;\r\n    localSettings: (csproto.comm.IClientSetting[]) = [];\r\n    settingDataUI: csproto.comm.SettingDataUI = new csproto.comm.SettingDataUI();\r\n    settingDataBattle: csproto.comm.SettingDataBattle = new csproto.comm.SettingDataBattle();\r\n\r\n    dailyCounter: Map<number, number> = new Map();\r\n    weekCounter: Map<number, number> = new Map();\r\n    monthlytCounter: Map<number, number> = new Map();\r\n    permanentCounter: Map<number, number> = new Map();\r\n\r\n    public init(): void {\r\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GET_ROLE_BASE_ATTR, this.onGetRoleBaseAttr, this);\r\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_LEVEL_UP, this.onLevelUp, this);\r\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GET_ROLE_EXT_ATTR, this.onGetRoleExtAttr, this);\r\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GET_ROLE_SIMPLE, this.onGetRoleSimple, this);\r\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GET_COUNTER, this.onGetCounter, this);\r\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GET_CLIENT_SETTING, this.onGetClientSetting, this);\r\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_SET_CLIENT_SETTING, this.onSetClientSetting, this);\r\n\r\n        this.cmdGetClientSettingUI();\r\n        this.cmdGetCounterDaily();\r\n        this.cmdGetCounterWeekly();\r\n        this.cmdGetCounterMonthly();\r\n        this.cmdGetCounterPermanent();\r\n    }\r\n    public update(): void {\r\n    }\r\n\r\n    public getCurLevelData(lvl: number) {\r\n        const tbResUpgrade = MyApp.lubanTables.TbResUpgrade;\r\n        const data = tbResUpgrade.get(lvl);\r\n        return data;\r\n    }\r\n    public setRole(data: csproto.cs.IS2CGetRole) {\r\n        if (!data) {\r\n            return;\r\n        }\r\n        this.setRoleBaseAttr(data.base!);\r\n    }\r\n    public setRoleBaseAttr(data: csproto.comm.IRoleBaseAttr) {\r\n        if (!data) {\r\n            return;\r\n        }\r\n        this.roleBaseAttr = data;\r\n        const tbResUpgrade = MyApp.lubanTables.TbResUpgrade;\r\n        const dataList = tbResUpgrade.getDataList();\r\n        if (dataList.length > 0) {\r\n            this.maxLevel = dataList[dataList.length - 1].roleLevel;\r\n        }\r\n        this.curLevel = data.level ?? 0;\r\n        this.curExp = data.xp ?? 0;\r\n        if (this.curLevel >= this.maxLevel) {\r\n            this.maxLevel = this.curLevel;\r\n            this.maxExp = 0;\r\n        } else {\r\n            this.maxExp = MyApp.lubanTables.TbResUpgrade.get(this.curLevel + 1)?.xp ?? 0;\r\n        }\r\n        if (this.curExp > this.maxExp) {\r\n            this.curExp = this.maxExp;\r\n        }\r\n        EventMgr.emit(DataEvent.RoleBaseAttrChange);\r\n        //MessageBox.show(\"获取成功\" + JSON.stringify(this.roleBaseAttr));\r\n    }\r\n    public getSetting(key: string) {\r\n        if (this.localSettings) {\r\n            const setting = this.localSettings.find(s => s.key === key);\r\n            if (setting) {\r\n                return setting.data;\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n    public getEndlessBestScore() {\r\n        return this.permanentCounter.get(csproto.comm.COUNTER_ID.COUNTER_ID_COMMON_GAME_ENDLESS_BEST_SCORE) ?? 0;\r\n    }\r\n    public getEndlessWeekBestScore() {\r\n        return this.weekCounter.get(csproto.comm.COUNTER_ID.COUNTER_ID_COMMON_GAME_ENDLESS_WEEK_BEST_SCORE) ?? 0;\r\n    }\r\n    public getEndlessDoubleRewardCount() {\r\n        return this.dailyCounter.get(csproto.comm.COUNTER_ID.COUNTER_ID_COMMON_GAME_ENDLESS_DOUBLE_REWARD_COUNT) ?? 0;\r\n    }\r\n    public getStoryDoubleRewardCount() {\r\n        return this.dailyCounter.get(csproto.comm.COUNTER_ID.COUNTER_ID_COMMON_GAME_STORY_DOUBLE_REWARD_COUNT) ?? 0;\r\n    }\r\n    public getSelfUin(){\r\n        return this.roleBaseAttr.uin;\r\n    }\r\n    public isSelfUin(uin: Long){\r\n        return this.roleBaseAttr.uin === uin;\r\n    }\r\n    //#region 收协议\r\n    onGetRoleBaseAttr(msg: csproto.cs.IS2CMsg): void {\r\n        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {\r\n            logWarn(\"NetMgr\", `onGetRoleBaseAttr failed ${msg.ret_code}`);\r\n\r\n        }\r\n        var data = msg.body?.get_role_base_attr;\r\n        if (!data) {\r\n            logWarn(\"NetMgr\", \"onGetRoleBaseAttr data is null\");\r\n            return;\r\n        }\r\n        this.setRoleBaseAttr(data.base!);\r\n    }\r\n    onLevelUp(msg: csproto.cs.IS2CMsg): void {\r\n        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {\r\n            logWarn(\"NetMgr\", `onLevelUp failed ${msg.ret_code}`);\r\n            return;\r\n        }\r\n        var data = msg.body?.level_up;\r\n        if (!data) {\r\n            logWarn(\"NetMgr\", \"onLevelUp data is null\");\r\n            return;\r\n        }\r\n        MessageBox.showLevelUP(data);\r\n    }\r\n    onGetRoleExtAttr(msg: csproto.cs.IS2CMsg): void {\r\n        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {\r\n            logWarn(\"NetMgr\", `onGetRoleExtAttr failed ${msg.ret_code}`);\r\n            return;\r\n        }\r\n        var data = msg.body?.get_role_ext_attr;\r\n        if (!data) {\r\n            logWarn(\"NetMgr\", \"onGetRoleExtAttr data is null\");\r\n            return;\r\n        }\r\n        this.roleExtAttr = data.ext!;\r\n        EventMgr.emit(DataEvent.RoleExtAttrChange);\r\n        //MessageBox.show(\"获取成功\" + JSON.stringify(this.roleExtAttr));\r\n    }\r\n    onGetRoleSimple(msg: csproto.cs.IS2CMsg): void {\r\n        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {\r\n            logWarn(\"NetMgr\", `onGetRoleSimple failed ${msg.ret_code}`);\r\n            return;\r\n        }\r\n        var data = msg.body?.get_role_simple;\r\n        if (!data) {\r\n            logWarn(\"NetMgr\", \"onGetRoleSimple data is null\");\r\n        }\r\n    }\r\n    onGetClientSetting(msg: csproto.cs.IS2CMsg): void {\r\n        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {\r\n            logWarn(\"NetMgr\", `onGetClientSetting failed ${msg.ret_code}`);\r\n            return;\r\n        }\r\n        var data = msg.body?.get_client_setting;\r\n        if (!data) {\r\n            logWarn(\"NetMgr\", \"onGetClientSetting data is null\");\r\n            return;\r\n        }\r\n        const settings = data.settings;\r\n        if (settings) {\r\n            settings.forEach(setting => {\r\n                const key = setting.key;\r\n                const data = setting.data;\r\n                if (key === ROLE_SETTING_TYPE.ROLE_SETTING_UI_DATA) {\r\n                    this.settingDataUI = csproto.comm.SettingDataUI.decode(data);\r\n                    //MessageBox.show(\"获取成功\" + JSON.stringify(this.settingDataUI));\r\n                } else if (key === ROLE_SETTING_TYPE.ROLE_SETTING_BATTLE_DATA) {\r\n                    this.settingDataBattle = csproto.comm.SettingDataBattle.decode(data);\r\n                    //MessageBox.show(\"获取成功\" + JSON.stringify(this.settingDataBattle));\r\n                }\r\n            })\r\n        }\r\n    }\r\n\r\n    onSetClientSetting(msg: csproto.cs.IS2CMsg): void {\r\n        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {\r\n            logWarn(\"NetMgr\", `onSetClientSetting failed ${msg.ret_code}`);\r\n            return;\r\n        }\r\n        var data = msg.body?.set_client_setting;\r\n        if (!data) {\r\n            logWarn(\"NetMgr\", \"onSetClientSetting data is null\");\r\n            return;\r\n        }\r\n        //MessageBox.show(\"设置成功\");\r\n        //没有Setting数据返回\r\n    }\r\n\r\n    onGetCounter(msg: csproto.cs.IS2CMsg): void {\r\n        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {\r\n            logWarn(\"NetMgr\", `onGetCounter failed ${msg.ret_code}`);\r\n            return;\r\n        }\r\n        var data = msg.body?.get_counter;\r\n        if (!data) {\r\n            logWarn(\"NetMgr\", \"onGetCounter data is null\");\r\n            return;\r\n        }\r\n        const counters = data.counters;\r\n        if (counters) {\r\n            counters.forEach(counter => {\r\n                const id = counter.id;\r\n                const value = counter.value;\r\n                if (counter.class === csproto.comm.COUNTER_CLASS.COUNTER_CLASS_COMMON_DAILY) {\r\n                    this.dailyCounter.set(id!, value!);\r\n                }\r\n                else if (counter.class === csproto.comm.COUNTER_CLASS.COUNTER_CLASS_COMMON_WEEKLY) {\r\n                    this.weekCounter.set(id!, value!);\r\n                }\r\n                else if (counter.class === csproto.comm.COUNTER_CLASS.COUNTER_CLASS_COMMON_MONTHLY) {\r\n                    this.monthlytCounter.set(id!, value!);\r\n                }\r\n                else if (counter.class === csproto.comm.COUNTER_CLASS.COUNTER_CLASS_COMMON_PERMANENT) {\r\n                    this.permanentCounter.set(id!, value!);\r\n                }\r\n            })\r\n        }\r\n    }\r\n    onDelClientSetting(msg: csproto.cs.IS2CMsg): void {\r\n        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {\r\n            logWarn(\"NetMgr\", `onDelClientSetting failed ${msg.ret_code}`);\r\n            return;\r\n        }\r\n        var data = msg.body?.del_client_setting;\r\n        if (!data) {\r\n            logWarn(\"NetMgr\", \"onDelClientSetting data is null\");\r\n            return;\r\n        }\r\n    }\r\n    //#endregion\r\n    //#region 发协议\r\n    cmdGetRoleBaseAttr() {\r\n        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GET_ROLE_BASE_ATTR, {\r\n            get_role_base_attr: {\r\n            }\r\n        })\r\n    }\r\n    //获取角色的扩展信息\r\n    cmdGetRoleExtAttr() {\r\n        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GET_ROLE_EXT_ATTR, {\r\n            get_role_ext_attr: {\r\n            }\r\n        })\r\n    }\r\n    CmdGetRoleSimple(uin: Long, area_id: number) {\r\n        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GET_ROLE_SIMPLE, {\r\n            get_role_simple: {\r\n                uin: uin,\r\n                area_id: area_id\r\n            }\r\n        })\r\n    }\r\n    cmdGetClientSettingUI() {\r\n        let getKeys: string[] = [\r\n            ROLE_SETTING_TYPE.ROLE_SETTING_UI_DATA,\r\n        ]\r\n        this.cmdGetClientSetting(getKeys);\r\n    }\r\n\r\n    cmdGetClientSettingBattle() {\r\n        let getKeys: string[] = [\r\n            ROLE_SETTING_TYPE.ROLE_SETTING_BATTLE_DATA,\r\n        ]\r\n        this.cmdGetClientSetting(getKeys);\r\n    }\r\n\r\n    cmdGetClientSetting(getKeys: string[]) {\r\n        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GET_CLIENT_SETTING, {\r\n            get_client_setting: {\r\n                keys: getKeys\r\n            }\r\n        })\r\n    }\r\n\r\n    cmdSetClientSettingUI(settingDataUI: csproto.comm.SettingDataUI) {\r\n        let settingDataUIBytes = csproto.comm.SettingDataUI.encode(settingDataUI).finish();\r\n        this.cmdSetClientSetting([\r\n            {\r\n                key: ROLE_SETTING_TYPE.ROLE_SETTING_UI_DATA,\r\n                data: settingDataUIBytes,\r\n            }\r\n        ]);\r\n    }\r\n\r\n    cmdSetClientSettingBattle(settingDataBattle: csproto.comm.SettingDataBattle) {\r\n        let settingDataBattleBytes = csproto.comm.SettingDataBattle.encode(settingDataBattle).finish();\r\n        this.cmdSetClientSetting([\r\n            {\r\n                key: ROLE_SETTING_TYPE.ROLE_SETTING_BATTLE_DATA,\r\n                data: settingDataBattleBytes,\r\n            }\r\n        ]);\r\n    }\r\n\r\n    cmdSetClientSetting(newSettings: csproto.comm.IClientSetting[]) {\r\n        if (newSettings.length === 0) {\r\n            return;\r\n        }\r\n        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_SET_CLIENT_SETTING, {\r\n            set_client_setting: {\r\n                settings: newSettings\r\n            }\r\n        })\r\n    }\r\n\r\n    cmdDelClientSetting(delKeys: string[]) {\r\n        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_DEL_CLIENT_SETTING, {\r\n            del_client_setting: {\r\n                keys: delKeys\r\n            }\r\n        })\r\n    }\r\n    //每日数据  COUNTER_CLASS_COMMON_DAILY;\r\n    cmdGetCounterDaily(counterID: number = 0) {\r\n        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GET_COUNTER, {\r\n            get_counter: {\r\n                \"class\": csproto.comm.COUNTER_CLASS.COUNTER_CLASS_COMMON_DAILY,\r\n                id: counterID\r\n            }\r\n        })\r\n    }\r\n    //每周数据  COUNTER_CLASS_COMMON_WEEKLY\r\n    cmdGetCounterWeekly(counterID: number = 0) {\r\n        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GET_COUNTER, {\r\n            get_counter: {\r\n                \"class\": csproto.comm.COUNTER_CLASS.COUNTER_CLASS_COMMON_WEEKLY,\r\n                id: counterID\r\n            }\r\n        })\r\n    }\r\n    //每月数据  COUNTER_CLASS_COMMON_MONTHLY\r\n    cmdGetCounterMonthly(counterID: number = 0) {\r\n        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GET_COUNTER, {\r\n            get_counter: {\r\n                \"class\": csproto.comm.COUNTER_CLASS.COUNTER_CLASS_COMMON_MONTHLY,\r\n                id: counterID\r\n            }\r\n        })\r\n    }\r\n    //永久数据  COUNTER_CLASS_COMMON_PERMANENT\r\n    cmdGetCounterPermanent(counterID: number = 0) {\r\n        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GET_COUNTER, {\r\n            get_counter: {\r\n                \"class\": csproto.comm.COUNTER_CLASS.COUNTER_CLASS_COMMON_PERMANENT,\r\n                id: counterID\r\n            }\r\n        })\r\n    }\r\n    //#endregion\r\n}\r\n"]}
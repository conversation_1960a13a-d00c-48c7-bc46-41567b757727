import { _decorator, instantiate, Prefab } from "cc";
import { MyApp } from 'db://assets/bundles/common/script/app/MyApp';
import { Wave } from "../../game/wave/Wave";
import { GameIns } from "../../game/GameIns";
import { LevelDataEventTrigger, LevelDataEventTriggerType } from "./LevelDataEventTrigger";

export class LevelDataEventWaveGroup {
    public waveUUID: string[] = [];
    public weight: number = 50;
}

export enum eEventTriggerWaveTag {
    None = 0,
    LastWave = 1, // 2, 4, 8
}

export class LevelDataEventTriggerWave extends LevelDataEventTrigger {
    // 是否是当前的最后一波
    public waveTag: number = 0;
    public waveGroup: LevelDataEventWaveGroup[] = [];
    private _isTriggered: boolean = false;
    private _selectedWaveGroup: LevelDataEventWaveGroup | null = null;
    
    constructor() {
        super(LevelDataEventTriggerType.Wave);
    }
    
    public onInit() {
        // 提前创建好wave，但不执行
        if (this.waveGroup.length > 0) {
            let totalWeight = 0;
            this.waveGroup.forEach(waveGroup => {
                totalWeight += waveGroup.weight;
            });

            let randomWeight = Math.floor(GameIns.battleManager.random() * totalWeight);
            let curWeight = 0;
            
            for (let waveGroup of this.waveGroup) {
                curWeight += waveGroup.weight;
                if (randomWeight <= curWeight) {
                    this._selectedWaveGroup = waveGroup;
                    break;
                }
            }
        }
    }

    public onTrigger(x: number, y: number) {
        this._selectedWaveGroup!.waveUUID.forEach((waveUUID) => {
            const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, waveUUID)
            MyApp.resMgr.load(path, (err: Error | null, prefab: Prefab) => {
                if (err) {
                    console.error('LevelDataEventTriggerWave', " onInit load wave prefab err", err);
                    return;
                }
                const waveComp = instantiate(prefab).getComponent(Wave);
                GameIns.waveManager.addWaveByLevel(waveComp!, x, y);
            });
        });

        this._isTriggered = true;
    }

    public isWaveCompleted(): boolean {
        if (!this._isTriggered) return false;

        this._selectedWaveGroup!.waveUUID.forEach((waveUUID) => {
            if (!GameIns.waveManager.isWaveCompleted(waveUUID)) {
                return false;
            }
        });

        return true;
    }

    public isWaveTriggered(): boolean {
        return this._isTriggered;
    }

    public fromJSON(obj: any): void {
        super.fromJSON(obj);
    }

    public toJSON(): any {
        // avoid private properties
        return {
            _type: this._type,
            waveTag: this.waveTag,
            waveGroup: this.waveGroup,
        };
    }
}



import { director, Rect } from "cc";
import { logInfo, logWarn } from "db://assets/scripts/utils/Logger";
import Long from "long";
import { GameConst } from "../../../../../scripts/core/base/GameConst";
import { SingletonBase } from "../../../../../scripts/core/base/SingletonBase";
import { UIMgr } from "../../../../../scripts/core/base/UIMgr";
import { MyApp } from "../../app/MyApp";
import { ModeType, randStrategy, ResChapter, ResGameMode } from "../../autogen/luban/schema";
import { DataMgr } from "../../data/DataManager";
import EventManager, { EventMgr } from "../../event/EventManager";
import { HomeUIEvent } from "../../event/HomeUIEvent";
import { GameReviveUI } from "../../ui/gameui/game/GameReviveUI";
import { MBoomUI } from "../../ui/gameui/game/MBoomUI";
import { LoadingUI } from "../../ui/gameui/LoadingUI";
import { BottomUI } from "../../ui/home/<USER>";
import { RogueUI } from "../../ui/home/<USER>/RogueUI";
import { HomeUI } from "../../ui/home/<USER>";
import { TopUI } from "../../ui/home/<USER>";
import { BulletSystem } from "../bullet/BulletSystem";
import { GameEvent } from "../event/GameEvent";
import { GameIns } from "../GameIns";
import GameMapRun from "../ui/map/GameMapRun";
import { lcgRand } from "../utils/Rand";
import { GameMain } from "../scenes/GameMain";

enum RAND_STRATEGY {
    WEIGHT_PRUE = 1, // 纯权重随机
    WEIGHT_NO_REPEAT = 2, //权重随机，不重复
    ORDER = 3 // 按顺序
}

export class BattleManager extends SingletonBase<BattleManager> {

    initBattleEnd: boolean = false;
    isGameStart: boolean = false;
    animSpeed: number = 1;

    _modeConfig: ResGameMode | null = null;
    curLevel: number = 0;//小阶段
    _chapterConfig: ResChapter | null = null;
    public get chapterConfig(): ResChapter | null { return this._chapterConfig; }
    private _levelList: number[] = []; // 随机出来的关卡列表（这个列表长度可能会大于章节表中的关卡数量）
    public get levelList(): number[] { return this._levelList; }
    public get modeConfig(): ResGameMode | null { return this._modeConfig; }

    _loadTotal = 0;
    _loadCount = 0;
    _rand: lcgRand = new lcgRand();

    constructor() {
        super();
        EventManager.Instance.on(GameEvent.onNetGameStart, this.onNetGameStart, this);
        EventManager.Instance.on(GameEvent.onNetGameOver, this.onNetGameOver, this);
    }

    //战斗开始接口
    startGameByMode(modeID: number, curLevel: number = 0, randSeed: number = Date.now()) {
        this._rand.seed = Long.fromNumber(randSeed);
        let modeConfig = MyApp.lubanTables.TbResGameMode.get(modeID);
        if (modeConfig == null) {
            logWarn("BattleManager", `can not find mode config by id ${modeID}`);
            return;
        }
        this._modeConfig = modeConfig;
        this.curLevel = curLevel;

        DataMgr.gameLogic.cmdGameStart(modeID);
    }

    async onNetGameStart() {
        await UIMgr.openUI(LoadingUI)
        EventMgr.emit(HomeUIEvent.Leave)
        GameIns.mainPlaneManager.setPlaneData(DataMgr.planeInfo.getPlaneInfoById());
        this._initLevelList(this._modeConfig!.chapterID);

        director.loadScene("Game")
    }

    onNetGameOver() {

    }

    // 根据策略随机出关卡列表
    private _initLevelList(chapterID: number) {
        this._chapterConfig = MyApp.lubanTables.TbResChapter.get(70001)!;/*(chapterID)*/;
        if (this._chapterConfig == null) {
            logWarn("BattleManager", `can not find chapter config by id ${chapterID}`);
            return;
        }

        // 随机出关卡组
        const levelGroupList = this._randomSelection(this._chapterConfig.strategyList, this._chapterConfig.levelGroupCount, this._chapterConfig.strategy);
        if (levelGroupList.length === 0) {
            logWarn('BattleManager', " levelGroupList is null");
            return;
        }

        // 随机出关卡
        this._levelList = [];
        for (const levelGroupID of levelGroupList) {
            const levelGroupData = MyApp.lubanTables.TbResLevelGroup.get(levelGroupID);
            if (levelGroupData == null) {
                logWarn('BattleManager', " levelGroupData is null");
                continue;
            }

            this._levelList.push(...this._randomSelection(levelGroupData.normSTList, levelGroupData.normLevelCount, levelGroupData.normLevelST));
            this._levelList.push(...this._randomSelection(levelGroupData.bossSTList, levelGroupData.bossLevelCount, levelGroupData.bossLevelST));
        }
        logInfo('BattleManager', ` _levelList: ${this._levelList}`);
    }

    mainReset() {
        GameIns.enemyManager.mainReset();
        GameIns.bossManager.mainReset();
        GameIns.waveManager.reset();
        GameIns.mainPlaneManager.mainReset();
        GameMapRun.instance!.reset();
        GameMapRun.instance!.clear();
        GameIns.hurtEffectManager.clear();
        GameIns.gameStateManager.reset();
        BulletSystem.destroy();
        this.isGameStart = false;
    }

    subReset() {
        this.animSpeed = 1;
        this.isGameStart = false;
        this.initBattleEnd = false;

        GameIns.mainPlaneManager.subReset();
        GameIns.gameStateManager.reset();
        GameIns.waveManager.reset();
        GameIns.enemyManager.subReset();
        GameIns.bossManager.subReset();
    }

    /**
     * 检查所有资源是否加载完成
     */
    async checkLoadFinish() {
        this._loadCount++;
        // let loadingUI = UIMgr.get(LoadingUI)
        // loadingUI.updateProgress(this._loadCount / this._loadTotal)
        if (this._loadCount >= this._loadTotal) {
            await UIMgr.closeUI(LoadingUI)
            EventManager.Instance.emit(GameEvent.GameLoadEnd)
            this.initBattle();
        }
    }

    addLoadCount(count: number) {
        this._loadTotal += count;
    }

    startLoading() {
        GameIns.mainPlaneManager.preload();
        GameIns.hurtEffectManager.preLoad();//伤害特效资源
        GameMapRun.instance!.initData();//地图背景初始化
        GameIns.enemyManager.preLoad();//敌人资源
        GameIns.bossManager.preLoad();//boss资源
    }

    initBattle() {
        GameIns.mainPlaneManager.mainPlane!.planeIn();
        BulletSystem.init(new Rect(0, 0, GameConst.ViewBattleWidth, GameConst.ViewHeight));
    }

    onPlaneIn() {
        this.initBattleEnd = true;
        EventManager.Instance.emit(GameEvent.GameMainPlaneIn)
    }

    beginBattle() {
        if (this.initBattleEnd && !this.isGameStart) {
            this.isGameStart = true;
            EventManager.Instance.emit(GameEvent.GameStart)
            GameIns.waveManager.gameStart();
            GameIns.gameStateManager.gameStart();

            GameIns.mainPlaneManager.mainPlane!.begine();
        }
    }

    /**
     * 更新游戏逻辑
     * @param {number} dt 每帧的时间间隔
     */
    updateGameLogic(dt: number) {
        dt = dt * this.animSpeed;
        if (GameIns.gameStateManager.isGameOver()) {
            if (GameIns.gamePlaneManager) {
                GameIns.gamePlaneManager.enemyTarget = null;
            }
            return;
        }

        if (GameIns.gameStateManager.isInBattle() || GameIns.gameStateManager.isGameWillOver()) {
            GameIns.gameDataManager.gameTime += dt;
            GameMapRun.instance?.updateGameLogic(dt)
            GameIns.gamePlaneManager.updateGameLogic(dt);
            GameIns.mainPlaneManager.updateGameLogic(dt);
            GameIns.waveManager.updateGameLogic(dt);
            GameIns.enemyManager.updateGameLogic(dt);
            GameIns.bossManager.updateGameLogic(dt);
            GameIns.gameStateManager.updateGameLogic(dt);

            //子弹发射器系统
            BulletSystem.tick(dt);

            GameIns.fColliderManager.updateGameLogic(dt);
        } else if (GameIns.gamePlaneManager) {
            GameIns.gamePlaneManager.enemyTarget = null;
        }
    }

    setTouchState(isTouch: boolean) {
        if (isTouch) {
            this.beginBattle();
            this.animSpeed = 1;
        } else {
            this.animSpeed = 0.2;
        }
        GameIns.enemyManager.setAnimSpeed(this.animSpeed);
        GameIns.bossManager.setAnimSpeed(this.animSpeed);
        GameIns.mainPlaneManager.mainPlane!.setAnimSpeed(this.animSpeed);
        GameMain.instance?.GameFightUI!.setTouchState(isTouch);
    }

    /**
     * 战斗复活逻辑
     */
    relifeBattle() {
        GameIns.gameStateManager.gameResume();
        GameIns.mainPlaneManager.revive();
    }

    setGameEnd(isWin: boolean) {
        if (isWin) {
            if (this.checkNextlevel()) {//判断是否有下一关
                UIMgr.openUI(RogueUI, () => {
                    this.startNextBattle();
                });
                return;
            }
        } else {
            GameIns.gameStateManager.gamePause();
            if (GameIns.mainPlaneManager.checkCanRevive()) {// 判断是否可以复活
                UIMgr.openUI(GameReviveUI);
                return;
            }
        }
        GameIns.mainPlaneManager.mainPlane!.hpNode!.active = false;
        this.endBattle();
        GameMain.instance!.showGameResult(isWin);
        DataMgr.gameLogic.cmdGameEnd(GameIns.gameDataManager.getGameResultData(), GameIns.gameDataManager.getGameLevelResultData());
    }

    checkNextlevel() {
        if (this._modeConfig!.modeType == ModeType.ENDLESS) {
            return true;
        }
        return this.curLevel + 1 <= this._chapterConfig!.levelCount;
    }
    /**
     * 继续下一场战斗
     */
    startNextBattle() {
        this.subReset();
        this.curLevel += 1;
        this.initBattle();
    }

    /**
     * 结束战斗
     */
    endBattle() {
        BulletSystem.destroy(false, false);
        GameMain.instance?.GameFightUI!.reset();
        GameIns.gameStateManager.gameOver();
    }


    async quitBattle() {
        this.mainReset();
        UIMgr.closeUI(MBoomUI)
        await UIMgr.openUI(HomeUI)
        await UIMgr.openUI(BottomUI)
        await UIMgr.openUI(TopUI)
        director.loadScene("Main");
    }

    bossChangeFinish(tip: string) {
        // const bossEnterDialog = GameIns.uiManager.getDialog(BossEnterDialog.default);
        // if (bossEnterDialog) {
        //     bossEnterDialog.node.active = true;
        //     GameIns.mainPlaneManager.moveAble = false;
        //     bossEnterDialog.showTips(bossName);
        // }
    }

    bossWillEnter() {
        // GameIns.mainPlaneManager.mainPlane!.setFireEnable(false);
        // GameIns.mainPlaneManager.moveAble = false;
    }
    /**
     * 开始Boss战斗
     */
    bossFightStart() {
        GameIns.mainPlaneManager.mainPlane!.setFireEnable(true);
        GameIns.mainPlaneManager.mainPlane!.setMoveAble(true);
        GameIns.bossManager.bossFightStart();
    }

    random(): number {
        return this._rand.random();
    }

    /**
     * 策略：
     * 1.严格按权重比例随机选择元素
     * 2.严格按权重比例随机选择元素，不重复
     * 3.按顺序选择元素
     * @param STList 带权重的元素数组
     * @param count 需要选择的元素数量
     * @returns 选中元素的ID数组
     */
    private _randomSelection(STList: randStrategy[], count: number, strategy: RAND_STRATEGY): number[] {
        if (STList.length === 0 || count <= 0) return [];

        const results: number[] = [];
        if (strategy === RAND_STRATEGY.WEIGHT_PRUE) {
            // 计算总权重
            const totalWeight = STList.reduce((sum, item) => sum + item.Weight, 0);

            // 如果所有权重都为0，则转为均匀随机
            if (totalWeight === 0) {
                for (let i = 0; i < count; i++) {
                    const randomIndex = Math.floor(GameIns.battleManager.random() * STList.length);
                    results.push(STList[randomIndex].ID);
                }
                return results;
            }

            // 严格按权重比例随机选择
            for (let i = 0; i < count; i++) {
                // 生成[0, totalWeight)区间的随机数
                const randomValue = GameIns.battleManager.random() * totalWeight;

                // 遍历查找随机数对应的元素
                let cumulativeWeight = 0;
                for (const item of STList) {
                    cumulativeWeight += item.Weight;
                    if (randomValue < cumulativeWeight) {
                        results.push(item.ID);
                        break;
                    }
                }
            }
        } else if (strategy === RAND_STRATEGY.WEIGHT_NO_REPEAT) {
            // 计算总权重
            const totalWeight = STList.reduce((sum, item) => sum + item.Weight, 0);

            // 如果所有权重都为0，则转为均匀随机
            if (totalWeight === 0) {
                for (let i = 0; i < count; i++) {
                    let randomIndex = Math.floor(GameIns.battleManager.random() * STList.length);
                    // 避免重复选择相同的ID
                    if (i > 0 && STList[randomIndex].ID === results[i - 1]) {
                        // 如果与上一次选择的相同，选择下一个（循环）
                        randomIndex = (randomIndex + 1) % STList.length;
                    }
                    results.push(STList[randomIndex].ID);
                }
                return results;
            }

            // 创建副本以避免修改原始数据
            const tempList = [...STList];

            let lastSelectedId = -1;
            for (let i = 0; i <= count; i++) {
                // 如果上一次选择的ID存在，且它在当前列表中，调整其位置
                if (lastSelectedId !== -1) {
                    const lastSelectedIndex = tempList.findIndex(item => item.ID === lastSelectedId);
                    if (lastSelectedIndex !== -1 && lastSelectedIndex < tempList.length - 1) {
                        // 将上一次选择的ID与下一个元素交换位置
                        [tempList[lastSelectedIndex], tempList[lastSelectedIndex + 1]] =
                            [tempList[lastSelectedIndex + 1], tempList[lastSelectedIndex]];
                    }
                }

                // 生成[0, totalWeight)区间的随机数
                const randomValue = GameIns.battleManager.random() * totalWeight;

                // 遍历查找随机数对应的元素
                let cumulativeWeight = 0;
                let selectedIndex = -1;

                for (let j = 0; j < tempList.length; j++) {
                    cumulativeWeight += tempList[j].Weight;
                    if (randomValue < cumulativeWeight) {
                        selectedIndex = j;
                        break;
                    }
                }

                // 如果未找到有效索引，选择最后一个元素
                if (selectedIndex === -1) {
                    selectedIndex = tempList.length - 1;
                }

                // 获取选中的ID
                const selectedId = tempList[selectedIndex].ID;
                results.push(selectedId);

                // 更新上一次选择的ID
                lastSelectedId = selectedId;
            }
        } else if (strategy === RAND_STRATEGY.ORDER) {
            // 按顺序选择元素，遇到ID为0时从数组开头重新开始
            let currentIndex = 0;

            for (let i = 0; i < count; i++) {
                // 如果当前元素的ID为0，则重置到数组开头
                if (STList[currentIndex].ID === 0) {
                    currentIndex = 0;

                    // 如果数组开头的元素ID也为0，则跳过所有ID为0的元素
                    while (currentIndex < STList.length && STList[currentIndex].ID === 0) {
                        currentIndex++;
                    }

                    // 如果所有元素ID都为0，则无法选择，跳出循环
                    if (currentIndex >= STList.length) {
                        break;
                    }
                }

                // 选择当前元素
                results.push(STList[currentIndex].ID);

                // 移动到下一个元素
                currentIndex++;

                // 如果到达数组末尾，回到开头
                if (currentIndex >= STList.length) {
                    currentIndex = 0;
                }
            }
        }

        return results;
    }

}
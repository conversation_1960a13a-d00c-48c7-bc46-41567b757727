[{"id": 10100001, "star_level": 1, "name": "小蓝", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能,活动解锁", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 1430, "HPRecovery": 0, "Attack": 143, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 2, "MaxEnergy": 0, "EnergyRecovery": 10}, "effects": [{"effect_id": 5001}], "materials": [{"material_id": 80200101, "material_count": 40}]}, {"id": 10010002, "star_level": 2, "name": "小蓝", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 1940, "HPRecovery": 0, "Attack": 194, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 3, "MaxEnergy": 0, "EnergyRecovery": 12}, "effects": [{"effect_id": 5001}, {"effect_id": 5002}], "materials": [{"material_id": 80200101, "material_count": 60}]}, {"id": 10010003, "star_level": 3, "name": "小蓝", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 2500, "HPRecovery": 0, "Attack": 250, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 4, "MaxEnergy": 0, "EnergyRecovery": 15}, "effects": [{"effect_id": 5001}, {"effect_id": 5002}, {"effect_id": 5003}], "materials": [{"material_id": 80200101, "material_count": 100}]}, {"id": 10010004, "star_level": 4, "name": "小蓝", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 3170, "HPRecovery": 0, "Attack": 317, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 5, "MaxEnergy": 0, "EnergyRecovery": 22}, "effects": [{"effect_id": 5001}, {"effect_id": 5002}, {"effect_id": 5003}, {"effect_id": 5004}], "materials": [{"material_id": 80200101, "material_count": 180}]}, {"id": 10010005, "star_level": 5, "name": "小蓝", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 3960, "HPRecovery": 0, "Attack": 396, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 6, "MaxEnergy": 0, "EnergyRecovery": 27}, "effects": [{"effect_id": 5001}, {"effect_id": 5002}, {"effect_id": 5003}, {"effect_id": 5004}, {"effect_id": 5005}], "materials": [{"material_id": 80200101, "material_count": 300}]}, {"id": 10010006, "star_level": 6, "name": "小蓝", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 5110, "HPRecovery": 0, "Attack": 511, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 7, "MaxEnergy": 0, "EnergyRecovery": 33}, "effects": [{"effect_id": 5001}, {"effect_id": 5002}, {"effect_id": 5003}, {"effect_id": 5004}, {"effect_id": 5005}, {"effect_id": 5006}], "materials": [{"material_id": 80200101, "material_count": 500}]}, {"id": 10010007, "star_level": 7, "name": "小蓝", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 6350, "HPRecovery": 0, "Attack": 635, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 8, "MaxEnergy": 0, "EnergyRecovery": 40}, "effects": [{"effect_id": 5001}, {"effect_id": 5002}, {"effect_id": 5003}, {"effect_id": 5004}, {"effect_id": 5005}, {"effect_id": 5006}], "materials": [{"material_id": 80200101, "material_count": 800}]}, {"id": 10010008, "star_level": 8, "name": "小蓝", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 7810, "HPRecovery": 0, "Attack": 781, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 9, "MaxEnergy": 0, "EnergyRecovery": 50}, "effects": [{"effect_id": 5001}, {"effect_id": 5002}, {"effect_id": 5003}, {"effect_id": 5004}, {"effect_id": 5005}, {"effect_id": 5006}], "materials": [{"material_id": 80200101, "material_count": 1200}]}, {"id": 10010101, "star_level": 1, "name": "小蓝1", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能,活动解锁", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 1430, "HPRecovery": 0, "Attack": 143, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 2, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200102, "material_count": 40}]}, {"id": 10010102, "star_level": 2, "name": "小蓝1", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 1940, "HPRecovery": 0, "Attack": 194, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 2, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200102, "material_count": 60}]}, {"id": 10010103, "star_level": 3, "name": "小蓝1", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 2500, "HPRecovery": 0, "Attack": 250, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 2, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200102, "material_count": 100}]}, {"id": 10010104, "star_level": 4, "name": "小蓝1", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 3170, "HPRecovery": 0, "Attack": 317, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 2, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200102, "material_count": 180}]}, {"id": 10010105, "star_level": 5, "name": "小蓝1", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 3960, "HPRecovery": 0, "Attack": 396, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 2, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200102, "material_count": 300}]}, {"id": 10010106, "star_level": 6, "name": "小蓝1", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 5110, "HPRecovery": 0, "Attack": 511, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 2, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200102, "material_count": 500}]}, {"id": 10010107, "star_level": 7, "name": "小蓝1", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 6350, "HPRecovery": 0, "Attack": 635, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 2, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200102, "material_count": 800}]}, {"id": 10010108, "star_level": 8, "name": "小蓝1", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 7810, "HPRecovery": 0, "Attack": 781, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 2, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200102, "material_count": 1200}]}, {"id": 10010201, "star_level": 1, "name": "小蓝2", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能,活动解锁", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 1430, "HPRecovery": 0, "Attack": 143, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 2, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200103, "material_count": 40}]}, {"id": 10010202, "star_level": 2, "name": "小蓝2", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 1940, "HPRecovery": 0, "Attack": 194, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 2, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200103, "material_count": 60}]}, {"id": 10010203, "star_level": 3, "name": "小蓝2", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 2500, "HPRecovery": 0, "Attack": 250, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 2, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200103, "material_count": 100}]}, {"id": 10010204, "star_level": 4, "name": "小蓝2", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 3170, "HPRecovery": 0, "Attack": 317, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 2, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200103, "material_count": 180}]}, {"id": 10010205, "star_level": 5, "name": "小蓝2", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 3960, "HPRecovery": 0, "Attack": 396, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 2, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200103, "material_count": 300}]}, {"id": 10010206, "star_level": 6, "name": "小蓝2", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 5110, "HPRecovery": 0, "Attack": 511, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 2, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200103, "material_count": 500}]}, {"id": 10010207, "star_level": 7, "name": "小蓝2", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 6350, "HPRecovery": 0, "Attack": 635, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 2, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200103, "material_count": 800}]}, {"id": 10010208, "star_level": 8, "name": "小蓝2", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 7810, "HPRecovery": 0, "Attack": 781, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 2, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200103, "material_count": 1200}]}, {"id": 10010301, "star_level": 1, "name": "小蓝3", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能,活动解锁", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 1430, "HPRecovery": 0, "Attack": 143, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200104, "material_count": 40}]}, {"id": 10010302, "star_level": 2, "name": "小蓝3", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 1940, "HPRecovery": 0, "Attack": 194, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200104, "material_count": 60}]}, {"id": 10010303, "star_level": 3, "name": "小蓝3", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 2500, "HPRecovery": 0, "Attack": 250, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200104, "material_count": 100}]}, {"id": 10010304, "star_level": 4, "name": "小蓝3", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 3170, "HPRecovery": 0, "Attack": 317, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200104, "material_count": 180}]}, {"id": 10010305, "star_level": 5, "name": "小蓝3", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 3960, "HPRecovery": 0, "Attack": 396, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200104, "material_count": 300}]}, {"id": 10010306, "star_level": 6, "name": "小蓝3", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 5110, "HPRecovery": 0, "Attack": 511, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200104, "material_count": 500}]}, {"id": 10010307, "star_level": 7, "name": "小蓝3", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 6350, "HPRecovery": 0, "Attack": 635, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200104, "material_count": 800}]}, {"id": 10010308, "star_level": 8, "name": "小蓝3", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 7810, "HPRecovery": 0, "Attack": 781, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200104, "material_count": 1200}]}, {"id": 10010401, "star_level": 1, "name": "小蓝4", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能,活动解锁", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 1430, "HPRecovery": 0, "Attack": 143, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200105, "material_count": 40}]}, {"id": 10010402, "star_level": 2, "name": "小蓝4", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 1940, "HPRecovery": 0, "Attack": 194, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200105, "material_count": 60}]}, {"id": 10010403, "star_level": 3, "name": "小蓝4", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 2500, "HPRecovery": 0, "Attack": 250, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200105, "material_count": 100}]}, {"id": 10010404, "star_level": 4, "name": "小蓝4", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 3170, "HPRecovery": 0, "Attack": 317, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200105, "material_count": 180}]}, {"id": 10010405, "star_level": 5, "name": "小蓝4", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 3960, "HPRecovery": 0, "Attack": 396, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200105, "material_count": 300}]}, {"id": 10010406, "star_level": 6, "name": "小蓝4", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 5110, "HPRecovery": 0, "Attack": 511, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200105, "material_count": 500}]}, {"id": 10010407, "star_level": 7, "name": "小蓝4", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 6350, "HPRecovery": 0, "Attack": 635, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200105, "material_count": 800}]}, {"id": 10010408, "star_level": 8, "name": "小蓝4", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 7810, "HPRecovery": 0, "Attack": 781, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200105, "material_count": 1200}]}, {"id": 10010501, "star_level": 1, "name": "小蓝5", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能,活动解锁", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 1430, "HPRecovery": 0, "Attack": 143, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200106, "material_count": 40}]}, {"id": 10010502, "star_level": 2, "name": "小蓝5", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 1940, "HPRecovery": 0, "Attack": 194, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200106, "material_count": 60}]}, {"id": 10010503, "star_level": 3, "name": "小蓝5", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 2500, "HPRecovery": 0, "Attack": 250, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200106, "material_count": 100}]}, {"id": 10010504, "star_level": 4, "name": "小蓝5", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 3170, "HPRecovery": 0, "Attack": 317, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200106, "material_count": 180}]}, {"id": 10010505, "star_level": 5, "name": "小蓝5", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 3960, "HPRecovery": 0, "Attack": 396, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200106, "material_count": 300}]}, {"id": 10010506, "star_level": 6, "name": "小蓝5", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 5110, "HPRecovery": 0, "Attack": 511, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200106, "material_count": 500}]}, {"id": 10010507, "star_level": 7, "name": "小蓝5", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 6350, "HPRecovery": 0, "Attack": 635, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200106, "material_count": 800}]}, {"id": 10010508, "star_level": 8, "name": "小蓝5", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 7810, "HPRecovery": 0, "Attack": 781, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200106, "material_count": 1200}]}, {"id": 10010601, "star_level": 1, "name": "小蓝6", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能,活动解锁", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 1430, "HPRecovery": 0, "Attack": 143, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200107, "material_count": 40}]}, {"id": 10010602, "star_level": 2, "name": "小蓝6", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 1940, "HPRecovery": 0, "Attack": 194, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200107, "material_count": 60}]}, {"id": 10010603, "star_level": 3, "name": "小蓝6", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 2500, "HPRecovery": 0, "Attack": 250, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200107, "material_count": 100}]}, {"id": 10010604, "star_level": 4, "name": "小蓝6", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 3170, "HPRecovery": 0, "Attack": 317, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200107, "material_count": 180}]}, {"id": 10010605, "star_level": 5, "name": "小蓝6", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 3960, "HPRecovery": 0, "Attack": 396, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200107, "material_count": 300}]}, {"id": 10010606, "star_level": 6, "name": "小蓝6", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 5110, "HPRecovery": 0, "Attack": 511, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200107, "material_count": 500}]}, {"id": 10010607, "star_level": 7, "name": "小蓝6", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 6350, "HPRecovery": 0, "Attack": 635, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200107, "material_count": 800}]}, {"id": 10010608, "star_level": 8, "name": "小蓝6", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 3, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 7810, "HPRecovery": 0, "Attack": 781, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 0, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200107, "material_count": 1200}]}, {"id": 10020001, "star_level": 1, "name": "小紫", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能,活动解锁", "quality": 4, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 1810, "HPRecovery": 0, "Attack": 181, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 2, "MaxEnergy": 0, "EnergyRecovery": 10}, "effects": [{"effect_id": 5001}], "materials": [{"material_id": 80200201, "material_count": 40}]}, {"id": 10002002, "star_level": 2, "name": "小紫", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 4, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 2380, "HPRecovery": 0, "Attack": 238, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 3, "MaxEnergy": 0, "EnergyRecovery": 12}, "effects": [{"effect_id": 5001}, {"effect_id": 5002}], "materials": [{"material_id": 80200201, "material_count": 60}]}, {"id": 10002003, "star_level": 3, "name": "小紫", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 4, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 3070, "HPRecovery": 0, "Attack": 307, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 4, "MaxEnergy": 0, "EnergyRecovery": 15}, "effects": [{"effect_id": 5001}, {"effect_id": 5002}, {"effect_id": 5003}], "materials": [{"material_id": 80200201, "material_count": 100}]}, {"id": 10002004, "star_level": 4, "name": "小紫", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 4, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 3880, "HPRecovery": 0, "Attack": 388, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 5, "MaxEnergy": 0, "EnergyRecovery": 22}, "effects": [{"effect_id": 5001}, {"effect_id": 5002}, {"effect_id": 5003}, {"effect_id": 5004}], "materials": [{"material_id": 80200201, "material_count": 180}]}, {"id": 10002005, "star_level": 5, "name": "小紫", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 4, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 5030, "HPRecovery": 0, "Attack": 503, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 6, "MaxEnergy": 0, "EnergyRecovery": 27}, "effects": [{"effect_id": 5001}, {"effect_id": 5002}, {"effect_id": 5003}, {"effect_id": 5004}, {"effect_id": 5005}], "materials": [{"material_id": 80200201, "material_count": 300}]}, {"id": 10002006, "star_level": 6, "name": "小紫", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 4, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 6250, "HPRecovery": 0, "Attack": 625, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 7, "MaxEnergy": 0, "EnergyRecovery": 33}, "effects": [{"effect_id": 5001}, {"effect_id": 5002}, {"effect_id": 5003}, {"effect_id": 5004}, {"effect_id": 5005}, {"effect_id": 5006}], "materials": [{"material_id": 80200201, "material_count": 500}]}, {"id": 10002007, "star_level": 7, "name": "小紫", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 4, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 7810, "HPRecovery": 0, "Attack": 781, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 8, "MaxEnergy": 0, "EnergyRecovery": 40}, "effects": [{"effect_id": 5001}, {"effect_id": 5002}, {"effect_id": 5003}, {"effect_id": 5004}, {"effect_id": 5005}, {"effect_id": 5006}], "materials": [{"material_id": 80200201, "material_count": 800}]}, {"id": 10002008, "star_level": 8, "name": "小紫", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 4, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 9650, "HPRecovery": 0, "Attack": 965, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 9, "MaxEnergy": 0, "EnergyRecovery": 50}, "effects": [{"effect_id": 5001}, {"effect_id": 5002}, {"effect_id": 5003}, {"effect_id": 5004}, {"effect_id": 5005}, {"effect_id": 5006}], "materials": [{"material_id": 80200201, "material_count": 1200}]}, {"id": 10020101, "star_level": 1, "name": "小紫1", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能,活动解锁", "quality": 4, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 1810, "HPRecovery": 0, "Attack": 181, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 3, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200202, "material_count": 40}]}, {"id": 10002102, "star_level": 2, "name": "小紫1", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 4, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 2380, "HPRecovery": 0, "Attack": 238, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 3, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200202, "material_count": 60}]}, {"id": 10002103, "star_level": 3, "name": "小紫1", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 4, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 3070, "HPRecovery": 0, "Attack": 307, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 3, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200202, "material_count": 100}]}, {"id": 10002104, "star_level": 4, "name": "小紫1", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 4, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 3880, "HPRecovery": 0, "Attack": 388, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 3, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200202, "material_count": 180}]}, {"id": 10002105, "star_level": 5, "name": "小紫1", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 4, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 5030, "HPRecovery": 0, "Attack": 503, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 3, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200202, "material_count": 300}]}, {"id": 10002106, "star_level": 6, "name": "小紫1", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 4, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 6250, "HPRecovery": 0, "Attack": 625, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 3, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200202, "material_count": 500}]}, {"id": 10002107, "star_level": 7, "name": "小紫1", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 4, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 7810, "HPRecovery": 0, "Attack": 781, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 3, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200202, "material_count": 800}]}, {"id": 10002108, "star_level": 8, "name": "小紫1", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 4, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 9650, "HPRecovery": 0, "Attack": 965, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 3, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200202, "material_count": 1200}]}, {"id": 10020201, "star_level": 1, "name": "小紫2", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能,活动解锁", "quality": 4, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 1810, "HPRecovery": 0, "Attack": 181, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 3, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200203, "material_count": 40}]}, {"id": 10002202, "star_level": 2, "name": "小紫2", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 4, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 2380, "HPRecovery": 0, "Attack": 238, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 3, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200203, "material_count": 60}]}, {"id": 10002203, "star_level": 3, "name": "小紫2", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 4, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 3070, "HPRecovery": 0, "Attack": 307, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 3, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200203, "material_count": 100}]}, {"id": 10002204, "star_level": 4, "name": "小紫2", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 4, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 3880, "HPRecovery": 0, "Attack": 388, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 3, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200203, "material_count": 180}]}, {"id": 10002205, "star_level": 5, "name": "小紫2", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 4, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 5030, "HPRecovery": 0, "Attack": 503, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 3, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200203, "material_count": 300}]}, {"id": 10002206, "star_level": 6, "name": "小紫2", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 4, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 6250, "HPRecovery": 0, "Attack": 625, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 3, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200203, "material_count": 500}]}, {"id": 10002207, "star_level": 7, "name": "小紫2", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 4, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 7810, "HPRecovery": 0, "Attack": 781, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 3, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200203, "material_count": 800}]}, {"id": 10002208, "star_level": 8, "name": "小紫2", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 4, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 9650, "HPRecovery": 0, "Attack": 965, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 3, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200203, "material_count": 1200}]}, {"id": 10003001, "star_level": 1, "name": "小黄", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能,活动解锁", "quality": 5, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 1970, "HPRecovery": 0, "Attack": 197, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 2, "MaxEnergy": 0, "EnergyRecovery": 10}, "effects": [{"effect_id": 5001}], "materials": [{"material_id": 80200301, "material_count": 40}]}, {"id": 10003002, "star_level": 2, "name": "小黄", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 5, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 2940, "HPRecovery": 0, "Attack": 294, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 3, "MaxEnergy": 0, "EnergyRecovery": 12}, "effects": [{"effect_id": 5001}, {"effect_id": 5002}], "materials": [{"material_id": 80200301, "material_count": 60}]}, {"id": 10003003, "star_level": 3, "name": "小黄", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 5, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 3770, "HPRecovery": 0, "Attack": 377, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 4, "MaxEnergy": 0, "EnergyRecovery": 15}, "effects": [{"effect_id": 5001}, {"effect_id": 5002}, {"effect_id": 5003}], "materials": [{"material_id": 80200301, "material_count": 100}]}, {"id": 10003004, "star_level": 4, "name": "小黄", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 5, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 4900, "HPRecovery": 0, "Attack": 490, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 5, "MaxEnergy": 0, "EnergyRecovery": 22}, "effects": [{"effect_id": 5001}, {"effect_id": 5002}, {"effect_id": 5003}, {"effect_id": 5004}], "materials": [{"material_id": 80200301, "material_count": 180}]}, {"id": 10003005, "star_level": 5, "name": "小黄", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 5, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 6160, "HPRecovery": 0, "Attack": 616, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 6, "MaxEnergy": 0, "EnergyRecovery": 27}, "effects": [{"effect_id": 5001}, {"effect_id": 5002}, {"effect_id": 5003}, {"effect_id": 5004}, {"effect_id": 5005}], "materials": [{"material_id": 80200301, "material_count": 300}]}, {"id": 10003006, "star_level": 6, "name": "小黄", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 5, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 7700, "HPRecovery": 0, "Attack": 770, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 7, "MaxEnergy": 0, "EnergyRecovery": 33}, "effects": [{"effect_id": 5001}, {"effect_id": 5002}, {"effect_id": 5003}, {"effect_id": 5004}, {"effect_id": 5005}, {"effect_id": 5006}], "materials": [{"material_id": 80200301, "material_count": 500}]}, {"id": 10003007, "star_level": 7, "name": "小黄", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 5, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 8560, "HPRecovery": 0, "Attack": 856, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 8, "MaxEnergy": 0, "EnergyRecovery": 40}, "effects": [{"effect_id": 5001}, {"effect_id": 5002}, {"effect_id": 5003}, {"effect_id": 5004}, {"effect_id": 5005}, {"effect_id": 5006}], "materials": [{"material_id": 80200301, "material_count": 800}]}, {"id": 10003008, "star_level": 8, "name": "小黄", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 5, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 10620, "HPRecovery": 0, "Attack": 1062, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 9, "MaxEnergy": 0, "EnergyRecovery": 50}, "effects": [{"effect_id": 5001}, {"effect_id": 5002}, {"effect_id": 5003}, {"effect_id": 5004}, {"effect_id": 5005}, {"effect_id": 5006}], "materials": [{"material_id": 80200301, "material_count": 1200}]}, {"id": 10003101, "star_level": 1, "name": "小黄1", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能,活动解锁", "quality": 5, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 1970, "HPRecovery": 0, "Attack": 197, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 4, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200302, "material_count": 40}]}, {"id": 10003102, "star_level": 2, "name": "小黄1", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 5, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 2940, "HPRecovery": 0, "Attack": 294, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 4, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200302, "material_count": 60}]}, {"id": 10003103, "star_level": 3, "name": "小黄1", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 5, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 3770, "HPRecovery": 0, "Attack": 377, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 4, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200302, "material_count": 100}]}, {"id": 10003104, "star_level": 4, "name": "小黄1", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 5, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 4900, "HPRecovery": 0, "Attack": 490, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 4, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200302, "material_count": 180}]}, {"id": 10003105, "star_level": 5, "name": "小黄1", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 5, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 6160, "HPRecovery": 0, "Attack": 616, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 4, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200302, "material_count": 300}]}, {"id": 10003106, "star_level": 6, "name": "小黄1", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 5, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 7700, "HPRecovery": 0, "Attack": 770, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 4, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200302, "material_count": 500}]}, {"id": 10003107, "star_level": 7, "name": "小黄1", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 5, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 8560, "HPRecovery": 0, "Attack": 856, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 4, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200302, "material_count": 800}]}, {"id": 10003108, "star_level": 8, "name": "小黄1", "icon": "plane_icon_001", "portrait": "plane_portrait_001", "prefab": "10100001", "description": "敏捷的疾风战机，拥有出色的机动性能", "quality": 5, "collide_level": 2, "collide_damage": 100, "property": {"MaxHP": 10620, "HPRecovery": 0, "Attack": 1062, "Fortunate": 0, "Miss": 0, "BulletHurtResistance": 0, "CollisionHurtResistance": 0, "PickRadius": 0, "FinalScore": 0, "NuclearMax": 4, "MaxEnergy": 0, "EnergyRecovery": 0}, "effects": [], "materials": [{"material_id": 80200302, "material_count": 1200}]}]
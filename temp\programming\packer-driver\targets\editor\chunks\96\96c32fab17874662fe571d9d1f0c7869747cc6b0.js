System.register(["__unresolved_0", "cc", "cc/env", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, assetManager, CCBoolean, CCFloat, CCInteger, Component, Enum, instantiate, Prefab, Vec3, EDITOR, LayerEmittierStrategy, LayerEmittierType, LayerRandomRange, GameIns, logDebug, LevelNodeCheckOutScreen, EmittierElem, _dec, _dec2, _dec3, _class, _class2, _descriptor, _descriptor2, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _dec10, _dec11, _dec12, _dec13, _dec14, _dec15, _dec16, _dec17, _dec18, _dec19, _dec20, _dec21, _dec22, _dec23, _dec24, _class5, _class6, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _descriptor9, _descriptor10, _descriptor11, _descriptor12, _descriptor13, _descriptor14, _descriptor15, _descriptor16, _descriptor17, _descriptor18, _crd, ccclass, property, executeInEditMode, menu, EMIITER_POOL_NAME, LayerEmittierTypeZh, EmittierStatus, SerializableRandomRange, EmittierTerrain;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfLayerEmittierStrategy(extras) {
    _reporterNs.report("LayerEmittierStrategy", "../../leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLayerEmittierType(extras) {
    _reporterNs.report("LayerEmittierType", "../../leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLayerRandomRange(extras) {
    _reporterNs.report("LayerRandomRange", "../../leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogDebug(extras) {
    _reporterNs.report("logDebug", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelNodeCheckOutScreen(extras) {
    _reporterNs.report("LevelNodeCheckOutScreen", "../ui/map/LevelNodeCheckOutScreen", _context.meta, extras);
  }

  _export("EmittierElem", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      assetManager = _cc.assetManager;
      CCBoolean = _cc.CCBoolean;
      CCFloat = _cc.CCFloat;
      CCInteger = _cc.CCInteger;
      Component = _cc.Component;
      Enum = _cc.Enum;
      instantiate = _cc.instantiate;
      Prefab = _cc.Prefab;
      Vec3 = _cc.Vec3;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }, function (_unresolved_2) {
      LayerEmittierStrategy = _unresolved_2.LayerEmittierStrategy;
      LayerEmittierType = _unresolved_2.LayerEmittierType;
      LayerRandomRange = _unresolved_2.LayerRandomRange;
    }, function (_unresolved_3) {
      GameIns = _unresolved_3.GameIns;
    }, function (_unresolved_4) {
      logDebug = _unresolved_4.logDebug;
    }, function (_unresolved_5) {
      LevelNodeCheckOutScreen = _unresolved_5.LevelNodeCheckOutScreen;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "b1dcb5RE6ZN+7KA2ed/t53E", "EmittierTerrain", undefined);

      __checkObsolete__(['_decorator', 'assetManager', 'Camera', 'CCBoolean', 'CCFloat', 'CCInteger', 'Component', 'director', 'Enum', 'instantiate', 'Prefab', 'UITransform', 'Vec3', 'view']);

      ({
        ccclass,
        property,
        executeInEditMode,
        menu
      } = _decorator);
      EMIITER_POOL_NAME = "emittier_pool";

      LayerEmittierTypeZh = function (LayerEmittierTypeZh) {
        LayerEmittierTypeZh[LayerEmittierTypeZh["\u65E0\u9650"] = (_crd && LayerEmittierType === void 0 ? (_reportPossibleCrUseOfLayerEmittierType({
          error: Error()
        }), LayerEmittierType) : LayerEmittierType).Infinite] = "\u65E0\u9650";
        LayerEmittierTypeZh[LayerEmittierTypeZh["\u6301\u7EED\u65F6\u95F4"] = (_crd && LayerEmittierType === void 0 ? (_reportPossibleCrUseOfLayerEmittierType({
          error: Error()
        }), LayerEmittierType) : LayerEmittierType).Duration] = "\u6301\u7EED\u65F6\u95F4";
        LayerEmittierTypeZh[LayerEmittierTypeZh["\u53D1\u5C04\u6B21\u6570"] = (_crd && LayerEmittierType === void 0 ? (_reportPossibleCrUseOfLayerEmittierType({
          error: Error()
        }), LayerEmittierType) : LayerEmittierType).Count] = "\u53D1\u5C04\u6B21\u6570";
        LayerEmittierTypeZh[LayerEmittierTypeZh["\u76D1\u542C\u4E8B\u4EF6"] = (_crd && LayerEmittierType === void 0 ? (_reportPossibleCrUseOfLayerEmittierType({
          error: Error()
        }), LayerEmittierType) : LayerEmittierType).Event] = "\u76D1\u542C\u4E8B\u4EF6";
        return LayerEmittierTypeZh;
      }(LayerEmittierTypeZh || {});

      _export("EmittierStatus", EmittierStatus = /*#__PURE__*/function (EmittierStatus) {
        EmittierStatus[EmittierStatus["inactive"] = 0] = "inactive";
        EmittierStatus[EmittierStatus["active"] = 1] = "active";
        EmittierStatus[EmittierStatus["pause"] = 2] = "pause";
        EmittierStatus[EmittierStatus["end"] = 3] = "end";
        return EmittierStatus;
      }({}));

      _export("SerializableRandomRange", SerializableRandomRange = (_dec = ccclass('SerializableRandomRange'), _dec2 = property({
        type: CCFloat,
        displayName: "最小值"
      }), _dec3 = property({
        type: CCFloat,
        displayName: "最大值"
      }), _dec(_class = (_class2 = class SerializableRandomRange {
        constructor() {
          _initializerDefineProperty(this, "min", _descriptor, this);

          _initializerDefineProperty(this, "max", _descriptor2, this);
        }

        // 提供转换方法
        toLayerRandomRange() {
          return new (_crd && LayerRandomRange === void 0 ? (_reportPossibleCrUseOfLayerRandomRange({
            error: Error()
          }), LayerRandomRange) : LayerRandomRange)(this.min, this.max);
        }

        fromLayerRandomRange(range) {
          this.min = range.min;
          this.max = range.max;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "min", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "max", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      })), _class2)) || _class));

      _export("EmittierElem", EmittierElem = class EmittierElem extends Component {
        constructor(...args) {
          super(...args);
          this._velocity = new Vec3(0, 0, 0);
        }

        /**
         * 初始化元素运动参数
         * @param speed 运动速度
         * @param angle 运动角度（度）
         */
        init(speed, angle) {
          // 将角度转换为弧度
          const rad = angle * Math.PI / 180; // 计算速度分量

          this._velocity.x = Math.cos(rad) * speed;
          this._velocity.y = Math.sin(rad) * speed;
        }

        tick(dt) {
          // 更新位置
          const pos = this.node.position.clone();
          pos.add(this._velocity.clone().multiplyScalar(dt));
          this.node.setPosition(pos);
        }

      });

      _export("EmittierTerrain", EmittierTerrain = (_dec4 = ccclass('EmittierTerrain'), _dec5 = executeInEditMode(), _dec6 = menu('地形系统/地形发射器'), _dec7 = property({
        visible: false
      }), _dec8 = property({
        type: CCBoolean,
        displayName: "是否跟随层级移动"
      }), _dec9 = property({
        type: Prefab,
        displayName: "发射器"
      }), _dec10 = property({
        type: Enum(_crd && LayerEmittierStrategy === void 0 ? (_reportPossibleCrUseOfLayerEmittierStrategy({
          error: Error()
        }), LayerEmittierStrategy) : LayerEmittierStrategy),
        displayName: "发射策略"
      }), _dec11 = property({
        type: [Prefab],
        displayName: "发射元素组"
      }), _dec12 = property({
        type: Enum(LayerEmittierTypeZh),
        displayName: "发射器类型"
      }), _dec13 = property({
        visible: false
      }), _dec14 = property({
        type: CCFloat,
        displayName: "效果值",
        tooltip: "值对应类型:Infinite为无限,Duration为持续时间(ms),Count为发射次数,Event为监听事件"
      }), _dec15 = property({
        type: SerializableRandomRange,
        displayName: "效果值校正"
      }), _dec16 = property({
        type: CCInteger,
        displayName: "初始延迟(ms)",
        min: 0
      }), _dec17 = property({
        type: SerializableRandomRange,
        displayName: "延迟校正(ms)"
      }), _dec18 = property({
        type: CCInteger,
        displayName: "发射间隔(ms)"
      }), _dec19 = property({
        type: SerializableRandomRange,
        displayName: "间隔校正(ms)"
      }), _dec20 = property({
        type: CCInteger,
        displayName: "发射角度(0-360)",
        min: 0,
        max: 360
      }), _dec21 = property({
        type: SerializableRandomRange,
        displayName: "角度校正(0-360)"
      }), _dec22 = property({
        type: CCFloat,
        displayName: "发射速度"
      }), _dec23 = property({
        type: SerializableRandomRange,
        displayName: "速度校正"
      }), _dec24 = property({
        type: SerializableRandomRange,
        displayName: "X偏移范围"
      }), _dec4(_class5 = _dec5(_class5 = _dec6(_class5 = (_class6 = class EmittierTerrain extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "_bfollow", _descriptor3, this);

          _initializerDefineProperty(this, "emittier", _descriptor4, this);

          _initializerDefineProperty(this, "strategy", _descriptor5, this);

          _initializerDefineProperty(this, "emittierElements", _descriptor6, this);

          _initializerDefineProperty(this, "type", _descriptor7, this);

          _initializerDefineProperty(this, "value", _descriptor8, this);

          // 根据type决定用途，Infinite为无限，Duration为持续时间，Count为发射次数，Event为监听事件
          _initializerDefineProperty(this, "valueModify", _descriptor9, this);

          _initializerDefineProperty(this, "initDelay", _descriptor10, this);

          _initializerDefineProperty(this, "delayModify", _descriptor11, this);

          _initializerDefineProperty(this, "interval", _descriptor12, this);

          _initializerDefineProperty(this, "intervalModify", _descriptor13, this);

          _initializerDefineProperty(this, "angle", _descriptor14, this);

          _initializerDefineProperty(this, "angleModify", _descriptor15, this);

          _initializerDefineProperty(this, "speed", _descriptor16, this);

          _initializerDefineProperty(this, "speedModify", _descriptor17, this);

          _initializerDefineProperty(this, "offSetX", _descriptor18, this);

          this._status = EmittierStatus.inactive;
          this._activeElements = [];
          this._curDelay = 0;
          // 当前延迟
          this._curValue = 0;
          // 当前效果值
          this._curEmiIndex = 0;
          // 当前发射器索引
          this._deltaTime = 0;
          // 发射器运行总时间
          this._lastEmitTime = 0;
          // 上次发射时间
          this._emitCount = 0;
          // 已发射元素计数
          this._nextInterval = 0;
          // 下一次发射的间隔时间
          this._initialDelayPassed = false;
          // 初始延迟是否已过
          this._poolName = '';
        }

        set bfollow(value) {
          this._bfollow = value;
        }

        get bfollow() {
          return this._bfollow;
        }

        get typeZh() {
          return this.type;
        }

        set typeZh(value) {
          this.type = value;
        }

        get status() {
          return this._status;
        }

        get follow() {
          return this._bfollow;
        }

        onLoad() {
          this._resetData();

          if (EDITOR) {
            this.node.removeAllChildren();
            assetManager.loadAny({
              uuid: this.emittier.uuid
            }, (err, prefab) => {
              if (err) {
                return;
              } else {
                const emitterNode = instantiate(prefab);
                this.node.addChild(emitterNode);
              }
            });
          }
        }

        init(poolName) {
          this._poolName = poolName;
          this._status = EmittierStatus.inactive;

          this._resetData();
        }

        startEmittier() {
          this._status = EmittierStatus.active;

          if (EDITOR) {
            this._curDelay = this.initDelay + this.delayModify.min + Math.random() * (this.delayModify.max - this.delayModify.min);
            this._curValue = this.value + Math.random() * (this.valueModify.max - this.valueModify.min);
          } else {
            this._curDelay = this.initDelay + this.delayModify.min + (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.random() * (this.delayModify.max - this.delayModify.min);
            this._curValue = this.value + this.valueModify.min + (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.random() * (this.valueModify.max - this.valueModify.min);
          }
        }

        tick(dt) {
          if (this._status !== EmittierStatus.active) return;
          const dtMs = dt * 1000; // 运行发射器

          this._updateEmitter(dtMs);

          this._updateActiveElements(dt);
        }
        /**
         * 更新发射器自身状态
         * @param dt 增量时间（豪秒）
         */


        _updateEmitter(dt) {
          this._deltaTime += dt;

          if (!this._initialDelayPassed) {
            const delaySeconds = this._curDelay;

            if (this._deltaTime >= delaySeconds) {
              this._initialDelayPassed = true;
              this._deltaTime = 0; // 重置时间，开始发射循环
            }

            return;
          } // 运行发射器


          this._runEmittier();
        }
        /**
         * 更新所有已发射的元素
         * @param dt 增量时间（秒）
         */


        _updateActiveElements(dt) {
          // 遍历所有元素并更新
          for (let i = this._activeElements.length - 1; i >= 0; i--) {
            const elem = this._activeElements[i]; // 检查元素是否已被销毁

            if (!elem.isValid) {
              // 从数组中移除已销毁的元素
              this._activeElements.splice(i, 1);

              continue;
            } // 更新元素状态


            elem.tick(dt);
          }
        }

        _runEmittier() {
          if (!this.emittier || this.emittierElements.length === 0) return;
          const currentTime = this._deltaTime; // 检查是否达到发射间隔

          if (currentTime - this._lastEmitTime < this._nextInterval) return; // 随机选择要发射的元素

          if (this.strategy === (_crd && LayerEmittierStrategy === void 0 ? (_reportPossibleCrUseOfLayerEmittierStrategy({
            error: Error()
          }), LayerEmittierStrategy) : LayerEmittierStrategy).Random) {
            if (EDITOR) {
              this._curEmiIndex = Math.floor(Math.random() * this.emittierElements.length);
            } else {
              this._curEmiIndex = Math.floor((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.random() * this.emittierElements.length);
            }
          } else if (this.strategy === (_crd && LayerEmittierStrategy === void 0 ? (_reportPossibleCrUseOfLayerEmittierStrategy({
            error: Error()
          }), LayerEmittierStrategy) : LayerEmittierStrategy).Sequence) {
            this._curEmiIndex = this._emitCount % this.emittierElements.length;
          }

          const elemPrefab = this.emittierElements[this._curEmiIndex];
          let elemNode = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameMapManager.mapObjectPoolManager.get(EMIITER_POOL_NAME, elemPrefab.name);

          if (elemNode) {
            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)('EmittierTerrain', ` 从对象池获取发射体 ${elemPrefab.name}`);
          } else {
            elemNode = instantiate(elemPrefab);
            const cheekOut = elemNode.addComponent(_crd && LevelNodeCheckOutScreen === void 0 ? (_reportPossibleCrUseOfLevelNodeCheckOutScreen({
              error: Error()
            }), LevelNodeCheckOutScreen) : LevelNodeCheckOutScreen);
            cheekOut.init(EMIITER_POOL_NAME);
          } // 设置初始位置


          let offsetX = 0;

          if (EDITOR) {
            offsetX = this.offSetX.min + Math.random() * (this.offSetX.max - this.offSetX.min);
            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)('EmittierTerrain', ` 随机地形 x坐标偏移：${offsetX}`);
          } else {
            offsetX = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.random() * (this.offSetX.max - this.offSetX.min);
          }

          elemNode.setPosition(this.node.position.x + offsetX, 0, 0); // 设置初始角度

          let angle = 0;

          if (EDITOR) {
            angle = this.angle + this.angleModify.min + Math.random() * (this.angleModify.max - this.angleModify.min);
          } else {
            angle = this.angle + this.angleModify.min + (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.random() * (this.angleModify.max - this.angleModify.min);
          } // 设置初始速度


          let speed = 0;

          if (EDITOR) {
            speed = this.speed + this.speedModify.min + Math.random() * (this.speedModify.max - this.speedModify.min);
            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)('EmittierTerrain', ` 随机地形 发射速度：${speed} `);
          } else {
            speed = this.speed + this.speedModify.min + (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.random() * (this.speedModify.max - this.speedModify.min);
          } // 获取或添加EmittierElem组件


          let elemComponent = elemNode.getComponent(EmittierElem);

          if (!elemComponent) {
            elemComponent = elemNode.addComponent(EmittierElem);
          } // 初始化元素运动


          elemComponent.init(speed, angle); // 添加到场景

          this.node.addChild(elemNode); // 添加到活动元素列表

          this._activeElements.push(elemComponent); // 更新发射计数和时间


          this._emitCount++;
          this._lastEmitTime = currentTime; // 计算下一次发射的间隔时间

          if (EDITOR) {
            this._nextInterval = this.interval + this.intervalModify.min + Math.random() * (this.intervalModify.max - this.intervalModify.min);
            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)('EmittierTerrain', ` 随机地形 下次发射间隔：${this._nextInterval}`);
          } else {
            this._nextInterval = this.interval + this.intervalModify.min + (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.random() * (this.intervalModify.max - this.intervalModify.min);
          } // 检查发射器是否结束


          this._checkEmittierEnd();
        }

        _checkEmittierEnd() {
          if (!this.emittier) return;

          switch (this.type) {
            case (_crd && LayerEmittierType === void 0 ? (_reportPossibleCrUseOfLayerEmittierType({
              error: Error()
            }), LayerEmittierType) : LayerEmittierType).Duration:
              // 持续时间结束
              if (this._deltaTime >= this._curValue) {
                this._status = EmittierStatus.end;
              }

              break;

            case (_crd && LayerEmittierType === void 0 ? (_reportPossibleCrUseOfLayerEmittierType({
              error: Error()
            }), LayerEmittierType) : LayerEmittierType).Count:
              // 发射次数达到
              if (this._emitCount >= this._curValue) {
                this._status = EmittierStatus.end;
              }

              break;

            case (_crd && LayerEmittierType === void 0 ? (_reportPossibleCrUseOfLayerEmittierType({
              error: Error()
            }), LayerEmittierType) : LayerEmittierType).Event:
              // 事件触发结束（需要外部实现）
              // 这里可以添加事件监听逻辑
              break;
            // 无限类型不需要结束检查
          }

          if (this._status === EmittierStatus.end) {
            this._destroyEmitter();
          }
        }

        _destroyEmitter() {
          this._destroyAllElements();

          if (!EDITOR) {
            this.node.destroy();
          } else {
            // 在编辑器中，我们只重置状态，不实际销毁节点
            this._resetData();

            this._recycleNode();
          }
        }

        _resetData() {
          this._status = EmittierStatus.inactive;
          this._emitCount = 0;
          this._initialDelayPassed = false;
          this._activeElements = [];
          this._deltaTime = 0;
          this._lastEmitTime = 0;
          this._nextInterval = 0;
          this._curValue = 0;
          this._curDelay = 0;
          this._curEmiIndex = 0;
        }

        play(bPlay) {
          if (EDITOR) {
            if (bPlay) {
              this._status = EmittierStatus.active;
              this.startEmittier();
              (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                error: Error()
              }), logDebug) : logDebug)('EmittierTerrain', "play emittier");
            } else {
              this._status = EmittierStatus.inactive;

              this._destroyEmitter();
            }
          }
        }
        /**
         * 销毁所有已发射的元素
         */


        _destroyAllElements() {
          // 销毁所有元素节点
          for (const elem of this._activeElements) {
            if (elem.isValid) {
              elem.node.destroy();
            }
          } // 清空元素列表


          this._activeElements = [];
        }

        onDestroy() {
          this.node.removeAllChildren();
        }

        _recycleNode() {
          if (this._poolName) {
            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)('EmittierTerrain', ` 节点${this.node.name}已回收到对象池${this._poolName}`);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gameMapManager.mapObjectPoolManager.put(this._poolName, this.node);
          } else {
            // 没有指定对象池时直接销毁
            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)('EmittierTerrain', `节点${this.node.name}已销毁`);
            this.node.destroy();
          }
        }

      }, (_descriptor3 = _applyDecoratedDescriptor(_class6.prototype, "_bfollow", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return false;
        }
      }), _applyDecoratedDescriptor(_class6.prototype, "bfollow", [_dec8], Object.getOwnPropertyDescriptor(_class6.prototype, "bfollow"), _class6.prototype), _descriptor4 = _applyDecoratedDescriptor(_class6.prototype, "emittier", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class6.prototype, "strategy", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return (_crd && LayerEmittierStrategy === void 0 ? (_reportPossibleCrUseOfLayerEmittierStrategy({
            error: Error()
          }), LayerEmittierStrategy) : LayerEmittierStrategy).Random;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class6.prototype, "emittierElements", [_dec11], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      }), _applyDecoratedDescriptor(_class6.prototype, "typeZh", [_dec12], Object.getOwnPropertyDescriptor(_class6.prototype, "typeZh"), _class6.prototype), _descriptor7 = _applyDecoratedDescriptor(_class6.prototype, "type", [_dec13], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return (_crd && LayerEmittierType === void 0 ? (_reportPossibleCrUseOfLayerEmittierType({
            error: Error()
          }), LayerEmittierType) : LayerEmittierType).Infinite;
        }
      }), _descriptor8 = _applyDecoratedDescriptor(_class6.prototype, "value", [_dec14], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor9 = _applyDecoratedDescriptor(_class6.prototype, "valueModify", [_dec15], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return new SerializableRandomRange();
        }
      }), _descriptor10 = _applyDecoratedDescriptor(_class6.prototype, "initDelay", [_dec16], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor11 = _applyDecoratedDescriptor(_class6.prototype, "delayModify", [_dec17], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return new SerializableRandomRange();
        }
      }), _descriptor12 = _applyDecoratedDescriptor(_class6.prototype, "interval", [_dec18], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor13 = _applyDecoratedDescriptor(_class6.prototype, "intervalModify", [_dec19], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return new SerializableRandomRange();
        }
      }), _descriptor14 = _applyDecoratedDescriptor(_class6.prototype, "angle", [_dec20], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor15 = _applyDecoratedDescriptor(_class6.prototype, "angleModify", [_dec21], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return new SerializableRandomRange();
        }
      }), _descriptor16 = _applyDecoratedDescriptor(_class6.prototype, "speed", [_dec22], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor17 = _applyDecoratedDescriptor(_class6.prototype, "speedModify", [_dec23], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return new SerializableRandomRange();
        }
      }), _descriptor18 = _applyDecoratedDescriptor(_class6.prototype, "offSetX", [_dec24], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return new SerializableRandomRange();
        }
      })), _class6)) || _class5) || _class5) || _class5));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=96c32fab17874662fe571d9d1f0c7869747cc6b0.js.map
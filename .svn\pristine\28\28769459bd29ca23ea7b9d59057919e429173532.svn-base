
import { Prefab, instantiate } from "cc";
import { MyApp } from 'db://assets/bundles/common/script/app/MyApp';
import { MainPlaneData } from "db://assets/bundles/common/script/data/plane/MainPlaneData";
import GameResourceList from "../const/GameResourceList";
import { GameIns } from "../GameIns";
import BattleLayer from "../ui/layer/BattleLayer";
import { type MainPlane } from "../ui/plane/mainPlane/MainPlane";

export class MainPlaneManager {

    _planeData: MainPlaneData | null = null;//飞机数据
    mainPlane: MainPlane | null = null;//飞机战斗UI

    hurtTotal: number = 0;//造成的总伤害
    reviveCount: number = 0;//已复活次数
    maxReviveCount: number = 0;//可复活次数

    curExp: number = 0;
    curMaxExp: number = 0;
    curLv: number = 1;

    setPlaneData(planeData: MainPlaneData) {
        this._planeData = planeData;
    }

    async preload() {
        this.reset();
        GameIns.battleManager.addLoadCount(1);
        await this.createMainPlane();
        GameIns.battleManager.checkLoadFinish();
    }

    reset() {
        this.hurtTotal = 0;
        this.subReset();
    }

    subReset() {
        this.reviveCount = 0;
        this.maxReviveCount = 1; // 默认可复活1次
        if (this.mainPlane) {
            this.mainPlane.resetPlane();
            this.mainPlane = null
        }
    }

    /**
     * 创建主飞机
     * @param isTrans 是否为特殊状态
     * @returns 主飞机对象
     */
    async createMainPlane(): Promise<MainPlane | null> {
        if (this.mainPlane) {
            this.mainPlane.resetPlane();
            return this.mainPlane;
        }

        const prefab = await MyApp.resMgr.loadAsync(GameResourceList.MainPlane, Prefab);
        let planeNode = instantiate(prefab);
        this.mainPlane = planeNode.getComponent("MainPlane") as MainPlane
        BattleLayer.instance?.addMainPlane();
        await this.mainPlane?.initPlane(this._planeData!);
        return this.mainPlane;
    }

    mainReset() {
        if (this.mainPlane) {
            this.mainPlane.node.destroy();
            this.mainPlane = null;
        }
    }

    checkCanRevive(): boolean {
        return this.reviveCount < this.maxReviveCount;
    }

    revive() {
        GameIns.mainPlaneManager.reviveCount += 1; // 增加复活次数
        this.mainPlane?.revive();
    }

    updateGameLogic(dt: number): void {
        this.mainPlane?.updateGameLogic(dt);
    }
}

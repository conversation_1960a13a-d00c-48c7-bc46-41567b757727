import { _decorator, Enum } from "cc";
const { ccclass, property } = _decorator;

import { IEventConditionData, EventConditionBase, Comparer, eCompareOp, eConditionOp } from "db://assets/bundles/common/script/game/eventgroup/IEventCondition";
import { ExpressionValue } from "db://assets/bundles/common/script/game/eventgroup/ExpressionValue";

export enum eEnemyCondition {
    ElapsedTime,
    Pos_X,
    Pos_Y,
    CurHP,
    CurHPPercent,
    Speed,
    SpeedAngle,
    Acceleration,
    AccelerationAngle,
    DistanceToPlayer,
    AngleToPlayer,

    Player_Pos_X,
    Player_Pos_Y,
    Player_CurHPPercent,
    Level_ElapsedTime,
    Level_InfLevel,                 // 无尽模式当前关卡等级
    Level_ChallengeLevelSection,    // 闯关模式当前关卡章节

    // 判断当前状态
    SelectLevel,
    ImmuneBulletDamage,
    ImmuneCollideDamage,
    IgnoreBullet,
    IgnoreCollide,
    ImmuneNuke,
    ImmuneActiveSkill,
    Invincible,
}

export enum eEnemyConditionCn {
    单位持续时间 = eEnemyCondition.ElapsedTime,
    单位坐标X = eEnemyCondition.Pos_X,
    单位坐标Y = eEnemyCondition.Pos_Y,
    单位生命值 = eEnemyCondition.CurHP,
    单位生命百分比 = eEnemyCondition.CurHPPercent,
    单位速度 = eEnemyCondition.Speed,
    单位速度角 = eEnemyCondition.SpeedAngle,
    单位加速度 = eEnemyCondition.Acceleration,
    单位加速度角 = eEnemyCondition.AccelerationAngle,
    单位与玩家的距离 = eEnemyCondition.DistanceToPlayer,
    单位与玩家的角度 = eEnemyCondition.AngleToPlayer,

    玩家坐标X = eEnemyCondition.Player_Pos_X,
    玩家坐标Y = eEnemyCondition.Player_Pos_Y,
    玩家生命百分比 = eEnemyCondition.Player_CurHPPercent,

    关卡已持续时间 = eEnemyCondition.Level_ElapsedTime,
    无尽模式当前等级 = eEnemyCondition.Level_InfLevel,
    闯关模式当前章节 = eEnemyCondition.Level_ChallengeLevelSection,

    选中级别 = eEnemyCondition.SelectLevel,
    是否免疫子弹伤害 = eEnemyCondition.ImmuneBulletDamage,
    是否免疫撞击伤害 = eEnemyCondition.ImmuneCollideDamage,
    是否无视子弹 = eEnemyCondition.IgnoreBullet,
    是否无视撞击 = eEnemyCondition.IgnoreCollide,  
    是否免疫核弹 = eEnemyCondition.ImmuneNuke,
    是否免疫主动技能 = eEnemyCondition.ImmuneActiveSkill,
    是否无敌 = eEnemyCondition.Invincible,
}

@ccclass('EnemyConditionData')
export class EnemyConditionData implements IEventConditionData {
    @property({ type: Enum(eConditionOp), displayName: '条件关系' })
    public op: eConditionOp = eConditionOp.And;

    @property({visible:false})
    public type: eEnemyCondition = eEnemyCondition.ElapsedTime;
    @property({ type: Enum(eEnemyConditionCn), displayName: '条件类型' })
    public get typeCn(): eEnemyConditionCn { return this.type  as unknown as eEnemyConditionCn; }
    public set typeCn(value: eEnemyConditionCn) { this.type = value  as unknown as eEnemyCondition; }

    @property({ type: Enum(eCompareOp), displayName: '比较方式' })
    public compareOp: eCompareOp = eCompareOp.Equal;

    // 条件值: 例如持续时间、距离
    @property({visible:false})
    public targetValue : ExpressionValue = new ExpressionValue('0');
    @property({displayName: '目标值'})
    public get targetValueStr(): string { return this.targetValue.raw; }
    public set targetValueStr(value: string) { this.targetValue.raw = value; }
}

[{"__type__": "cc.Prefab", "_name": "RandomElement01", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "RandomElement01", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 5}, {"__id__": 8}], "_active": true, "_components": [{"__id__": 11}, {"__id__": 13}], "_prefab": {"__id__": 18}, "_lpos": {"__type__": "cc.Vec3", "x": 21.722, "y": 2.935, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 3}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 2}, "asset": {"__uuid__": "6d35985a-2571-4129-a1e6-bd3334135fa1", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 4}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "7eJSdB1elJZZLQ1qpnTAGp", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 6}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 5}, "asset": {"__uuid__": "e549918d-a699-468c-9d07-2b1c9dbc1a7a", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 7}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "17En5WaQlKEa6IUN2zbSSX", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 9}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 8}, "asset": {"__uuid__": "475c7890-0252-4203-8578-e5928cc7c2e8", "__expectedType__": "cc.Prefab"}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "instance": {"__id__": 10}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "6eH9NfUnJGDqtdKEDkJEHo", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [], "removedComponents": []}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 12}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b5/VPJtoNB8pcW4CUnwz2m"}, {"__type__": "c06femD0WhPk63lQ/D1YJtE", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 14}, "terrain": [{"__id__": 15}, {"__id__": 16}, {"__id__": 17}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e6UGaBglVBDLINB4zk372m"}, {"__type__": "TerrainElem", "weight": 0, "elem": null, "offSet": {"__type__": "cc.Vec2", "x": 0, "y": 0}}, {"__type__": "TerrainElem", "weight": 0, "elem": {"__uuid__": "e549918d-a699-468c-9d07-2b1c9dbc1a7a", "__expectedType__": "cc.Prefab"}, "offSet": {"__type__": "cc.Vec2", "x": 0, "y": 0}}, {"__type__": "TerrainElem", "weight": 0, "elem": {"__uuid__": "475c7890-0252-4203-8578-e5928cc7c2e8", "__expectedType__": "cc.Prefab"}, "offSet": {"__type__": "cc.Vec2", "x": 0, "y": 0}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e6gsbpksNFNKSa6hJ0q0NX", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 8}, {"__id__": 5}, {"__id__": 2}]}]
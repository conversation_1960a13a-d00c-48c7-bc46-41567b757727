{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/game/GamePauseUI.ts"], "names": ["_decorator", "Node", "Sprite", "SpriteFrame", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "GameIns", "MyApp", "BundleName", "ccclass", "property", "GamePauseUI", "_countdown", "_countdownInterval", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getUIOption", "isClickBgCloseUI", "getBundleName", "GameFight", "onLoad", "closeUI", "onShow", "NodePauseUI", "active", "NodeTimeUI", "refreshPauseUI", "onHide", "onClose", "stopCountdown", "rogueManager", "recycleRogueItems", "contentRogue", "onDestroy", "data", "getSeLectedHistory", "for<PERSON>ach", "value", "key", "config", "lubanTables", "TbResWordGroup", "get", "setRogueItem", "wordId", "onBtnResumeClicked", "startCountdown", "onBtnExitClicked", "battleManager", "quitBattle", "updateCountdownLabel", "setInterval", "onCountdownFinished", "clearInterval", "spTime", "spriteFrame", "downFrames", "gameStateManager", "gameResume"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAmBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAqBC,MAAAA,W,OAAAA,W;;AAE9CC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;6BAGjBY,W,WADZF,OAAO,CAAC,aAAD,C,UAGHC,QAAQ,CAAC,CAACR,WAAD,CAAD,C,UAGRQ,QAAQ,CAACV,IAAD,C,UAERU,QAAQ,CAACV,IAAD,C,UAERU,QAAQ,CAACT,MAAD,C,UAGRS,QAAQ,CAACV,IAAD,C,2BAbb,MACaW,WADb;AAAA;AAAA,4BACwC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAe5BC,UAf4B,GAeP,CAfO;AAeJ;AAfI,eAgB5BC,kBAhB4B,GAgBgC,IAhBhC;AAAA;;AAgBsC;AAEtD,eAANC,MAAM,GAAW;AAAE,iBAAO,oBAAP;AAA8B;;AACzC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAwB;;AACnC,eAAXC,WAAW,GAAU;AAAE,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AAAoC;;AAC9C,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,SAAlB;AAA6B;;AAE3DC,QAAAA,MAAM,GAAS,CAExB;;AACY,cAAPC,OAAO,GAAG;AACZ;AAAA;AAAA,8BAAMA,OAAN,CAAcX,WAAd;AACH;;AACW,cAANY,MAAM,GAAkB;AAC1B,eAAKC,WAAL,CAAkBC,MAAlB,GAA2B,IAA3B;AACA,eAAKC,UAAL,CAAiBD,MAAjB,GAA0B,KAA1B;AACA,eAAKE,cAAL;AACH;;AACW,cAANC,MAAM,GAAkB,CAC7B;;AACY,cAAPC,OAAO,GAAkB;AAC3B,eAAKC,aAAL,GAD2B,CACL;;AACtB;AAAA;AAAA,kCAAQC,YAAR,CAAqBC,iBAArB,CAAuC,KAAKC,YAA5C;AACH;;AACSC,QAAAA,SAAS,GAAS,CAC3B;;AAEDP,QAAAA,cAAc,GAAE;AACZ,cAAIQ,IAAI,GAAG;AAAA;AAAA,kCAAQJ,YAAR,CAAqBK,kBAArB,EAAX;AACA;AAAA;AAAA,kCAAQL,YAAR,CAAqBC,iBAArB,CAAuC,KAAKC,YAA5C;AACAE,UAAAA,IAAI,CAACE,OAAL,CAAa,CAACC,KAAD,EAAOC,GAAP,KAAa;AACtB,gBAAIC,MAAM,GAAG;AAAA;AAAA,gCAAMC,WAAN,CAAkBC,cAAlB,CAAiCC,GAAjC,CAAqCJ,GAArC,CAAb;AACA;AAAA;AAAA,oCAAQR,YAAR,CAAqBa,YAArB,CAAkC,KAAKX,YAAvC,EAAqDO,MAAM,CAAEK,MAA7D,EAAoEP,KAApE;AACH,WAHD;AAIH;;AAEDQ,QAAAA,kBAAkB,GAAG;AACjB,eAAKtB,WAAL,CAAkBC,MAAlB,GAA2B,KAA3B;AACA,eAAKC,UAAL,CAAiBD,MAAjB,GAA0B,IAA1B;AACA,eAAKsB,cAAL;AACH;;AAEDC,QAAAA,gBAAgB,GAAG;AACf,eAAK1B,OAAL;AACA;AAAA;AAAA,kCAAQ2B,aAAR,CAAsBC,UAAtB;AACH,SA7DmC,CA+DpC;;;AACQH,QAAAA,cAAc,GAAS;AAC3B,eAAKnC,UAAL,GAAkB,CAAlB,CAD2B,CACN;;AACrB,eAAKuC,oBAAL,GAF2B,CAEE;;AAE7B,eAAKtC,kBAAL,GAA0BuC,WAAW,CAAC,MAAM;AACxC,iBAAKxC,UAAL;AACA,iBAAKuC,oBAAL;;AAEA,gBAAI,KAAKvC,UAAL,IAAmB,CAAvB,EAA0B;AACtB,mBAAKkB,aAAL;AACA,mBAAKuB,mBAAL;AACH;AACJ,WARoC,EAQlC,IARkC,CAArC,CAJ2B,CAYjB;AACb,SA7EmC,CA+EpC;;;AACQvB,QAAAA,aAAa,GAAS;AAC1B,cAAI,KAAKjB,kBAAL,KAA4B,IAAhC,EAAsC;AAClCyC,YAAAA,aAAa,CAAC,KAAKzC,kBAAN,CAAb;AACA,iBAAKA,kBAAL,GAA0B,IAA1B;AACH;AACJ,SArFmC,CAuFpC;;;AACQsC,QAAAA,oBAAoB,GAAS;AACjC,cAAI,KAAKI,MAAT,EAAiB;AACb,iBAAKA,MAAL,CAAYC,WAAZ,GAA0B,KAAKC,UAAL,CAAgB,KAAK7C,UAArB,CAA1B;AACH;AACJ,SA5FmC,CA8FpC;;;AACQyC,QAAAA,mBAAmB,GAAS;AAChC;AAAA;AAAA,kCAAQK,gBAAR,CAAyBC,UAAzB;AACA,eAAKrC,OAAL;AACH;;AAlGmC,O;;;;;iBAGT,E;;;;;;;iBAGA,I;;;;;;;iBAED,I;;;;;;;iBAEF,I;;;;;;;iBAGI,I", "sourcesContent": ["import { _decorator, Label, Node, Sprite, Sprite<PERSON><PERSON><PERSON>, Sprite<PERSON>rame } from 'cc';\r\n\r\nimport { BaseUI, UILayer, UIMgr, UIOpt } from 'db://assets/scripts/core/base/UIMgr';\r\nimport { GameIns } from '../../../game/GameIns';\r\nimport { MyApp } from '../../../app/MyApp';\r\nimport { BundleName } from '../../../const/BundleConst';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('GamePauseUI')\r\nexport class GamePauseUI extends BaseUI {\r\n\r\n    @property([SpriteFrame])\r\n    downFrames:SpriteFrame[] = [];\r\n\r\n    @property(Node)\r\n    NodePauseUI: Node | null = null;\r\n    @property(Node)\r\n    NodeTimeUI: Node | null = null;\r\n    @property(Sprite)\r\n    spTime: Sprite | null = null;\r\n\r\n    @property(Node)\r\n    contentRogue: Node | null = null;\r\n\r\n    private _countdown: number = 7; // 倒计时初始值\r\n    private _countdownInterval: ReturnType<typeof setInterval> | null = null; // 用于存储计时器 ID\r\n\r\n    public static getUrl(): string { return \"prefab/GamePauseUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Default }\r\n    public static getUIOption(): UIOpt { return { isClickBgCloseUI: false } }\r\n    public static getBundleName(): string { return BundleName.GameFight }\r\n\r\n    protected onLoad(): void {\r\n\r\n    }\r\n    async closeUI() {\r\n        UIMgr.closeUI(GamePauseUI);\r\n    }\r\n    async onShow(): Promise<void> {\r\n        this.NodePauseUI!.active = true;\r\n        this.NodeTimeUI!.active = false;\r\n        this.refreshPauseUI();\r\n    }\r\n    async onHide(): Promise<void> {\r\n    }\r\n    async onClose(): Promise<void> {\r\n        this.stopCountdown(); // 停止倒计时\r\n        GameIns.rogueManager.recycleRogueItems(this.contentRogue!);\r\n    }\r\n    protected onDestroy(): void {\r\n    }\r\n\r\n    refreshPauseUI(){\r\n        let data = GameIns.rogueManager.getSeLectedHistory();\r\n        GameIns.rogueManager.recycleRogueItems(this.contentRogue!);\r\n        data.forEach((value,key)=>{\r\n            let config = MyApp.lubanTables.TbResWordGroup.get(key);\r\n            GameIns.rogueManager.setRogueItem(this.contentRogue!,config!.wordId,value);\r\n        })\r\n    }\r\n\r\n    onBtnResumeClicked() {\r\n        this.NodePauseUI!.active = false;\r\n        this.NodeTimeUI!.active = true;\r\n        this.startCountdown();\r\n    }\r\n\r\n    onBtnExitClicked() {\r\n        this.closeUI();\r\n        GameIns.battleManager.quitBattle();\r\n    }\r\n\r\n    // 开始倒计时\r\n    private startCountdown(): void {\r\n        this._countdown = 3; // 初始化倒计时\r\n        this.updateCountdownLabel(); // 更新初始显示\r\n\r\n        this._countdownInterval = setInterval(() => {\r\n            this._countdown--;\r\n            this.updateCountdownLabel();\r\n\r\n            if (this._countdown <= 0) {\r\n                this.stopCountdown();\r\n                this.onCountdownFinished();\r\n            }\r\n        }, 1000); // 每秒更新一次\r\n    }\r\n\r\n    // 停止倒计时\r\n    private stopCountdown(): void {\r\n        if (this._countdownInterval !== null) {\r\n            clearInterval(this._countdownInterval);\r\n            this._countdownInterval = null;\r\n        }\r\n    }\r\n\r\n    // 更新倒计时文本\r\n    private updateCountdownLabel(): void {\r\n        if (this.spTime) {\r\n            this.spTime.spriteFrame = this.downFrames[this._countdown];\r\n        }\r\n    }\r\n\r\n    // 倒计时结束时的逻辑\r\n    private onCountdownFinished(): void {\r\n        GameIns.gameStateManager.gameResume();\r\n        this.closeUI();\r\n    }\r\n}\r\n\r\n\r\n"]}
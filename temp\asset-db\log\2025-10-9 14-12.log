2025-10-9 14:12:17-debug: start **** info
2025-10-9 14:12:17-log: Cannot access game frame or container.
2025-10-9 14:12:18-debug: asset-db:require-engine-code (426ms)
2025-10-9 14:12:18-log: [bullet]:bullet wasm lib loaded.
2025-10-9 14:12:18-log: [box2d]:box2d wasm lib loaded.
2025-10-9 14:12:18-log: meshopt wasm decoder initialized
2025-10-9 14:12:18-log: Cocos Creator v3.8.6
2025-10-9 14:12:18-debug: [Assets Memory track]: asset-db-plugin-register: programming start:81.27MB, end 84.03MB, increase: 2.76MB
2025-10-9 14:12:18-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:30.91MB, end 80.15MB, increase: 49.24MB
2025-10-9 14:12:18-log: Using legacy pipeline
2025-10-9 14:12:19-debug: [Assets Memory track]: asset-db-plugin-register: builder start:84.06MB, end 224.94MB, increase: 140.88MB
2025-10-9 14:12:19-debug: [Assets Memory track]: asset-db-plugin-register: engine-extends start:225.19MB, end 228.33MB, increase: 3.14MB
2025-10-9 14:12:19-debug: [Assets Memory track]: asset-db-plugin-register: project start:81.13MB, end 228.53MB, increase: 147.41MB
2025-10-9 14:12:18-log: Forward render pipeline initialized.
2025-10-9 14:12:19-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:80.17MB, end 228.56MB, increase: 148.39MB
2025-10-9 14:12:19-debug: run package(huawei-quick-game) handler(enable) start
2025-10-9 14:12:19-debug: run package(huawei-agc) handler(enable) success!
2025-10-9 14:12:19-debug: run package(huawei-quick-game) handler(enable) success!
2025-10-9 14:12:19-debug: run package(ios) handler(enable) start
2025-10-9 14:12:19-debug: run package(huawei-agc) handler(enable) start
2025-10-9 14:12:19-debug: run package(linux) handler(enable) start
2025-10-9 14:12:19-debug: run package(ios) handler(enable) success!
2025-10-9 14:12:19-debug: run package(linux) handler(enable) success!
2025-10-9 14:12:19-debug: run package(mac) handler(enable) start
2025-10-9 14:12:19-debug: run package(mac) handler(enable) success!
2025-10-9 14:12:19-debug: run package(migu-mini-game) handler(enable) start
2025-10-9 14:12:19-debug: run package(migu-mini-game) handler(enable) success!
2025-10-9 14:12:19-debug: run package(ohos) handler(enable) start
2025-10-9 14:12:19-debug: run package(native) handler(enable) success!
2025-10-9 14:12:19-debug: run package(ohos) handler(enable) success!
2025-10-9 14:12:19-debug: run package(native) handler(enable) start
2025-10-9 14:12:19-debug: run package(oppo-mini-game) handler(enable) start
2025-10-9 14:12:19-debug: run package(runtime-dev-tools) handler(enable) success!
2025-10-9 14:12:19-debug: run package(oppo-mini-game) handler(enable) success!
2025-10-9 14:12:19-debug: run package(taobao-mini-game) handler(enable) start
2025-10-9 14:12:19-debug: run package(taobao-mini-game) handler(enable) success!
2025-10-9 14:12:19-debug: run package(vivo-mini-game) handler(enable) start
2025-10-9 14:12:19-debug: run package(runtime-dev-tools) handler(enable) start
2025-10-9 14:12:19-debug: run package(web-desktop) handler(enable) start
2025-10-9 14:12:19-debug: run package(web-desktop) handler(enable) success!
2025-10-9 14:12:19-debug: run package(vivo-mini-game) handler(enable) success!
2025-10-9 14:12:19-debug: run package(web-mobile) handler(enable) success!
2025-10-9 14:12:19-debug: run package(web-mobile) handler(enable) start
2025-10-9 14:12:19-debug: run package(wechatgame) handler(enable) start
2025-10-9 14:12:19-debug: run package(wechatgame) handler(enable) success!
2025-10-9 14:12:19-debug: run package(wechatprogram) handler(enable) success!
2025-10-9 14:12:19-debug: run package(wechatprogram) handler(enable) start
2025-10-9 14:12:19-debug: run package(windows) handler(enable) success!
2025-10-9 14:12:19-debug: run package(xiaomi-quick-game) handler(enable) start
2025-10-9 14:12:19-debug: run package(windows) handler(enable) start
2025-10-9 14:12:19-debug: run package(cocos-service) handler(enable) start
2025-10-9 14:12:19-debug: run package(cocos-service) handler(enable) success!
2025-10-9 14:12:19-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-10-9 14:12:19-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-10-9 14:12:19-debug: run package(im-plugin) handler(enable) start
2025-10-9 14:12:19-debug: run package(im-plugin) handler(enable) success!
2025-10-9 14:12:19-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-10-9 14:12:19-debug: run package(emitter-editor) handler(enable) success!
2025-10-9 14:12:19-debug: start refresh asset from db://assets/editor/enum-gen/EmitterEnum.ts...
2025-10-9 14:12:19-debug: run package(emitter-editor) handler(enable) start
2025-10-9 14:12:19-debug: refresh asset db://assets/editor/enum-gen success
2025-10-9 14:12:19-debug: start refresh asset from db://assets/editor/enum-gen/EnemyEnum.ts...
2025-10-9 14:12:19-debug: refresh asset db://assets/editor/enum-gen success
2025-10-9 14:12:19-debug: asset-db:worker-init: initPlugin (963ms)
2025-10-9 14:12:19-debug: [Assets Memory track]: asset-db:worker-init start:30.90MB, end 225.34MB, increase: 194.44MB
2025-10-9 14:12:19-debug: Run asset db hook programming:beforePreStart success!
2025-10-9 14:12:19-debug: Run asset db hook engine-extends:beforePreStart ...
2025-10-9 14:12:19-debug: Run asset db hook engine-extends:beforePreStart success!
2025-10-9 14:12:19-debug: Run asset db hook programming:beforePreStart ...
2025-10-9 14:12:19-debug: start custom db i18n...
2025-10-9 14:12:19-debug: run package(i18n) handler(enable) start
2025-10-9 14:12:19-debug: run package(level-editor) handler(enable) start
2025-10-9 14:12:19-debug: run package(i18n) handler(enable) success!
2025-10-9 14:12:19-debug: run package(level-editor) handler(enable) success!
2025-10-9 14:12:19-debug: start asset-db(i18n)...
2025-10-9 14:12:19-debug: run package(placeholder) handler(enable) start
2025-10-9 14:12:19-debug: run package(placeholder) handler(enable) success!
2025-10-9 14:12:19-debug: asset-db:worker-init (1565ms)
2025-10-9 14:12:19-debug: asset-db-hook-programming-beforePreStart (108ms)
2025-10-9 14:12:19-debug: asset-db-hook-engine-extends-beforePreStart (108ms)
2025-10-9 14:12:19-debug: [Assets Memory track]: asset-db:worker-startup-database[i18n] start:226.37MB, end 230.27MB, increase: 3.90MB
2025-10-9 14:12:19-debug: asset-db:worker-startup-database[i18n] (35ms)
2025-10-9 14:12:19-debug: Preimport db internal success
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\scenes
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\prefab
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\resources\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\const
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\reviveUI
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\mask
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueItem
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_friend\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_task\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\plane
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\dyncTerrain
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\utils
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\friend
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\gameui\game
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\editor\enum-gen\EmitterEnum.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\editor\enum-gen\EnemyEnum.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\CommonEntry.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\const\BundleConst.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\plane\PlaneCacheInfo.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\dyncTerrain\EmittierTerrain.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\dyncTerrain\RandTerrain.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\BattleManager.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\GameMapManager.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\RogueManager.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\utils\UITools.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\friend\FriendUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\LevelLayerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\LevelNodeCheckOutScreen.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\gameui\game\GameReviveUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\gameui\game\GamePauseUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\gameui\game\MBoomUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\gameui\game\WheelSpinnerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\gameui\game\RogueUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:19-debug: Preimport db assets success
2025-10-9 14:12:19-debug: Run asset db hook programming:afterPreStart ...
2025-10-9 14:12:19-debug: starting packer-driver...
2025-10-9 14:12:27-debug: initialize scripting environment...
2025-10-9 14:12:27-debug: [[Executor]] prepare before lock
2025-10-9 14:12:27-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-10-9 14:12:27-debug: [[Executor]] prepare after unlock
2025-10-9 14:12:27-debug: Run asset db hook programming:afterPreStart success!
2025-10-9 14:12:27-debug: Run asset db hook engine-extends:afterPreStart ...
2025-10-9 14:12:27-debug: Run asset db hook engine-extends:afterPreStart success!
2025-10-9 14:12:27-debug: [Assets Memory track]: asset-db:worker-init: preStart start:225.36MB, end 240.50MB, increase: 15.14MB
2025-10-9 14:12:27-debug: Start up the 'internal' database...
2025-10-9 14:12:28-debug: asset-db:worker-effect-data-processing (241ms)
2025-10-9 14:12:27-debug: asset-db-hook-programming-afterPreStart (8587ms)
2025-10-9 14:12:28-debug: asset-db-hook-engine-extends-afterPreStart (241ms)
2025-10-9 14:12:28-debug: Start up the 'assets' database...
2025-10-9 14:12:28-debug: asset-db:worker-startup-database[internal] (9089ms)
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\scenes\Game.scene
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbplane.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\prefab\GameReviveUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\prefab\MBoomUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\prefab\GamePauseUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\prefab\RogueItem.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\prefab\RogueUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\prefab\WheelSpinnerUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\mainCommon.plist
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\mainCommon.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\mainCommon.plist@b3a44
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\mainCommon.plist@d490c
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\mainCommon.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\auto-atlas.pac
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\mainCommon.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\bg_num.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\boss.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\boss_hp.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\boss_hp2.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\bg_num.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\boss.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\bg_num.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\boss_hp.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\boss.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\boss_hp_di.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\boss_hp2.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\boss_hp.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\boss_hp2.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\boss_hp_di.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\boss_hp_di2.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\boss_hp_di.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\finger.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\fingerprint.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\HP.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\boss_hp_di2.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\boss_hp_di2.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\fingerprint.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\finger.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\fingerprint.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\hp_bar.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\finger.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\HP.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\HP.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\hp_bar.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\hp_bar_bg.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\Instructions_txt.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\hp_bar.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\love.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\pause.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\hp_bar_bg.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\Instructions_txt.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\love.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\hp_bar_bg.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\Instructions_txt.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\love.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\pause.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\plane_state.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\pause.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\plane_state.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\point to.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\prgress.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\prgress_bg.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\plane_state.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\score.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\point to.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\prgress.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\prgress_bg.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\prgress.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\point to.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\prgress_bg.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\score.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\skill_bg.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\score.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\skill_bg.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\0.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\skill_mask.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\mask\mask1.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\skill_bg.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\1.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\0.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\skill_mask.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\mask\mask1.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\0.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\fightUI\skill_mask.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\mask\mask1.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\1.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\2.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\1.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\2.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\3.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\4.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\2.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\5.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\3.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\4.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\4.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\3.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\5.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\6.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\7.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\5.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\6.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\8.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\9.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\6.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\7.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\auto-atlas.pac
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\7.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\8.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\9.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\8.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\Keep fighting.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\9.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\pause.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\Received rewards.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\Keep fighting.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\pause.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\Keep fighting.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\Return to the game.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\pause.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\Received rewards.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\withdraw from action.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\Received rewards.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\reviveUI\auto-atlas.pac
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\Return to the game.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\withdraw from action.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\Return to the game.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\pauseUI\withdraw from action.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\reviveUI\Close_btn.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\reviveUI\Resurrection_bg.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\reviveUI\Resurrection_green.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\reviveUI\Close_btn.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\reviveUI\Resurrection_yellow.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\reviveUI\Resurrection_bg.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueItem\empty.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\reviveUI\Resurrection_green.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\reviveUI\Close_btn.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\reviveUI\Resurrection_bg.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\reviveUI\Resurrection_green.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\reviveUI\Resurrection_yellow.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueItem\empty.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\reviveUI\Resurrection_yellow.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueItem\empty.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueItem\quantity_bg.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueItem\skill_bg.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\auto-atlas.pac
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\ad.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\bg1.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueItem\quantity_bg.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueItem\skill_bg.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueItem\quantity_bg.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\bg2.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueItem\skill_bg.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\ad.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\ad.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\bg1.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\bg1.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\bg2.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\bgText.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\bg2.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\btn.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\kaung1.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\bgText.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\btn.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\bgText.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\kuang2.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\btn.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\kaung1.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\null.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\kaung1.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\kuang2.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\kuang2.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\progress1.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\null.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\progress2.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\null.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\progress3.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\progress1.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\progress2.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\progress2.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\progress1.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\progress4.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\progress3.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\progress5.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\progress3.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\progress4.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\progress4.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\title.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\1.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\progress5.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\progress5.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\2.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\title.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\1.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\3.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\rogueUI\title.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\1.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\2.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\4.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\2.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\3.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\auto-atlas.pac
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\3.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\4.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\center.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\4.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\Circle center.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\Close Text.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\center.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\Lottery Text.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\center.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\Circle center.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\Close Text.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\pointer.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\Circle center.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\Lottery Text.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\Close Text.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\Lottery Text.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\pointer.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\reward_bg.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\pointer.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\Selected skills.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\start_btn.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\turntable1.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\reward_bg.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\reward_bg.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\Selected skills.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\start_btn.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\turntable1.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\turntable2.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\Selected skills.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\start_btn.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\turntable1.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\turntable2.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\turntable2.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\prefab\ui\GmButtonUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\prefab\ui\GmUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\turntable_bg.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\turntable_bright.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\turntable_bg.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\turntable_bg.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\turntable_bright.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\wheelSpinnerUI\turntable_bright.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_friend\prefab\ui\FriendAddUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_friend\prefab\ui\FriendListUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_friend\prefab\ui\FriendCellUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_friend\prefab\ui\FriendStrangerUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_friend\prefab\ui\FriendUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_task\prefab\ui\TaskTipUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\mainCommon.plist
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cReImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\mainCommon.plist@b3a44
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: %cReImport%c: E:\M2Game\Client\assets\bundles\game_fight\texture\mainCommon.plist@d490c
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:12:28-debug: lazy register asset handler *
2025-10-9 14:12:28-debug: lazy register asset handler directory
2025-10-9 14:12:28-debug: lazy register asset handler spine-data
2025-10-9 14:12:28-debug: lazy register asset handler json
2025-10-9 14:12:28-debug: lazy register asset handler text
2025-10-9 14:12:28-debug: lazy register asset handler dragonbones
2025-10-9 14:12:28-debug: lazy register asset handler dragonbones-atlas
2025-10-9 14:12:28-debug: lazy register asset handler javascript
2025-10-9 14:12:28-debug: lazy register asset handler terrain
2025-10-9 14:12:28-debug: lazy register asset handler typescript
2025-10-9 14:12:28-debug: lazy register asset handler prefab
2025-10-9 14:12:28-debug: lazy register asset handler sprite-frame
2025-10-9 14:12:28-debug: lazy register asset handler scene
2025-10-9 14:12:28-debug: lazy register asset handler tiled-map
2025-10-9 14:12:28-debug: lazy register asset handler buffer
2025-10-9 14:12:28-debug: lazy register asset handler image
2025-10-9 14:12:28-debug: lazy register asset handler alpha-image
2025-10-9 14:12:28-debug: lazy register asset handler texture-cube
2025-10-9 14:12:28-debug: lazy register asset handler texture
2025-10-9 14:12:28-debug: lazy register asset handler erp-texture-cube
2025-10-9 14:12:28-debug: lazy register asset handler render-texture
2025-10-9 14:12:28-debug: lazy register asset handler texture-cube-face
2025-10-9 14:12:28-debug: lazy register asset handler sign-image
2025-10-9 14:12:28-debug: lazy register asset handler rt-sprite-frame
2025-10-9 14:12:28-debug: lazy register asset handler gltf
2025-10-9 14:12:28-debug: lazy register asset handler gltf-mesh
2025-10-9 14:12:28-debug: lazy register asset handler gltf-animation
2025-10-9 14:12:28-debug: lazy register asset handler gltf-skeleton
2025-10-9 14:12:28-debug: lazy register asset handler gltf-embeded-image
2025-10-9 14:12:28-debug: lazy register asset handler gltf-scene
2025-10-9 14:12:28-debug: lazy register asset handler material
2025-10-9 14:12:28-debug: lazy register asset handler fbx
2025-10-9 14:12:28-debug: lazy register asset handler gltf-material
2025-10-9 14:12:28-debug: lazy register asset handler physics-material
2025-10-9 14:12:28-debug: lazy register asset handler effect-header
2025-10-9 14:12:28-debug: lazy register asset handler effect
2025-10-9 14:12:28-debug: lazy register asset handler animation-graph
2025-10-9 14:12:28-debug: lazy register asset handler animation-graph-variant
2025-10-9 14:12:28-debug: lazy register asset handler audio-clip
2025-10-9 14:12:28-debug: lazy register asset handler animation-clip
2025-10-9 14:12:28-debug: lazy register asset handler animation-mask
2025-10-9 14:12:28-debug: lazy register asset handler bitmap-font
2025-10-9 14:12:28-debug: lazy register asset handler particle
2025-10-9 14:12:28-debug: lazy register asset handler ttf-font
2025-10-9 14:12:28-debug: lazy register asset handler label-atlas
2025-10-9 14:12:28-debug: lazy register asset handler sprite-atlas
2025-10-9 14:12:28-debug: lazy register asset handler render-pipeline
2025-10-9 14:12:28-debug: lazy register asset handler render-flow
2025-10-9 14:12:28-debug: lazy register asset handler auto-atlas
2025-10-9 14:12:28-debug: lazy register asset handler render-stage
2025-10-9 14:12:28-debug: lazy register asset handler instantiation-material
2025-10-9 14:12:28-debug: lazy register asset handler instantiation-animation
2025-10-9 14:12:28-debug: lazy register asset handler instantiation-mesh
2025-10-9 14:12:28-debug: lazy register asset handler instantiation-skeleton
2025-10-9 14:12:28-debug: lazy register asset handler video-clip
2025-10-9 14:12:28-debug: asset-db:worker-startup-database[assets] (9393ms)
2025-10-9 14:12:28-debug: asset-db:start-database (9549ms)
2025-10-9 14:12:28-debug: asset-db:ready (12708ms)
2025-10-9 14:12:28-debug: init worker message success
2025-10-9 14:12:28-debug: fix the bug of updateDefaultUserData
2025-10-9 14:12:28-debug: programming:execute-script (3ms)
2025-10-9 14:12:29-debug: [Build Memory track]: builder:worker-init start:198.05MB, end 205.69MB, increase: 7.64MB
2025-10-9 14:12:29-debug: builder:worker-init (393ms)
2025-10-9 14:23:06-debug: refresh db internal success
2025-10-9 14:23:06-debug: refresh db i18n success
2025-10-9 14:23:06-debug: refresh db assets success
2025-10-9 14:23:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:23:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:23:06-debug: asset-db:refresh-all-database (200ms)
2025-10-9 14:23:06-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 14:23:12-debug: Query all assets info in project
2025-10-9 14:23:12-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-9 14:23:12-debug: Skip compress image, progress: 0%
2025-10-9 14:23:12-debug: Num of bundles: 19..., progress: 0%
2025-10-9 14:23:12-debug: Init all bundles start..., progress: 0%
2025-10-9 14:23:12-debug: Init bundle root assets start..., progress: 0%
2025-10-9 14:23:12-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:23:12-debug: 查询 Asset Bundle start, progress: 0%
2025-10-9 14:23:12-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-9 14:23:12-debug:   Number of other assets: 2659
2025-10-9 14:23:12-debug: Init bundle root assets success..., progress: 0%
2025-10-9 14:23:12-debug:   Number of all scenes: 11
2025-10-9 14:23:12-debug:   Number of all scripts: 313
2025-10-9 14:23:12-debug: // ---- build task 查询 Asset Bundle ---- (28ms)
2025-10-9 14:23:12-debug: [Build Memory track]: 查询 Asset Bundle start:209.57MB, end 208.22MB, increase: -1387.20KB
2025-10-9 14:23:12-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:23:12-debug: 查询 Asset Bundle start, progress: 5%
2025-10-9 14:23:12-log: run build task 查询 Asset Bundle success in 28 ms√, progress: 5%
2025-10-9 14:23:12-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-10-9 14:23:12-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-9 14:23:12-debug: [Build Memory track]: 查询 Asset Bundle start:208.25MB, end 208.62MB, increase: 386.18KB
2025-10-9 14:23:12-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-10-9 14:23:12-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:23:12-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-10-9 14:23:12-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-10-9 14:23:12-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:208.66MB, end 208.69MB, increase: 34.24KB
2025-10-9 14:23:12-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-9 14:23:12-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-9 14:23:12-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-10-9 14:23:12-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-10-9 14:23:12-debug: [Build Memory track]: 填充脚本数据到 settings.json start:208.72MB, end 208.75MB, increase: 26.65KB
2025-10-9 14:23:12-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-9 14:23:12-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:23:12-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-10-9 14:23:12-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-10-9 14:23:12-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:208.78MB, end 209.10MB, increase: 329.80KB
2025-10-9 14:23:12-debug: Query all assets info in project
2025-10-9 14:23:12-debug: Query all assets info in project
2025-10-9 14:23:12-debug: Query all assets info in project
2025-10-9 14:23:12-debug: Query all assets info in project
2025-10-9 14:23:12-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-9 14:23:12-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-9 14:23:12-debug: Skip compress image, progress: 0%
2025-10-9 14:23:12-debug: Skip compress image, progress: 0%
2025-10-9 14:23:12-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-9 14:23:12-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-9 14:23:12-debug: Skip compress image, progress: 0%
2025-10-9 14:23:12-debug: Skip compress image, progress: 0%
2025-10-9 14:23:12-debug: Init all bundles start..., progress: 0%
2025-10-9 14:23:12-debug: Num of bundles: 19..., progress: 0%
2025-10-9 14:23:12-debug: 查询 Asset Bundle start, progress: 0%
2025-10-9 14:23:12-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:23:12-debug: Init bundle root assets start..., progress: 0%
2025-10-9 14:23:12-debug: 查询 Asset Bundle start, progress: 0%
2025-10-9 14:23:12-debug: Num of bundles: 19..., progress: 0%
2025-10-9 14:23:12-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:23:12-debug: Init bundle root assets start..., progress: 0%
2025-10-9 14:23:12-debug: 查询 Asset Bundle start, progress: 0%
2025-10-9 14:23:12-debug: Init all bundles start..., progress: 0%
2025-10-9 14:23:12-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:23:12-debug: Init all bundles start..., progress: 0%
2025-10-9 14:23:12-debug: Init bundle root assets start..., progress: 0%
2025-10-9 14:23:12-debug: Init all bundles start..., progress: 0%
2025-10-9 14:23:12-debug: Num of bundles: 19..., progress: 0%
2025-10-9 14:23:12-debug: 查询 Asset Bundle start, progress: 0%
2025-10-9 14:23:12-debug: Num of bundles: 19..., progress: 0%
2025-10-9 14:23:12-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:23:12-debug: Init bundle root assets start..., progress: 0%
2025-10-9 14:23:12-debug:   Number of all scripts: 313
2025-10-9 14:23:12-debug:   Number of other assets: 2659
2025-10-9 14:23:12-debug: Init bundle root assets success..., progress: 0%
2025-10-9 14:23:12-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-9 14:23:12-debug:   Number of all scenes: 11
2025-10-9 14:23:12-debug:   Number of all scripts: 313
2025-10-9 14:23:12-debug:   Number of other assets: 2659
2025-10-9 14:23:12-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-9 14:23:12-debug: Init bundle root assets success..., progress: 0%
2025-10-9 14:23:12-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-9 14:23:12-debug:   Number of all scripts: 313
2025-10-9 14:23:12-debug:   Number of all scenes: 11
2025-10-9 14:23:12-debug:   Number of all scenes: 11
2025-10-9 14:23:12-debug:   Number of other assets: 2659
2025-10-9 14:23:12-debug: Init bundle root assets success..., progress: 0%
2025-10-9 14:23:12-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-9 14:23:12-debug:   Number of all scenes: 11
2025-10-9 14:23:12-debug:   Number of other assets: 2659
2025-10-9 14:23:12-debug:   Number of all scripts: 313
2025-10-9 14:23:12-debug: Init bundle root assets success..., progress: 0%
2025-10-9 14:23:12-debug: // ---- build task 查询 Asset Bundle ---- (68ms)
2025-10-9 14:23:12-log: run build task 查询 Asset Bundle success in 68 ms√, progress: 5%
2025-10-9 14:23:12-debug: 查询 Asset Bundle start, progress: 5%
2025-10-9 14:23:12-debug: [Build Memory track]: 查询 Asset Bundle start:210.12MB, end 216.98MB, increase: 6.86MB
2025-10-9 14:23:12-debug: [Build Memory track]: 查询 Asset Bundle start:217.59MB, end 217.03MB, increase: -571.12KB
2025-10-9 14:23:12-log: run build task 查询 Asset Bundle success in √, progress: 5%
2025-10-9 14:23:12-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:23:12-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:23:12-debug: 查询 Asset Bundle start, progress: 5%
2025-10-9 14:23:12-log: run build task 查询 Asset Bundle success in √, progress: 5%
2025-10-9 14:23:12-log: run build task 查询 Asset Bundle success in √, progress: 5%
2025-10-9 14:23:12-debug: 查询 Asset Bundle start, progress: 5%
2025-10-9 14:23:12-debug: [Build Memory track]: 查询 Asset Bundle start:217.06MB, end 217.07MB, increase: 12.15KB
2025-10-9 14:23:12-debug: 查询 Asset Bundle start, progress: 5%
2025-10-9 14:23:12-debug: [Build Memory track]: 查询 Asset Bundle start:217.11MB, end 217.12MB, increase: 12.64KB
2025-10-9 14:23:12-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:23:12-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:23:12-debug: // ---- build task 查询 Asset Bundle ---- (4ms)
2025-10-9 14:23:12-debug: [Build Memory track]: 查询 Asset Bundle start:217.15MB, end 218.60MB, increase: 1.44MB
2025-10-9 14:23:12-log: run build task 查询 Asset Bundle success in 4 ms√, progress: 10%
2025-10-9 14:23:12-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-9 14:23:12-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:23:12-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-9 14:23:12-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:23:12-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-10-9 14:23:12-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:23:12-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-10-9 14:23:12-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-9 14:23:12-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-9 14:23:12-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-10-9 14:23:12-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:23:12-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:218.72MB, end 218.75MB, increase: 32.59KB
2025-10-9 14:23:12-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-10-9 14:23:12-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-10-9 14:23:12-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-9 14:23:12-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-9 14:23:12-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-10-9 14:23:12-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-9 14:23:12-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-9 14:23:12-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-10-9 14:23:12-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-10-9 14:23:12-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 12%
2025-10-9 14:23:12-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-9 14:23:12-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-9 14:23:12-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-10-9 14:23:12-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-10-9 14:23:12-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-10-9 14:23:12-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-9 14:23:12-debug: [Build Memory track]: 填充脚本数据到 settings.json start:218.91MB, end 218.93MB, increase: 19.77KB
2025-10-9 14:23:12-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-9 14:23:12-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-9 14:23:12-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:23:12-debug: // ---- build task 填充脚本数据到 settings.json ---- (2ms)
2025-10-9 14:23:12-log: run build task 填充脚本数据到 settings.json success in 2 ms√, progress: 13%
2025-10-9 14:23:12-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-9 14:23:12-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:23:12-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-10-9 14:23:12-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-10-9 14:23:12-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-9 14:23:12-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-10-9 14:23:12-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:23:12-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-10-9 14:23:12-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:23:12-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-9 14:23:12-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-10-9 14:23:12-log: run build task 整理部分构建选项内数据到 settings.json success in 4 ms√, progress: 15%
2025-10-9 14:23:12-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:219.10MB, end 220.31MB, increase: 1.21MB
2025-10-9 14:23:12-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-10-9 14:23:12-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-10-9 14:23:12-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-10-9 14:23:29-debug: %cImport%c: E:\M2Game\Client\assets\editor\PlaneEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:23:29-debug: asset-db:reimport-assetd90c1d4b-98f0-42eb-8111-dacc86636312 (3ms)
2025-10-9 14:23:31-debug: Query all assets info in project
2025-10-9 14:23:31-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-9 14:23:31-debug: Skip compress image, progress: 0%
2025-10-9 14:23:31-debug: Init all bundles start..., progress: 0%
2025-10-9 14:23:31-debug: 查询 Asset Bundle start, progress: 0%
2025-10-9 14:23:31-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:23:31-debug: Num of bundles: 19..., progress: 0%
2025-10-9 14:23:31-debug: Init bundle root assets start..., progress: 0%
2025-10-9 14:23:31-debug:   Number of all scenes: 11
2025-10-9 14:23:31-debug:   Number of all scripts: 313
2025-10-9 14:23:31-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-9 14:23:31-debug:   Number of other assets: 2659
2025-10-9 14:23:31-debug: Init bundle root assets success..., progress: 0%
2025-10-9 14:23:31-debug: // ---- build task 查询 Asset Bundle ---- (27ms)
2025-10-9 14:23:31-log: run build task 查询 Asset Bundle success in 27 ms√, progress: 5%
2025-10-9 14:23:31-debug: [Build Memory track]: 查询 Asset Bundle start:220.45MB, end 210.92MB, increase: -9753.54KB
2025-10-9 14:23:31-debug: 查询 Asset Bundle start, progress: 5%
2025-10-9 14:23:31-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:23:31-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-10-9 14:23:31-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-10-9 14:23:31-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-9 14:23:31-debug: [Build Memory track]: 查询 Asset Bundle start:210.95MB, end 211.34MB, increase: 397.28KB
2025-10-9 14:23:31-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:23:31-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-10-9 14:23:31-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:211.37MB, end 211.38MB, increase: 17.48KB
2025-10-9 14:23:31-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-9 14:23:31-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-9 14:23:31-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-10-9 14:23:31-debug: [Build Memory track]: 填充脚本数据到 settings.json start:211.41MB, end 211.44MB, increase: 25.84KB
2025-10-9 14:23:31-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-9 14:23:31-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-10-9 14:23:31-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:23:31-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-10-9 14:23:31-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-10-9 14:23:31-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:211.47MB, end 211.78MB, increase: 323.52KB
2025-10-9 14:23:43-debug: refresh db internal success
2025-10-9 14:23:43-debug: refresh db i18n success
2025-10-9 14:23:43-debug: refresh db assets success
2025-10-9 14:23:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:23:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:23:43-debug: asset-db:refresh-all-database (158ms)
2025-10-9 14:23:43-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 14:23:43-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 14:25:05-debug: refresh db internal success
2025-10-9 14:25:05-debug: refresh db i18n success
2025-10-9 14:25:05-debug: %cImport%c: E:\M2Game\Client\assets\editor\plane\PlaneEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:25:06-debug: refresh db assets success
2025-10-9 14:25:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:25:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:25:06-debug: asset-db:refresh-all-database (210ms)
2025-10-9 14:25:06-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 14:25:06-debug: Query all assets info in project
2025-10-9 14:25:06-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-9 14:25:06-debug: Skip compress image, progress: 0%
2025-10-9 14:25:06-debug: Init all bundles start..., progress: 0%
2025-10-9 14:25:06-debug: Num of bundles: 19..., progress: 0%
2025-10-9 14:25:06-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:25:06-debug: 查询 Asset Bundle start, progress: 0%
2025-10-9 14:25:06-debug: Init bundle root assets start..., progress: 0%
2025-10-9 14:25:06-debug:   Number of all scenes: 11
2025-10-9 14:25:06-debug:   Number of other assets: 2659
2025-10-9 14:25:06-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-9 14:25:06-debug:   Number of all scripts: 313
2025-10-9 14:25:06-debug: Init bundle root assets success..., progress: 0%
2025-10-9 14:25:06-log: run build task 查询 Asset Bundle success in 26 ms√, progress: 5%
2025-10-9 14:25:06-debug: // ---- build task 查询 Asset Bundle ---- (26ms)
2025-10-9 14:25:06-debug: [Build Memory track]: 查询 Asset Bundle start:226.09MB, end 226.48MB, increase: 398.92KB
2025-10-9 14:25:06-debug: 查询 Asset Bundle start, progress: 5%
2025-10-9 14:25:06-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:25:06-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-10-9 14:25:06-debug: [Build Memory track]: 查询 Asset Bundle start:226.51MB, end 226.88MB, increase: 377.82KB
2025-10-9 14:25:06-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-10-9 14:25:06-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-9 14:25:06-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:25:06-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-10-9 14:25:06-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:226.91MB, end 226.92MB, increase: 17.02KB
2025-10-9 14:25:06-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-9 14:25:06-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-9 14:25:06-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-10-9 14:25:06-debug: [Build Memory track]: 填充脚本数据到 settings.json start:226.95MB, end 226.97MB, increase: 16.93KB
2025-10-9 14:25:06-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-9 14:25:06-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:25:06-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-10-9 14:25:06-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-10-9 14:25:06-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:227.00MB, end 227.31MB, increase: 322.16KB
2025-10-9 14:25:10-debug: refresh db internal success
2025-10-9 14:25:10-debug: refresh db i18n success
2025-10-9 14:25:10-debug: refresh db assets success
2025-10-9 14:25:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:25:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:25:10-debug: asset-db:refresh-all-database (176ms)
2025-10-9 14:26:00-debug: refresh db internal success
2025-10-9 14:26:00-debug: refresh db i18n success
2025-10-9 14:26:00-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\enemy\EnemyPlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:26:00-debug: refresh db assets success
2025-10-9 14:26:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:26:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:26:00-debug: asset-db:refresh-all-database (179ms)
2025-10-9 14:26:00-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 14:26:00-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 14:26:05-debug: Query all assets info in project
2025-10-9 14:26:05-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-9 14:26:05-debug: Skip compress image, progress: 0%
2025-10-9 14:26:05-debug: 查询 Asset Bundle start, progress: 0%
2025-10-9 14:26:05-debug: Num of bundles: 19..., progress: 0%
2025-10-9 14:26:05-debug: Init all bundles start..., progress: 0%
2025-10-9 14:26:05-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:26:05-debug: Init bundle root assets start..., progress: 0%
2025-10-9 14:26:05-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-9 14:26:05-debug:   Number of other assets: 2659
2025-10-9 14:26:05-debug: Init bundle root assets success..., progress: 0%
2025-10-9 14:26:05-debug:   Number of all scripts: 313
2025-10-9 14:26:05-debug:   Number of all scenes: 11
2025-10-9 14:26:05-log: run build task 查询 Asset Bundle success in 23 ms√, progress: 5%
2025-10-9 14:26:05-debug: // ---- build task 查询 Asset Bundle ---- (23ms)
2025-10-9 14:26:05-debug: [Build Memory track]: 查询 Asset Bundle start:228.33MB, end 228.59MB, increase: 271.02KB
2025-10-9 14:26:05-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:26:05-debug: 查询 Asset Bundle start, progress: 5%
2025-10-9 14:26:05-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-10-9 14:26:05-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-10-9 14:26:05-debug: [Build Memory track]: 查询 Asset Bundle start:228.62MB, end 228.99MB, increase: 378.16KB
2025-10-9 14:26:05-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:26:05-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-9 14:26:05-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-10-9 14:26:05-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:229.02MB, end 229.04MB, increase: 17.13KB
2025-10-9 14:26:05-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-9 14:26:05-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-9 14:26:05-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-10-9 14:26:05-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-10-9 14:26:05-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-9 14:26:05-debug: [Build Memory track]: 填充脚本数据到 settings.json start:229.06MB, end 229.09MB, increase: 25.83KB
2025-10-9 14:26:05-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:26:05-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-10-9 14:26:05-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-10-9 14:26:05-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:229.12MB, end 229.43MB, increase: 322.25KB
2025-10-9 14:26:49-debug: refresh db internal success
2025-10-9 14:26:49-debug: refresh db i18n success
2025-10-9 14:26:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\enemy\EnemyPlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:26:49-debug: refresh db assets success
2025-10-9 14:26:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:26:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:26:49-debug: asset-db:refresh-all-database (189ms)
2025-10-9 14:26:51-debug: Query all assets info in project
2025-10-9 14:26:51-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-9 14:26:51-debug: Skip compress image, progress: 0%
2025-10-9 14:26:51-debug: Init all bundles start..., progress: 0%
2025-10-9 14:26:51-debug: Num of bundles: 19..., progress: 0%
2025-10-9 14:26:51-debug: 查询 Asset Bundle start, progress: 0%
2025-10-9 14:26:51-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:26:51-debug: Init bundle root assets start..., progress: 0%
2025-10-9 14:26:51-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-9 14:26:51-debug:   Number of all scenes: 11
2025-10-9 14:26:51-debug:   Number of all scripts: 313
2025-10-9 14:26:51-debug: Init bundle root assets success..., progress: 0%
2025-10-9 14:26:51-debug:   Number of other assets: 2659
2025-10-9 14:26:51-debug: // ---- build task 查询 Asset Bundle ---- (37ms)
2025-10-9 14:26:51-log: run build task 查询 Asset Bundle success in 37 ms√, progress: 5%
2025-10-9 14:26:51-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:26:51-debug: [Build Memory track]: 查询 Asset Bundle start:213.03MB, end 210.41MB, increase: -2682.09KB
2025-10-9 14:26:51-debug: 查询 Asset Bundle start, progress: 5%
2025-10-9 14:26:51-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-10-9 14:26:51-debug: [Build Memory track]: 查询 Asset Bundle start:210.44MB, end 210.81MB, increase: 378.70KB
2025-10-9 14:26:51-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:26:51-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-9 14:26:51-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-10-9 14:26:51-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-10-9 14:26:51-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-10-9 14:26:51-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:210.84MB, end 210.87MB, increase: 26.38KB
2025-10-9 14:26:51-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-9 14:26:51-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-9 14:26:51-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-10-9 14:26:51-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-9 14:26:51-debug: [Build Memory track]: 填充脚本数据到 settings.json start:210.90MB, end 210.91MB, increase: 16.85KB
2025-10-9 14:26:51-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:26:51-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-10-9 14:26:51-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:210.94MB, end 211.28MB, increase: 342.85KB
2025-10-9 14:26:51-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-10-9 14:28:13-debug: refresh db internal success
2025-10-9 14:28:13-debug: refresh db i18n success
2025-10-9 14:28:13-debug: %cImport%c: E:\M2Game\Client\assets\editor\plane\PlaneEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:28:13-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\enemy\EnemyPlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:28:13-debug: refresh db assets success
2025-10-9 14:28:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:28:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:28:13-debug: asset-db:refresh-all-database (192ms)
2025-10-9 14:28:13-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 14:28:13-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 14:28:15-debug: Query all assets info in project
2025-10-9 14:28:15-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-9 14:28:15-debug: Skip compress image, progress: 0%
2025-10-9 14:28:15-debug: Init all bundles start..., progress: 0%
2025-10-9 14:28:15-debug: Num of bundles: 19..., progress: 0%
2025-10-9 14:28:15-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:28:15-debug: Init bundle root assets start..., progress: 0%
2025-10-9 14:28:15-debug: 查询 Asset Bundle start, progress: 0%
2025-10-9 14:28:15-debug:   Number of all scenes: 11
2025-10-9 14:28:15-debug:   Number of all scripts: 313
2025-10-9 14:28:15-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-9 14:28:15-debug:   Number of other assets: 2659
2025-10-9 14:28:15-debug: Init bundle root assets success..., progress: 0%
2025-10-9 14:28:15-debug: // ---- build task 查询 Asset Bundle ---- (23ms)
2025-10-9 14:28:15-debug: 查询 Asset Bundle start, progress: 5%
2025-10-9 14:28:15-debug: [Build Memory track]: 查询 Asset Bundle start:219.81MB, end 220.18MB, increase: 377.19KB
2025-10-9 14:28:15-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:28:15-log: run build task 查询 Asset Bundle success in 23 ms√, progress: 5%
2025-10-9 14:28:15-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-10-9 14:28:15-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-10-9 14:28:15-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-9 14:28:15-debug: [Build Memory track]: 查询 Asset Bundle start:220.21MB, end 220.58MB, increase: 378.39KB
2025-10-9 14:28:15-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:28:15-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-10-9 14:28:15-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:220.61MB, end 220.63MB, increase: 17.61KB
2025-10-9 14:28:15-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-9 14:28:15-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-9 14:28:15-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-10-9 14:28:15-debug: [Build Memory track]: 填充脚本数据到 settings.json start:220.66MB, end 220.67MB, increase: 16.50KB
2025-10-9 14:28:15-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:28:15-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-9 14:28:15-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-10-9 14:28:15-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:220.70MB, end 221.01MB, increase: 322.30KB
2025-10-9 14:28:15-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-10-9 14:28:53-debug: refresh db internal success
2025-10-9 14:28:53-debug: refresh db i18n success
2025-10-9 14:28:53-debug: %cImport%c: E:\M2Game\Client\assets\editor\plane\PlaneEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:28:53-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\enemy\EnemyPlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:28:53-debug: refresh db assets success
2025-10-9 14:28:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:28:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:28:53-debug: asset-db:refresh-all-database (198ms)
2025-10-9 14:28:55-debug: Query all assets info in project
2025-10-9 14:28:55-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-9 14:28:55-debug: Skip compress image, progress: 0%
2025-10-9 14:28:55-debug: Init all bundles start..., progress: 0%
2025-10-9 14:28:55-debug: 查询 Asset Bundle start, progress: 0%
2025-10-9 14:28:55-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:28:55-debug: Num of bundles: 19..., progress: 0%
2025-10-9 14:28:55-debug: Init bundle root assets start..., progress: 0%
2025-10-9 14:28:55-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-9 14:28:55-debug:   Number of all scripts: 313
2025-10-9 14:28:55-debug:   Number of other assets: 2659
2025-10-9 14:28:55-debug: Init bundle root assets success..., progress: 0%
2025-10-9 14:28:55-debug:   Number of all scenes: 11
2025-10-9 14:28:55-debug: // ---- build task 查询 Asset Bundle ---- (25ms)
2025-10-9 14:28:55-log: run build task 查询 Asset Bundle success in 25 ms√, progress: 5%
2025-10-9 14:28:55-debug: [Build Memory track]: 查询 Asset Bundle start:212.19MB, end 214.94MB, increase: 2.75MB
2025-10-9 14:28:55-debug: 查询 Asset Bundle start, progress: 5%
2025-10-9 14:28:55-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:28:55-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-10-9 14:28:55-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-9 14:28:55-debug: [Build Memory track]: 查询 Asset Bundle start:214.97MB, end 215.33MB, increase: 378.60KB
2025-10-9 14:28:55-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-10-9 14:28:55-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:28:55-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-10-9 14:28:55-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:215.36MB, end 215.38MB, increase: 16.88KB
2025-10-9 14:28:55-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-9 14:28:55-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-9 14:28:55-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-10-9 14:28:55-debug: [Build Memory track]: 填充脚本数据到 settings.json start:215.41MB, end 215.43MB, increase: 16.69KB
2025-10-9 14:28:55-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-9 14:28:55-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:28:55-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-10-9 14:28:55-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:215.45MB, end 215.78MB, increase: 332.38KB
2025-10-9 14:28:55-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-10-9 14:29:12-debug: refresh db internal success
2025-10-9 14:29:12-debug: refresh db i18n success
2025-10-9 14:29:12-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\enemy\EnemyPlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:29:12-debug: refresh db assets success
2025-10-9 14:29:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:29:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:29:12-debug: asset-db:refresh-all-database (188ms)
2025-10-9 14:29:12-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 14:29:13-debug: Query all assets info in project
2025-10-9 14:29:13-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-9 14:29:13-debug: Skip compress image, progress: 0%
2025-10-9 14:29:13-debug: 查询 Asset Bundle start, progress: 0%
2025-10-9 14:29:13-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:29:13-debug: Init all bundles start..., progress: 0%
2025-10-9 14:29:13-debug: Num of bundles: 19..., progress: 0%
2025-10-9 14:29:13-debug: Init bundle root assets start..., progress: 0%
2025-10-9 14:29:13-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-9 14:29:13-debug:   Number of all scenes: 11
2025-10-9 14:29:13-debug:   Number of other assets: 2659
2025-10-9 14:29:13-log: run build task 查询 Asset Bundle success in 24 ms√, progress: 5%
2025-10-9 14:29:13-debug: [Build Memory track]: 查询 Asset Bundle start:218.90MB, end 216.96MB, increase: -1993.52KB
2025-10-9 14:29:13-debug:   Number of all scripts: 313
2025-10-9 14:29:13-debug: 查询 Asset Bundle start, progress: 5%
2025-10-9 14:29:13-debug: Init bundle root assets success..., progress: 0%
2025-10-9 14:29:13-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:29:13-debug: // ---- build task 查询 Asset Bundle ---- (24ms)
2025-10-9 14:29:13-debug: // ---- build task 查询 Asset Bundle ---- (7ms)
2025-10-9 14:29:13-log: run build task 查询 Asset Bundle success in 7 ms√, progress: 10%
2025-10-9 14:29:13-debug: [Build Memory track]: 查询 Asset Bundle start:216.99MB, end 217.35MB, increase: 377.36KB
2025-10-9 14:29:13-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-9 14:29:13-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:29:13-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-10-9 14:29:13-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:217.38MB, end 217.40MB, increase: 17.75KB
2025-10-9 14:29:13-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-9 14:29:13-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-9 14:29:13-debug: // ---- build task 填充脚本数据到 settings.json ---- (11ms)
2025-10-9 14:29:13-log: run build task 填充脚本数据到 settings.json success in 11 ms√, progress: 13%
2025-10-9 14:29:13-debug: [Build Memory track]: 填充脚本数据到 settings.json start:217.43MB, end 203.02MB, increase: -14753.35KB
2025-10-9 14:29:13-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-9 14:29:13-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:29:13-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-10-9 14:29:13-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:203.05MB, end 203.38MB, increase: 340.49KB
2025-10-9 14:29:13-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-10-9 14:29:30-debug: refresh db internal success
2025-10-9 14:29:30-debug: refresh db i18n success
2025-10-9 14:29:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\enemy\EnemyPlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:29:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:29:30-debug: refresh db assets success
2025-10-9 14:29:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:29:30-debug: asset-db:refresh-all-database (175ms)
2025-10-9 14:29:30-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 14:29:30-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 14:29:31-debug: Query all assets info in project
2025-10-9 14:29:31-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-9 14:29:31-debug: Skip compress image, progress: 0%
2025-10-9 14:29:31-debug: Init all bundles start..., progress: 0%
2025-10-9 14:29:31-debug: 查询 Asset Bundle start, progress: 0%
2025-10-9 14:29:31-debug: Num of bundles: 19..., progress: 0%
2025-10-9 14:29:31-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:29:31-debug: Init bundle root assets start..., progress: 0%
2025-10-9 14:29:31-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-9 14:29:31-debug:   Number of all scripts: 313
2025-10-9 14:29:31-debug:   Number of other assets: 2659
2025-10-9 14:29:31-debug: Init bundle root assets success..., progress: 0%
2025-10-9 14:29:31-debug:   Number of all scenes: 11
2025-10-9 14:29:31-debug: // ---- build task 查询 Asset Bundle ---- (31ms)
2025-10-9 14:29:31-log: run build task 查询 Asset Bundle success in 31 ms√, progress: 5%
2025-10-9 14:29:31-debug: [Build Memory track]: 查询 Asset Bundle start:212.57MB, end 211.11MB, increase: -1503.74KB
2025-10-9 14:29:31-debug: 查询 Asset Bundle start, progress: 5%
2025-10-9 14:29:31-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:29:31-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-10-9 14:29:31-debug: [Build Memory track]: 查询 Asset Bundle start:211.14MB, end 211.51MB, increase: 381.68KB
2025-10-9 14:29:31-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-10-9 14:29:31-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-9 14:29:31-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:29:31-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-10-9 14:29:31-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:211.54MB, end 211.55MB, increase: 16.86KB
2025-10-9 14:29:31-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-9 14:29:31-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-9 14:29:31-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-10-9 14:29:31-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-9 14:29:31-debug: [Build Memory track]: 填充脚本数据到 settings.json start:211.58MB, end 211.60MB, increase: 16.57KB
2025-10-9 14:29:31-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:29:31-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-10-9 14:29:31-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:211.63MB, end 211.94MB, increase: 321.69KB
2025-10-9 14:29:31-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-10-9 14:29:49-debug: refresh db internal success
2025-10-9 14:29:49-debug: refresh db i18n success
2025-10-9 14:29:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\enemy\EnemyPlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:29:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:29:49-debug: refresh db assets success
2025-10-9 14:29:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:29:49-debug: asset-db:refresh-all-database (176ms)
2025-10-9 14:29:49-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 14:29:49-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 14:29:50-debug: Query all assets info in project
2025-10-9 14:29:50-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-9 14:29:50-debug: Skip compress image, progress: 0%
2025-10-9 14:29:50-debug: Init all bundles start..., progress: 0%
2025-10-9 14:29:50-debug: 查询 Asset Bundle start, progress: 0%
2025-10-9 14:29:50-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:29:50-debug: Num of bundles: 19..., progress: 0%
2025-10-9 14:29:50-debug: Init bundle root assets start..., progress: 0%
2025-10-9 14:29:50-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-9 14:29:50-debug:   Number of all scripts: 313
2025-10-9 14:29:50-debug:   Number of other assets: 2659
2025-10-9 14:29:50-debug: Init bundle root assets success..., progress: 0%
2025-10-9 14:29:50-debug:   Number of all scenes: 11
2025-10-9 14:29:50-debug: // ---- build task 查询 Asset Bundle ---- (24ms)
2025-10-9 14:29:50-log: run build task 查询 Asset Bundle success in 24 ms√, progress: 5%
2025-10-9 14:29:50-debug: 查询 Asset Bundle start, progress: 5%
2025-10-9 14:29:50-debug: [Build Memory track]: 查询 Asset Bundle start:220.71MB, end 217.06MB, increase: -3741.30KB
2025-10-9 14:29:50-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:29:50-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-10-9 14:29:50-debug: [Build Memory track]: 查询 Asset Bundle start:217.09MB, end 217.46MB, increase: 377.24KB
2025-10-9 14:29:50-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-9 14:29:50-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-10-9 14:29:50-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:29:50-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:217.49MB, end 217.50MB, increase: 16.86KB
2025-10-9 14:29:50-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-10-9 14:29:50-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-9 14:29:50-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-9 14:29:50-debug: [Build Memory track]: 填充脚本数据到 settings.json start:217.53MB, end 217.55MB, increase: 16.34KB
2025-10-9 14:29:50-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-9 14:29:50-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-10-9 14:29:50-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:29:50-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-10-9 14:29:50-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-10-9 14:29:50-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:217.57MB, end 217.89MB, increase: 321.90KB
2025-10-9 14:30:21-debug: refresh db internal success
2025-10-9 14:30:21-debug: refresh db i18n success
2025-10-9 14:30:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\PlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:30:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\enemy\EnemyPlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:30:22-debug: refresh db assets success
2025-10-9 14:30:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:30:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:30:22-debug: asset-db:refresh-all-database (180ms)
2025-10-9 14:30:22-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 14:30:22-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 14:30:22-debug: Query all assets info in project
2025-10-9 14:30:22-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-9 14:30:22-debug: Skip compress image, progress: 0%
2025-10-9 14:30:22-debug: Init all bundles start..., progress: 0%
2025-10-9 14:30:22-debug: Num of bundles: 19..., progress: 0%
2025-10-9 14:30:22-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:30:22-debug: 查询 Asset Bundle start, progress: 0%
2025-10-9 14:30:22-debug: Init bundle root assets start..., progress: 0%
2025-10-9 14:30:22-debug:   Number of all scenes: 11
2025-10-9 14:30:22-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-9 14:30:22-debug:   Number of other assets: 2659
2025-10-9 14:30:22-debug: Init bundle root assets success..., progress: 0%
2025-10-9 14:30:22-debug:   Number of all scripts: 313
2025-10-9 14:30:22-debug: // ---- build task 查询 Asset Bundle ---- (36ms)
2025-10-9 14:30:22-log: run build task 查询 Asset Bundle success in 36 ms√, progress: 5%
2025-10-9 14:30:22-debug: [Build Memory track]: 查询 Asset Bundle start:205.03MB, end 209.04MB, increase: 4.01MB
2025-10-9 14:30:22-debug: 查询 Asset Bundle start, progress: 5%
2025-10-9 14:30:22-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:30:22-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-10-9 14:30:22-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-10-9 14:30:22-debug: [Build Memory track]: 查询 Asset Bundle start:209.07MB, end 209.44MB, increase: 378.30KB
2025-10-9 14:30:22-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-9 14:30:22-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:30:22-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-10-9 14:30:22-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:209.47MB, end 209.49MB, increase: 17.09KB
2025-10-9 14:30:22-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-9 14:30:22-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-9 14:30:22-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-10-9 14:30:22-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-10-9 14:30:22-debug: [Build Memory track]: 填充脚本数据到 settings.json start:209.51MB, end 209.54MB, increase: 25.44KB
2025-10-9 14:30:22-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-9 14:30:22-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:30:22-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-10-9 14:30:22-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-10-9 14:30:22-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:209.57MB, end 209.89MB, increase: 327.74KB
2025-10-9 14:30:56-debug: refresh db internal success
2025-10-9 14:30:56-debug: refresh db i18n success
2025-10-9 14:30:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\PlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:30:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\event\EventGroupCom.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:30:56-debug: refresh db assets success
2025-10-9 14:30:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:30:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:30:56-debug: asset-db:refresh-all-database (181ms)
2025-10-9 14:30:56-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-10-9 14:30:58-debug: Query all assets info in project
2025-10-9 14:30:58-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-9 14:30:58-debug: Skip compress image, progress: 0%
2025-10-9 14:30:58-debug: Init all bundles start..., progress: 0%
2025-10-9 14:30:58-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:30:58-debug: Num of bundles: 19..., progress: 0%
2025-10-9 14:30:58-debug: 查询 Asset Bundle start, progress: 0%
2025-10-9 14:30:58-debug: Init bundle root assets start..., progress: 0%
2025-10-9 14:30:58-debug:   Number of all scenes: 11
2025-10-9 14:30:58-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-9 14:30:58-debug: Init bundle root assets success..., progress: 0%
2025-10-9 14:30:58-debug:   Number of other assets: 2659
2025-10-9 14:30:58-debug:   Number of all scripts: 313
2025-10-9 14:30:58-debug: // ---- build task 查询 Asset Bundle ---- (25ms)
2025-10-9 14:30:58-log: run build task 查询 Asset Bundle success in 25 ms√, progress: 5%
2025-10-9 14:30:58-debug: 查询 Asset Bundle start, progress: 5%
2025-10-9 14:30:58-debug: [Build Memory track]: 查询 Asset Bundle start:214.62MB, end 212.71MB, increase: -1953.63KB
2025-10-9 14:30:58-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:30:58-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-10-9 14:30:58-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-10-9 14:30:58-debug: [Build Memory track]: 查询 Asset Bundle start:212.74MB, end 213.11MB, increase: 377.25KB
2025-10-9 14:30:58-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-9 14:30:58-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:30:58-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-10-9 14:30:58-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-10-9 14:30:58-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-9 14:30:58-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:213.14MB, end 213.17MB, increase: 27.23KB
2025-10-9 14:30:58-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-9 14:30:58-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-10-9 14:30:58-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-10-9 14:30:58-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-9 14:30:58-debug: [Build Memory track]: 填充脚本数据到 settings.json start:213.19MB, end 213.22MB, increase: 25.22KB
2025-10-9 14:30:58-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:30:58-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-10-9 14:30:58-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:213.25MB, end 213.56MB, increase: 322.55KB
2025-10-9 14:30:58-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-10-9 14:31:02-debug: refresh db internal success
2025-10-9 14:31:02-debug: refresh db i18n success
2025-10-9 14:31:02-debug: refresh db assets success
2025-10-9 14:31:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:31:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:31:02-debug: asset-db:refresh-all-database (141ms)
2025-10-9 14:31:02-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 14:31:02-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 14:31:17-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\plane\enemyplane\100005.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:31:17-debug: asset-db:reimport-assetf3918511-c282-4cae-87ca-d509f333186e (3ms)
2025-10-9 14:31:19-debug: Query all assets info in project
2025-10-9 14:31:19-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-9 14:31:19-debug: Skip compress image, progress: 0%
2025-10-9 14:31:19-debug: Init all bundles start..., progress: 0%
2025-10-9 14:31:19-debug: Num of bundles: 19..., progress: 0%
2025-10-9 14:31:19-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:31:19-debug: 查询 Asset Bundle start, progress: 0%
2025-10-9 14:31:19-debug: Init bundle root assets start..., progress: 0%
2025-10-9 14:31:19-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-9 14:31:19-debug:   Number of all scenes: 11
2025-10-9 14:31:19-debug:   Number of other assets: 2659
2025-10-9 14:31:19-debug: Init bundle root assets success..., progress: 0%
2025-10-9 14:31:19-debug:   Number of all scripts: 313
2025-10-9 14:31:19-debug: // ---- build task 查询 Asset Bundle ---- (24ms)
2025-10-9 14:31:19-log: run build task 查询 Asset Bundle success in 24 ms√, progress: 5%
2025-10-9 14:31:19-debug: [Build Memory track]: 查询 Asset Bundle start:221.59MB, end 213.72MB, increase: -8062.17KB
2025-10-9 14:31:19-debug: 查询 Asset Bundle start, progress: 5%
2025-10-9 14:31:19-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:31:19-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-10-9 14:31:19-debug: [Build Memory track]: 查询 Asset Bundle start:213.75MB, end 214.12MB, increase: 376.29KB
2025-10-9 14:31:19-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-10-9 14:31:19-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-9 14:31:19-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:31:19-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-10-9 14:31:19-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-9 14:31:19-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-10-9 14:31:19-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:214.15MB, end 214.17MB, increase: 26.30KB
2025-10-9 14:31:19-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-9 14:31:19-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-10-9 14:31:19-debug: [Build Memory track]: 填充脚本数据到 settings.json start:214.20MB, end 214.23MB, increase: 25.87KB
2025-10-9 14:31:19-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-10-9 14:31:19-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-9 14:31:19-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:31:19-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-10-9 14:31:19-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:214.25MB, end 214.57MB, increase: 322.63KB
2025-10-9 14:31:19-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-10-9 14:31:34-debug: refresh db internal success
2025-10-9 14:31:34-debug: refresh db i18n success
2025-10-9 14:31:34-debug: refresh db assets success
2025-10-9 14:31:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:31:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:31:34-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 14:31:34-debug: asset-db:refresh-all-database (181ms)
2025-10-9 14:31:34-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 14:31:39-debug: refresh db internal success
2025-10-9 14:31:39-debug: refresh db i18n success
2025-10-9 14:31:39-debug: refresh db assets success
2025-10-9 14:31:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:31:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:31:39-debug: asset-db:refresh-all-database (172ms)
2025-10-9 14:31:39-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-10-9 14:31:39-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 14:32:28-debug: refresh db internal success
2025-10-9 14:32:28-debug: refresh db i18n success
2025-10-9 14:32:28-debug: %cImport%c: E:\M2Game\Client\assets\editor\plane\PlaneEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:32:28-debug: refresh db assets success
2025-10-9 14:32:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:32:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:32:28-debug: asset-db:refresh-all-database (191ms)
2025-10-9 14:32:30-debug: Query all assets info in project
2025-10-9 14:32:30-debug: Skip compress image, progress: 0%
2025-10-9 14:32:30-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-9 14:32:30-debug: Init all bundles start..., progress: 0%
2025-10-9 14:32:30-debug: Num of bundles: 19..., progress: 0%
2025-10-9 14:32:30-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:32:30-debug: 查询 Asset Bundle start, progress: 0%
2025-10-9 14:32:30-debug: Init bundle root assets start..., progress: 0%
2025-10-9 14:32:30-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-9 14:32:30-debug:   Number of all scenes: 11
2025-10-9 14:32:30-debug:   Number of other assets: 2659
2025-10-9 14:32:30-debug: Init bundle root assets success..., progress: 0%
2025-10-9 14:32:30-debug:   Number of all scripts: 313
2025-10-9 14:32:30-debug: // ---- build task 查询 Asset Bundle ---- (26ms)
2025-10-9 14:32:30-log: run build task 查询 Asset Bundle success in 26 ms√, progress: 5%
2025-10-9 14:32:30-debug: [Build Memory track]: 查询 Asset Bundle start:212.74MB, end 210.22MB, increase: -2573.16KB
2025-10-9 14:32:30-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:32:30-debug: 查询 Asset Bundle start, progress: 5%
2025-10-9 14:32:30-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-10-9 14:32:30-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-10-9 14:32:30-debug: [Build Memory track]: 查询 Asset Bundle start:210.25MB, end 210.63MB, increase: 383.02KB
2025-10-9 14:32:30-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-9 14:32:30-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:32:30-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:210.66MB, end 210.67MB, increase: 17.09KB
2025-10-9 14:32:30-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-10-9 14:32:30-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-9 14:32:30-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-9 14:32:30-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-10-9 14:32:30-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-9 14:32:30-debug: [Build Memory track]: 填充脚本数据到 settings.json start:210.71MB, end 210.73MB, increase: 16.43KB
2025-10-9 14:32:30-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:32:30-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-10-9 14:32:30-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:210.76MB, end 211.08MB, increase: 326.35KB
2025-10-9 14:32:30-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-10-9 14:33:16-debug: refresh db internal success
2025-10-9 14:33:16-debug: refresh db i18n success
2025-10-9 14:33:16-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:33:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:33:16-debug: refresh db assets success
2025-10-9 14:33:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:33:16-debug: asset-db:refresh-all-database (182ms)
2025-10-9 14:33:17-debug: Query all assets info in project
2025-10-9 14:33:17-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-9 14:33:17-debug: Skip compress image, progress: 0%
2025-10-9 14:33:17-debug: Init all bundles start..., progress: 0%
2025-10-9 14:33:17-debug: Num of bundles: 19..., progress: 0%
2025-10-9 14:33:17-debug: 查询 Asset Bundle start, progress: 0%
2025-10-9 14:33:17-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:33:17-debug: Init bundle root assets start..., progress: 0%
2025-10-9 14:33:17-debug:   Number of all scenes: 11
2025-10-9 14:33:17-debug:   Number of other assets: 2659
2025-10-9 14:33:17-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-9 14:33:17-debug:   Number of all scripts: 313
2025-10-9 14:33:17-debug: Init bundle root assets success..., progress: 0%
2025-10-9 14:33:17-debug: // ---- build task 查询 Asset Bundle ---- (24ms)
2025-10-9 14:33:17-debug: [Build Memory track]: 查询 Asset Bundle start:218.84MB, end 216.73MB, increase: -2160.35KB
2025-10-9 14:33:17-debug: 查询 Asset Bundle start, progress: 5%
2025-10-9 14:33:17-log: run build task 查询 Asset Bundle success in 24 ms√, progress: 5%
2025-10-9 14:33:17-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:33:17-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-10-9 14:33:17-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-10-9 14:33:17-debug: [Build Memory track]: 查询 Asset Bundle start:216.76MB, end 217.13MB, increase: 377.59KB
2025-10-9 14:33:17-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-9 14:33:17-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:33:17-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (-1ms)
2025-10-9 14:33:17-log: run build task 整理部分构建选项内数据到 settings.json success in -1 h -1 min -1 s√, progress: 12%
2025-10-9 14:33:17-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:217.16MB, end 217.18MB, increase: 26.63KB
2025-10-9 14:33:17-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-9 14:33:17-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-9 14:33:17-debug: // ---- build task 填充脚本数据到 settings.json ---- (-1ms)
2025-10-9 14:33:17-log: run build task 填充脚本数据到 settings.json success in -1 h -1 min -1 s√, progress: 13%
2025-10-9 14:33:17-debug: [Build Memory track]: 填充脚本数据到 settings.json start:217.21MB, end 217.24MB, increase: 26.26KB
2025-10-9 14:33:17-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-9 14:33:17-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:33:17-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-10-9 14:33:18-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-10-9 14:33:18-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:217.27MB, end 217.58MB, increase: 322.60KB
2025-10-9 14:33:56-debug: refresh db internal success
2025-10-9 14:33:56-debug: refresh db i18n success
2025-10-9 14:33:56-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:33:57-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\weapon\Weapon.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:33:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:33:57-debug: refresh db assets success
2025-10-9 14:33:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:33:57-debug: asset-db:refresh-all-database (183ms)
2025-10-9 14:33:59-debug: Query all assets info in project
2025-10-9 14:33:59-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-9 14:33:59-debug: Skip compress image, progress: 0%
2025-10-9 14:33:59-debug: Init all bundles start..., progress: 0%
2025-10-9 14:33:59-debug: 查询 Asset Bundle start, progress: 0%
2025-10-9 14:33:59-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:33:59-debug: Num of bundles: 19..., progress: 0%
2025-10-9 14:33:59-debug: Init bundle root assets start..., progress: 0%
2025-10-9 14:33:59-debug:   Number of all scripts: 313
2025-10-9 14:33:59-debug:   Number of other assets: 2659
2025-10-9 14:33:59-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-9 14:33:59-debug: Init bundle root assets success..., progress: 0%
2025-10-9 14:33:59-debug:   Number of all scenes: 11
2025-10-9 14:33:59-log: run build task 查询 Asset Bundle success in 25 ms√, progress: 5%
2025-10-9 14:33:59-debug: [Build Memory track]: 查询 Asset Bundle start:205.07MB, end 209.19MB, increase: 4.13MB
2025-10-9 14:33:59-debug: 查询 Asset Bundle start, progress: 5%
2025-10-9 14:33:59-debug: // ---- build task 查询 Asset Bundle ---- (25ms)
2025-10-9 14:33:59-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:33:59-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-10-9 14:33:59-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-10-9 14:33:59-debug: [Build Memory track]: 查询 Asset Bundle start:209.22MB, end 209.59MB, increase: 377.80KB
2025-10-9 14:33:59-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-9 14:33:59-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:33:59-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-10-9 14:33:59-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:209.62MB, end 209.63MB, increase: 16.86KB
2025-10-9 14:33:59-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-9 14:33:59-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-9 14:33:59-debug: [Build Memory track]: 填充脚本数据到 settings.json start:209.66MB, end 209.69MB, increase: 26.08KB
2025-10-9 14:33:59-log: run build task 填充脚本数据到 settings.json success in -1 h -1 min -1 s√, progress: 13%
2025-10-9 14:33:59-debug: // ---- build task 填充脚本数据到 settings.json ---- (-1ms)
2025-10-9 14:33:59-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-9 14:33:59-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:33:59-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-10-9 14:33:59-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-10-9 14:33:59-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:209.72MB, end 210.04MB, increase: 327.03KB
2025-10-9 14:34:26-debug: refresh db internal success
2025-10-9 14:34:26-debug: refresh db i18n success
2025-10-9 14:34:26-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:34:26-debug: refresh db assets success
2025-10-9 14:34:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:34:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:34:26-debug: asset-db:refresh-all-database (184ms)
2025-10-9 14:34:26-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 14:34:26-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 14:34:28-debug: Query all assets info in project
2025-10-9 14:34:28-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-9 14:34:28-debug: Skip compress image, progress: 0%
2025-10-9 14:34:28-debug: Init all bundles start..., progress: 0%
2025-10-9 14:34:28-debug: Num of bundles: 19..., progress: 0%
2025-10-9 14:34:28-debug: 查询 Asset Bundle start, progress: 0%
2025-10-9 14:34:28-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:34:28-debug: Init bundle root assets start..., progress: 0%
2025-10-9 14:34:28-debug:   Number of all scenes: 11
2025-10-9 14:34:28-debug:   Number of all scripts: 313
2025-10-9 14:34:28-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-9 14:34:28-debug:   Number of other assets: 2659
2025-10-9 14:34:28-debug: Init bundle root assets success..., progress: 0%
2025-10-9 14:34:28-debug: // ---- build task 查询 Asset Bundle ---- (23ms)
2025-10-9 14:34:28-log: run build task 查询 Asset Bundle success in 23 ms√, progress: 5%
2025-10-9 14:34:28-debug: [Build Memory track]: 查询 Asset Bundle start:215.55MB, end 212.32MB, increase: -3308.88KB
2025-10-9 14:34:28-debug: 查询 Asset Bundle start, progress: 5%
2025-10-9 14:34:28-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:34:28-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-10-9 14:34:28-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-10-9 14:34:28-debug: [Build Memory track]: 查询 Asset Bundle start:212.35MB, end 212.71MB, increase: 377.91KB
2025-10-9 14:34:28-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:34:28-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-9 14:34:28-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-10-9 14:34:28-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-9 14:34:28-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-10-9 14:34:28-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:212.75MB, end 212.77MB, increase: 26.39KB
2025-10-9 14:34:28-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-9 14:34:28-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-10-9 14:34:28-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-10-9 14:34:28-debug: [Build Memory track]: 填充脚本数据到 settings.json start:212.80MB, end 212.83MB, increase: 25.80KB
2025-10-9 14:34:28-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-9 14:34:28-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:34:28-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-10-9 14:34:28-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-10-9 14:34:28-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:212.85MB, end 213.17MB, increase: 322.77KB
2025-10-9 14:35:12-debug: refresh db internal success
2025-10-9 14:35:12-debug: refresh db i18n success
2025-10-9 14:35:12-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:35:12-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:35:12-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\weapon\Weapon.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:35:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:35:12-debug: refresh db assets success
2025-10-9 14:35:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:35:12-debug: asset-db:refresh-all-database (187ms)
2025-10-9 14:35:12-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 14:35:12-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 14:35:14-debug: Query all assets info in project
2025-10-9 14:35:14-debug: Skip compress image, progress: 0%
2025-10-9 14:35:14-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-9 14:35:14-debug: Num of bundles: 19..., progress: 0%
2025-10-9 14:35:14-debug: 查询 Asset Bundle start, progress: 0%
2025-10-9 14:35:14-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:35:14-debug: Init all bundles start..., progress: 0%
2025-10-9 14:35:14-debug: Init bundle root assets start..., progress: 0%
2025-10-9 14:35:14-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-9 14:35:14-debug:   Number of all scripts: 313
2025-10-9 14:35:14-debug:   Number of other assets: 2659
2025-10-9 14:35:14-debug: Init bundle root assets success..., progress: 0%
2025-10-9 14:35:14-debug:   Number of all scenes: 11
2025-10-9 14:35:14-debug: // ---- build task 查询 Asset Bundle ---- (25ms)
2025-10-9 14:35:14-log: run build task 查询 Asset Bundle success in 25 ms√, progress: 5%
2025-10-9 14:35:14-debug: [Build Memory track]: 查询 Asset Bundle start:221.45MB, end 219.56MB, increase: -1926.11KB
2025-10-9 14:35:14-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:35:14-debug: 查询 Asset Bundle start, progress: 5%
2025-10-9 14:35:14-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-10-9 14:35:14-debug: [Build Memory track]: 查询 Asset Bundle start:219.59MB, end 219.96MB, increase: 378.16KB
2025-10-9 14:35:14-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-10-9 14:35:14-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-9 14:35:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:35:14-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-9 14:35:14-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-10-9 14:35:14-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:219.99MB, end 220.01MB, increase: 17.09KB
2025-10-9 14:35:14-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-9 14:35:14-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-10-9 14:35:14-debug: [Build Memory track]: 填充脚本数据到 settings.json start:220.04MB, end 220.05MB, increase: 16.14KB
2025-10-9 14:35:14-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-9 14:35:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:35:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-10-9 14:35:14-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-10-9 14:35:14-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:220.08MB, end 220.39MB, increase: 321.90KB
2025-10-9 14:36:05-debug: refresh db internal success
2025-10-9 14:36:05-debug: refresh db i18n success
2025-10-9 14:36:05-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:36:05-debug: refresh db assets success
2025-10-9 14:36:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:36:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:36:05-debug: asset-db:refresh-all-database (186ms)
2025-10-9 14:36:05-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 14:36:05-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 14:36:06-debug: Query all assets info in project
2025-10-9 14:36:06-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-9 14:36:06-debug: Skip compress image, progress: 0%
2025-10-9 14:36:06-debug: Num of bundles: 19..., progress: 0%
2025-10-9 14:36:06-debug: Init all bundles start..., progress: 0%
2025-10-9 14:36:06-debug: 查询 Asset Bundle start, progress: 0%
2025-10-9 14:36:06-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:36:06-debug: Init bundle root assets start..., progress: 0%
2025-10-9 14:36:07-debug:   Number of all scripts: 313
2025-10-9 14:36:06-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-9 14:36:07-debug:   Number of other assets: 2659
2025-10-9 14:36:07-debug: Init bundle root assets success..., progress: 0%
2025-10-9 14:36:07-debug:   Number of all scenes: 11
2025-10-9 14:36:07-debug: // ---- build task 查询 Asset Bundle ---- (26ms)
2025-10-9 14:36:07-debug: 查询 Asset Bundle start, progress: 5%
2025-10-9 14:36:07-log: run build task 查询 Asset Bundle success in 26 ms√, progress: 5%
2025-10-9 14:36:07-debug: [Build Memory track]: 查询 Asset Bundle start:209.01MB, end 206.21MB, increase: -2867.71KB
2025-10-9 14:36:07-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:36:07-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-10-9 14:36:07-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-10-9 14:36:07-debug: [Build Memory track]: 查询 Asset Bundle start:206.24MB, end 206.61MB, increase: 383.77KB
2025-10-9 14:36:07-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-9 14:36:07-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:36:07-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-10-9 14:36:07-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-10-9 14:36:07-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:206.64MB, end 206.66MB, increase: 26.37KB
2025-10-9 14:36:07-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-9 14:36:07-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-9 14:36:07-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-10-9 14:36:07-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-10-9 14:36:07-debug: [Build Memory track]: 填充脚本数据到 settings.json start:206.69MB, end 206.72MB, increase: 25.87KB
2025-10-9 14:36:07-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-9 14:36:07-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:36:07-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-10-9 14:36:07-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:206.75MB, end 207.07MB, increase: 327.38KB
2025-10-9 14:36:07-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-10-9 14:37:17-debug: refresh db internal success
2025-10-9 14:37:17-debug: refresh db i18n success
2025-10-9 14:37:17-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:37:17-debug: refresh db assets success
2025-10-9 14:37:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:37:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:37:17-debug: asset-db:refresh-all-database (181ms)
2025-10-9 14:39:14-debug: refresh db internal success
2025-10-9 14:39:14-debug: refresh db i18n success
2025-10-9 14:39:14-debug: %cImport%c: E:\M2Game\Client\assets\editor\plane\PlaneEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:39:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:39:14-debug: refresh db assets success
2025-10-9 14:39:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:39:14-debug: asset-db:refresh-all-database (209ms)
2025-10-9 14:39:16-debug: refresh db internal success
2025-10-9 14:39:16-debug: refresh db i18n success
2025-10-9 14:39:16-debug: Query all assets info in project
2025-10-9 14:39:16-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-9 14:39:16-debug: Skip compress image, progress: 0%
2025-10-9 14:39:16-debug: Init all bundles start..., progress: 0%
2025-10-9 14:39:16-debug: 查询 Asset Bundle start, progress: 0%
2025-10-9 14:39:16-debug: Num of bundles: 19..., progress: 0%
2025-10-9 14:39:16-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:39:16-debug: Init bundle root assets start..., progress: 0%
2025-10-9 14:39:16-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-9 14:39:16-debug:   Number of all scenes: 11
2025-10-9 14:39:16-debug:   Number of all scripts: 313
2025-10-9 14:39:16-debug:   Number of other assets: 2659
2025-10-9 14:39:16-debug: Init bundle root assets success..., progress: 0%
2025-10-9 14:39:16-debug: [Build Memory track]: 查询 Asset Bundle start:218.74MB, end 220.54MB, increase: 1.79MB
2025-10-9 14:39:16-log: run build task 查询 Asset Bundle success in 26 ms√, progress: 5%
2025-10-9 14:39:16-debug: 查询 Asset Bundle start, progress: 5%
2025-10-9 14:39:16-debug: // ---- build task 查询 Asset Bundle ---- (26ms)
2025-10-9 14:39:16-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:39:16-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-10-9 14:39:16-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-10-9 14:39:16-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-9 14:39:16-debug: [Build Memory track]: 查询 Asset Bundle start:220.56MB, end 221.04MB, increase: 491.03KB
2025-10-9 14:39:16-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:39:16-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-9 14:39:16-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-10-9 14:39:16-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:221.07MB, end 221.11MB, increase: 41.14KB
2025-10-9 14:39:16-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-9 14:39:16-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-10-9 14:39:16-debug: [Build Memory track]: 填充脚本数据到 settings.json start:221.14MB, end 221.25MB, increase: 110.12KB
2025-10-9 14:39:16-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-9 14:39:16-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:39:16-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (6ms)
2025-10-9 14:39:16-log: run build task 整理部分构建选项内数据到 settings.json success in 6 ms√, progress: 15%
2025-10-9 14:39:16-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:221.28MB, end 222.12MB, increase: 859.91KB
2025-10-9 14:39:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:39:16-debug: refresh db assets success
2025-10-9 14:39:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:39:16-debug: asset-db:refresh-all-database (223ms)
2025-10-9 14:39:30-debug: refresh db internal success
2025-10-9 14:39:30-debug: refresh db i18n success
2025-10-9 14:39:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:39:30-debug: refresh db assets success
2025-10-9 14:39:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:39:30-debug: asset-db:refresh-all-database (166ms)
2025-10-9 14:39:49-debug: refresh db internal success
2025-10-9 14:39:49-debug: refresh db i18n success
2025-10-9 14:39:49-debug: %cImport%c: E:\M2Game\Client\assets\editor\plane\PlaneEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:39:49-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:39:49-debug: refresh db assets success
2025-10-9 14:39:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:39:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:39:49-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 14:39:49-debug: asset-db:refresh-all-database (162ms)
2025-10-9 14:39:49-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 14:39:55-debug: %cImport%c: E:\M2Game\Client\assets\editor\PlaneEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:39:55-debug: asset-db:reimport-assetd90c1d4b-98f0-42eb-8111-dacc86636312 (3ms)
2025-10-9 14:40:00-debug: %cImport%c: E:\M2Game\Client\assets\editor\PlaneEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:40:00-debug: asset-db:reimport-assetd90c1d4b-98f0-42eb-8111-dacc86636312 (2ms)
2025-10-9 14:40:01-debug: Query all assets info in project
2025-10-9 14:40:01-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-9 14:40:01-debug: Skip compress image, progress: 0%
2025-10-9 14:40:01-debug: Init all bundles start..., progress: 0%
2025-10-9 14:40:01-debug: Num of bundles: 19..., progress: 0%
2025-10-9 14:40:01-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:40:01-debug: Init bundle root assets start..., progress: 0%
2025-10-9 14:40:01-debug: 查询 Asset Bundle start, progress: 0%
2025-10-9 14:40:01-debug:   Number of all scenes: 11
2025-10-9 14:40:01-debug:   Number of all scripts: 313
2025-10-9 14:40:01-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-9 14:40:01-debug: Init bundle root assets success..., progress: 0%
2025-10-9 14:40:01-debug:   Number of other assets: 2659
2025-10-9 14:40:01-log: run build task 查询 Asset Bundle success in 24 ms√, progress: 5%
2025-10-9 14:40:01-debug: // ---- build task 查询 Asset Bundle ---- (24ms)
2025-10-9 14:40:01-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 14:40:01-debug: 查询 Asset Bundle start, progress: 5%
2025-10-9 14:40:01-debug: [Build Memory track]: 查询 Asset Bundle start:214.36MB, end 213.40MB, increase: -978.14KB
2025-10-9 14:40:01-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-10-9 14:40:01-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-10-9 14:40:01-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:40:01-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-9 14:40:01-debug: [Build Memory track]: 查询 Asset Bundle start:213.43MB, end 213.80MB, increase: 378.09KB
2025-10-9 14:40:01-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-10-9 14:40:01-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:213.83MB, end 213.85MB, increase: 16.89KB
2025-10-9 14:40:01-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-9 14:40:01-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-9 14:40:01-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-10-9 14:40:01-debug: [Build Memory track]: 填充脚本数据到 settings.json start:213.87MB, end 213.89MB, increase: 16.90KB
2025-10-9 14:40:01-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-9 14:40:01-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 14:40:01-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-10-9 14:40:01-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-10-9 14:40:01-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:213.92MB, end 214.24MB, increase: 325.53KB
2025-10-9 14:44:05-debug: refresh db internal success
2025-10-9 14:44:05-debug: refresh db i18n success
2025-10-9 14:44:05-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:44:05-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbreschapter.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:44:05-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbreslevelgroup.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:44:05-debug: %cImport%c: E:\M2Game\Client\assets\bundles\luban\tbreslevel.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:44:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:44:05-debug: refresh db assets success
2025-10-9 14:44:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:44:05-debug: asset-db:refresh-all-database (186ms)
2025-10-9 14:44:05-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 14:44:05-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 14:44:47-debug: refresh db internal success
2025-10-9 14:44:47-debug: refresh db i18n success
2025-10-9 14:44:47-debug: refresh db assets success
2025-10-9 14:44:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:44:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:44:47-debug: asset-db:refresh-all-database (187ms)
2025-10-9 14:44:58-debug: refresh db internal success
2025-10-9 14:44:58-debug: refresh db i18n success
2025-10-9 14:44:58-debug: refresh db assets success
2025-10-9 14:44:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:44:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:44:58-debug: asset-db:refresh-all-database (190ms)
2025-10-9 14:45:15-debug: refresh db internal success
2025-10-9 14:45:15-debug: refresh db i18n success
2025-10-9 14:45:15-debug: refresh db assets success
2025-10-9 14:45:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:45:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:45:15-debug: asset-db:refresh-all-database (143ms)
2025-10-9 14:45:15-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 14:45:26-warn: Error: pngload: libspng read error
2025-10-9 14:45:26-warn: resizeThumbnail failed for db://assets/resources/game/texture/mainPlane/mainBullet.png@f9941
2025-10-9 14:46:18-debug: refresh db internal success
2025-10-9 14:46:18-debug: refresh db i18n success
2025-10-9 14:46:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\mainPlane\MainPlane.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 14:46:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 14:46:18-debug: refresh db assets success
2025-10-9 14:46:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 14:46:18-debug: asset-db:refresh-all-database (184ms)
2025-10-9 14:46:18-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 14:46:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)

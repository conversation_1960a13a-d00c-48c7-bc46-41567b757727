import { _decorator } from "cc";
import { FnOnLateUpdate, FnOnUpdate, IMgr } from "../../../../scripts/core/base/IMgr";
import { ResManager } from "../../../../scripts/core/base/ResManager";
import { audioManager } from '../audio/audioManager';
import * as cfg from '../autogen/luban/schema';
import { GlobalDataManager } from "../game/manager/GlobalDataManager";
import { LubanMgr } from "../luban/LubanMgr";
import { NetMgr } from "../network/NetMgr";
import { PlaneManager } from "../plane/PlaneManager";
import { CreateLoginSDK, IPlatformSDK } from "../platformsdk/IPlatformSDK";

const { ccclass } = _decorator;

@ccclass("MyApp")
export class MyApp {
    private static _instance: MyApp | null = null;
    public static GetInstance(): MyApp {
        if (!MyApp._instance) {
            MyApp._instance = new MyApp();
        }
        return MyApp._instance!;
    }
    private ManagerPool: IMgr[] = [];
    private _updateContainer: FnOnUpdate[] = [];
    private _lateUpdateContainer: FnOnLateUpdate[] = [];

    private _lubanMgr: LubanMgr | null = null;
    private _netMgr: NetMgr | null = null;
    private _resMgr: ResManager | null = null;
    private _platformSDK: IPlatformSDK | null = null;
    private _globalDataManager: GlobalDataManager | null = null;
    private _planeManager: PlaneManager | null = null;
    private _audioManager: audioManager | null = null;

    init() {
        this.ManagerPool.push(audioManager.instance);
        this._lubanMgr = new LubanMgr();
        this.ManagerPool.push(this._lubanMgr);
        this._resMgr = ResManager.instance;
        this.ManagerPool.push(this._resMgr);
        this._platformSDK = CreateLoginSDK();
        this._netMgr = new NetMgr(this._platformSDK);
        this.ManagerPool.push(this._netMgr);
        this._globalDataManager = new GlobalDataManager();
        this.ManagerPool.push(this._globalDataManager);
        this._planeManager = new PlaneManager();
        this._audioManager = audioManager.instance

        this.ManagerPool.forEach(manager => {
            manager.init();
            this._updateContainer.push(manager.onUpdate.bind(manager));
            this._lateUpdateContainer.push(manager.onLateUpdate.bind(manager));
        });
    }
    update(deltaTime: number): void {
        for (let i = 0; i < this._updateContainer.length; i++) {
            this._updateContainer[i](deltaTime);
        }
    }

    lateUpdate(): void {
        for (let i = 0; i < this._lateUpdateContainer.length; i++) {
            this._lateUpdateContainer[i]();
        }
    }

    static get netMgr(): NetMgr {
        return MyApp.GetInstance()._netMgr!;
    }
    static get lubanMgr(): LubanMgr {
        return MyApp.GetInstance()._lubanMgr!;
    }
    static get lubanTables(): cfg.Tables {
        return MyApp.GetInstance()._lubanMgr!.table;
    }
    static get platformSDK(): IPlatformSDK {
        return MyApp.GetInstance()._platformSDK!;
    }
    static get resMgr(): ResManager {
        return MyApp.GetInstance()._resMgr!;
    }
    static get globalMgr(): GlobalDataManager {
        return MyApp.GetInstance()._globalDataManager!;
    }
    static get planeMgr(): PlaneManager {
        return MyApp.GetInstance()._planeManager!;
    }
    static get audioMgr(): audioManager {
        return MyApp.GetInstance()._audioManager!;
    }
}
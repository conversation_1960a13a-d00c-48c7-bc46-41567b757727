2025-10-9 10:20:08 - log: Load engine in C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine
2025-10-9 10:20:08 - log: Register native engine in C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\native
2025-10-9 10:20:09 - log: Request namespace: device-list
2025-10-9 10:20:12 - log: emitter-editor extension loaded
2025-10-9 10:20:12 - log: Available methods: [
  'movePlayerUp',
  'movePlayerDown',
  'movePlayerLeft',
  'movePlayerRight',
  'onAssetChanged',
  'createEmitterEnum',
  'createEnemyEnum'
]
2025-10-9 10:20:12 - log: EmitterEnum.ts generated successfully at: E:\M2Game\Client\assets\editor\enum-gen\EmitterEnum.ts
2025-10-9 10:20:12 - log: EnemyEnum.ts generated successfully at: E:\M2Game\Client\assets\editor\enum-gen\EnemyEnum.ts
2025-10-9 10:20:21 - log: [Scene] meshopt wasm decoder initialized
2025-10-9 10:20:21 - log: [Scene] [bullet]:bullet wasm lib loaded.
2025-10-9 10:20:21 - log: [Scene] [box2d]:box2d wasm lib loaded.
2025-10-9 10:20:21 - log: [Scene] [PHYSICS]: using builtin.
2025-10-9 10:20:21 - log: [Scene] Cocos Creator v3.8.6
2025-10-9 10:20:24 - log: [Scene] start init_cs_proto.js
2025-10-9 10:20:24 - log: [Scene] Using custom pipeline: Builtin
2025-10-9 10:20:24 - log: [Scene] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-10-9 10:21:07 - log: [Scene] LevelEditorBaseUI start.
2025-10-9 10:21:07 - log: [Scene] LevelEditorBaseUI start 62oMpiqNVJeLfnCzN4mnAI
2025-10-9 10:21:07 - log: [Scene] GizmoManager: Registered drawer EmitterGizmo
2025-10-9 10:21:07 - log: [Scene] LevelUI start
2025-10-9 10:21:07 - log: [Scene] LevelEditorUI set levelPrefabUUID 8e352073-9af1-4033-b6b6-f5db6fdccbc6
2025-10-9 10:21:07 - log: [Scene] LevelEditorLayerUI initByLevelData
2025-10-9 10:21:07 - log: [Scene] LevelEditorPrefabParse begin - 预制体:  RandNod_StartCloud02 uuid: b70143d5-a8b5-4c0f-8f7a-4c320af9b7d3
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Clouds_start02
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Cloudshadow-001
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Clouds_start01
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: NormalCloud3
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: NormalCloud5
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Clouds_start04
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Cloudshadow
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Clouds_start03
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: HeavyClouds02
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: NormalCloud4
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Clouds_start05
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Clouds_start06
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Clouds_start07
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: NormalCloud6
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Clouds_start08
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Clouds_start09
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] LevelEditorPrefabParse end - 预制体:  rand_0_0
2025-10-9 10:21:07 - log: [Scene] LevelEditorPrefabParse begin - 预制体:  RandNod_StartCloud01 uuid: 1f86a690-2793-4143-a94d-c4aa0099b0da
2025-10-9 10:21:07 - log: [Scene] LevelEditorPrefabParse end - 预制体:  rand_0_1
2025-10-9 10:21:07 - log: [Scene] LevelEditorPrefabParse begin - 预制体:  RandomNod_LayerClouds_01_short uuid: 1fcebabb-3d05-4213-bdd6-db00d1cfe6c1
2025-10-9 10:21:07 - log: [Scene] LevelEditorPrefabParse end - 预制体:  rand_0_1
2025-10-9 10:21:07 - log: [Scene] LevelEditorPrefabParse begin - 预制体:  RandomNod_LayerClouds_02_Long uuid: 5a73da98-80f4-47b3-9c5c-1c730af63ec9
2025-10-9 10:21:07 - log: [Scene] LevelEditorPrefabParse end - 预制体:  rand_0_2
2025-10-9 10:21:07 - log: [Scene] LevelEditorPrefabParse begin - 预制体:  RandNod_Island_01 uuid: df3ecd42-2e9e-4cdd-9e15-96b95bc775b8
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: BG_HightLand
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: BG_HightLand-001
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Node_Element_ToOcean
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Island6
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Island4
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Island5
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Island1
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Island2
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Island8
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Node_Element_ToOcean
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Node_Element_ToOcean-001
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Node_Node_Element_ToLand
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount5
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount2
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount3
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount4
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount7
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant05
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant02
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant03
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant04
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant06
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant07
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant08
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant09
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant10
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant11
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount2
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_lake3
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant05
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant12
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant13
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant14
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant15
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant16
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant17
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant18
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant19
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant20
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount5
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount6
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant01
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant21
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Island4
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Island3
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] LevelEditorPrefabParse end - 预制体:  rand_0_0
2025-10-9 10:21:07 - log: [Scene] LevelEditorPrefabParse begin - 预制体:  RandNod_Island_02 uuid: b0e825f0-cdc7-45d9-b5ba-15e1f24b6b59
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Node_Element_ToOcean
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Island2
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Island4
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Island5
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Island1
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Island6
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] LevelEditorPrefabParse end - 预制体:  rand_0_1
2025-10-9 10:21:07 - log: [Scene] LevelEditorPrefabParse begin - 预制体:  RandNod_Island_14 uuid: 532eda6d-8611-46b0-83fd-e7291470adf4
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: BG_HightLand
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Node_Element_ToOcean
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Node_Node_Element_ToLand
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_lake2
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Node_Element_ToOcean
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_lake3
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_lake1
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant07
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant05
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant04
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount1
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant03
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant06
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant08
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount8
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant01
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant09
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] LevelEditorPrefabParse end - 预制体:  rand_0_0
2025-10-9 10:21:07 - log: [Scene] LevelEditorPrefabParse begin - 预制体:  RandNod_Island_16 uuid: a7baef35-8451-4b4d-9104-8c09bedd8c8b
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: BG_HightLand
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: BG_HightLand-001
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: BG_HightLand-002
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Node_Element_ToOcean
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Node_Element_ToOcean-001
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Node_Element_ToLand2
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant01
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant02
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant03
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount7
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount9
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount3
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount4
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant06
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount8
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount6
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount10
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount2
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant04
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant05
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant07
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant08
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant09
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant10
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant11
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant12
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount5
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount1
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant13
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant14
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant15
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant16
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant17
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant18
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant19
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant20
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant21
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant22
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant23
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant24
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant25
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant26
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant09
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant28
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant29
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant05
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant30
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant31
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] LevelEditorPrefabParse end - 预制体:  rand_0_1
2025-10-9 10:21:07 - log: [Scene] LevelEditorPrefabParse begin - 预制体:  RandNod_Island_15 uuid: 9b114850-d2db-4403-a6c5-87cd709aa082
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: BG_HightLand
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: BG_HightLand-001
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: BG_HightLand-002
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: BG_HightLand-003
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Node_Element_ToOcean
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Node_Element_ToOcean-001
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Node_Element_ToLand2
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_lake2
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant01
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant02
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant03
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_lake3
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount7
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount9
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount3
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount1
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount4
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount5
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant06
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount8
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount6
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount10
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant04
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant05
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant07
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant08
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant09
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant10
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant11
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant12
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant13
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant14
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant15
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant16
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant17
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant18
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant19
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant20
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount2
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant21
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant22
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant23
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount11
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Element_Mount12
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant24
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant25
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant26
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant27
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant09
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant28
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant29
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] 该节点不是独立的预制体: Plant05
2025-10-9 10:21:07 - log: [Scene] sprite uuid: <EMAIL>
2025-10-9 10:21:07 - log: [Scene] LevelEditorPrefabParse end - 预制体:  rand_0_2
2025-10-9 10:21:07 - log: [Scene] LevelEditorLayerUI initByLevelData
2025-10-9 10:21:07 - log: [Scene] LevelEditorLayerUI initByLevelData
2025-10-9 10:21:07 - log: [Scene] LevelEditorLayerUI initByLevelData
2025-10-9 10:21:07 - log: [Scene] LevelEditorElemUI set layer LevelEditorEventUI
2025-10-9 10:21:07 - log: [Scene] LevelEditorElemUI set layer LevelEditorEventUI
2025-10-9 10:21:07 - log: [Scene] LevelEditorElemUI set layer LevelEditorEventUI
2025-10-9 10:21:07 - log: [Scene] LevelEditorElemUI set layer LevelEditorEventUI
2025-10-9 10:21:07 - log: [Scene] LevelEditorElemUI set layer LevelEditorEventUI
2025-10-9 10:21:07 - log: [Scene] LevelEditorElemUI set layer LevelEditorEventUI
2025-10-9 10:21:07 - log: [Scene] LevelEditorElemUI set layer LevelEditorEventUI
2025-10-9 10:21:07 - log: [Scene] LevelEditorElemUI set layer LevelEditorEventUI
2025-10-9 10:21:07 - log: [Scene] LevelEditorElemUI set layer LevelEditorEventUI
2025-10-9 10:21:07 - log: [Scene] LevelEditorElemUI set layer LevelEditorEventUI
2025-10-9 10:21:07 - log: [Scene] LevelEditorElemUI set layer LevelEditorEventUI
2025-10-9 10:21:07 - log: [Scene] LevelEditorElemUI set layer LevelEditorEventUI

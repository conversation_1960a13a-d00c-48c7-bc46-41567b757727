2025-10-9 14:12:15 - log: Load engine in C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine
2025-10-9 14:12:15 - log: Register native engine in C:\ProgramData\cocos\editors\Creator\3.8.6\resources\resources\3d\engine\native
2025-10-9 14:12:15 - log: Request namespace: device-list
2025-10-9 14:12:19 - log: emitter-editor extension loaded
2025-10-9 14:12:19 - log: Available methods: [
  'movePlayerUp',
  'movePlayerDown',
  'movePlayerLeft',
  'movePlayerRight',
  'onAssetChanged',
  'createEmitterEnum',
  'createEnemyEnum'
]
2025-10-9 14:12:19 - log: EmitterEnum.ts generated successfully at: E:\M2Game\Client\assets\editor\enum-gen\EmitterEnum.ts
2025-10-9 14:12:19 - log: EnemyEnum.ts generated successfully at: E:\M2Game\Client\assets\editor\enum-gen\EnemyEnum.ts
2025-10-9 14:12:20 - info: [PreviewInEditor] 预览环境初始化完毕
2025-10-9 14:12:29 - log: [Scene] meshopt wasm decoder initialized
2025-10-9 14:12:29 - log: [Scene] [bullet]:bullet wasm lib loaded.
2025-10-9 14:12:29 - log: [Scene] [box2d]:box2d wasm lib loaded.
2025-10-9 14:12:29 - log: [Scene] [PHYSICS]: using builtin.
2025-10-9 14:12:29 - log: [Scene] Cocos Creator v3.8.6
2025-10-9 14:12:32 - log: [Scene] start init_cs_proto.js
2025-10-9 14:12:32 - log: [Scene] Using custom pipeline: Builtin
2025-10-9 14:12:32 - log: [Scene] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-10-9 14:23:12 - log: [PreviewInEditor] meshopt wasm decoder initialized
2025-10-9 14:23:12 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
2025-10-9 14:23:12 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
2025-10-9 14:23:12 - log: [PreviewInEditor] [PHYSICS]: using builtin.
2025-10-9 14:23:12 - log: [PreviewInEditor] Cocos Creator v3.8.6
2025-10-9 14:23:13 - log: [PreviewInEditor] start init_cs_proto.js
2025-10-9 14:23:13 - log: [PreviewInEditor] Using custom pipeline: Builtin
2025-10-9 14:23:13 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-10-9 14:23:13 - log: [PreviewInEditor] [2025/10/09 14:23:13] [INFO] NetMgr Network manager initialized
2025-10-9 14:23:13 - log: [PreviewInEditor] [2025/10/09 14:23:13] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_SESSION
2025-10-9 14:23:13 - log: [PreviewInEditor] [2025/10/09 14:23:13] [INFO] NetMgr Registered handler for msgId: CS_CMD_HEARTBEAT
2025-10-9 14:23:13 - log: [PreviewInEditor] [2025/10/09 14:23:13] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_ROLE
2025-10-9 14:23:13 - log: [PreviewInEditor] Spine: Animation not found: Idle
2025-10-9 14:23:16 - warn: [PreviewInEditor] IPC message has been lost.Error: [PreviewInEditor] IPC message has been lost.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at IpcRenderer.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\ipc\web\webview.ccc:1:1495)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
2025-10-9 14:23:17 - info: [PreviewInEditor] 预览环境初始化完毕
2025-10-9 14:23:31 - log: [PreviewInEditor] meshopt wasm decoder initialized
2025-10-9 14:23:31 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
2025-10-9 14:23:31 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
2025-10-9 14:23:32 - log: [PreviewInEditor] [PHYSICS]: using builtin.
2025-10-9 14:23:32 - log: [PreviewInEditor] Cocos Creator v3.8.6
2025-10-9 14:23:32 - log: [PreviewInEditor] start init_cs_proto.js
2025-10-9 14:23:32 - log: [PreviewInEditor] Using custom pipeline: Builtin
2025-10-9 14:23:32 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-10-9 14:23:32 - log: [PreviewInEditor] [2025/10/09 14:23:32] [INFO] NetMgr Network manager initialized
2025-10-9 14:23:32 - log: [PreviewInEditor] [2025/10/09 14:23:32] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_SESSION
2025-10-9 14:23:32 - log: [PreviewInEditor] [2025/10/09 14:23:32] [INFO] NetMgr Registered handler for msgId: CS_CMD_HEARTBEAT
2025-10-9 14:23:32 - log: [PreviewInEditor] [2025/10/09 14:23:32] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_ROLE
2025-10-9 14:23:32 - log: [PreviewInEditor] Spine: Animation not found: Idle
2025-10-9 14:23:35 - warn: [PreviewInEditor] IPC message has been lost.Error: [PreviewInEditor] IPC message has been lost.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at IpcRenderer.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\ipc\web\webview.ccc:1:1495)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
2025-10-9 14:23:35 - info: [PreviewInEditor] 预览环境初始化完毕
2025-10-9 14:25:06 - log: [Scene] start init_cs_proto.js
2025-10-9 14:25:06 - log: [PreviewInEditor] meshopt wasm decoder initialized
2025-10-9 14:25:06 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
2025-10-9 14:25:06 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
2025-10-9 14:25:06 - log: [PreviewInEditor] [PHYSICS]: using builtin.
2025-10-9 14:25:06 - log: [PreviewInEditor] Cocos Creator v3.8.6
2025-10-9 14:25:07 - log: [PreviewInEditor] start init_cs_proto.js
2025-10-9 14:25:07 - log: [PreviewInEditor] Using custom pipeline: Builtin
2025-10-9 14:25:07 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-10-9 14:25:07 - log: [PreviewInEditor] [2025/10/09 14:25:07] [INFO] NetMgr Network manager initialized
2025-10-9 14:25:07 - log: [PreviewInEditor] [2025/10/09 14:25:07] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_SESSION
2025-10-9 14:25:07 - log: [PreviewInEditor] [2025/10/09 14:25:07] [INFO] NetMgr Registered handler for msgId: CS_CMD_HEARTBEAT
2025-10-9 14:25:07 - log: [PreviewInEditor] [2025/10/09 14:25:07] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_ROLE
2025-10-9 14:25:07 - log: [PreviewInEditor] Spine: Animation not found: Idle
2025-10-9 14:25:16 - warn: [PreviewInEditor] IPC message has been lost.Error: [PreviewInEditor] IPC message has been lost.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at IpcRenderer.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\ipc\web\webview.ccc:1:1495)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
2025-10-9 14:25:17 - info: [PreviewInEditor] 预览环境初始化完毕
2025-10-9 14:26:01 - log: [Scene] start init_cs_proto.js
2025-10-9 14:26:05 - log: [PreviewInEditor] meshopt wasm decoder initialized
2025-10-9 14:26:05 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
2025-10-9 14:26:05 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
2025-10-9 14:26:05 - log: [PreviewInEditor] [PHYSICS]: using builtin.
2025-10-9 14:26:06 - log: [PreviewInEditor] Cocos Creator v3.8.6
2025-10-9 14:26:06 - log: [PreviewInEditor] start init_cs_proto.js
2025-10-9 14:26:06 - log: [PreviewInEditor] Using custom pipeline: Builtin
2025-10-9 14:26:06 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-10-9 14:26:06 - log: [PreviewInEditor] [2025/10/09 14:26:06] [INFO] NetMgr Network manager initialized
2025-10-9 14:26:06 - log: [PreviewInEditor] [2025/10/09 14:26:06] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_SESSION
2025-10-9 14:26:06 - log: [PreviewInEditor] [2025/10/09 14:26:06] [INFO] NetMgr Registered handler for msgId: CS_CMD_HEARTBEAT
2025-10-9 14:26:06 - log: [PreviewInEditor] [2025/10/09 14:26:06] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_ROLE
2025-10-9 14:26:06 - log: [PreviewInEditor] Spine: Animation not found: Idle
2025-10-9 14:26:06 - log: [PreviewInEditor] this._enemyPrefabData:  null
2025-10-9 14:26:49 - log: [Scene] start init_cs_proto.js
2025-10-9 14:26:50 - info: [PreviewInEditor] 预览环境初始化完毕
2025-10-9 14:26:51 - log: [PreviewInEditor] meshopt wasm decoder initialized
2025-10-9 14:26:51 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
2025-10-9 14:26:51 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
2025-10-9 14:26:51 - log: [PreviewInEditor] [PHYSICS]: using builtin.
2025-10-9 14:26:51 - log: [PreviewInEditor] Cocos Creator v3.8.6
2025-10-9 14:26:52 - log: [PreviewInEditor] start init_cs_proto.js
2025-10-9 14:26:52 - log: [PreviewInEditor] Using custom pipeline: Builtin
2025-10-9 14:26:52 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-10-9 14:26:52 - log: [PreviewInEditor] [2025/10/09 14:26:52] [INFO] NetMgr Network manager initialized
2025-10-9 14:26:52 - log: [PreviewInEditor] [2025/10/09 14:26:52] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_SESSION
2025-10-9 14:26:52 - log: [PreviewInEditor] [2025/10/09 14:26:52] [INFO] NetMgr Registered handler for msgId: CS_CMD_HEARTBEAT
2025-10-9 14:26:52 - log: [PreviewInEditor] [2025/10/09 14:26:52] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_ROLE
2025-10-9 14:26:52 - log: [PreviewInEditor] Spine: Animation not found: Idle
2025-10-9 14:26:52 - log: [PreviewInEditor] this._enemyPrefabData:  <ref *1> EnemyPrefab {
  _objFlags: 59392,
  _name: '',
  __editorExtras__: {},
  node: <ref *2> Node {
    _objFlags: 0,
    _name: '100005',
    __editorExtras__: {},
    _parent: Node {
      _objFlags: 0,
      _name: 'Plane',
      __editorExtras__: {},
      _parent: [Node],
      _children: [Array],
      _active: true,
      _components: [Array],
      _prefab: [PrefabInfo],
      _scene: [Scene],
      _activeInHierarchy: true,
      _id: 'afDGRiIbFCgadv/obfAhBv',
      _eventProcessor: [NodeEventProcessor],
      _eventMask: 1,
      _siblingIndex: 0,
      _originalSceneId: '',
      _uiProps: [NodeUIProperties],
      _static: false,
      _lpos: [Vec3],
      _lrot: [Quat],
      _lscale: [Vec3],
      _mobility: 0,
      _layer: 1073741824,
      _euler: [Vec3],
      _transformFlags: 15,
      _eulerDirty: false,
      _flagChangeVersion: 17,
      _hasChangedFlags: 7,
      _pos: [Vec3],
      _rot: [Quat],
      _scale: [Vec3],
      _mat: [Mat4]
    },
    _children: [ [Node], [Node], [Node], [Node] ],
    _active: true,
    _components: [ [UITransform], [Circular *1] ],
    _prefab: PrefabInfo {
      root: [Circular *2],
      asset: [Prefab],
      fileId: '69dYEnX85PPaKFm4lX3qPp',
      instance: [PrefabInstance],
      targetOverrides: null,
      nestedPrefabInstanceRoots: null
    },
    _scene: <ref *3> Scene {
      _objFlags: 0,
      _name: 'PlaneEditor',
      __editorExtras__: {},
      _parent: null,
      _children: [Array],
      _active: true,
      _components: [],
      _prefab: [PrefabInfo],
      _scene: [Circular *3],
      _activeInHierarchy: true,
      _id: 'd90c1d4b-98f0-42eb-8111-dacc86636312',
      _eventProcessor: [NodeEventProcessor],
      _eventMask: 1,
      _siblingIndex: 0,
      _originalSceneId: '',
      _uiProps: [NodeUIProperties],
      _static: false,
      _lpos: [Vec3],
      _lrot: [Quat],
      _lscale: [Vec3],
      _mobility: 0,
      _layer: 1073741824,
      _euler: [Vec3],
      _transformFlags: 0,
      _eulerDirty: false,
      _flagChangeVersion: 0,
      _hasChangedFlags: 0,
      _pos: [Vec3],
      _rot: [Quat],
      _scale: [Vec3],
      _mat: [Mat4],
      autoReleaseAssets: false,
      _globals: [SceneGlobals],
      dependAssets: null,
      _renderScene: [RenderScene],
      _prefabSyncedInLiveReload: false,
      _inited: true
    },
    _activeInHierarchy: true,
    _id: '00g3MuJ2dNFr9+dz6DBk+Z',
    _eventProcessor: NodeEventProcessor {
      claimedTouchIdList: [],
      maskList: null,
      cachedCameraPriority: 0,
      previousMouseIn: false,
      bubblingTarget: [CallbacksInvoker],
      capturingTarget: null,
      shouldHandleEventMouse: false,
      shouldHandleEventTouch: false,
      _dispatchingTouch: null,
      _isEnabled: true,
      _isMouseLeaveWindow: false,
      _node: [Circular *2]
    },
    _eventMask: 1,
    _siblingIndex: 0,
    _originalSceneId: '',
    _uiProps: NodeUIProperties {
      _uiComp: null,
      _opacity: 1,
      _localOpacity: 1,
      colorDirty: true,
      _uiTransformComp: [UITransform],
      _uiSkewComp: null,
      _node: [Circular *2]
    },
    _static: false,
    _lpos: Vec3 { x: 0, y: 0, z: 0 },
    _lrot: Quat { x: 0, y: 0, z: 0, w: 1 },
    _lscale: Vec3 { x: 1.5, y: 1.5, z: 1 },
    _mobility: 0,
    _layer: 1073741824,
    _euler: Vec3 { x: 0, y: 0, z: 0 },
    _transformFlags: 15,
    _eulerDirty: false,
    _flagChangeVersion: 17,
    _hasChangedFlags: 7,
    _pos: Vec3 { x: 0, y: 0, z: 0 },
    _rot: Quat { x: 0, y: 0, z: 0, w: 1 },
    _scale: Vec3 { x: 1, y: 1, z: 1 },
    _mat: Mat4 {
      m00: 1,
      m01: 0,
      m02: 0,
      m03: 0,
      m04: 0,
      m05: 1,
      m06: 0,
      m07: 0,
      m08: 0,
      m09: 0,
      m10: 1,
      m11: 0,
      m12: 0,
      m13: 0,
      m14: 0,
      m15: 1
    }
  },
  _enabled: true,
  __prefab: CompPrefabInfo { fileId: '11zr7lXqtOIbhI5vFb6G32' },
  _sceneGetter: null,
  _id: '41V/FjA/xKKqEJz+Nuei0j',
  tiltSpeed: 100,
  tiltOffset: 100,
  eventGroups: [
    EnemyEventGroupData {
      name: '',
      triggerCount: 1,
      conditions: [Array],
      actions: []
    }
  ],
  persistentGizmo: null,
  iconGizmo: null,
  _watcherHandle: undefined
}
2025-10-9 14:27:14 - warn: [PreviewInEditor] IPC message has been lost.Error: [PreviewInEditor] IPC message has been lost.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at IpcRenderer.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\ipc\web\webview.ccc:1:1495)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
2025-10-9 14:27:14 - info: [PreviewInEditor] 预览环境初始化完毕
2025-10-9 14:28:13 - log: [Scene] start init_cs_proto.js
2025-10-9 14:28:14 - log: [Scene] start init_cs_proto.js
2025-10-9 14:28:15 - log: [PreviewInEditor] meshopt wasm decoder initialized
2025-10-9 14:28:15 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
2025-10-9 14:28:15 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
2025-10-9 14:28:15 - log: [PreviewInEditor] [PHYSICS]: using builtin.
2025-10-9 14:28:15 - log: [PreviewInEditor] Cocos Creator v3.8.6
2025-10-9 14:28:15 - log: [PreviewInEditor] start init_cs_proto.js
2025-10-9 14:28:15 - log: [PreviewInEditor] Using custom pipeline: Builtin
2025-10-9 14:28:15 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-10-9 14:28:16 - log: [PreviewInEditor] [2025/10/09 14:28:16] [INFO] NetMgr Network manager initialized
2025-10-9 14:28:16 - log: [PreviewInEditor] [2025/10/09 14:28:16] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_SESSION
2025-10-9 14:28:16 - log: [PreviewInEditor] [2025/10/09 14:28:16] [INFO] NetMgr Registered handler for msgId: CS_CMD_HEARTBEAT
2025-10-9 14:28:16 - log: [PreviewInEditor] [2025/10/09 14:28:16] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_ROLE
2025-10-9 14:28:16 - log: [PreviewInEditor] Spine: Animation not found: Idle
2025-10-9 14:28:19 - info: [PreviewInEditor] 预览环境初始化完毕
2025-10-9 14:28:54 - log: [Scene] start init_cs_proto.js
2025-10-9 14:28:54 - log: [Scene] start init_cs_proto.js
2025-10-9 14:28:55 - log: [PreviewInEditor] meshopt wasm decoder initialized
2025-10-9 14:28:55 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
2025-10-9 14:28:55 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
2025-10-9 14:28:55 - log: [PreviewInEditor] [PHYSICS]: using builtin.
2025-10-9 14:28:55 - log: [PreviewInEditor] Cocos Creator v3.8.6
2025-10-9 14:28:56 - log: [PreviewInEditor] start init_cs_proto.js
2025-10-9 14:28:56 - log: [PreviewInEditor] Using custom pipeline: Builtin
2025-10-9 14:28:56 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-10-9 14:28:56 - log: [PreviewInEditor] [2025/10/09 14:28:56] [INFO] NetMgr Network manager initialized
2025-10-9 14:28:56 - log: [PreviewInEditor] [2025/10/09 14:28:56] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_SESSION
2025-10-9 14:28:56 - log: [PreviewInEditor] [2025/10/09 14:28:56] [INFO] NetMgr Registered handler for msgId: CS_CMD_HEARTBEAT
2025-10-9 14:28:56 - log: [PreviewInEditor] [2025/10/09 14:28:56] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_ROLE
2025-10-9 14:28:56 - log: [PreviewInEditor] Spine: Animation not found: Idle
2025-10-9 14:28:56 - log: [PreviewInEditor] onEnemyCreated 0
2025-10-9 14:28:59 - info: [PreviewInEditor] 预览环境初始化完毕
2025-10-9 14:29:12 - log: [Scene] start init_cs_proto.js
2025-10-9 14:29:13 - log: [PreviewInEditor] meshopt wasm decoder initialized
2025-10-9 14:29:13 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
2025-10-9 14:29:13 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
2025-10-9 14:29:13 - log: [PreviewInEditor] [PHYSICS]: using builtin.
2025-10-9 14:29:13 - log: [PreviewInEditor] Cocos Creator v3.8.6
2025-10-9 14:29:14 - log: [PreviewInEditor] start init_cs_proto.js
2025-10-9 14:29:14 - log: [PreviewInEditor] Using custom pipeline: Builtin
2025-10-9 14:29:14 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-10-9 14:29:14 - log: [PreviewInEditor] [2025/10/09 14:29:14] [INFO] NetMgr Network manager initialized
2025-10-9 14:29:14 - log: [PreviewInEditor] [2025/10/09 14:29:14] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_SESSION
2025-10-9 14:29:14 - log: [PreviewInEditor] [2025/10/09 14:29:14] [INFO] NetMgr Registered handler for msgId: CS_CMD_HEARTBEAT
2025-10-9 14:29:14 - log: [PreviewInEditor] [2025/10/09 14:29:14] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_ROLE
2025-10-9 14:29:14 - log: [PreviewInEditor] Spine: Animation not found: Idle
2025-10-9 14:29:16 - info: [PreviewInEditor] 预览环境初始化完毕
2025-10-9 14:29:30 - log: [Scene] start init_cs_proto.js
2025-10-9 14:29:31 - log: [PreviewInEditor] meshopt wasm decoder initialized
2025-10-9 14:29:31 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
2025-10-9 14:29:31 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
2025-10-9 14:29:31 - log: [PreviewInEditor] [PHYSICS]: using builtin.
2025-10-9 14:29:31 - log: [PreviewInEditor] Cocos Creator v3.8.6
2025-10-9 14:29:31 - log: [PreviewInEditor] start init_cs_proto.js
2025-10-9 14:29:31 - log: [PreviewInEditor] Using custom pipeline: Builtin
2025-10-9 14:29:31 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-10-9 14:29:32 - log: [PreviewInEditor] [2025/10/09 14:29:32] [INFO] NetMgr Network manager initialized
2025-10-9 14:29:32 - log: [PreviewInEditor] [2025/10/09 14:29:32] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_SESSION
2025-10-9 14:29:32 - log: [PreviewInEditor] [2025/10/09 14:29:32] [INFO] NetMgr Registered handler for msgId: CS_CMD_HEARTBEAT
2025-10-9 14:29:32 - log: [PreviewInEditor] [2025/10/09 14:29:32] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_ROLE
2025-10-9 14:29:32 - log: [PreviewInEditor] Spine: Animation not found: Idle
2025-10-9 14:29:34 - info: [PreviewInEditor] 预览环境初始化完毕
2025-10-9 14:29:49 - log: [Scene] start init_cs_proto.js
2025-10-9 14:29:50 - log: [PreviewInEditor] meshopt wasm decoder initialized
2025-10-9 14:29:50 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
2025-10-9 14:29:50 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
2025-10-9 14:29:50 - log: [PreviewInEditor] [PHYSICS]: using builtin.
2025-10-9 14:29:50 - log: [PreviewInEditor] Cocos Creator v3.8.6
2025-10-9 14:29:50 - log: [PreviewInEditor] start init_cs_proto.js
2025-10-9 14:29:50 - log: [PreviewInEditor] Using custom pipeline: Builtin
2025-10-9 14:29:50 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-10-9 14:29:51 - log: [PreviewInEditor] [2025/10/09 14:29:51] [INFO] NetMgr Network manager initialized
2025-10-9 14:29:51 - log: [PreviewInEditor] [2025/10/09 14:29:51] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_SESSION
2025-10-9 14:29:51 - log: [PreviewInEditor] [2025/10/09 14:29:51] [INFO] NetMgr Registered handler for msgId: CS_CMD_HEARTBEAT
2025-10-9 14:29:51 - log: [PreviewInEditor] [2025/10/09 14:29:51] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_ROLE
2025-10-9 14:29:51 - log: [PreviewInEditor] Spine: Animation not found: Idle
2025-10-9 14:29:51 - log: [PreviewInEditor] onEnemyCreated 1
2025-10-9 14:29:54 - error: (node:9228) MaxListenersExceededWarning: Possible EventEmitter memory leak detected. 11 render-view-deleted listeners added to [WebContents]. MaxListeners is 10. Use emitter.setMaxListeners() to increase limit
(Use `CocosCreator --trace-warnings ...` to show where the warning was created)
2025-10-9 14:29:54 - error: (node:9228) MaxListenersExceededWarning: Possible EventEmitter memory leak detected. 11 new-window listeners added to [WebContents]. MaxListeners is 10. Use emitter.setMaxListeners() to increase limit
2025-10-9 14:29:54 - warn: [PreviewInEditor] IPC message has been lost.Error: [PreviewInEditor] IPC message has been lost.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at IpcRenderer.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\ipc\web\webview.ccc:1:1495)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
2025-10-9 14:29:54 - info: [PreviewInEditor] 预览环境初始化完毕
2025-10-9 14:30:22 - log: [Scene] start init_cs_proto.js
2025-10-9 14:30:23 - log: [PreviewInEditor] meshopt wasm decoder initialized
2025-10-9 14:30:23 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
2025-10-9 14:30:23 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
2025-10-9 14:30:23 - log: [PreviewInEditor] [PHYSICS]: using builtin.
2025-10-9 14:30:23 - log: [PreviewInEditor] Cocos Creator v3.8.6
2025-10-9 14:30:23 - log: [PreviewInEditor] start init_cs_proto.js
2025-10-9 14:30:23 - log: [PreviewInEditor] Using custom pipeline: Builtin
2025-10-9 14:30:23 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-10-9 14:30:24 - log: [PreviewInEditor] [2025/10/09 14:30:24] [INFO] NetMgr Network manager initialized
2025-10-9 14:30:24 - log: [PreviewInEditor] [2025/10/09 14:30:24] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_SESSION
2025-10-9 14:30:24 - log: [PreviewInEditor] [2025/10/09 14:30:24] [INFO] NetMgr Registered handler for msgId: CS_CMD_HEARTBEAT
2025-10-9 14:30:24 - log: [PreviewInEditor] [2025/10/09 14:30:24] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_ROLE
2025-10-9 14:30:24 - log: [PreviewInEditor] Spine: Animation not found: Idle
2025-10-9 14:30:24 - log: [PreviewInEditor] onEnemyCreated 0
2025-10-9 14:30:24 - log: [PreviewInEditor] onEnemyCreated 1
2025-10-9 14:30:25 - info: [PreviewInEditor] 预览环境初始化完毕
2025-10-9 14:30:57 - log: [Scene] start init_cs_proto.js
2025-10-9 14:30:58 - log: [PreviewInEditor] meshopt wasm decoder initialized
2025-10-9 14:30:58 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
2025-10-9 14:30:58 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
2025-10-9 14:30:58 - log: [PreviewInEditor] [PHYSICS]: using builtin.
2025-10-9 14:30:58 - log: [PreviewInEditor] Cocos Creator v3.8.6
2025-10-9 14:30:58 - log: [PreviewInEditor] start init_cs_proto.js
2025-10-9 14:30:58 - log: [PreviewInEditor] Using custom pipeline: Builtin
2025-10-9 14:30:58 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-10-9 14:30:59 - log: [PreviewInEditor] [2025/10/09 14:30:59] [INFO] NetMgr Network manager initialized
2025-10-9 14:30:59 - log: [PreviewInEditor] [2025/10/09 14:30:59] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_SESSION
2025-10-9 14:30:59 - log: [PreviewInEditor] [2025/10/09 14:30:59] [INFO] NetMgr Registered handler for msgId: CS_CMD_HEARTBEAT
2025-10-9 14:30:59 - log: [PreviewInEditor] [2025/10/09 14:30:59] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_ROLE
2025-10-9 14:30:59 - log: [PreviewInEditor] Spine: Animation not found: Idle
2025-10-9 14:31:04 - info: [PreviewInEditor] 预览环境初始化完毕
2025-10-9 14:31:19 - log: [PreviewInEditor] meshopt wasm decoder initialized
2025-10-9 14:31:19 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
2025-10-9 14:31:19 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
2025-10-9 14:31:19 - log: [PreviewInEditor] [PHYSICS]: using builtin.
2025-10-9 14:31:19 - log: [PreviewInEditor] Cocos Creator v3.8.6
2025-10-9 14:31:19 - log: [PreviewInEditor] start init_cs_proto.js
2025-10-9 14:31:19 - log: [PreviewInEditor] Using custom pipeline: Builtin
2025-10-9 14:31:19 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-10-9 14:31:20 - log: [PreviewInEditor] [2025/10/09 14:31:20] [INFO] NetMgr Network manager initialized
2025-10-9 14:31:20 - log: [PreviewInEditor] [2025/10/09 14:31:20] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_SESSION
2025-10-9 14:31:20 - log: [PreviewInEditor] [2025/10/09 14:31:20] [INFO] NetMgr Registered handler for msgId: CS_CMD_HEARTBEAT
2025-10-9 14:31:20 - log: [PreviewInEditor] [2025/10/09 14:31:20] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_ROLE
2025-10-9 14:31:20 - log: [PreviewInEditor] Spine: Animation not found: Idle
2025-10-9 14:31:48 - warn: [PreviewInEditor] IPC message has been lost.Error: [PreviewInEditor] IPC message has been lost.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at IpcRenderer.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\ipc\web\webview.ccc:1:1495)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
2025-10-9 14:31:48 - info: [PreviewInEditor] 预览环境初始化完毕
2025-10-9 14:32:29 - log: [Scene] start init_cs_proto.js
2025-10-9 14:32:30 - log: [PreviewInEditor] meshopt wasm decoder initialized
2025-10-9 14:32:30 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
2025-10-9 14:32:30 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
2025-10-9 14:32:30 - log: [PreviewInEditor] [PHYSICS]: using builtin.
2025-10-9 14:32:31 - log: [PreviewInEditor] Cocos Creator v3.8.6
2025-10-9 14:32:31 - log: [PreviewInEditor] start init_cs_proto.js
2025-10-9 14:32:31 - log: [PreviewInEditor] Using custom pipeline: Builtin
2025-10-9 14:32:31 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-10-9 14:32:31 - log: [PreviewInEditor] [2025/10/09 14:32:31] [INFO] NetMgr Network manager initialized
2025-10-9 14:32:31 - log: [PreviewInEditor] [2025/10/09 14:32:31] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_SESSION
2025-10-9 14:32:31 - log: [PreviewInEditor] [2025/10/09 14:32:31] [INFO] NetMgr Registered handler for msgId: CS_CMD_HEARTBEAT
2025-10-9 14:32:31 - log: [PreviewInEditor] [2025/10/09 14:32:31] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_ROLE
2025-10-9 14:32:32 - log: [PreviewInEditor] Spine: Animation not found: Idle
2025-10-9 14:33:17 - log: [Scene] start init_cs_proto.js
2025-10-9 14:33:17 - warn: [PreviewInEditor] IPC message has been lost.Error: [PreviewInEditor] IPC message has been lost.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at IpcRenderer.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\ipc\web\webview.ccc:1:1495)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
2025-10-9 14:33:17 - info: [PreviewInEditor] 预览环境初始化完毕
2025-10-9 14:33:18 - log: [PreviewInEditor] meshopt wasm decoder initialized
2025-10-9 14:33:18 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
2025-10-9 14:33:18 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
2025-10-9 14:33:18 - log: [PreviewInEditor] [PHYSICS]: using builtin.
2025-10-9 14:33:18 - log: [PreviewInEditor] Cocos Creator v3.8.6
2025-10-9 14:33:18 - log: [PreviewInEditor] start init_cs_proto.js
2025-10-9 14:33:18 - log: [PreviewInEditor] Using custom pipeline: Builtin
2025-10-9 14:33:18 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-10-9 14:33:19 - log: [PreviewInEditor] [2025/10/09 14:33:19] [INFO] NetMgr Network manager initialized
2025-10-9 14:33:19 - log: [PreviewInEditor] [2025/10/09 14:33:19] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_SESSION
2025-10-9 14:33:19 - log: [PreviewInEditor] [2025/10/09 14:33:19] [INFO] NetMgr Registered handler for msgId: CS_CMD_HEARTBEAT
2025-10-9 14:33:19 - log: [PreviewInEditor] [2025/10/09 14:33:19] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_ROLE
2025-10-9 14:33:19 - log: [PreviewInEditor] Spine: Animation not found: Idle
2025-10-9 14:33:21 - info: [PreviewInEditor] 预览环境初始化完毕
2025-10-9 14:33:57 - log: [Scene] start init_cs_proto.js
2025-10-9 14:33:58 - log: [Scene] start init_cs_proto.js
2025-10-9 14:33:59 - log: [PreviewInEditor] meshopt wasm decoder initialized
2025-10-9 14:33:59 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
2025-10-9 14:33:59 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
2025-10-9 14:33:59 - log: [PreviewInEditor] [PHYSICS]: using builtin.
2025-10-9 14:33:59 - log: [PreviewInEditor] Cocos Creator v3.8.6
2025-10-9 14:33:59 - log: [PreviewInEditor] start init_cs_proto.js
2025-10-9 14:33:59 - log: [PreviewInEditor] Using custom pipeline: Builtin
2025-10-9 14:33:59 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-10-9 14:34:00 - log: [PreviewInEditor] [2025/10/09 14:34:00] [INFO] NetMgr Network manager initialized
2025-10-9 14:34:00 - log: [PreviewInEditor] [2025/10/09 14:34:00] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_SESSION
2025-10-9 14:34:00 - log: [PreviewInEditor] [2025/10/09 14:34:00] [INFO] NetMgr Registered handler for msgId: CS_CMD_HEARTBEAT
2025-10-9 14:34:00 - log: [PreviewInEditor] [2025/10/09 14:34:00] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_ROLE
2025-10-9 14:34:00 - log: [PreviewInEditor] Spine: Animation not found: Idle
2025-10-9 14:34:00 - log: [PreviewInEditor] emitter created
2025-10-9 14:34:00 - log: [PreviewInEditor] emitter created
2025-10-9 14:34:03 - info: [PreviewInEditor] 预览环境初始化完毕
2025-10-9 14:34:26 - log: [Scene] start init_cs_proto.js
2025-10-9 14:34:28 - log: [PreviewInEditor] meshopt wasm decoder initialized
2025-10-9 14:34:28 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
2025-10-9 14:34:28 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
2025-10-9 14:34:28 - log: [PreviewInEditor] [PHYSICS]: using builtin.
2025-10-9 14:34:29 - log: [PreviewInEditor] Cocos Creator v3.8.6
2025-10-9 14:34:29 - log: [PreviewInEditor] start init_cs_proto.js
2025-10-9 14:34:29 - log: [PreviewInEditor] Using custom pipeline: Builtin
2025-10-9 14:34:29 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-10-9 14:34:29 - log: [PreviewInEditor] [2025/10/09 14:34:29] [INFO] NetMgr Network manager initialized
2025-10-9 14:34:29 - log: [PreviewInEditor] [2025/10/09 14:34:29] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_SESSION
2025-10-9 14:34:29 - log: [PreviewInEditor] [2025/10/09 14:34:29] [INFO] NetMgr Registered handler for msgId: CS_CMD_HEARTBEAT
2025-10-9 14:34:29 - log: [PreviewInEditor] [2025/10/09 14:34:29] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_ROLE
2025-10-9 14:34:30 - log: [PreviewInEditor] Spine: Animation not found: Idle
2025-10-9 14:34:30 - log: [PreviewInEditor] emitter created
2025-10-9 14:34:30 - log: [PreviewInEditor] add emitter
2025-10-9 14:34:30 - log: [PreviewInEditor] emitter created
2025-10-9 14:34:30 - log: [PreviewInEditor] add emitter
2025-10-9 14:34:32 - warn: [PreviewInEditor] IPC message has been lost.Error: [PreviewInEditor] IPC message has been lost.
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at IpcRenderer.<anonymous> (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\scene\dist\script\3d\manager\ipc\web\webview.ccc:1:1495)
    at IpcRenderer.emit (node:events:519:28)
    at Object.onMessage (node:electron/js2c/renderer_init:2:8837)
2025-10-9 14:34:33 - info: [PreviewInEditor] 预览环境初始化完毕
2025-10-9 14:35:13 - log: [Scene] start init_cs_proto.js
2025-10-9 14:35:13 - log: [Scene] start init_cs_proto.js
2025-10-9 14:35:14 - log: [PreviewInEditor] meshopt wasm decoder initialized
2025-10-9 14:35:14 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
2025-10-9 14:35:14 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
2025-10-9 14:35:14 - log: [PreviewInEditor] [PHYSICS]: using builtin.
2025-10-9 14:35:14 - log: [PreviewInEditor] Cocos Creator v3.8.6
2025-10-9 14:35:15 - log: [PreviewInEditor] start init_cs_proto.js
2025-10-9 14:35:15 - log: [PreviewInEditor] Using custom pipeline: Builtin
2025-10-9 14:35:15 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-10-9 14:35:15 - log: [PreviewInEditor] [2025/10/09 14:35:15] [INFO] NetMgr Network manager initialized
2025-10-9 14:35:15 - log: [PreviewInEditor] [2025/10/09 14:35:15] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_SESSION
2025-10-9 14:35:15 - log: [PreviewInEditor] [2025/10/09 14:35:15] [INFO] NetMgr Registered handler for msgId: CS_CMD_HEARTBEAT
2025-10-9 14:35:15 - log: [PreviewInEditor] [2025/10/09 14:35:15] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_ROLE
2025-10-9 14:35:15 - log: [PreviewInEditor] Spine: Animation not found: Idle
2025-10-9 14:35:15 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:15 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:15 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:15 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:15 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:15 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:15 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:15 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:15 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:15 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:15 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:15 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:15 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:15 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:15 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:15 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:16 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  0
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  1
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  1
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:17 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:18 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:19 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:20 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:21 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:22 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:23 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:24 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:25 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:26 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:27 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:27 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:27 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:27 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:27 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:27 - log: [PreviewInEditor] tick emitter:  2
2025-10-9 14:35:27 - info: [PreviewInEditor] 预览环境初始化完毕
2025-10-9 14:36:06 - log: [Scene] start init_cs_proto.js
2025-10-9 14:36:07 - log: [PreviewInEditor] meshopt wasm decoder initialized
2025-10-9 14:36:07 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
2025-10-9 14:36:07 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
2025-10-9 14:36:07 - log: [PreviewInEditor] [PHYSICS]: using builtin.
2025-10-9 14:36:07 - log: [PreviewInEditor] Cocos Creator v3.8.6
2025-10-9 14:36:07 - log: [PreviewInEditor] start init_cs_proto.js
2025-10-9 14:36:07 - log: [PreviewInEditor] Using custom pipeline: Builtin
2025-10-9 14:36:07 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-10-9 14:36:08 - log: [PreviewInEditor] [2025/10/09 14:36:08] [INFO] NetMgr Network manager initialized
2025-10-9 14:36:08 - log: [PreviewInEditor] [2025/10/09 14:36:08] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_SESSION
2025-10-9 14:36:08 - log: [PreviewInEditor] [2025/10/09 14:36:08] [INFO] NetMgr Registered handler for msgId: CS_CMD_HEARTBEAT
2025-10-9 14:36:08 - log: [PreviewInEditor] [2025/10/09 14:36:08] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_ROLE
2025-10-9 14:36:08 - log: [PreviewInEditor] Spine: Animation not found: Idle
2025-10-9 14:36:10 - log: [PreviewInEditor] create bullet:  <ref *1> Bullet {
  _objFlags: 0,
  _name: '',
  __editorExtras__: {},
  node: <ref *2> Node {
    _objFlags: 0,
    _name: '_bullet_',
    __editorExtras__: {},
    _parent: null,
    _children: [],
    _active: true,
    _components: [
      [UITransform],
      [Circular *1],
      [DefaultMove],
      [Sprite],
      [FBoxCollider],
      [BoxCollider2D]
    ],
    _prefab: PrefabInfo {
      root: [Circular *2],
      asset: [Prefab],
      fileId: 'cceer9/u5JSKedIniAH4fm',
      instance: [PrefabInstance],
      targetOverrides: null,
      nestedPrefabInstanceRoots: null
    },
    _scene: null,
    _activeInHierarchy: false,
    _id: '01/UDQmWVDerxFNsOhH6TE',
    _eventProcessor: NodeEventProcessor {
      claimedTouchIdList: [],
      maskList: null,
      cachedCameraPriority: 0,
      previousMouseIn: false,
      bubblingTarget: null,
      capturingTarget: null,
      shouldHandleEventMouse: false,
      shouldHandleEventTouch: false,
      _dispatchingTouch: null,
      _isEnabled: false,
      _isMouseLeaveWindow: false,
      _node: [Circular *2]
    },
    _eventMask: 0,
    _siblingIndex: 0,
    _originalSceneId: '',
    _uiProps: NodeUIProperties {
      _uiComp: null,
      _opacity: 1,
      _localOpacity: 1,
      colorDirty: true,
      _uiTransformComp: null,
      _uiSkewComp: null,
      _node: [Circular *2]
    },
    _static: false,
    _lpos: Vec3 { x: 0, y: 0, z: 0 },
    _lrot: Quat { x: 0, y: 0, z: 0, w: 1 },
    _lscale: Vec3 { x: 1, y: 1, z: 1 },
    _mobility: 0,
    _layer: 33554432,
    _euler: Vec3 { x: 0, y: 0, z: 0 },
    _transformFlags: 15,
    _eulerDirty: false,
    _flagChangeVersion: 144,
    _hasChangedFlags: 7,
    _pos: Vec3 { x: 0, y: 0, z: 0 },
    _rot: Quat { x: 0, y: 0, z: 0, w: 1 },
    _scale: Vec3 { x: 1, y: 1, z: 1 },
    _mat: Mat4 {
      m00: 1,
      m01: 0,
      m02: 0,
      m03: 0,
      m04: 0,
      m05: 1,
      m06: 0,
      m07: 0,
      m08: 0,
      m09: 0,
      m10: 1,
      m11: 0,
      m12: 0,
      m13: 0,
      m14: 0,
      m15: 1
    },
    puuid: '33ebf993-63d1-47cf-b113-21b161680e15'
  },
  _enabled: true,
  __prefab: CompPrefabInfo { fileId: '38AJsQOnBNTqyxAE5iOh//' },
  _sceneGetter: null,
  _id: '83W/RbQ/NHDqGD8QvoIa71',
  entityID: 0,
  isDead: false,
  m_comps: Map(0) {},
  m_tags: 0,
  mover: DefaultMove {
    _objFlags: 0,
    _name: '',
    __editorExtras__: {},
    node: <ref *2> Node {
      _objFlags: 0,
      _name: '_bullet_',
      __editorExtras__: {},
      _parent: null,
      _children: [],
      _active: true,
      _components: [Array],
      _prefab: [PrefabInfo],
      _scene: null,
      _activeInHierarchy: false,
      _id: '01/UDQmWVDerxFNsOhH6TE',
      _eventProcessor: [NodeEventProcessor],
      _eventMask: 0,
      _siblingIndex: 0,
      _originalSceneId: '',
      _uiProps: [NodeUIProperties],
      _static: false,
      _lpos: [Vec3],
      _lrot: [Quat],
      _lscale: [Vec3],
      _mobility: 0,
      _layer: 33554432,
      _euler: [Vec3],
      _transformFlags: 15,
      _eulerDirty: false,
      _flagChangeVersion: 144,
      _hasChangedFlags: 7,
      _pos: [Vec3],
      _rot: [Quat],
      _scale: [Vec3],
      _mat: [Mat4],
      puuid: '33ebf993-63d1-47cf-b113-21b161680e15'
    },
    _enabled: true,
    __prefab: CompPrefabInfo { fileId: '9cnnRQ0rRDu6dUJerhQ3H6' },
    _sceneGetter: null,
    _id: 'e7vwfTncBNEYmAbHk9xk7c',
    forwardOrientation: -90,
    speed: 100,
    speedAngle: 0,
    acceleration: 0,
    accelerationAngle: 0,
    turnSpeed: 60,
    tiltSpeed: 0,
    tiltOffset: 100,
    orientation: 0,
    orientationType: 0,
    orientationParam: 0,
    _wasVisible: false,
    _isVisible: false,
    _isMovable: true,
    _tiltTime: 0,
    _selfSize: Vec2 { x: 0, y: 0 },
    _position: Vec3 { x: 0, y: 0, z: 0 },
    _basePosition: Vec3 { x: 0, y: 0, z: 0 },
    _visibilityCheckCounter: 0,
    _eventListeners: Map(0) {},
    _trackingTimeLimit: 0,
    _trackingAngleLimit: 180,
    _targetingDelegate: null,
    _target: null
  },
  bulletSprite: Sprite {
    _objFlags: 0,
    _name: '',
    __editorExtras__: {},
    node: <ref *2> Node {
      _objFlags: 0,
      _name: '_bullet_',
      __editorExtras__: {},
      _parent: null,
      _children: [],
      _active: true,
      _components: [Array],
      _prefab: [PrefabInfo],
      _scene: null,
      _activeInHierarchy: false,
      _id: '01/UDQmWVDerxFNsOhH6TE',
      _eventProcessor: [NodeEventProcessor],
      _eventMask: 0,
      _siblingIndex: 0,
      _originalSceneId: '',
      _uiProps: [NodeUIProperties],
      _static: false,
      _lpos: [Vec3],
      _lrot: [Quat],
      _lscale: [Vec3],
      _mobility: 0,
      _layer: 33554432,
      _euler: [Vec3],
      _transformFlags: 15,
      _eulerDirty: false,
      _flagChangeVersion: 144,
      _hasChangedFlags: 7,
      _pos: [Vec3],
      _rot: [Quat],
      _scale: [Vec3],
      _mat: [Mat4],
      puuid: '33ebf993-63d1-47cf-b113-21b161680e15'
    },
    _enabled: true,
    __prefab: CompPrefabInfo { fileId: 'dfRp+18UNAgZndnJ9STTKM' },
    _sceneGetter: null,
    _id: '38+X7zMYBB9pEMZp3nDGLD',
    _materials: [],
    _materialInstances: [],
    _renderData: null,
    _customMaterial: null,
    _srcBlendFactor: 2,
    _dstBlendFactor: 4,
    _color: Color { _data: [Uint8ClampedArray] },
    _stencilStage: 0,
    _assembler: null,
    _postAssembler: null,
    _renderDataFlag: true,
    _renderFlag: true,
    _instanceMaterialType: -1,
    _srcBlendFactorCache: 2,
    _dstBlendFactorCache: 4,
    _dirtyVersion: -1,
    _internalId: -1,
    _flagChangedVersion: -1,
    _useVertexOpacity: false,
    _lastParent: null,
    _renderEntity: RenderEntity {
      _renderEntityType: 0,
      _dynamicDrawInfoArr: [],
      _node: null,
      _renderTransform: null,
      _stencilStage: 0,
      _useLocal: false,
      _maskMode: 0,
      _color: [Color],
      _localOpacity: 255,
      _colorDirty: true,
      _enabled: false
    },
    _spriteFrame: SpriteFrame {
      _objFlags: 0,
      _name: 'dj_zd_46',
      __editorExtras__: {},
      _callbackTable: [Object: null prototype] {},
      loaded: true,
      _native: '',
      _nativeUrl: '',
      _file: null,
      _ref: 2,
      vertices: [Object],
      uv: [Array],
      unbiasUV: [Array],
      uvSliced: [Array],
      _rect: [Rect],
      _trimmedBorder: [Vec4],
      _offset: [Vec2],
      _originalSize: [Size],
      _rotated: true,
      _capInsets: [Array],
      _atlasUuid: '691e06ea-ee40-40c0-89bb-fab92b54dd9d',
      _texture: [Texture2D],
      _isFlipUVY: false,
      _isFlipUVX: false,
      _original: null,
      _packable: false,
      _pixelsToUnit: 100,
      _pivot: [Vec2],
      _meshType: 0,
      _extrude: 0,
      _customOutLine: [],
      _mesh: null,
      _minPos: [Vec3],
      _maxPos: [Vec3]
    },
    _type: 0,
    _fillType: 0,
    _sizeMode: 1,
    _fillCenter: Vec2 { x: 0, y: 0 },
    _fillStart: 0,
    _fillRange: 0,
    _isTrimmedMode: true,
    _useGrayscale: false,
    _atlas: SpriteAtlas {
      _objFlags: 0,
      _name: 'enemyBullet1',
      __editorExtras__: {},
      _callbackTable: [Object: null prototype] {},
      loaded: true,
      _native: '',
      _nativeUrl: '',
      _file: null,
      _ref: 1,
      spriteFrames: [Object: null prototype]
    }
  },
  collider: FBoxCollider {
    _objFlags: 0,
    _name: '',
    __editorExtras__: {},
    node: <ref *2> Node {
      _objFlags: 0,
      _name: '_bullet_',
      __editorExtras__: {},
      _parent: null,
      _children: [],
      _active: true,
      _components: [Array],
      _prefab: [PrefabInfo],
      _scene: null,
      _activeInHierarchy: false,
      _id: '01/UDQmWVDerxFNsOhH6TE',
      _eventProcessor: [NodeEventProcessor],
      _eventMask: 0,
      _siblingIndex: 0,
      _originalSceneId: '',
      _uiProps: [NodeUIProperties],
      _static: false,
      _lpos: [Vec3],
      _lrot: [Quat],
      _lscale: [Vec3],
      _mobility: 0,
      _layer: 33554432,
      _euler: [Vec3],
      _transformFlags: 15,
      _eulerDirty: false,
      _flagChangeVersion: 144,
      _hasChangedFlags: 7,
      _pos: [Vec3],
      _rot: [Quat],
      _scale: [Vec3],
      _mat: [Mat4],
      puuid: '33ebf993-63d1-47cf-b113-21b161680e15'
    },
    _enabled: true,
    __prefab: CompPrefabInfo { fileId: 'ffOChq06xK/owFUGDrFa6R' },
    _sceneGetter: null,
    _id: '62nm7ZGM1IQaaFbAdj1zko',
    isConvex: true,
    isEnable: true,
    isImmunityBullet: false,
    isImmunityBulletHurt: false,
    isImmunityCollider: false,
    isImmunityColliderHurt: false,
    entity: null,
    aabb: Rect { x: 0, y: 0, width: 0, height: 0 },
    colliderId: 0,
    _offset: Vec2 { x: 0, y: 0 },
    groupType: 1,
    worldPoints: [ [Vec2], [Vec2], [Vec2], [Vec2] ],
    worldEdge: [],
    _size: Size { width: 50, height: 50 }
  },
  hitEffectPrefab: null,
  isAlive: false,
  elapsedTime: 0,
  emitter: undefined,
  bulletData: undefined,
  config: undefined,
  prop: BulletProperty {
    _anyPropertyDirty: false,
    _properties: Map(17) {
      1 => [Property],
      2 => [Property],
      3 => [Property],
      4 => [Property],
      5 => [Property],
      6 => [Property],
      7 => [Property],
      8 => [Property],
      9 => [Property],
      16 => [Property],
      17 => [Property],
      10 => [Property],
      11 => [Property],
      12 => [Property],
      13 => [Property],
      14 => [Property],
      15 => [Property]
    },
    duration: Property {
      _value: 6000,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    delayDestroy: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    attack: Property {
      _value: 1,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    speed: Property {
      _value: 600,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    speedAngle: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    acceleration: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    accelerationAngle: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    orientation: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    orientationParam: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    maxTrackingDuration: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    maxTrackingAngle: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    scale: Property {
      _value: 1,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    color: Property {
      _value: [Color],
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    defaultFacing: Property {
      _value: -90,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    isDestroyOutScreen: Property {
      _value: true,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    isDestructive: Property {
      _value: false,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    isDestructiveOnHit: Property {
      _value: false,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    config: undefined
  },
  eventGroups: []
}
2025-10-9 14:36:10 - log: [PreviewInEditor] create bullet:  <ref *1> Bullet {
  _objFlags: 0,
  _name: '',
  __editorExtras__: {},
  node: <ref *2> Node {
    _objFlags: 0,
    _name: '_bullet_',
    __editorExtras__: {},
    _parent: null,
    _children: [],
    _active: true,
    _components: [
      [UITransform],
      [Circular *1],
      [DefaultMove],
      [Sprite],
      [FBoxCollider],
      [BoxCollider2D]
    ],
    _prefab: PrefabInfo {
      root: [Circular *2],
      asset: [Prefab],
      fileId: 'cceer9/u5JSKedIniAH4fm',
      instance: [PrefabInstance],
      targetOverrides: null,
      nestedPrefabInstanceRoots: null
    },
    _scene: null,
    _activeInHierarchy: false,
    _id: '02/Oiy44FMp4+il4B05Pfa',
    _eventProcessor: NodeEventProcessor {
      claimedTouchIdList: [],
      maskList: null,
      cachedCameraPriority: 0,
      previousMouseIn: false,
      bubblingTarget: null,
      capturingTarget: null,
      shouldHandleEventMouse: false,
      shouldHandleEventTouch: false,
      _dispatchingTouch: null,
      _isEnabled: false,
      _isMouseLeaveWindow: false,
      _node: [Circular *2]
    },
    _eventMask: 0,
    _siblingIndex: 0,
    _originalSceneId: '',
    _uiProps: NodeUIProperties {
      _uiComp: null,
      _opacity: 1,
      _localOpacity: 1,
      colorDirty: true,
      _uiTransformComp: null,
      _uiSkewComp: null,
      _node: [Circular *2]
    },
    _static: false,
    _lpos: Vec3 { x: 0, y: 0, z: 0 },
    _lrot: Quat { x: 0, y: 0, z: 0, w: 1 },
    _lscale: Vec3 { x: 1, y: 1, z: 1 },
    _mobility: 0,
    _layer: 33554432,
    _euler: Vec3 { x: 0, y: 0, z: 0 },
    _transformFlags: 15,
    _eulerDirty: false,
    _flagChangeVersion: 144,
    _hasChangedFlags: 7,
    _pos: Vec3 { x: 0, y: 0, z: 0 },
    _rot: Quat { x: 0, y: 0, z: 0, w: 1 },
    _scale: Vec3 { x: 1, y: 1, z: 1 },
    _mat: Mat4 {
      m00: 1,
      m01: 0,
      m02: 0,
      m03: 0,
      m04: 0,
      m05: 1,
      m06: 0,
      m07: 0,
      m08: 0,
      m09: 0,
      m10: 1,
      m11: 0,
      m12: 0,
      m13: 0,
      m14: 0,
      m15: 1
    },
    puuid: '33ebf993-63d1-47cf-b113-21b161680e15'
  },
  _enabled: true,
  __prefab: CompPrefabInfo { fileId: '38AJsQOnBNTqyxAE5iOh//' },
  _sceneGetter: null,
  _id: 'f11BobftBMbId5wExtVfw8',
  entityID: 0,
  isDead: false,
  m_comps: Map(0) {},
  m_tags: 0,
  mover: DefaultMove {
    _objFlags: 0,
    _name: '',
    __editorExtras__: {},
    node: <ref *2> Node {
      _objFlags: 0,
      _name: '_bullet_',
      __editorExtras__: {},
      _parent: null,
      _children: [],
      _active: true,
      _components: [Array],
      _prefab: [PrefabInfo],
      _scene: null,
      _activeInHierarchy: false,
      _id: '02/Oiy44FMp4+il4B05Pfa',
      _eventProcessor: [NodeEventProcessor],
      _eventMask: 0,
      _siblingIndex: 0,
      _originalSceneId: '',
      _uiProps: [NodeUIProperties],
      _static: false,
      _lpos: [Vec3],
      _lrot: [Quat],
      _lscale: [Vec3],
      _mobility: 0,
      _layer: 33554432,
      _euler: [Vec3],
      _transformFlags: 15,
      _eulerDirty: false,
      _flagChangeVersion: 144,
      _hasChangedFlags: 7,
      _pos: [Vec3],
      _rot: [Quat],
      _scale: [Vec3],
      _mat: [Mat4],
      puuid: '33ebf993-63d1-47cf-b113-21b161680e15'
    },
    _enabled: true,
    __prefab: CompPrefabInfo { fileId: '9cnnRQ0rRDu6dUJerhQ3H6' },
    _sceneGetter: null,
    _id: 'ea4cI47NZEo70b4VXiU7Q0',
    forwardOrientation: -90,
    speed: 100,
    speedAngle: 0,
    acceleration: 0,
    accelerationAngle: 0,
    turnSpeed: 60,
    tiltSpeed: 0,
    tiltOffset: 100,
    orientation: 0,
    orientationType: 0,
    orientationParam: 0,
    _wasVisible: false,
    _isVisible: false,
    _isMovable: true,
    _tiltTime: 0,
    _selfSize: Vec2 { x: 0, y: 0 },
    _position: Vec3 { x: 0, y: 0, z: 0 },
    _basePosition: Vec3 { x: 0, y: 0, z: 0 },
    _visibilityCheckCounter: 0,
    _eventListeners: Map(0) {},
    _trackingTimeLimit: 0,
    _trackingAngleLimit: 180,
    _targetingDelegate: null,
    _target: null
  },
  bulletSprite: Sprite {
    _objFlags: 0,
    _name: '',
    __editorExtras__: {},
    node: <ref *2> Node {
      _objFlags: 0,
      _name: '_bullet_',
      __editorExtras__: {},
      _parent: null,
      _children: [],
      _active: true,
      _components: [Array],
      _prefab: [PrefabInfo],
      _scene: null,
      _activeInHierarchy: false,
      _id: '02/Oiy44FMp4+il4B05Pfa',
      _eventProcessor: [NodeEventProcessor],
      _eventMask: 0,
      _siblingIndex: 0,
      _originalSceneId: '',
      _uiProps: [NodeUIProperties],
      _static: false,
      _lpos: [Vec3],
      _lrot: [Quat],
      _lscale: [Vec3],
      _mobility: 0,
      _layer: 33554432,
      _euler: [Vec3],
      _transformFlags: 15,
      _eulerDirty: false,
      _flagChangeVersion: 144,
      _hasChangedFlags: 7,
      _pos: [Vec3],
      _rot: [Quat],
      _scale: [Vec3],
      _mat: [Mat4],
      puuid: '33ebf993-63d1-47cf-b113-21b161680e15'
    },
    _enabled: true,
    __prefab: CompPrefabInfo { fileId: 'dfRp+18UNAgZndnJ9STTKM' },
    _sceneGetter: null,
    _id: '3bU3e1AwRIGIztt08czCah',
    _materials: [],
    _materialInstances: [],
    _renderData: null,
    _customMaterial: null,
    _srcBlendFactor: 2,
    _dstBlendFactor: 4,
    _color: Color { _data: [Uint8ClampedArray] },
    _stencilStage: 0,
    _assembler: null,
    _postAssembler: null,
    _renderDataFlag: true,
    _renderFlag: true,
    _instanceMaterialType: -1,
    _srcBlendFactorCache: 2,
    _dstBlendFactorCache: 4,
    _dirtyVersion: -1,
    _internalId: -1,
    _flagChangedVersion: -1,
    _useVertexOpacity: false,
    _lastParent: null,
    _renderEntity: RenderEntity {
      _renderEntityType: 0,
      _dynamicDrawInfoArr: [],
      _node: null,
      _renderTransform: null,
      _stencilStage: 0,
      _useLocal: false,
      _maskMode: 0,
      _color: [Color],
      _localOpacity: 255,
      _colorDirty: true,
      _enabled: false
    },
    _spriteFrame: SpriteFrame {
      _objFlags: 0,
      _name: 'dj_zd_46',
      __editorExtras__: {},
      _callbackTable: [Object: null prototype] {},
      loaded: true,
      _native: '',
      _nativeUrl: '',
      _file: null,
      _ref: 2,
      vertices: [Object],
      uv: [Array],
      unbiasUV: [Array],
      uvSliced: [Array],
      _rect: [Rect],
      _trimmedBorder: [Vec4],
      _offset: [Vec2],
      _originalSize: [Size],
      _rotated: true,
      _capInsets: [Array],
      _atlasUuid: '691e06ea-ee40-40c0-89bb-fab92b54dd9d',
      _texture: [Texture2D],
      _isFlipUVY: false,
      _isFlipUVX: false,
      _original: null,
      _packable: false,
      _pixelsToUnit: 100,
      _pivot: [Vec2],
      _meshType: 0,
      _extrude: 0,
      _customOutLine: [],
      _mesh: null,
      _minPos: [Vec3],
      _maxPos: [Vec3]
    },
    _type: 0,
    _fillType: 0,
    _sizeMode: 1,
    _fillCenter: Vec2 { x: 0, y: 0 },
    _fillStart: 0,
    _fillRange: 0,
    _isTrimmedMode: true,
    _useGrayscale: false,
    _atlas: SpriteAtlas {
      _objFlags: 0,
      _name: 'enemyBullet1',
      __editorExtras__: {},
      _callbackTable: [Object: null prototype] {},
      loaded: true,
      _native: '',
      _nativeUrl: '',
      _file: null,
      _ref: 1,
      spriteFrames: [Object: null prototype]
    }
  },
  collider: FBoxCollider {
    _objFlags: 0,
    _name: '',
    __editorExtras__: {},
    node: <ref *2> Node {
      _objFlags: 0,
      _name: '_bullet_',
      __editorExtras__: {},
      _parent: null,
      _children: [],
      _active: true,
      _components: [Array],
      _prefab: [PrefabInfo],
      _scene: null,
      _activeInHierarchy: false,
      _id: '02/Oiy44FMp4+il4B05Pfa',
      _eventProcessor: [NodeEventProcessor],
      _eventMask: 0,
      _siblingIndex: 0,
      _originalSceneId: '',
      _uiProps: [NodeUIProperties],
      _static: false,
      _lpos: [Vec3],
      _lrot: [Quat],
      _lscale: [Vec3],
      _mobility: 0,
      _layer: 33554432,
      _euler: [Vec3],
      _transformFlags: 15,
      _eulerDirty: false,
      _flagChangeVersion: 144,
      _hasChangedFlags: 7,
      _pos: [Vec3],
      _rot: [Quat],
      _scale: [Vec3],
      _mat: [Mat4],
      puuid: '33ebf993-63d1-47cf-b113-21b161680e15'
    },
    _enabled: true,
    __prefab: CompPrefabInfo { fileId: 'ffOChq06xK/owFUGDrFa6R' },
    _sceneGetter: null,
    _id: '93k7NBwWNNNL/ru7wQsEh8',
    isConvex: true,
    isEnable: true,
    isImmunityBullet: false,
    isImmunityBulletHurt: false,
    isImmunityCollider: false,
    isImmunityColliderHurt: false,
    entity: null,
    aabb: Rect { x: 0, y: 0, width: 0, height: 0 },
    colliderId: 0,
    _offset: Vec2 { x: 0, y: 0 },
    groupType: 1,
    worldPoints: [ [Vec2], [Vec2], [Vec2], [Vec2] ],
    worldEdge: [],
    _size: Size { width: 50, height: 50 }
  },
  hitEffectPrefab: null,
  isAlive: false,
  elapsedTime: 0,
  emitter: undefined,
  bulletData: undefined,
  config: undefined,
  prop: BulletProperty {
    _anyPropertyDirty: false,
    _properties: Map(17) {
      1 => [Property],
      2 => [Property],
      3 => [Property],
      4 => [Property],
      5 => [Property],
      6 => [Property],
      7 => [Property],
      8 => [Property],
      9 => [Property],
      16 => [Property],
      17 => [Property],
      10 => [Property],
      11 => [Property],
      12 => [Property],
      13 => [Property],
      14 => [Property],
      15 => [Property]
    },
    duration: Property {
      _value: 6000,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    delayDestroy: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    attack: Property {
      _value: 1,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    speed: Property {
      _value: 600,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    speedAngle: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    acceleration: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    accelerationAngle: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    orientation: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    orientationParam: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    maxTrackingDuration: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    maxTrackingAngle: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    scale: Property {
      _value: 1,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    color: Property {
      _value: [Color],
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    defaultFacing: Property {
      _value: -90,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    isDestroyOutScreen: Property {
      _value: true,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    isDestructive: Property {
      _value: false,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    isDestructiveOnHit: Property {
      _value: false,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    config: undefined
  },
  eventGroups: []
}
2025-10-9 14:36:10 - log: [PreviewInEditor] create bullet:  <ref *1> Bullet {
  _objFlags: 0,
  _name: '',
  __editorExtras__: {},
  node: <ref *2> Node {
    _objFlags: 0,
    _name: '_bullet_',
    __editorExtras__: {},
    _parent: null,
    _children: [],
    _active: true,
    _components: [
      [UITransform],
      [Circular *1],
      [DefaultMove],
      [Sprite],
      [FBoxCollider],
      [BoxCollider2D]
    ],
    _prefab: PrefabInfo {
      root: [Circular *2],
      asset: [Prefab],
      fileId: 'cceer9/u5JSKedIniAH4fm',
      instance: [PrefabInstance],
      targetOverrides: null,
      nestedPrefabInstanceRoots: null
    },
    _scene: null,
    _activeInHierarchy: false,
    _id: 'f0Ho+y16dLnp1xe5+ZAZHG',
    _eventProcessor: NodeEventProcessor {
      claimedTouchIdList: [],
      maskList: null,
      cachedCameraPriority: 0,
      previousMouseIn: false,
      bubblingTarget: null,
      capturingTarget: null,
      shouldHandleEventMouse: false,
      shouldHandleEventTouch: false,
      _dispatchingTouch: null,
      _isEnabled: false,
      _isMouseLeaveWindow: false,
      _node: [Circular *2]
    },
    _eventMask: 0,
    _siblingIndex: 0,
    _originalSceneId: '',
    _uiProps: NodeUIProperties {
      _uiComp: null,
      _opacity: 1,
      _localOpacity: 1,
      colorDirty: true,
      _uiTransformComp: null,
      _uiSkewComp: null,
      _node: [Circular *2]
    },
    _static: false,
    _lpos: Vec3 { x: 0, y: 0, z: 0 },
    _lrot: Quat { x: 0, y: 0, z: 0, w: 1 },
    _lscale: Vec3 { x: 1, y: 1, z: 1 },
    _mobility: 0,
    _layer: 33554432,
    _euler: Vec3 { x: 0, y: 0, z: 0 },
    _transformFlags: 15,
    _eulerDirty: false,
    _flagChangeVersion: 144,
    _hasChangedFlags: 7,
    _pos: Vec3 { x: 0, y: 0, z: 0 },
    _rot: Quat { x: 0, y: 0, z: 0, w: 1 },
    _scale: Vec3 { x: 1, y: 1, z: 1 },
    _mat: Mat4 {
      m00: 1,
      m01: 0,
      m02: 0,
      m03: 0,
      m04: 0,
      m05: 1,
      m06: 0,
      m07: 0,
      m08: 0,
      m09: 0,
      m10: 1,
      m11: 0,
      m12: 0,
      m13: 0,
      m14: 0,
      m15: 1
    },
    puuid: '33ebf993-63d1-47cf-b113-21b161680e15'
  },
  _enabled: true,
  __prefab: CompPrefabInfo { fileId: '38AJsQOnBNTqyxAE5iOh//' },
  _sceneGetter: null,
  _id: '25XfL/kQ1KfaSZzc+rPTKK',
  entityID: 0,
  isDead: false,
  m_comps: Map(0) {},
  m_tags: 0,
  mover: DefaultMove {
    _objFlags: 0,
    _name: '',
    __editorExtras__: {},
    node: <ref *2> Node {
      _objFlags: 0,
      _name: '_bullet_',
      __editorExtras__: {},
      _parent: null,
      _children: [],
      _active: true,
      _components: [Array],
      _prefab: [PrefabInfo],
      _scene: null,
      _activeInHierarchy: false,
      _id: 'f0Ho+y16dLnp1xe5+ZAZHG',
      _eventProcessor: [NodeEventProcessor],
      _eventMask: 0,
      _siblingIndex: 0,
      _originalSceneId: '',
      _uiProps: [NodeUIProperties],
      _static: false,
      _lpos: [Vec3],
      _lrot: [Quat],
      _lscale: [Vec3],
      _mobility: 0,
      _layer: 33554432,
      _euler: [Vec3],
      _transformFlags: 15,
      _eulerDirty: false,
      _flagChangeVersion: 144,
      _hasChangedFlags: 7,
      _pos: [Vec3],
      _rot: [Quat],
      _scale: [Vec3],
      _mat: [Mat4],
      puuid: '33ebf993-63d1-47cf-b113-21b161680e15'
    },
    _enabled: true,
    __prefab: CompPrefabInfo { fileId: '9cnnRQ0rRDu6dUJerhQ3H6' },
    _sceneGetter: null,
    _id: '2aBmd7F6RJ1bJbrxajjEqE',
    forwardOrientation: -90,
    speed: 100,
    speedAngle: 0,
    acceleration: 0,
    accelerationAngle: 0,
    turnSpeed: 60,
    tiltSpeed: 0,
    tiltOffset: 100,
    orientation: 0,
    orientationType: 0,
    orientationParam: 0,
    _wasVisible: false,
    _isVisible: false,
    _isMovable: true,
    _tiltTime: 0,
    _selfSize: Vec2 { x: 0, y: 0 },
    _position: Vec3 { x: 0, y: 0, z: 0 },
    _basePosition: Vec3 { x: 0, y: 0, z: 0 },
    _visibilityCheckCounter: 0,
    _eventListeners: Map(0) {},
    _trackingTimeLimit: 0,
    _trackingAngleLimit: 180,
    _targetingDelegate: null,
    _target: null
  },
  bulletSprite: Sprite {
    _objFlags: 0,
    _name: '',
    __editorExtras__: {},
    node: <ref *2> Node {
      _objFlags: 0,
      _name: '_bullet_',
      __editorExtras__: {},
      _parent: null,
      _children: [],
      _active: true,
      _components: [Array],
      _prefab: [PrefabInfo],
      _scene: null,
      _activeInHierarchy: false,
      _id: 'f0Ho+y16dLnp1xe5+ZAZHG',
      _eventProcessor: [NodeEventProcessor],
      _eventMask: 0,
      _siblingIndex: 0,
      _originalSceneId: '',
      _uiProps: [NodeUIProperties],
      _static: false,
      _lpos: [Vec3],
      _lrot: [Quat],
      _lscale: [Vec3],
      _mobility: 0,
      _layer: 33554432,
      _euler: [Vec3],
      _transformFlags: 15,
      _eulerDirty: false,
      _flagChangeVersion: 144,
      _hasChangedFlags: 7,
      _pos: [Vec3],
      _rot: [Quat],
      _scale: [Vec3],
      _mat: [Mat4],
      puuid: '33ebf993-63d1-47cf-b113-21b161680e15'
    },
    _enabled: true,
    __prefab: CompPrefabInfo { fileId: 'dfRp+18UNAgZndnJ9STTKM' },
    _sceneGetter: null,
    _id: '92BS2p0xFNT6YGEJFyDVLS',
    _materials: [],
    _materialInstances: [],
    _renderData: null,
    _customMaterial: null,
    _srcBlendFactor: 2,
    _dstBlendFactor: 4,
    _color: Color { _data: [Uint8ClampedArray] },
    _stencilStage: 0,
    _assembler: null,
    _postAssembler: null,
    _renderDataFlag: true,
    _renderFlag: true,
    _instanceMaterialType: -1,
    _srcBlendFactorCache: 2,
    _dstBlendFactorCache: 4,
    _dirtyVersion: -1,
    _internalId: -1,
    _flagChangedVersion: -1,
    _useVertexOpacity: false,
    _lastParent: null,
    _renderEntity: RenderEntity {
      _renderEntityType: 0,
      _dynamicDrawInfoArr: [],
      _node: null,
      _renderTransform: null,
      _stencilStage: 0,
      _useLocal: false,
      _maskMode: 0,
      _color: [Color],
      _localOpacity: 255,
      _colorDirty: true,
      _enabled: false
    },
    _spriteFrame: SpriteFrame {
      _objFlags: 0,
      _name: 'dj_zd_46',
      __editorExtras__: {},
      _callbackTable: [Object: null prototype] {},
      loaded: true,
      _native: '',
      _nativeUrl: '',
      _file: null,
      _ref: 2,
      vertices: [Object],
      uv: [Array],
      unbiasUV: [Array],
      uvSliced: [Array],
      _rect: [Rect],
      _trimmedBorder: [Vec4],
      _offset: [Vec2],
      _originalSize: [Size],
      _rotated: true,
      _capInsets: [Array],
      _atlasUuid: '691e06ea-ee40-40c0-89bb-fab92b54dd9d',
      _texture: [Texture2D],
      _isFlipUVY: false,
      _isFlipUVX: false,
      _original: null,
      _packable: false,
      _pixelsToUnit: 100,
      _pivot: [Vec2],
      _meshType: 0,
      _extrude: 0,
      _customOutLine: [],
      _mesh: null,
      _minPos: [Vec3],
      _maxPos: [Vec3]
    },
    _type: 0,
    _fillType: 0,
    _sizeMode: 1,
    _fillCenter: Vec2 { x: 0, y: 0 },
    _fillStart: 0,
    _fillRange: 0,
    _isTrimmedMode: true,
    _useGrayscale: false,
    _atlas: SpriteAtlas {
      _objFlags: 0,
      _name: 'enemyBullet1',
      __editorExtras__: {},
      _callbackTable: [Object: null prototype] {},
      loaded: true,
      _native: '',
      _nativeUrl: '',
      _file: null,
      _ref: 1,
      spriteFrames: [Object: null prototype]
    }
  },
  collider: FBoxCollider {
    _objFlags: 0,
    _name: '',
    __editorExtras__: {},
    node: <ref *2> Node {
      _objFlags: 0,
      _name: '_bullet_',
      __editorExtras__: {},
      _parent: null,
      _children: [],
      _active: true,
      _components: [Array],
      _prefab: [PrefabInfo],
      _scene: null,
      _activeInHierarchy: false,
      _id: 'f0Ho+y16dLnp1xe5+ZAZHG',
      _eventProcessor: [NodeEventProcessor],
      _eventMask: 0,
      _siblingIndex: 0,
      _originalSceneId: '',
      _uiProps: [NodeUIProperties],
      _static: false,
      _lpos: [Vec3],
      _lrot: [Quat],
      _lscale: [Vec3],
      _mobility: 0,
      _layer: 33554432,
      _euler: [Vec3],
      _transformFlags: 15,
      _eulerDirty: false,
      _flagChangeVersion: 144,
      _hasChangedFlags: 7,
      _pos: [Vec3],
      _rot: [Quat],
      _scale: [Vec3],
      _mat: [Mat4],
      puuid: '33ebf993-63d1-47cf-b113-21b161680e15'
    },
    _enabled: true,
    __prefab: CompPrefabInfo { fileId: 'ffOChq06xK/owFUGDrFa6R' },
    _sceneGetter: null,
    _id: '2e5jkWy7RMVIQCI49eMXwH',
    isConvex: true,
    isEnable: true,
    isImmunityBullet: false,
    isImmunityBulletHurt: false,
    isImmunityCollider: false,
    isImmunityColliderHurt: false,
    entity: null,
    aabb: Rect { x: 0, y: 0, width: 0, height: 0 },
    colliderId: 0,
    _offset: Vec2 { x: 0, y: 0 },
    groupType: 1,
    worldPoints: [ [Vec2], [Vec2], [Vec2], [Vec2] ],
    worldEdge: [],
    _size: Size { width: 50, height: 50 }
  },
  hitEffectPrefab: null,
  isAlive: false,
  elapsedTime: 0,
  emitter: undefined,
  bulletData: undefined,
  config: undefined,
  prop: BulletProperty {
    _anyPropertyDirty: false,
    _properties: Map(17) {
      1 => [Property],
      2 => [Property],
      3 => [Property],
      4 => [Property],
      5 => [Property],
      6 => [Property],
      7 => [Property],
      8 => [Property],
      9 => [Property],
      16 => [Property],
      17 => [Property],
      10 => [Property],
      11 => [Property],
      12 => [Property],
      13 => [Property],
      14 => [Property],
      15 => [Property]
    },
    duration: Property {
      _value: 6000,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    delayDestroy: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    attack: Property {
      _value: 1,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    speed: Property {
      _value: 600,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    speedAngle: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    acceleration: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    accelerationAngle: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    orientation: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    orientationParam: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    maxTrackingDuration: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    maxTrackingAngle: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    scale: Property {
      _value: 1,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    color: Property {
      _value: [Color],
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    defaultFacing: Property {
      _value: -90,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    isDestroyOutScreen: Property {
      _value: true,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    isDestructive: Property {
      _value: false,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    isDestructiveOnHit: Property {
      _value: false,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    config: undefined
  },
  eventGroups: []
}
2025-10-9 14:36:10 - log: [PreviewInEditor] create bullet:  <ref *1> Bullet {
  _objFlags: 0,
  _name: '',
  __editorExtras__: {},
  node: <ref *2> Node {
    _objFlags: 0,
    _name: '_bullet_',
    __editorExtras__: {},
    _parent: null,
    _children: [],
    _active: true,
    _components: [
      [UITransform],
      [Circular *1],
      [DefaultMove],
      [Sprite],
      [FBoxCollider],
      [BoxCollider2D]
    ],
    _prefab: PrefabInfo {
      root: [Circular *2],
      asset: [Prefab],
      fileId: 'cceer9/u5JSKedIniAH4fm',
      instance: [PrefabInstance],
      targetOverrides: null,
      nestedPrefabInstanceRoots: null
    },
    _scene: null,
    _activeInHierarchy: false,
    _id: '01u4rdec1H0oN2U1JnKEpF',
    _eventProcessor: NodeEventProcessor {
      claimedTouchIdList: [],
      maskList: null,
      cachedCameraPriority: 0,
      previousMouseIn: false,
      bubblingTarget: null,
      capturingTarget: null,
      shouldHandleEventMouse: false,
      shouldHandleEventTouch: false,
      _dispatchingTouch: null,
      _isEnabled: false,
      _isMouseLeaveWindow: false,
      _node: [Circular *2]
    },
    _eventMask: 0,
    _siblingIndex: 0,
    _originalSceneId: '',
    _uiProps: NodeUIProperties {
      _uiComp: null,
      _opacity: 1,
      _localOpacity: 1,
      colorDirty: true,
      _uiTransformComp: null,
      _uiSkewComp: null,
      _node: [Circular *2]
    },
    _static: false,
    _lpos: Vec3 { x: 0, y: 0, z: 0 },
    _lrot: Quat { x: 0, y: 0, z: 0, w: 1 },
    _lscale: Vec3 { x: 1, y: 1, z: 1 },
    _mobility: 0,
    _layer: 33554432,
    _euler: Vec3 { x: 0, y: 0, z: 0 },
    _transformFlags: 15,
    _eulerDirty: false,
    _flagChangeVersion: 144,
    _hasChangedFlags: 7,
    _pos: Vec3 { x: 0, y: 0, z: 0 },
    _rot: Quat { x: 0, y: 0, z: 0, w: 1 },
    _scale: Vec3 { x: 1, y: 1, z: 1 },
    _mat: Mat4 {
      m00: 1,
      m01: 0,
      m02: 0,
      m03: 0,
      m04: 0,
      m05: 1,
      m06: 0,
      m07: 0,
      m08: 0,
      m09: 0,
      m10: 1,
      m11: 0,
      m12: 0,
      m13: 0,
      m14: 0,
      m15: 1
    },
    puuid: '33ebf993-63d1-47cf-b113-21b161680e15'
  },
  _enabled: true,
  __prefab: CompPrefabInfo { fileId: '38AJsQOnBNTqyxAE5iOh//' },
  _sceneGetter: null,
  _id: '1caPkN6HhKDJT2Crkt4Z8b',
  entityID: 0,
  isDead: false,
  m_comps: Map(0) {},
  m_tags: 0,
  mover: DefaultMove {
    _objFlags: 0,
    _name: '',
    __editorExtras__: {},
    node: <ref *2> Node {
      _objFlags: 0,
      _name: '_bullet_',
      __editorExtras__: {},
      _parent: null,
      _children: [],
      _active: true,
      _components: [Array],
      _prefab: [PrefabInfo],
      _scene: null,
      _activeInHierarchy: false,
      _id: '01u4rdec1H0oN2U1JnKEpF',
      _eventProcessor: [NodeEventProcessor],
      _eventMask: 0,
      _siblingIndex: 0,
      _originalSceneId: '',
      _uiProps: [NodeUIProperties],
      _static: false,
      _lpos: [Vec3],
      _lrot: [Quat],
      _lscale: [Vec3],
      _mobility: 0,
      _layer: 33554432,
      _euler: [Vec3],
      _transformFlags: 15,
      _eulerDirty: false,
      _flagChangeVersion: 144,
      _hasChangedFlags: 7,
      _pos: [Vec3],
      _rot: [Quat],
      _scale: [Vec3],
      _mat: [Mat4],
      puuid: '33ebf993-63d1-47cf-b113-21b161680e15'
    },
    _enabled: true,
    __prefab: CompPrefabInfo { fileId: '9cnnRQ0rRDu6dUJerhQ3H6' },
    _sceneGetter: null,
    _id: '37iC8NerVHIqRFWZ/UlRdP',
    forwardOrientation: -90,
    speed: 100,
    speedAngle: 0,
    acceleration: 0,
    accelerationAngle: 0,
    turnSpeed: 60,
    tiltSpeed: 0,
    tiltOffset: 100,
    orientation: 0,
    orientationType: 0,
    orientationParam: 0,
    _wasVisible: false,
    _isVisible: false,
    _isMovable: true,
    _tiltTime: 0,
    _selfSize: Vec2 { x: 0, y: 0 },
    _position: Vec3 { x: 0, y: 0, z: 0 },
    _basePosition: Vec3 { x: 0, y: 0, z: 0 },
    _visibilityCheckCounter: 0,
    _eventListeners: Map(0) {},
    _trackingTimeLimit: 0,
    _trackingAngleLimit: 180,
    _targetingDelegate: null,
    _target: null
  },
  bulletSprite: Sprite {
    _objFlags: 0,
    _name: '',
    __editorExtras__: {},
    node: <ref *2> Node {
      _objFlags: 0,
      _name: '_bullet_',
      __editorExtras__: {},
      _parent: null,
      _children: [],
      _active: true,
      _components: [Array],
      _prefab: [PrefabInfo],
      _scene: null,
      _activeInHierarchy: false,
      _id: '01u4rdec1H0oN2U1JnKEpF',
      _eventProcessor: [NodeEventProcessor],
      _eventMask: 0,
      _siblingIndex: 0,
      _originalSceneId: '',
      _uiProps: [NodeUIProperties],
      _static: false,
      _lpos: [Vec3],
      _lrot: [Quat],
      _lscale: [Vec3],
      _mobility: 0,
      _layer: 33554432,
      _euler: [Vec3],
      _transformFlags: 15,
      _eulerDirty: false,
      _flagChangeVersion: 144,
      _hasChangedFlags: 7,
      _pos: [Vec3],
      _rot: [Quat],
      _scale: [Vec3],
      _mat: [Mat4],
      puuid: '33ebf993-63d1-47cf-b113-21b161680e15'
    },
    _enabled: true,
    __prefab: CompPrefabInfo { fileId: 'dfRp+18UNAgZndnJ9STTKM' },
    _sceneGetter: null,
    _id: 'afQj97r8FJlrrzcbpJxQZS',
    _materials: [],
    _materialInstances: [],
    _renderData: null,
    _customMaterial: null,
    _srcBlendFactor: 2,
    _dstBlendFactor: 4,
    _color: Color { _data: [Uint8ClampedArray] },
    _stencilStage: 0,
    _assembler: null,
    _postAssembler: null,
    _renderDataFlag: true,
    _renderFlag: true,
    _instanceMaterialType: -1,
    _srcBlendFactorCache: 2,
    _dstBlendFactorCache: 4,
    _dirtyVersion: -1,
    _internalId: -1,
    _flagChangedVersion: -1,
    _useVertexOpacity: false,
    _lastParent: null,
    _renderEntity: RenderEntity {
      _renderEntityType: 0,
      _dynamicDrawInfoArr: [],
      _node: null,
      _renderTransform: null,
      _stencilStage: 0,
      _useLocal: false,
      _maskMode: 0,
      _color: [Color],
      _localOpacity: 255,
      _colorDirty: true,
      _enabled: false
    },
    _spriteFrame: SpriteFrame {
      _objFlags: 0,
      _name: 'dj_zd_46',
      __editorExtras__: {},
      _callbackTable: [Object: null prototype] {},
      loaded: true,
      _native: '',
      _nativeUrl: '',
      _file: null,
      _ref: 2,
      vertices: [Object],
      uv: [Array],
      unbiasUV: [Array],
      uvSliced: [Array],
      _rect: [Rect],
      _trimmedBorder: [Vec4],
      _offset: [Vec2],
      _originalSize: [Size],
      _rotated: true,
      _capInsets: [Array],
      _atlasUuid: '691e06ea-ee40-40c0-89bb-fab92b54dd9d',
      _texture: [Texture2D],
      _isFlipUVY: false,
      _isFlipUVX: false,
      _original: null,
      _packable: false,
      _pixelsToUnit: 100,
      _pivot: [Vec2],
      _meshType: 0,
      _extrude: 0,
      _customOutLine: [],
      _mesh: null,
      _minPos: [Vec3],
      _maxPos: [Vec3]
    },
    _type: 0,
    _fillType: 0,
    _sizeMode: 1,
    _fillCenter: Vec2 { x: 0, y: 0 },
    _fillStart: 0,
    _fillRange: 0,
    _isTrimmedMode: true,
    _useGrayscale: false,
    _atlas: SpriteAtlas {
      _objFlags: 0,
      _name: 'enemyBullet1',
      __editorExtras__: {},
      _callbackTable: [Object: null prototype] {},
      loaded: true,
      _native: '',
      _nativeUrl: '',
      _file: null,
      _ref: 1,
      spriteFrames: [Object: null prototype]
    }
  },
  collider: FBoxCollider {
    _objFlags: 0,
    _name: '',
    __editorExtras__: {},
    node: <ref *2> Node {
      _objFlags: 0,
      _name: '_bullet_',
      __editorExtras__: {},
      _parent: null,
      _children: [],
      _active: true,
      _components: [Array],
      _prefab: [PrefabInfo],
      _scene: null,
      _activeInHierarchy: false,
      _id: '01u4rdec1H0oN2U1JnKEpF',
      _eventProcessor: [NodeEventProcessor],
      _eventMask: 0,
      _siblingIndex: 0,
      _originalSceneId: '',
      _uiProps: [NodeUIProperties],
      _static: false,
      _lpos: [Vec3],
      _lrot: [Quat],
      _lscale: [Vec3],
      _mobility: 0,
      _layer: 33554432,
      _euler: [Vec3],
      _transformFlags: 15,
      _eulerDirty: false,
      _flagChangeVersion: 144,
      _hasChangedFlags: 7,
      _pos: [Vec3],
      _rot: [Quat],
      _scale: [Vec3],
      _mat: [Mat4],
      puuid: '33ebf993-63d1-47cf-b113-21b161680e15'
    },
    _enabled: true,
    __prefab: CompPrefabInfo { fileId: 'ffOChq06xK/owFUGDrFa6R' },
    _sceneGetter: null,
    _id: '70LLF4uvlD5La4cdJoSzxZ',
    isConvex: true,
    isEnable: true,
    isImmunityBullet: false,
    isImmunityBulletHurt: false,
    isImmunityCollider: false,
    isImmunityColliderHurt: false,
    entity: null,
    aabb: Rect { x: 0, y: 0, width: 0, height: 0 },
    colliderId: 0,
    _offset: Vec2 { x: 0, y: 0 },
    groupType: 1,
    worldPoints: [ [Vec2], [Vec2], [Vec2], [Vec2] ],
    worldEdge: [],
    _size: Size { width: 50, height: 50 }
  },
  hitEffectPrefab: null,
  isAlive: false,
  elapsedTime: 0,
  emitter: undefined,
  bulletData: undefined,
  config: undefined,
  prop: BulletProperty {
    _anyPropertyDirty: false,
    _properties: Map(17) {
      1 => [Property],
      2 => [Property],
      3 => [Property],
      4 => [Property],
      5 => [Property],
      6 => [Property],
      7 => [Property],
      8 => [Property],
      9 => [Property],
      16 => [Property],
      17 => [Property],
      10 => [Property],
      11 => [Property],
      12 => [Property],
      13 => [Property],
      14 => [Property],
      15 => [Property]
    },
    duration: Property {
      _value: 6000,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    delayDestroy: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    attack: Property {
      _value: 1,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    speed: Property {
      _value: 600,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    speedAngle: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    acceleration: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    accelerationAngle: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    orientation: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    orientationParam: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    maxTrackingDuration: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    maxTrackingAngle: Property {
      _value: 0,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    scale: Property {
      _value: 1,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    color: Property {
      _value: [Color],
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    defaultFacing: Property {
      _value: -90,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    isDestroyOutScreen: Property {
      _value: true,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    isDestructive: Property {
      _value: false,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    isDestructiveOnHit: Property {
      _value: false,
      _writeMask: 0,
      _isDirty: false,
      _listeners: [],
      _onModified: [Function (anonymous)]
    },
    config: undefined
  },
  eventGroups: []
}
2025-10-9 14:36:13 - info: [PreviewInEditor] 预览环境初始化完毕
2025-10-9 14:37:17 - log: [Scene] start init_cs_proto.js
2025-10-9 14:39:14 - log: [Scene] start init_cs_proto.js
2025-10-9 14:39:16 - log: [PreviewInEditor] meshopt wasm decoder initialized
2025-10-9 14:39:16 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
2025-10-9 14:39:16 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
2025-10-9 14:39:16 - log: [PreviewInEditor] [PHYSICS]: using builtin.
2025-10-9 14:39:16 - log: [PreviewInEditor] Cocos Creator v3.8.6
2025-10-9 14:39:17 - log: [PreviewInEditor] start init_cs_proto.js
2025-10-9 14:39:17 - log: [PreviewInEditor] Using custom pipeline: Builtin
2025-10-9 14:39:17 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-10-9 14:39:17 - log: [PreviewInEditor] [2025/10/09 14:39:17] [INFO] NetMgr Network manager initialized
2025-10-9 14:39:17 - log: [PreviewInEditor] [2025/10/09 14:39:17] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_SESSION
2025-10-9 14:39:17 - log: [PreviewInEditor] [2025/10/09 14:39:17] [INFO] NetMgr Registered handler for msgId: CS_CMD_HEARTBEAT
2025-10-9 14:39:17 - log: [PreviewInEditor] [2025/10/09 14:39:17] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_ROLE
2025-10-9 14:39:17 - warn: [PreviewInEditor] 没有找到子弹父节点请检查路径:Canvas/bullet_rootError: [PreviewInEditor] 没有找到子弹父节点请检查路径:Canvas/bullet_root
    at console.warn (C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\node_modules\@sentry\src\instrument\console.ts:40:20)
    at Function.init (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/d7/d7f2169f5673db219f871f7e98c93ef6ea4327e6.js:72:25)
    at PlaneEditor.init (file:///E:/M2Game/Client/temp/programming/packer-driver/targets/editor/chunks/7f/7f9ed24194562105bcb6d37ac2a847b7201a12a0.js:162:45)
2025-10-9 14:39:17 - log: [PreviewInEditor] Spine: Animation not found: Idle
2025-10-9 14:39:30 - info: [PreviewInEditor] 预览环境初始化完毕
2025-10-9 14:39:49 - log: [Scene] start init_cs_proto.js
2025-10-9 14:39:50 - log: [Scene] start init_cs_proto.js
2025-10-9 14:40:01 - log: [PreviewInEditor] meshopt wasm decoder initialized
2025-10-9 14:40:01 - log: [PreviewInEditor] [box2d]:box2d wasm lib loaded.
2025-10-9 14:40:01 - log: [PreviewInEditor] [bullet]:bullet wasm lib loaded.
2025-10-9 14:40:01 - log: [PreviewInEditor] [PHYSICS]: using builtin.
2025-10-9 14:40:01 - log: [PreviewInEditor] Cocos Creator v3.8.6
2025-10-9 14:40:02 - log: [PreviewInEditor] start init_cs_proto.js
2025-10-9 14:40:02 - log: [PreviewInEditor] Using custom pipeline: Builtin
2025-10-9 14:40:02 - log: [PreviewInEditor] [PHYSICS2D]: switch from box2d-wasm to builtin.
2025-10-9 14:40:02 - log: [PreviewInEditor] [2025/10/09 14:40:02] [INFO] NetMgr Network manager initialized
2025-10-9 14:40:02 - log: [PreviewInEditor] [2025/10/09 14:40:02] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_SESSION
2025-10-9 14:40:02 - log: [PreviewInEditor] [2025/10/09 14:40:02] [INFO] NetMgr Registered handler for msgId: CS_CMD_HEARTBEAT
2025-10-9 14:40:02 - log: [PreviewInEditor] [2025/10/09 14:40:02] [INFO] NetMgr Registered handler for msgId: CS_CMD_GET_ROLE
2025-10-9 14:40:02 - log: [PreviewInEditor] Spine: Animation not found: Idle
2025-10-9 14:40:14 - info: [PreviewInEditor] 预览环境初始化完毕

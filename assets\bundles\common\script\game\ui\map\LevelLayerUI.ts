import { _decorator, Component, instantiate, js, JsonAsset, log, Node, Prefab, Sprite, SpriteFrame, UITransform, v2, Vec2, view } from "cc";
import { MyApp } from 'db://assets/bundles/common/script/app/MyApp';
import { LayerSplicingMode, LevelDataEvent, LevelDataLayer, LevelDataScroll, LevelDataTerrain } from "db://assets/bundles/common/script/leveldata/leveldata";
import { LevelEventRun } from "./LevelEventRun"
import { Tools } from "db://assets/bundles/common/script/game/utils/Tools";
import { GameIns } from "db://assets/bundles/common/script/game/GameIns";
import { logDebug, logError, logInfo } from "db://assets/scripts/utils/Logger";
import { GameConst } from "db://assets/scripts/core/base/GameConst";
import { LevelNodeCheckOutScreen } from "./LevelNodeCheckOutScreen";
import { LevelUtils } from "./LevelUtils";
import { EmittierStatus, EmittierTerrain } from "db://assets/bundles/common/script/game/dyncTerrain/EmittierTerrain";
import { LevelLayer } from "./LevelLayer";

const { ccclass } = _decorator;

const SCROLL_POOL_NAME = "scroll_pool";
const TERRAIN_POOL_NAME = "terrain_pool";
const EMIITER_POOL_NAME = "emittier_pool";

const TerrainsNodeName = "terrains";
const DynamicNodeName = "dynamic";
const ScrollsNodeName = "scrolls";
const EmittiersNodeName = "emittiers";

const jsonRootPath: string = 'game/level/background/Prefab/Config/';

interface TerrainInfo {
    data: LevelDataTerrain;
    parentNode: Node;
    zIndex: number; // 在父节点中的顺序，用于层级排序
}

@ccclass('LevelLayerUI')
export class LevelLayerUI extends LevelLayer {
    private _offSetY: number = 0; // 当前关卡的偏移量

    private terrainsNode: Node | null = null;
    private dynamicNode: Node | null = null;
    private scrollsNode: Node | null = null;
    private emittiersNode: Node | null = null;

    private _scorllData: LevelDataScroll | null = null;
    private _scorllPrefabs: Prefab[] = []; // 已经加载的
    private _lastScrollNodeHeight: number = 0;

    private _emittierInfo: LevelDataTerrain[] = [];
    private _inactiveEmittierNodes: Node[] = []; // 未激活的发射器节点列表
    private _insEmittierLock: boolean = false;

    // 当前关卡的地形信息，用于动态实例化
    private _terrainsInfo: TerrainInfo[] = []; // 地形基础元素，单个terrain根据Y坐标排序（背景、滚动层、发射器单独处理）
    private _insTerrainLock: boolean = false;

    private events: LevelDataEvent[] = [];
    private eventRunners: LevelEventRun[] = [];

    onLoad(): void {

    }

    public async initByLevelData(data: LevelDataLayer, offSetY: number, bFirstLoad: boolean): Promise<void> {
        this._offSetY = offSetY;
        this.node.setPosition(0, offSetY, 0);

        this.terrainsNode = LevelUtils.getOrAddNode(this.node, TerrainsNodeName);
        this.dynamicNode = LevelUtils.getOrAddNode(this.node, DynamicNodeName);
        this.scrollsNode = LevelUtils.getOrAddNode(this.node, ScrollsNodeName);
        this.emittiersNode = LevelUtils.getOrAddNode(this.node, EmittiersNodeName);

        this._scorllPrefabs = [];
        this._terrainsInfo = [];
        this._emittierInfo = [];
        this._inactiveEmittierNodes = [];
        this._insTerrainLock = false;
        this._insEmittierLock = false;
        // 首关基础地形只加载一屏半的资源，剩余的动态加载
        // ------------------------------------------
        await this._initTerrainsByLevelData(data, bFirstLoad);
        await this._initDynamicsByLevelData(data, bFirstLoad);
        await this._initEmittierLevelData(data, bFirstLoad);
        // ------------------------------------------
        await this._initScorllsByLevelData(data, bFirstLoad);
        
        this._sortTerrainsInfo();
        
        this.events = [...data.events]
        this.events.sort((a, b) => a.position.y - b.position.y);
        this.events.forEach((event) => {
            this.eventRunners.push(new LevelEventRun(event, this));
        });
    }

    private async _initTerrainsByLevelData(data: LevelDataLayer, bFirstLoad: boolean): Promise<void> {
        if (!data || this.terrainsNode === null || data.terrains.length === 0) { return; }  

        logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name} 固定地形初始化：${data.remark}`);
        let zIndex = 0;
        for (const terrain of data.terrains){
            if (bFirstLoad) {
                if (terrain.type.length > 0) {
                    if (terrain.type.endsWith('.json')) {
                        const json_path = jsonRootPath + terrain.type.replace('.json', '');
                        logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name}] jsonData : ${LevelUtils.extractPathPart(json_path)}`);
                        const jsonAsset = await MyApp.resMgr.loadAsync(json_path, JsonAsset);
                        const jsonData = jsonAsset.json;
                        if (jsonData!.terrains && Array.isArray(jsonData!.terrains)) {
                            let subZIndex = 0;
                            const subParentNode = LevelUtils.getOrAddNode(this.terrainsNode!, `terr_${zIndex}`); 
                            logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name}] init terrain 创建节点 subParentNode: ${subParentNode.name} 父节点：${subParentNode.parent!.name}`);
                            subParentNode.setSiblingIndex(zIndex);
                            for (const t of jsonData!.terrains) {
                                const terrainData = Object.assign(new LevelDataTerrain(), t);
                                const terrainPosY = terrain.position.y + terrainData.position.y;
                                if ((terrainPosY - terrainData.height / 2 + this.node.position.y) <= GameConst.VIEWPORT_LOAD_POS) {
                                    if (terrainData.type.endsWith('.png')) {
                                        const png_path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrainData.type.replace('.png', ''));
                                        logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name}] 静态元素(通过json配置) png : ${LevelUtils.extractPathPart(png_path)}`);
                                        await this._createPngNode(
                                            png_path, v2(terrain.position.x + terrainData.position.x, terrainPosY), v2(terrainData.scale.x, terrainData.scale.y),
                                            terrainData.rotation, subParentNode, subZIndex);
                                    } else {
                                        const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrainData.uuid);
                                        const prefabNode = await this._createTerrainPrefabNode(
                                            path, v2(terrain.position.x + terrainData.position.x, terrainPosY), v2(terrainData.scale.x, terrainData.scale.y),
                                            terrainData.rotation, subParentNode, subZIndex);      
                                        logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name}] 静态元素(通过json配置) prefab : ${prefabNode!.name}`);
                                    }
                                } else {
                                    terrainData.position.y = terrainPosY;
                                    terrainData.position.x += terrain.position.x;
                                    terrainData.subParentNode = subParentNode;
                                    this._addTerrainInfo(terrainData, subParentNode, subZIndex);
                                }
                                subZIndex++;                             
                            }
                        } else {
                            logError("LevelLayerUI", "JSON data has no terrains array");
                        }
                    } else if (terrain.type.endsWith('.png')) {      
                        if (((terrain.position.y - terrain.height / 2) + this.node.position.y) <= GameConst.VIEWPORT_LOAD_POS) { 
                            const png_path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrain.type.replace('.png', ''));
                            await this._createPngNode(
                                png_path, v2(terrain.position.x, terrain.position.y), v2(terrain.scale.x, terrain.scale.y),
                                terrain.rotation, this.terrainsNode!, zIndex);
                            logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name}] 静态元素 png : ${LevelUtils.extractPathPart(png_path)}`);
                        } else {
                            this._addTerrainInfo(terrain, this.terrainsNode!, zIndex);
                        }
                    }
                } else {
                    if ((terrain.position.y - terrain.height / 2 + this.node.position.y) <= GameConst.VIEWPORT_LOAD_POS) {
                        const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrain.uuid);
                        const prefabNode = await this._createTerrainPrefabNode(
                            path, v2(terrain.position.x, terrain.position.y), v2(terrain.scale.x, terrain.scale.y),
                            terrain.rotation, this.terrainsNode!, zIndex);             
                        logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name}] 静态元素 prefab Node: ${prefabNode!.name}`);
                    } else {
                        this._addTerrainInfo(terrain, this.terrainsNode!, zIndex);
                    }
                }  
            } else {
                this._addTerrainInfo(terrain, this.terrainsNode!, zIndex);
            }
            zIndex++;        
        }
    }

    private async _initDynamicsByLevelData(data: LevelDataLayer, bFirstLoad: boolean): Promise<void> {
        if (!data || this.dynamicNode === null || data.dynamics.length === 0) { return; } 
        
        logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name} 随机地形初始化：${data.remark}`);
        let zIndex = 0;
        for (const dynamics of data.dynamics) { 
            let weights: number[] = [];
            dynamics.group.forEach((dynamic) => {
                weights.push(dynamic.weight);
            });
            const dynaIndex = Tools.getRandomIndexByWeights(weights, GameIns.battleManager.random());
            const dynamic = dynamics.group[dynaIndex];

            weights = [];
            dynamic.terrains.forEach((terrain) => {
                weights.push(terrain.weight);
            });
            const terrainIndex = Tools.getRandomIndexByWeights(weights, GameIns.battleManager.random());
            const terrain = dynamic.terrains[terrainIndex];
            
            const randomOffsetX = GameIns.battleManager.random() * (terrain.offSetX.max - terrain.offSetX.min) + terrain.offSetX.min;   
            const randomOffsetY = GameIns.battleManager.random() * (terrain.offSetY.max - terrain.offSetY.min) + terrain.offSetY.min; 
            
            if (!this.node.parent || !this.node.parent.parent) {
                console.log("LevelLayerUI", "this.node.parent || !this.node.parent.parent");
                return;
            }
            if (bFirstLoad) {
                if (terrain.type.length > 0) {
                    logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name}] 随机出的地形 : ${LevelUtils.extractPathPart(MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrain.uuid))}`);
                    if (terrain.type.endsWith('.json')) {
                        const json_path = jsonRootPath + terrain.type.replace('.json', '');
                        logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name}] jsonData : ${LevelUtils.extractPathPart(json_path)}`);
                        const jsonAsset = await MyApp.resMgr.loadAsync(json_path, JsonAsset);
                        const jsonData = jsonAsset.json;
                        if (jsonData!.terrains && Array.isArray(jsonData!.terrains)) {
                            let subZIndex = 0;
                            const subParentNode = LevelUtils.getOrAddNode(this.dynamicNode!, `dyna_${zIndex}`);
                            logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name}] init dyna 创建节点 subParentNode: ${subParentNode.name} 父节点：${subParentNode.parent!.name}`);
                            subParentNode.setSiblingIndex(zIndex);
                            for (const t of jsonData!.terrains) {
                                const terrainData = Object.assign(new LevelDataTerrain(), t);  
                                //logDebug("LevelLayerUI", `jsonData.terrains: ${JSON.stringify(terrainData)}`);
                                const terrainPosY = dynamic.position.y + randomOffsetY + terrainData.position.y;
                                const curOffPosY = terrainPosY - terrainData.height / 2;
                                const curPosY = curOffPosY + this.node.position.y;
                                logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name}] 父节点坐标：${this.node.position.y} 子节偏移坐标：${curOffPosY}`);
                                if ((curPosY) <= GameConst.VIEWPORT_LOAD_POS) {
                                    if (terrainData.type.endsWith('.png')) {
                                        const png_path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrainData.type.replace('.png', ''));
                                        logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name}] 随机元素(通过json配置) png : ${LevelUtils.extractPathPart(png_path)}`);
                                        await this._createPngNode(
                                            png_path, v2(dynamic.position.x + randomOffsetX + terrainData.position.x, terrainPosY), v2(terrainData.scale.x, terrainData.scale.y),
                                            terrainData.rotation, subParentNode, subZIndex);
                                    } else {
                                        const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrainData.uuid); 
                                        const prefabNode = await this._createTerrainPrefabNode(
                                            path, v2(dynamic.position.x + randomOffsetX + terrainData.position.x, terrainPosY), v2(terrainData.scale.x, terrainData.scale.y),
                                            terrainData.rotation, subParentNode, subZIndex);
                                        logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name}] 随机元素(通过json配置) prefab : ${prefabNode.name}`);
                                    }
                                } else {
                                    this._addTerrainInfo({
                                        uuid: terrainData.uuid,
                                        type: terrainData.type,
                                        height: terrainData.height,
                                        position: v2(dynamic.position.x + randomOffsetX + terrainData.position.x, terrainPosY),
                                        scale: v2(terrainData.scale.x, terrainData.scale.y),
                                        rotation: terrainData.rotation,
                                    }, subParentNode, subZIndex);
                                }
                                subZIndex++;
                            }
                        } else {
                            logError("LevelLayerUI", "JSON data has no terrains array");
                        }
                    } else if (terrain.type.endsWith('.png')) {       
                        if ((dynamic.position.y - terrain.height / 2 + randomOffsetY + this.node.position.y) <= GameConst.VIEWPORT_LOAD_POS) {
                            const png_path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrain.type.replace('.png', ''));
                            logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name}] 随机元素 png : ${LevelUtils.extractPathPart(png_path)}`);
                            await this._createPngNode(
                                png_path, v2(dynamic.position.x + randomOffsetX, dynamic.position.y + randomOffsetY), v2(dynamic.scale.x, dynamic.scale.y),
                                dynamic.rotation, this.dynamicNode!, zIndex);
                        } else {
                            this._addTerrainInfo({
                                uuid: terrain.uuid,
                                type: terrain.type,
                                height: terrain.height,
                                position: v2(dynamic.position.x + randomOffsetX , dynamic.position.y + randomOffsetY),
                                scale: v2(dynamic.scale.x, dynamic.scale.y),
                                rotation: dynamic.rotation,
                            }, this.dynamicNode!, zIndex);
                        }
                    }
                } else {
                    if ((dynamic.position.y + randomOffsetX - terrain.height / 2 + this.node.position.y) <= GameConst.VIEWPORT_LOAD_POS) { 
                        const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrain.uuid)
                        const prefabNode = await this._createTerrainPrefabNode(
                            path, v2(dynamic.position.x + randomOffsetX, dynamic.position.y + randomOffsetY), v2(dynamic.scale.x, dynamic.scale.y), 
                            dynamic.rotation, this.dynamicNode!, zIndex);
                        logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name}] 随机元素 prefab : ${prefabNode.name}`);
                    } else {
                        this._addTerrainInfo({
                            uuid: terrain.uuid,
                            type: '',
                            height: terrain.height,
                            position: v2(dynamic.position.x + randomOffsetX, dynamic.position.y + randomOffsetY),
                            scale: v2(dynamic.scale.x, dynamic.scale.y),
                            rotation: dynamic.rotation,
                        }, this.dynamicNode!, zIndex);
                    }
                }
            } else {
                this._addTerrainInfo({
                    uuid: terrain.uuid,
                    type: terrain.type,
                    height: terrain.height,
                    position: v2(dynamic.position.x + randomOffsetX, dynamic.position.y + randomOffsetY),
                    scale: v2(dynamic.scale.x, dynamic.scale.y),
                    rotation: dynamic.rotation,
                }, this.dynamicNode!, zIndex);
            }

            zIndex++;
        }
    }

    private async _initEmittierLevelData(data: LevelDataLayer, bFristLevel: boolean): Promise<void> {
        if (!data || this.emittiersNode === null || data.emittiers.length === 0) { return; } 
 
        let index = 0;
        for (const emittier of data.emittiers) {
            if (bFristLevel) {
                if (emittier.position.y - emittier.height / 2 + this.node.position.y < GameConst.VIEWPORT_LOAD_POS) {
                    const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, emittier.uuid);
                    const prefab = await this._createEmittierNode(
                        path, v2(emittier.position.x, emittier.position.y), v2(emittier.scale.x, emittier.scale.y), 
                        emittier.rotation, index);
                    logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name}] 发射器 prefab : ${prefab.name}`);
                } else {
                    this._addEmittierInfo(emittier);
                }
            } else {
                this._addEmittierInfo(emittier);
            }
            index++;  
        }

        this._emittierInfo.sort((a, b) => {
            return a.position.y - b.position.y;
        });
    }

    public async _initScorllsByLevelData(data: LevelDataLayer, bFristLevel: boolean):Promise<void> {
        if (!data || this.scrollsNode === null || data.scrolls.length === 0) { return; } 

        // 根据权重随机出一个滚动组
        const weights: number[] = [];
        data.scrolls.forEach(element => {
            weights.push(element.weight);
        });
        const srocllIndex = Tools.getRandomIndexByWeights(weights, GameIns.battleManager.random());
        this._scorllData = data.scrolls[srocllIndex];

        for (let i = 0; i < this._scorllData.uuids.length; i++) {
            const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, this._scorllData.uuids[i]);
            const prefab = await MyApp.resMgr.loadAsync(path, Prefab);
            this._scorllPrefabs.push(prefab);
        }
        
        var offSetY = 0;
        var halfHeight = 0;
        while (this._scorllPrefabs.length > 0 && (offSetY - halfHeight) < GameConst.VIEWPORT_LOAD_POS) {
            const randomOffsetX = GameIns.battleManager.random() * (this._scorllData.offSetX!.max - this._scorllData.offSetX!.min) + this._scorllData.offSetX!.min;
            const node = this._addScrollNode(randomOffsetX, offSetY);
            var nodeHeight = 0;
            if (this._scorllData.splicingMode === LayerSplicingMode.node_height) {    
                nodeHeight = node.getComponent(UITransform)!.contentSize.height;
            } else if (this._scorllData.splicingMode === LayerSplicingMode.fix_height) {
                nodeHeight = 1334;
            } else if (this._scorllData.splicingMode === LayerSplicingMode.random_height) {
                nodeHeight = Math.max(this._scorllData.offSetY!.min,this._scorllData.offSetY!.max) + node.getComponent(UITransform)!.contentSize.height;
            }
            
            offSetY += nodeHeight;
            halfHeight = nodeHeight / 2;
            this._lastScrollNodeHeight = nodeHeight;
        }
    }

    public tick(deltaTime: number): void {
        let posY = this.node.position.y;
        posY -= deltaTime * this.speed;
        this.node.setPosition(0, posY, 0);

        // 说明: event的激活，是从进入世界范围开始。
        // 又：因为游戏是从上往下滚动，因为我们用一个固定大小变量定义上边界，一旦进入这个范围，就激活。
        const scrollY =  -posY + GameConst.VIEWPORT_TOP;
        // console.log('LevelLayerUI tick scrollY', scrollY, -posY, this._offSetY, GameConst.VIEWPORT_TOP);
        for (let i = 0; i < this.eventRunners.length; i++) {
            const eventRunner = this.eventRunners[i];
            eventRunner.tick(scrollY);
            if (eventRunner.isTriggered) {
                // 条件已触发
                this.eventRunners.splice(i, 1);
                i--;
            }
        }

        if (!this._insTerrainLock) {
            this._checkDynaTerrain(posY);
        }
        this._checkScroll();
        if (this._insEmittierLock) {
            this._checkEmittier(posY);
        }
    }

    private _sortTerrainsInfo() {
        this._terrainsInfo.sort((a, b) => {
            return a.data.position.y - b.data.position.y;
        });
        
        logInfo("LevelLayerUI", `${this.node.name} 地形元素数量: ${this._terrainsInfo.length}`);
        /*this._terrainsInfo.forEach((terrain, index) => {
            logInfo("LevelLayerUI", `[${index}] Y坐标: ${terrain.data.position.y} 元素类型：${terrain.parentNode} uuid:${terrain.data.uuid} json:${terrain.data.type}`);
        });*/
    }

    private _addTerrainInfo(
        terrainData: {uuid: string, type: string, height: number, position: Vec2, scale: Vec2, rotation: number},
        parentNode: Node,
        zIndex: number
    ) {
        let attr = '';
        let elemName = '';
        if (terrainData.type.length > 0) {
            attr = '图片';
            elemName = LevelUtils.extractPathPart(MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrainData.type.replace('.png', '')));
        } else {
            attr = '预制体';
            elemName = LevelUtils.extractPathPart(MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrainData.uuid),'');
        }
        logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name}] 添加到预加载地形组 类型: ${attr} 元素: ${elemName}  父节点：${parentNode.name}  zIndex: ${zIndex}`);
        this._terrainsInfo.push({
            data: terrainData,
            parentNode: parentNode,
            zIndex: zIndex
        });
    }

    private _addEmittierInfo( terrainData: LevelDataTerrain ) {
        this._emittierInfo.push(terrainData);
        logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name}] 添加到预加载发射器组 元素: ${LevelUtils.extractPathPart(MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrainData.uuid),'')}`);
    }

    // 动态实例化场景元素，当元素的位置在一个屏幕以上位置时，就实例化
    private async _checkDynaTerrain(curPosY: number) {
        if (!this.node.parent || !this.node.parent.parent) return;

        if (this._insTerrainLock) return;

        this._insTerrainLock = true;
        try {
            const indicesToRemove: number[] = [];
            const newTerrainsInfo: TerrainInfo[] = [];
            for (let i = 0; i < this._terrainsInfo.length; i++) {
                const terrainInfo = this._terrainsInfo[i];

                // 因为列表排过序，先简单判断一次只要保存的元素第一个不在预判屏幕内，就跳出循环，因为后续的元素也都不会在预判屏幕内
                // 逻辑后面会根据每个节点的高度再仔细判断一次
                if (terrainInfo.data.position.y - terrainInfo.data.height / 2 + curPosY > GameConst.VIEWPORT_LOAD_POS) {
                    break;
                }

                if (terrainInfo.data.type.length > 0) {
                    if (terrainInfo.data.type.endsWith('.json')) { 
                        const json_path = jsonRootPath + terrainInfo.data.type.replace('.json', '');
                        const jsonAsset = await MyApp.resMgr.loadAsync(json_path, JsonAsset);
                        const jsonData = jsonAsset.json;
                        if (jsonData!.terrains && Array.isArray(jsonData!.terrains)) {
                            let subZIndex = 0;
                            const subParentNode = LevelUtils.getOrAddNode(terrainInfo.parentNode!, `dyna_${terrainInfo.zIndex}`);
                            logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name}] _instantiate 创建节点 subParentNode: ${subParentNode.name} 父节点：${subParentNode.parent!.name}`);
                            subParentNode.setSiblingIndex(terrainInfo.zIndex);
                            for (const t of jsonData!.terrains) {
                                const terrainData = Object.assign(new LevelDataTerrain(), t);
                                if ((terrainInfo.data.position.y + terrainData.position.y - terrainData.height / 2 + curPosY) <= GameConst.VIEWPORT_LOAD_POS) {
                                    if (terrainData.type.endsWith('.png')) {
                                        const png_path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrainData.type.replace('.png', ''));
                                        logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name}] 随机元素 png 路径: ${png_path} index: ${i}`);
                                        await this._createPngNode(
                                            png_path, v2(terrainInfo.data.position.x + terrainData.position.x, terrainInfo.data.position.y + terrainData.position.y), v2(terrainData.scale.x, terrainData.scale.y),
                                            terrainData.rotation, subParentNode!, subZIndex);
                                    } else {
                                        const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrainData.uuid);
                                        const prefabNode = await this._createTerrainPrefabNode(
                                            path, v2(terrainInfo.data.position.x + terrainData.position.x, terrainInfo.data.position.y + terrainData.position.y), v2(terrainData.scale.x, terrainData.scale.y),
                                            terrainData.rotation, subParentNode!, subZIndex);
                                        logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name}] 实时创建元素(通过json配置) prefab Node: ${prefabNode.name} index: ${i}`);
                                    }
                                } else {
                                    const name = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrainData.uuid);
                                    logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name}] push TerrainsInfo name: ${name} parentName: ${subParentNode.name} index: ${i} subZIndex: ${subZIndex}`);
                                    newTerrainsInfo.push({
                                        data: {
                                            uuid: terrainData.uuid,
                                            type: terrainData.type,
                                            height: terrainData.height,
                                            position: v2(terrainInfo.data.position.x + terrainData.position.x, terrainInfo.data.position.y + terrainData.position.y),
                                            scale: v2(terrainData.scale.x, terrainData.scale.y),
                                            rotation: terrainData.rotation
                                        },
                                        parentNode: subParentNode,
                                        zIndex: subZIndex
                                    });
                                }
                                subZIndex++;
                            }
                            
                            indicesToRemove.push(i);
                        }
                    } else if (terrainInfo.data.type.endsWith('.png')) {
                    if ((terrainInfo.data.position.y - terrainInfo.data.height / 2 + curPosY) <= GameConst.VIEWPORT_LOAD_POS) {
                            const png_path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrainInfo.data.type.replace('.png', ''));
                            await this._createPngNode(
                                png_path, v2(terrainInfo.data.position.x, terrainInfo.data.position.y), v2(terrainInfo.data.scale.x, terrainInfo.data.scale.y),
                                terrainInfo.data.rotation, terrainInfo.parentNode!, terrainInfo.zIndex);
                            indicesToRemove.push(i);
                        }
                    }
                } else {
                    if ((terrainInfo.data.position.y - terrainInfo.data.height / 2 + curPosY) <= GameConst.VIEWPORT_LOAD_POS) {
                        const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrainInfo.data.uuid);
                        const prefabNode = await this._createTerrainPrefabNode(
                            path, v2(terrainInfo.data.position.x, terrainInfo.data.position.y), v2(terrainInfo.data.scale.x, terrainInfo.data.scale.y),
                            terrainInfo.data.rotation, terrainInfo.parentNode!, terrainInfo.zIndex);
                        logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name}] 实时创建元素 prefab Node: ${prefabNode.name} 父节点：${terrainInfo.parentNode!.name} 节点顺序：${terrainInfo.zIndex}`);
                        indicesToRemove.push(i);
                    }
                }
            }
            for (let i = indicesToRemove.length - 1; i >= 0; i--) {
                this._terrainsInfo.splice(indicesToRemove[i], 1);
            }
            if (newTerrainsInfo.length > 0) { 
                this._terrainsInfo.push(...newTerrainsInfo);
                this._sortTerrainsInfo();
            }
        } catch (error) {
            logError("LevelLayerUI", `_instantiate Terrain error: ${error}`);
        } finally {
            this._insTerrainLock = false;
        }
    }

    private _checkScroll(): void {
        if (!this.scrollsNode || this._scorllPrefabs.length === 0) return;

        const lastChild = this.scrollsNode.children[this.scrollsNode.children.length - 1];
        const lastChildTransform = lastChild.getComponent(UITransform);

        if (!lastChildTransform) return;

        const lastChildTop = this.node.position.y + lastChild.position.y + this._lastScrollNodeHeight / 2;

        if (lastChildTop < GameConst.VIEWPORT_TOP) {
            const newY = lastChild.position.y + this._lastScrollNodeHeight;
            const randomOffsetX = GameIns.battleManager.random() * (this._scorllData!.offSetX!.max - this._scorllData!.offSetX!.min) + this._scorllData!.offSetX!.min;
            const newNode = this._addScrollNode(randomOffsetX, newY);
            var nodeHeight = 0;
            if (this._scorllData!.splicingMode === LayerSplicingMode.node_height) {    
                nodeHeight = newNode.getComponent(UITransform)!.contentSize.height;
            } else if (this._scorllData!.splicingMode === LayerSplicingMode.fix_height) {
                nodeHeight = 1334;
            } else if (this._scorllData!.splicingMode === LayerSplicingMode.random_height) {
                nodeHeight = Math.max(this._scorllData!.offSetY!.min,this._scorllData!.offSetY!.max) + newNode.getComponent(UITransform)!.contentSize.height;
            }
            this._lastScrollNodeHeight = nodeHeight;
        }
    }

    private async _checkEmittier(posY: number) {
        if (!this.emittiersNode || this._emittierInfo.length === 0) return;

        if (!this._insEmittierLock) return;
        
        this._insEmittierLock = true;
        try {
            const indicesToRemove: number[] = [];
            for (let i = 0; i < this._emittierInfo.length; i++) {
                const emittierInfo = this._emittierInfo[i];
                if (emittierInfo.position.y - emittierInfo.height / 2 + posY > GameConst.VIEWPORT_LOAD_POS) {
                    break;
                } else {
                    const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, emittierInfo.uuid);
                    const emittierNode = await this._createEmittierNode(
                        path, v2(emittierInfo.position.x, emittierInfo.position.y), v2(emittierInfo.scale.x, emittierInfo.scale.y),
                        emittierInfo.rotation, i);
                    logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name}] 实时创建发射器 prefab Node: ${emittierNode.name} 父节点：${this.emittiersNode!.name} 节点顺序：${i}`);
                    indicesToRemove.push(i);
                }
            }

            for (let i = indicesToRemove.length - 1; i >= 0; i--) {
                this._emittierInfo.splice(indicesToRemove[i], 1);
            }
        } catch (error) {
            logError("LevelLayerUI", `_checkEmittier error: ${error}`);
        } finally {
            this._insEmittierLock = false;
        }

        for (let i = this._inactiveEmittierNodes.length - 1; i >= 0; i--) {
            const node = this._inactiveEmittierNodes[i];
            
            if (!node.isValid) {
                this._inactiveEmittierNodes.splice(i, 1);
                continue;
            }
            
            const comp = node.getComponent(EmittierTerrain);
            if (!comp) {
                this._inactiveEmittierNodes.splice(i, 1);
                continue;
            }
            
            if (comp.status === EmittierStatus.inactive && (posY + node.position.y) <= GameConst.VIEWPORT_TOP) {
                comp.startEmittier();
                if (comp.follow === false) {
                    this.speed = 0;
                }
                this._inactiveEmittierNodes.splice(i, 1);
            }
        }
    }

    /**
     * 创建并配置 PNG 类型的地形节点
     * @param spriteFramePath - SpriteFrame 资源路径
     * @param position - 节点位置
     * @param scale - 节点缩放
     * @param rotation - 节点旋转角度
     * @param parentNode - 父节点
     * @param zIndex - 在父节点中的层级顺序
     * @returns 返回创建好的地形节点
     */
    private async _createPngNode(
        spriteFramePath: string,
        position: Vec2,
        scale: Vec2,
        rotation: number,
        parentNode: Node,
        zIndex: number
    ): Promise<Node | null> {
        if (!spriteFramePath.endsWith('/spriteFrame')) {
            logError("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name}] 非标准 png 路径: ${spriteFramePath}`);
            return null;
        }

        const name = LevelUtils.extractPathPart(spriteFramePath);
        let terrainNode = GameIns.gameMapManager.mapObjectPoolManager.get(
            TERRAIN_POOL_NAME, 
            name
        );

        if (terrainNode) {
            // 2. 如果从对象池获取到节点，直接配置属性
            logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name}] 从对象池${TERRAIN_POOL_NAME}获取 PNG 元素节点: ${name}`);
        } else {
            const spriteFrame = await MyApp.resMgr.loadAsync(spriteFramePath, SpriteFrame);
            logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name}] 创建 PNG 元素节点: ${name} 父节点：${parentNode.name} 节点顺序：${zIndex}`);
            terrainNode = new Node();
            terrainNode.name = name;
            const terrainSprite = terrainNode.addComponent(Sprite);
            terrainSprite.spriteFrame = spriteFrame;
            const uiTransform = terrainNode.getComponent(UITransform);
            if (uiTransform && spriteFrame) {
                const size = spriteFrame.originalSize;
                uiTransform.setContentSize(size.width, size.height);
                //logDebug("LevelLayerUI", `PNG 尺寸: ${size.width}x${size.height}`);
            } 
            const checkOut = terrainNode.addComponent(LevelNodeCheckOutScreen);
            checkOut.init(TERRAIN_POOL_NAME);
        }

        terrainNode.setPosition(position.x, position.y, 0);
        terrainNode.setScale(scale.x, scale.y);
        terrainNode.setRotationFromEuler(0, 0, rotation);
        
        parentNode.addChild(terrainNode);
        terrainNode.setSiblingIndex(zIndex);
        
        return terrainNode;
    }

    /**
     * 创建并配置 Prefab 类型的地形节点
     * @param prefabPath - Prefab 资源路径
     * @param position - 节点位置
     * @param scale - 节点缩放
     * @param rotation - 节点旋转角度
     * @param parentNode - 父节点
     * @param zIndex - 在父节点中的层级顺序
     * @returns 返回创建好的地形节点
     */
    private async _createTerrainPrefabNode(
        prefabPath: string,
        position: Vec2,
        scale: Vec2,
        rotation: number,
        parentNode: Node,
        zIndex: number
    ): Promise<Node> {
        const name = LevelUtils.extractPathPart(prefabPath,'');
        let terrainNode = GameIns.gameMapManager.mapObjectPoolManager.get(
            TERRAIN_POOL_NAME, 
            name
        );
        if (!terrainNode) {
            const prefab = await MyApp.resMgr.loadAsync(prefabPath, Prefab);
            terrainNode = instantiate(prefab);
            const checkOut = terrainNode.addComponent(LevelNodeCheckOutScreen);
            checkOut.init(TERRAIN_POOL_NAME);
            logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name}] 创建 Prefab 元素节点: ${name} 父节点：${parentNode.name} 节点顺序：${zIndex}`);
        } else {
            logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name}] 从对象池${TERRAIN_POOL_NAME}获取 Prefab 元素节点: ${name}`);
        }

        terrainNode.setPosition(position.x, position.y, 0);
        terrainNode.setScale(scale.x, scale.y);
        terrainNode.setRotationFromEuler(0, 0, rotation);
        parentNode.addChild(terrainNode);
        terrainNode.setSiblingIndex(zIndex);
        
        return terrainNode;
    }

    /**
     * 创建并配置 Prefab 类型的地形节点
     * @param prefabPath - Prefab 资源路径
     * @param position - 节点位置
     * @param scale - 节点缩放
     * @param rotation - 节点旋转角度
     * @param zIndex - 在父节点中的层级顺序
     * @returns 返回创建好的地形节点
     */
    private async _createEmittierNode(
        prefabPath: string,
        position: Vec2,
        scale: Vec2,
        rotation: number,
        zIndex: number
    ): Promise<Node> {
        const name = LevelUtils.extractPathPart(prefabPath,'');
        let emittierNode = GameIns.gameMapManager.mapObjectPoolManager.get(
            EMIITER_POOL_NAME, 
            name
        );
        if (!emittierNode) {
            const prefab = await MyApp.resMgr.loadAsync(prefabPath, Prefab);
            emittierNode = instantiate(prefab);
            const emittierTerrain = emittierNode.addComponent(EmittierTerrain);
            emittierTerrain.init(EMIITER_POOL_NAME);
            logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name}] 创建发射器节点: ${name} 父节点：${this.emittiersNode!.name} 节点顺序：${zIndex}`);
        } else {
            logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name}] 从对象池${EMIITER_POOL_NAME}获取发射器节点: ${name}`);
        }

        emittierNode.setPosition(position.x, position.y, 0);
        emittierNode.setScale(scale.x, scale.y);
        emittierNode.setRotationFromEuler(0, 0, rotation);
        this.emittiersNode!.addChild(emittierNode);
        emittierNode.setSiblingIndex(zIndex);
        
        return emittierNode;
    }

    private _addScrollNode(xPos:number, yPos: number): Node {
        const index = this.scrollsNode!.children.length % this._scorllPrefabs.length;
        const prefab = this._scorllPrefabs[index];
        const prefabName = prefab.name;
        let node = GameIns.gameMapManager.mapObjectPoolManager.get(SCROLL_POOL_NAME, prefabName);
        if (!node) {
            node = instantiate(this._scorllPrefabs[index]);
            const checkOut = node.addComponent(LevelNodeCheckOutScreen);
            checkOut.init(SCROLL_POOL_NAME);
        } else {
            logDebug("LevelLayerUI", `[${this.node.parent?.parent?.name}:${this.node.name}] 从对象池${SCROLL_POOL_NAME}获取 Prefab 元素节点: ${name}`);
        }
        this.scrollsNode!.addChild(node);
        node.setPosition(xPos, yPos, 0);
        return node;
    }

    public getEventByElemID(elemID: string): LevelDataEvent | null {
        for (let event of this.events) {
            if (event.elemID == elemID) {
                return event;
            }
        }
        return null;
    }
}

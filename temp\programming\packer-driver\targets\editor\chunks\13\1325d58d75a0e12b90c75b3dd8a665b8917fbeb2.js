System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Label, Node, Slider, BundleName, BaseUI, UILayer, UIMgr, DataMgr, ButtonPlus, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _dec10, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _descriptor9, _crd, ccclass, property, PKBuyUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "db://assets/bundles/common/script/const/BundleConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIOpt(extras) {
    _reporterNs.report("UIOpt", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataMgr(extras) {
    _reporterNs.report("DataMgr", "../../data/DataManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "../common/components/button/ButtonPlus", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Label = _cc.Label;
      Node = _cc.Node;
      Slider = _cc.Slider;
    }, function (_unresolved_2) {
      BundleName = _unresolved_2.BundleName;
    }, function (_unresolved_3) {
      BaseUI = _unresolved_3.BaseUI;
      UILayer = _unresolved_3.UILayer;
      UIMgr = _unresolved_3.UIMgr;
    }, function (_unresolved_4) {
      DataMgr = _unresolved_4.DataMgr;
    }, function (_unresolved_5) {
      ButtonPlus = _unresolved_5.ButtonPlus;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "86eb39NLDJDmbkko5FahyFb", "PKBuyUI", undefined);

      __checkObsolete__(['_decorator', 'Label', 'Node', 'Slider']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("PKBuyUI", PKBuyUI = (_dec = ccclass('PKBuyUI'), _dec2 = property(Slider), _dec3 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec4 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec5 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec6 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec7 = property(Label), _dec8 = property(Node), _dec9 = property(Label), _dec10 = property(Label), _dec(_class = (_class2 = class PKBuyUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "slider", _descriptor, this);

          _initializerDefineProperty(this, "btnReduce", _descriptor2, this);

          _initializerDefineProperty(this, "btnAdd", _descriptor3, this);

          _initializerDefineProperty(this, "btnMax", _descriptor4, this);

          _initializerDefineProperty(this, "btnBuy", _descriptor5, this);

          _initializerDefineProperty(this, "lblPrice", _descriptor6, this);

          _initializerDefineProperty(this, "itemQuaIcon", _descriptor7, this);

          _initializerDefineProperty(this, "itemName", _descriptor8, this);

          _initializerDefineProperty(this, "buyTimes", _descriptor9, this);

          this.maxVal = 10;
          this.curVal = 1;
        }

        static getUrl() {
          return "prefab/ui/PKBuyUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Default;
        }

        static getBundleName() {
          return (_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).HomePK;
        }

        static getUIOption() {
          return {
            isClickBgCloseUI: true
          };
        }

        onLoad() {
          this.btnReduce.addClick(this.onReduceClick, this);
          this.btnAdd.addClick(this.onAddClick, this);
          this.btnMax.addClick(this.onMaxClick, this);
          this.btnBuy.addClick(this.onBuyClick, this);
          this.slider.node.on('slide', this.onSliderValueChanged, this);
        }

        onReduceClick() {
          this.curVal--;
          this.curVal = Math.max(1, this.curVal);
          this.itemName.string = "购买：" + this.curVal.toString();
          this.updateSliderProgress();
        }

        onAddClick() {
          this.curVal++;
          this.curVal = Math.min(this.maxVal, this.curVal);
          this.itemName.string = "购买：" + this.curVal.toString();
          this.updateSliderProgress();
        }

        onMaxClick() {
          this.curVal = this.maxVal;
          this.itemName.string = "购买：" + this.curVal.toString();
          this.updateSliderProgress();
        }

        onSliderValueChanged(slider) {
          if (this.maxVal <= 1) {
            this.curVal = 1;
            this.itemName.string = "购买：" + this.curVal.toString();
            return;
          }

          let val = Math.round(slider.progress * (this.maxVal - 1)) + 1;
          val = Math.max(1, Math.min(this.maxVal, val));
          this.curVal = val;
          this.itemName.string = "购买：" + this.curVal.toString();
        }

        updateSliderProgress() {
          if (this.slider) {
            if (this.maxVal <= 1) {
              this.slider.progress = 1;
              return;
            }

            this.slider.progress = (this.curVal - 1) / (this.maxVal - 1);
          }
        }

        onBuyClick() {
          (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).pk.cmdStoreBuy(1, this.curVal, 1, 1, 1, 1);
        }

        async closeUI() {
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).closeUI(PKBuyUI);
        }

        async onShow() {}

        async onHide() {}

        async onClose() {}

        onDestroy() {}

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "slider", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "btnReduce", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "btnAdd", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "btnMax", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "btnBuy", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "lblPrice", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "itemQuaIcon", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor8 = _applyDecoratedDescriptor(_class2.prototype, "itemName", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor9 = _applyDecoratedDescriptor(_class2.prototype, "buyTimes", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=1325d58d75a0e12b90c75b3dd8a665b8917fbeb2.js.map
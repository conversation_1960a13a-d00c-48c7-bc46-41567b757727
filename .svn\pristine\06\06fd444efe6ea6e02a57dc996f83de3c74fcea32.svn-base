import { _decorator, misc, Component, Node, Sprite, Color, Prefab, AudioClip } from 'cc';
import { EDITOR } from 'cc/env';

import { logWarn } from 'db://assets/scripts/utils/Logger';
import { BulletData } from '../data/bullet/BulletData';
import { ObjectPool } from './ObjectPool';
import { eSpriteOrientation, eMoveEvent, eOrientationType } from '../move/IMovable';
import { DefaultMove } from '../move/DefaultMove';
import { BulletSystem } from './BulletSystem';
import { EventGroup, EventGroupContext } from './EventGroup';
import { Property, PropertyContainer } from './PropertyContainer';
import { Emitter } from './Emitter';
import FCollider, { ColliderGroupType } from 'db://assets/bundles/common/script/game/collider-system/FCollider';
import FBoxCollider from 'db://assets/bundles/common/script/game/collider-system/FBoxCollider';
import Entity from 'db://assets/bundles/common/script/game/ui/base/Entity';
import { eEntityTag } from 'db://assets/bundles/common/script/game/ui/base/Entity';
import { ResEmitter, BulletSourceType, BulletType } from 'db://assets/bundles/common/script/autogen/luban/schema';
import type PlaneBase from '../ui/plane/PlaneBase';
import { AttributeData } from '../../data/base/AttributeData';
import { GameIns } from 'db://assets/bundles/common/script/game/GameIns';

const { ccclass, property, executeInEditMode, requireComponent } = _decorator;

export enum eBulletProp {
    Duration = 1, DelayDestroy, Attack, Speed, SpeedAngle, Acceleration, AccelerationAngle, Orientation, OrientationParam, Scale, Color, DefaultFacing,
    IsDestroyOutScreen, IsDestructive, IsDestructiveOnHit, MaxTrackingDuration, MaxTrackingAngle
}

export class BulletProperty extends PropertyContainer<number> {
    public duration!: Property<number>;                // 子弹持续时间(超出后销毁回收)
    public delayDestroy!: Property<number>;            // 延迟销毁时间

    public attack!: Property<number>;                  // 子弹伤害
    public speed!: Property<number>;                   // 子弹速度
    public speedAngle!: Property<number>;              // 子弹速度角度
    public acceleration!: Property<number>;            // 子弹加速度
    public accelerationAngle!: Property<number>;       // 子弹加速度角度
    public orientation!: Property<number>;             // 子弹朝向类型
    public orientationParam!: Property<number>;        // 子弹朝向参数
    public maxTrackingDuration!: Property<number>;     // 最大追踪时间
    public maxTrackingAngle!: Property<number>;        // 最大追踪角度

    public scale!: Property<number>;                   // 子弹缩放
    public color!: Property<Color>;                    // 子弹颜色
    public defaultFacing!: Property<eSpriteOrientation>;          // 子弹初始朝向

    public isDestroyOutScreen!: Property<boolean>;     // 是否超出屏幕销毁
    public isDestructive!: Property<boolean>;          // 是否可被破坏
    public isDestructiveOnHit!: Property<boolean>;     // 命中时是否被销毁

    public config: ResEmitter|undefined = undefined;

    constructor() {
        super();
        this.duration = this.addProperty(eBulletProp.Duration, 6000);
        this.delayDestroy = this.addProperty(eBulletProp.DelayDestroy, 0);
        this.attack = this.addProperty(eBulletProp.Attack, 1);
        this.speed = this.addProperty(eBulletProp.Speed, 600);
        this.speedAngle = this.addProperty(eBulletProp.SpeedAngle, 0);
        this.acceleration = this.addProperty(eBulletProp.Acceleration, 0);
        this.accelerationAngle = this.addProperty(eBulletProp.AccelerationAngle, 0);
        this.orientation = this.addProperty(eBulletProp.Orientation, eOrientationType.Path as number);
        this.orientationParam = this.addProperty(eBulletProp.OrientationParam, 0);
        this.maxTrackingDuration = this.addProperty(eBulletProp.MaxTrackingDuration, 0);
        this.maxTrackingAngle = this.addProperty(eBulletProp.MaxTrackingAngle, 0);
        this.scale = this.addProperty(eBulletProp.Scale, 1);
        this.color = this.addProperty(eBulletProp.Color, Color.WHITE);
        this.defaultFacing = this.addProperty<eSpriteOrientation>(eBulletProp.DefaultFacing, eSpriteOrientation.Up);
        this.isDestroyOutScreen = this.addProperty(eBulletProp.IsDestroyOutScreen, true);
        this.isDestructive = this.addProperty(eBulletProp.IsDestructive, false);
        this.isDestructiveOnHit = this.addProperty(eBulletProp.IsDestructiveOnHit, false);
    }

    public resetFromData(data: BulletData) {
        this.duration.value = data.duration.eval(null, true); 
        this.delayDestroy.value = data.delayDestroy.eval(null, true); 
        this.speed.value = data.speed.eval(null, true); 
        // this.speedAngle.value = data.speedAngle.eval(); 
        this.acceleration.value = data.acceleration.eval(null, true); 
        this.accelerationAngle.value = data.accelerationAngle.eval(null, true); 
        this.orientation.value = data.orientationType; 
        this.orientationParam.value = data.orientationParam.eval(null, true); 
        this.maxTrackingDuration.value = data.maxTrackingDuration.eval(null, true); 
        this.maxTrackingAngle.value = data.maxTrackingAngle.eval(null, true); 
        this.scale.value = data.scale.eval(null, true); 
        // this.color.value = data.color.eval(); 
        this.isDestroyOutScreen.value = data.isDestroyOutScreen; 
        this.isDestructive.value = data.isDestructive; 
        this.isDestructiveOnHit.value = data.isDestructiveOnHit; 
    }

    public copyFrom(other: BulletProperty) {
        this.forEachProperty((k, prop) => {
            prop.value = other.getPropertyValue(k)!;
        });
    }

    public clear() {
        // Clear all listeners
        this.forEachProperty((k, prop) => prop.clear());
    }
}

// 子弹 Bullet
// 如何集成到项目里? 
@ccclass('Bullet')
@executeInEditMode(true)
@requireComponent(DefaultMove)
export class Bullet extends Entity {
    @property({type: DefaultMove, displayName: "移动组件"})
    public mover!: DefaultMove;

    @property({type: Sprite, displayName: "子弹精灵"})
    public bulletSprite: Sprite|null = null;

    @property({type: FCollider, displayName: '碰撞组件'})
    public collider: FCollider|null = null;

    @property({type: Prefab, displayName: "碰撞特效"})
    public hitEffectPrefab: Prefab|null = null;
    
    public isAlive: boolean = false;
    public elapsedTime: number = 0;         // 子弹存活时间
    public emitter!: Emitter;
    public bulletData!: BulletData;         // 子弹编辑器下的配置
    public config: ResEmitter|undefined = undefined;   // 子弹表格配置
    // 以下属性重新定义一遍, 作为可修改的属性, 部分定义在movable里
    public prop: BulletProperty = new BulletProperty();
    public eventGroups: EventGroup[] = [];

    // 增加部分便捷接口获取子弹属性
    public get sourceType(): BulletSourceType {
        return this.config?.source || BulletSourceType.MAINPLANE;
    }
    public get type(): BulletType {
        return this.config?.type || BulletType.NORMAL;
    }

    onLoad(): void {
        if (!this.mover) {
            this.mover = this.getComponent(DefaultMove) || this.addComponent(DefaultMove)!;
        }
        this.mover.on(eMoveEvent.onBecomeInvisible, () => {
            if (this.prop.isDestroyOutScreen.value) {
                BulletSystem.onDestroyBullet(this);
            }
        });
        if (!this.collider) {
            let boxCollider = this.getComponent(FBoxCollider) || this.addComponent(FBoxCollider)!;
            this.collider = boxCollider;
        }
 
        this.prop.defaultFacing.value = this.mover.forwardOrientation;
        // listen to property changes
        this.prop.orientation.on((value) => {
            this.mover.setOrientation(value as eOrientationType, this.prop.orientationParam.value);
        });
        this.prop.orientationParam.on((value) => {
            this.mover.setOrientation(this.prop.orientation.value as eOrientationType, value);
        });
        this.prop.maxTrackingDuration.on((value) => {
            this.mover.setTrackingDuration(value);
        });
        this.prop.maxTrackingAngle.on((value) => {
            this.mover.setTrackingMaxTurnAngle(value);
        });
        this.prop.speed.on((value) => {
            this.mover.speed = value;
        });
        this.prop.speedAngle.on((value) => {
            this.mover.speedAngle = value;
        });
        this.prop.acceleration.on((value) => {
            this.mover.acceleration = value;
        });
        this.prop.accelerationAngle.on((value) => {
            this.mover.accelerationAngle = value;
        });
        this.prop.scale.on((value) => {
            this.node.setScale(value, value, value);
        });
        this.prop.color.on((value) => {
            if (this.bulletSprite) {
                this.bulletSprite.color = value;
            }
        });
    }

    public onCreate(emitter: Emitter, bulletConfig: ResEmitter|undefined): void {
        this.isAlive = true;
        this.elapsedTime = 0;
        this.emitter = emitter;
        this.bulletData = emitter.bulletData!;
        this.config = bulletConfig;

        // TODO: 创建entity的时候，设置正确的tag.
        const ent = emitter.getEntity();
        if (ent) {
            const isShootFromEnemy = this.config?.source == BulletSourceType.ENEMYPLANE;
            this.collider!.initBaseData(this);
            this.collider!.groupType = isShootFromEnemy ? ColliderGroupType.BULLET_ENEMY : ColliderGroupType.BULLET_SELF;
            this.collider!.isEnable = true;
            this.addTag(isShootFromEnemy ? eEntityTag.EnemyBullet : eEntityTag.PlayerBullet);
        } else {
            logWarn("emitter", "onCreate bullet", "has no entity");
        }

        this.resetProperties();
    }

    public onReady() {
        this.prop.notifyAll(true);
        this.mover.setMovable(true);
        this.resetEventGroups();
        
        const ent = this.emitter.getEntity();
        if (ent) {
            this.mover.setTarget(ent.target);
        }
    }

    private destroySelf() {
        if (!this.node || !this.node.isValid) return;
        
        if (EDITOR) {
            this.node.destroy();
        } else {
            ObjectPool.returnNode(this.node);
        }
    }

    public resetProperties(): void {
        if (!this.emitter) return;

        this.prop.copyFrom(this.emitter.bulletProp);
        this.prop.notifyAll(true);
    }

    public resetEventGroups(): void {
        // create event groups here
        if (this.bulletData && this.bulletData.eventGroupData.length > 0) {
            let ctx = new EventGroupContext();
            ctx.bullet = this;
            ctx.emitter = this.emitter;
            for (const eventName of this.bulletData.eventGroupData) {
                BulletSystem.createBulletEventGroup(ctx, eventName);
            }
        }
    }

    public tick(dt: number): void {
        if (!this.isAlive) return;

        this.elapsedTime += dt;
        if (this.elapsedTime > this.prop.duration.value) {
            BulletSystem.onDestroyBullet(this);
            return;
        }
        
        // 毫秒 -> 秒
        this.mover!.tick(dt / 1000);
        if (this.prop.anyPropertyDirty) 
            this.prop.notifyAll();
    }

    public willDestroy(): void {
        this.isAlive = false;
        if (this.eventGroups && this.eventGroups.length > 0) {
            this.eventGroups.forEach(group => group.tryStop()); // stop all event groups before destroying the bullet itself.
            this.eventGroups = []; // clear the event groups array
        }
        
        this.mover.setMovable(false);
        this.collider!.isEnable = false;
        this.removeAllComp();
        this.clearTags();
        // 只有离开屏幕（不可见）的时候，才启用延迟
        if (!this.mover.isVisible && this.prop.delayDestroy && this.prop.delayDestroy.value > 0) {
            this.scheduleOnce(() => {
                this.destroySelf();
            }, this.prop.delayDestroy.value);
        } else {
            this.destroySelf();
        }
    }

    onCollide(collider: FCollider) {
        this.remove();
    }

    public remove() {
        BulletSystem.onDestroyBullet(this);
    }

    getAttack(): number {
        const ent = this.emitter.getEntity();
        return ent!.getAttack();
    }
    calcDamage(defender: PlaneBase): number {
        const ent = this.emitter.getEntity();
        if (!ent) {
            return 0;
        }
        if (!this.config) {
            logWarn("Bullet", "bullet config is null");
            return 0;
        }

        return AttributeData.CalcBulletDamage(ent, defender, 
            this.config!.attackCoefficient, true, this.config!.damageType, false, 1/* TODO ybgg 攻击力修正 */);
    }

    /** 
     * 获取子弹到玩家的距离平方 
     */
    getSquaredDistanceToPlayer(): number {
        const mainPlaneManager = GameIns.mainPlaneManager;
        if (!mainPlaneManager.mainPlane) return 0;
        const pos = this.node.position;
        const playerPos = mainPlaneManager.mainPlane.node.position;
        return (pos.x - playerPos.x) * (pos.x - playerPos.x) + (pos.y - playerPos.y) * (pos.y - playerPos.y);
    }
}

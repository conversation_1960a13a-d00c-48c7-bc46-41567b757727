import { _decorator, Enum, CCFloat } from 'cc';
const { ccclass, property} = _decorator;

import { LevelDataEventCondtion, LevelDataEventCondtionComb, LevelDataEventCondtionType } from 'db://assets/bundles/common/script/leveldata/condition/LevelDataEventCondtion';
import { LevelDataEventCondtionDelayTime } from 'db://assets/bundles/common/script/leveldata/condition/LevelDataEventCondtionDelayTime';
import { LevelDataEventCondtionDelayDistance } from 'db://assets/bundles/common/script/leveldata/condition/LevelDataEventCondtionDelayDistance';
import { LevelDataEventCondtionWave, LevelDataEventConditionWaveType } from 'db://assets/bundles/common/script/leveldata/condition/LevelDataEventCondtionWave';
import { newCondition } from 'db://assets/bundles/common/script/leveldata/condition/newCondition';

import { LevelEditorElemUI } from './LevelEditorElemUI';

@ccclass('LevelEditorCondition')
export class LevelEditorCondition {
    public _index: number = 0;
    @property({
        type:Enum(LevelDataEventCondtionType),
    })
    public dataType: LevelDataEventCondtionType = LevelDataEventCondtionType.DelayTime;

    private dataMap: Map<LevelDataEventCondtionType, LevelDataEventCondtion> = new Map();
    private getData<T extends LevelDataEventCondtion>(type: LevelDataEventCondtionType): T {
        return this.dataMap.get(type)! as T;
    }

    constructor() {
        for (let t = 0; t < LevelDataEventCondtionType.Max; t++) {
            this.dataMap.set(t, newCondition({comb: LevelDataEventCondtionComb.And, _type: t}));
        }
    }

    // 根据type返回当前类型
    public get data(): LevelDataEventCondtion {
        return this.getData(this.dataType);
    }
    public set data(value: LevelDataEventCondtion) {
        this.dataType = value._type;
        this.dataMap.set(this.dataType, value);
    }

    private get delayTimeData(): LevelDataEventCondtionDelayTime {
        return this.getData(LevelDataEventCondtionType.DelayTime);
    }
    private get delayDistanceData(): LevelDataEventCondtionDelayDistance {
        return this.getData(LevelDataEventCondtionType.DelayDistance);
    }
    private get waveData(): LevelDataEventCondtionWave {
        return this.getData(LevelDataEventCondtionType.Wave);
    }

    @property({
        type:Enum(LevelDataEventCondtionComb),
        visible() {
            // @ts-ignore
            return this._index != 0;
        }
    })
    public get comb(): LevelDataEventCondtionComb {
        return this.data.comb;
    }
    public set comb(value: LevelDataEventCondtionComb) {
        this.dataMap.forEach((cond) => {
            cond.comb = value;
        });
    }

    @property({
        type :CCFloat,
        visible () {
            // @ts-ignore
            return this.dataType == LevelDataEventCondtionType.DelayTime ;
        }
    })
    public get delayTime(): number {
        return this.delayTimeData.time;
    }
    public set delayTime(value: number) {
        this.delayTimeData.time = value;
    }

    @property({
        type :CCFloat,
        visible () {
            // @ts-ignore
            return this.dataType == LevelDataEventCondtionType.DelayDistance;
        }
    })
    public get delayDistance(): number {
        return this.delayDistanceData.distance;
    }
    public set delayDistance(value: number) {
        this.delayDistanceData.distance = value;
    }

    @property({visible: false})
    public _targetElem: LevelEditorElemUI | null = null;
    @property({
        type: LevelEditorElemUI,
        displayName: "目标事件的波次(event)",
        visible () {
            // @ts-ignore
            return this.dataType == LevelDataEventCondtionType.Wave;
        }
    })
    public get targetElem(): LevelEditorElemUI | null {
         return this._targetElem;
    }
    public set targetElem(value: LevelEditorElemUI | null) { 
        this._targetElem = value;
        this.waveData.targetElemID = value?.elemID ?? "";
    }

    @property({
        type: Enum(LevelDataEventConditionWaveType),
        displayName: "波次条件(完成or触发)",
        visible () {
            // @ts-ignore
            return this.dataType == LevelDataEventCondtionType.Wave;
        }
    })
    public get waveCondType(): LevelDataEventConditionWaveType {
        return this.waveData.waveCondType;
    }
    public set waveCondType(value: LevelDataEventConditionWaveType) {
        this.waveData.waveCondType = value;
    }

    public copyFrom(source: LevelEditorCondition) {
        this._index = source._index;
        this.comb = source.comb;
        this.dataType = source.dataType;
        switch (this.dataType) {
            case LevelDataEventCondtionType.DelayTime:
                this.delayTime = (source.data as LevelDataEventCondtionDelayTime).time;
                break;
            case LevelDataEventCondtionType.DelayDistance:
                this.delayDistance = (source.data as LevelDataEventCondtionDelayDistance).distance;
                break;
            case LevelDataEventCondtionType.Wave:
                this.targetElem = source._targetElem;
                this.waveCondType = (source.data as LevelDataEventCondtionWave).waveCondType;
                break;
            default:
                break;
        }
    }
}
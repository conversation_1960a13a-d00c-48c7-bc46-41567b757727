2025-10-9 09:19:52-log: Cannot access game frame or container.
2025-10-9 09:19:53-debug: asset-db:require-engine-code (433ms)
2025-10-9 09:19:53-log: [box2d]:box2d wasm lib loaded.
2025-10-9 09:19:53-log: [bullet]:bullet wasm lib loaded.
2025-10-9 09:19:53-log: meshopt wasm decoder initialized
2025-10-9 09:19:53-log: Cocos Creator v3.8.6
2025-10-9 09:19:53-log: Using legacy pipeline
2025-10-9 09:19:53-debug: [Assets Memory track]: asset-db-plugin-register: programming start:81.17MB, end 84.39MB, increase: 3.22MB
2025-10-9 09:19:53-log: Forward render pipeline initialized.
2025-10-9 09:19:53-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:31.00MB, end 80.04MB, increase: 49.04MB
2025-10-9 09:19:54-debug: [Assets Memory track]: asset-db-plugin-register: builder start:84.44MB, end 224.81MB, increase: 140.37MB
2025-10-9 09:19:54-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:80.06MB, end 228.43MB, increase: 148.37MB
2025-10-9 09:19:54-debug: [Assets Memory track]: asset-db-plugin-register: engine-extends start:225.06MB, end 228.20MB, increase: 3.13MB
2025-10-9 09:19:54-debug: [Assets Memory track]: asset-db-plugin-register: project start:81.02MB, end 228.40MB, increase: 147.38MB
2025-10-9 09:19:54-debug: run package(harmonyos-next) handler(enable) start
2025-10-9 09:19:54-debug: run package(harmonyos-next) handler(enable) success!
2025-10-9 09:19:54-debug: run package(honor-mini-game) handler(enable) start
2025-10-9 09:19:54-debug: run package(honor-mini-game) handler(enable) success!
2025-10-9 09:19:54-debug: run package(huawei-quick-game) handler(enable) success!
2025-10-9 09:19:54-debug: run package(huawei-agc) handler(enable) start
2025-10-9 09:19:54-debug: run package(ios) handler(enable) start
2025-10-9 09:19:54-debug: run package(huawei-quick-game) handler(enable) start
2025-10-9 09:19:54-debug: run package(huawei-agc) handler(enable) success!
2025-10-9 09:19:54-debug: run package(linux) handler(enable) start
2025-10-9 09:19:54-debug: run package(linux) handler(enable) success!
2025-10-9 09:19:54-debug: run package(mac) handler(enable) start
2025-10-9 09:19:54-debug: run package(mac) handler(enable) success!
2025-10-9 09:19:54-debug: run package(migu-mini-game) handler(enable) success!
2025-10-9 09:19:54-debug: run package(ios) handler(enable) success!
2025-10-9 09:19:54-debug: run package(native) handler(enable) success!
2025-10-9 09:19:54-debug: run package(migu-mini-game) handler(enable) start
2025-10-9 09:19:54-debug: run package(ohos) handler(enable) start
2025-10-9 09:19:54-debug: run package(native) handler(enable) start
2025-10-9 09:19:54-debug: run package(oppo-mini-game) handler(enable) start
2025-10-9 09:19:54-debug: run package(ohos) handler(enable) success!
2025-10-9 09:19:54-debug: run package(runtime-dev-tools) handler(enable) success!
2025-10-9 09:19:54-debug: run package(oppo-mini-game) handler(enable) success!
2025-10-9 09:19:54-debug: run package(taobao-mini-game) handler(enable) start
2025-10-9 09:19:54-debug: run package(taobao-mini-game) handler(enable) success!
2025-10-9 09:19:54-debug: run package(runtime-dev-tools) handler(enable) start
2025-10-9 09:19:54-debug: run package(vivo-mini-game) handler(enable) start
2025-10-9 09:19:54-debug: run package(web-desktop) handler(enable) start
2025-10-9 09:19:54-debug: run package(vivo-mini-game) handler(enable) success!
2025-10-9 09:19:54-debug: run package(web-mobile) handler(enable) success!
2025-10-9 09:19:54-debug: run package(wechatgame) handler(enable) start
2025-10-9 09:19:54-debug: run package(web-desktop) handler(enable) success!
2025-10-9 09:19:54-debug: run package(web-mobile) handler(enable) start
2025-10-9 09:19:54-debug: run package(wechatprogram) handler(enable) start
2025-10-9 09:19:54-debug: run package(wechatprogram) handler(enable) success!
2025-10-9 09:19:54-debug: run package(wechatgame) handler(enable) success!
2025-10-9 09:19:54-debug: run package(windows) handler(enable) start
2025-10-9 09:19:54-debug: run package(windows) handler(enable) success!
2025-10-9 09:19:54-debug: run package(xiaomi-quick-game) handler(enable) start
2025-10-9 09:19:54-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-10-9 09:19:54-debug: run package(cocos-service) handler(enable) success!
2025-10-9 09:19:54-debug: run package(im-plugin) handler(enable) start
2025-10-9 09:19:54-debug: run package(cocos-service) handler(enable) start
2025-10-9 09:19:54-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-10-9 09:19:54-debug: run package(im-plugin) handler(enable) success!
2025-10-9 09:19:54-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-10-9 09:19:54-debug: run package(emitter-editor) handler(enable) start
2025-10-9 09:19:54-debug: run package(emitter-editor) handler(enable) success!
2025-10-9 09:19:54-debug: asset-db:worker-init: initPlugin (980ms)
2025-10-9 09:19:54-debug: start refresh asset from db://assets/editor/enum-gen/EmitterEnum.ts...
2025-10-9 09:19:54-debug: refresh asset db://assets/editor/enum-gen success
2025-10-9 09:19:54-debug: start refresh asset from db://assets/editor/enum-gen/EnemyEnum.ts...
2025-10-9 09:19:54-debug: refresh asset db://assets/editor/enum-gen success
2025-10-9 09:19:54-debug: [Assets Memory track]: asset-db:worker-init start:30.99MB, end 225.39MB, increase: 194.40MB
2025-10-9 09:19:54-debug: Run asset db hook programming:beforePreStart ...
2025-10-9 09:19:54-debug: Run asset db hook engine-extends:beforePreStart ...
2025-10-9 09:19:54-debug: Run asset db hook programming:beforePreStart success!
2025-10-9 09:19:54-debug: Run asset db hook engine-extends:beforePreStart success!
2025-10-9 09:19:54-debug: run package(i18n) handler(enable) start
2025-10-9 09:19:54-debug: start custom db i18n...
2025-10-9 09:19:54-debug: run package(i18n) handler(enable) success!
2025-10-9 09:19:54-debug: run package(level-editor) handler(enable) success!
2025-10-9 09:19:54-debug: start asset-db(i18n)...
2025-10-9 09:19:54-debug: run package(level-editor) handler(enable) start
2025-10-9 09:19:54-debug: run package(placeholder) handler(enable) start
2025-10-9 09:19:54-debug: run package(placeholder) handler(enable) success!
2025-10-9 09:19:54-debug: asset-db:worker-init (1586ms)
2025-10-9 09:19:54-debug: asset-db-hook-engine-extends-beforePreStart (96ms)
2025-10-9 09:19:54-debug: asset-db-hook-programming-beforePreStart (97ms)
2025-10-9 09:19:54-debug: [Assets Memory track]: asset-db:worker-startup-database[i18n] start:226.65MB, end 230.54MB, increase: 3.90MB
2025-10-9 09:19:54-debug: asset-db:worker-startup-database[i18n] (40ms)
2025-10-9 09:19:54-debug: Preimport db internal success
2025-10-9 09:19:54-debug: %cImport%c: E:\M2Game\Client\assets\editor\enum-gen\EmitterEnum.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:54-debug: %cImport%c: E:\M2Game\Client\assets\editor\enum-gen\EnemyEnum.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:54-debug: Preimport db assets success
2025-10-9 09:19:54-debug: Run asset db hook programming:afterPreStart ...
2025-10-9 09:19:54-debug: starting packer-driver...
2025-10-9 09:20:01-debug: initialize scripting environment...
2025-10-9 09:20:01-debug: [[Executor]] prepare before lock
2025-10-9 09:20:01-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-10-9 09:20:01-debug: [[Executor]] prepare after unlock
2025-10-9 09:20:01-debug: Run asset db hook programming:afterPreStart success!
2025-10-9 09:20:01-debug: Run asset db hook engine-extends:afterPreStart ...
2025-10-9 09:20:01-debug: Run asset db hook engine-extends:afterPreStart success!
2025-10-9 09:20:01-debug: Start up the 'internal' database...
2025-10-9 09:20:01-debug: [Assets Memory track]: asset-db:worker-init: preStart start:225.40MB, end 241.21MB, increase: 15.80MB
2025-10-9 09:20:01-debug: asset-db-hook-programming-afterPreStart (7469ms)
2025-10-9 09:20:01-debug: asset-db:worker-effect-data-processing (273ms)
2025-10-9 09:20:01-debug: asset-db-hook-engine-extends-afterPreStart (273ms)
2025-10-9 09:20:01-debug: Start up the 'assets' database...
2025-10-9 09:20:02-debug: asset-db:worker-startup-database[internal] (7980ms)
2025-10-9 09:20:02-debug: lazy register asset handler *
2025-10-9 09:20:02-debug: lazy register asset handler directory
2025-10-9 09:20:02-debug: lazy register asset handler spine-data
2025-10-9 09:20:02-debug: lazy register asset handler text
2025-10-9 09:20:02-debug: lazy register asset handler dragonbones
2025-10-9 09:20:02-debug: lazy register asset handler dragonbones-atlas
2025-10-9 09:20:02-debug: lazy register asset handler json
2025-10-9 09:20:02-debug: lazy register asset handler terrain
2025-10-9 09:20:02-debug: lazy register asset handler javascript
2025-10-9 09:20:02-debug: lazy register asset handler sprite-frame
2025-10-9 09:20:02-debug: lazy register asset handler typescript
2025-10-9 09:20:02-debug: lazy register asset handler scene
2025-10-9 09:20:02-debug: lazy register asset handler prefab
2025-10-9 09:20:02-debug: lazy register asset handler buffer
2025-10-9 09:20:02-debug: lazy register asset handler tiled-map
2025-10-9 09:20:02-debug: lazy register asset handler sign-image
2025-10-9 09:20:02-debug: lazy register asset handler alpha-image
2025-10-9 09:20:02-debug: lazy register asset handler image
2025-10-9 09:20:02-debug: lazy register asset handler texture
2025-10-9 09:20:02-debug: lazy register asset handler erp-texture-cube
2025-10-9 09:20:02-debug: lazy register asset handler rt-sprite-frame
2025-10-9 09:20:02-debug: lazy register asset handler render-texture
2025-10-9 09:20:02-debug: lazy register asset handler texture-cube
2025-10-9 09:20:02-debug: lazy register asset handler gltf-mesh
2025-10-9 09:20:02-debug: lazy register asset handler gltf-animation
2025-10-9 09:20:02-debug: lazy register asset handler gltf
2025-10-9 09:20:02-debug: lazy register asset handler texture-cube-face
2025-10-9 09:20:02-debug: lazy register asset handler gltf-skeleton
2025-10-9 09:20:02-debug: lazy register asset handler gltf-material
2025-10-9 09:20:02-debug: lazy register asset handler gltf-scene
2025-10-9 09:20:02-debug: lazy register asset handler physics-material
2025-10-9 09:20:02-debug: lazy register asset handler gltf-embeded-image
2025-10-9 09:20:02-debug: lazy register asset handler fbx
2025-10-9 09:20:02-debug: lazy register asset handler material
2025-10-9 09:20:02-debug: lazy register asset handler effect-header
2025-10-9 09:20:02-debug: lazy register asset handler audio-clip
2025-10-9 09:20:02-debug: lazy register asset handler animation-clip
2025-10-9 09:20:02-debug: lazy register asset handler effect
2025-10-9 09:20:02-debug: lazy register asset handler animation-graph-variant
2025-10-9 09:20:02-debug: lazy register asset handler animation-graph
2025-10-9 09:20:02-debug: lazy register asset handler ttf-font
2025-10-9 09:20:02-debug: lazy register asset handler animation-mask
2025-10-9 09:20:02-debug: lazy register asset handler sprite-atlas
2025-10-9 09:20:02-debug: lazy register asset handler bitmap-font
2025-10-9 09:20:02-debug: lazy register asset handler particle
2025-10-9 09:20:02-debug: lazy register asset handler auto-atlas
2025-10-9 09:20:02-debug: lazy register asset handler render-stage
2025-10-9 09:20:02-debug: lazy register asset handler render-pipeline
2025-10-9 09:20:02-debug: lazy register asset handler label-atlas
2025-10-9 09:20:02-debug: lazy register asset handler instantiation-material
2025-10-9 09:20:02-debug: lazy register asset handler render-flow
2025-10-9 09:20:02-debug: lazy register asset handler instantiation-mesh
2025-10-9 09:20:02-debug: lazy register asset handler video-clip
2025-10-9 09:20:02-debug: lazy register asset handler instantiation-skeleton
2025-10-9 09:20:02-debug: lazy register asset handler instantiation-animation
2025-10-9 09:20:02-debug: asset-db:start-database (8088ms)
2025-10-9 09:20:02-debug: fix the bug of updateDefaultUserData
2025-10-9 09:20:02-debug: asset-db:worker-startup-database[assets] (7942ms)
2025-10-9 09:20:02-debug: asset-db:ready (11228ms)
2025-10-9 09:20:02-debug: init worker message success
2025-10-9 09:20:02-debug: programming:execute-script (3ms)
2025-10-9 09:20:02-debug: [Build Memory track]: builder:worker-init start:193.90MB, end 207.20MB, increase: 13.30MB
2025-10-9 09:20:02-debug: builder:worker-init (317ms)
2025-10-9 09:48:03-debug: refresh db internal success
2025-10-9 09:48:03-debug: refresh db i18n success
2025-10-9 09:48:04-debug: refresh db assets success
2025-10-9 09:48:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 09:48:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 09:48:04-debug: asset-db:refresh-all-database (254ms)
2025-10-9 09:48:04-debug: asset-db:worker-effect-data-processing (2ms)
2025-10-9 09:48:04-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-10-9 09:51:33-debug: refresh db internal success
2025-10-9 09:51:33-debug: refresh db i18n success
2025-10-9 09:51:33-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\weapon\Weapon.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:51:33-debug: refresh db assets success
2025-10-9 09:51:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 09:51:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 09:51:33-debug: asset-db:refresh-all-database (214ms)
2025-10-9 09:51:33-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 09:51:33-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 10:00:22-debug: refresh db internal success
2025-10-9 10:00:22-debug: refresh db i18n success
2025-10-9 10:00:22-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\preview\WavePreview.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:00:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\weapon\Weapon.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:00:22-debug: refresh db assets success
2025-10-9 10:00:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 10:00:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 10:00:22-debug: asset-db:refresh-all-database (246ms)
2025-10-9 10:00:22-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 10:00:22-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 10:00:35-debug: start refresh asset from db://assets/resources/game/level/background/Prefab/Config...
2025-10-9 10:00:35-debug: start refresh asset from db://assets/resources/game/level/background/Prefab/Config...
2025-10-9 10:00:35-debug: start refresh asset from db://assets/resources/game/level/background/Prefab/Config...
2025-10-9 10:00:35-debug: start refresh asset from db://assets/resources/game/level/background/Prefab/Config...
2025-10-9 10:00:35-debug: start refresh asset from db://assets/resources/game/level/background/Prefab/Config...
2025-10-9 10:00:35-debug: start refresh asset from db://assets/resources/game/level/background/Prefab/Config...
2025-10-9 10:00:35-debug: start refresh asset from db://assets/resources/game/level/background/Prefab/Config...
2025-10-9 10:00:35-debug: start refresh asset from db://assets/resources/game/level/background/Prefab/Config...
2025-10-9 10:00:35-debug: start refresh asset from db://assets/resources/game/level/background/Prefab/Config...
2025-10-9 10:00:35-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_StartCloud02.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:00:35-debug: refresh asset db://assets/resources/game/level/background/Prefab success
2025-10-9 10:00:35-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_Island_01.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:00:35-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_Island_14.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:00:35-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_Island_02.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:00:35-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_Island_15.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:00:35-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_Island_16.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:00:35-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_StartCloud01.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:00:35-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandomNod_LayerClouds_02_Long.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:00:35-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandomNod_LayerClouds_01_short.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:00:35-debug: refresh asset db://assets/resources/game/level/background/Prefab success
2025-10-9 10:00:35-debug: refresh asset db://assets/resources/game/level/background/Prefab success
2025-10-9 10:00:35-debug: refresh asset db://assets/resources/game/level/background/Prefab success
2025-10-9 10:00:35-debug: refresh asset db://assets/resources/game/level/background/Prefab success
2025-10-9 10:00:35-debug: refresh asset db://assets/resources/game/level/background/Prefab success
2025-10-9 10:00:35-debug: refresh asset db://assets/resources/game/level/background/Prefab success
2025-10-9 10:00:35-debug: refresh asset db://assets/resources/game/level/background/Prefab success
2025-10-9 10:00:35-debug: refresh asset db://assets/resources/game/level/background/Prefab success
2025-10-9 10:00:35-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_StartCloud02.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:00:35-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_StartCloud01.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:00:35-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_Island_01.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:00:35-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandomNod_LayerClouds_01_short.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:00:35-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandomNod_LayerClouds_02_Long.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:00:35-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_Island_14.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:00:35-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_Island_02.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:00:35-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_Island_16.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:00:35-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_Island_15.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:00:35-debug: asset-db:reimport-asset5165156c-8464-4aee-97ef-3cc8a5619632 (75ms)
2025-10-9 10:00:35-debug: asset-db:reimport-asset271467a7-62c8-465d-a373-bf2f4d45e3bc (74ms)
2025-10-9 10:00:35-debug: asset-db:reimport-asset43037389-e38f-4941-a90e-98d7f0819b94 (74ms)
2025-10-9 10:00:35-debug: asset-db:reimport-asset4eb60cb1-351f-4b70-aeb4-104a20af3493 (74ms)
2025-10-9 10:00:35-debug: asset-db:reimport-asset8e9a0448-202a-4b18-aeb4-2c6758698cde (74ms)
2025-10-9 10:00:35-debug: asset-db:reimport-assetd6f91465-f5e3-4846-b3d1-0c56dfb033bc (74ms)
2025-10-9 10:00:35-debug: asset-db:reimport-asset0aca13ea-da5c-4055-ae7d-b91e72dc8fa8 (74ms)
2025-10-9 10:00:35-debug: asset-db:reimport-asset4cb5bbf6-5eba-49a3-891e-63577031516f (74ms)
2025-10-9 10:00:35-debug: asset-db:reimport-assetd8162466-ff6e-4785-bdfe-4ebd4372517e (74ms)
2025-10-9 10:00:46-debug: refresh db internal success
2025-10-9 10:00:46-debug: refresh db i18n success
2025-10-9 10:00:46-debug: refresh db assets success
2025-10-9 10:00:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 10:00:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 10:00:46-debug: asset-db:worker-effect-data-processing (2ms)
2025-10-9 10:00:46-debug: asset-db:refresh-all-database (179ms)
2025-10-9 10:00:46-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-10-9 10:01:27-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\131000001.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:01:27-debug: asset-db:reimport-asset8e352073-9af1-4033-b6b6-f5db6fdccbc6 (4ms)
2025-10-9 10:01:30-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\wave\wave_精英飞机 一型路径 左至右 【上】.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:01:30-debug: asset-db:reimport-asset1d9e2cf3-9efe-444e-9d1a-7f667ae32a0b (3ms)
2025-10-9 10:02:02-debug: refresh db internal success
2025-10-9 10:02:02-debug: refresh db i18n success
2025-10-9 10:02:02-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\preview\WavePreview.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:02:02-debug: refresh db assets success
2025-10-9 10:02:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 10:02:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 10:02:02-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 10:02:02-debug: asset-db:refresh-all-database (233ms)
2025-10-9 10:02:03-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\131000001.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:02:03-debug: asset-db:reimport-asset8e352073-9af1-4033-b6b6-f5db6fdccbc6 (10ms)
2025-10-9 10:02:25-debug: refresh db internal success
2025-10-9 10:02:25-debug: refresh db i18n success
2025-10-9 10:02:25-debug: refresh db assets success
2025-10-9 10:02:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 10:02:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 10:02:25-debug: asset-db:refresh-all-database (183ms)
2025-10-9 10:02:25-debug: asset-db:worker-effect-data-processing (2ms)
2025-10-9 10:02:25-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-10-9 10:02:27-debug: refresh db internal success
2025-10-9 10:02:27-debug: refresh db i18n success
2025-10-9 10:02:27-debug: refresh db assets success
2025-10-9 10:02:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 10:02:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 10:02:27-debug: asset-db:refresh-all-database (171ms)
2025-10-9 10:02:27-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 10:02:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 10:03:27-debug: refresh db internal success
2025-10-9 10:03:27-debug: refresh db i18n success
2025-10-9 10:03:27-debug: refresh db assets success
2025-10-9 10:03:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 10:03:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 10:03:27-debug: asset-db:refresh-all-database (179ms)
2025-10-9 10:03:27-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 10:03:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 10:03:51-debug: refresh db internal success
2025-10-9 10:03:51-debug: refresh db i18n success
2025-10-9 10:03:51-debug: refresh db assets success
2025-10-9 10:03:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 10:03:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 10:03:51-debug: asset-db:refresh-all-database (232ms)
2025-10-9 10:03:51-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 10:03:51-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 10:05:04-debug: refresh db internal success
2025-10-9 10:05:04-debug: refresh db i18n success
2025-10-9 10:05:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 10:05:04-debug: refresh db assets success
2025-10-9 10:05:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 10:05:04-debug: asset-db:refresh-all-database (217ms)
2025-10-9 10:05:24-debug: refresh db internal success
2025-10-9 10:05:24-debug: refresh db i18n success
2025-10-9 10:05:25-debug: %cImport%c: E:\M2Game\Client\assets\scenes
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:05:25-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:05:25-debug: %cImport%c: E:\M2Game\Client\assets\scenes\Game.scene
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:05:25-debug: %cImport%c: E:\M2Game\Client\assets\scenes\PathEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:05:25-debug: refresh db assets success
2025-10-9 10:05:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 10:05:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 10:05:25-debug: asset-db:refresh-all-database (255ms)
2025-10-9 10:07:22-debug: start move asset from E:\M2Game\Client\assets\scenes\BulletEditor.scene -> E:\M2Game\Client\assets\editor\BulletEditor.scene...
2025-10-9 10:07:22-debug: start move asset from E:\M2Game\Client\assets\scenes\FormationEditor.scene -> E:\M2Game\Client\assets\editor\FormationEditor.scene...
2025-10-9 10:07:22-debug: start move asset from E:\M2Game\Client\assets\scenes\PathEditor.scene -> E:\M2Game\Client\assets\editor\PathEditor.scene...
2025-10-9 10:07:22-debug: start move asset from E:\M2Game\Client\assets\scenes\PlaneEditor.scene -> E:\M2Game\Client\assets\editor\PlaneEditor.scene...
2025-10-9 10:07:22-debug: start refresh asset from E:\M2Game\Client\assets\editor\BulletEditor.scene...
2025-10-9 10:07:22-debug: start refresh asset from E:\M2Game\Client\assets\editor\PathEditor.scene...
2025-10-9 10:07:22-debug: start refresh asset from E:\M2Game\Client\assets\editor\FormationEditor.scene...
2025-10-9 10:07:22-debug: start refresh asset from E:\M2Game\Client\assets\editor\PlaneEditor.scene...
2025-10-9 10:07:22-debug: %cImport%c: E:\M2Game\Client\assets\editor\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:07:22-debug: %cImport%c: E:\M2Game\Client\assets\editor\FormationEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:07:22-debug: %cImport%c: E:\M2Game\Client\assets\editor\PathEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:07:22-debug: %cImport%c: E:\M2Game\Client\assets\editor\PlaneEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:07:22-debug: refresh asset E:\M2Game\Client\assets\editor success
2025-10-9 10:07:22-debug: start refresh asset from E:\M2Game\Client\assets\scenes...
2025-10-9 10:07:22-debug: refresh asset E:\M2Game\Client\assets\editor success
2025-10-9 10:07:22-debug: refresh asset E:\M2Game\Client\assets\editor success
2025-10-9 10:07:22-debug: start refresh asset from E:\M2Game\Client\assets\scenes...
2025-10-9 10:07:22-debug: start refresh asset from E:\M2Game\Client\assets\scenes...
2025-10-9 10:07:22-debug: refresh asset E:\M2Game\Client\assets\editor success
2025-10-9 10:07:22-debug: start refresh asset from E:\M2Game\Client\assets\scenes...
2025-10-9 10:07:22-debug: %cImport%c: E:\M2Game\Client\assets\scenes
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:07:22-debug: refresh asset E:\M2Game\Client\assets success
2025-10-9 10:07:22-debug: move asset from E:\M2Game\Client\assets\scenes\BulletEditor.scene -> E:\M2Game\Client\assets\editor\BulletEditor.scene success
2025-10-9 10:07:22-debug: refresh asset E:\M2Game\Client\assets success
2025-10-9 10:07:22-debug: move asset from E:\M2Game\Client\assets\scenes\FormationEditor.scene -> E:\M2Game\Client\assets\editor\FormationEditor.scene success
2025-10-9 10:07:22-debug: refresh asset E:\M2Game\Client\assets success
2025-10-9 10:07:22-debug: move asset from E:\M2Game\Client\assets\scenes\PathEditor.scene -> E:\M2Game\Client\assets\editor\PathEditor.scene success
2025-10-9 10:07:22-debug: refresh asset E:\M2Game\Client\assets success
2025-10-9 10:07:22-debug: move asset from E:\M2Game\Client\assets\scenes\PlaneEditor.scene -> E:\M2Game\Client\assets\editor\PlaneEditor.scene success
2025-10-9 10:07:22-debug: %cImport%c: E:\M2Game\Client\assets\editor
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:10:26-debug: refresh db internal success
2025-10-9 10:10:26-debug: refresh db i18n success
2025-10-9 10:10:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 10:10:26-debug: refresh db assets success
2025-10-9 10:10:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 10:10:26-debug: asset-db:refresh-all-database (232ms)
2025-10-9 10:10:26-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 10:10:26-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 10:10:39-debug: refresh db internal success
2025-10-9 10:10:39-debug: refresh db i18n success
2025-10-9 10:10:39-debug: refresh db assets success
2025-10-9 10:10:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 10:10:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 10:10:39-debug: asset-db:refresh-all-database (246ms)
2025-10-9 10:10:39-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 10:10:44-debug: refresh db internal success
2025-10-9 10:10:44-debug: refresh db i18n success
2025-10-9 10:10:44-debug: refresh db assets success
2025-10-9 10:10:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 10:10:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 10:10:44-debug: asset-db:refresh-all-database (170ms)
2025-10-9 10:10:44-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 10:10:44-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 10:10:53-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\131000001.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:10:53-debug: asset-db:reimport-asset8e352073-9af1-4033-b6b6-f5db6fdccbc6 (5ms)
2025-10-9 10:10:54-debug: refresh db internal success
2025-10-9 10:10:54-debug: refresh db i18n success
2025-10-9 10:10:54-debug: refresh db assets success
2025-10-9 10:10:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 10:10:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 10:10:54-debug: asset-db:refresh-all-database (196ms)
2025-10-9 10:10:54-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 10:10:54-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 10:14:24-debug: Query all assets info in project
2025-10-9 10:14:24-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-9 10:14:24-debug: Skip compress image, progress: 0%
2025-10-9 10:14:24-debug: Init all bundles start..., progress: 0%
2025-10-9 10:14:24-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 10:14:24-debug: 查询 Asset Bundle start, progress: 0%
2025-10-9 10:14:24-debug: Num of bundles: 18..., progress: 0%
2025-10-9 10:14:24-debug: Init bundle root assets start..., progress: 0%
2025-10-9 10:14:24-debug:   Number of all scripts: 312
2025-10-9 10:14:24-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-9 10:14:24-debug:   Number of all scenes: 11
2025-10-9 10:14:24-debug:   Number of other assets: 2659
2025-10-9 10:14:24-debug: Init bundle root assets success..., progress: 0%
2025-10-9 10:14:24-debug: // ---- build task 查询 Asset Bundle ---- (36ms)
2025-10-9 10:14:24-debug: [Build Memory track]: 查询 Asset Bundle start:206.70MB, end 205.82MB, increase: -898.69KB
2025-10-9 10:14:24-debug: 查询 Asset Bundle start, progress: 5%
2025-10-9 10:14:24-log: run build task 查询 Asset Bundle success in 36 ms√, progress: 5%
2025-10-9 10:14:24-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 10:14:24-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-10-9 10:14:24-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-10-9 10:14:24-debug: [Build Memory track]: 查询 Asset Bundle start:205.85MB, end 205.33MB, increase: -538.48KB
2025-10-9 10:14:24-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 10:14:24-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-9 10:14:24-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:205.37MB, end 205.39MB, increase: 26.18KB
2025-10-9 10:14:24-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-10-9 10:14:24-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-9 10:14:24-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-9 10:14:24-debug: [Build Memory track]: 填充脚本数据到 settings.json start:205.43MB, end 205.45MB, increase: 19.30KB
2025-10-9 10:14:24-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-10-9 10:14:24-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-9 10:14:24-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 10:14:24-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-10-9 10:14:24-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-10-9 10:14:24-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:205.49MB, end 205.82MB, increase: 336.07KB
2025-10-9 10:14:26-debug: refresh db internal success
2025-10-9 10:14:26-debug: refresh db i18n success
2025-10-9 10:14:26-debug: refresh db assets success
2025-10-9 10:14:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 10:14:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 10:14:26-debug: asset-db:refresh-all-database (173ms)
2025-10-9 10:14:26-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 10:14:43-debug: refresh db internal success
2025-10-9 10:14:43-debug: refresh db i18n success
2025-10-9 10:14:44-debug: refresh db assets success
2025-10-9 10:14:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 10:14:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 10:14:44-debug: asset-db:refresh-all-database (185ms)
2025-10-9 10:14:44-debug: asset-db:worker-effect-data-processing (3ms)
2025-10-9 10:14:44-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-10-9 10:14:53-debug: Query all assets info in project
2025-10-9 10:14:53-debug: Skip compress image, progress: 0%
2025-10-9 10:14:53-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-9 10:14:53-debug: Init all bundles start..., progress: 0%
2025-10-9 10:14:53-debug: Num of bundles: 18..., progress: 0%
2025-10-9 10:14:53-debug: 查询 Asset Bundle start, progress: 0%
2025-10-9 10:14:53-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 10:14:53-debug: Init bundle root assets start..., progress: 0%
2025-10-9 10:14:53-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-9 10:14:53-debug:   Number of other assets: 2659
2025-10-9 10:14:53-debug:   Number of all scenes: 11
2025-10-9 10:14:53-debug:   Number of all scripts: 312
2025-10-9 10:14:53-debug: Init bundle root assets success..., progress: 0%
2025-10-9 10:14:53-debug: // ---- build task 查询 Asset Bundle ---- (29ms)
2025-10-9 10:14:53-log: run build task 查询 Asset Bundle success in 29 ms√, progress: 5%
2025-10-9 10:14:53-debug: [Build Memory track]: 查询 Asset Bundle start:211.88MB, end 212.84MB, increase: 983.05KB
2025-10-9 10:14:53-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 10:14:53-debug: 查询 Asset Bundle start, progress: 5%
2025-10-9 10:14:53-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-10-9 10:14:53-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-10-9 10:14:53-debug: [Build Memory track]: 查询 Asset Bundle start:212.87MB, end 213.25MB, increase: 386.18KB
2025-10-9 10:14:53-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 10:14:53-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-9 10:14:53-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:213.29MB, end 213.30MB, increase: 17.88KB
2025-10-9 10:14:53-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-9 10:14:53-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-10-9 10:14:53-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-9 10:14:53-debug: [Build Memory track]: 填充脚本数据到 settings.json start:213.33MB, end 213.35MB, increase: 21.55KB
2025-10-9 10:14:53-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-10-9 10:14:53-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-9 10:14:53-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 10:14:53-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-10-9 10:14:53-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:213.38MB, end 213.70MB, increase: 323.47KB
2025-10-9 10:14:53-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-10-9 10:14:57-debug: refresh db internal success
2025-10-9 10:14:57-debug: refresh db i18n success
2025-10-9 10:14:57-debug: refresh db assets success
2025-10-9 10:14:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 10:14:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 10:14:57-debug: asset-db:refresh-all-database (160ms)
2025-10-9 10:14:57-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 10:14:57-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 10:15:02-debug: Query all assets info in project
2025-10-9 10:15:02-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-9 10:15:02-debug: Skip compress image, progress: 0%
2025-10-9 10:15:02-debug: Init all bundles start..., progress: 0%
2025-10-9 10:15:02-debug: Num of bundles: 18..., progress: 0%
2025-10-9 10:15:02-debug: 查询 Asset Bundle start, progress: 0%
2025-10-9 10:15:02-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 10:15:02-debug: Init bundle root assets start..., progress: 0%
2025-10-9 10:15:02-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-9 10:15:02-debug:   Number of other assets: 2659
2025-10-9 10:15:02-debug:   Number of all scenes: 11
2025-10-9 10:15:02-debug:   Number of all scripts: 312
2025-10-9 10:15:02-debug: Init bundle root assets success..., progress: 0%
2025-10-9 10:15:02-debug: // ---- build task 查询 Asset Bundle ---- (24ms)
2025-10-9 10:15:02-log: run build task 查询 Asset Bundle success in 24 ms√, progress: 5%
2025-10-9 10:15:02-debug: 查询 Asset Bundle start, progress: 5%
2025-10-9 10:15:02-debug: [Build Memory track]: 查询 Asset Bundle start:216.15MB, end 218.54MB, increase: 2.39MB
2025-10-9 10:15:02-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 10:15:02-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-10-9 10:15:02-debug: [Build Memory track]: 查询 Asset Bundle start:218.57MB, end 218.95MB, increase: 387.36KB
2025-10-9 10:15:02-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-10-9 10:15:02-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-9 10:15:02-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 10:15:02-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-10-9 10:15:02-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:218.98MB, end 219.00MB, increase: 17.12KB
2025-10-9 10:15:02-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-9 10:15:02-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-9 10:15:02-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-10-9 10:15:02-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-10-9 10:15:02-debug: [Build Memory track]: 填充脚本数据到 settings.json start:219.02MB, end 219.05MB, increase: 26.55KB
2025-10-9 10:15:02-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-9 10:15:02-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 10:15:02-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-10-9 10:15:02-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-10-9 10:15:02-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:219.08MB, end 219.39MB, increase: 322.62KB
2025-10-9 10:15:05-debug: refresh db internal success
2025-10-9 10:15:05-debug: refresh db i18n success
2025-10-9 10:15:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 10:15:06-debug: refresh db assets success
2025-10-9 10:15:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 10:15:06-debug: asset-db:refresh-all-database (159ms)
2025-10-9 10:15:45-debug: refresh db internal success
2025-10-9 10:15:45-debug: refresh db i18n success
2025-10-9 10:15:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 10:15:45-debug: refresh db assets success
2025-10-9 10:15:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 10:15:45-debug: asset-db:refresh-all-database (236ms)
2025-10-9 10:15:45-debug: asset-db:worker-effect-data-processing (2ms)
2025-10-9 10:15:45-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-10-9 10:15:58-debug: refresh db internal success
2025-10-9 10:15:58-debug: refresh db i18n success
2025-10-9 10:15:58-debug: refresh db assets success
2025-10-9 10:15:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 10:15:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 10:15:58-debug: asset-db:refresh-all-database (210ms)
2025-10-9 10:15:58-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 10:15:58-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 10:16:40-debug: start refresh asset from E:\M2Game\Client\assets\editor\plane...
2025-10-9 10:16:40-debug: %cImport%c: E:\M2Game\Client\assets\editor\plane
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:16:40-debug: refresh asset E:\M2Game\Client\assets\editor success
2025-10-9 10:16:40-debug: %cImport%c: E:\M2Game\Client\assets\editor
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:16:58-debug: start refresh asset from E:\M2Game\Client\assets\editor\plane\PlaneEditor.ts...
2025-10-9 10:16:58-debug: %cImport%c: E:\M2Game\Client\assets\editor\plane\PlaneEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:16:58-debug: refresh asset E:\M2Game\Client\assets\editor\plane success
2025-10-9 10:16:58-debug: %cImport%c: E:\M2Game\Client\assets\editor\plane
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:18:15-debug: refresh db internal success
2025-10-9 10:18:15-debug: refresh db i18n success
2025-10-9 10:18:15-debug: %cImport%c: E:\M2Game\Client\assets\editor\plane\PlaneEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:18:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 10:18:15-debug: refresh db assets success
2025-10-9 10:18:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 10:18:15-debug: asset-db:refresh-all-database (216ms)
2025-10-9 10:18:15-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-10-9 10:18:15-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 10:18:19-debug: Query all assets info in project
2025-10-9 10:18:19-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-9 10:18:19-debug: Skip compress image, progress: 0%
2025-10-9 10:18:19-debug: Init all bundles start..., progress: 0%
2025-10-9 10:18:19-debug: Num of bundles: 18..., progress: 0%
2025-10-9 10:18:19-debug: 查询 Asset Bundle start, progress: 0%
2025-10-9 10:18:19-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 10:18:19-debug: Init bundle root assets start..., progress: 0%
2025-10-9 10:18:19-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-9 10:18:19-debug:   Number of other assets: 2659
2025-10-9 10:18:19-debug: Init bundle root assets success..., progress: 0%
2025-10-9 10:18:19-debug:   Number of all scenes: 11
2025-10-9 10:18:19-debug:   Number of all scripts: 313
2025-10-9 10:18:19-debug: // ---- build task 查询 Asset Bundle ---- (40ms)
2025-10-9 10:18:19-log: run build task 查询 Asset Bundle success in 40 ms√, progress: 5%
2025-10-9 10:18:19-debug: [Build Memory track]: 查询 Asset Bundle start:222.20MB, end 219.59MB, increase: -2668.34KB
2025-10-9 10:18:19-debug: 查询 Asset Bundle start, progress: 5%
2025-10-9 10:18:19-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 10:18:19-debug: // ---- build task 查询 Asset Bundle ---- (5ms)
2025-10-9 10:18:19-log: run build task 查询 Asset Bundle success in 5 ms√, progress: 10%
2025-10-9 10:18:19-debug: [Build Memory track]: 查询 Asset Bundle start:219.62MB, end 220.00MB, increase: 386.84KB
2025-10-9 10:18:19-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-9 10:18:19-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 10:18:19-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-10-9 10:18:19-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-10-9 10:18:19-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-9 10:18:19-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:220.03MB, end 220.05MB, increase: 24.88KB
2025-10-9 10:18:19-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-9 10:18:20-debug: // ---- build task 填充脚本数据到 settings.json ---- (19ms)
2025-10-9 10:18:20-debug: [Build Memory track]: 填充脚本数据到 settings.json start:220.08MB, end 202.63MB, increase: -17865.74KB
2025-10-9 10:18:20-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-9 10:18:20-log: run build task 填充脚本数据到 settings.json success in 19 ms√, progress: 13%
2025-10-9 10:18:20-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 10:18:20-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-10-9 10:18:20-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:202.66MB, end 203.01MB, increase: 360.05KB
2025-10-9 10:18:20-log: run build task 整理部分构建选项内数据到 settings.json success in 3 ms√, progress: 15%
2025-10-9 10:18:22-debug: refresh db internal success
2025-10-9 10:18:22-debug: refresh db i18n success
2025-10-9 10:18:22-debug: refresh db assets success
2025-10-9 10:18:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 10:18:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 10:18:22-debug: asset-db:refresh-all-database (200ms)
2025-10-9 10:18:22-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 10:18:22-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 10:18:24-debug: refresh db internal success
2025-10-9 10:18:24-debug: refresh db i18n success
2025-10-9 10:18:24-debug: refresh db assets success
2025-10-9 10:18:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 10:18:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 10:18:24-debug: asset-db:refresh-all-database (244ms)
2025-10-9 10:18:24-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 10:18:24-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 10:18:27-debug: Query all assets info in project
2025-10-9 10:18:27-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-9 10:18:27-debug: Skip compress image, progress: 0%
2025-10-9 10:18:27-debug: Init all bundles start..., progress: 0%
2025-10-9 10:18:27-debug: Num of bundles: 18..., progress: 0%
2025-10-9 10:18:27-debug: 查询 Asset Bundle start, progress: 0%
2025-10-9 10:18:27-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 10:18:27-debug: Init bundle root assets start..., progress: 0%
2025-10-9 10:18:27-debug:   Number of all scenes: 11
2025-10-9 10:18:27-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-9 10:18:27-debug:   Number of all scripts: 313
2025-10-9 10:18:27-debug:   Number of other assets: 2659
2025-10-9 10:18:27-debug: Init bundle root assets success..., progress: 0%
2025-10-9 10:18:27-log: run build task 查询 Asset Bundle success in 28 ms√, progress: 5%
2025-10-9 10:18:27-debug: // ---- build task 查询 Asset Bundle ---- (28ms)
2025-10-9 10:18:27-debug: // ---- build task 查询 Asset Bundle ----
2025-10-9 10:18:27-debug: [Build Memory track]: 查询 Asset Bundle start:205.82MB, end 208.60MB, increase: 2.78MB
2025-10-9 10:18:27-debug: 查询 Asset Bundle start, progress: 5%
2025-10-9 10:18:27-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-10-9 10:18:27-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-10-9 10:18:27-debug: [Build Memory track]: 查询 Asset Bundle start:208.66MB, end 209.04MB, increase: 380.61KB
2025-10-9 10:18:27-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-9 10:18:27-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 10:18:27-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:209.06MB, end 209.09MB, increase: 28.27KB
2025-10-9 10:18:27-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-10-9 10:18:27-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-9 10:18:27-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-10-9 10:18:27-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-9 10:18:27-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-10-9 10:18:27-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-9 10:18:27-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-9 10:18:27-debug: [Build Memory track]: 填充脚本数据到 settings.json start:209.12MB, end 209.14MB, increase: 16.91KB
2025-10-9 10:18:27-log: run build task 整理部分构建选项内数据到 settings.json success in 3 ms√, progress: 15%
2025-10-9 10:18:27-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:209.17MB, end 209.49MB, increase: 329.96KB
2025-10-9 10:18:27-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-10-9 10:18:31-debug: refresh db internal success
2025-10-9 10:18:31-debug: refresh db i18n success
2025-10-9 10:18:31-debug: refresh db assets success
2025-10-9 10:18:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 10:18:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 10:18:31-debug: asset-db:refresh-all-database (167ms)
2025-10-9 10:18:31-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 10:18:31-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 10:19:19-debug: refresh db internal success
2025-10-9 10:19:19-debug: refresh db i18n success
2025-10-9 10:19:19-debug: refresh db assets success
2025-10-9 10:19:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 10:19:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 10:19:19-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 10:19:19-debug: asset-db:refresh-all-database (207ms)
2025-10-9 10:19:19-debug: asset-db-hook-engine-extends-afterRefresh (1ms)

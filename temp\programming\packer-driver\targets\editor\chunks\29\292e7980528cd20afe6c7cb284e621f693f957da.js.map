{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/mainPlane/MainPlane.ts"], "names": ["_decorator", "instantiate", "Node", "Prefab", "size", "UIOpacity", "Vec3", "view", "MyApp", "AttributeConst", "GameConst", "Plane", "EDITOR", "PlaneBase", "Bullet", "Emitter", "FBoxCollider", "ColliderGroupType", "GameResourceList", "GameIns", "eEntityTag", "EffectLayer", "EnemyPlaneBase", "MainPlaneDebug", "MainPlaneStat", "GameFightUI", "ccclass", "property", "MainPlane", "m_moveEnable", "emitter<PERSON>omp", "_hurtActTime", "_hurtActDuration", "_planeData", "_plane", "_fireEnable", "_unColliderTime", "statData", "_maxMoveSpeedX", "_maxMoveSpeedY", "_screenRatio", "_battleWidth", "_targetPosition", "_lastPosition", "hpRecoveryTime", "_nuclearNum", "nuclearNum", "addNuclear", "num", "addScore", "score", "onLoad", "_calculateScreenRatio", "node", "getPosition", "set", "update", "dt", "battleManager", "animSpeed", "cancelUncollide", "smoothMoveToTarget", "updateGameLogic", "gameDataManager", "gameTime", "hpRecovery", "attribute", "getHPRecovery", "addHp", "initPlane", "planeData", "reset<PERSON>lane", "getFinalAttributeByKey", "NuclearMax", "prefab", "resMgr", "loadAsync", "recoursePrefab", "plane", "planeMgr", "getPlane", "getComponent", "planeParent", "<PERSON><PERSON><PERSON><PERSON>", "collide<PERSON>omp", "getComponentInChildren", "init", "groupType", "PLAYER", "addTag", "Player", "addComponent", "reset", "setFireEnable", "setMoveAble", "colliderEnabled", "isDead", "curHp", "maxHp", "updateHpUI", "targetY", "getVisibleSize", "height", "setPosition", "instance", "updatePlayerUI", "setEmitter", "path", "EmitterPrefabPath", "then", "NodeEmitter", "setEntity", "setIsActive", "emitterId", "planeIn", "opacity", "scheduleOnce", "onEnter", "onPlaneIn", "onCollide", "collision", "damage", "entity", "StatusImmuneBulletHurt", "calcDamage", "hurt", "collisionPlane", "onControl", "localX", "touchY", "worldPos", "_convertTouchToWorld", "isLeft", "position", "x", "onMoveCommand", "halfWidth", "halfHeight", "ViewHeight", "posX", "Math", "min", "max", "posY", "dx", "dy", "y", "distance", "sqrt", "maxMoveX", "maxMoveY", "newX", "newY", "abs", "sign", "begine", "isContinue", "setUncollideByTime", "revive", "active", "playHurtAnim", "showRedScreen", "to<PERSON><PERSON>", "setGameEnd", "collisionLevel", "config", "collideLevel", "collisionHurt", "collideDamage", "getAttack", "enable", "time", "InvincibleNode", "setAnimSpeed", "speed", "pickDiamond<PERSON>um", "<PERSON><PERSON><PERSON><PERSON>", "killEnemyNum", "killEnemy", "usedNuclearNum", "usedNuclear", "usedSuperNum", "usedSuper", "visibleSize", "width", "designWidth", "ViewBattleWidth", "touchX", "scaleX", "scaleY", "designHeight", "screenX", "screenY", "worldX", "worldY"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;;AAG9DC,MAAAA,K,iBAAAA,K;;AAEAC,MAAAA,c,iBAAAA,c;;AAEAC,MAAAA,S,iBAAAA,S;;AAGAC,MAAAA,K,iBAAAA,K;;AAEAC,MAAAA,M,UAAAA,M;;AACFC,MAAAA,S;;AACEC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,Y;;AACaC,MAAAA,iB,kBAAAA,iB;;AACbC,MAAAA,gB;;AACEC,MAAAA,O,kBAAAA,O;;AACAC,MAAAA,U,kBAAAA,U;;AACFC,MAAAA,W;;AACAC,MAAAA,c;;AACAC,MAAAA,c;;AACAC,MAAAA,a;;AACEC,MAAAA,W,kBAAAA,W;;;;;;;;;OAxBH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwB3B,U;;2BA2BjB4B,S,WADZF,OAAO,CAAC,WAAD,C,UAGHC,QAAQ,CAACzB,IAAD,C,UAERyB,QAAQ,CAACzB,IAAD,C,UAERyB,QAAQ,CAACzB,IAAD,C,2BAPb,MACa0B,SADb;AAAA;AAAA,kCACyC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eASrCC,YATqC,GAStB,IATsB;AAShB;AATgB,eAUrCC,WAVqC,GAUP,IAVO;AAUD;AAVC,eAYrCC,YAZqC,GAYtB,CAZsB;AAYnB;AAZmB,eAarCC,gBAbqC,GAalB,GAbkB;AAab;AAba,eAerCC,UAfqC,GAeF,IAfE;AAeG;AAfH,eAgBrCC,MAhBqC,GAgBd,IAhBc;AAgBT;AAhBS,eAiBrCC,WAjBqC,GAiBvB,IAjBuB;AAiBlB;AAjBkB,eAkBrCC,eAlBqC,GAkBX,CAlBW;AAkBT;AAlBS,eAmBrCC,QAnBqC,GAmBX;AAAA;AAAA,+CAnBW;AAAA,eAqB7BC,cArB6B,GAqBJ,IArBI;AAqBE;AArBF,eAsB7BC,cAtB6B,GAsBJ,IAtBI;AAsBE;AAtBF,eAuB7BC,YAvB6B,GAuBN,CAvBM;AAAA,eAwB7BC,YAxB6B,GAwBN,CAxBM;AAwBH;AAxBG,eAyB7BC,eAzB6B,GAyBL,IAAIpC,IAAJ,EAzBK;AAyBO;AAzBP,eA0B7BqC,aA1B6B,GA0BP,IAAIrC,IAAJ,EA1BO;AA0BK;AA1BL,eA4B7BsC,cA5B6B,GA4BZ,CA5BY;AAAA,eA6B7BC,WA7B6B,GA6Bf,CA7Be;AAAA;;AA8BvB,YAAVC,UAAU,GAAG;AACb,iBAAO,KAAKD,WAAZ;AACH;;AACDE,QAAAA,UAAU,CAACC,GAAD,EAAc;AACpB,eAAKH,WAAL,IAAoBG,GAApB;AACH;;AACDC,QAAAA,QAAQ,CAACD,GAAD,EAAc;AAClB,eAAKX,QAAL,CAAca,KAAd,IAAuBF,GAAvB;AACH;;AAEDG,QAAAA,MAAM,GAAG;AACL;AACA,eAAKC,qBAAL,GAFK,CAIL;;;AACA,eAAKC,IAAL,CAAUC,WAAV,CAAsB,KAAKX,aAA3B;;AACA,eAAKD,eAAL,CAAqBa,GAArB,CAAyB,KAAKZ,aAA9B;AACH,SA/CoC,CAiDrC;;;AACAa,QAAAA,MAAM,CAACC,EAAD,EAAa;AACfA,UAAAA,EAAE,GAAGA,EAAE,GAAG;AAAA;AAAA,kCAAQC,aAAR,CAAsBC,SAAhC;AACA,eAAK5B,YAAL,IAAqB0B,EAArB;;AAEA,cAAI,KAAKrB,eAAL,GAAuB,CAA3B,EAA8B;AAC1B,iBAAKA,eAAL,IAAwBqB,EAAxB;;AACA,gBAAI,KAAKrB,eAAL,IAAwB,CAA5B,EAA+B;AAC3B,mBAAKwB,eAAL;AACH;AACJ;;AAED,eAAKC,kBAAL,CAAwBJ,EAAxB;AACH;;AAEDK,QAAAA,eAAe,CAACL,EAAD,EAAmB;AAC9B,gBAAMK,eAAN,CAAsBL,EAAtB;;AACA,iBAAO,KAAKb,cAAL,IAAuB;AAAA;AAAA,kCAAQmB,eAAR,CAAwBC,QAAtD,EAAgE;AAC5D,iBAAKpB,cAAL,IAAuB,CAAvB;AACA,gBAAIqB,UAAU,GAAG,KAAKC,SAAL,CAAeC,aAAf,EAAjB;AACA,iBAAKC,KAAL,CAAWH,UAAX;AACH;AACJ;;AAEc,cAATI,SAAS,CAACC,SAAD,EAA2B;AACtC,eAAKrC,UAAL,GAAkBqC,SAAlB;AACA,eAAKC,UAAL;AAEA,eAAK1B,WAAL,GAAmByB,SAAS,CAACE,sBAAV,CAAiC;AAAA;AAAA,gDAAeC,UAAhD,CAAnB,CAJsC,CAKtC;;AACA,gBAAMC,MAAM,GAAG,MAAM;AAAA;AAAA,8BAAMC,MAAN,CAAaC,SAAb,CAAuBN,SAAS,CAACO,cAAjC,EAAiD1E,MAAjD,CAArB;AACA,cAAI2E,KAAK,GAAG;AAAA;AAAA,8BAAMC,QAAN,CAAeC,QAAf,CAAwBV,SAAxB,EAAmCI,MAAnC,CAAZ;AACA,eAAKxC,MAAL,GAAc4C,KAAK,CAACG,YAAN;AAAA;AAAA,6BAAd;AACA,eAAKC,WAAL,CAAkBC,QAAlB,CAA2BL,KAA3B;AAEA,eAAKM,WAAL,GAAmB,KAAKC,sBAAL;AAAA;AAAA,2CAAnB;AACA,eAAKD,WAAL,CAAkBE,IAAlB,CAAuB,IAAvB,EAA6BlF,IAAI,CAAC,GAAD,EAAM,GAAN,CAAjC,EAZsC,CAYQ;;AAC9C,eAAKgF,WAAL,CAAkBG,SAAlB,GAA8B;AAAA;AAAA,sDAAkBC,MAAhD;AAEA,eAAKC,MAAL,CAAY;AAAA;AAAA,wCAAWC,MAAvB,EAfsC,CAiBtC;AACA;;AAEA,gBAAMJ,IAAN;;AAEA,cAAI1E,MAAJ,EAAY;AACR,iBAAKyC,IAAL,CAAUsC,YAAV;AAAA;AAAA;AACH;AACJ;;AAEDpB,QAAAA,UAAU,GAAG;AAAA;;AACT,+BAAKrC,MAAL,0BAAa0D,KAAb,GADS,CAET;;AACA,eAAKC,aAAL,CAAmB,KAAnB;AACA,eAAKC,WAAL,CAAiB,KAAjB;AACA,eAAKC,eAAL,GAAuB,KAAvB;AACA,eAAKC,MAAL,GAAc,KAAd;AACA,eAAKC,KAAL,GAAa,KAAKC,KAAlB;AACA,eAAKC,UAAL;AAEA,eAAKvD,cAAL,GAAsB,CAAtB;AAEA,gBAAMwD,OAAO,GAAG,CAAC7F,IAAI,CAAC8F,cAAL,GAAsBC,MAAvB,GAAgC,CAAhC,GAAoC,GAApD;AACA,eAAKjD,IAAL,CAAUkD,WAAV,CAAsB,CAAtB,EAAyBH,OAAzB;;AACA,eAAK1D,eAAL,CAAqBa,GAArB,CAAyB,CAAzB,EAA4B6C,OAA5B,EAAqC,CAArC;;AACA,eAAKzD,aAAL,CAAmBY,GAAnB,CAAuB,CAAvB,EAA0B6C,OAA1B,EAAmC,CAAnC;AACH;;AAEDD,QAAAA,UAAU,GAAE;AACR;AAAA;AAAA,0CAAYK,QAAZ,CAAqBC,cAArB;AACH;;AAEDC,QAAAA,UAAU,GAAG;AACT;AACA,cAAIC,IAAI,GAAG;AAAA;AAAA,oDAAiBC,iBAAjB,GAAqC,iBAAhD;AACA;AAAA;AAAA,8BAAMjC,MAAN,CAAaC,SAAb,CAAuB+B,IAAvB,EAA6BxG,MAA7B,EAAqC0G,IAArC,CAA2CnC,MAAD,IAAY;AAAA;;AAClD,gBAAIrB,IAAI,GAAGpD,WAAW,CAACyE,MAAD,CAAtB;AACA,sCAAKoC,WAAL,+BAAkB3B,QAAlB,CAA2B9B,IAA3B;AACAA,YAAAA,IAAI,CAACkD,WAAL,CAAiB,CAAjB,EAAoB,CAApB;AAEA,iBAAKzE,WAAL,GAAmBuB,IAAI,CAAC4B,YAAL;AAAA;AAAA,mCAAnB;AACA,iBAAKnD,WAAL,CAAkBiF,SAAlB,CAA4B,IAA5B;AACA,iBAAKjF,WAAL,CAAkBkF,WAAlB,CAA8B,KAAK7E,WAAnC;AACA,iBAAKL,WAAL,CAAkBmF,SAAlB,GAA8B,OAA9B;AACH,WATD;AAUH;AAED;AACJ;AACA;;;AACIC,QAAAA,OAAO,GAAS;AACZ,eAAK7D,IAAL,CAAU4B,YAAV,CAAuB5E,SAAvB,EAAmC8G,OAAnC,GAA6C,CAA7C;AACA,eAAKC,YAAL,CAAkB,MAAM;AAAA;;AACpB,iBAAK/D,IAAL,CAAU4B,YAAV,CAAuB5E,SAAvB,EAAmC8G,OAAnC,GAA6C,GAA7C;AACA,kCAAKjF,MAAL,2BAAamF,OAAb,CAAqB,MAAM;AACvB;AAAA;AAAA,sCAAQ3D,aAAR,CAAsB4D,SAAtB;AACH,aAFD;AAGH,WALD,EAKG,GALH;AAMH;AAGD;AACJ;AACA;AACA;;;AACIC,QAAAA,SAAS,CAACC,SAAD,EAAuB;AAC5B,cAAIC,MAAM,GAAG,CAAb;;AACA,cAAID,SAAS,CAACE,MAAV;AAAA;AAAA,+BAAJ,EAAwC;AACpC,gBAAI,KAAKxD,SAAL,CAAeM,sBAAf,CAAsC;AAAA;AAAA,kDAAemD,sBAArD,KAAgF,CAApF,EAAuF;AACnFF,cAAAA,MAAM,GAAGD,SAAS,CAACE,MAAV,CAAiBE,UAAjB,CAA4B,IAA5B,CAAT;AACH;;AACD,gBAAIH,MAAM,GAAG,CAAb,EAAgB;AACZ,mBAAKI,IAAL,CAAUJ,MAAV;AACH;AACJ,WAPD,MAOO,IAAID,SAAS,CAACE,MAAV;AAAA;AAAA,+CAAJ,EAAgD;AACnD,iBAAKI,cAAL,CAAoBN,SAAS,CAACE,MAA9B;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIK,QAAAA,SAAS,CAACC,MAAD,EAAiBC,MAAjB,EAAiC;AACtC,cAAI,CAAC,KAAKjC,MAAN,IAAgB,KAAKnE,YAAzB,EAAuC;AAAA;;AACnC;AACA,kBAAMqG,QAAQ,GAAG,KAAKC,oBAAL,CAA0BH,MAA1B,EAAkCC,MAAlC,CAAjB;;AAEA,gBAAIG,MAAM,GAAGJ,MAAM,GAAG,KAAK3E,IAAL,CAAUgF,QAAV,CAAmBC,CAAzC;AACA,kCAAKpG,MAAL,2BAAaqG,aAAb,CAA2BH,MAA3B,EALmC,CAOnC;;AACA,kBAAMI,SAAS,GAAG,KAAK/F,YAAL,GAAoB,CAAtC;AACA,kBAAMgG,UAAU,GAAG;AAAA;AAAA,wCAAUC,UAAV,GAAuB,CAA1C,CATmC,CAWnC;;AACA,gBAAIC,IAAI,GAAGC,IAAI,CAACC,GAAL,CAASL,SAAT,EAAoBI,IAAI,CAACE,GAAL,CAAS,CAACN,SAAV,EAAqBR,MAArB,CAApB,CAAX;AACA,gBAAIe,IAAI,GAAGH,IAAI,CAACC,GAAL,CAASJ,UAAT,EAAqBG,IAAI,CAACE,GAAL,CAAS,CAACL,UAAV,EAAsBR,MAAtB,CAArB,CAAX;;AAEA,iBAAKvF,eAAL,CAAqBa,GAArB,CAAyBoF,IAAzB,EAA+BI,IAA/B,EAAqC,CAArC,EAfmC,CAgBnC;AAEA;AACA;;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACYlF,QAAAA,kBAAkB,CAACJ,EAAD,EAAa;AACnC,cAAI,CAAC,KAAK5B,YAAN,IAAsB,KAAKmE,MAA/B,EAAuC,OADJ,CAGnC;;AACA,eAAK3C,IAAL,CAAUC,WAAV,CAAsB,KAAKX,aAA3B,EAJmC,CAMnC;;AACA,gBAAMqG,EAAE,GAAG,KAAKtG,eAAL,CAAqB4F,CAArB,GAAyB,KAAK3F,aAAL,CAAmB2F,CAAvD;AACA,gBAAMW,EAAE,GAAG,KAAKvG,eAAL,CAAqBwG,CAArB,GAAyB,KAAKvG,aAAL,CAAmBuG,CAAvD;AACA,gBAAMC,QAAQ,GAAGP,IAAI,CAACQ,IAAL,CAAUJ,EAAE,GAAGA,EAAL,GAAUC,EAAE,GAAGA,EAAzB,CAAjB,CATmC,CAWnC;;AACA,gBAAMI,QAAQ,GAAG,KAAK/G,cAAL,GAAsBmB,EAAvC;AACA,gBAAM6F,QAAQ,GAAG,KAAK/G,cAAL,GAAsBkB,EAAvC,CAbmC,CAenC;;AACA,cAAI8F,IAAI,GAAG,KAAK5G,aAAL,CAAmB2F,CAA9B;AACA,cAAIkB,IAAI,GAAG,KAAK7G,aAAL,CAAmBuG,CAA9B,CAjBmC,CAmBnC;;AACA,cAAIN,IAAI,CAACa,GAAL,CAAST,EAAT,IAAeK,QAAnB,EAA6B;AACzBE,YAAAA,IAAI,IAAIX,IAAI,CAACc,IAAL,CAAUV,EAAV,IAAgBK,QAAxB;AACH,WAFD,MAEO;AACHE,YAAAA,IAAI,GAAG,KAAK7G,eAAL,CAAqB4F,CAA5B;AACH,WAxBkC,CA0BnC;;;AACA,cAAIM,IAAI,CAACa,GAAL,CAASR,EAAT,IAAeK,QAAnB,EAA6B;AACzBE,YAAAA,IAAI,IAAIZ,IAAI,CAACc,IAAL,CAAUT,EAAV,IAAgBK,QAAxB;AACH,WAFD,MAEO;AACHE,YAAAA,IAAI,GAAG,KAAK9G,eAAL,CAAqBwG,CAA5B;AACH,WA/BkC,CAiCnC;;;AACA,eAAK7F,IAAL,CAAUkD,WAAV,CAAsBgD,IAAtB,EAA4BC,IAA5B;AACH;;AAGDG,QAAAA,MAAM,CAACC,UAAU,GAAG,KAAd,EAAqB;AACvB,eAAK/D,aAAL,CAAmB,IAAnB;AACA,eAAKC,WAAL,CAAiB,IAAjB;;AACA,cAAI8D,UAAJ,EAAgB;AACZ,iBAAKC,kBAAL,CAAwB,CAAxB;AACH,WAFD,MAEO;AACH,iBAAKjG,eAAL;AACH;AACJ;;AAEDkG,QAAAA,MAAM,GAAG;AACL,eAAKzG,IAAL,CAAU0G,MAAV,GAAmB,IAAnB;AACA,eAAKJ,MAAL,CAAY,IAAZ;AACH,SA7PoC,CA+PrC;;;AACAK,QAAAA,YAAY,GAAG;AACX,cAAI,KAAKjI,YAAL,GAAoB,KAAKC,gBAA7B,EAA+C;AAC3C,iBAAKD,YAAL,GAAoB,CAApB,CAD2C,CAE3C;;AACA;AAAA;AAAA,4CAAYyE,QAAZ,CAAqByD,aAArB;AACH;AACJ;;AAEDC,QAAAA,KAAK,GAAY;AACb,cAAI,CAAC,MAAMA,KAAN,EAAL,EAAoB;AAChB,mBAAO,KAAP;AACH;;AACD,eAAK7G,IAAL,CAAU0G,MAAV,GAAmB,KAAnB;AACA,eAAKxF,UAAL;AACA;AAAA;AAAA,kCAAQb,aAAR,CAAsByG,UAAtB,CAAiC,KAAjC;AACA,iBAAO,IAAP;AACH;;AAEiB,YAAdC,cAAc,GAAG;AAAA;;AACjB,iBAAO,iBAAKnI,UAAL,CAAiBoI,MAAjB,6BAAyBC,YAAzB,KAAyC,CAAhD;AACH;;AACgB,YAAbC,aAAa,GAAG;AAAA;;AAChB,iBAAO,kBAAKtI,UAAL,CAAiBoI,MAAjB,8BAAyBG,aAAzB,KAA0C,CAAjD;AACH;;AAEY,YAATtG,SAAS,GAAkB;AAC3B,iBAAO,KAAKjC,UAAZ;AACH;;AAEDwI,QAAAA,SAAS,GAAW;AAChB,iBAAO,KAAKxI,UAAL,CAAiBwI,SAAjB,EAAP;AACH;;AAED3E,QAAAA,WAAW,CAAC4E,MAAD,EAAkB;AACzB,eAAK7I,YAAL,GAAoB6I,MAApB;AACH;;AAED7E,QAAAA,aAAa,CAAC6E,MAAD,EAAkB;AAC3B,eAAKvI,WAAL,GAAmBuI,MAAnB;;AACA,cAAI,KAAK5I,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAkBkF,WAAlB,CAA8B0D,MAA9B;AACH;AACJ,SA1SoC,CA2SrC;;;AACAb,QAAAA,kBAAkB,CAACc,IAAD,EAAe;AAC7B,eAAKvI,eAAL,GAAuBuI,IAAvB;AACA,eAAK5E,eAAL,GAAuB,KAAvB;AACA,eAAK6E,cAAL,CAAqBb,MAArB,GAA8B,IAA9B;AACH,SAhToC,CAiTrC;;;AACAnG,QAAAA,eAAe,GAAG;AACd,eAAKmC,eAAL,GAAuB,IAAvB;AACA,eAAK6E,cAAL,CAAqBb,MAArB,GAA8B,KAA9B;AACH;;AAEDc,QAAAA,YAAY,CAACC,KAAD,EAAgB;AACxB,cAAI,KAAK5I,MAAT,EAAiB;AACb,iBAAKA,MAAL,CAAY2I,YAAZ,CAAyBC,KAAzB;AACH;AACJ;;AACiB,YAAdC,cAAc,GAAW;AACzB,iBAAO,KAAK1I,QAAL,CAAc2I,WAArB;AACH;;AACe,YAAZC,YAAY,GAAW;AACvB,iBAAO,KAAK5I,QAAL,CAAc6I,SAArB;AACH;;AACiB,YAAdC,cAAc,GAAW;AACzB,iBAAO,KAAK9I,QAAL,CAAc+I,WAArB;AACH;;AACe,YAAZC,YAAY,GAAW;AACvB,iBAAO,KAAKhJ,QAAL,CAAciJ,SAArB;AACH;AAED;AACJ;AACA;;;AACYlI,QAAAA,qBAAqB,GAAG;AAC5B,gBAAMmI,WAAW,GAAGhL,IAAI,CAAC8F,cAAL,EAApB;AACA,eAAK7D,YAAL,GAAoB+I,WAAW,CAACC,KAAZ,GAAoB;AAAA;AAAA,sCAAUC,WAAlD,CAF4B,CAI5B;;AACA,eAAKhJ,YAAL,GAAoB;AAAA;AAAA,sCAAUiJ,eAAV,GAA4B,KAAKlJ,YAArD,CAL4B,CAM5B;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACY2F,QAAAA,oBAAoB,CAACwD,MAAD,EAAiB1D,MAAjB,EAAuC;AAC/D;AACA,gBAAMsD,WAAW,GAAGhL,IAAI,CAAC8F,cAAL,EAApB,CAF+D,CAI/D;;AACA,gBAAMuF,MAAM,GAAGL,WAAW,CAACC,KAAZ,GAAoB;AAAA;AAAA,sCAAUC,WAA7C;AACA,gBAAMI,MAAM,GAAGN,WAAW,CAACjF,MAAZ,GAAqB;AAAA;AAAA,sCAAUwF,YAA9C,CAN+D,CAQ/D;;AACA,gBAAMC,OAAO,GAAGJ,MAAM,GAAGC,MAAzB;AACA,gBAAMI,OAAO,GAAG/D,MAAM,GAAG4D,MAAzB,CAV+D,CAY/D;AACA;;AACA,gBAAMI,MAAM,GAAGF,OAAO,GAAGR,WAAW,CAACC,KAAZ,GAAoB,CAA7C;AACA,gBAAMU,MAAM,GAAGF,OAAO,GAAGT,WAAW,CAACjF,MAAZ,GAAqB,CAA9C,CAf+D,CAiB/D;AACA;;AAEA,iBAAO,IAAIhG,IAAJ,CAAS2L,MAAT,EAAiBC,MAAjB,EAAyB,CAAzB,CAAP;AACH;;AAhXoC,O;;;;;iBAGV,I;;;;;;;iBAEA,I;;;;;;;iBAEG,I", "sourcesContent": ["import { _decorator, instantiate, Node, Prefab, size, UIOpacity, Vec3, view } from \"cc\";\r\nconst { ccclass, property } = _decorator;\r\n\r\nimport { MyApp } from \"db://assets/bundles/common/script/app/MyApp\";\r\n\r\nimport { AttributeConst } from \"db://assets/bundles/common/script/const/AttributeConst\";\r\nimport { AttributeData } from \"db://assets/bundles/common/script/data/base/AttributeData\";\r\nimport { GameConst } from \"../../../../../../../scripts/core/base/GameConst\";\r\n\r\nimport { MainPlaneData } from \"db://assets/bundles/common/script/data/plane/MainPlaneData\";\r\nimport { Plane } from \"db://assets/bundles/common/script/ui/Plane\";\r\n\r\nimport { EDITOR } from \"cc/env\";\r\nimport PlaneBase from \"db://assets/bundles/common/script/game/ui/plane/PlaneBase\";\r\nimport { Bullet } from \"../../../bullet/Bullet\";\r\nimport { Emitter } from \"../../../bullet/Emitter\";\r\nimport FBoxCollider from \"../../../collider-system/FBoxCollider\";\r\nimport FCollider, { ColliderGroupType } from \"../../../collider-system/FCollider\";\r\nimport GameResourceList from \"../../../const/GameResourceList\";\r\nimport { GameIns } from \"../../../GameIns\";\r\nimport { eEntityTag } from \"../../base/Entity\";\r\nimport EffectLayer from \"../../layer/EffectLayer\";\r\nimport EnemyPlaneBase from \"../enemy/EnemyPlaneBase\";\r\nimport MainPlaneDebug from \"./MainPlaneDebug\";\r\nimport MainPlaneStat from \"./MainPlaneStat\";\r\nimport { GameFightUI } from \"../../layer/GameFightUI\";\r\n\r\n@ccclass(\"MainPlane\")\r\nexport class MainPlane extends PlaneBase {\r\n\r\n    @property(Node)\r\n    planeParent: Node | null = null;\r\n    @property(Node)\r\n    NodeEmitter: Node | null = null;\r\n    @property(Node)\r\n    InvincibleNode: Node | null = null;\r\n\r\n    m_moveEnable = true; // 是否允许移动\r\n    emitterComp: Emitter | null = null; // 发射器\r\n\r\n    _hurtActTime = 0; // 受伤动画时间\r\n    _hurtActDuration = 0.5; // 受伤动画持续时间\r\n\r\n    _planeData: MainPlaneData | null = null;//飞机数据\r\n    _plane: Plane | null = null;//飞机显示节点\r\n    _fireEnable = true;//是否允许射击\r\n    _unColliderTime: number = 0;//无敌时间\r\n    statData: MainPlaneStat = new MainPlaneStat();\r\n\r\n    private _maxMoveSpeedX: number = 2400; // 水平方向最大移动速度\r\n    private _maxMoveSpeedY: number = 4000; // 垂直方向最大移动速度\r\n    private _screenRatio: number = 1;\r\n    private _battleWidth: number = 0; // 实际战斗宽度\r\n    private _targetPosition: Vec3 = new Vec3(); // 目标位置\r\n    private _lastPosition: Vec3 = new Vec3(); // 上一帧位置\r\n\r\n    private hpRecoveryTime = 0;\r\n    private _nuclearNum = 0;\r\n    get nuclearNum() {\r\n        return this._nuclearNum;\r\n    }\r\n    addNuclear(num: number) {\r\n        this._nuclearNum += num;\r\n    }\r\n    addScore(num: number) {\r\n        this.statData.score += num;\r\n    }\r\n\r\n    onLoad() {\r\n        // 计算屏幕适配比例\r\n        this._calculateScreenRatio();\r\n\r\n        // 初始化位置\r\n        this.node.getPosition(this._lastPosition);\r\n        this._targetPosition.set(this._lastPosition);\r\n    }\r\n\r\n    // 纯表现层业务，请勿将逻辑代码写到这里\r\n    update(dt: number) {\r\n        dt = dt * GameIns.battleManager.animSpeed;\r\n        this._hurtActTime += dt;\r\n\r\n        if (this._unColliderTime > 0) {\r\n            this._unColliderTime -= dt;\r\n            if (this._unColliderTime <= 0) {\r\n                this.cancelUncollide();\r\n            }\r\n        }\r\n\r\n        this.smoothMoveToTarget(dt);\r\n    }\r\n\r\n    updateGameLogic(dt: number): void {\r\n        super.updateGameLogic(dt)\r\n        while (this.hpRecoveryTime <= GameIns.gameDataManager.gameTime) {\r\n            this.hpRecoveryTime += 1;\r\n            let hpRecovery = this.attribute.getHPRecovery()\r\n            this.addHp(hpRecovery);\r\n        }\r\n    }\r\n\r\n    async initPlane(planeData: MainPlaneData) {\r\n        this._planeData = planeData;\r\n        this.resetPlane();\r\n\r\n        this._nuclearNum = planeData.getFinalAttributeByKey(AttributeConst.NuclearMax);\r\n        //加载飞机显示\r\n        const prefab = await MyApp.resMgr.loadAsync(planeData.recoursePrefab, Prefab);\r\n        let plane = MyApp.planeMgr.getPlane(planeData, prefab);\r\n        this._plane = plane.getComponent(Plane);\r\n        this.planeParent!.addChild(plane);\r\n        \r\n        this.collideComp = this.getComponentInChildren(FBoxCollider)\r\n        this.collideComp!.init(this, size(128, 128)); // 初始化碰撞组件\r\n        this.collideComp!.groupType = ColliderGroupType.PLAYER;\r\n\r\n        this.addTag(eEntityTag.Player);\r\n\r\n        // 临时: 设置飞机发射组件\r\n        // this.setEmitter();\r\n\r\n        super.init();\r\n\r\n        if (EDITOR) {\r\n            this.node.addComponent(MainPlaneDebug)\r\n        }\r\n    }\r\n\r\n    resetPlane() {\r\n        this._plane?.reset();\r\n        // 禁用射击\r\n        this.setFireEnable(false);\r\n        this.setMoveAble(false);\r\n        this.colliderEnabled = false;\r\n        this.isDead = false;\r\n        this.curHp = this.maxHp;\r\n        this.updateHpUI();\r\n\r\n        this.hpRecoveryTime = 0;\r\n\r\n        const targetY = -view.getVisibleSize().height / 2 * 0.3;\r\n        this.node.setPosition(0, targetY);\r\n        this._targetPosition.set(0, targetY, 0);\r\n        this._lastPosition.set(0, targetY, 0);\r\n    }\r\n\r\n    updateHpUI(){\r\n        GameFightUI.instance.updatePlayerUI();\r\n    }\r\n\r\n    setEmitter() {\r\n        //后期根据飞机的数据，加载不同的发送组件预制体\r\n        let path = GameResourceList.EmitterPrefabPath + \"Emitter_main_01\";\r\n        MyApp.resMgr.loadAsync(path, Prefab).then((prefab) => {\r\n            let node = instantiate(prefab);\r\n            this.NodeEmitter?.addChild(node);\r\n            node.setPosition(0, 0);\r\n\r\n            this.emitterComp = node.getComponent(Emitter);\r\n            this.emitterComp!.setEntity(this);\r\n            this.emitterComp!.setIsActive(this._fireEnable);\r\n            this.emitterComp!.emitterId = 1000001;\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 主飞机入场动画\r\n     */\r\n    planeIn(): void {\r\n        this.node.getComponent(UIOpacity)!.opacity = 0;\r\n        this.scheduleOnce(() => {\r\n            this.node.getComponent(UIOpacity)!.opacity = 255;\r\n            this._plane?.onEnter(() => {\r\n                GameIns.battleManager.onPlaneIn();\r\n            });\r\n        }, 0.7);\r\n    }\r\n\r\n\r\n    /**\r\n     * 碰撞处理\r\n     * @param {Object} collision 碰撞对象\r\n     */\r\n    onCollide(collision: FCollider) {\r\n        let damage = 0;\r\n        if (collision.entity instanceof Bullet) {\r\n            if (this.attribute.getFinalAttributeByKey(AttributeConst.StatusImmuneBulletHurt) == 0) {\r\n                damage = collision.entity.calcDamage(this);\r\n            }\r\n            if (damage > 0) {\r\n                this.hurt(damage)\r\n            }\r\n        } else if (collision.entity instanceof EnemyPlaneBase) {\r\n            this.collisionPlane(collision.entity);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 控制飞机移动\r\n     * @param {number} localX\r\n     * @param {number} touchY\r\n     */\r\n    onControl(localX: number, touchY: number) {\r\n        if (!this.isDead && this.m_moveEnable) {\r\n            // 将设计分辨率坐标转换为世界坐标\r\n            const worldPos = this._convertTouchToWorld(localX, touchY);\r\n\r\n            let isLeft = localX < this.node.position.x;\r\n            this._plane?.onMoveCommand(isLeft);\r\n\r\n            // 获取战斗边界\r\n            const halfWidth = this._battleWidth / 2;\r\n            const halfHeight = GameConst.ViewHeight / 2;\r\n\r\n            // 限制飞机在战斗区域内\r\n            let posX = Math.min(halfWidth, Math.max(-halfWidth, localX));\r\n            let posY = Math.min(halfHeight, Math.max(-halfHeight, touchY));\r\n\r\n            this._targetPosition.set(posX, posY, 0);\r\n            //this.node.setPosition(posX, posY);\r\n\r\n            // 调试日志\r\n            //logInfo('MainPlane', ` 接收位置: (${localX},${touchY}) 限制后: (${posX},${posY}) 边界: X:±${halfWidth}, Y:±${halfHeight} `);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 平滑移动到目标位置\r\n     * @param dt 帧时间（秒）\r\n     */\r\n    private smoothMoveToTarget(dt: number) {\r\n        if (!this.m_moveEnable || this.isDead) return;\r\n\r\n        // 获取当前位置\r\n        this.node.getPosition(this._lastPosition);\r\n\r\n        // 计算当前位置到目标位置的距离\r\n        const dx = this._targetPosition.x - this._lastPosition.x;\r\n        const dy = this._targetPosition.y - this._lastPosition.y;\r\n        const distance = Math.sqrt(dx * dx + dy * dy);\r\n\r\n        // 计算最大允许移动距离（分别应用X和Y方向的速度限制）\r\n        const maxMoveX = this._maxMoveSpeedX * dt;\r\n        const maxMoveY = this._maxMoveSpeedY * dt;\r\n\r\n        // 应用移动速度限制（分别处理X和Y方向）\r\n        let newX = this._lastPosition.x;\r\n        let newY = this._lastPosition.y;\r\n\r\n        // X方向移动限制\r\n        if (Math.abs(dx) > maxMoveX) {\r\n            newX += Math.sign(dx) * maxMoveX;\r\n        } else {\r\n            newX = this._targetPosition.x;\r\n        }\r\n\r\n        // Y方向移动限制\r\n        if (Math.abs(dy) > maxMoveY) {\r\n            newY += Math.sign(dy) * maxMoveY;\r\n        } else {\r\n            newY = this._targetPosition.y;\r\n        }\r\n\r\n        // 设置新位置\r\n        this.node.setPosition(newX, newY);\r\n    }\r\n\r\n\r\n    begine(isContinue = false) {\r\n        this.setFireEnable(true);\r\n        this.setMoveAble(true);\r\n        if (isContinue) {\r\n            this.setUncollideByTime(2);\r\n        } else {\r\n            this.cancelUncollide();\r\n        }\r\n    }\r\n\r\n    revive() {\r\n        this.node.active = true;\r\n        this.begine(true);\r\n    }\r\n\r\n    //实现父类的方法\r\n    playHurtAnim() {\r\n        if (this._hurtActTime > this._hurtActDuration) {\r\n            this._hurtActTime = 0;\r\n            // 显示红屏效果\r\n            EffectLayer.instance.showRedScreen();\r\n        }\r\n    }\r\n\r\n    toDie(): boolean {\r\n        if (!super.toDie()) {\r\n            return false;\r\n        }\r\n        this.node.active = false;\r\n        this.resetPlane();\r\n        GameIns.battleManager.setGameEnd(false);\r\n        return true;\r\n    }\r\n\r\n    get collisionLevel() {\r\n        return this._planeData!.config?.collideLevel || 0;\r\n    }\r\n    get collisionHurt() {\r\n        return this._planeData!.config?.collideDamage || 0;\r\n    }\r\n\r\n    get attribute(): AttributeData {\r\n        return this._planeData!;\r\n    }\r\n\r\n    getAttack(): number {\r\n        return this._planeData!.getAttack();\r\n    }\r\n\r\n    setMoveAble(enable: boolean) {\r\n        this.m_moveEnable = enable;\r\n    }\r\n\r\n    setFireEnable(enable: boolean) {\r\n        this._fireEnable = enable;\r\n        if (this.emitterComp) {\r\n            this.emitterComp!.setIsActive(enable);\r\n        }\r\n    }\r\n    //设置无敌状态\r\n    setUncollideByTime(time: number) {\r\n        this._unColliderTime = time;\r\n        this.colliderEnabled = false;\r\n        this.InvincibleNode!.active = true;\r\n    }\r\n    //取消无敌状态\r\n    cancelUncollide() {\r\n        this.colliderEnabled = true;\r\n        this.InvincibleNode!.active = false;\r\n    }\r\n\r\n    setAnimSpeed(speed: number) {\r\n        if (this._plane) {\r\n            this._plane.setAnimSpeed(speed);\r\n        }\r\n    }\r\n    get pickDiamondNum(): number {\r\n        return this.statData.pickDiamond;\r\n    }\r\n    get killEnemyNum(): number {\r\n        return this.statData.killEnemy;\r\n    }\r\n    get usedNuclearNum(): number {\r\n        return this.statData.usedNuclear;\r\n    }\r\n    get usedSuperNum(): number {\r\n        return this.statData.usedSuper;\r\n    }\r\n\r\n    /**\r\n     * 计算屏幕适配比例\r\n     */\r\n    private _calculateScreenRatio() {\r\n        const visibleSize = view.getVisibleSize();\r\n        this._screenRatio = visibleSize.width / GameConst.designWidth;\r\n\r\n        // 计算实际战斗宽度（考虑宽屏适配）\r\n        this._battleWidth = GameConst.ViewBattleWidth * this._screenRatio;\r\n        //logInfo('CameraMove', `屏幕比例: ${this._screenRatio}, 战斗宽度: ${this._battleWidth}`);\r\n    }\r\n\r\n    /**\r\n     * 将触摸坐标转换为世界坐标\r\n     * @param touchX 触摸点X坐标（0-750）\r\n     * @param touchY 触摸点Y坐标（0-1334）\r\n     * @returns 世界坐标\r\n     */\r\n    private _convertTouchToWorld(touchX: number, touchY: number): Vec3 {\r\n        // 获取实际屏幕尺寸\r\n        const visibleSize = view.getVisibleSize();\r\n\r\n        // 计算设计分辨率到实际屏幕的比例\r\n        const scaleX = visibleSize.width / GameConst.designWidth;\r\n        const scaleY = visibleSize.height / GameConst.designHeight;\r\n\r\n        // 将设计坐标转换为屏幕坐标\r\n        const screenX = touchX * scaleX;\r\n        const screenY = touchY * scaleY;\r\n\r\n        // 将屏幕坐标转换为世界坐标\r\n        // 世界坐标原点在屏幕中心，X范围：[-battleWidth/2, battleWidth/2]\r\n        const worldX = screenX - visibleSize.width / 2;\r\n        const worldY = screenY - visibleSize.height / 2;\r\n\r\n        // 添加调试日志\r\n        //logInfo('MainPlane', `转换: 设计(${touchX},${touchY}) -> 屏幕(${screenX},${screenY}) -> 世界(${worldX},${worldY})`);\r\n\r\n        return new Vec3(worldX, worldY, 0);\r\n    }\r\n}"]}
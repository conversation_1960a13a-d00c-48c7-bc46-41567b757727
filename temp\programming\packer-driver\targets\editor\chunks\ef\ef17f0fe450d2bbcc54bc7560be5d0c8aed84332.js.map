{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/GameMapManager.ts"], "names": ["MapObjectPoolManager", "_decorator", "SingletonBase", "GameMapRun", "MyApp", "logInfo", "log<PERSON>arn", "GameIns", "GameEnum", "ccclass", "eRAND_STRATEGY", "_pools", "Map", "get", "poolName", "prefabName", "typePool", "pool", "length", "node", "pop", "active", "put", "name", "removeFromParent", "has", "set", "push", "clearPool", "for<PERSON>ach", "destroy", "delete", "clearAll", "clear", "GameMapManager", "chapterConfig", "_chapterConfig", "levelList", "_levelList", "mapObjectPoolManager", "_objectPoolManager", "constructor", "_chapterID", "start", "preLoad", "chapterID", "_initLevelList", "instance", "initData", "switchSectionState", "state", "updateGameLogic", "dt", "reset", "reInitLevelList", "eSECTION_STATE", "RESET", "lubanTables", "TbResChapter", "levelGroupList", "_randomSelection", "strategyList", "levelGroupCount", "strategy", "levelGroupID", "levelGroupData", "TbResLevelGroup", "normSTList", "normLevelCount", "normLevelST", "bossSTList", "bossLevelCount", "bossLevelST", "STList", "count", "results", "WEIGHT_PRUE", "totalWeight", "reduce", "sum", "item", "Weight", "i", "randomIndex", "Math", "floor", "battleManager", "random", "ID", "randomValue", "cumulativeWeight", "WEIGHT_NO_REPEAT", "tempList", "lastSelectedId", "lastSelectedIndex", "findIndex", "selectedIndex", "j", "selectedId", "ORDER", "currentIndex"], "mappings": ";;;kKAiBMA,oB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAjBGC,MAAAA,U,OAAAA,U;;AACAC,MAAAA,a,iBAAAA,a;;AACFC,MAAAA,U;;AAEEC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,O,iBAAAA,O;;AACTC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;;;;;;;;;OAEH;AAAEC,QAAAA;AAAF,O,GAAcR,U;;AAEfS,MAAAA,c,0BAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;eAAAA,c;QAAAA,c;;AAMCV,MAAAA,oB,GAAN,MAAMA,oBAAN,CAA2B;AAAA;AAAA,eACfW,MADe,GAC4B,IAAIC,GAAJ,EAD5B;AAAA;;AAGvB;AACJ;AACA;AACA;AACA;AACWC,QAAAA,GAAG,CAACC,QAAD,EAAmBC,UAAnB,EAAoD;AAC1D,gBAAMC,QAAQ,GAAG,KAAKL,MAAL,CAAYE,GAAZ,CAAgBC,QAAhB,CAAjB;;AACA,cAAI,CAACE,QAAL,EAAe,OAAO,IAAP;AAEf,gBAAMC,IAAI,GAAGD,QAAQ,CAACH,GAAT,CAAaE,UAAb,CAAb;;AACA,cAAIE,IAAI,IAAIA,IAAI,CAACC,MAAL,GAAc,CAA1B,EAA6B;AACzB,kBAAMC,IAAI,GAAGF,IAAI,CAACG,GAAL,EAAb;AACAD,YAAAA,IAAI,CAACE,MAAL,GAAc,IAAd;AACA,mBAAOF,IAAP;AACH;;AACD,iBAAO,IAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACWG,QAAAA,GAAG,CAACR,QAAD,EAAmBK,IAAnB,EAAqC;AAC3C,cAAI,CAACA,IAAL,EAAW;AAEX,gBAAMJ,UAAU,GAAGI,IAAI,CAACI,IAAxB;AAEAJ,UAAAA,IAAI,CAACE,MAAL,GAAc,KAAd;AACAF,UAAAA,IAAI,CAACK,gBAAL;;AAEA,cAAI,CAAC,KAAKb,MAAL,CAAYc,GAAZ,CAAgBX,QAAhB,CAAL,EAAgC;AAC5B,iBAAKH,MAAL,CAAYe,GAAZ,CAAgBZ,QAAhB,EAA0B,IAAIF,GAAJ,EAA1B;AACH;;AAED,gBAAMI,QAAQ,GAAG,KAAKL,MAAL,CAAYE,GAAZ,CAAgBC,QAAhB,CAAjB;;AAEA,cAAI,CAACE,QAAQ,CAACS,GAAT,CAAaV,UAAb,CAAL,EAA+B;AAC3BC,YAAAA,QAAQ,CAACU,GAAT,CAAaX,UAAb,EAAyB,EAAzB;AACH;;AAEDC,UAAAA,QAAQ,CAACH,GAAT,CAAaE,UAAb,EAA0BY,IAA1B,CAA+BR,IAA/B;AACH,SA7CsB,CA+CvB;;;AACOS,QAAAA,SAAS,CAACd,QAAD,EAAyB;AACrC,cAAI,KAAKH,MAAL,CAAYc,GAAZ,CAAgBX,QAAhB,CAAJ,EAA+B;AAC3B,kBAAME,QAAQ,GAAG,KAAKL,MAAL,CAAYE,GAAZ,CAAgBC,QAAhB,CAAjB;;AACAE,YAAAA,QAAQ,CAACa,OAAT,CAAiBZ,IAAI,IAAI;AACrBA,cAAAA,IAAI,CAACY,OAAL,CAAaV,IAAI,IAAIA,IAAI,CAACW,OAAL,EAArB;AACH,aAFD;;AAGA,iBAAKnB,MAAL,CAAYoB,MAAZ,CAAmBjB,QAAnB;AACH;AACJ,SAxDsB,CA0DvB;;;AACOkB,QAAAA,QAAQ,GAAS;AACpB,eAAKrB,MAAL,CAAYkB,OAAZ,CAAoB,CAACb,QAAD,EAAWF,QAAX,KAAwB;AACxCE,YAAAA,QAAQ,CAACa,OAAT,CAAiBZ,IAAI,IAAI;AACrBA,cAAAA,IAAI,CAACY,OAAL,CAAaV,IAAI,IAAIA,IAAI,CAACW,OAAL,EAArB;AACH,aAFD;AAGH,WAJD;;AAKA,eAAKnB,MAAL,CAAYsB,KAAZ;AACH;;AAlEsB,O;;gCAsEdC,c,WADZzB,OAAO,CAAC,gBAAD,C,iBAAR,MACayB,cADb;AAAA;AAAA,0CACkE;AAItC,YAAbC,aAAa,GAAsB;AAAE,iBAAO,KAAKC,cAAZ;AAA6B;;AAC1C;AACf,YAATC,SAAS,GAAa;AAAE,iBAAO,KAAKC,UAAZ;AAAyB;;AAI7B,YAApBC,oBAAoB,GAAyB;AACpD,iBAAO,KAAKC,kBAAZ;AACH;;AAEDC,QAAAA,WAAW,GAAG;AACV;AADU,eAZdC,UAYc,GAZO,CAYP;AAAA,eAXdN,cAWc,GAXsB,IAWtB;AAAA,eATNE,UASM,GATiB,EASjB;AAAA,eANNE,kBAMM,GANqC,IAAIxC,oBAAJ,EAMrC;AAEb;;AAED2C,QAAAA,KAAK,GAAG,CAEP;;AAEY,cAAPC,OAAO,CAACC,SAAD,EAAoC;AAC7C,eAAKH,UAAL,GAAkBG,SAAlB;;AACA,eAAKC,cAAL;;AACA,gBAAM;AAAA;AAAA,wCAAWC,QAAX,CAAqBC,QAArB,CAA8B,KAAKV,UAAnC,CAAN;AACH;;AAEDW,QAAAA,kBAAkB,CAACC,KAAD,EAAiC;AAC/C;AAAA;AAAA,wCAAWH,QAAX,CAAqBE,kBAArB,CAAwCC,KAAxC;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,eAAe,CAACC,EAAD,EAAa;AACxB;AAAA;AAAA,wCAAWL,QAAX,CAAqBI,eAArB,CAAqCC,EAArC;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJ,eAAKb,kBAAL,CAAwBR,QAAxB;;AACA;AAAA;AAAA,wCAAWe,QAAX,CAAqBM,KAArB;AACH;;AAEDpB,QAAAA,KAAK,GAAG;AACJ;AAAA;AAAA,wCAAWc,QAAX,CAAqBd,KAArB;AACH;;AAEoB,cAAfqB,eAAe,GAAG;AACpB,eAAKR,cAAL;;AACA,gBAAM;AAAA;AAAA,wCAAWC,QAAX,CAAqBC,QAArB,CAA8B,KAAKV,UAAnC,CAAN;AACA;AAAA;AAAA,wCAAWS,QAAX,CAAqBE,kBAArB,CAAwC;AAAA;AAAA,oCAASM,cAAT,CAAwBC,KAAhE;AACH,SArD6D,CAuD9D;;;AACQV,QAAAA,cAAc,GAAG;AACrB,eAAKV,cAAL,GAAsB;AAAA;AAAA,8BAAMqB,WAAN,CAAkBC,YAAlB,CAA+B7C,GAA/B,CAAmC,KAAK6B,UAAxC,CAAtB;AAA2E;;AAC3E,cAAI,KAAKN,cAAL,IAAuB,IAA3B,EAAiC;AAC7B;AAAA;AAAA,oCAAQ,gBAAR,EAA2B,qCAAoC,KAAKM,UAAW,EAA/E;AACA;AACH,WALoB,CAOrB;;;AACA,gBAAMiB,cAAc,GAAG,KAAKC,gBAAL,CAAsB,KAAKxB,cAAL,CAAoByB,YAA1C,EAAwD,KAAKzB,cAAL,CAAoB0B,eAA5E,EAA6F,KAAK1B,cAAL,CAAoB2B,QAAjH,CAAvB;;AACA,cAAIJ,cAAc,CAACzC,MAAf,KAA0B,CAA9B,EAAiC;AAC7B;AAAA;AAAA,oCAAQ,gBAAR,EAA0B,yBAA1B;AACA;AACH,WAZoB,CAcrB;;;AACA,eAAKoB,UAAL,GAAkB,EAAlB;;AACA,eAAK,MAAM0B,YAAX,IAA2BL,cAA3B,EAA2C;AACvC,kBAAMM,cAAc,GAAG;AAAA;AAAA,gCAAMR,WAAN,CAAkBS,eAAlB,CAAkCrD,GAAlC,CAAsCmD,YAAtC,CAAvB;;AACA,gBAAIC,cAAc,IAAI,IAAtB,EAA4B;AACxB;AAAA;AAAA,sCAAQ,gBAAR,EAA0B,yBAA1B;AACA;AACH;;AAED,iBAAK3B,UAAL,CAAgBX,IAAhB,CAAqB,GAAG,KAAKiC,gBAAL,CAAsBK,cAAc,CAACE,UAArC,EAAiDF,cAAc,CAACG,cAAhE,EAAgFH,cAAc,CAACI,WAA/F,CAAxB;;AACA,iBAAK/B,UAAL,CAAgBX,IAAhB,CAAqB,GAAG,KAAKiC,gBAAL,CAAsBK,cAAc,CAACK,UAArC,EAAiDL,cAAc,CAACM,cAAhE,EAAgFN,cAAc,CAACO,WAA/F,CAAxB;AACH;;AACD;AAAA;AAAA,kCAAQ,gBAAR,EAA2B,gBAAe,KAAKlC,UAAW,EAA1D;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACYsB,QAAAA,gBAAgB,CAACa,MAAD,EAAyBC,KAAzB,EAAwCX,QAAxC,EAA4E;AAChG,cAAIU,MAAM,CAACvD,MAAP,KAAkB,CAAlB,IAAuBwD,KAAK,IAAI,CAApC,EAAuC,OAAO,EAAP;AAEvC,gBAAMC,OAAiB,GAAG,EAA1B;;AACA,cAAIZ,QAAQ,KAAKrD,cAAc,CAACkE,WAAhC,EAA6C;AACzC;AACA,kBAAMC,WAAW,GAAGJ,MAAM,CAACK,MAAP,CAAc,CAACC,GAAD,EAAMC,IAAN,KAAeD,GAAG,GAAGC,IAAI,CAACC,MAAxC,EAAgD,CAAhD,CAApB,CAFyC,CAIzC;;AACA,gBAAIJ,WAAW,KAAK,CAApB,EAAuB;AACnB,mBAAK,IAAIK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGR,KAApB,EAA2BQ,CAAC,EAA5B,EAAgC;AAC5B,sBAAMC,WAAW,GAAGC,IAAI,CAACC,KAAL,CAAW;AAAA;AAAA,wCAAQC,aAAR,CAAsBC,MAAtB,KAAiCd,MAAM,CAACvD,MAAnD,CAApB;AACAyD,gBAAAA,OAAO,CAAChD,IAAR,CAAa8C,MAAM,CAACU,WAAD,CAAN,CAAoBK,EAAjC;AACH;;AACD,qBAAOb,OAAP;AACH,aAXwC,CAazC;;;AACA,iBAAK,IAAIO,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGR,KAApB,EAA2BQ,CAAC,EAA5B,EAAgC;AAC5B;AACA,oBAAMO,WAAW,GAAG;AAAA;AAAA,sCAAQH,aAAR,CAAsBC,MAAtB,KAAiCV,WAArD,CAF4B,CAI5B;;AACA,kBAAIa,gBAAgB,GAAG,CAAvB;;AACA,mBAAK,MAAMV,IAAX,IAAmBP,MAAnB,EAA2B;AACvBiB,gBAAAA,gBAAgB,IAAIV,IAAI,CAACC,MAAzB;;AACA,oBAAIQ,WAAW,GAAGC,gBAAlB,EAAoC;AAChCf,kBAAAA,OAAO,CAAChD,IAAR,CAAaqD,IAAI,CAACQ,EAAlB;AACA;AACH;AACJ;AACJ;AACJ,WA5BD,MA4BO,IAAIzB,QAAQ,KAAKrD,cAAc,CAACiF,gBAAhC,EAAkD;AACrD;AACA,kBAAMd,WAAW,GAAGJ,MAAM,CAACK,MAAP,CAAc,CAACC,GAAD,EAAMC,IAAN,KAAeD,GAAG,GAAGC,IAAI,CAACC,MAAxC,EAAgD,CAAhD,CAApB,CAFqD,CAIrD;;AACA,gBAAIJ,WAAW,KAAK,CAApB,EAAuB;AACnB,mBAAK,IAAIK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGR,KAApB,EAA2BQ,CAAC,EAA5B,EAAgC;AAC5B,oBAAIC,WAAW,GAAGC,IAAI,CAACC,KAAL,CAAW;AAAA;AAAA,wCAAQC,aAAR,CAAsBC,MAAtB,KAAiCd,MAAM,CAACvD,MAAnD,CAAlB,CAD4B,CAE5B;;AACA,oBAAIgE,CAAC,GAAG,CAAJ,IAAST,MAAM,CAACU,WAAD,CAAN,CAAoBK,EAApB,KAA2Bb,OAAO,CAACO,CAAC,GAAG,CAAL,CAA/C,EAAwD;AACpD;AACAC,kBAAAA,WAAW,GAAG,CAACA,WAAW,GAAG,CAAf,IAAoBV,MAAM,CAACvD,MAAzC;AACH;;AACDyD,gBAAAA,OAAO,CAAChD,IAAR,CAAa8C,MAAM,CAACU,WAAD,CAAN,CAAoBK,EAAjC;AACH;;AACD,qBAAOb,OAAP;AACH,aAhBoD,CAkBrD;;;AACA,kBAAMiB,QAAQ,GAAG,CAAC,GAAGnB,MAAJ,CAAjB;AAEA,gBAAIoB,cAAc,GAAG,CAAC,CAAtB;;AACA,iBAAK,IAAIX,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAIR,KAArB,EAA4BQ,CAAC,EAA7B,EAAiC;AAC7B;AACA,kBAAIW,cAAc,KAAK,CAAC,CAAxB,EAA2B;AACvB,sBAAMC,iBAAiB,GAAGF,QAAQ,CAACG,SAAT,CAAmBf,IAAI,IAAIA,IAAI,CAACQ,EAAL,KAAYK,cAAvC,CAA1B;;AACA,oBAAIC,iBAAiB,KAAK,CAAC,CAAvB,IAA4BA,iBAAiB,GAAGF,QAAQ,CAAC1E,MAAT,GAAkB,CAAtE,EAAyE;AACrE;AACA,mBAAC0E,QAAQ,CAACE,iBAAD,CAAT,EAA8BF,QAAQ,CAACE,iBAAiB,GAAG,CAArB,CAAtC,IACI,CAACF,QAAQ,CAACE,iBAAiB,GAAG,CAArB,CAAT,EAAkCF,QAAQ,CAACE,iBAAD,CAA1C,CADJ;AAEH;AACJ,eAT4B,CAW7B;;;AACA,oBAAML,WAAW,GAAG;AAAA;AAAA,sCAAQH,aAAR,CAAsBC,MAAtB,KAAiCV,WAArD,CAZ6B,CAc7B;;AACA,kBAAIa,gBAAgB,GAAG,CAAvB;AACA,kBAAIM,aAAa,GAAG,CAAC,CAArB;;AAEA,mBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGL,QAAQ,CAAC1E,MAA7B,EAAqC+E,CAAC,EAAtC,EAA0C;AACtCP,gBAAAA,gBAAgB,IAAIE,QAAQ,CAACK,CAAD,CAAR,CAAYhB,MAAhC;;AACA,oBAAIQ,WAAW,GAAGC,gBAAlB,EAAoC;AAChCM,kBAAAA,aAAa,GAAGC,CAAhB;AACA;AACH;AACJ,eAxB4B,CA0B7B;;;AACA,kBAAID,aAAa,KAAK,CAAC,CAAvB,EAA0B;AACtBA,gBAAAA,aAAa,GAAGJ,QAAQ,CAAC1E,MAAT,GAAkB,CAAlC;AACH,eA7B4B,CA+B7B;;;AACA,oBAAMgF,UAAU,GAAGN,QAAQ,CAACI,aAAD,CAAR,CAAwBR,EAA3C;AACAb,cAAAA,OAAO,CAAChD,IAAR,CAAauE,UAAb,EAjC6B,CAmC7B;;AACAL,cAAAA,cAAc,GAAGK,UAAjB;AACH;AACJ,WA5DM,MA4DA,IAAInC,QAAQ,KAAKrD,cAAc,CAACyF,KAAhC,EAAuC;AAC1C;AACA,gBAAIC,YAAY,GAAG,CAAnB;;AAEA,iBAAK,IAAIlB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGR,KAApB,EAA2BQ,CAAC,EAA5B,EAAgC;AAC5B;AACA,kBAAIT,MAAM,CAAC2B,YAAD,CAAN,CAAqBZ,EAArB,KAA4B,CAAhC,EAAmC;AAC/BY,gBAAAA,YAAY,GAAG,CAAf,CAD+B,CAG/B;;AACA,uBAAOA,YAAY,GAAG3B,MAAM,CAACvD,MAAtB,IAAgCuD,MAAM,CAAC2B,YAAD,CAAN,CAAqBZ,EAArB,KAA4B,CAAnE,EAAsE;AAClEY,kBAAAA,YAAY;AACf,iBAN8B,CAQ/B;;;AACA,oBAAIA,YAAY,IAAI3B,MAAM,CAACvD,MAA3B,EAAmC;AAC/B;AACH;AACJ,eAd2B,CAgB5B;;;AACAyD,cAAAA,OAAO,CAAChD,IAAR,CAAa8C,MAAM,CAAC2B,YAAD,CAAN,CAAqBZ,EAAlC,EAjB4B,CAmB5B;;AACAY,cAAAA,YAAY,GApBgB,CAsB5B;;AACA,kBAAIA,YAAY,IAAI3B,MAAM,CAACvD,MAA3B,EAAmC;AAC/BkF,gBAAAA,YAAY,GAAG,CAAf;AACH;AACJ;AACJ;;AAED,iBAAOzB,OAAP;AACH;;AA5N6D,O", "sourcesContent": ["import { _decorator, Node } from 'cc';\r\nimport { SingletonBase } from 'db://assets/scripts/core/base/SingletonBase';\r\nimport GameMapRun from 'db://assets/bundles/common/script/game/ui/map/GameMapRun';\r\nimport { ResChapter, randStrategy } from 'db://assets/bundles/common/script/autogen/luban/schema';\r\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\r\nimport { logInfo, logWarn } from 'db://assets/scripts/utils/Logger';\r\nimport { GameIns } from 'db://assets/bundles/common/script/game/GameIns';\r\nimport { GameEnum } from 'db://assets/bundles/common/script/game/const/GameEnum';\r\n\r\nconst { ccclass } = _decorator;\r\n\r\nenum eRAND_STRATEGY {\r\n    WEIGHT_PRUE = 1, // 纯权重随机\r\n    WEIGHT_NO_REPEAT = 2, //权重随机，不重复\r\n    ORDER = 3 // 按顺序\r\n}\r\n\r\nclass MapObjectPoolManager {\r\n    private _pools: Map<string, Map<string, Node[]>> = new Map();\r\n    \r\n    /**\r\n     * 获取特定类型的节点\r\n     * @param poolName 对象池名称（如\"Backgrounds\"）\r\n     * @param prefabName 预制体名称（如\"ForestBackground\"）\r\n     */\r\n    public get(poolName: string, prefabName: string): Node | null {\r\n        const typePool = this._pools.get(poolName);\r\n        if (!typePool) return null;\r\n        \r\n        const pool = typePool.get(prefabName);\r\n        if (pool && pool.length > 0) {\r\n            const node = pool.pop()!;\r\n            node.active = true;\r\n            return node;\r\n        }\r\n        return null;\r\n    }\r\n    \r\n    /**\r\n     * 回收节点\r\n     * @param poolName 对象池名称\r\n     * @param node 要回收的节点\r\n     */\r\n    public put(poolName: string, node: Node): void {\r\n        if (!node) return;\r\n        \r\n        const prefabName = node.name;\r\n        \r\n        node.active = false;\r\n        node.removeFromParent();\r\n        \r\n        if (!this._pools.has(poolName)) {\r\n            this._pools.set(poolName, new Map());\r\n        }\r\n        \r\n        const typePool = this._pools.get(poolName)!;\r\n        \r\n        if (!typePool.has(prefabName)) {\r\n            typePool.set(prefabName, []);\r\n        }\r\n        \r\n        typePool.get(prefabName)!.push(node);\r\n    }\r\n    \r\n    // 清空指定对象池\r\n    public clearPool(poolName: string): void {\r\n        if (this._pools.has(poolName)) {\r\n            const typePool = this._pools.get(poolName)!;\r\n            typePool.forEach(pool => {\r\n                pool.forEach(node => node.destroy());\r\n            });\r\n            this._pools.delete(poolName);\r\n        }\r\n    }\r\n    \r\n    // 清空所有对象池\r\n    public clearAll(): void {\r\n        this._pools.forEach((typePool, poolName) => {\r\n            typePool.forEach(pool => {\r\n                pool.forEach(node => node.destroy());\r\n            });\r\n        });\r\n        this._pools.clear();\r\n    }\r\n}\r\n\r\n@ccclass('GameMapManager')\r\nexport class GameMapManager extends SingletonBase<GameMapManager> {\r\n\r\n    _chapterID: number = 0;\r\n    _chapterConfig: ResChapter | null = null;\r\n    public get chapterConfig(): ResChapter | null { return this._chapterConfig; }\r\n    private _levelList: number[] = []; // 随机出来的关卡列表（这个列表长度可能会大于章节表中的关卡数量）\r\n    public get levelList(): number[] { return this._levelList; }\r\n\r\n    private _objectPoolManager: MapObjectPoolManager = new MapObjectPoolManager();\r\n\r\n    public get mapObjectPoolManager(): MapObjectPoolManager {\r\n        return this._objectPoolManager;\r\n    }\r\n\r\n    constructor() {\r\n        super();\r\n    }\r\n\r\n    start() {\r\n\r\n    }\r\n\r\n    async preLoad(chapterID: number) : Promise<void> {\r\n        this._chapterID = chapterID;\r\n        this._initLevelList();\r\n        await GameMapRun.instance!.initData(this._levelList);\r\n    }\r\n\r\n    switchSectionState(state: GameEnum.eSECTION_STATE) {\r\n        GameMapRun.instance!.switchSectionState(state);\r\n    }\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param {number} dt 每帧的时间间隔\r\n     */\r\n    updateGameLogic(dt: number) {\r\n        GameMapRun.instance!.updateGameLogic(dt);\r\n    }\r\n\r\n    reset() {\r\n        this._objectPoolManager.clearAll();\r\n        GameMapRun.instance!.reset();\r\n    }\r\n\r\n    clear() {\r\n        GameMapRun.instance!.clear();\r\n    }\r\n\r\n    async reInitLevelList() {\r\n        this._initLevelList();\r\n        await GameMapRun.instance!.initData(this._levelList);\r\n        GameMapRun.instance!.switchSectionState(GameEnum.eSECTION_STATE.RESET);\r\n    }\r\n\r\n    // 根据策略随机出关卡列表\r\n    private _initLevelList() {\r\n        this._chapterConfig = MyApp.lubanTables.TbResChapter.get(this._chapterID)!;;\r\n        if (this._chapterConfig == null) {\r\n            logWarn(\"GameMapManager\", `can not find chapter config by id ${this._chapterID}`);\r\n            return;\r\n        }\r\n\r\n        // 随机出关卡组\r\n        const levelGroupList = this._randomSelection(this._chapterConfig.strategyList, this._chapterConfig.levelGroupCount, this._chapterConfig.strategy);\r\n        if (levelGroupList.length === 0) {\r\n            logWarn('GameMapManager', \" levelGroupList is null\");\r\n            return;\r\n        }\r\n\r\n        // 随机出关卡\r\n        this._levelList = [];\r\n        for (const levelGroupID of levelGroupList) {\r\n            const levelGroupData = MyApp.lubanTables.TbResLevelGroup.get(levelGroupID);\r\n            if (levelGroupData == null) {\r\n                logWarn('GameMapManager', \" levelGroupData is null\");\r\n                continue;\r\n            }\r\n\r\n            this._levelList.push(...this._randomSelection(levelGroupData.normSTList, levelGroupData.normLevelCount, levelGroupData.normLevelST));\r\n            this._levelList.push(...this._randomSelection(levelGroupData.bossSTList, levelGroupData.bossLevelCount, levelGroupData.bossLevelST));\r\n        }\r\n        logInfo('GameMapManager', ` _levelList: ${this._levelList}`);\r\n    }\r\n\r\n    /**\r\n     * 策略：\r\n     * 1.严格按权重比例随机选择元素\r\n     * 2.严格按权重比例随机选择元素，不重复\r\n     * 3.按顺序选择元素\r\n     * @param STList 带权重的元素数组\r\n     * @param count 需要选择的元素数量\r\n     * @returns 选中元素的ID数组\r\n     */\r\n    private _randomSelection(STList: randStrategy[], count: number, strategy: eRAND_STRATEGY): number[] {\r\n        if (STList.length === 0 || count <= 0) return [];\r\n\r\n        const results: number[] = [];\r\n        if (strategy === eRAND_STRATEGY.WEIGHT_PRUE) {\r\n            // 计算总权重\r\n            const totalWeight = STList.reduce((sum, item) => sum + item.Weight, 0);\r\n\r\n            // 如果所有权重都为0，则转为均匀随机\r\n            if (totalWeight === 0) {\r\n                for (let i = 0; i < count; i++) {\r\n                    const randomIndex = Math.floor(GameIns.battleManager.random() * STList.length);\r\n                    results.push(STList[randomIndex].ID);\r\n                }\r\n                return results;\r\n            }\r\n\r\n            // 严格按权重比例随机选择\r\n            for (let i = 0; i < count; i++) {\r\n                // 生成[0, totalWeight)区间的随机数\r\n                const randomValue = GameIns.battleManager.random() * totalWeight;\r\n\r\n                // 遍历查找随机数对应的元素\r\n                let cumulativeWeight = 0;\r\n                for (const item of STList) {\r\n                    cumulativeWeight += item.Weight;\r\n                    if (randomValue < cumulativeWeight) {\r\n                        results.push(item.ID);\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n        } else if (strategy === eRAND_STRATEGY.WEIGHT_NO_REPEAT) {\r\n            // 计算总权重\r\n            const totalWeight = STList.reduce((sum, item) => sum + item.Weight, 0);\r\n\r\n            // 如果所有权重都为0，则转为均匀随机\r\n            if (totalWeight === 0) {\r\n                for (let i = 0; i < count; i++) {\r\n                    let randomIndex = Math.floor(GameIns.battleManager.random() * STList.length);\r\n                    // 避免重复选择相同的ID\r\n                    if (i > 0 && STList[randomIndex].ID === results[i - 1]) {\r\n                        // 如果与上一次选择的相同，选择下一个（循环）\r\n                        randomIndex = (randomIndex + 1) % STList.length;\r\n                    }\r\n                    results.push(STList[randomIndex].ID);\r\n                }\r\n                return results;\r\n            }\r\n\r\n            // 创建副本以避免修改原始数据\r\n            const tempList = [...STList];\r\n\r\n            let lastSelectedId = -1;\r\n            for (let i = 0; i <= count; i++) {\r\n                // 如果上一次选择的ID存在，且它在当前列表中，调整其位置\r\n                if (lastSelectedId !== -1) {\r\n                    const lastSelectedIndex = tempList.findIndex(item => item.ID === lastSelectedId);\r\n                    if (lastSelectedIndex !== -1 && lastSelectedIndex < tempList.length - 1) {\r\n                        // 将上一次选择的ID与下一个元素交换位置\r\n                        [tempList[lastSelectedIndex], tempList[lastSelectedIndex + 1]] =\r\n                            [tempList[lastSelectedIndex + 1], tempList[lastSelectedIndex]];\r\n                    }\r\n                }\r\n\r\n                // 生成[0, totalWeight)区间的随机数\r\n                const randomValue = GameIns.battleManager.random() * totalWeight;\r\n\r\n                // 遍历查找随机数对应的元素\r\n                let cumulativeWeight = 0;\r\n                let selectedIndex = -1;\r\n\r\n                for (let j = 0; j < tempList.length; j++) {\r\n                    cumulativeWeight += tempList[j].Weight;\r\n                    if (randomValue < cumulativeWeight) {\r\n                        selectedIndex = j;\r\n                        break;\r\n                    }\r\n                }\r\n\r\n                // 如果未找到有效索引，选择最后一个元素\r\n                if (selectedIndex === -1) {\r\n                    selectedIndex = tempList.length - 1;\r\n                }\r\n\r\n                // 获取选中的ID\r\n                const selectedId = tempList[selectedIndex].ID;\r\n                results.push(selectedId);\r\n\r\n                // 更新上一次选择的ID\r\n                lastSelectedId = selectedId;\r\n            }\r\n        } else if (strategy === eRAND_STRATEGY.ORDER) {\r\n            // 按顺序选择元素，遇到ID为0时从数组开头重新开始\r\n            let currentIndex = 0;\r\n\r\n            for (let i = 0; i < count; i++) {\r\n                // 如果当前元素的ID为0，则重置到数组开头\r\n                if (STList[currentIndex].ID === 0) {\r\n                    currentIndex = 0;\r\n\r\n                    // 如果数组开头的元素ID也为0，则跳过所有ID为0的元素\r\n                    while (currentIndex < STList.length && STList[currentIndex].ID === 0) {\r\n                        currentIndex++;\r\n                    }\r\n\r\n                    // 如果所有元素ID都为0，则无法选择，跳出循环\r\n                    if (currentIndex >= STList.length) {\r\n                        break;\r\n                    }\r\n                }\r\n\r\n                // 选择当前元素\r\n                results.push(STList[currentIndex].ID);\r\n\r\n                // 移动到下一个元素\r\n                currentIndex++;\r\n\r\n                // 如果到达数组末尾，回到开头\r\n                if (currentIndex >= STList.length) {\r\n                    currentIndex = 0;\r\n                }\r\n            }\r\n        }\r\n\r\n        return results;\r\n    }\r\n}\r\n\r\n\r\n"]}
import { _decorator, error, v2, Vec2, Prefab, Enum } from "cc";
const { ccclass, property } = _decorator;

/**
 * ActionType对应要修改的属性
 * 以下是发射器的行为
 */
export enum eEmitterAction {
    Emitter_Active = 1,         // 发射器是否启用
    Emitter_InitialDelay,       // 发射器当前的初始延迟
    Emitter_Prewarm,            // 发射器是否启用预热
    Emitter_PrewarmDuration,    // 发射器预热的持续时间
    Emitter_Duration,           // 发射器配置的持续时间
    Emitter_ElapsedTime,        // 发射器已运行的时间
    Emitter_Loop,               // 发射器是否循环
    Emitter_LoopInterval,       // 发射器循环的间隔时间

    Emitter_EmitInterval,       // 发射器开火间隔时间

    Emitter_PerEmitCount,      // 发射器单次开火子弹数量
    Emitter_PerEmitInterval,   // 发射器单次开火子弹间隔
    Emitter_PerEmitOffsetX,    // 发射器单次开火子弹偏移

    Emitter_Angle,             // 发射器弹道角度
    Emitter_Count,             // 发射器弹道数量

    Emitter_FireEffect,         // 发射器开火特效
    
    Bullet_Duration,
    Bullet_Damage,
    Bullet_Speed,
    Bullet_SpeedAngle,
    Bullet_Acceleration,
    Bullet_AccelerationAngle,
    // Bullet_Effect,              // 子弹-拖尾特效?
    Bullet_Scale,
    Bullet_ColorR,
    Bullet_ColorG,
    Bullet_ColorB,
    Bullet_Orientation,
    Bullet_OrientationParam,
    Bullet_TrackingTarget,
    Bullet_Destructive,
    Bullet_DestructiveOnHit,
    
    Unit_Life,
    Unit_LifePercent,
    Unit_PosX,
    Unit_PosY,
    Unit_Speed,
    Unit_SpeedAngle,
    Unit_Acceleration,
    Unit_AccelerationAngle,
}

/**
 * ActionType对应要修改的属性
 * 以下是子弹的行为
 */
export enum eBulletAction {
    Bullet_Duration = 100,
    Bullet_ElapsedTime,
    Bullet_PosX,
    Bullet_PosY,
    Bullet_Damage,
    Bullet_Speed,
    Bullet_SpeedAngle,
    Bullet_Acceleration,
    Bullet_AccelerationAngle,
    Bullet_Scale,
    Bullet_ColorR,
    Bullet_ColorG,
    Bullet_ColorB,
    Bullet_Orientation,
    Bullet_OrientationParam,
    Bullet_TrackingTarget,
    Bullet_Destructive,
    Bullet_DestructiveOnHit,
}

export type eEventActionType = eEmitterAction | eBulletAction;


// 以下枚举值用于编辑器显示，实际运行时不会用到
export enum eEmitterActionCn {
    发射器启用 = eEmitterAction.Emitter_Active,
    发射器初始延迟 = eEmitterAction.Emitter_InitialDelay,
    发射器预热 = eEmitterAction.Emitter_Prewarm,
    发射器预热持续时间 = eEmitterAction.Emitter_PrewarmDuration,
    发射器持续时间 = eEmitterAction.Emitter_Duration,
    发射器已运行时间 = eEmitterAction.Emitter_ElapsedTime,
    发射器循环 = eEmitterAction.Emitter_Loop,
    发射器循环间隔 = eEmitterAction.Emitter_LoopInterval,
    发射器开火间隔 = eEmitterAction.Emitter_EmitInterval,
    发射器单次开火次数 = eEmitterAction.Emitter_PerEmitCount,
    发射器单次开火间隔 = eEmitterAction.Emitter_PerEmitInterval,
    发射器单次开火偏移 = eEmitterAction.Emitter_PerEmitOffsetX,
    发射器弹道角度 = eEmitterAction.Emitter_Angle,
    发射器弹道数量 = eEmitterAction.Emitter_Count,

    子弹持续时间 = eEmitterAction.Bullet_Duration,
    子弹伤害 = eEmitterAction.Bullet_Damage,
    子弹速度 = eEmitterAction.Bullet_Speed,
    子弹速度角度 = eEmitterAction.Bullet_SpeedAngle,
    子弹加速度 = eEmitterAction.Bullet_Acceleration,
    子弹加速度角度 = eEmitterAction.Bullet_AccelerationAngle,
    子弹缩放 = eEmitterAction.Bullet_Scale,
    子弹颜色R = eEmitterAction.Bullet_ColorR,
    子弹颜色G = eEmitterAction.Bullet_ColorG,
    子弹颜色B = eEmitterAction.Bullet_ColorB,
    子弹朝向类型 = eEmitterAction.Bullet_Orientation,
    子弹朝向参数 = eEmitterAction.Bullet_OrientationParam,
    子弹追踪目标 = eEmitterAction.Bullet_TrackingTarget,
    子弹破坏性 = eEmitterAction.Bullet_Destructive,
    子弹命中时破坏 = eEmitterAction.Bullet_DestructiveOnHit,

    单位生命值 = eEmitterAction.Unit_Life,
    单位生命值百分比 = eEmitterAction.Unit_LifePercent,
    单位位置X = eEmitterAction.Unit_PosX,
    单位位置Y = eEmitterAction.Unit_PosY,
    单位速度 = eEmitterAction.Unit_Speed,
    单位速度角度 = eEmitterAction.Unit_SpeedAngle,
    单位加速度 = eEmitterAction.Unit_Acceleration,
    单位加速度角度 = eEmitterAction.Unit_AccelerationAngle,
}

export enum eBulletActionCn {
    子弹持续时间 = eBulletAction.Bullet_Duration,
    子弹已运行时间 = eBulletAction.Bullet_ElapsedTime,
    子弹位置X = eBulletAction.Bullet_PosX,
    子弹位置Y = eBulletAction.Bullet_PosY,
    子弹伤害 = eBulletAction.Bullet_Damage,
    子弹速度 = eBulletAction.Bullet_Speed,
    子弹速度角度 = eBulletAction.Bullet_SpeedAngle,
    子弹加速度 = eBulletAction.Bullet_Acceleration,
    子弹加速度角度 = eBulletAction.Bullet_AccelerationAngle,
    子弹缩放 = eBulletAction.Bullet_Scale,
    子弹颜色R = eBulletAction.Bullet_ColorR,
    子弹颜色G = eBulletAction.Bullet_ColorG,
    子弹颜色B = eBulletAction.Bullet_ColorB,
    子弹朝向类型 = eBulletAction.Bullet_Orientation,
    子弹朝向参数 = eBulletAction.Bullet_OrientationParam,
    子弹追踪目标 = eBulletAction.Bullet_TrackingTarget,
    子弹破坏性 = eBulletAction.Bullet_Destructive,
    子弹命中时破坏 = eBulletAction.Bullet_DestructiveOnHit,
}

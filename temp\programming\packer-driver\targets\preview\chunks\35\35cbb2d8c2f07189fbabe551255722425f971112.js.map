{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelLayer.ts"], "names": ["_decorator", "Component", "ccclass", "property", "<PERSON><PERSON><PERSON><PERSON>", "speed", "tick", "deltaTime"], "mappings": ";;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;;;;;;;;OACf;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBH,U;;4BAGRI,U,WADrBF,OAAO,CAAC,YAAD,C,gBAAR,MACsBE,UADtB,SACyCH,SADzC,CACmD;AAAA;AAAA;AAAA,eACxCI,KADwC,GACxB,CADwB;AAAA;;AAGxCC,QAAAA,IAAI,CAACC,SAAD,EAA0B,CACjC;AACH;;AAL8C,O", "sourcesContent": ["import { _decorator, Component, Prefab, Node, instantiate, UITransform } from 'cc';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('LevelLayer')\r\nexport abstract class LevelLayer extends Component {\r\n    public speed: number = 0;\r\n    \r\n    public tick(deltaTime: number): void {\r\n        // 重写此方法\r\n    }\r\n}"]}
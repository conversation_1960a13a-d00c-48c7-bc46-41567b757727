import { _decorator, Component, Prefab } from 'cc';

import { UIMgr } from 'db://assets/scripts/core/base/UIMgr';
import { logError, logInfo } from 'db://assets/scripts/utils/Logger';
import { MyApp } from '../../app/MyApp';
import csproto from '../../autogen/pb/cs_proto.js';
import { DevLoginUI } from '../gameui/DevLoginUI';

import { WECHAT } from 'cc/env';
import { BundleName } from 'db://assets/bundles/common/script/const/BundleConst';


const { ccclass, property } = _decorator;

@ccclass('MainUI')
export class MainUI extends Component {
    onLoad(): void {
    }

    async start() {
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_KICK_OFF, this.onKickOff, this)
        if (DevLoginUI.needLogin) {
            if (WECHAT) {
                MyApp.netMgr.connect();
            } else {
                await UIMgr.openUI(DevLoginUI)
            }
        }
    }

    protected onDestroy(): void {
        MyApp.netMgr.unregisterHandler(csproto.cs.CS_CMD.CS_CMD_KICK_OFF, this.onKickOff, this)
    }
    onKickOff(msg: csproto.cs.IS2CMsg) {
        logInfo("MainUI", "onKickOff")
        MyApp.netMgr.disableReconnect()
        if (WECHAT) {
        } else {
            UIMgr.openUI(DevLoginUI)
        }
    }

    update(deltaTime: number) {

    }
}


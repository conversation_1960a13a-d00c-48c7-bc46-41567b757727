{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/enemy/EnemyPlaneBase.ts"], "names": ["_decorator", "Node", "GameEnum", "PlaneBase", "Bullet", "Plane", "EnemyPrefab", "eMoveEvent", "eOrientationType", "PathMove", "GameIns", "MyApp", "EventGroupComp", "AttributeConst", "ccclass", "property", "EnemyPlaneBase", "_plane", "_enemyData", "_enemyPrefabData", "_moveCom", "_eventGroupComp", "removeAble", "bullets", "resConfig", "config", "enemyPrefabData", "moveCom", "eventGroupComp", "reset", "initPlane", "data", "prefab", "enemy", "plane", "planeMgr", "getPlane", "getComponent", "planeParent", "<PERSON><PERSON><PERSON><PERSON>", "getComponentInChildren", "eventGroups", "getComp", "addComp", "init", "addComponent", "removeAllListeners", "on", "onBecomeInvisible", "onBecameInvisible", "onBecomeVisible", "onBecameVisible", "refreshAttributes", "initMove", "x", "y", "angle", "setPos", "refreshMoveParam", "speedAngle", "setMovable", "initPath", "pathData", "setOffset", "set<PERSON>ath", "speed", "getFinalAttributeByKey", "MoveSpeed", "turnSpeed", "TurnSpeed", "acceleration", "accelerationAngle", "tiltSpeed", "tiltOffset", "orientationType", "orientation", "Path", "orientationParam", "setOrientation", "dieWhenOffScreen", "to<PERSON><PERSON>", "EnemyDestroyType", "Leave", "attribute", "addBaseAttribute", "MaxHPOutAdd", "baseHp", "AttackOutAdd", "baseAtk", "StatusImmuneBulletHurt", "immuneBulletDamage", "StatusImmuneCollisionHurt", "immuneCollideDamage", "StatusIgnoreBullet", "ignoreBullet", "StatusIgnoreCollision", "ignoreCollide", "StatusImmuneNuclearHurt", "immuneNuke", "StatusImmuneActiveSkillHurt", "immuneActiveSkill", "curHp", "maxHp", "destroyType", "Die", "colliderEnabled", "onDie", "will<PERSON><PERSON><PERSON>", "playDieAnim", "TrackOver", "TimeOver", "callBack", "value", "setBaseAttribute", "setOn<PERSON><PERSON>", "rotateSpeed", "Rotate", "collisionLevel", "collideLevel", "collisionHurt", "collideDamage", "invincible", "StatusInvincible", "target", "mainPlaneManager", "mainPlane", "getAttack", "onCollide", "collider", "isDead", "entity", "damage", "calcDamage", "hurtEffectManager", "createHurtNumByType", "node", "getPosition", "hurt", "attacker", "emitter", "getEntity", "penetrationRate", "BulletPenetrationRate", "battleManager", "random", "buff<PERSON><PERSON>p", "A<PERSON><PERSON><PERSON><PERSON>", "lubanTables", "TbGlobalAttr", "BulletPenetrationFlagBuffID", "collisionPlane", "addBullet", "bullet", "push", "removeBullet", "index", "indexOf", "splice", "setPosition"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;;AACZC,MAAAA,Q,iBAAAA,Q;;AACFC,MAAAA,S;;AAEEC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,K,iBAAAA,K;;AACWC,MAAAA,W,iBAAAA,W;;AACXC,MAAAA,U,iBAAAA,U;AAAYC,MAAAA,gB,iBAAAA,gB;;AACZC,MAAAA,Q,iBAAAA,Q;;AAEAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,K,kBAAAA,K;;AACAC,MAAAA,c,kBAAAA,c;;AACAC,MAAAA,c,kBAAAA,c;;;;;;;;;OAIH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBf,U;;yBAGTgB,c,WADpBF,OAAO,CAAC,gBAAD,C,UAEHC,QAAQ,CAACd,IAAD,C,2BAFb,MACqBe,cADrB;AAAA;AAAA,kCACsD;AAAA;AAAA;;AAAA;;AAAA,eAIlDC,MAJkD,GAI3B,IAJ2B;AAAA,eAKlDC,UALkD,GAKnB,IALmB;AAAA,eAMlDC,gBANkD,GAMX,IANW;AAAA,eAalDC,QAbkD,GAatB,IAbsB;AAAA,eAelDC,eAfkD,GAeT,IAfS;AAAA,eAkBlDC,UAlBkD,GAkB7B,KAlB6B;AAAA,eAmBlDC,OAnBkD,GAmB9B,EAnB8B;AAAA;;AAQ9B,YAATC,SAAS,GAAoB;AAAA;;AACpC,iBAAO,0BAAKN,UAAL,sCAAiBO,MAAjB,KAA2B,IAAlC;AACH;;AACyB,YAAfC,eAAe,GAAuB;AAAE,iBAAO,KAAKP,gBAAZ;AAA+B;;AAGhE,YAAPQ,OAAO,GAAG;AAAE,iBAAO,KAAKP,QAAZ;AAAuB;;AAErB,YAAdQ,cAAc,GAAG;AAAE,iBAAO,KAAKP,eAAZ;AAA8B;;AAK5DQ,QAAAA,KAAK,GAAG;AAAA;;AACJ;AACA,gBAAMA,KAAN,GAFI,CAGJ;;AACA,+BAAKZ,MAAL,0BAAaY,KAAb;AACA,iCAAKT,QAAL,4BAAeS,KAAf;AACH;;AAEDC,QAAAA,SAAS,CAACC,IAAD,EAAkBC,MAAlB,EAAkC;AACvC,eAAKH,KAAL;AACA,eAAKI,KAAL,GAAa,IAAb;AACA,eAAKf,UAAL,GAAkBa,IAAlB;AAEA,eAAKT,UAAL,GAAkB,KAAlB,CALuC,CAMvC;;AACA,cAAI,CAAC,KAAKL,MAAV,EAAkB;AACd,gBAAIiB,KAAK,GAAG;AAAA;AAAA,gCAAMC,QAAN,CAAeC,QAAf,CAAwBL,IAAxB,EAA8BC,MAA9B,CAAZ;AACA,iBAAKf,MAAL,GAAciB,KAAK,CAACG,YAAN;AAAA;AAAA,+BAAd;AACA,iBAAKC,WAAL,CAAkBC,QAAlB,CAA2BL,KAA3B;AACA,iBAAKf,gBAAL,GAAwBe,KAAK,CAACM,sBAAN;AAAA;AAAA,2CAAxB;;AACA,gBAAI,KAAKrB,gBAAL,IAAyB,KAAKA,gBAAL,CAAsBsB,WAAnD,EAAgE;AAC5D,mBAAKpB,eAAL,GAAuB,KAAKqB,OAAL,CAAa,YAAb,CAAvB;;AACA,kBAAI,CAAC,KAAKrB,eAAV,EAA2B;AACvB,qBAAKA,eAAL,GAAuB;AAAA;AAAA,uDAAvB;AACA,qBAAKsB,OAAL,CAAa,YAAb,EAA2B,KAAKtB,eAAhC;AACH;AACJ;AACJ;;AAED,gBAAMuB,IAAN;AAEA,eAAKxB,QAAL,GAAgB,KAAKiB,YAAL;AAAA;AAAA,uCAA+B,KAAKQ,YAAL;AAAA;AAAA,mCAA/C;;AACA,eAAKzB,QAAL,CAAe0B,kBAAf;;AACA,eAAK1B,QAAL,CAAe2B,EAAf,CAAkB;AAAA;AAAA,wCAAWC,iBAA7B,EAAgD,MAAM,KAAKC,iBAAL,EAAtD;;AACA,eAAK7B,QAAL,CAAe2B,EAAf,CAAkB;AAAA;AAAA,wCAAWG,eAA7B,EAA8C,MAAM,KAAKC,eAAL,EAApD;;AAEA,eAAKC,iBAAL;AACH;;AAEDC,QAAAA,QAAQ,CAACC,CAAD,EAAYC,CAAZ,EAAuBC,KAAvB,EAAsC;AAC1C,eAAKC,MAAL,CAAYH,CAAZ,EAAeC,CAAf,EAD0C,CAE1C;;AACA,cAAI,CAAC,KAAKnC,QAAV,EAAoB;AAChB,iBAAKA,QAAL,GAAgB,KAAKiB,YAAL;AAAA;AAAA,yCAA+B,KAAKQ,YAAL;AAAA;AAAA,qCAA/C;AACH;;AACD,eAAKa,gBAAL;AACA,eAAKtC,QAAL,CAAeuC,UAAf,GAA4BH,KAA5B;;AACA,eAAKpC,QAAL,CAAewC,UAAf,CAA0B,IAA1B;AACH;;AAEDC,QAAAA,QAAQ,CAACP,CAAD,EAAYC,CAAZ,EAAuBO,QAAvB,EAA2C;AAC/C,cAAI,CAAC,KAAK1C,QAAV,EAAoB;AAChB;AACH;;AAED,eAAKqC,MAAL,CAAYH,CAAZ,EAAeC,CAAf;AACA,eAAKG,gBAAL;;AACA,eAAKtC,QAAL,CAAe2C,SAAf,CAAyBT,CAAzB,EAA4BC,CAA5B,EAA+BS,OAA/B,CAAuCF,QAAvC,EAAiDF,UAAjD,CAA4D,IAA5D;AACH;;AAEOF,QAAAA,gBAAgB,GAAG;AAAA;;AACvB,cAAI,CAAC,KAAKtC,QAAV,EAAoB;AAChB;AACH;;AAED,eAAKA,QAAL,CAAc6C,KAAd,GAAsB,2BAAK/C,UAAL,uCAAiBgD,sBAAjB,CAAwC;AAAA;AAAA,gDAAeC,SAAvD,MAAqE,GAA3F;AACA,eAAK/C,QAAL,CAAcgD,SAAd,GAA0B,2BAAKlD,UAAL,uCAAiBgD,sBAAjB,CAAwC;AAAA;AAAA,gDAAeG,SAAvD,MAAqE,CAA/F;AACA,eAAKjD,QAAL,CAAckD,YAAd,GAA6B,CAA7B;AACA,eAAKlD,QAAL,CAAcmD,iBAAd,GAAkC,CAAlC;AACA,eAAKnD,QAAL,CAAcoD,SAAd,GAA0B,+BAAKrD,gBAAL,2CAAuBqD,SAAvB,KAAoC,CAA9D;AACA,eAAKpD,QAAL,CAAcqD,UAAd,GAA2B,gCAAKtD,gBAAL,4CAAuBsD,UAAvB,KAAqC,CAAhE;AACA,cAAIC,eAAe,GAAG,2BAAKxD,UAAL,oDAAiBO,MAAjB,uCAAyBkD,WAAzB,KAAmE;AAAA;AAAA,oDAAiBC,IAA1G;AACA,cAAIC,gBAAgB,GAAG,2BAAK3D,UAAL,oDAAiBO,MAAjB,uCAAyBoD,gBAAzB,KAA6C,CAApE;;AACA,eAAKzD,QAAL,CAAc0D,cAAd,CAA6BJ,eAA7B,EAA8CG,gBAA9C;AACH;;AAEOE,QAAAA,gBAAgB,GAAG;AACvB,eAAKC,KAAL,CAAW;AAAA;AAAA,oCAASC,gBAAT,CAA0BC,KAArC;AACH;;AAEOjC,QAAAA,iBAAiB,GAAG;AACxB;AACA,eAAK8B,gBAAL,GAFwB,CAGxB;AACH;;AAEO5B,QAAAA,eAAe,GAAG,CACtB;AACH;;AAEDC,QAAAA,iBAAiB,GAAG;AAAA;;AAChB,gBAAM3B,MAAM,wBAAG,KAAKP,UAAR,qBAAG,kBAAiBO,MAAhC;;AACA,cAAI,CAACA,MAAL,EAAa;AACT;AACH;;AACD,eAAK0D,SAAL,CAAeC,gBAAf,CAAgC;AAAA;AAAA,gDAAeC,WAA/C,EAA4D5D,MAAM,CAAC6D,MAAnE;AACA,eAAKH,SAAL,CAAeC,gBAAf,CAAgC;AAAA;AAAA,gDAAeG,YAA/C,EAA6D9D,MAAM,CAAC+D,OAApE;AACA,eAAKL,SAAL,CAAeC,gBAAf,CAAgC;AAAA;AAAA,gDAAeK,sBAA/C,EAAuEhE,MAAM,CAACiE,kBAAP,GAA0B,CAA1B,GAA4B,CAAnG;AACA,eAAKP,SAAL,CAAeC,gBAAf,CAAgC;AAAA;AAAA,gDAAeO,yBAA/C,EAA0ElE,MAAM,CAACmE,mBAAP,GAA2B,CAA3B,GAA6B,CAAvG;AACA,eAAKT,SAAL,CAAeC,gBAAf,CAAgC;AAAA;AAAA,gDAAeS,kBAA/C,EAAmEpE,MAAM,CAACqE,YAAP,GAAoB,CAApB,GAAsB,CAAzF;AACA,eAAKX,SAAL,CAAeC,gBAAf,CAAgC;AAAA;AAAA,gDAAeW,qBAA/C,EAAsEtE,MAAM,CAACuE,aAAP,GAAqB,CAArB,GAAuB,CAA7F;AACA,eAAKb,SAAL,CAAeC,gBAAf,CAAgC;AAAA;AAAA,gDAAea,uBAA/C,EAAwExE,MAAM,CAACyE,UAAP,GAAkB,CAAlB,GAAoB,CAA5F;AACA,eAAKf,SAAL,CAAeC,gBAAf,CAAgC;AAAA;AAAA,gDAAee,2BAA/C,EAA4E1E,MAAM,CAAC2E,iBAAP,GAAyB,CAAzB,GAA2B,CAAvG;AAEA,eAAKC,KAAL,GAAa,KAAKC,KAAlB;AACH;;AAEDtB,QAAAA,KAAK,CAACuB,WAAsC,GAAG;AAAA;AAAA,kCAAStB,gBAAT,CAA0BuB,GAApE,EAAmF;AACpF,cAAI,CAAC,MAAMxB,KAAN,CAAYuB,WAAZ,CAAL,EAA+B;AAC3B,mBAAO,KAAP;AACH;;AACD,eAAKE,eAAL,GAAuB,KAAvB;AAEA,eAAKC,KAAL,CAAWH,WAAX;AACA,iBAAO,IAAP;AACH;;AAEDG,QAAAA,KAAK,CAACH,WAAD,EAAsB;AACvB,eAAKI,UAAL;;AAEA,kBAAQJ,WAAR;AACI,iBAAK;AAAA;AAAA,sCAAStB,gBAAT,CAA0BuB,GAA/B;AACI,mBAAKI,WAAL,CAAiB,MAAI;AACjB,qBAAKtF,UAAL,GAAkB,IAAlB;AACH,eAFD;AAGA;;AAEJ,iBAAK;AAAA;AAAA,sCAAS2D,gBAAT,CAA0BC,KAA/B;AACA,iBAAK;AAAA;AAAA,sCAASD,gBAAT,CAA0B4B,SAA/B;AACA,iBAAK;AAAA;AAAA,sCAAS5B,gBAAT,CAA0B6B,QAA/B;AACI,mBAAKxF,UAAL,GAAkB,IAAlB;AACA;AAXR;AAaH;;AAEDsF,QAAAA,WAAW,CAACG,QAAD,EAAqB;AAC5B;AACA;AACA;AACAA,UAAAA,QAAQ,QAAR,IAAAA,QAAQ;AACX;;AACmB,YAAT5B,SAAS,GAAkB;AAClC,iBAAO,KAAKjE,UAAZ;AACH,SApKiD,CAqKlD;;;AACgB,YAAL+C,KAAK,GAAG;AACf,iBAAO,KAAKkB,SAAL,CAAejB,sBAAf,CAAsC;AAAA;AAAA,gDAAeC,SAArD,CAAP;AACH;;AACe,YAALF,KAAK,CAAC+C,KAAD,EAAgB;AAC5B,eAAK7B,SAAL,CAAe8B,gBAAf,CAAgC;AAAA;AAAA,gDAAe9C,SAA/C,EAA0D6C,KAA1D;AACA,eAAKrF,OAAL,CAAcsC,KAAd,GAAsB+C,KAAtB;AACH;;AAEmB,YAAT5C,SAAS,GAAG;AACnB,iBAAO,KAAKe,SAAL,CAAejB,sBAAf,CAAsC;AAAA;AAAA,gDAAeG,SAArD,CAAP;AACH;;AACmB,YAATD,SAAS,CAAC4C,KAAD,EAAgB;AAChC,eAAK7B,SAAL,CAAe8B,gBAAf,CAAgC;AAAA;AAAA,gDAAe5C,SAA/C,EAA0D2C,KAA1D;AACA,eAAKrF,OAAL,CAAcyC,SAAd,GAA0B4C,KAA1B;AACH;;AAEoB,YAAVrD,UAAU,GAAG;AACpB,iBAAO,KAAKhC,OAAL,CAAcgC,UAArB;AACH;;AACoB,YAAVA,UAAU,CAACqD,KAAD,EAAgB;AACjC,eAAKrF,OAAL,CAAcgC,UAAd,GAA2BqD,KAA3B,CADiC,CAEjC;;AACA,eAAKrF,OAAL,CAAcuF,SAAd,CAAwB,KAAxB;AACH;;AAEsB,YAAZ5C,YAAY,GAAG;AACtB,iBAAO,KAAK3C,OAAL,CAAc2C,YAArB;AACH;;AACsB,YAAZA,YAAY,CAAC0C,KAAD,EAAgB;AACnC,eAAKrF,OAAL,CAAc2C,YAAd,GAA6B0C,KAA7B;AACH;;AAE2B,YAAjBzC,iBAAiB,GAAG;AAC3B,iBAAO,KAAK5C,OAAL,CAAc4C,iBAArB;AACH;;AAC2B,YAAjBA,iBAAiB,CAACyC,KAAD,EAAgB;AACxC,eAAKrF,OAAL,CAAc4C,iBAAd,GAAkCyC,KAAlC,CADwC,CAExC;;AACA,eAAKrF,OAAL,CAAcuF,SAAd,CAAwB,KAAxB;AACH;;AAEmB,YAAT1C,SAAS,GAAG;AACnB,iBAAO,KAAK7C,OAAL,CAAc6C,SAArB;AACH;;AACmB,YAATA,SAAS,CAACwC,KAAD,EAAgB;AAChC,eAAKrF,OAAL,CAAc6C,SAAd,GAA0BwC,KAA1B;AACH;;AAEoB,YAAVvC,UAAU,GAAG;AACpB,iBAAO,KAAK9C,OAAL,CAAc8C,UAArB;AACH;;AACoB,YAAVA,UAAU,CAACuC,KAAD,EAAgB;AACjC,eAAKrF,OAAL,CAAc8C,UAAd,GAA2BuC,KAA3B;AACH;;AAEqB,YAAXG,WAAW,GAAG;AACrB,cAAI,KAAKxF,OAAL,CAAc+C,eAAd,KAAkC;AAAA;AAAA,oDAAiB0C,MAAvD,EAA+D;AAC3D,mBAAO,KAAKzF,OAAL,CAAckD,gBAArB;AACH;;AACD,iBAAO,CAAP;AACH;;AACqB,YAAXsC,WAAW,CAACH,KAAD,EAAgB;AAClC,eAAKrF,OAAL,CAAcmD,cAAd,CAA6B;AAAA;AAAA,oDAAiBsC,MAA9C,EAAsDJ,KAAtD;AACH,SArOiD,CAuOlD;;;AACyB,YAAdK,cAAc,GAAG;AAAA;;AACxB,iBAAO,iBAAKnG,UAAL,CAAiBO,MAAjB,6BAAyB6F,YAAzB,KAAyC,CAAhD;AACH;;AACuB,YAAbC,aAAa,GAAG;AAAA;;AACvB,iBAAO,kBAAKrG,UAAL,CAAiBO,MAAjB,8BAAyB+F,aAAzB,KAA0C,CAAjD;AACH;;AAC4B,YAAlB9B,kBAAkB,GAAY;AACrC,iBAAO,KAAKP,SAAL,CAAejB,sBAAf,CAAsC;AAAA;AAAA,gDAAeuB,sBAArD,KAAgF,CAAvF;AACH;;AAC6B,YAAnBG,mBAAmB,GAAY;AACtC,iBAAO,KAAKT,SAAL,CAAejB,sBAAf,CAAsC;AAAA;AAAA,gDAAeyB,yBAArD,KAAmF,CAA1F;AACH;;AACsB,YAAZG,YAAY,GAAY;AAC/B,iBAAO,KAAKX,SAAL,CAAejB,sBAAf,CAAsC;AAAA;AAAA,gDAAe2B,kBAArD,KAA4E,CAAnF;AACH;;AACuB,YAAbG,aAAa,GAAY;AAChC,iBAAO,KAAKb,SAAL,CAAejB,sBAAf,CAAsC;AAAA;AAAA,gDAAe6B,qBAArD,KAA+E,CAAtF;AACH;;AACoB,YAAVG,UAAU,GAAY;AAC7B,iBAAO,KAAKf,SAAL,CAAejB,sBAAf,CAAsC;AAAA;AAAA,gDAAe+B,uBAArD,KAAiF,CAAxF;AACH;;AAC2B,YAAjBG,iBAAiB,GAAY;AACpC,iBAAO,KAAKjB,SAAL,CAAejB,sBAAf,CAAsC;AAAA;AAAA,gDAAeiC,2BAArD,KAAqF,CAA5F;AACH;;AACoB,YAAVsB,UAAU,GAAY;AAC7B,iBAAO,KAAKtC,SAAL,CAAejB,sBAAf,CAAsC;AAAA;AAAA,gDAAewD,gBAArD,KAA0E,CAAjF;AACH;;AAES,YAANC,MAAM,GAAkB;AAAA;;AACxB,sCAAO;AAAA;AAAA,kCAAQC,gBAAf,qBAAO,kBAA0BC,SAAjC;AACH;;AAEDC,QAAAA,SAAS,GAAU;AACf,iBAAO,KAAK5G,UAAL,CAAiB4G,SAAjB,EAAP;AACH;;AAEDC,QAAAA,SAAS,CAACC,QAAD,EAAsB;AAC3B,cAAI,KAAKC,MAAT,EAAiB;AACb;AACH;;AACD,cAAID,QAAQ,CAACE,MAAT;AAAA;AAAA,+BAAJ,EAAuC;AACnC,gBAAI,KAAK/C,SAAL,CAAejB,sBAAf,CAAsC;AAAA;AAAA,kDAAeuB,sBAArD,KAAgF,CAApF,EAAuF;AACnF,oBAAM0C,MAAM,GAAGH,QAAQ,CAACE,MAAT,CAAgBE,UAAhB,CAA2B,IAA3B,CAAf;AACA;AAAA;AAAA,sCAAQC,iBAAR,CAA0BC,mBAA1B,CAA8CN,QAAQ,CAACE,MAAT,CAAgBK,IAAhB,CAAqBC,WAArB,EAA9C,EAAkFL,MAAlF;AACA,mBAAKM,IAAL,CAAUN,MAAV,EAHmF,CAInF;;AACA,oBAAMO,QAAQ,GAAGV,QAAQ,CAACE,MAAT,CAAgBS,OAAhB,CAAwBC,SAAxB,EAAjB;AACA,oBAAMC,eAAe,GAAG,CAAAH,QAAQ,QAAR,YAAAA,QAAQ,CAAEvD,SAAV,CAAoBjB,sBAApB,CAA2C;AAAA;AAAA,oDAAe4E,qBAA1D,MAAoF,CAA5G;;AACA,kBAAI;AAAA;AAAA,sCAAQC,aAAR,CAAsBC,MAAtB,KAAiCH,eAAe,GAAC,KAArD,EAA4D;AACxD,qBAAKI,QAAL,CAAcC,SAAd,CAAwB,KAAxB,EAA+B;AAAA;AAAA,oCAAMC,WAAN,CAAkBC,YAAlB,CAA+BC,2BAA9D;AACH;AACJ;AACJ,WAZD,MAYO,IAAIrB,QAAQ,CAACE,MAAT;AAAA;AAAA,yCAAwC,CAACF,QAAQ,CAACE,MAAT,CAAgBjG,KAA7D,EAAoE;AACvE,iBAAKqH,cAAL,CAAoBtB,QAAQ,CAACE,MAA7B;AACH;AACJ;AAED;AACJ;AACA;;;AACIvB,QAAAA,UAAU,GAAG,CAEZ;;AAED4C,QAAAA,SAAS,CAACC,MAAD,EAAiB;AACtB,cAAI,KAAKjI,OAAT,EAAkB;AACd,iBAAKA,OAAL,CAAakI,IAAb,CAAkBD,MAAlB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,YAAY,CAACF,MAAD,EAAiB;AACzB,cAAI,KAAKjI,OAAT,EAAkB;AACd,kBAAMoI,KAAK,GAAG,KAAKpI,OAAL,CAAaqI,OAAb,CAAqBJ,MAArB,CAAd;;AACA,gBAAIG,KAAK,IAAI,CAAb,EAAgB;AACZ,mBAAKpI,OAAL,CAAasI,MAAb,CAAoBF,KAApB,EAA2B,CAA3B;AACH;AACJ;AACJ;;AAEDlG,QAAAA,MAAM,CAACH,CAAD,EAAYC,CAAZ,EAAuB;AACzB,eAAKgF,IAAL,CAAUuB,WAAV,CAAsBxG,CAAtB,EAAyBC,CAAzB;AACH;;AA7TiD,O;;;;;iBAEvB,I", "sourcesContent": ["import { _decorator, Node, Prefab} from 'cc';\r\nimport { GameEnum } from '../../../const/GameEnum';\r\nimport PlaneBase from 'db://assets/bundles/common/script/game/ui/plane/PlaneBase';\r\nimport FCollider from '../../../collider-system/FCollider';\r\nimport { Bullet } from '../../../bullet/Bullet';\r\nimport { Plane } from 'db://assets/bundles/common/script/ui/Plane';\r\nimport { EnemyData, EnemyPrefab } from '../../../data/EnemyData';\r\nimport { eMoveEvent, eOrientationType } from 'db://assets/bundles/common/script/game/move/IMovable'\r\nimport { PathMove } from 'db://assets/bundles/common/script/game/move/PathMove';\r\nimport { PathData } from 'db://assets/bundles/common/script/game/data/PathData';\r\nimport { GameIns } from '../../../GameIns';\r\nimport { MyApp } from '../../../../app/MyApp';\r\nimport { EventGroupComp } from '../event/EventGroupCom';\r\nimport { AttributeConst } from 'db://assets/bundles/common/script/const/AttributeConst';\r\nimport { AttributeData } from '../../../../data/base/AttributeData';\r\nimport { ResEnemy } from 'db://assets/bundles/common/script/autogen/luban/schema';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('EnemyPlaneBase')\r\nexport default class EnemyPlaneBase extends PlaneBase {\r\n    @property(Node)\r\n    planeParent: Node | null = null;\r\n\r\n    _plane: Plane | null = null;\r\n    _enemyData: EnemyData | null = null;\r\n    _enemyPrefabData: EnemyPrefab | null = null;\r\n\r\n    public get resConfig(): ResEnemy | null {\r\n        return this._enemyData?.config || null;\r\n    }\r\n    public get enemyPrefabData(): EnemyPrefab | null { return this._enemyPrefabData; }\r\n\r\n    _moveCom: PathMove | null = null;\r\n    public get moveCom() { return this._moveCom; }\r\n    _eventGroupComp: EventGroupComp | null = null;\r\n    public get eventGroupComp() { return this._eventGroupComp; }\r\n\r\n    removeAble:boolean = false;\r\n    bullets: Bullet[] = [];\r\n\r\n    reset() {\r\n        // 继承自BaseComp的组件在reset里会执行reset\r\n        super.reset();\r\n        // 继承自Component的组件这里自行reset\r\n        this._plane?.reset();\r\n        this._moveCom?.reset();\r\n    }\r\n\r\n    initPlane(data: EnemyData, prefab: Prefab) {\r\n        this.reset();\r\n        this.enemy = true\r\n        this._enemyData = data;\r\n\r\n        this.removeAble = false;\r\n        //加载飞机显示\r\n        if (!this._plane) {\r\n            let plane = MyApp.planeMgr.getPlane(data, prefab);\r\n            this._plane = plane.getComponent(Plane)!;\r\n            this.planeParent!.addChild(plane);\r\n            this._enemyPrefabData = plane.getComponentInChildren(EnemyPrefab);\r\n            if (this._enemyPrefabData && this._enemyPrefabData.eventGroups) {\r\n                this._eventGroupComp = this.getComp('eventGroup') as EventGroupComp;\r\n                if (!this._eventGroupComp) {\r\n                    this._eventGroupComp = new EventGroupComp();\r\n                    this.addComp('eventGroup', this._eventGroupComp);\r\n                }\r\n            }\r\n        }\r\n\r\n        super.init();\r\n\r\n        this._moveCom = this.getComponent(PathMove) || this.addComponent(PathMove);\r\n        this._moveCom!.removeAllListeners();\r\n        this._moveCom!.on(eMoveEvent.onBecomeInvisible, () => this.onBecameInvisible());\r\n        this._moveCom!.on(eMoveEvent.onBecomeVisible, () => this.onBecameVisible());\r\n\r\n        this.refreshAttributes();\r\n    }\r\n\r\n    initMove(x: number, y: number, angle: number) {\r\n        this.setPos(x, y);\r\n        // 兼容编辑器\r\n        if (!this._moveCom) {        \r\n            this._moveCom = this.getComponent(PathMove) || this.addComponent(PathMove);\r\n        }\r\n        this.refreshMoveParam();\r\n        this._moveCom!.speedAngle = angle;\r\n        this._moveCom!.setMovable(true);\r\n    }\r\n\r\n    initPath(x: number, y: number, pathData: PathData) {\r\n        if (!this._moveCom) {\r\n            return;\r\n        }\r\n        \r\n        this.setPos(x, y);\r\n        this.refreshMoveParam();\r\n        this._moveCom!.setOffset(x, y).setPath(pathData).setMovable(true);\r\n    }\r\n\r\n    private refreshMoveParam() {\r\n        if (!this._moveCom) {\r\n            return;\r\n        }\r\n\r\n        this._moveCom.speed = this._enemyData?.getFinalAttributeByKey(AttributeConst.MoveSpeed) || 100;\r\n        this._moveCom.turnSpeed = this._enemyData?.getFinalAttributeByKey(AttributeConst.TurnSpeed) || 0;\r\n        this._moveCom.acceleration = 0;\r\n        this._moveCom.accelerationAngle = 0;\r\n        this._moveCom.tiltSpeed = this._enemyPrefabData?.tiltSpeed || 0;\r\n        this._moveCom.tiltOffset = this._enemyPrefabData?.tiltOffset || 0;\r\n        let orientationType = this._enemyData?.config?.orientation as any as eOrientationType || eOrientationType.Path;\r\n        let orientationParam = this._enemyData?.config?.orientationParam || 0;\r\n        this._moveCom.setOrientation(orientationType, orientationParam);\r\n    }\r\n\r\n    private dieWhenOffScreen() {\r\n        this.toDie(GameEnum.EnemyDestroyType.Leave);\r\n    }\r\n\r\n    private onBecameInvisible() {\r\n        // TODO: 从表格里增加延时销毁的配置\r\n        this.dieWhenOffScreen();\r\n        // this.scheduleOnce(this.dieWhenOffScreen, delayDestroy);\r\n    }\r\n\r\n    private onBecameVisible() {\r\n        // this.unschedule(this.dieWhenOffScreen);\r\n    }\r\n\r\n    refreshAttributes() {\r\n        const config = this._enemyData?.config;\r\n        if (!config) {\r\n            return;\r\n        }\r\n        this.attribute.addBaseAttribute(AttributeConst.MaxHPOutAdd, config.baseHp);\r\n        this.attribute.addBaseAttribute(AttributeConst.AttackOutAdd, config.baseAtk);\r\n        this.attribute.addBaseAttribute(AttributeConst.StatusImmuneBulletHurt, config.immuneBulletDamage?1:0);\r\n        this.attribute.addBaseAttribute(AttributeConst.StatusImmuneCollisionHurt, config.immuneCollideDamage?1:0);\r\n        this.attribute.addBaseAttribute(AttributeConst.StatusIgnoreBullet, config.ignoreBullet?1:0);\r\n        this.attribute.addBaseAttribute(AttributeConst.StatusIgnoreCollision, config.ignoreCollide?1:0);\r\n        this.attribute.addBaseAttribute(AttributeConst.StatusImmuneNuclearHurt, config.immuneNuke?1:0);\r\n        this.attribute.addBaseAttribute(AttributeConst.StatusImmuneActiveSkillHurt, config.immuneActiveSkill?1:0);\r\n\r\n        this.curHp = this.maxHp\r\n    }\r\n\r\n    toDie(destroyType: GameEnum.EnemyDestroyType = GameEnum.EnemyDestroyType.Die): boolean  {\r\n        if (!super.toDie(destroyType)) {\r\n            return false;\r\n        }\r\n        this.colliderEnabled = false;\r\n\r\n        this.onDie(destroyType);\r\n        return true;\r\n    }\r\n\r\n    onDie(destroyType: number) {\r\n        this.willRemove();\r\n\r\n        switch (destroyType) {\r\n            case GameEnum.EnemyDestroyType.Die:\r\n                this.playDieAnim(()=>{\r\n                    this.removeAble = true;\r\n                });\r\n                break;\r\n\r\n            case GameEnum.EnemyDestroyType.Leave:\r\n            case GameEnum.EnemyDestroyType.TrackOver:\r\n            case GameEnum.EnemyDestroyType.TimeOver:\r\n                this.removeAble = true;\r\n                break;\r\n        }\r\n    }\r\n\r\n    playDieAnim(callBack: Function) {\r\n        // if (this.plane) {\r\n        //     this.plane.playDieAnim(callBack);   \r\n        // }\r\n        callBack?.();\r\n    }\r\n    public get attribute(): AttributeData {\r\n        return this._enemyData!;\r\n    }\r\n    // 表现相关的'属性'接口\r\n    public get speed() {\r\n        return this.attribute.getFinalAttributeByKey(AttributeConst.MoveSpeed);\r\n    }\r\n    public set speed(value: number) {\r\n        this.attribute.setBaseAttribute(AttributeConst.MoveSpeed, value);\r\n        this.moveCom!.speed = value;\r\n    }\r\n\r\n    public get turnSpeed() {\r\n        return this.attribute.getFinalAttributeByKey(AttributeConst.TurnSpeed);\r\n    }\r\n    public set turnSpeed(value: number) {\r\n        this.attribute.setBaseAttribute(AttributeConst.TurnSpeed, value);\r\n        this.moveCom!.turnSpeed = value;\r\n    }\r\n    \r\n    public get speedAngle() {\r\n        return this.moveCom!.speedAngle;\r\n    }\r\n    public set speedAngle(value: number) {\r\n        this.moveCom!.speedAngle = value;\r\n        // 一旦通过speedAngle设置角度，就关闭路径\r\n        this.moveCom!.setOnPath(false);\r\n    }\r\n\r\n    public get acceleration() {\r\n        return this.moveCom!.acceleration;\r\n    }\r\n    public set acceleration(value: number) {\r\n        this.moveCom!.acceleration = value;\r\n    }\r\n\r\n    public get accelerationAngle() {\r\n        return this.moveCom!.accelerationAngle;\r\n    }\r\n    public set accelerationAngle(value: number) {\r\n        this.moveCom!.accelerationAngle = value;\r\n        // 一旦通过accelerationAngle设置角度，就关闭路径\r\n        this.moveCom!.setOnPath(false);\r\n    }\r\n\r\n    public get tiltSpeed() {\r\n        return this.moveCom!.tiltSpeed;\r\n    }\r\n    public set tiltSpeed(value: number) {\r\n        this.moveCom!.tiltSpeed = value;\r\n    }\r\n\r\n    public get tiltOffset() {\r\n        return this.moveCom!.tiltOffset;\r\n    }\r\n    public set tiltOffset(value: number) {\r\n        this.moveCom!.tiltOffset = value;\r\n    }\r\n\r\n    public get rotateSpeed() {\r\n        if (this.moveCom!.orientationType === eOrientationType.Rotate) {\r\n            return this.moveCom!.orientationParam;\r\n        }\r\n        return 0;\r\n    }\r\n    public set rotateSpeed(value: number) {\r\n        this.moveCom!.setOrientation(eOrientationType.Rotate, value);\r\n    }\r\n\r\n    // 常用的属性接口\r\n    public get collisionLevel() {\r\n        return this._enemyData!.config?.collideLevel || 0;\r\n    }\r\n    public get collisionHurt() {\r\n        return this._enemyData!.config?.collideDamage || 0;\r\n    }\r\n    public get immuneBulletDamage(): boolean {\r\n        return this.attribute.getFinalAttributeByKey(AttributeConst.StatusImmuneBulletHurt) == 1;\r\n    }\r\n    public get immuneCollideDamage(): boolean {\r\n        return this.attribute.getFinalAttributeByKey(AttributeConst.StatusImmuneCollisionHurt) == 1;\r\n    }\r\n    public get ignoreBullet(): boolean {\r\n        return this.attribute.getFinalAttributeByKey(AttributeConst.StatusIgnoreBullet) == 1;\r\n    }\r\n    public get ignoreCollide(): boolean {\r\n        return this.attribute.getFinalAttributeByKey(AttributeConst.StatusIgnoreCollision) == 1;\r\n    }\r\n    public get immuneNuke(): boolean {\r\n        return this.attribute.getFinalAttributeByKey(AttributeConst.StatusImmuneNuclearHurt) == 1;\r\n    }\r\n    public get immuneActiveSkill(): boolean {\r\n        return this.attribute.getFinalAttributeByKey(AttributeConst.StatusImmuneActiveSkillHurt) == 1;\r\n    }\r\n    public get invincible(): boolean {\r\n        return this.attribute.getFinalAttributeByKey(AttributeConst.StatusInvincible) == 1;\r\n    }\r\n\r\n    get target():PlaneBase|null {\r\n        return GameIns.mainPlaneManager?.mainPlane;\r\n    }\r\n\r\n    getAttack():number {\r\n        return this._enemyData!.getAttack();\r\n    }\r\n\r\n    onCollide(collider: FCollider) {\r\n        if (this.isDead) {\r\n            return\r\n        }\r\n        if (collider.entity instanceof Bullet) {\r\n            if (this.attribute.getFinalAttributeByKey(AttributeConst.StatusImmuneBulletHurt) == 0) {\r\n                const damage = collider.entity.calcDamage(this);\r\n                GameIns.hurtEffectManager.createHurtNumByType(collider.entity.node.getPosition(), damage);\r\n                this.hurt(damage)\r\n                // 计算破甲概率\r\n                const attacker = collider.entity.emitter.getEntity();\r\n                const penetrationRate = attacker?.attribute.getFinalAttributeByKey(AttributeConst.BulletPenetrationRate) || 0;\r\n                if (GameIns.battleManager.random() < penetrationRate/10000) {\r\n                    this.buffComp.ApplyBuff(false, MyApp.lubanTables.TbGlobalAttr.BulletPenetrationFlagBuffID)\r\n                }\r\n            }\r\n        } else if (collider.entity instanceof PlaneBase && !collider.entity.enemy) {\r\n            this.collisionPlane(collider.entity);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 准备移除敌机\r\n     */\r\n    willRemove() {\r\n\r\n    }\r\n\r\n    addBullet(bullet: Bullet) {\r\n        if (this.bullets) {\r\n            this.bullets.push(bullet);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 从敌人移除子弹\r\n     * @param {Bullet} bullet 子弹对象\r\n     */\r\n    removeBullet(bullet: Bullet) {\r\n        if (this.bullets) {\r\n            const index = this.bullets.indexOf(bullet);\r\n            if (index >= 0) {\r\n                this.bullets.splice(index, 1);\r\n            }\r\n        }\r\n    }\r\n\r\n    setPos(x: number, y: number) {\r\n        this.node.setPosition(x, y);\r\n    }\r\n}"]}
{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelBaseUI.ts"], "names": ["_decorator", "Component", "Node", "LevelLayerUI", "LevelBackgroundLayerUI", "LevelUtils", "ccclass", "property", "LevelBaseUI", "_curLevelIndex", "_totalTime", "_speed", "_preLevelOffsetY", "_backgroundLayerNode", "_floorLayersNode", "_skyLayersNode", "_backgroundLayer", "_floorLayers", "_skyLayers", "background<PERSON>ayer", "floorLayers", "skyLayers", "TotalTime", "onLoad", "levelPrefab", "levelData", "levelInfo", "getOrAddNode", "node", "bFirstLoad", "_initByLevelData", "switchLevel", "time", "levelIndex", "_removeNode", "parentNode", "name", "getChildByName", "removeFromParent", "data", "levelBackground", "levelFloor", "levelSky", "_initBackgroundLayer", "_initLayers", "dataLayer", "backgrounds", "length", "addComponent", "<PERSON><PERSON><PERSON><PERSON>", "speed", "initByLevelData", "layers", "dataLayers", "i", "layer", "<PERSON><PERSON>ayer", "_addLayer", "push", "layerNode", "layerCom", "tick", "deltaTime", "layerUI", "for<PERSON>ach"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;AAEvBC,MAAAA,Y,iBAAAA,Y;;AAEAC,MAAAA,sB,iBAAAA,sB;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;OAGH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;;6BAGjBQ,W,WADZF,OAAO,CAAC,aAAD,C,gBAAR,MACaE,WADb,SACiCP,SADjC,CAC2C;AAAA;AAAA;AAAA,eAC/BQ,cAD+B,GACN,CAAC,CADK;AACF;AADE,eAE/BC,UAF+B,GAEV,EAFU;AAEN;AAFM,eAG/BC,MAH+B,GAGd,CAHc;AAGX;AAHW,eAI/BC,gBAJ+B,GAIJ,CAJI;AAID;AAJC,eAM/BC,oBAN+B,GAMK,IANL;AAAA,eAO/BC,gBAP+B,GAOC,IAPD;AAAA,eAQ/BC,cAR+B,GAQD,IARC;AAAA,eAU/BC,gBAV+B,GAUO,IAVP;AAAA,eAW/BC,YAX+B,GAWF,EAXE;AAAA,eAY/BC,UAZ+B,GAYJ,EAZI;AAAA;;AAcb,YAAfC,eAAe,GAAe;AACrC,iBAAO,KAAKH,gBAAZ;AACH;;AAEqB,YAAXI,WAAW,GAAiB;AACnC,iBAAO,KAAKH,YAAZ;AACH;;AACmB,YAATI,SAAS,GAAiB;AACjC,iBAAO,KAAKH,UAAZ;AACH;;AAEmB,YAATI,SAAS,GAAW;AAC3B,iBAAO,KAAKZ,UAAZ;AACH;;AAESa,QAAAA,MAAM,GAAS,CACxB;;AAEuB,cAAXC,WAAW,CAACC,SAAD,EAAuBC,SAAvB,EAA6F;AACjH,eAAKb,oBAAL,GAA4B;AAAA;AAAA,wCAAWc,YAAX,CAAwB,KAAKC,IAA7B,EAAmC,iBAAnC,CAA5B;AACA,eAAKd,gBAAL,GAAwB;AAAA;AAAA,wCAAWa,YAAX,CAAwB,KAAKC,IAA7B,EAAmC,aAAnC,CAAxB;AACA,eAAKb,cAAL,GAAsB;AAAA;AAAA,wCAAWY,YAAX,CAAwB,KAAKC,IAA7B,EAAmC,WAAnC,CAAtB;;AAEA,cAAIF,SAAS,CAACG,UAAd,EAA0B;AACtB,kBAAM,KAAKC,gBAAL,CAAsBL,SAAtB,EAAiCC,SAAjC,CAAN;AACH,WAFD,MAEO;AACH,iBAAKI,gBAAL,CAAsBL,SAAtB,EAAiCC,SAAjC;AACH;AACJ;;AAEMK,QAAAA,WAAW,CAACC,IAAD,EAAeC,UAAf,EAAyC;AACvD,eAAKvB,UAAL,GAAkBsB,IAAlB,CADuD,CAGvD;;AACA,cAAI,KAAKvB,cAAL,KAAwB,CAAC,CAA7B,EAAgC;AAC5B,iBAAKyB,WAAL,CAAiB,KAAKrB,oBAAtB,EAA8C,SAAQ,KAAKJ,cAAe,EAA1E;;AACA,iBAAKyB,WAAL,CAAiB,KAAKpB,gBAAtB,EAA0C,SAAQ,KAAKL,cAAe,EAAtE;;AACA,iBAAKyB,WAAL,CAAiB,KAAKnB,cAAtB,EAAwC,SAAQ,KAAKN,cAAe,EAApE;AACH;;AAED,eAAKA,cAAL,GAAsBwB,UAAtB;AACH;;AAEOC,QAAAA,WAAW,CAACC,UAAD,EAAmBC,IAAnB,EAAuC;AACtD,cAAIR,IAAI,GAAGO,UAAU,CAACE,cAAX,CAA0BD,IAA1B,CAAX;;AACA,cAAIR,IAAJ,EAAU;AACNA,YAAAA,IAAI,CAACU,gBAAL;AACH;AACJ;;AAE4B,cAAhBR,gBAAgB,CAACS,IAAD,EAAkBb,SAAlB,EAAwF;AACjH,gBAAMc,eAAe,GAAG;AAAA;AAAA,wCAAWb,YAAX,CAAwB,KAAKd,oBAA7B,EAAqD,SAAQa,SAAS,CAACO,UAAW,EAAlF,CAAxB;AACA,gBAAMQ,UAAU,GAAG;AAAA;AAAA,wCAAWd,YAAX,CAAwB,KAAKb,gBAA7B,EAAiD,SAAQY,SAAS,CAACO,UAAW,EAA9E,CAAnB;AACA,gBAAMS,QAAQ,GAAG;AAAA;AAAA,wCAAWf,YAAX,CAAwB,KAAKZ,cAA7B,EAA+C,SAAQW,SAAS,CAACO,UAAW,EAA5E,CAAjB;AAEA,gBAAM,KAAKU,oBAAL,CAA0BH,eAA1B,EAA2CD,IAAI,CAACpB,eAAhD,EAAiEO,SAAS,CAACG,UAA3E,CAAN;AACA,gBAAM,KAAKe,WAAL,CAAiBH,UAAjB,EAA6B,KAAKrB,WAAlC,EAA+CmB,IAAI,CAACnB,WAApD,EAAiEM,SAAS,CAACG,UAA3E,CAAN;AACA,gBAAM,KAAKe,WAAL,CAAiBF,QAAjB,EAA2B,KAAKrB,SAAhC,EAA2CkB,IAAI,CAAClB,SAAhD,EAA2DK,SAAS,CAACG,UAArE,CAAN;AACH;;AAEiC,cAApBc,oBAAoB,CAACR,UAAD,EAAmBU,SAAnB,EAAwDhB,UAAxD,EAA4F;AAC1H,cAAIgB,SAAS,CAACC,WAAV,CAAsBC,MAAtB,GAA+B,CAAnC,EAAsC;AAClC,gBAAInB,IAAI,GAAG,IAAI1B,IAAJ,CAAS,MAAT,CAAX;AACA,kBAAMiB,eAAe,GAAGS,IAAI,CAACoB,YAAL;AAAA;AAAA,iEAAxB;AACAb,YAAAA,UAAU,CAACc,QAAX,CAAoBrB,IAApB;AACAT,YAAAA,eAAe,CAAC+B,KAAhB,GAAwBL,SAAS,CAACK,KAAlC;AACA,kBAAM/B,eAAe,CAAEgC,eAAjB,CAAiCN,SAAjC,EAA4C,KAAKjC,gBAAjD,EAAmEiB,UAAnE,CAAN;AACA,iBAAKb,gBAAL,GAAwBG,eAAxB;AACH;AACJ;;AAEwB,cAAXyB,WAAW,CAACT,UAAD,EAAmBiB,MAAnB,EAAyCC,UAAzC,EAAuExB,UAAvE,EAA2G;AAChI,eAAK,IAAIyB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,UAAU,CAACN,MAA/B,EAAuCO,CAAC,EAAxC,EAA4C;AACxC,kBAAMC,KAAK,GAAGF,UAAU,CAACC,CAAD,CAAxB;;AACA,gBAAIE,UAAU,GAAG,KAAKC,SAAL,CAAetB,UAAf,EAA4B,SAAQmB,CAAE,EAAtC,CAAjB;;AACAE,YAAAA,UAAU,CAAEN,KAAZ,GAAoBK,KAAK,CAACL,KAA1B;AACA,kBAAMM,UAAU,CAAEL,eAAZ,CAA4BI,KAA5B,EAAmC,KAAK3C,gBAAxC,EAA0DiB,UAA1D,CAAN;AACAuB,YAAAA,MAAM,CAACM,IAAP,CAAYF,UAAZ;AACH;AACJ;;AAEOC,QAAAA,SAAS,CAACtB,UAAD,EAAmBC,IAAnB,EAA+C;AAC5D,cAAIuB,SAAS,GAAG,IAAIzD,IAAJ,CAASkC,IAAT,CAAhB;AACA,cAAIwB,QAAQ,GAAGD,SAAS,CAACX,YAAV;AAAA;AAAA,2CAAf;AACAb,UAAAA,UAAU,CAACc,QAAX,CAAoBU,SAApB;AACA,iBAAOC,QAAP;AACH;;AAEMC,QAAAA,IAAI,CAACC,SAAD,EAA0B;AACjC,gBAAMC,OAAO,GAAG,KAAK5C,eAArB,CADiC,CACI;;AACrC,cAAI4C,OAAJ,EAAa;AACTA,YAAAA,OAAO,CAACF,IAAR,CAAaC,SAAb;AACH;;AAED,eAAK1C,WAAL,CAAiB4C,OAAjB,CAA0BT,KAAD,IAAW;AAChC,kBAAMQ,OAAO,GAAGR,KAAhB,CADgC,CACV;;AACtB,gBAAIQ,OAAJ,EAAa;AACTA,cAAAA,OAAO,CAACF,IAAR,CAAaC,SAAb;AACH;AACJ,WALD;AAOA,eAAKzC,SAAL,CAAe2C,OAAf,CAAwBT,KAAD,IAAW;AAC9B,kBAAMQ,OAAO,GAAGR,KAAhB,CAD8B,CACR;;AACtB,gBAAIQ,OAAJ,EAAa;AACTA,cAAAA,OAAO,CAACF,IAAR,CAAaC,SAAb;AACH;AACJ,WALD;AAMH;;AAzHsC,O", "sourcesContent": ["import { _decorator, Component, Node, Prefab } from \"cc\";\r\nimport { LevelData, LevelDataBackgroundLayer, LevelDataLayer } from \"db://assets/bundles/common/script/leveldata/leveldata\";\r\nimport { LevelLayerUI } from \"./LevelLayerUI\";\r\nimport { logError } from \"db://assets/scripts/utils/Logger\";\r\nimport { LevelBackgroundLayerUI } from \"./LevelBackgroundLayerUI\";\r\nimport { LevelUtils } from \"./LevelUtils\";\r\nimport { LevelLayer } from \"./LevelLayer\";\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('LevelBaseUI')\r\nexport class LevelBaseUI extends Component {\r\n    private _curLevelIndex: number = -1; // 当前关卡索引\r\n    private _totalTime: number = 10; // 当前关卡的时长\r\n    private _speed: number = 0; // 当前关卡的速度\r\n    private _preLevelOffsetY: number = 0; // 上一关的关卡偏移量\r\n\r\n    private _backgroundLayerNode: Node | null = null;\r\n    private _floorLayersNode: Node | null = null;\r\n    private _skyLayersNode: Node | null = null;\r\n\r\n    private _backgroundLayer: LevelLayer | null = null;\r\n    private _floorLayers: LevelLayer[] = [];\r\n    private _skyLayers: LevelLayer[] = [];\r\n\r\n    public get backgroundLayer(): LevelLayer {\r\n        return this._backgroundLayer!;\r\n    }\r\n\r\n    public get floorLayers(): LevelLayer[] {\r\n        return this._floorLayers;\r\n    }\r\n    public get skyLayers(): LevelLayer[] {\r\n        return this._skyLayers;\r\n    }\r\n\r\n    public get TotalTime(): number {\r\n        return this._totalTime;\r\n    }\r\n\r\n    protected onLoad(): void {\r\n    }\r\n\r\n    public async levelPrefab(levelData: LevelData, levelInfo: {bFirstLoad: boolean, levelIndex: number }): Promise<void> {\r\n        this._backgroundLayerNode = LevelUtils.getOrAddNode(this.node, \"BackgroundLayer\");\r\n        this._floorLayersNode = LevelUtils.getOrAddNode(this.node, \"FloorLayers\");\r\n        this._skyLayersNode = LevelUtils.getOrAddNode(this.node, \"SkyLayers\");\r\n\r\n        if (levelInfo.bFirstLoad) {\r\n            await this._initByLevelData(levelData, levelInfo);\r\n        } else {\r\n            this._initByLevelData(levelData, levelInfo);\r\n        }\r\n    }\r\n\r\n    public switchLevel(time: number, levelIndex: number): void {\r\n        this._totalTime = time;\r\n\r\n        // 释放上一关资源\r\n        if (this._curLevelIndex !== -1) {\r\n            this._removeNode(this._backgroundLayerNode!, `level_${this._curLevelIndex}`);\r\n            this._removeNode(this._floorLayersNode!, `level_${this._curLevelIndex}`);\r\n            this._removeNode(this._skyLayersNode!, `level_${this._curLevelIndex}`);\r\n        }\r\n\r\n        this._curLevelIndex = levelIndex;\r\n    }\r\n\r\n    private _removeNode(parentNode: Node, name: string): void {\r\n        var node = parentNode.getChildByName(name);\r\n        if (node) {\r\n            node.removeFromParent();\r\n        }\r\n    }\r\n\r\n    public async _initByLevelData(data: LevelData, levelInfo: {bFirstLoad: boolean, levelIndex: number }): Promise<void> {\r\n        const levelBackground = LevelUtils.getOrAddNode(this._backgroundLayerNode!, `level_${levelInfo.levelIndex}`);\r\n        const levelFloor = LevelUtils.getOrAddNode(this._floorLayersNode!, `level_${levelInfo.levelIndex}`);\r\n        const levelSky = LevelUtils.getOrAddNode(this._skyLayersNode!, `level_${levelInfo.levelIndex}`);\r\n\r\n        await this._initBackgroundLayer(levelBackground, data.backgroundLayer, levelInfo.bFirstLoad);\r\n        await this._initLayers(levelFloor, this.floorLayers, data.floorLayers, levelInfo.bFirstLoad);\r\n        await this._initLayers(levelSky, this.skyLayers, data.skyLayers, levelInfo.bFirstLoad);\r\n    }\r\n\r\n    private async _initBackgroundLayer(parentNode: Node, dataLayer: LevelDataBackgroundLayer, bFirstLoad: boolean): Promise<void> {\r\n        if (dataLayer.backgrounds.length > 0) {\r\n            let node = new Node('bg_0');\r\n            const backgroundLayer = node.addComponent(LevelBackgroundLayerUI);\r\n            parentNode.addChild(node);\r\n            backgroundLayer.speed = dataLayer.speed;\r\n            await backgroundLayer!.initByLevelData(dataLayer, this._preLevelOffsetY, bFirstLoad);\r\n            this._backgroundLayer = backgroundLayer;\r\n        }\r\n    }\r\n\r\n    private async _initLayers(parentNode: Node, layers: LevelLayer[], dataLayers: LevelDataLayer[], bFirstLoad: boolean): Promise<void> {\r\n        for (let i = 0; i < dataLayers.length; i++) {\r\n            const layer = dataLayers[i];\r\n            let levelLayer = this._addLayer(parentNode, `layer_${i}`);\r\n            levelLayer!.speed = layer.speed;\r\n            await levelLayer!.initByLevelData(layer, this._preLevelOffsetY, bFirstLoad);\r\n            layers.push(levelLayer!);\r\n        }\r\n    }\r\n\r\n    private _addLayer(parentNode: Node, name: string): LevelLayerUI {\r\n        var layerNode = new Node(name);\r\n        var layerCom = layerNode.addComponent<LevelLayerUI>(LevelLayerUI);\r\n        parentNode.addChild(layerNode);\r\n        return layerCom;\r\n    }\r\n\r\n    public tick(deltaTime: number): void {\r\n        const layerUI = this.backgroundLayer;//!.node?.getComponent<LevelBackgroundLayerUI>(LevelBackgroundLayerUI);\r\n        if (layerUI) {\r\n            layerUI.tick(deltaTime);\r\n        }\r\n\r\n        this.floorLayers.forEach((layer) => {\r\n            const layerUI = layer;//.node?.getComponent<LevelLayerUI>(LevelLayerUI);\r\n            if (layerUI) {\r\n                layerUI.tick(deltaTime);\r\n            }\r\n        });\r\n        \r\n        this.skyLayers.forEach((layer) => {\r\n            const layerUI = layer;//.node?.getComponent<LevelLayerUI>(LevelLayerUI);\r\n            if (layerUI) {\r\n                layerUI.tick(deltaTime);\r\n            }\r\n        });\r\n    }\r\n}"]}
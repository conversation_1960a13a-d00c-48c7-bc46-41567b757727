/**
 * 波次编辑器
 */
'use strict';

const { updatePropByDump, disconnectGroup } = require('./../../prop');
import { assetManager, Asset } from 'cc';
import { getAssetUuidByPath } from '../../assets-menu';

type Selector<$> = { $: Record<keyof $, any | null> }

export const template = `
<div class="component-container"></div>
<ui-prop>
    <ui-button class="btn-formation">编辑阵型</ui-button>
    <ui-button class="btn-path">编辑路径</ui-button>
</ui-prop>
`;

export const $ = {
    componentContainer: '.component-container',
    btnFormation: '.btn-formation',
    btnPath: '.btn-path',
};

type PanelThis = Selector<typeof $> & { dump: any };

export function update(this: PanelThis, dump: any) {
    updatePropByDump(this, dump);
    this.dump = dump;
}

export async function ready(this: PanelThis) {
    disconnectGroup(this);
    
    this.$.btnFormation.addEventListener('confirm', async () => {
        // 打开FormationEditor场景 Uuid = aa842f3a-8aea-42c2-a480-968aade3dfef
        getAssetUuidByPath('db://assets/scenes/FormationEditor.scene').then((uuid: string) => {
            const _uuid = this.dump?.value.waveData?.value.formationAsset?.value.uuid;
            // @ts-ignore
            Editor.Message.request('scene', 'open-scene', uuid).then(() => {
            // 场景打开后，加载选中的阵型JSON文件到FormationEditor组件
            // @ts-ignore
            Editor.Message.request('scene', 'execute-scene-script', {
                name: 'level-editor',
                method: 'loadFormationData',
                args: [_uuid]
            });
            }).catch((err: any) => {
                console.error('Failed to open FormationEditor scene:', err);
            });
        });
    });

    this.$.btnPath.addEventListener('confirm', async () => {
        // 打开PathEditor场景
        getAssetUuidByPath('db://assets/scenes/PathEditor.scene').then((uuid: string) => {
            const _uuid = this.dump?.value.waveData?.value.pathAsset?.value.uuid;
            // @ts-ignore
            Editor.Message.request('scene', 'open-scene', uuid).then(() => {
            // 场景打开后，加载选中的路径JSON文件到PathEditor组件
            // @ts-ignore
            Editor.Message.request('scene', 'execute-scene-script', {
                name: 'level-editor',
                method: 'loadPathData',
                args: [_uuid]
            });
            }).catch((err: any) => {
                console.error('Failed to open PathEditor scene:', err);
            });
        });
    });
}

/**
 * path: '/assets/resources/game/level/wave/path/'
 * extension: 'json'
 */
// async function createAsset(path: string, extension: string): Promise<Asset|null> {
//     // @ts-ignore
//     return Editor.Dialog.save({
//         path: Editor.Project.path + path,
//         filters: [
//             { name: extension, extensions: [extension] },
//         ],
//     }).then((file) => {
//         if (file.canceled || !file.filePath) {
//             return null;
//         }
//         else {
//             // @ts-ignore
//             Editor.Message.request('asset-db', 'create-asset', file.filePath, pathEditor.save()).then((res) => {
//                 if (res) {
//                     assetManager.loadAny({uuid: res.uuid}, (err, asset) => {
//                         if (err) {
//                             console.error(err);
//                         } else {
//                             return asset;
//                         }
//                     });
//                 }
//             });
//         }
//     });
// }

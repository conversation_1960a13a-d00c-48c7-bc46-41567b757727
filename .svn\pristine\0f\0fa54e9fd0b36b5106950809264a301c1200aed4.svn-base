import { _decorator, Node } from 'cc';
import { BundleName } from 'db://assets/bundles/common/script/const/BundleConst';
import { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';
import { BaseUI, UILayer, UIMgr, UIOpt, } from 'db://assets/scripts/core/base/UIMgr';
import { DataMgr } from '../../data/DataManager';
import { DataEvent } from '../../event/DataEvent';
import { EventMgr } from '../../event/EventManager';
import List from '../common/components/list/List';
import { FriendFusionCellUI } from './FriendFusionCellUI';

const { ccclass, property } = _decorator;

@ccclass('FriendFusionUI')
export class FriendFusionUI extends BaseUI {
    @property(ButtonPlus)
    btnClose: ButtonPlus | null = null;
    @property(ButtonPlus)
    btnBattle: ButtonPlus | null = null;
    @property(List)
    list: List | null = null;

    public static getUrl(): string { return "prefab/ui/FriendFusionUI"; }
    public static getLayer(): UILayer { return UILayer.Default }
    public static getBundleName(): string { return BundleName.HomePK; }
    public static getUIOption(): UIOpt { return { isClickBgCloseUI: true } }
    protected onLoad(): void {
        this.btnClose!.addClick(this.onCloseClick, this);
        this.btnBattle!.addClick(this.onBattleClick, this);
        this.list!.numItems = 10;
        EventMgr.on(DataEvent.FusionCellClick, this.onFusionCellClick, this);
    }

    async onCloseClick() {
        UIMgr.closeUI(FriendFusionUI);
    }
    async onBattleClick() {

    }
    private onFusionCellClick(index: number) {
        this.list!.content.children.forEach(element => {
            element.getComponent(FriendFusionCellUI)!.updateSelect();
        });
    }
    async onShow(): Promise<void> {

    }
    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {
    }
    protected onDestroy(): void {
        EventMgr.off(DataEvent.FusionCellClick, this.onFusionCellClick, this);
    }

    onListRender(listItem: Node, row: number) {
        const cell = listItem.getComponent(FriendFusionCellUI);
        if (cell !== null) {
            cell.setData(row + 1, row + 1);
        }
    }
}



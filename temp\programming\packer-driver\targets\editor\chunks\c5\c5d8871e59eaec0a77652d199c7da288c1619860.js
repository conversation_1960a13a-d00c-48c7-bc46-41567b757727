System.register(["__unresolved_0", "cc", "__unresolved_1", "long", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10", "__unresolved_11", "__unresolved_12", "__unresolved_13", "__unresolved_14", "__unresolved_15", "__unresolved_16", "__unresolved_17", "__unresolved_18", "__unresolved_19", "__unresolved_20", "__unresolved_21", "__unresolved_22", "__unresolved_23"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, director, <PERSON><PERSON>, log<PERSON><PERSON><PERSON>, <PERSON>, GameConst, SingletonBase, UIMgr, MyApp, ModeType, DataMgr, EventManager, EventMgr, HomeUIEvent, GameReviveUI, MBoomUI, LoadingUI, BottomUI, HomeUI, TopUI, BulletSystem, GameEvent, GameIns, lcgRand, GameMain, RogueUI, GameEnum, BundleName, BattleManager, _crd;

  function _reportPossibleCrUseOflogWarn(extras) {
    _reporterNs.report("logWarn", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLong(extras) {
    _reporterNs.report("Long", "long", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../../../../scripts/core/base/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSingletonBase(extras) {
    _reporterNs.report("SingletonBase", "../../../../../scripts/core/base/SingletonBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "../../../../../scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfModeType(extras) {
    _reporterNs.report("ModeType", "../../autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResGameMode(extras) {
    _reporterNs.report("ResGameMode", "../../autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataMgr(extras) {
    _reporterNs.report("DataMgr", "../../data/DataManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventManager(extras) {
    _reporterNs.report("EventManager", "../../event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "../../event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfHomeUIEvent(extras) {
    _reporterNs.report("HomeUIEvent", "../../event/HomeUIEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameReviveUI(extras) {
    _reporterNs.report("GameReviveUI", "../../ui/gameui/game/GameReviveUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMBoomUI(extras) {
    _reporterNs.report("MBoomUI", "../../ui/gameui/game/MBoomUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLoadingUI(extras) {
    _reporterNs.report("LoadingUI", "../../ui/gameui/LoadingUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBottomUI(extras) {
    _reporterNs.report("BottomUI", "../../ui/home/<USER>", _context.meta, extras);
  }

  function _reportPossibleCrUseOfHomeUI(extras) {
    _reporterNs.report("HomeUI", "../../ui/home/<USER>", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTopUI(extras) {
    _reporterNs.report("TopUI", "../../ui/home/<USER>", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletSystem(extras) {
    _reporterNs.report("BulletSystem", "../bullet/BulletSystem", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEvent(extras) {
    _reporterNs.report("GameEvent", "../event/GameEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOflcgRand(extras) {
    _reporterNs.report("lcgRand", "../utils/Rand", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameMain(extras) {
    _reporterNs.report("GameMain", "../scenes/GameMain", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRogueUI(extras) {
    _reporterNs.report("RogueUI", "../../ui/gameui/game/RogueUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameStartInfo(extras) {
    _reporterNs.report("GameStartInfo", "./GameStartInfo", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../const/GameEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "../../const/BundleConst", _context.meta, extras);
  }

  _export("BattleManager", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      director = _cc.director;
      Rect = _cc.Rect;
    }, function (_unresolved_2) {
      logWarn = _unresolved_2.logWarn;
    }, function (_long) {
      Long = _long.default;
    }, function (_unresolved_3) {
      GameConst = _unresolved_3.GameConst;
    }, function (_unresolved_4) {
      SingletonBase = _unresolved_4.SingletonBase;
    }, function (_unresolved_5) {
      UIMgr = _unresolved_5.UIMgr;
    }, function (_unresolved_6) {
      MyApp = _unresolved_6.MyApp;
    }, function (_unresolved_7) {
      ModeType = _unresolved_7.ModeType;
    }, function (_unresolved_8) {
      DataMgr = _unresolved_8.DataMgr;
    }, function (_unresolved_9) {
      EventManager = _unresolved_9.default;
      EventMgr = _unresolved_9.EventMgr;
    }, function (_unresolved_10) {
      HomeUIEvent = _unresolved_10.HomeUIEvent;
    }, function (_unresolved_11) {
      GameReviveUI = _unresolved_11.GameReviveUI;
    }, function (_unresolved_12) {
      MBoomUI = _unresolved_12.MBoomUI;
    }, function (_unresolved_13) {
      LoadingUI = _unresolved_13.LoadingUI;
    }, function (_unresolved_14) {
      BottomUI = _unresolved_14.BottomUI;
    }, function (_unresolved_15) {
      HomeUI = _unresolved_15.HomeUI;
    }, function (_unresolved_16) {
      TopUI = _unresolved_16.TopUI;
    }, function (_unresolved_17) {
      BulletSystem = _unresolved_17.BulletSystem;
    }, function (_unresolved_18) {
      GameEvent = _unresolved_18.GameEvent;
    }, function (_unresolved_19) {
      GameIns = _unresolved_19.GameIns;
    }, function (_unresolved_20) {
      lcgRand = _unresolved_20.lcgRand;
    }, function (_unresolved_21) {
      GameMain = _unresolved_21.GameMain;
    }, function (_unresolved_22) {
      RogueUI = _unresolved_22.RogueUI;
    }, function (_unresolved_23) {
      GameEnum = _unresolved_23.GameEnum;
    }, function (_unresolved_24) {
      BundleName = _unresolved_24.BundleName;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "64d867wWTtD/LPANmmLR3y9", "BattleManager", undefined);

      __checkObsolete__(['director', 'Rect']);

      _export("BattleManager", BattleManager = class BattleManager extends (_crd && SingletonBase === void 0 ? (_reportPossibleCrUseOfSingletonBase({
        error: Error()
      }), SingletonBase) : SingletonBase) {
        //小阶段
        get modeConfig() {
          return this._modeConfig;
        }

        constructor() {
          super();
          this.initBattleEnd = false;
          this.isGameStart = false;
          this.animSpeed = 1;
          this.startInfo = null;
          this._modeConfig = null;
          this.curLevel = 0;
          this._loadTotal = 0;
          this._loadCount = 0;
          this._rand = new (_crd && lcgRand === void 0 ? (_reportPossibleCrUseOflcgRand({
            error: Error()
          }), lcgRand) : lcgRand)();
          (_crd && EventManager === void 0 ? (_reportPossibleCrUseOfEventManager({
            error: Error()
          }), EventManager) : EventManager).Instance.on((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
            error: Error()
          }), GameEvent) : GameEvent).onNetGameStart, this.onNetGameStart, this);
          (_crd && EventManager === void 0 ? (_reportPossibleCrUseOfEventManager({
            error: Error()
          }), EventManager) : EventManager).Instance.on((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
            error: Error()
          }), GameEvent) : GameEvent).onNetGameOver, this.onNetGameOver, this);
        } //战斗开始接口


        startGameByMode(startInfo) {
          this.startInfo = startInfo;
          this._rand.seed = (_crd && Long === void 0 ? (_reportPossibleCrUseOfLong({
            error: Error()
          }), Long) : Long).fromNumber(startInfo.randSeed);
          let modeConfig = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbResGameMode.get(startInfo.modeID);

          if (modeConfig == null) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("BattleManager", `can not find mode config by id ${startInfo.modeID}`);
            return;
          }

          this._modeConfig = modeConfig;
          this.curLevel = startInfo.curLevel;
          (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).gameLogic.cmdGameStart(startInfo.modeID);
        }

        async onNetGameStart() {
          await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && LoadingUI === void 0 ? (_reportPossibleCrUseOfLoadingUI({
            error: Error()
          }), LoadingUI) : LoadingUI);
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).emit((_crd && HomeUIEvent === void 0 ? (_reportPossibleCrUseOfHomeUIEvent({
            error: Error()
          }), HomeUIEvent) : HomeUIEvent).Leave);
          await (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadBundle((_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).GameFight);
          await new Promise(resolve => {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gameResManager.preloadCommon(() => {
              resolve();
            }, null);
          });
          console.log("ybgg BattleManager onNetGameStart gameResManager preloadCommon done");
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.setPlaneData(this.startInfo.mainPlane);
          director.loadScene("Game");
        }

        onNetGameOver() {}

        isSectionFinish() {
          // todo
          return (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).waveManager.isAllWaveCompleted;
        }

        clear() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).enemyManager.clear();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).bossManager.clear();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).waveManager.reset();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.clear();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameMapManager.clear();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).hurtEffectManager.clear();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameStateManager.reset();
          (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).destroy();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).rogueManager.clear();
          this.isGameStart = false;
        }

        subReset() {
          this.animSpeed = 1;
          this.isGameStart = false;
          this.initBattleEnd = false;
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.subReset();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameStateManager.reset();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).waveManager.reset();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).enemyManager.subReset();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).bossManager.subReset(); //GameIns.gameMapManager.reset();
        }
        /**
         * 检查所有资源是否加载完成
         */


        async checkLoadFinish() {
          this._loadCount++; // let loadingUI = UIMgr.get(LoadingUI)
          // loadingUI.updateProgress(this._loadCount / this._loadTotal)

          if (this._loadCount >= this._loadTotal) {
            await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).closeUI(_crd && LoadingUI === void 0 ? (_reportPossibleCrUseOfLoadingUI({
              error: Error()
            }), LoadingUI) : LoadingUI);
            (_crd && EventManager === void 0 ? (_reportPossibleCrUseOfEventManager({
              error: Error()
            }), EventManager) : EventManager).Instance.emit((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
              error: Error()
            }), GameEvent) : GameEvent).GameLoadEnd);
            this.initBattle();
          }
        }

        addLoadCount(count) {
          this._loadTotal += count;
        }

        startLoading() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).rogueManager.reset();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.preload();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).hurtEffectManager.preLoad(); //伤害特效资源

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameMapManager.preLoad(this._modeConfig.chapterID); //地图资源

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).enemyManager.preLoad(); //敌人资源

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).bossManager.preLoad(); //boss资源
        }

        initBattle() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainPlane.planeIn();
          (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).init(new Rect(0, 0, (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).ViewBattleWidth, (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).ViewHeight));
        }

        onPlaneIn() {
          let endCallback = () => {
            this.initBattleEnd = true;
            (_crd && EventManager === void 0 ? (_reportPossibleCrUseOfEventManager({
              error: Error()
            }), EventManager) : EventManager).Instance.emit((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
              error: Error()
            }), GameEvent) : GameEvent).GameMainPlaneIn);
          }; // if (this._modeConfig!.rogueFirst > 0){


          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && RogueUI === void 0 ? (_reportPossibleCrUseOfRogueUI({
            error: Error()
          }), RogueUI) : RogueUI, 10001, buffIDs => {
            buffIDs.forEach(buffID => {
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).mainPlaneManager.mainPlane.buffComp.ApplyBuff(false, buffID);
            });
            endCallback();
          }, 3); // }else{
          //     endCallback();
          // }
        }

        beginBattle() {
          if (this.initBattleEnd && !this.isGameStart) {
            this.isGameStart = true;
            (_crd && EventManager === void 0 ? (_reportPossibleCrUseOfEventManager({
              error: Error()
            }), EventManager) : EventManager).Instance.emit((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
              error: Error()
            }), GameEvent) : GameEvent).GameStart);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).waveManager.gameStart();
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gameStateManager.gameStart();
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).mainPlaneManager.mainPlane.begine();
          }
        }
        /**
         * 更新游戏逻辑
         * @param {number} dt 每帧的时间间隔
         */


        updateGameLogic(dt) {
          dt = dt * this.animSpeed;

          if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameStateManager.isGameOver()) {
            if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gamePlaneManager) {
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).gamePlaneManager.enemyTarget = null;
            }

            return;
          }

          if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameStateManager.isInBattle() || (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameStateManager.isGameWillOver()) {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gameDataManager.gameTime += dt;
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gameMapManager.updateGameLogic(dt);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gamePlaneManager.updateGameLogic(dt);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).mainPlaneManager.updateGameLogic(dt);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).waveManager.updateGameLogic(dt);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).enemyManager.updateGameLogic(dt);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).bossManager.updateGameLogic(dt);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gameStateManager.updateGameLogic(dt); //子弹发射器系统

            (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
              error: Error()
            }), BulletSystem) : BulletSystem).tick(dt);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).fColliderManager.updateGameLogic(dt);
          } else if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gamePlaneManager) {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gamePlaneManager.enemyTarget = null;
          }
        }

        setTouchState(isTouch) {
          var _instance;

          if (isTouch) {
            this.beginBattle();
            this.animSpeed = 1;
          } else {
            this.animSpeed = 0.2;
          }

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).enemyManager.setAnimSpeed(this.animSpeed);
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).bossManager.setAnimSpeed(this.animSpeed);
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainPlane.setAnimSpeed(this.animSpeed);
          (_instance = (_crd && GameMain === void 0 ? (_reportPossibleCrUseOfGameMain({
            error: Error()
          }), GameMain) : GameMain).instance) == null || _instance.GameFightUI.setTouchState(isTouch);
        }
        /**
         * 战斗复活逻辑
         */


        relifeBattle() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameStateManager.gameResume();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.revive();
        }

        setGameEnd(isWin) {
          if (isWin) {
            if (this.checkNextlevel()) {
              //判断是否有下一关
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).gameMapManager.switchSectionState((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                error: Error()
              }), GameEnum) : GameEnum).eSECTION_STATE.FINISH);
              (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
                error: Error()
              }), UIMgr) : UIMgr).openUI(_crd && RogueUI === void 0 ? (_reportPossibleCrUseOfRogueUI({
                error: Error()
              }), RogueUI) : RogueUI, buffIDs => {
                buffIDs.forEach(buffID => {
                  (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                    error: Error()
                  }), GameIns) : GameIns).mainPlaneManager.mainPlane.buffComp.ApplyBuff(false, buffID);
                });
                this.startNextBattle();
              });
              return;
            }
          } else {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gameStateManager.gamePause();

            if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).mainPlaneManager.checkCanRevive()) {
              // 判断是否可以复活
              (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
                error: Error()
              }), UIMgr) : UIMgr).openUI(_crd && GameReviveUI === void 0 ? (_reportPossibleCrUseOfGameReviveUI({
                error: Error()
              }), GameReviveUI) : GameReviveUI);
              return;
            }
          }

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainPlane.hpNode.active = false;
          this.endBattle();
          (_crd && GameMain === void 0 ? (_reportPossibleCrUseOfGameMain({
            error: Error()
          }), GameMain) : GameMain).instance.showGameResult(isWin);
          (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).gameLogic.cmdGameEnd((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameDataManager.getGameResultData(), (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameDataManager.getGameLevelResultData());
        }

        checkNextlevel() {
          if (this._modeConfig.modeType == (_crd && ModeType === void 0 ? (_reportPossibleCrUseOfModeType({
            error: Error()
          }), ModeType) : ModeType).ENDLESS) {
            // 如果关卡全部跑完，重新随机一次关卡列表
            if (this.curLevel + 1 >= (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gameMapManager.chapterConfig.levelCount) {
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).gameMapManager.reInitLevelList();
            }

            return true;
          }

          return this.curLevel + 1 <= (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameMapManager.chapterConfig.levelCount;
        }
        /**
         * 继续下一场战斗
         */


        startNextBattle() {
          this.subReset();
          this.curLevel += 1;
          this.initBattle();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameMapManager.switchSectionState((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).eSECTION_STATE.START);
        }
        /**
         * 结束战斗
         */


        endBattle() {
          var _instance2;

          (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).destroy(false, false);
          (_instance2 = (_crd && GameMain === void 0 ? (_reportPossibleCrUseOfGameMain({
            error: Error()
          }), GameMain) : GameMain).instance) == null || _instance2.GameFightUI.reset();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameStateManager.gameOver();
        }

        async quitBattle() {
          this.clear();
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).closeUI(_crd && MBoomUI === void 0 ? (_reportPossibleCrUseOfMBoomUI({
            error: Error()
          }), MBoomUI) : MBoomUI);
          await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && HomeUI === void 0 ? (_reportPossibleCrUseOfHomeUI({
            error: Error()
          }), HomeUI) : HomeUI);
          await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && BottomUI === void 0 ? (_reportPossibleCrUseOfBottomUI({
            error: Error()
          }), BottomUI) : BottomUI);
          await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && TopUI === void 0 ? (_reportPossibleCrUseOfTopUI({
            error: Error()
          }), TopUI) : TopUI);
          director.loadScene("Main");
        }

        bossChangeFinish(tip) {// const bossEnterDialog = GameIns.uiManager.getDialog(BossEnterDialog.default);
          // if (bossEnterDialog) {
          //     bossEnterDialog.node.active = true;
          //     GameIns.mainPlaneManager.moveAble = false;
          //     bossEnterDialog.showTips(bossName);
          // }
        }

        bossWillEnter() {// GameIns.mainPlaneManager.mainPlane!.setFireEnable(false);
          // GameIns.mainPlaneManager.moveAble = false;
        }
        /**
         * 开始Boss战斗
         */


        bossFightStart() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainPlane.setFireEnable(true);
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainPlane.setMoveAble(true);
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).bossManager.bossFightStart();
        }

        random() {
          return this._rand.random();
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c5d8871e59eaec0a77652d199c7da288c1619860.js.map
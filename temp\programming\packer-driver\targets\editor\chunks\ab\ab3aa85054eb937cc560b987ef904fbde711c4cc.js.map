{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/game/RogueUI.ts"], "names": ["_decorator", "color", "find", "instantiate", "Label", "Node", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "BundleName", "ButtonPlus", "WordType", "GameIns", "MyApp", "getI18StrByKey", "StringUtils", "TYPE_COLOR", "None", "Prop", "Missile", "Laser", "ccclass", "property", "RogueUI", "_callFunc", "_groupId", "_maxSelectCount", "_selectedIds", "_curRefreshTimes", "_maxRefreshTimes", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getBundleName", "GameFight", "onLoad", "nodeFresh", "getComponentInChildren", "addClick", "onFreshClick", "itemProgress", "active", "onShow", "groupId", "callFunc", "maxSelectCount", "refreshRogueUI", "refreshSelectUI", "refreshProgressUI", "refreshTimesUI", "onHide", "args", "unscheduleAllCallbacks", "onClose", "rogueManager", "recycleRogueItems", "NodeSelect", "closeUI", "coverToBuffIds", "list", "randomWords", "len", "length", "i", "wordGroup", "item", "rogueSelectNodes", "wordConfig", "lubanTables", "TbResWord", "get", "wordId", "NodeItem", "setRogueItem", "rogueDesc", "rogueType", "getComponent", "string", "desc", "type", "selectRogue", "push", "ID", "scheduleOnce", "data", "getSeLectedHistory", "for<PERSON>ach", "value", "key", "config", "TbResWordGroup", "NodeProgress", "children", "curSelectCount", "name", "setPosition", "position", "x", "<PERSON><PERSON><PERSON><PERSON>", "progressBg", "progressBg2", "complete", "LabelIndex", "String", "freshTimes", "getReplaceStr"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;;AAC7CC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;AACeC,MAAAA,Q,iBAAAA,Q;;AACfC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,c,iBAAAA,c;;AACFC,MAAAA,W;;;;;;;;;AAEDC,MAAAA,U,GAAa;AACf,SAAC;AAAA;AAAA,kCAASC,IAAV,GAAiB,SADF;AAEf,SAAC;AAAA;AAAA,kCAASC,IAAV,GAAiB,SAFF;AAGf,SAAC;AAAA;AAAA,kCAASC,OAAV,GAAoB,SAHL;AAIf,SAAC;AAAA;AAAA,kCAASC,KAAV,GAAkB;AAJH,O;OAOb;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBtB,U;;yBAGjBuB,O,WADZF,OAAO,CAAC,SAAD,C,UAGHC,QAAQ,CAACjB,IAAD,C,UAGRiB,QAAQ,CAAC,CAACjB,IAAD,CAAD,C,UAERiB,QAAQ,CAAClB,KAAD,C,UAGRkB,QAAQ,CAACjB,IAAD,C,UAGRiB,QAAQ,CAACjB,IAAD,C,UAERiB,QAAQ,CAACjB,IAAD,C,2BAhBb,MACakB,OADb;AAAA;AAAA,4BACoC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAkBhCC,SAlBgC,GAkBH,IAlBG;AAAA,eAmBhCC,QAnBgC,GAmBb,CAnBa;AAAA,eAoBhCC,eApBgC,GAoBN,CApBM;AAAA,eAqBhCC,YArBgC,GAqBP,EArBO;AAAA,eAsBhCC,gBAtBgC,GAsBL,CAtBK;AAAA,eAuBhCC,gBAvBgC,GAuBL,CAvBK;AAAA;;AAyBZ,eAANC,MAAM,GAAW;AAAE,iBAAO,gBAAP;AAA0B;;AACrC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAwB;;AACjC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,SAAlB;AAA6B;;AAC3DC,QAAAA,MAAM,GAAS;AACrB,eAAKC,SAAL,CAAgBC,sBAAhB;AAAA;AAAA,wCAAoDC,QAApD,CAA6D,KAAKC,YAAlE,EAAgF,IAAhF;AACA,eAAKC,YAAL,CAAmBC,MAAnB,GAA4B,KAA5B;AACH;;AACW,cAANC,MAAM,CAACC,OAAD,EAAkBC,QAAyB,GAAG,IAA9C,EAAoDC,cAAsB,GAAG,CAA7E,EAA+F;AACvG,eAAKpB,QAAL,GAAgBkB,OAAhB;AACA,eAAKjB,eAAL,GAAuBmB,cAAvB;AACA,eAAKrB,SAAL,GAAiBoB,QAAjB;AACA,eAAKjB,YAAL,GAAoB,EAApB;AACA,eAAKE,gBAAL,GAAwB,CAAxB;AACA,eAAKiB,cAAL;AACA,eAAKC,eAAL;AACA,eAAKC,iBAAL;AACA,eAAKC,cAAL;AACH;;AACW,cAANC,MAAM,CAAC,GAAGC,IAAJ,EAAgC;AACxC,eAAKC,sBAAL;AACF;;AACW,cAAPC,OAAO,CAAC,GAAGF,IAAJ,EAAgC;AACzC;AAAA;AAAA,kCAAQG,YAAR,CAAqBC,iBAArB,CAAuC,KAAKC,UAA5C;AACH;;AAEY,cAAPC,OAAO,GAAG;AAAA;;AACZ;AAAA;AAAA,8BAAMA,OAAN,CAAclC,OAAd;AACA,kCAAKC,SAAL,wCAAiB;AAAA;AAAA,kCAAQ8B,YAAR,CAAqBI,cAArB,CAAoC,KAAK/B,YAAzC,CAAjB;AACH;;AAEDmB,QAAAA,cAAc,GAAG;AACb,cAAIa,IAAqB,GAAG;AAAA;AAAA,kCAAQL,YAAR,CAAqBM,WAArB,CAAiC,KAAKnC,QAAtC,CAA5B;AACA,cAAIoC,GAAG,GAAGF,IAAI,CAACG,MAAf;;AACA,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,GAApB,EAAyBE,CAAC,EAA1B,EAA8B;AAC1B,gBAAIC,SAAS,GAAGL,IAAI,CAACI,CAAD,CAApB;AACA,gBAAIE,IAAI,GAAG,KAAKC,gBAAL,CAAsBH,CAAtB,CAAX;AACAE,YAAAA,IAAI,CAACxB,MAAL,GAAcuB,SAAS,IAAI,IAA3B;;AAEA,gBAAI,CAACC,IAAI,CAACxB,MAAV,EAAkB;AACd;AACH;;AACD,gBAAI0B,UAAU,GAAG;AAAA;AAAA,gCAAMC,WAAN,CAAkBC,SAAlB,CAA4BC,GAA5B,CAAgCN,SAAS,CAACO,MAA1C,CAAjB;AAEA,gBAAIC,QAAQ,GAAGtE,IAAI,CAAC,UAAD,EAAa+D,IAAb,CAAnB;AACA;AAAA;AAAA,oCAAQX,YAAR,CAAqBC,iBAArB,CAAuCiB,QAAvC;AACA;AAAA;AAAA,oCAAQlB,YAAR,CAAqBmB,YAArB,CAAkCD,QAAlC,EAA4CR,SAAS,CAACO,MAAtD;AAEA,gBAAIG,SAAS,GAAGxE,IAAI,CAAC,WAAD,EAAc+D,IAAd,CAApB;AACA,gBAAIU,SAAS,GAAGzE,IAAI,CAAC,WAAD,EAAc+D,IAAd,CAApB;AAEAS,YAAAA,SAAS,CAAEE,YAAX,CAAwBxE,KAAxB,EAAgCyE,MAAhC,GAAyCV,UAAU,CAAEW,IAArD;AACAJ,YAAAA,SAAS,CAAEE,YAAX,CAAwBxE,KAAxB,EAAgCH,KAAhC,GAAwCA,KAAK,CAACe,UAAU,CAACgD,SAAS,CAACe,IAAX,CAAV,IAA8B,EAA/B,CAA7C;AAEAd,YAAAA,IAAI,CAACW,YAAL;AAAA;AAAA,0CAA+BtC,QAA/B,CAAwC,MAAM;AAC1C,mBAAK0C,WAAL,CAAiBhB,SAAjB;AACH,aAFD,EAEG,IAFH;AAGH;AACJ;;AAEDzB,QAAAA,YAAY,GAAG;AACX,cAAI,KAAKZ,YAAL,CAAkBmC,MAAlB,IAA4B,KAAKpC,eAArC,EAAsD;AAClD;AACH;;AACD,cAAI,KAAKE,gBAAL,IAAyB,KAAKC,gBAAlC,EAAoD;AAChD;AACH;;AACD,eAAKD,gBAAL;AACA,eAAKkB,cAAL;AACA,eAAKG,cAAL;AACH;;AAED+B,QAAAA,WAAW,CAAChB,SAAD,EAA2B;AAClC,cAAI,KAAKrC,YAAL,CAAkBmC,MAAlB,IAA4B,KAAKpC,eAArC,EAAsD;AAClD;AACH;;AACD;AAAA;AAAA,kCAAQ4B,YAAR,CAAqB0B,WAArB,CAAiC,CAAChB,SAAD,CAAjC;;AACA,eAAKrC,YAAL,CAAkBsD,IAAlB,CAAuBjB,SAAS,CAACkB,EAAjC;;AAEA,eAAKnC,eAAL;AACA,eAAKC,iBAAL;AACA,eAAKC,cAAL;;AAEA,cAAI,KAAKtB,YAAL,CAAkBmC,MAAlB,IAA4B,KAAKpC,eAArC,EAAsD;AAClD,iBAAKyD,YAAL,CAAkB,MAAM;AACpB,mBAAK1B,OAAL;AACH,aAFD,EAEG,GAFH;AAGH,WAJD,MAIK;AACD,iBAAKX,cAAL;AACH;AACJ;;AAEDC,QAAAA,eAAe,GAAG;AACd,cAAIqC,IAAI,GAAG;AAAA;AAAA,kCAAQ9B,YAAR,CAAqB+B,kBAArB,EAAX;AACA;AAAA;AAAA,kCAAQ/B,YAAR,CAAqBC,iBAArB,CAAuC,KAAKC,UAA5C;AACA4B,UAAAA,IAAI,CAACE,OAAL,CAAa,CAACC,KAAD,EAAQC,GAAR,KAAgB;AACzB,gBAAIC,MAAM,GAAG;AAAA;AAAA,gCAAMrB,WAAN,CAAkBsB,cAAlB,CAAiCpB,GAAjC,CAAqCkB,GAArC,CAAb;AACA;AAAA;AAAA,oCAAQlC,YAAR,CAAqBmB,YAArB,CAAkC,KAAKjB,UAAvC,EAAoDiC,MAAM,CAAElB,MAA5D,EAAoEgB,KAApE;AACH,WAHD;AAIH;;AACDvC,QAAAA,iBAAiB,GAAG;AAChB,eAAK2C,YAAL,CAAmBC,QAAnB,CAA4BN,OAA5B,CAAoCrB,IAAI,IAAI;AACxCA,YAAAA,IAAI,CAACxB,MAAL,GAAc,KAAd;AACH,WAFD;AAGA,cAAIoD,cAAc,GAAG,KAAKlE,YAAL,CAAkBmC,MAAvC;;AACA,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAI,KAAKrC,eAA1B,EAA2CqC,CAAC,EAA5C,EAAgD;AAC5C,gBAAIE,IAAI,GAAG/D,IAAI,CAAE,OAAM6D,CAAE,EAAV,EAAa,KAAK4B,YAAlB,CAAf;;AACA,gBAAI,CAAC1B,IAAL,EAAW;AACPA,cAAAA,IAAI,GAAG9D,WAAW,CAAC,KAAKqC,YAAN,CAAlB;AACAyB,cAAAA,IAAI,CAAC6B,IAAL,GAAa,OAAM/B,CAAE,EAArB;AACAE,cAAAA,IAAI,CAAC8B,WAAL,CAAiB9B,IAAI,CAAC+B,QAAL,CAAcC,CAA/B,EAAkC,CAAlC;AACA,mBAAKN,YAAL,CAAmBO,QAAnB,CAA4BjC,IAA5B;AACH;;AACDA,YAAAA,IAAI,CAACxB,MAAL,GAAc,IAAd;AACA,gBAAI0D,UAAU,GAAGjG,IAAI,CAAC,YAAD,EAAe+D,IAAf,CAArB;AACA,gBAAImC,WAAW,GAAGlG,IAAI,CAAC,aAAD,EAAgB+D,IAAhB,CAAtB;AACA,gBAAIoC,QAAQ,GAAGnG,IAAI,CAAC,UAAD,EAAa+D,IAAb,CAAnB;AACA,gBAAIqC,UAAU,GAAGpG,IAAI,CAAC,YAAD,EAAe+D,IAAf,CAArB;AACAkC,YAAAA,UAAU,CAAE1D,MAAZ,GAAqBsB,CAAC,GAAG,KAAKrC,eAA9B;AACA0E,YAAAA,WAAW,CAAE3D,MAAb,GAAsBsB,CAAC,GAAG,KAAKrC,eAAT,IAA4BqC,CAAC,IAAI8B,cAAvD;AACAQ,YAAAA,QAAQ,CAAE5D,MAAV,GAAmBsB,CAAC,IAAI8B,cAAxB;AACAS,YAAAA,UAAU,CAAE1B,YAAZ,CAAyBxE,KAAzB,EAAiCyE,MAAjC,GAA0C0B,MAAM,CAACxC,CAAD,CAAhD;AACH;AACJ;;AAEDd,QAAAA,cAAc,GAAG;AACb,eAAKuD,UAAL,CAAiB3B,MAAjB,GAA0B;AAAA;AAAA,0CAAY4B,aAAZ,CAA0B;AAAA;AAAA,gDAAe,kBAAf,CAA1B,EAA8D,CAAC,KAAK5E,gBAAL,GAAwB,KAAKD,gBAA9B,EAAgD,KAAKC,gBAArD,CAA9D,CAA1B;AACH;;AAvJ+B,O;;;;;iBAGP,I;;;;;;;iBAGE,E;;;;;;;iBAEA,I;;;;;;;iBAGD,I;;;;;;;iBAGE,I;;;;;;;iBAEA,I", "sourcesContent": ["import { _decorator, color, find, instantiate, Label, Node } from 'cc';\r\nimport { BaseUI, UILayer, UIMgr } from 'db://assets/scripts/core/base/UIMgr';\r\nimport { BundleName } from '../../../const/BundleConst';\r\nimport { ButtonPlus } from '../../common/components/button/ButtonPlus';\r\nimport { ResWorldGroup, WordType } from '../../../autogen/luban/schema';\r\nimport { GameIns } from '../../../game/GameIns';\r\nimport { MyApp } from '../../../app/MyApp';\r\nimport { getI18StrByKey } from '../../../../../../../extensions/i18n/assets/LanguageData';\r\nimport StringUtils from '../../../../../../scripts/utils/StringUtils';\r\n\r\nconst TYPE_COLOR = {\r\n    [WordType.None]: \"#FFFB80\",\r\n    [WordType.Prop]: \"#FFFB80\",\r\n    [WordType.Missile]: \"#80ff86\",\r\n    [WordType.Laser]: \"#59ffdb\",\r\n}\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass(\"RogueUI\")\r\nexport class RogueUI extends BaseUI {\r\n\r\n    @property(Node)\r\n    nodeFresh: Node | null = null;\r\n\r\n    @property([Node])\r\n    rogueSelectNodes: Node[] = [];\r\n    @property(Label)\r\n    freshTimes: Label | null = null;\r\n\r\n    @property(Node)\r\n    NodeSelect: Node | null = null;\r\n\r\n    @property(Node)\r\n    itemProgress: Node | null = null;\r\n    @property(Node)\r\n    NodeProgress: Node | null = null;\r\n\r\n    _callFunc: Function | null = null;\r\n    _groupId: number = 0;\r\n    _maxSelectCount: number = 0;\r\n    _selectedIds: number[] = [];\r\n    _curRefreshTimes: number = 0;\r\n    _maxRefreshTimes: number = 0;\r\n\r\n    public static getUrl(): string { return \"prefab/RogueUI\"; };\r\n    public static getLayer(): UILayer { return UILayer.Default }\r\n    public static getBundleName(): string { return BundleName.GameFight }\r\n    protected onLoad(): void {\r\n        this.nodeFresh!.getComponentInChildren(ButtonPlus)!.addClick(this.onFreshClick, this);\r\n        this.itemProgress!.active = false;\r\n    }\r\n    async onShow(groupId: number, callFunc: Function | null = null, maxSelectCount: number = 1): Promise<void> {\r\n        this._groupId = groupId;\r\n        this._maxSelectCount = maxSelectCount;\r\n        this._callFunc = callFunc;\r\n        this._selectedIds = [];\r\n        this._maxRefreshTimes = 2;\r\n        this.refreshRogueUI();\r\n        this.refreshSelectUI();\r\n        this.refreshProgressUI();\r\n        this.refreshTimesUI();\r\n    }\r\n    async onHide(...args: any[]): Promise<void> {\r\n        this.unscheduleAllCallbacks();\r\n     }\r\n    async onClose(...args: any[]): Promise<void> {\r\n        GameIns.rogueManager.recycleRogueItems(this.NodeSelect!);\r\n    }\r\n\r\n    async closeUI() {\r\n        UIMgr.closeUI(RogueUI)\r\n        this._callFunc?.(GameIns.rogueManager.coverToBuffIds(this._selectedIds));\r\n    }\r\n\r\n    refreshRogueUI() {\r\n        let list: ResWorldGroup[] = GameIns.rogueManager.randomWords(this._groupId);\r\n        let len = list.length;\r\n        for (let i = 0; i < len; i++) {\r\n            let wordGroup = list[i];\r\n            let item = this.rogueSelectNodes[i];\r\n            item.active = wordGroup != null;\r\n\r\n            if (!item.active) {\r\n                continue;\r\n            }\r\n            let wordConfig = MyApp.lubanTables.TbResWord.get(wordGroup.wordId)\r\n\r\n            let NodeItem = find(\"NodeItem\", item)!;\r\n            GameIns.rogueManager.recycleRogueItems(NodeItem);\r\n            GameIns.rogueManager.setRogueItem(NodeItem, wordGroup.wordId);\r\n\r\n            let rogueDesc = find(\"rogueDesc\", item);\r\n            let rogueType = find(\"rogueType\", item);\r\n\r\n            rogueDesc!.getComponent(Label)!.string = wordConfig!.desc;\r\n            rogueDesc!.getComponent(Label)!.color = color(TYPE_COLOR[wordGroup.type] || \"\")\r\n\r\n            item.getComponent(ButtonPlus)!.addClick(() => {\r\n                this.selectRogue(wordGroup)\r\n            }, this)\r\n        }\r\n    }\r\n\r\n    onFreshClick() {\r\n        if (this._selectedIds.length >= this._maxSelectCount) {\r\n            return;\r\n        }\r\n        if (this._curRefreshTimes >= this._maxRefreshTimes) {\r\n            return;\r\n        }\r\n        this._curRefreshTimes++;\r\n        this.refreshRogueUI();\r\n        this.refreshTimesUI();\r\n    }\r\n\r\n    selectRogue(wordGroup: ResWorldGroup) {\r\n        if (this._selectedIds.length >= this._maxSelectCount) {\r\n            return;\r\n        }\r\n        GameIns.rogueManager.selectRogue([wordGroup]);\r\n        this._selectedIds.push(wordGroup.ID);\r\n\r\n        this.refreshSelectUI();\r\n        this.refreshProgressUI();\r\n        this.refreshTimesUI();\r\n\r\n        if (this._selectedIds.length >= this._maxSelectCount) {\r\n            this.scheduleOnce(() => {\r\n                this.closeUI();\r\n            }, 0.5)\r\n        }else{\r\n            this.refreshRogueUI();\r\n        }\r\n    }\r\n\r\n    refreshSelectUI() {\r\n        let data = GameIns.rogueManager.getSeLectedHistory();\r\n        GameIns.rogueManager.recycleRogueItems(this.NodeSelect!);\r\n        data.forEach((value, key) => {\r\n            let config = MyApp.lubanTables.TbResWordGroup.get(key);\r\n            GameIns.rogueManager.setRogueItem(this.NodeSelect!, config!.wordId, value);\r\n        })\r\n    }\r\n    refreshProgressUI() {\r\n        this.NodeProgress!.children.forEach(item => {\r\n            item.active = false;\r\n        })\r\n        let curSelectCount = this._selectedIds.length;\r\n        for (let i = 1; i <= this._maxSelectCount; i++) {\r\n            let item = find(`item${i}`, this.NodeProgress!);\r\n            if (!item) {\r\n                item = instantiate(this.itemProgress!);\r\n                item.name = `item${i}`;\r\n                item.setPosition(item.position.x, 0);\r\n                this.NodeProgress!.addChild(item);\r\n            }\r\n            item.active = true;\r\n            let progressBg = find(\"progressBg\", item);\r\n            let progressBg2 = find(\"progressBg2\", item);\r\n            let complete = find(\"complete\", item);\r\n            let LabelIndex = find(\"LabelIndex\", item);\r\n            progressBg!.active = i < this._maxSelectCount;\r\n            progressBg2!.active = i < this._maxSelectCount && i <= curSelectCount;\r\n            complete!.active = i <= curSelectCount;\r\n            LabelIndex!.getComponent(Label)!.string = String(i);\r\n        }\r\n    }\r\n\r\n    refreshTimesUI() {\r\n        this.freshTimes!.string = StringUtils.getReplaceStr(getI18StrByKey(\"ROUGE_LEFT_TIMES\"), [this._maxRefreshTimes - this._curRefreshTimes, this._maxRefreshTimes]);\r\n    }\r\n}\r\n"]}
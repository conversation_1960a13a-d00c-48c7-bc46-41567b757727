import { _decorator, Component, Node, Prefab, Enum, Rect, JsonAsset } from 'cc';
import { EnemyEnum } from '../enum-gen/EnemyEnum';
import PlaneBase from 'db://assets/bundles/common/script/game/ui/plane/PlaneBase';
import EnemyPlane from 'db://assets/bundles/common/script/game/ui/plane/enemy/EnemyPlane';
import { EnemyData } from 'db://assets/bundles/common/script/game/data/EnemyData';
import { MyApp } from 'db://assets/bundles/common/script/app/MyApp';
import { PathData } from 'db://assets/bundles/common/script/game/data/PathData';
import { BundleName } from 'db://assets/bundles/common/script/const/BundleConst';
import { BulletSystem } from 'db://assets/bundles/common/script/game/bullet/BulletSystem';
import { ResManager } from 'db://assets/scripts/core/base/ResManager';
import { GameConst } from 'db://assets/scripts/core/base/GameConst'
const { ccclass, property, executeInEditMode } = _decorator;

@ccclass('PlaneEditor')
// @executeInEditMode(true)
export class PlaneEditor extends Component {
    @property({type: Enum(EnemyEnum), displayName: "敌机ID"})
    public enemyPlaneID: number = 0;

    @property({type: JsonAsset, displayName: "路径"})
    public pathAsset: JsonAsset|null = null;

    @property({type: EnemyPlane, displayName: "敌机节点"})
    public enemyPlane: EnemyPlane|null = null;

    @property({type: PlaneBase, displayName: "玩家飞机"})
    public playerPlane: PlaneBase|null = null;

    private _pathData: PathData|null = null;
    private _planeList: PlaneBase[] = [];

    onLoad() {
        MyApp.GetInstance().init();
        if (this.pathAsset) {
            this._pathData = PathData.fromJSON(this.pathAsset.json);
        }
        this.init();
    }

    async init() {
        await MyApp.planeMgr.load();
        await ResManager.instance.loadBundle(BundleName.Luban) //优先加载完配置
        await MyApp.lubanMgr.load();
        
        BulletSystem.init(new Rect(0, 0, GameConst.ViewBattleWidth, GameConst.ViewHeight));
        this.createEnemyPlane(this.enemyPlaneID).then((plane) => {
            this.onEnemyCreated(plane);
        }).catch((error) => {
            console.error("createEnemyPlane error: ", error);
        });

    }

    update(deltaTime: number) {
        BulletSystem.tick(deltaTime);
        
        this._planeList.forEach((plane) => {
            plane.updateGameLogic(deltaTime);
        });
    }

    private async createEnemyPlane(id: number): Promise<EnemyPlane> {
        return new Promise(async (resolve, reject) => {
            let enemyData = new EnemyData(id);
            const prefab = await MyApp.resMgr.loadAsync(enemyData.recoursePrefab, Prefab);
            if (!prefab) {
                reject("Failed to load prefab");
                return;
            }
            
            if (!this.enemyPlane) {
                reject("EnemyPlane component not found on node");
                return;
            }
            this.enemyPlane.initPlane(enemyData, prefab);
            resolve(this.enemyPlane);
        });
    }

    private onEnemyCreated(plane: EnemyPlane) {
        plane.initMove(0, -200, -90);
        plane.moveCom!.speed = 1;
        if (this._pathData) {
            plane.initPath(0, 0, this._pathData);
        }

        this._planeList.push(plane);
    }
}

import { _decorator, Component, Node, Prefab, Enum, JsonAsset } from 'cc';
import { EnemyEnum } from '../enum-gen/EnemyEnum';
import PlaneBase from 'db://assets/bundles/common/script/game/ui/plane/PlaneBase';
import EnemyPlane from 'db://assets/bundles/common/script/game/ui/plane/enemy/EnemyPlane';
import { EnemyData } from 'db://assets/bundles/common/script/game/data/EnemyData';
import { MyApp } from 'db://assets/bundles/common/script/app/MyApp';
const { ccclass, property, executeInEditMode } = _decorator;

@ccclass('PlaneEditor')
// @executeInEditMode(true)
export class PlaneEditor extends Component {
    @property({type: Enum(EnemyEnum), displayName: "敌机ID"})
    public enemyPlaneID: number = 0;

    @property({type: JsonAsset, displayName: "路径"})
    public pathAsset: JsonAsset|null = null;

    @property({type: PlaneBase, displayName: "玩家飞机"})
    public playerPlane: PlaneBase|null = null;

    private _enemyPlane: EnemyPlane|null = null;

    onLoad() {
        MyApp.GetInstance().init();
        this.createEnemyPlane(this.enemyPlaneID);
    }

    update(deltaTime: number) {
        if (this._enemyPlane) {
            this._enemyPlane.updateGameLogic(deltaTime);
        }
    }

    private async createEnemyPlane(id: number) {
        let enemyData = new EnemyData(id);
        const prefab = await MyApp.resMgr.loadAsync(enemyData.recoursePrefab, Prefab);
        this._enemyPlane = this.node.getComponent(EnemyPlane);
        if (!this._enemyPlane) {
            console.log("no plane"); 
            return;
        }

        this._enemyPlane.initPlane(enemyData, prefab);
    }
}

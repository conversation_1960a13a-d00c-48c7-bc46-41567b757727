
import FColliderManager from "./collider-system/FColliderManager";
import { GameIns } from "./GameIns";
import { BattleManager } from "./manager/BattleManager";
import { BossManager } from "./manager/BossManager";
import { EnemyManager } from "./manager/EnemyManager";
import { GameDataManager } from "./manager/GameDataManager";
import { GamePlaneManager } from "./manager/GamePlaneManager";
import {GameStartInfo, GameStartInfoMainPlane } from "./manager/GameStartInfo";
import { GameStateManager } from "./manager/GameStateManager";
import { HurtEffectManager } from "./manager/HurtEffectManager";
import { MainPlaneManager } from "./manager/MainPlaneManager";
import { RogueManager } from "./manager/RogueManager";
import WaveManager from "./manager/WaveManager";
import { GameResManager } from "./manager/GameResManager";
import { DataMgr } from "../data/DataManager";

let initGameIns = function() {
    GameIns._battleManager = BattleManager.getInstance<BattleManager>(BattleManager);
    GameIns._bossManager = BossManager.getInstance<BossManager>(BossManager);
    GameIns._enemyManager = EnemyManager.getInstance<EnemyManager>(EnemyManager);
    GameIns._gameStateManager = GameStateManager.getInstance<GameStateManager>(GameStateManager);
    GameIns._hurtEffectManager = HurtEffectManager.getInstance<HurtEffectManager>(HurtEffectManager);
    GameIns._mainPlaneManager = new MainPlaneManager();
    GameIns._gamePlaneManager = GamePlaneManager.getInstance<GamePlaneManager>(GamePlaneManager);
    GameIns._waveManager = WaveManager.getInstance<WaveManager>(WaveManager);
    GameIns._gameDataManager = GameDataManager.getInstance<GameDataManager>(GameDataManager);
    GameIns._rogueManager = RogueManager.getInstance<RogueManager>(RogueManager);
    GameIns._fColliderManager = FColliderManager.instance;
    GameIns._gameResManager = GameResManager.getInstance<GameResManager>(GameResManager);
}

//战斗开始接口
export function startGameByMode(modeID: number, curLevel: number = 1,randSeed:number = Date.now()) {
    initGameIns();
    const mainPlane:GameStartInfoMainPlane = {
        ID: DataMgr.planeInfo.curPlaneId,
    }
    const startInfo:GameStartInfo = {
        modeID:modeID, 
        curLevel:curLevel,
        randSeed: randSeed,
        mainPlane:mainPlane,
    }
    GameIns.battleManager.startGameByMode(startInfo);
}
    
import { _decorator, Node, Prefab, sp, UITransform } from 'cc';
import { BaseUI, UILayer, UIOpt } from "db://assets/scripts/core/base/UIMgr";
import { MyApp } from '../../app/MyApp';
import { BundleName } from '../../const/BundleConst';
import { DataMgr } from '../../data/DataManager';
import { MainPlaneData } from '../../data/plane/MainPlaneData';

const { ccclass, property } = _decorator;

@ccclass('PlaneShowUI')
export class PlaneShowUI extends BaseUI {

    @property(Node)
    planeCon: Node | null = null;

    public static getUrl(): string { return "prefab/ui/PlaneShowUI"; }
    public static getLayer(): UILayer { return UILayer.Default }
    public static getBundleName(): string { return BundleName.Home }
    public static getUIOption(): UIOpt {
        return { isClickBgCloseUI: false }
    }

    protected async onLoad(): Promise<void> {
        let planeData = new MainPlaneData(DataMgr.planeInfo.curPlaneId)
        const prefab = await MyApp.resMgr.loadAsync(planeData.recoursePrefab, Prefab);
        let planeNode = MyApp.planeMgr.getPlane(planeData, prefab);
        this.planeCon!.addChild(planeNode);
        const spine = planeNode.getComponentInChildren(sp.Skeleton);
        spine?.setAnimation(0, "Idle", true)
    }

    async onShow(): Promise<void> {

    }
    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {
    }

}

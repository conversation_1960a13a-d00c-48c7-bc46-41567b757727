import { MyApp } from "../../app/MyApp";
import { AttributeConst } from "db://assets/bundles/common/script/const/AttributeConst";
import { DamageType } from "../../autogen/luban/schema";
import type PlaneBase from "../../game/ui/plane/PlaneBase";

export class AttributeData {
    private _baseAttributes: Map<AttributeConst, number> = new Map(); // 基础属性
    private _Attributes: Map<AttributeConst, number> = new Map(); // 缓存最终属性值
    private _AttributeModifies: Map<AttributeConst, Map<number, number>> = new Map();
    private _sources: Map<number,  AttributeConst[]> = new Map()// 属性来源

    constructor() {
    }

    addBaseAttribute(key: AttributeConst, value: number) {
        this._baseAttributes.set(key, this._baseAttributes.get(key)||0 + value);
        this.recalculateAttributes(key)
    }

    // 添加属性来源
    addModify(id: number, key:AttributeConst, value: number) {
        let modify = this._AttributeModifies.get(key);
        if (!modify) {
            modify = new Map()
            this._AttributeModifies.set(id, modify)
        }
        modify.set(id, value)

        let source = this._sources.get(key);
        if (!source) {
            source = []
            this._sources.set(key, source);
        }
        source.push(id);

        this.recalculateAttributes(key);
    }

    // 移除属性来源
    removeModify(id: number) {
        let source = this._sources.get(id)
        source?.forEach((key) => {
            let modify = this._AttributeModifies.get(key);
            if (modify) {
                modify.delete(id);
            }
            this.recalculateAttributes(key);
        });
        this._sources.delete(id);
    }

    // 重新计算属性
    private recalculateAttributes(key:AttributeConst) {
        let finalValue = this._baseAttributes.get(key) || 0;

        // 遍历所有属性来源
        this._AttributeModifies.forEach(source => {
            source.forEach(value => {
                finalValue += value;
            });
        });

        // 计算最终属性值: (基础值 + 加法值) * (1 + 百分比值/10000)
        this._Attributes.set(key, finalValue);
    }

    getFinalAttributeByKey(key:AttributeConst):number {
        return this._Attributes.get(key) || 0
    }

    getFinialAttributeByOutInKey(outAddKey:AttributeConst, outPreKey:AttributeConst, 
        inAddKey:AttributeConst, inPerKey:AttributeConst): number {

        return (this.getFinalAttributeByKey(outAddKey) * (1 + this.getFinalAttributeByKey(outPreKey) / 10000) 
            + this.getFinalAttributeByKey(inAddKey)) * (1 + this.getFinalAttributeByKey(inPerKey) / 10000)
    }

    static CalcBulletDamage(attacker: PlaneBase, defender: PlaneBase, 
            hurtRate: number, isNuclear: boolean, damageType: DamageType, isBoss: boolean, attackPowerFix:number): number {

        const attackerAttr = attacker.attribute;
        const defenderAttr = defender.attribute;
        // ①局外属性面板上显示的攻击力值=(攻击力局外绝对值1+攻击力局外绝对值2+…)×(1+攻击力局外百分比1+攻击力局外百分比2+…)
        // ②攻击力局内总值=(①局外属性面板上显示的攻击力值+攻击力局内绝对值1+攻击力局内绝对值2+⋯)×(1+攻击力局内百分比1+攻击力局内百分比2+⋯)
        let attack = attackerAttr.getAttack();

        // ③子弹or核弹的局内攻击力=[(②攻击力局内总值×攻击转换系数+子弹攻击局外绝对值1+子弹攻击局外绝对值2+⋯)×(1+子弹攻击局外百分比1+子弹攻击局外百分比2+⋯)+子弹攻击局内绝对值1+子弹攻击局内绝对值2+⋯]×(1+子弹攻击局内百分比1+子弹攻击局内百分比2+⋯)
        if (!isNuclear) {
            let damageAttackOutAddKey = 0;;
            let damageAttackOutPerKey = 0;;
            let damageAttackInAddKey  = 0;;
            let damageAttackInPerKey  = 0;;
            switch(damageType) {
                case DamageType.EXPLOSIVE:
                    damageAttackOutAddKey = AttributeConst.ExplosiveBulletAttackInAdd
                    damageAttackOutPerKey = AttributeConst.ExplosiveBulletAttackInPer
                    damageAttackInAddKey  = AttributeConst.ExplosiveBulletAttackOutAdd
                    damageAttackInPerKey  = AttributeConst.ExplosiveBulletAttackOutPer
                    break;
                case DamageType.NORMAL:
                    damageAttackOutAddKey = AttributeConst.NormalBulletAttackInAdd
                    damageAttackOutPerKey = AttributeConst.NormalBulletAttackInPer
                    damageAttackInAddKey  = AttributeConst.NormalBulletAttackOutAdd
                    damageAttackInPerKey  = AttributeConst.NormalBulletAttackOutPer
                    break;
                case DamageType.ENERGETIC:
                    damageAttackOutAddKey = AttributeConst.EnergeticBulletAttackInAdd
                    damageAttackOutPerKey = AttributeConst.EnergeticBulletAttackInPer
                    damageAttackInAddKey  = AttributeConst.EnergeticBulletAttackOutAdd
                    damageAttackInPerKey  = AttributeConst.EnergeticBulletAttackOutPer
                    break;
                case DamageType.PHYSICAL:
                    damageAttackOutAddKey = AttributeConst.PhysicsBulletAttackInAdd
                    damageAttackOutPerKey = AttributeConst.PhysicsBulletAttackInPer
                    damageAttackInAddKey  = AttributeConst.PhysicsBulletAttackOutAdd
                    damageAttackInPerKey  = AttributeConst.PhysicsBulletAttackOutPer
                    break;
            }
            if (damageType != DamageType.ALL) {
                attack = (((attack * hurtRate + attackerAttr.getFinalAttributeByKey(AttributeConst.BulletAttackOutAdd)
                            + attackerAttr.getFinalAttributeByKey(damageAttackOutAddKey))
                        * (1 + (attackerAttr.getFinalAttributeByKey(AttributeConst.BulletAttackOutPer)
                            + attackerAttr.getFinalAttributeByKey(damageAttackOutPerKey)) / 10000))
                    + attackerAttr.getFinalAttributeByKey(AttributeConst.BulletAttackInAdd)
                    + attackerAttr.getFinalAttributeByKey(damageAttackInAddKey)) 
                    * (1 + (attackerAttr.getFinalAttributeByKey(AttributeConst.BulletAttackInPer) 
                        + attackerAttr.getFinalAttributeByKey(damageAttackInPerKey)) / 10000);
            } else {
                attack = (((attack * hurtRate + attackerAttr.getFinalAttributeByKey(AttributeConst.BulletAttackOutAdd))
                        * (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.BulletAttackOutPer) / 10000))
                    + attackerAttr.getFinalAttributeByKey(AttributeConst.BulletAttackInAdd)) 
                        * (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.BulletAttackInPer) / 10000);
            }
        } else {
            attack = (((attack * hurtRate + attackerAttr.getFinalAttributeByKey(AttributeConst.NuclearAttackOutAdd))
                    * (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.NuclearAttackOutPer) / 10000))
                + attackerAttr.getFinalAttributeByKey(AttributeConst.NuclearAttackInAdd)) 
                    * (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.NuclearAttackInPer) / 10000);
        }

        // ④子弹or核弹伤害抗性局内总值=[(子弹伤害抗性局外绝对值1+子弹伤害抗性局外绝对值2+⋯)×(1+子弹伤害抗性局外百分比1+子弹伤害抗性局外百分比2+⋯)+子弹伤害抗性局内绝对值1+子弹伤害抗性局内绝对值2+⋯]×(1+子弹伤害抗性局内百分比1+子弹伤害抗性局内百分比2+⋯)
        let hurtResistanceOutAddKey = isNuclear ? AttributeConst.NuclearHurtResistanceOutAdd : AttributeConst.BulletHurtResistanceOutAdd;
        let hurtResistanceOutPerKey = isNuclear ? AttributeConst.NuclearHurtResistanceOutPer : AttributeConst.BulletHurtResistanceOutPer; 
        let hurtResistanceInAddKey  = isNuclear ? AttributeConst.NuclearHurtResistanceInAdd  : AttributeConst.BulletHurtResistanceInAdd;
        let hurtResistanceInPerKey  = isNuclear ? AttributeConst.NuclearHurtResistanceInPer  : AttributeConst.BulletHurtResistanceInPer;  

        let hurtResistance = defenderAttr.getFinialAttributeByOutInKey(
            hurtResistanceOutAddKey, hurtResistanceOutPerKey,
            hurtResistanceInAddKey, hurtResistanceInPerKey);

        // ⑤承受子弹伤害%局内总值=(1-子弹伤害减免局外百分比1-子弹伤害减免局外百分比2-…)×(1-子弹伤害减免局内百分比1-子弹伤害减免局内百分比2-…)
        let hurtDerate = (1 - defenderAttr.getFinalAttributeByKey(AttributeConst.BulletHurtDerateOut)) 
            * (1 - defenderAttr.getFinalAttributeByKey(AttributeConst.BulletHurtDerateIn));

        // ⑥对boss or 普通怪物伤害%局内总值=(1+对boss or 普通怪物伤害局外百分比1+对boss or 普通怪物伤害局外百分比2+⋯)×(1+对boss or 普通怪物伤害局内百分比1+对boss or 普通怪物伤害局内百分比2+⋯)
        let hurtBonus: number
        if (isBoss) {
            hurtBonus = (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.BossHurtBonusOut)) 
                * (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.BossHurtBonusIn));
        } else {
            hurtBonus = (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.NormalHurtBonusOut)) 
                * (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.NormalHurtBonusIn));
        }

        //⑦子弹（分类型）or核弹的局内伤害修正=(1+子弹伤害局外百分比1+子弹伤害局外百分比2+⋯)×(1+子弹伤害局内百分比1+子弹伤害局内百分比2+⋯)
        let hurtFix:number
        if (!isNuclear) {
            let damageHurtFixOutKey = 0;
            let damageHurtFixInKey  = 0;
            switch(damageType) {
                case DamageType.EXPLOSIVE:
                    damageHurtFixOutKey = AttributeConst.ExplosiveBulletHurtFixOut
                    damageHurtFixInKey  = AttributeConst.ExplosiveBulletHurtFixIn
                    break;
                case DamageType.NORMAL:
                    damageHurtFixOutKey = AttributeConst.NormalBulletHurtFixOut
                    damageHurtFixInKey  = AttributeConst.NormalBulletHurtFixIn
                    break;
                case DamageType.ENERGETIC:
                    damageHurtFixOutKey = AttributeConst.EnergeticBulletHurtFixOut
                    damageHurtFixInKey  = AttributeConst.EnergeticBulletHurtFixIn
                    break;
                case DamageType.PHYSICAL:
                    damageHurtFixOutKey = AttributeConst.PhysicsBulletHurtFixOut
                    damageHurtFixInKey  = AttributeConst.PhysicsBulletHurtFixIn
                    break;
            }
            if (damageType != DamageType.ALL) {
                hurtFix = (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.BulletHurtFixOut) + attackerAttr.getFinalAttributeByKey(damageHurtFixOutKey)) 
                    * (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.BulletHurtFixIn) +  attackerAttr.getFinalAttributeByKey(damageHurtFixInKey));
            } else {
                hurtFix = (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.BulletHurtFixOut)) 
                    * (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.BulletHurtFixIn));
            }
        } else {
            hurtFix = (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.NuclearHurtFixOut)) 
                * (1 + attackerAttr.getFinalAttributeByKey(AttributeConst.NuclearHurtFixIn));
        }

        // ⑧子弹or核弹最终造成的伤害=[(③子弹or核弹的局内攻击力×⑦子弹or核弹的局内伤害修正×战斗力差伤害修正百分比)-④受击方子弹伤害抗性局内总值]×⑤受击方承受子弹伤害%局内总值×(1+⑥攻击方对boss or 普通怪物伤害%局内总值)
        let damage = ((attack * hurtFix * attackPowerFix) - hurtResistance) * hurtDerate * hurtBonus;

        // ○15弹的局内破甲伤害（对核弹和主动技能无效）=(1+子弹破甲伤害局外百分比1+子弹破甲伤害局外百分比2+⋯)×(1+子弹破甲伤害局内百分比1+子弹破甲伤害局内百分比2+⋯)
        if (!isNuclear && defender.buffComp.HasBuff(MyApp.lubanTables.TbGlobalAttr.BulletPenetrationFlagBuffID)) {
            const penetration = (1+attackerAttr.getFinalAttributeByKey(AttributeConst.BulletPenetrationOut))
                * (1+attackerAttr.getFinalAttributeByKey(AttributeConst.BulletPenetrationIn));
            damage *= penetration;
        }

        return Math.ceil(damage);
    }

    getMaxHP():number {
        return Math.floor(this.getFinialAttributeByOutInKey(AttributeConst.MaxHPOutAdd, AttributeConst.MaxHPOutPer, 
            AttributeConst.MaxHPInAdd, AttributeConst.MaxHPInPer));
    }

    getAttack():number {
        return Math.floor(this.getFinialAttributeByOutInKey(
            AttributeConst.AttackOutAdd, AttributeConst.AttackOutPer, 
            AttributeConst.AttackInAdd, AttributeConst.AttackInPer));
    }

    getHPRecovery():number {
        return Math.floor(this.getFinialAttributeByOutInKey(
                AttributeConst.HPRecoveryOutAdd, AttributeConst.HPRecoveryOutPer, 
                AttributeConst.HPRecoveryInAdd, AttributeConst.HPRecoveryInPer)
            + this.getMaxHP() 
                * (this.getFinalAttributeByKey(AttributeConst.MaxHPRecoveryRateOut) 
                    + (this.getFinalAttributeByKey(AttributeConst.MaxHPRecoveryRateIn)))/10000);
    }
    
}
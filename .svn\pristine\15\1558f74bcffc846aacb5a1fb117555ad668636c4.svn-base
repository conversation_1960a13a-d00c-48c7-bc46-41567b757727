import { _decorator, Component, JsonAsset, resources, view } from 'cc';
import { MyApp } from 'db://assets/bundles/common/script/app/MyApp';
import { LevelData } from 'db://assets/bundles/common/script/leveldata/leveldata';
import { GameIns } from 'db://assets/bundles/common/script/game/GameIns';
import { GameEnum } from 'db://assets/bundles/common/script/game/const/GameEnum';
import { LevelBaseUI } from './LevelBaseUI';
import EventManager from 'db://assets/bundles/common/script/event/EventManager';
import { GameEvent } from 'db://assets/bundles/common/script/game/event/GameEvent';
import { eLevelSpecialEvent } from 'db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerSpecialEvent';
const { ccclass } = _decorator;

enum PRELOAD_STATE {
    NONE = 0,
    CHECK = 1,
    LOADING = 2,
    LOADED = 3
}

const MAX_LEVEL_COUNT = 2; // 场景存在最大关卡数

interface LevelBaseUIInfo {
    levelID: number;
    totalTime: number;
    speed: number;
}

/*
 * @description 加载策略：
 * 根据当前移动到的关卡，提前加载下一关的关卡（目前只提前加载的关卡数定为1）
*/
@ccclass('GameMapRun')
export default class GameMapRun extends Component {
    
    static instance: GameMapRun | null = null;

    private _levelList: number[] = [];
    private _chapterMaxLevelCount: number = 0;

    private _initOver = false;
    private _preloadState = PRELOAD_STATE.NONE;

    private _levelLoadIndex = 0; // 当前关卡加载索引
    private _levelIndex = 0; // 当前关卡索引（实时移动到的）
    private _levelUIInfoList: Array<LevelBaseUIInfo> = []; //已经加载的关卡的基本信息

    private _levelTotalDuration: number = 0; // 关卡总持续时间
    private _levelTotalHeight: number = 0; // 关卡总高度
    private _levelDistance: number = 0; // 当前关卡移动的距离
    private _levelDuration: number = 0; // 当前关卡的持续时间
    private _levelSpeed: number = 0; // 当前关卡的移动速度
    private _bLevelRunFinish = false; 

    public get SectionRunFinish(): boolean { return this._bLevelRunFinish; }

    public resetRunState() {
        this._bLevelRunFinish = false;
    }

    public get MapSpeed(): number {
        return this._levelSpeed;
    }

    public get ViewTop(): number {
        return view.getVisibleSize().height * -0.5;
    }

    public get levelDistance(): number {
        return this._levelDistance;
    }

    public get levelDuration(): number {
        return this._levelDuration;
    }

    onLoad() {
        GameMapRun.instance = this;
    }

    async initData(): Promise<void> {
        this.reset();
        this._levelList = GameIns.battleManager.levelList;
        this._chapterMaxLevelCount = GameIns.battleManager.chapterConfig!.levelCount;
        await this._loadNextLevelPrefab();
        this._initCurLevelData();
        const levelBaseUI = this.node.getComponent<LevelBaseUI>(LevelBaseUI);
        if (levelBaseUI) {
            await new Promise<void>((resolve) => {
                // 确保 LevelBaseUI 的初始化完成（例如背景加载）
                const check = () => {
                    if (levelBaseUI.backgroundLayer!.backgrounds!.length > 0) {
                        resolve();
                    } else {
                        setTimeout(check, 100);
                    }
                };
                check();
            });
        }
        this._initOver = true;
        this._preloadState = PRELOAD_STATE.CHECK;
    }


    initBattle() {

    }

    updateGameLogic(deltaTime: number) {
        const gameState = GameIns.gameStateManager.gameState;
        if (
            gameState !== GameEnum.GameState.Battle &&
            gameState !== GameEnum.GameState.WillOver &&
            gameState !== GameEnum.GameState.Idle &&
            gameState !== GameEnum.GameState.Over
        ) {
            return;
        }

        if (!this._initOver) {
            return;
        }

        this._tick(deltaTime);
        this._checkNextLevelLoading();
    }

    clear() {
        // 清理加载的资源

    }

    reset() {
        // 重置地图数据
        //this.node.removeAllChildren();
        this._initOver = false;
        this._levelLoadIndex = 0;
        this._levelIndex = 0;
        this._levelDistance = 0;
        this._levelTotalDuration = 0;
        this._levelTotalHeight = 0;
        this._levelDuration = 0;
        this._levelList = [];
        this._levelUIInfoList = [];
    }

    private async _loadNextLevelPrefab(): Promise<void> {
        // 计算实际需要加载的关卡数量
        const remainingLevels = MAX_LEVEL_COUNT - this._levelUIInfoList.length;
        console.log('GameMapRun', ' levelCount:', this._chapterMaxLevelCount, 'this._levelLoadIndex:', this._levelLoadIndex, 'remainingLevels:', remainingLevels);
        const levelsToLoad = Math.min(MAX_LEVEL_COUNT, remainingLevels);

        if (levelsToLoad <= 0) {
            console.log('GameMapRun', ' no level to load');
            this._preloadState = PRELOAD_STATE.LOADED;
            return;
        }

        let loadIndex = this._levelLoadIndex;
        const loadPromises: Promise<void>[] = [];
        for (let i = 0; i < levelsToLoad; i++) {
            const levelID = this._levelList[loadIndex + i];
            console.log('GameMapRun', ' level LoadIndex:', loadIndex + i);
            const levelConfig = MyApp.lubanTables.TbResLevel.get(levelID);
            if (levelConfig == null) {
                console.log('GameMapRun', ' level data not found', levelID);
                return;
            }

            const prefabName = `game/level/${levelConfig.prefab}`;
            const loadPromise = new Promise<void>((resolve) => {
                resources.load(prefabName, JsonAsset, async (err, prefab) => {
                    if (err) {
                        console.error('GameMapRun', '加载关卡预制体失败:', err);
                        resolve();
                        return;
                    }

                    var levelBaseUI = this.node.getComponent(LevelBaseUI);
                    //const nodeLayer = new Node(`chapter${this._chapterData.id}`);
                    if (levelBaseUI == null) {
                        levelBaseUI = this.node.addComponent(LevelBaseUI);
                    }

                    const levelInfo = {
                        levelID: levelID,
                        levelCount: this._chapterMaxLevelCount,
                        levelIndex: loadIndex + i
                    };

                    var levelData = LevelData.fromJSON(prefab?.json);
                    if (levelInfo.levelIndex === 0) {
                        await levelBaseUI.levelPrefab(levelData, levelInfo);
                    } else {
                        levelBaseUI.levelPrefab(levelData, levelInfo);
                    }
                    //this.node.addChild(nodeLayer);
                    var levelBaseUIInfo = this._initLevelInfo(levelID, levelData.totalTime, levelData.backgroundLayer.speed);
                    this._levelUIInfoList.push(levelBaseUIInfo);
                    console.log('GameMapRun', '加载关卡:', levelID, 'loadIndex:', levelInfo.levelIndex, ' - ', levelConfig.prefab, '_levelUIInfoList push length:', this._levelUIInfoList.length);

                    resolve();
                });
            });

            loadPromises.push(loadPromise);
        }

        if (this._levelIndex === 0) {
            await Promise.all(loadPromises); // 首次加载需要等待
            this._preloadState = PRELOAD_STATE.LOADED;
            this._levelLoadIndex += levelsToLoad;
        } else {
            await Promise.all(loadPromises)
                .then(() => {
                    this._preloadState = PRELOAD_STATE.LOADED;
                    this._levelLoadIndex += levelsToLoad;
                    console.log('GameMapRun', '关卡预加载完成');
                })
                .catch((err) => {
                    console.error('后台预加载失败:', err);
                });
        }

        if (this._levelLoadIndex >= this._chapterMaxLevelCount) {
            this._levelLoadIndex = 0;
            console.log('GameMapRun', '所有关卡已加载完成,循环加载开始');
        }
    }

    private _checkNextLevelLoading() {
        if (this._preloadState === PRELOAD_STATE.CHECK && this._levelUIInfoList.length < MAX_LEVEL_COUNT) {
            console.log('GameMapRun', ' 开始预加载关卡: _levelIndex', this._levelIndex, 'this._levelLoadIndex:', this._levelLoadIndex);
            this._preloadState = PRELOAD_STATE.LOADING;
            this._loadNextLevelPrefab().catch(err => {
                console.error('GameMapRun', ' Background loading failed:', err);
            });
        }
    }

    private _initCurLevelData() {
        if (this._levelIndex >= this._chapterMaxLevelCount) {
            console.error('GameMapRun', ' no level to init');
            return;
        }

        const levelBaseUI = this.node.getComponent<LevelBaseUI>(LevelBaseUI);
        this._levelTotalHeight = levelBaseUI!.getLevelTotalHeightByIndex(this._levelIndex);

        const levelBase = this._levelUIInfoList[0];
        if (levelBase) {
            levelBaseUI!.switchLevel(levelBase.speed, levelBase.totalTime, this._levelIndex);
            this._levelSpeed = levelBase.speed;
            this._levelDistance = 0;
            this._levelDuration = 0;

            this._preloadState = PRELOAD_STATE.CHECK;
        }
    }

    private _tick(deltaTime: number) {

        if (this._levelIndex >= this._chapterMaxLevelCount) {
            console.error('GameMapRun', ' no level to tick');
            return;
        }

        if (this._levelTotalHeight <= 0) {
            return;
        }

        this._levelDuration += deltaTime;
        this._levelTotalDuration += deltaTime;
        this._levelDistance += this.MapSpeed * deltaTime;

        const levelBaseUI = this.node.getComponent<LevelBaseUI>(LevelBaseUI);
        if (levelBaseUI != null) {
            //console.log('GameMapRun',' tick level', levelData.levelID, this._levelDuration);
            levelBaseUI.tick(deltaTime);
            var targetDistance = this._levelTotalHeight;

            if (this._levelDistance >= targetDistance) {
                const finishedLevel = this._levelUIInfoList.shift();
                this._bLevelRunFinish = true;
                console.log('GameMapRun', '关卡完成:', this._levelIndex, '_levelUIInfoList shift length:', this._levelUIInfoList.length);
                this._levelIndex++;

                if (this._levelIndex >= this._chapterMaxLevelCount) {
                    this._levelIndex = 0;
                    console.log('GameMapRun', '章节完成，开始循环');
                }

                this._initCurLevelData();
            }
        }
    }

    private _initLevelInfo(levelID: number, time: number, speed: number): LevelBaseUIInfo {
        return {
            levelID: levelID,
            totalTime: time,
            speed: speed,
        };
    }
}
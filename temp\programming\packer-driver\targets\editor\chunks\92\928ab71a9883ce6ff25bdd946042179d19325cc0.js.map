{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelLayerUI.ts"], "names": ["_decorator", "instantiate", "JsonAsset", "Node", "Prefab", "Sprite", "SpriteFrame", "UITransform", "v2", "MyApp", "LayerSplicingMode", "LevelDataTerrain", "LevelEventRun", "Tools", "GameIns", "logDebug", "logError", "logInfo", "GameConst", "LevelNodeCheckOutScreen", "LevelUtils", "EmittierStatus", "EmittierTerrain", "<PERSON><PERSON><PERSON><PERSON>", "ccclass", "SCROLL_POOL_NAME", "TERRAIN_POOL_NAME", "EMIITER_POOL_NAME", "TerrainsNodeName", "DynamicNodeName", "ScrollsNodeName", "EmittiersNodeName", "jsonRootPath", "LevelLayerUI", "_offSetY", "terrainsNode", "dynamicNode", "scrollsNode", "emittiersNode", "_scorllData", "_scorllPrefabs", "_lastScrollNodeHeight", "_emittierInfo", "_inactiveEmittierNodes", "_insEmittierLock", "_terrainsInfo", "_insTerrainLock", "events", "eventRunners", "onLoad", "initByLevelData", "data", "offSetY", "bFirstLoad", "node", "setPosition", "getOrAddNode", "_initTerrainsByLevelData", "_initDynamicsByLevelData", "_initEmittierLevelData", "_initScorllsByLevelData", "_sortTerrainsInfo", "sort", "a", "b", "position", "y", "for<PERSON>ach", "event", "push", "terrains", "length", "parent", "name", "remark", "zIndex", "terrain", "type", "endsWith", "json_path", "replace", "extractPathPart", "jsonAsset", "resMgr", "loadAsync", "jsonData", "json", "Array", "isArray", "subZIndex", "subParentNode", "setSiblingIndex", "t", "terrainData", "Object", "assign", "terrainPosY", "height", "VIEWPORT_LOAD_POS", "png_path", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultBundleName", "_createPngNode", "x", "scale", "rotation", "path", "uuid", "prefabNode", "_createTerrainPrefabNode", "_addTerrainInfo", "dynamics", "weights", "group", "dynamic", "weight", "dynaIndex", "getRandomIndexByWeights", "battleManager", "random", "terrainIndex", "randomOffsetX", "offSetX", "max", "min", "randomOffsetY", "console", "log", "curOffPosY", "curPosY", "bFristLevel", "emittiers", "index", "emittier", "prefab", "_createEmittierNode", "_addEmittierInfo", "scrolls", "element", "srocllIndex", "i", "uuids", "halfHeight", "_addScrollNode", "nodeHeight", "splicingMode", "node_height", "getComponent", "contentSize", "fix_height", "random_height", "Math", "tick", "deltaTime", "posY", "speed", "scrollY", "VIEWPORT_TOP", "eventRunner", "isTriggered", "splice", "_checkDynaTerrain", "_checkScroll", "_check<PERSON><PERSON><PERSON>", "parentNode", "attr", "elemName", "indicesToRemove", "newTerrainsInfo", "terrainInfo", "error", "<PERSON><PERSON><PERSON><PERSON>", "children", "lastChildTransform", "lastChildTop", "newY", "newNode", "emittierInfo", "emittierNode", "<PERSON><PERSON><PERSON><PERSON>", "comp", "status", "inactive", "startEmittier", "follow", "spriteFramePath", "terrainNode", "gameMapManager", "mapObjectPoolManager", "get", "spriteFrame", "terrainSprite", "addComponent", "uiTransform", "size", "originalSize", "setContentSize", "width", "checkOut", "init", "setScale", "setRotationFromEuler", "<PERSON><PERSON><PERSON><PERSON>", "prefabPath", "emittierTerrain", "xPos", "yPos", "prefabName", "getEventByElemID", "elemID"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAuBC,MAAAA,W,OAAAA,W;AAAiBC,MAAAA,S,OAAAA,S;AAAgBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,E,OAAAA,E;;AACxGC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,iB,iBAAAA,iB;AAAoEC,MAAAA,gB,iBAAAA,gB;;AACpEC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,O,iBAAAA,O;;AACpBC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,uB,iBAAAA,uB;;AACAC,MAAAA,U,kBAAAA,U;;AACAC,MAAAA,c,kBAAAA,c;AAAgBC,MAAAA,e,kBAAAA,e;;AAChBC,MAAAA,U,kBAAAA,U;;;;;;;;;OAEH;AAAEC,QAAAA;AAAF,O,GAAcxB,U;AAEdyB,MAAAA,gB,GAAmB,a;AACnBC,MAAAA,iB,GAAoB,c;AACpBC,MAAAA,iB,GAAoB,e;AAEpBC,MAAAA,gB,GAAmB,U;AACnBC,MAAAA,e,GAAkB,S;AAClBC,MAAAA,e,GAAkB,S;AAClBC,MAAAA,iB,GAAoB,W;AAEpBC,MAAAA,Y,GAAuB,sC;;8BAShBC,Y,WADZT,OAAO,CAAC,cAAD,C,gBAAR,MACaS,YADb;AAAA;AAAA,oCAC6C;AAAA;AAAA;AAAA,eACjCC,QADiC,GACd,CADc;AACX;AADW,eAGjCC,YAHiC,GAGL,IAHK;AAAA,eAIjCC,WAJiC,GAIN,IAJM;AAAA,eAKjCC,WALiC,GAKN,IALM;AAAA,eAMjCC,aANiC,GAMJ,IANI;AAAA,eAQjCC,WARiC,GAQK,IARL;AAAA,eASjCC,cATiC,GASN,EATM;AASF;AATE,eAUjCC,qBAViC,GAUD,CAVC;AAAA,eAYjCC,aAZiC,GAYG,EAZH;AAAA,eAajCC,sBAbiC,GAaA,EAbA;AAaI;AAbJ,eAcjCC,gBAdiC,GAcL,KAdK;AAgBzC;AAhByC,eAiBjCC,aAjBiC,GAiBF,EAjBE;AAiBE;AAjBF,eAkBjCC,eAlBiC,GAkBN,KAlBM;AAAA,eAoBjCC,MApBiC,GAoBN,EApBM;AAAA,eAqBjCC,YArBiC,GAqBD,EArBC;AAAA;;AAuBzCC,QAAAA,MAAM,GAAS,CAEd;;AAE2B,cAAfC,eAAe,CAACC,IAAD,EAAuBC,OAAvB,EAAwCC,UAAxC,EAA4E;AACpG,eAAKnB,QAAL,GAAgBkB,OAAhB;AACA,eAAKE,IAAL,CAAUC,WAAV,CAAsB,CAAtB,EAAyBH,OAAzB,EAAkC,CAAlC;AAEA,eAAKjB,YAAL,GAAoB;AAAA;AAAA,wCAAWqB,YAAX,CAAwB,KAAKF,IAA7B,EAAmC1B,gBAAnC,CAApB;AACA,eAAKQ,WAAL,GAAmB;AAAA;AAAA,wCAAWoB,YAAX,CAAwB,KAAKF,IAA7B,EAAmCzB,eAAnC,CAAnB;AACA,eAAKQ,WAAL,GAAmB;AAAA;AAAA,wCAAWmB,YAAX,CAAwB,KAAKF,IAA7B,EAAmCxB,eAAnC,CAAnB;AACA,eAAKQ,aAAL,GAAqB;AAAA;AAAA,wCAAWkB,YAAX,CAAwB,KAAKF,IAA7B,EAAmCvB,iBAAnC,CAArB;AAEA,eAAKS,cAAL,GAAsB,EAAtB;AACA,eAAKK,aAAL,GAAqB,EAArB;AACA,eAAKH,aAAL,GAAqB,EAArB;AACA,eAAKC,sBAAL,GAA8B,EAA9B;AACA,eAAKG,eAAL,GAAuB,KAAvB;AACA,eAAKF,gBAAL,GAAwB,KAAxB,CAdoG,CAepG;AACA;;AACA,gBAAM,KAAKa,wBAAL,CAA8BN,IAA9B,EAAoCE,UAApC,CAAN;AACA,gBAAM,KAAKK,wBAAL,CAA8BP,IAA9B,EAAoCE,UAApC,CAAN;AACA,gBAAM,KAAKM,sBAAL,CAA4BR,IAA5B,EAAkCE,UAAlC,CAAN,CAnBoG,CAoBpG;;AACA,gBAAM,KAAKO,uBAAL,CAA6BT,IAA7B,EAAmCE,UAAnC,CAAN;;AAEA,eAAKQ,iBAAL;;AAEA,eAAKd,MAAL,GAAc,CAAC,GAAGI,IAAI,CAACJ,MAAT,CAAd;AACA,eAAKA,MAAL,CAAYe,IAAZ,CAAiB,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAACE,QAAF,CAAWC,CAAX,GAAeF,CAAC,CAACC,QAAF,CAAWC,CAArD;AACA,eAAKnB,MAAL,CAAYoB,OAAZ,CAAqBC,KAAD,IAAW;AAC3B,iBAAKpB,YAAL,CAAkBqB,IAAlB,CAAuB;AAAA;AAAA,gDAAkBD,KAAlB,EAAyB,IAAzB,CAAvB;AACH,WAFD;AAGH;;AAEqC,cAAxBX,wBAAwB,CAACN,IAAD,EAAuBE,UAAvB,EAA2D;AAAA;;AAC7F,cAAI,CAACF,IAAD,IAAS,KAAKhB,YAAL,KAAsB,IAA/B,IAAuCgB,IAAI,CAACmB,QAAL,CAAcC,MAAd,KAAyB,CAApE,EAAuE;AAAE;AAAS;;AAElF;AAAA;AAAA,oCAAS,cAAT,EAA0B,IAAD,qBAAI,KAAKjB,IAAL,CAAUkB,MAAd,kCAAI,kBAAkBA,MAAtB,qBAAI,kBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,YAAWtB,IAAI,CAACuB,MAAO,EAArG;AACA,cAAIC,MAAM,GAAG,CAAb;;AACA,eAAK,MAAMC,OAAX,IAAsBzB,IAAI,CAACmB,QAA3B,EAAoC;AAChC,gBAAIjB,UAAJ,EAAgB;AACZ,kBAAIuB,OAAO,CAACC,IAAR,CAAaN,MAAb,GAAsB,CAA1B,EAA6B;AACzB,oBAAIK,OAAO,CAACC,IAAR,CAAaC,QAAb,CAAsB,OAAtB,CAAJ,EAAoC;AAAA;;AAChC,wBAAMC,SAAS,GAAG/C,YAAY,GAAG4C,OAAO,CAACC,IAAR,CAAaG,OAAb,CAAqB,OAArB,EAA8B,EAA9B,CAAjC;AACA;AAAA;AAAA,4CAAS,cAAT,EAA0B,IAAD,sBAAI,KAAK1B,IAAL,CAAUkB,MAAd,mCAAI,mBAAkBA,MAAtB,qBAAI,mBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,gBAAe;AAAA;AAAA,gDAAWQ,eAAX,CAA2BF,SAA3B,CAAsC,EAAnI;AACA,wBAAMG,SAAS,GAAG,MAAM;AAAA;AAAA,sCAAMC,MAAN,CAAaC,SAAb,CAAuBL,SAAvB,EAAkC7E,SAAlC,CAAxB;AACA,wBAAMmF,QAAQ,GAAGH,SAAS,CAACI,IAA3B;;AACA,sBAAID,QAAQ,CAAEf,QAAV,IAAsBiB,KAAK,CAACC,OAAN,CAAcH,QAAQ,CAAEf,QAAxB,CAA1B,EAA6D;AAAA;;AACzD,wBAAImB,SAAS,GAAG,CAAhB;AACA,0BAAMC,aAAa,GAAG;AAAA;AAAA,kDAAWlC,YAAX,CAAwB,KAAKrB,YAA7B,EAA6C,QAAOwC,MAAO,EAA3D,CAAtB;AACA;AAAA;AAAA,8CAAS,cAAT,EAA0B,IAAD,sBAAI,KAAKrB,IAAL,CAAUkB,MAAd,mCAAI,mBAAkBA,MAAtB,qBAAI,mBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,sCAAqCiB,aAAa,CAACjB,IAAK,QAAOiB,aAAa,CAAClB,MAAd,CAAsBC,IAAK,EAAxK;AACAiB,oBAAAA,aAAa,CAACC,eAAd,CAA8BhB,MAA9B;;AACA,yBAAK,MAAMiB,CAAX,IAAgBP,QAAQ,CAAEf,QAA1B,EAAoC;AAChC,4BAAMuB,WAAW,GAAGC,MAAM,CAACC,MAAP,CAAc;AAAA;AAAA,iEAAd,EAAsCH,CAAtC,CAApB;AACA,4BAAMI,WAAW,GAAGpB,OAAO,CAACX,QAAR,CAAiBC,CAAjB,GAAqB2B,WAAW,CAAC5B,QAAZ,CAAqBC,CAA9D;;AACA,0BAAK8B,WAAW,GAAGH,WAAW,CAACI,MAAZ,GAAqB,CAAnC,GAAuC,KAAK3C,IAAL,CAAUW,QAAV,CAAmBC,CAA3D,IAAiE;AAAA;AAAA,kDAAUgC,iBAA/E,EAAkG;AAC9F,4BAAIL,WAAW,CAAChB,IAAZ,CAAiBC,QAAjB,CAA0B,MAA1B,CAAJ,EAAuC;AAAA;;AACnC,gCAAMqB,QAAQ,GAAG;AAAA;AAAA,8CAAMhB,MAAN,CAAaiB,YAAb,CAA0B;AAAA;AAAA,8CAAMjB,MAAN,CAAakB,iBAAvC,EAA0DR,WAAW,CAAChB,IAAZ,CAAiBG,OAAjB,CAAyB,MAAzB,EAAiC,EAAjC,CAA1D,CAAjB;AACA;AAAA;AAAA,oDAAS,cAAT,EAA0B,IAAD,sBAAI,KAAK1B,IAAL,CAAUkB,MAAd,mCAAI,mBAAkBA,MAAtB,qBAAI,mBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,0BAAyB;AAAA;AAAA,wDAAWQ,eAAX,CAA2BkB,QAA3B,CAAqC,EAA5I;AACA,gCAAM,KAAKG,cAAL,CACFH,QADE,EACQ3F,EAAE,CAACoE,OAAO,CAACX,QAAR,CAAiBsC,CAAjB,GAAqBV,WAAW,CAAC5B,QAAZ,CAAqBsC,CAA3C,EAA8CP,WAA9C,CADV,EACsExF,EAAE,CAACqF,WAAW,CAACW,KAAZ,CAAkBD,CAAnB,EAAsBV,WAAW,CAACW,KAAZ,CAAkBtC,CAAxC,CADxE,EAEF2B,WAAW,CAACY,QAFV,EAEoBf,aAFpB,EAEmCD,SAFnC,CAAN;AAGH,yBAND,MAMO;AAAA;;AACH,gCAAMiB,IAAI,GAAG;AAAA;AAAA,8CAAMvB,MAAN,CAAaiB,YAAb,CAA0B;AAAA;AAAA,8CAAMjB,MAAN,CAAakB,iBAAvC,EAA0DR,WAAW,CAACc,IAAtE,CAAb;AACA,gCAAMC,UAAU,GAAG,MAAM,KAAKC,wBAAL,CACrBH,IADqB,EACflG,EAAE,CAACoE,OAAO,CAACX,QAAR,CAAiBsC,CAAjB,GAAqBV,WAAW,CAAC5B,QAAZ,CAAqBsC,CAA3C,EAA8CP,WAA9C,CADa,EAC+CxF,EAAE,CAACqF,WAAW,CAACW,KAAZ,CAAkBD,CAAnB,EAAsBV,WAAW,CAACW,KAAZ,CAAkBtC,CAAxC,CADjD,EAErB2B,WAAW,CAACY,QAFS,EAECf,aAFD,EAEgBD,SAFhB,CAAzB;AAGA;AAAA;AAAA,oDAAS,cAAT,EAA0B,IAAD,sBAAI,KAAKnC,IAAL,CAAUkB,MAAd,mCAAI,mBAAkBA,MAAtB,qBAAI,mBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,6BAA4BmC,UAAU,CAAEnC,IAAK,EAA3H;AACH;AACJ,uBAdD,MAcO;AACHoB,wBAAAA,WAAW,CAAC5B,QAAZ,CAAqBC,CAArB,GAAyB8B,WAAzB;AACAH,wBAAAA,WAAW,CAAC5B,QAAZ,CAAqBsC,CAArB,IAA0B3B,OAAO,CAACX,QAAR,CAAiBsC,CAA3C;AACAV,wBAAAA,WAAW,CAACH,aAAZ,GAA4BA,aAA5B;;AACA,6BAAKoB,eAAL,CAAqBjB,WAArB,EAAkCH,aAAlC,EAAiDD,SAAjD;AACH;;AACDA,sBAAAA,SAAS;AACZ;AACJ,mBA9BD,MA8BO;AACH;AAAA;AAAA,8CAAS,cAAT,EAAyB,iCAAzB;AACH;AACJ,iBAtCD,MAsCO,IAAIb,OAAO,CAACC,IAAR,CAAaC,QAAb,CAAsB,MAAtB,CAAJ,EAAmC;AACtC,sBAAMF,OAAO,CAACX,QAAR,CAAiBC,CAAjB,GAAqBU,OAAO,CAACqB,MAAR,GAAiB,CAAvC,GAA4C,KAAK3C,IAAL,CAAUW,QAAV,CAAmBC,CAAhE,IAAsE;AAAA;AAAA,8CAAUgC,iBAApF,EAAuG;AAAA;;AACnG,0BAAMC,QAAQ,GAAG;AAAA;AAAA,wCAAMhB,MAAN,CAAaiB,YAAb,CAA0B;AAAA;AAAA,wCAAMjB,MAAN,CAAakB,iBAAvC,EAA0DzB,OAAO,CAACC,IAAR,CAAaG,OAAb,CAAqB,MAArB,EAA6B,EAA7B,CAA1D,CAAjB;AACA,0BAAM,KAAKsB,cAAL,CACFH,QADE,EACQ3F,EAAE,CAACoE,OAAO,CAACX,QAAR,CAAiBsC,CAAlB,EAAqB3B,OAAO,CAACX,QAAR,CAAiBC,CAAtC,CADV,EACoD1D,EAAE,CAACoE,OAAO,CAAC4B,KAAR,CAAcD,CAAf,EAAkB3B,OAAO,CAAC4B,KAAR,CAActC,CAAhC,CADtD,EAEFU,OAAO,CAAC6B,QAFN,EAEgB,KAAKtE,YAFrB,EAEoCwC,MAFpC,CAAN;AAGA;AAAA;AAAA,8CAAS,cAAT,EAA0B,IAAD,sBAAI,KAAKrB,IAAL,CAAUkB,MAAd,mCAAI,mBAAkBA,MAAtB,qBAAI,mBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,gBAAe;AAAA;AAAA,kDAAWQ,eAAX,CAA2BkB,QAA3B,CAAqC,EAAlI;AACH,mBAND,MAMO;AACH,yBAAKW,eAAL,CAAqBlC,OAArB,EAA8B,KAAKzC,YAAnC,EAAkDwC,MAAlD;AACH;AACJ;AACJ,eAlDD,MAkDO;AACH,oBAAKC,OAAO,CAACX,QAAR,CAAiBC,CAAjB,GAAqBU,OAAO,CAACqB,MAAR,GAAiB,CAAtC,GAA0C,KAAK3C,IAAL,CAAUW,QAAV,CAAmBC,CAA9D,IAAoE;AAAA;AAAA,4CAAUgC,iBAAlF,EAAqG;AAAA;;AACjG,wBAAMQ,IAAI,GAAG;AAAA;AAAA,sCAAMvB,MAAN,CAAaiB,YAAb,CAA0B;AAAA;AAAA,sCAAMjB,MAAN,CAAakB,iBAAvC,EAA0DzB,OAAO,CAAC+B,IAAlE,CAAb;AACA,wBAAMC,UAAU,GAAG,MAAM,KAAKC,wBAAL,CACrBH,IADqB,EACflG,EAAE,CAACoE,OAAO,CAACX,QAAR,CAAiBsC,CAAlB,EAAqB3B,OAAO,CAACX,QAAR,CAAiBC,CAAtC,CADa,EAC6B1D,EAAE,CAACoE,OAAO,CAAC4B,KAAR,CAAcD,CAAf,EAAkB3B,OAAO,CAAC4B,KAAR,CAActC,CAAhC,CAD/B,EAErBU,OAAO,CAAC6B,QAFa,EAEH,KAAKtE,YAFF,EAEiBwC,MAFjB,CAAzB;AAGA;AAAA;AAAA,4CAAS,cAAT,EAA0B,IAAD,sBAAI,KAAKrB,IAAL,CAAUkB,MAAd,mCAAI,mBAAkBA,MAAtB,qBAAI,mBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,uBAAsBmC,UAAU,CAAEnC,IAAK,EAArH;AACH,iBAND,MAMO;AACH,uBAAKqC,eAAL,CAAqBlC,OAArB,EAA8B,KAAKzC,YAAnC,EAAkDwC,MAAlD;AACH;AACJ;AACJ,aA9DD,MA8DO;AACH,mBAAKmC,eAAL,CAAqBlC,OAArB,EAA8B,KAAKzC,YAAnC,EAAkDwC,MAAlD;AACH;;AACDA,YAAAA,MAAM;AACT;AACJ;;AAEqC,cAAxBjB,wBAAwB,CAACP,IAAD,EAAuBE,UAAvB,EAA2D;AAAA;;AAC7F,cAAI,CAACF,IAAD,IAAS,KAAKf,WAAL,KAAqB,IAA9B,IAAsCe,IAAI,CAAC4D,QAAL,CAAcxC,MAAd,KAAyB,CAAnE,EAAsE;AAAE;AAAS;;AAEjF;AAAA;AAAA,oCAAS,cAAT,EAA0B,IAAD,sBAAI,KAAKjB,IAAL,CAAUkB,MAAd,mCAAI,mBAAkBA,MAAtB,qBAAI,mBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,YAAWtB,IAAI,CAACuB,MAAO,EAArG;AACA,cAAIC,MAAM,GAAG,CAAb;;AACA,eAAK,MAAMoC,QAAX,IAAuB5D,IAAI,CAAC4D,QAA5B,EAAsC;AAClC,gBAAIC,OAAiB,GAAG,EAAxB;AACAD,YAAAA,QAAQ,CAACE,KAAT,CAAe9C,OAAf,CAAwB+C,OAAD,IAAa;AAChCF,cAAAA,OAAO,CAAC3C,IAAR,CAAa6C,OAAO,CAACC,MAArB;AACH,aAFD;AAGA,kBAAMC,SAAS,GAAG;AAAA;AAAA,gCAAMC,uBAAN,CAA8BL,OAA9B,EAAuC;AAAA;AAAA,oCAAQM,aAAR,CAAsBC,MAAtB,EAAvC,CAAlB;AACA,kBAAML,OAAO,GAAGH,QAAQ,CAACE,KAAT,CAAeG,SAAf,CAAhB;AAEAJ,YAAAA,OAAO,GAAG,EAAV;AACAE,YAAAA,OAAO,CAAC5C,QAAR,CAAiBH,OAAjB,CAA0BS,OAAD,IAAa;AAClCoC,cAAAA,OAAO,CAAC3C,IAAR,CAAaO,OAAO,CAACuC,MAArB;AACH,aAFD;AAGA,kBAAMK,YAAY,GAAG;AAAA;AAAA,gCAAMH,uBAAN,CAA8BL,OAA9B,EAAuC;AAAA;AAAA,oCAAQM,aAAR,CAAsBC,MAAtB,EAAvC,CAArB;AACA,kBAAM3C,OAAO,GAAGsC,OAAO,CAAC5C,QAAR,CAAiBkD,YAAjB,CAAhB;AAEA,kBAAMC,aAAa,GAAG;AAAA;AAAA,oCAAQH,aAAR,CAAsBC,MAAtB,MAAkC3C,OAAO,CAAC8C,OAAR,CAAgBC,GAAhB,GAAsB/C,OAAO,CAAC8C,OAAR,CAAgBE,GAAxE,IAA+EhD,OAAO,CAAC8C,OAAR,CAAgBE,GAArH;AACA,kBAAMC,aAAa,GAAG;AAAA;AAAA,oCAAQP,aAAR,CAAsBC,MAAtB,MAAkC3C,OAAO,CAACxB,OAAR,CAAgBuE,GAAhB,GAAsB/C,OAAO,CAACxB,OAAR,CAAgBwE,GAAxE,IAA+EhD,OAAO,CAACxB,OAAR,CAAgBwE,GAArH;;AAEA,gBAAI,CAAC,KAAKtE,IAAL,CAAUkB,MAAX,IAAqB,CAAC,KAAKlB,IAAL,CAAUkB,MAAV,CAAiBA,MAA3C,EAAmD;AAC/CsD,cAAAA,OAAO,CAACC,GAAR,CAAY,cAAZ,EAA4B,8CAA5B;AACA;AACH;;AACD,gBAAI1E,UAAJ,EAAgB;AACZ,kBAAIuB,OAAO,CAACC,IAAR,CAAaN,MAAb,GAAsB,CAA1B,EAA6B;AAAA;;AACzB;AAAA;AAAA,0CAAS,cAAT,EAA0B,IAAD,sBAAI,KAAKjB,IAAL,CAAUkB,MAAd,mCAAI,mBAAkBA,MAAtB,qBAAI,mBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,cAAa;AAAA;AAAA,8CAAWQ,eAAX,CAA2B;AAAA;AAAA,oCAAME,MAAN,CAAaiB,YAAb,CAA0B;AAAA;AAAA,oCAAMjB,MAAN,CAAakB,iBAAvC,EAA0DzB,OAAO,CAAC+B,IAAlE,CAA3B,CAAoG,EAA/L;;AACA,oBAAI/B,OAAO,CAACC,IAAR,CAAaC,QAAb,CAAsB,OAAtB,CAAJ,EAAoC;AAAA;;AAChC,wBAAMC,SAAS,GAAG/C,YAAY,GAAG4C,OAAO,CAACC,IAAR,CAAaG,OAAb,CAAqB,OAArB,EAA8B,EAA9B,CAAjC;AACA;AAAA;AAAA,4CAAS,cAAT,EAA0B,IAAD,uBAAI,KAAK1B,IAAL,CAAUkB,MAAd,oCAAI,oBAAkBA,MAAtB,qBAAI,oBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,gBAAe;AAAA;AAAA,gDAAWQ,eAAX,CAA2BF,SAA3B,CAAsC,EAAnI;AACA,wBAAMG,SAAS,GAAG,MAAM;AAAA;AAAA,sCAAMC,MAAN,CAAaC,SAAb,CAAuBL,SAAvB,EAAkC7E,SAAlC,CAAxB;AACA,wBAAMmF,QAAQ,GAAGH,SAAS,CAACI,IAA3B;;AACA,sBAAID,QAAQ,CAAEf,QAAV,IAAsBiB,KAAK,CAACC,OAAN,CAAcH,QAAQ,CAAEf,QAAxB,CAA1B,EAA6D;AAAA;;AACzD,wBAAImB,SAAS,GAAG,CAAhB;AACA,0BAAMC,aAAa,GAAG;AAAA;AAAA,kDAAWlC,YAAX,CAAwB,KAAKpB,WAA7B,EAA4C,QAAOuC,MAAO,EAA1D,CAAtB;AACA;AAAA;AAAA,8CAAS,cAAT,EAA0B,IAAD,uBAAI,KAAKrB,IAAL,CAAUkB,MAAd,oCAAI,oBAAkBA,MAAtB,qBAAI,oBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,mCAAkCiB,aAAa,CAACjB,IAAK,QAAOiB,aAAa,CAAClB,MAAd,CAAsBC,IAAK,EAArK;AACAiB,oBAAAA,aAAa,CAACC,eAAd,CAA8BhB,MAA9B;;AACA,yBAAK,MAAMiB,CAAX,IAAgBP,QAAQ,CAAEf,QAA1B,EAAoC;AAAA;;AAChC,4BAAMuB,WAAW,GAAGC,MAAM,CAACC,MAAP,CAAc;AAAA;AAAA,iEAAd,EAAsCH,CAAtC,CAApB,CADgC,CAEhC;;AACA,4BAAMI,WAAW,GAAGkB,OAAO,CAACjD,QAAR,CAAiBC,CAAjB,GAAqB2D,aAArB,GAAqChC,WAAW,CAAC5B,QAAZ,CAAqBC,CAA9E;AACA,4BAAM8D,UAAU,GAAGhC,WAAW,GAAGH,WAAW,CAACI,MAAZ,GAAqB,CAAtD;AACA,4BAAMgC,OAAO,GAAGD,UAAU,GAAG,KAAK1E,IAAL,CAAUW,QAAV,CAAmBC,CAAhD;AACA;AAAA;AAAA,gDAAS,cAAT,EAA0B,IAAD,uBAAI,KAAKZ,IAAL,CAAUkB,MAAd,oCAAI,oBAAkBA,MAAtB,qBAAI,oBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,WAAU,KAAKnB,IAAL,CAAUW,QAAV,CAAmBC,CAAE,WAAU8D,UAAW,EAAlI;;AACA,0BAAKC,OAAD,IAAa;AAAA;AAAA,kDAAU/B,iBAA3B,EAA8C;AAC1C,4BAAIL,WAAW,CAAChB,IAAZ,CAAiBC,QAAjB,CAA0B,MAA1B,CAAJ,EAAuC;AAAA;;AACnC,gCAAMqB,QAAQ,GAAG;AAAA;AAAA,8CAAMhB,MAAN,CAAaiB,YAAb,CAA0B;AAAA;AAAA,8CAAMjB,MAAN,CAAakB,iBAAvC,EAA0DR,WAAW,CAAChB,IAAZ,CAAiBG,OAAjB,CAAyB,MAAzB,EAAiC,EAAjC,CAA1D,CAAjB;AACA;AAAA;AAAA,oDAAS,cAAT,EAA0B,IAAD,uBAAI,KAAK1B,IAAL,CAAUkB,MAAd,oCAAI,oBAAkBA,MAAtB,qBAAI,oBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,0BAAyB;AAAA;AAAA,wDAAWQ,eAAX,CAA2BkB,QAA3B,CAAqC,EAA5I;AACA,gCAAM,KAAKG,cAAL,CACFH,QADE,EACQ3F,EAAE,CAAC0G,OAAO,CAACjD,QAAR,CAAiBsC,CAAjB,GAAqBkB,aAArB,GAAqC5B,WAAW,CAAC5B,QAAZ,CAAqBsC,CAA3D,EAA8DP,WAA9D,CADV,EACsFxF,EAAE,CAACqF,WAAW,CAACW,KAAZ,CAAkBD,CAAnB,EAAsBV,WAAW,CAACW,KAAZ,CAAkBtC,CAAxC,CADxF,EAEF2B,WAAW,CAACY,QAFV,EAEoBf,aAFpB,EAEmCD,SAFnC,CAAN;AAGH,yBAND,MAMO;AAAA;;AACH,gCAAMiB,IAAI,GAAG;AAAA;AAAA,8CAAMvB,MAAN,CAAaiB,YAAb,CAA0B;AAAA;AAAA,8CAAMjB,MAAN,CAAakB,iBAAvC,EAA0DR,WAAW,CAACc,IAAtE,CAAb;AACA,gCAAMC,UAAU,GAAG,MAAM,KAAKC,wBAAL,CACrBH,IADqB,EACflG,EAAE,CAAC0G,OAAO,CAACjD,QAAR,CAAiBsC,CAAjB,GAAqBkB,aAArB,GAAqC5B,WAAW,CAAC5B,QAAZ,CAAqBsC,CAA3D,EAA8DP,WAA9D,CADa,EAC+DxF,EAAE,CAACqF,WAAW,CAACW,KAAZ,CAAkBD,CAAnB,EAAsBV,WAAW,CAACW,KAAZ,CAAkBtC,CAAxC,CADjE,EAErB2B,WAAW,CAACY,QAFS,EAECf,aAFD,EAEgBD,SAFhB,CAAzB;AAGA;AAAA;AAAA,oDAAS,cAAT,EAA0B,IAAD,uBAAI,KAAKnC,IAAL,CAAUkB,MAAd,oCAAI,oBAAkBA,MAAtB,qBAAI,oBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,6BAA4BmC,UAAU,CAACnC,IAAK,EAA1H;AACH;AACJ,uBAdD,MAcO;AACH,6BAAKqC,eAAL,CAAqB;AACjBH,0BAAAA,IAAI,EAAEd,WAAW,CAACc,IADD;AAEjB9B,0BAAAA,IAAI,EAAEgB,WAAW,CAAChB,IAFD;AAGjBoB,0BAAAA,MAAM,EAAEJ,WAAW,CAACI,MAHH;AAIjBhC,0BAAAA,QAAQ,EAAEzD,EAAE,CAAC0G,OAAO,CAACjD,QAAR,CAAiBsC,CAAjB,GAAqBkB,aAArB,GAAqC5B,WAAW,CAAC5B,QAAZ,CAAqBsC,CAA3D,EAA8DP,WAA9D,CAJK;AAKjBQ,0BAAAA,KAAK,EAAEhG,EAAE,CAACqF,WAAW,CAACW,KAAZ,CAAkBD,CAAnB,EAAsBV,WAAW,CAACW,KAAZ,CAAkBtC,CAAxC,CALQ;AAMjBuC,0BAAAA,QAAQ,EAAEZ,WAAW,CAACY;AANL,yBAArB,EAOGf,aAPH,EAOkBD,SAPlB;AAQH;;AACDA,sBAAAA,SAAS;AACZ;AACJ,mBAtCD,MAsCO;AACH;AAAA;AAAA,8CAAS,cAAT,EAAyB,iCAAzB;AACH;AACJ,iBA9CD,MA8CO,IAAIb,OAAO,CAACC,IAAR,CAAaC,QAAb,CAAsB,MAAtB,CAAJ,EAAmC;AACtC,sBAAKoC,OAAO,CAACjD,QAAR,CAAiBC,CAAjB,GAAqBU,OAAO,CAACqB,MAAR,GAAiB,CAAtC,GAA0C4B,aAA1C,GAA0D,KAAKvE,IAAL,CAAUW,QAAV,CAAmBC,CAA9E,IAAoF;AAAA;AAAA,8CAAUgC,iBAAlG,EAAqH;AAAA;;AACjH,0BAAMC,QAAQ,GAAG;AAAA;AAAA,wCAAMhB,MAAN,CAAaiB,YAAb,CAA0B;AAAA;AAAA,wCAAMjB,MAAN,CAAakB,iBAAvC,EAA0DzB,OAAO,CAACC,IAAR,CAAaG,OAAb,CAAqB,MAArB,EAA6B,EAA7B,CAA1D,CAAjB;AACA;AAAA;AAAA,8CAAS,cAAT,EAA0B,IAAD,uBAAI,KAAK1B,IAAL,CAAUkB,MAAd,oCAAI,oBAAkBA,MAAtB,qBAAI,oBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,gBAAe;AAAA;AAAA,kDAAWQ,eAAX,CAA2BkB,QAA3B,CAAqC,EAAlI;AACA,0BAAM,KAAKG,cAAL,CACFH,QADE,EACQ3F,EAAE,CAAC0G,OAAO,CAACjD,QAAR,CAAiBsC,CAAjB,GAAqBkB,aAAtB,EAAqCP,OAAO,CAACjD,QAAR,CAAiBC,CAAjB,GAAqB2D,aAA1D,CADV,EACoFrH,EAAE,CAAC0G,OAAO,CAACV,KAAR,CAAcD,CAAf,EAAkBW,OAAO,CAACV,KAAR,CAActC,CAAhC,CADtF,EAEFgD,OAAO,CAACT,QAFN,EAEgB,KAAKrE,WAFrB,EAEmCuC,MAFnC,CAAN;AAGH,mBAND,MAMO;AACH,yBAAKmC,eAAL,CAAqB;AACjBH,sBAAAA,IAAI,EAAE/B,OAAO,CAAC+B,IADG;AAEjB9B,sBAAAA,IAAI,EAAED,OAAO,CAACC,IAFG;AAGjBoB,sBAAAA,MAAM,EAAErB,OAAO,CAACqB,MAHC;AAIjBhC,sBAAAA,QAAQ,EAAEzD,EAAE,CAAC0G,OAAO,CAACjD,QAAR,CAAiBsC,CAAjB,GAAqBkB,aAAtB,EAAsCP,OAAO,CAACjD,QAAR,CAAiBC,CAAjB,GAAqB2D,aAA3D,CAJK;AAKjBrB,sBAAAA,KAAK,EAAEhG,EAAE,CAAC0G,OAAO,CAACV,KAAR,CAAcD,CAAf,EAAkBW,OAAO,CAACV,KAAR,CAActC,CAAhC,CALQ;AAMjBuC,sBAAAA,QAAQ,EAAES,OAAO,CAACT;AAND,qBAArB,EAOG,KAAKrE,WAPR,EAOsBuC,MAPtB;AAQH;AACJ;AACJ,eAlED,MAkEO;AACH,oBAAKuC,OAAO,CAACjD,QAAR,CAAiBC,CAAjB,GAAqBuD,aAArB,GAAqC7C,OAAO,CAACqB,MAAR,GAAiB,CAAtD,GAA0D,KAAK3C,IAAL,CAAUW,QAAV,CAAmBC,CAA9E,IAAoF;AAAA;AAAA,4CAAUgC,iBAAlG,EAAqH;AAAA;;AACjH,wBAAMQ,IAAI,GAAG;AAAA;AAAA,sCAAMvB,MAAN,CAAaiB,YAAb,CAA0B;AAAA;AAAA,sCAAMjB,MAAN,CAAakB,iBAAvC,EAA0DzB,OAAO,CAAC+B,IAAlE,CAAb;AACA,wBAAMC,UAAU,GAAG,MAAM,KAAKC,wBAAL,CACrBH,IADqB,EACflG,EAAE,CAAC0G,OAAO,CAACjD,QAAR,CAAiBsC,CAAjB,GAAqBkB,aAAtB,EAAqCP,OAAO,CAACjD,QAAR,CAAiBC,CAAjB,GAAqB2D,aAA1D,CADa,EAC6DrH,EAAE,CAAC0G,OAAO,CAACV,KAAR,CAAcD,CAAf,EAAkBW,OAAO,CAACV,KAAR,CAActC,CAAhC,CAD/D,EAErBgD,OAAO,CAACT,QAFa,EAEH,KAAKrE,WAFF,EAEgBuC,MAFhB,CAAzB;AAGA;AAAA;AAAA,4CAAS,cAAT,EAA0B,IAAD,uBAAI,KAAKrB,IAAL,CAAUkB,MAAd,oCAAI,oBAAkBA,MAAtB,qBAAI,oBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,mBAAkBmC,UAAU,CAACnC,IAAK,EAAhH;AACH,iBAND,MAMO;AACH,uBAAKqC,eAAL,CAAqB;AACjBH,oBAAAA,IAAI,EAAE/B,OAAO,CAAC+B,IADG;AAEjB9B,oBAAAA,IAAI,EAAE,EAFW;AAGjBoB,oBAAAA,MAAM,EAAErB,OAAO,CAACqB,MAHC;AAIjBhC,oBAAAA,QAAQ,EAAEzD,EAAE,CAAC0G,OAAO,CAACjD,QAAR,CAAiBsC,CAAjB,GAAqBkB,aAAtB,EAAqCP,OAAO,CAACjD,QAAR,CAAiBC,CAAjB,GAAqB2D,aAA1D,CAJK;AAKjBrB,oBAAAA,KAAK,EAAEhG,EAAE,CAAC0G,OAAO,CAACV,KAAR,CAAcD,CAAf,EAAkBW,OAAO,CAACV,KAAR,CAActC,CAAhC,CALQ;AAMjBuC,oBAAAA,QAAQ,EAAES,OAAO,CAACT;AAND,mBAArB,EAOG,KAAKrE,WAPR,EAOsBuC,MAPtB;AAQH;AACJ;AACJ,aArFD,MAqFO;AACH,mBAAKmC,eAAL,CAAqB;AACjBH,gBAAAA,IAAI,EAAE/B,OAAO,CAAC+B,IADG;AAEjB9B,gBAAAA,IAAI,EAAED,OAAO,CAACC,IAFG;AAGjBoB,gBAAAA,MAAM,EAAErB,OAAO,CAACqB,MAHC;AAIjBhC,gBAAAA,QAAQ,EAAEzD,EAAE,CAAC0G,OAAO,CAACjD,QAAR,CAAiBsC,CAAjB,GAAqBkB,aAAtB,EAAqCP,OAAO,CAACjD,QAAR,CAAiBC,CAAjB,GAAqB2D,aAA1D,CAJK;AAKjBrB,gBAAAA,KAAK,EAAEhG,EAAE,CAAC0G,OAAO,CAACV,KAAR,CAAcD,CAAf,EAAkBW,OAAO,CAACV,KAAR,CAActC,CAAhC,CALQ;AAMjBuC,gBAAAA,QAAQ,EAAES,OAAO,CAACT;AAND,eAArB,EAOG,KAAKrE,WAPR,EAOsBuC,MAPtB;AAQH;;AAEDA,YAAAA,MAAM;AACT;AACJ;;AAEmC,cAAtBhB,sBAAsB,CAACR,IAAD,EAAuB+E,WAAvB,EAA4D;AAC5F,cAAI,CAAC/E,IAAD,IAAS,KAAKb,aAAL,KAAuB,IAAhC,IAAwCa,IAAI,CAACgF,SAAL,CAAe5D,MAAf,KAA0B,CAAtE,EAAyE;AAAE;AAAS;;AAEpF,cAAI6D,KAAK,GAAG,CAAZ;;AACA,eAAK,MAAMC,QAAX,IAAuBlF,IAAI,CAACgF,SAA5B,EAAuC;AACnC,gBAAID,WAAJ,EAAiB;AACb,kBAAIG,QAAQ,CAACpE,QAAT,CAAkBC,CAAlB,GAAsBmE,QAAQ,CAACpC,MAAT,GAAkB,CAAxC,GAA4C,KAAK3C,IAAL,CAAUW,QAAV,CAAmBC,CAA/D,GAAmE;AAAA;AAAA,0CAAUgC,iBAAjF,EAAoG;AAAA;;AAChG,sBAAMQ,IAAI,GAAG;AAAA;AAAA,oCAAMvB,MAAN,CAAaiB,YAAb,CAA0B;AAAA;AAAA,oCAAMjB,MAAN,CAAakB,iBAAvC,EAA0DgC,QAAQ,CAAC1B,IAAnE,CAAb;AACA,sBAAM2B,MAAM,GAAG,MAAM,KAAKC,mBAAL,CACjB7B,IADiB,EACXlG,EAAE,CAAC6H,QAAQ,CAACpE,QAAT,CAAkBsC,CAAnB,EAAsB8B,QAAQ,CAACpE,QAAT,CAAkBC,CAAxC,CADS,EACmC1D,EAAE,CAAC6H,QAAQ,CAAC7B,KAAT,CAAeD,CAAhB,EAAmB8B,QAAQ,CAAC7B,KAAT,CAAetC,CAAlC,CADrC,EAEjBmE,QAAQ,CAAC5B,QAFQ,EAEE2B,KAFF,CAArB;AAGA;AAAA;AAAA,0CAAS,cAAT,EAA0B,IAAD,uBAAI,KAAK9E,IAAL,CAAUkB,MAAd,oCAAI,oBAAkBA,MAAtB,qBAAI,oBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,kBAAiB6D,MAAM,CAAC7D,IAAK,EAA3G;AACH,eAND,MAMO;AACH,qBAAK+D,gBAAL,CAAsBH,QAAtB;AACH;AACJ,aAVD,MAUO;AACH,mBAAKG,gBAAL,CAAsBH,QAAtB;AACH;;AACDD,YAAAA,KAAK;AACR;;AAED,eAAK1F,aAAL,CAAmBoB,IAAnB,CAAwB,CAACC,CAAD,EAAIC,CAAJ,KAAU;AAC9B,mBAAOD,CAAC,CAACE,QAAF,CAAWC,CAAX,GAAeF,CAAC,CAACC,QAAF,CAAWC,CAAjC;AACH,WAFD;AAGH;;AAEmC,cAAvBN,uBAAuB,CAACT,IAAD,EAAuB+E,WAAvB,EAA2D;AAC3F,cAAI,CAAC/E,IAAD,IAAS,KAAKd,WAAL,KAAqB,IAA9B,IAAsCc,IAAI,CAACsF,OAAL,CAAalE,MAAb,KAAwB,CAAlE,EAAqE;AAAE;AAAS,WADW,CAG3F;;;AACA,gBAAMyC,OAAiB,GAAG,EAA1B;AACA7D,UAAAA,IAAI,CAACsF,OAAL,CAAatE,OAAb,CAAqBuE,OAAO,IAAI;AAC5B1B,YAAAA,OAAO,CAAC3C,IAAR,CAAaqE,OAAO,CAACvB,MAArB;AACH,WAFD;AAGA,gBAAMwB,WAAW,GAAG;AAAA;AAAA,8BAAMtB,uBAAN,CAA8BL,OAA9B,EAAuC;AAAA;AAAA,kCAAQM,aAAR,CAAsBC,MAAtB,EAAvC,CAApB;AACA,eAAKhF,WAAL,GAAmBY,IAAI,CAACsF,OAAL,CAAaE,WAAb,CAAnB;;AAEA,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKrG,WAAL,CAAiBsG,KAAjB,CAAuBtE,MAA3C,EAAmDqE,CAAC,EAApD,EAAwD;AACpD,kBAAMlC,IAAI,GAAG;AAAA;AAAA,gCAAMvB,MAAN,CAAaiB,YAAb,CAA0B;AAAA;AAAA,gCAAMjB,MAAN,CAAakB,iBAAvC,EAA0D,KAAK9D,WAAL,CAAiBsG,KAAjB,CAAuBD,CAAvB,CAA1D,CAAb;AACA,kBAAMN,MAAM,GAAG,MAAM;AAAA;AAAA,gCAAMnD,MAAN,CAAaC,SAAb,CAAuBsB,IAAvB,EAA6BtG,MAA7B,CAArB;;AACA,iBAAKoC,cAAL,CAAoB6B,IAApB,CAAyBiE,MAAzB;AACH;;AAED,cAAIlF,OAAO,GAAG,CAAd;AACA,cAAI0F,UAAU,GAAG,CAAjB;;AACA,iBAAO,KAAKtG,cAAL,CAAoB+B,MAApB,GAA6B,CAA7B,IAAmCnB,OAAO,GAAG0F,UAAX,GAAyB;AAAA;AAAA,sCAAU5C,iBAA5E,EAA+F;AAC3F,kBAAMuB,aAAa,GAAG;AAAA;AAAA,oCAAQH,aAAR,CAAsBC,MAAtB,MAAkC,KAAKhF,WAAL,CAAiBmF,OAAjB,CAA0BC,GAA1B,GAAgC,KAAKpF,WAAL,CAAiBmF,OAAjB,CAA0BE,GAA5F,IAAmG,KAAKrF,WAAL,CAAiBmF,OAAjB,CAA0BE,GAAnJ;;AACA,kBAAMtE,IAAI,GAAG,KAAKyF,cAAL,CAAoBtB,aAApB,EAAmCrE,OAAnC,CAAb;;AACA,gBAAI4F,UAAU,GAAG,CAAjB;;AACA,gBAAI,KAAKzG,WAAL,CAAiB0G,YAAjB,KAAkC;AAAA;AAAA,wDAAkBC,WAAxD,EAAqE;AACjEF,cAAAA,UAAU,GAAG1F,IAAI,CAAC6F,YAAL,CAAkB5I,WAAlB,EAAgC6I,WAAhC,CAA4CnD,MAAzD;AACH,aAFD,MAEO,IAAI,KAAK1D,WAAL,CAAiB0G,YAAjB,KAAkC;AAAA;AAAA,wDAAkBI,UAAxD,EAAoE;AACvEL,cAAAA,UAAU,GAAG,IAAb;AACH,aAFM,MAEA,IAAI,KAAKzG,WAAL,CAAiB0G,YAAjB,KAAkC;AAAA;AAAA,wDAAkBK,aAAxD,EAAuE;AAC1EN,cAAAA,UAAU,GAAGO,IAAI,CAAC5B,GAAL,CAAS,KAAKpF,WAAL,CAAiBa,OAAjB,CAA0BwE,GAAnC,EAAuC,KAAKrF,WAAL,CAAiBa,OAAjB,CAA0BuE,GAAjE,IAAwErE,IAAI,CAAC6F,YAAL,CAAkB5I,WAAlB,EAAgC6I,WAAhC,CAA4CnD,MAAjI;AACH;;AAED7C,YAAAA,OAAO,IAAI4F,UAAX;AACAF,YAAAA,UAAU,GAAGE,UAAU,GAAG,CAA1B;AACA,iBAAKvG,qBAAL,GAA6BuG,UAA7B;AACH;AACJ;;AAEMQ,QAAAA,IAAI,CAACC,SAAD,EAA0B;AACjC,cAAIC,IAAI,GAAG,KAAKpG,IAAL,CAAUW,QAAV,CAAmBC,CAA9B;AACAwF,UAAAA,IAAI,IAAID,SAAS,GAAG,KAAKE,KAAzB;AACA,eAAKrG,IAAL,CAAUC,WAAV,CAAsB,CAAtB,EAAyBmG,IAAzB,EAA+B,CAA/B,EAHiC,CAKjC;AACA;;AACA,gBAAME,OAAO,GAAI,CAACF,IAAD,GAAQ;AAAA;AAAA,sCAAUG,YAAnC,CAPiC,CAQjC;;AACA,eAAK,IAAIjB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK5F,YAAL,CAAkBuB,MAAtC,EAA8CqE,CAAC,EAA/C,EAAmD;AAC/C,kBAAMkB,WAAW,GAAG,KAAK9G,YAAL,CAAkB4F,CAAlB,CAApB;AACAkB,YAAAA,WAAW,CAACN,IAAZ,CAAiBI,OAAjB;;AACA,gBAAIE,WAAW,CAACC,WAAhB,EAA6B;AACzB;AACA,mBAAK/G,YAAL,CAAkBgH,MAAlB,CAAyBpB,CAAzB,EAA4B,CAA5B;AACAA,cAAAA,CAAC;AACJ;AACJ;;AAED,cAAI,CAAC,KAAK9F,eAAV,EAA2B;AACvB,iBAAKmH,iBAAL,CAAuBP,IAAvB;AACH;;AACD,eAAKQ,YAAL;;AACA,cAAI,KAAKtH,gBAAT,EAA2B;AACvB,iBAAKuH,cAAL,CAAoBT,IAApB;AACH;AACJ;;AAEO7F,QAAAA,iBAAiB,GAAG;AACxB,eAAKhB,aAAL,CAAmBiB,IAAnB,CAAwB,CAACC,CAAD,EAAIC,CAAJ,KAAU;AAC9B,mBAAOD,CAAC,CAACZ,IAAF,CAAOc,QAAP,CAAgBC,CAAhB,GAAoBF,CAAC,CAACb,IAAF,CAAOc,QAAP,CAAgBC,CAA3C;AACH,WAFD;;AAIA;AAAA;AAAA,kCAAQ,cAAR,EAAyB,GAAE,KAAKZ,IAAL,CAAUmB,IAAK,YAAW,KAAK5B,aAAL,CAAmB0B,MAAO,EAA/E;AACA;AACR;AACA;AACK;;AAEOuC,QAAAA,eAAe,CACnBjB,WADmB,EAEnBuE,UAFmB,EAGnBzF,MAHmB,EAIrB;AAAA;;AACE,cAAI0F,IAAI,GAAG,EAAX;AACA,cAAIC,QAAQ,GAAG,EAAf;;AACA,cAAIzE,WAAW,CAAChB,IAAZ,CAAiBN,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B8F,YAAAA,IAAI,GAAG,IAAP;AACAC,YAAAA,QAAQ,GAAG;AAAA;AAAA,0CAAWrF,eAAX,CAA2B;AAAA;AAAA,gCAAME,MAAN,CAAaiB,YAAb,CAA0B;AAAA;AAAA,gCAAMjB,MAAN,CAAakB,iBAAvC,EAA0DR,WAAW,CAAChB,IAAZ,CAAiBG,OAAjB,CAAyB,MAAzB,EAAiC,EAAjC,CAA1D,CAA3B,CAAX;AACH,WAHD,MAGO;AACHqF,YAAAA,IAAI,GAAG,KAAP;AACAC,YAAAA,QAAQ,GAAG;AAAA;AAAA,0CAAWrF,eAAX,CAA2B;AAAA;AAAA,gCAAME,MAAN,CAAaiB,YAAb,CAA0B;AAAA;AAAA,gCAAMjB,MAAN,CAAakB,iBAAvC,EAA0DR,WAAW,CAACc,IAAtE,CAA3B,EAAuG,EAAvG,CAAX;AACH;;AACD;AAAA;AAAA,oCAAS,cAAT,EAA0B,IAAD,uBAAI,KAAKrD,IAAL,CAAUkB,MAAd,oCAAI,oBAAkBA,MAAtB,qBAAI,oBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,mBAAkB4F,IAAK,QAAOC,QAAS,SAAQF,UAAU,CAAC3F,IAAK,aAAYE,MAAO,EAAhK;;AACA,eAAK9B,aAAL,CAAmBwB,IAAnB,CAAwB;AACpBlB,YAAAA,IAAI,EAAE0C,WADc;AAEpBuE,YAAAA,UAAU,EAAEA,UAFQ;AAGpBzF,YAAAA,MAAM,EAAEA;AAHY,WAAxB;AAKH;;AAEO6D,QAAAA,gBAAgB,CAAE3C,WAAF,EAAkC;AAAA;;AACtD,eAAKnD,aAAL,CAAmB2B,IAAnB,CAAwBwB,WAAxB;;AACA;AAAA;AAAA,oCAAS,cAAT,EAA0B,IAAD,uBAAI,KAAKvC,IAAL,CAAUkB,MAAd,oCAAI,oBAAkBA,MAAtB,qBAAI,oBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,oBAAmB;AAAA;AAAA,wCAAWQ,eAAX,CAA2B;AAAA;AAAA,8BAAME,MAAN,CAAaiB,YAAb,CAA0B;AAAA;AAAA,8BAAMjB,MAAN,CAAakB,iBAAvC,EAA0DR,WAAW,CAACc,IAAtE,CAA3B,CAAwG,EAAzM;AACH,SApYwC,CAsYzC;;;AAC+B,cAAjBsD,iBAAiB,CAAChC,OAAD,EAAkB;AAC7C,cAAI,CAAC,KAAK3E,IAAL,CAAUkB,MAAX,IAAqB,CAAC,KAAKlB,IAAL,CAAUkB,MAAV,CAAiBA,MAA3C,EAAmD;AAEnD,cAAI,KAAK1B,eAAT,EAA0B;AAE1B,eAAKA,eAAL,GAAuB,IAAvB;;AACA,cAAI;AACA,kBAAMyH,eAAyB,GAAG,EAAlC;AACA,kBAAMC,eAA8B,GAAG,EAAvC;;AACA,iBAAK,IAAI5B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK/F,aAAL,CAAmB0B,MAAvC,EAA+CqE,CAAC,EAAhD,EAAoD;AAChD,oBAAM6B,WAAW,GAAG,KAAK5H,aAAL,CAAmB+F,CAAnB,CAApB,CADgD,CAGhD;AACA;;AACA,kBAAI6B,WAAW,CAACtH,IAAZ,CAAiBc,QAAjB,CAA0BC,CAA1B,GAA8BuG,WAAW,CAACtH,IAAZ,CAAiB8C,MAAjB,GAA0B,CAAxD,GAA4DgC,OAA5D,GAAsE;AAAA;AAAA,0CAAU/B,iBAApF,EAAuG;AACnG;AACH;;AAED,kBAAIuE,WAAW,CAACtH,IAAZ,CAAiB0B,IAAjB,CAAsBN,MAAtB,GAA+B,CAAnC,EAAsC;AAClC,oBAAIkG,WAAW,CAACtH,IAAZ,CAAiB0B,IAAjB,CAAsBC,QAAtB,CAA+B,OAA/B,CAAJ,EAA6C;AACzC,wBAAMC,SAAS,GAAG/C,YAAY,GAAGyI,WAAW,CAACtH,IAAZ,CAAiB0B,IAAjB,CAAsBG,OAAtB,CAA8B,OAA9B,EAAuC,EAAvC,CAAjC;AACA,wBAAME,SAAS,GAAG,MAAM;AAAA;AAAA,sCAAMC,MAAN,CAAaC,SAAb,CAAuBL,SAAvB,EAAkC7E,SAAlC,CAAxB;AACA,wBAAMmF,QAAQ,GAAGH,SAAS,CAACI,IAA3B;;AACA,sBAAID,QAAQ,CAAEf,QAAV,IAAsBiB,KAAK,CAACC,OAAN,CAAcH,QAAQ,CAAEf,QAAxB,CAA1B,EAA6D;AAAA;;AACzD,wBAAImB,SAAS,GAAG,CAAhB;AACA,0BAAMC,aAAa,GAAG;AAAA;AAAA,kDAAWlC,YAAX,CAAwBiH,WAAW,CAACL,UAApC,EAAkD,QAAOK,WAAW,CAAC9F,MAAO,EAA5E,CAAtB;AACA;AAAA;AAAA,8CAAS,cAAT,EAA0B,IAAD,uBAAI,KAAKrB,IAAL,CAAUkB,MAAd,oCAAI,oBAAkBA,MAAtB,qBAAI,oBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,sCAAqCiB,aAAa,CAACjB,IAAK,QAAOiB,aAAa,CAAClB,MAAd,CAAsBC,IAAK,EAAxK;AACAiB,oBAAAA,aAAa,CAACC,eAAd,CAA8B8E,WAAW,CAAC9F,MAA1C;;AACA,yBAAK,MAAMiB,CAAX,IAAgBP,QAAQ,CAAEf,QAA1B,EAAoC;AAChC,4BAAMuB,WAAW,GAAGC,MAAM,CAACC,MAAP,CAAc;AAAA;AAAA,iEAAd,EAAsCH,CAAtC,CAApB;;AACA,0BAAK6E,WAAW,CAACtH,IAAZ,CAAiBc,QAAjB,CAA0BC,CAA1B,GAA8B2B,WAAW,CAAC5B,QAAZ,CAAqBC,CAAnD,GAAuD2B,WAAW,CAACI,MAAZ,GAAqB,CAA5E,GAAgFgC,OAAjF,IAA6F;AAAA;AAAA,kDAAU/B,iBAA3G,EAA8H;AAC1H,4BAAIL,WAAW,CAAChB,IAAZ,CAAiBC,QAAjB,CAA0B,MAA1B,CAAJ,EAAuC;AAAA;;AACnC,gCAAMqB,QAAQ,GAAG;AAAA;AAAA,8CAAMhB,MAAN,CAAaiB,YAAb,CAA0B;AAAA;AAAA,8CAAMjB,MAAN,CAAakB,iBAAvC,EAA0DR,WAAW,CAAChB,IAAZ,CAAiBG,OAAjB,CAAyB,MAAzB,EAAiC,EAAjC,CAA1D,CAAjB;AACA;AAAA;AAAA,oDAAS,cAAT,EAA0B,IAAD,uBAAI,KAAK1B,IAAL,CAAUkB,MAAd,oCAAI,oBAAkBA,MAAtB,qBAAI,oBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,kBAAiB0B,QAAS,WAAUyC,CAAE,EAApH;AACA,gCAAM,KAAKtC,cAAL,CACFH,QADE,EACQ3F,EAAE,CAACiK,WAAW,CAACtH,IAAZ,CAAiBc,QAAjB,CAA0BsC,CAA1B,GAA8BV,WAAW,CAAC5B,QAAZ,CAAqBsC,CAApD,EAAuDkE,WAAW,CAACtH,IAAZ,CAAiBc,QAAjB,CAA0BC,CAA1B,GAA8B2B,WAAW,CAAC5B,QAAZ,CAAqBC,CAA1G,CADV,EACwH1D,EAAE,CAACqF,WAAW,CAACW,KAAZ,CAAkBD,CAAnB,EAAsBV,WAAW,CAACW,KAAZ,CAAkBtC,CAAxC,CAD1H,EAEF2B,WAAW,CAACY,QAFV,EAEoBf,aAFpB,EAEoCD,SAFpC,CAAN;AAGH,yBAND,MAMO;AAAA;;AACH,gCAAMiB,IAAI,GAAG;AAAA;AAAA,8CAAMvB,MAAN,CAAaiB,YAAb,CAA0B;AAAA;AAAA,8CAAMjB,MAAN,CAAakB,iBAAvC,EAA0DR,WAAW,CAACc,IAAtE,CAAb;AACA,gCAAMC,UAAU,GAAG,MAAM,KAAKC,wBAAL,CACrBH,IADqB,EACflG,EAAE,CAACiK,WAAW,CAACtH,IAAZ,CAAiBc,QAAjB,CAA0BsC,CAA1B,GAA8BV,WAAW,CAAC5B,QAAZ,CAAqBsC,CAApD,EAAuDkE,WAAW,CAACtH,IAAZ,CAAiBc,QAAjB,CAA0BC,CAA1B,GAA8B2B,WAAW,CAAC5B,QAAZ,CAAqBC,CAA1G,CADa,EACiG1D,EAAE,CAACqF,WAAW,CAACW,KAAZ,CAAkBD,CAAnB,EAAsBV,WAAW,CAACW,KAAZ,CAAkBtC,CAAxC,CADnG,EAErB2B,WAAW,CAACY,QAFS,EAECf,aAFD,EAEiBD,SAFjB,CAAzB;AAGA;AAAA;AAAA,oDAAS,cAAT,EAA0B,IAAD,uBAAI,KAAKnC,IAAL,CAAUkB,MAAd,oCAAI,oBAAkBA,MAAtB,qBAAI,oBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,mCAAkCmC,UAAU,CAACnC,IAAK,WAAUmE,CAAE,EAA5I;AACH;AACJ,uBAdD,MAcO;AAAA;;AACH,8BAAMnE,IAAI,GAAG;AAAA;AAAA,4CAAMU,MAAN,CAAaiB,YAAb,CAA0B;AAAA;AAAA,4CAAMjB,MAAN,CAAakB,iBAAvC,EAA0DR,WAAW,CAACc,IAAtE,CAAb;AACA;AAAA;AAAA,kDAAS,cAAT,EAA0B,IAAD,uBAAI,KAAKrD,IAAL,CAAUkB,MAAd,oCAAI,oBAAkBA,MAAtB,qBAAI,oBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,6BAA4BA,IAAK,gBAAeiB,aAAa,CAACjB,IAAK,WAAUmE,CAAE,eAAcnD,SAAU,EAArL;AACA+E,wBAAAA,eAAe,CAACnG,IAAhB,CAAqB;AACjBlB,0BAAAA,IAAI,EAAE;AACFwD,4BAAAA,IAAI,EAAEd,WAAW,CAACc,IADhB;AAEF9B,4BAAAA,IAAI,EAAEgB,WAAW,CAAChB,IAFhB;AAGFoB,4BAAAA,MAAM,EAAEJ,WAAW,CAACI,MAHlB;AAIFhC,4BAAAA,QAAQ,EAAEzD,EAAE,CAACiK,WAAW,CAACtH,IAAZ,CAAiBc,QAAjB,CAA0BsC,CAA1B,GAA8BV,WAAW,CAAC5B,QAAZ,CAAqBsC,CAApD,EAAuDkE,WAAW,CAACtH,IAAZ,CAAiBc,QAAjB,CAA0BC,CAA1B,GAA8B2B,WAAW,CAAC5B,QAAZ,CAAqBC,CAA1G,CAJV;AAKFsC,4BAAAA,KAAK,EAAEhG,EAAE,CAACqF,WAAW,CAACW,KAAZ,CAAkBD,CAAnB,EAAsBV,WAAW,CAACW,KAAZ,CAAkBtC,CAAxC,CALP;AAMFuC,4BAAAA,QAAQ,EAAEZ,WAAW,CAACY;AANpB,2BADW;AASjB2D,0BAAAA,UAAU,EAAE1E,aATK;AAUjBf,0BAAAA,MAAM,EAAEc;AAVS,yBAArB;AAYH;;AACDA,sBAAAA,SAAS;AACZ;;AAED8E,oBAAAA,eAAe,CAAClG,IAAhB,CAAqBuE,CAArB;AACH;AACJ,iBA9CD,MA8CO,IAAI6B,WAAW,CAACtH,IAAZ,CAAiB0B,IAAjB,CAAsBC,QAAtB,CAA+B,MAA/B,CAAJ,EAA4C;AACnD,sBAAK2F,WAAW,CAACtH,IAAZ,CAAiBc,QAAjB,CAA0BC,CAA1B,GAA8BuG,WAAW,CAACtH,IAAZ,CAAiB8C,MAAjB,GAA0B,CAAxD,GAA4DgC,OAA7D,IAAyE;AAAA;AAAA,8CAAU/B,iBAAvF,EAA0G;AAClG,0BAAMC,QAAQ,GAAG;AAAA;AAAA,wCAAMhB,MAAN,CAAaiB,YAAb,CAA0B;AAAA;AAAA,wCAAMjB,MAAN,CAAakB,iBAAvC,EAA0DoE,WAAW,CAACtH,IAAZ,CAAiB0B,IAAjB,CAAsBG,OAAtB,CAA8B,MAA9B,EAAsC,EAAtC,CAA1D,CAAjB;AACA,0BAAM,KAAKsB,cAAL,CACFH,QADE,EACQ3F,EAAE,CAACiK,WAAW,CAACtH,IAAZ,CAAiBc,QAAjB,CAA0BsC,CAA3B,EAA8BkE,WAAW,CAACtH,IAAZ,CAAiBc,QAAjB,CAA0BC,CAAxD,CADV,EACsE1D,EAAE,CAACiK,WAAW,CAACtH,IAAZ,CAAiBqD,KAAjB,CAAuBD,CAAxB,EAA2BkE,WAAW,CAACtH,IAAZ,CAAiBqD,KAAjB,CAAuBtC,CAAlD,CADxE,EAEFuG,WAAW,CAACtH,IAAZ,CAAiBsD,QAFf,EAEyBgE,WAAW,CAACL,UAFrC,EAEkDK,WAAW,CAAC9F,MAF9D,CAAN;AAGA4F,oBAAAA,eAAe,CAAClG,IAAhB,CAAqBuE,CAArB;AACH;AACJ;AACJ,eAxDD,MAwDO;AACH,oBAAK6B,WAAW,CAACtH,IAAZ,CAAiBc,QAAjB,CAA0BC,CAA1B,GAA8BuG,WAAW,CAACtH,IAAZ,CAAiB8C,MAAjB,GAA0B,CAAxD,GAA4DgC,OAA7D,IAAyE;AAAA;AAAA,4CAAU/B,iBAAvF,EAA0G;AAAA;;AACtG,wBAAMQ,IAAI,GAAG;AAAA;AAAA,sCAAMvB,MAAN,CAAaiB,YAAb,CAA0B;AAAA;AAAA,sCAAMjB,MAAN,CAAakB,iBAAvC,EAA0DoE,WAAW,CAACtH,IAAZ,CAAiBwD,IAA3E,CAAb;AACA,wBAAMC,UAAU,GAAG,MAAM,KAAKC,wBAAL,CACrBH,IADqB,EACflG,EAAE,CAACiK,WAAW,CAACtH,IAAZ,CAAiBc,QAAjB,CAA0BsC,CAA3B,EAA8BkE,WAAW,CAACtH,IAAZ,CAAiBc,QAAjB,CAA0BC,CAAxD,CADa,EAC+C1D,EAAE,CAACiK,WAAW,CAACtH,IAAZ,CAAiBqD,KAAjB,CAAuBD,CAAxB,EAA2BkE,WAAW,CAACtH,IAAZ,CAAiBqD,KAAjB,CAAuBtC,CAAlD,CADjD,EAErBuG,WAAW,CAACtH,IAAZ,CAAiBsD,QAFI,EAEMgE,WAAW,CAACL,UAFlB,EAE+BK,WAAW,CAAC9F,MAF3C,CAAzB;AAGA;AAAA;AAAA,4CAAS,cAAT,EAA0B,IAAD,uBAAI,KAAKrB,IAAL,CAAUkB,MAAd,oCAAI,oBAAkBA,MAAtB,qBAAI,oBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,yBAAwBmC,UAAU,CAACnC,IAAK,QAAOgG,WAAW,CAACL,UAAZ,CAAwB3F,IAAK,SAAQgG,WAAW,CAAC9F,MAAO,EAArL;AACA4F,kBAAAA,eAAe,CAAClG,IAAhB,CAAqBuE,CAArB;AACH;AACJ;AACJ;;AACD,iBAAK,IAAIA,CAAC,GAAG2B,eAAe,CAAChG,MAAhB,GAAyB,CAAtC,EAAyCqE,CAAC,IAAI,CAA9C,EAAiDA,CAAC,EAAlD,EAAsD;AAClD,mBAAK/F,aAAL,CAAmBmH,MAAnB,CAA0BO,eAAe,CAAC3B,CAAD,CAAzC,EAA8C,CAA9C;AACH;;AACD,gBAAI4B,eAAe,CAACjG,MAAhB,GAAyB,CAA7B,EAAgC;AAC5B,mBAAK1B,aAAL,CAAmBwB,IAAnB,CAAwB,GAAGmG,eAA3B;;AACA,mBAAK3G,iBAAL;AACH;AACJ,WAtFD,CAsFE,OAAO6G,KAAP,EAAc;AACZ;AAAA;AAAA,sCAAS,cAAT,EAA0B,+BAA8BA,KAAM,EAA9D;AACH,WAxFD,SAwFU;AACN,iBAAK5H,eAAL,GAAuB,KAAvB;AACH;AACJ;;AAEOoH,QAAAA,YAAY,GAAS;AACzB,cAAI,CAAC,KAAK7H,WAAN,IAAqB,KAAKG,cAAL,CAAoB+B,MAApB,KAA+B,CAAxD,EAA2D;AAE3D,gBAAMoG,SAAS,GAAG,KAAKtI,WAAL,CAAiBuI,QAAjB,CAA0B,KAAKvI,WAAL,CAAiBuI,QAAjB,CAA0BrG,MAA1B,GAAmC,CAA7D,CAAlB;AACA,gBAAMsG,kBAAkB,GAAGF,SAAS,CAACxB,YAAV,CAAuB5I,WAAvB,CAA3B;AAEA,cAAI,CAACsK,kBAAL,EAAyB;AAEzB,gBAAMC,YAAY,GAAG,KAAKxH,IAAL,CAAUW,QAAV,CAAmBC,CAAnB,GAAuByG,SAAS,CAAC1G,QAAV,CAAmBC,CAA1C,GAA8C,KAAKzB,qBAAL,GAA6B,CAAhG;;AAEA,cAAIqI,YAAY,GAAG;AAAA;AAAA,sCAAUjB,YAA7B,EAA2C;AACvC,kBAAMkB,IAAI,GAAGJ,SAAS,CAAC1G,QAAV,CAAmBC,CAAnB,GAAuB,KAAKzB,qBAAzC;;AACA,kBAAMgF,aAAa,GAAG;AAAA;AAAA,oCAAQH,aAAR,CAAsBC,MAAtB,MAAkC,KAAKhF,WAAL,CAAkBmF,OAAlB,CAA2BC,GAA3B,GAAiC,KAAKpF,WAAL,CAAkBmF,OAAlB,CAA2BE,GAA9F,IAAqG,KAAKrF,WAAL,CAAkBmF,OAAlB,CAA2BE,GAAtJ;;AACA,kBAAMoD,OAAO,GAAG,KAAKjC,cAAL,CAAoBtB,aAApB,EAAmCsD,IAAnC,CAAhB;;AACA,gBAAI/B,UAAU,GAAG,CAAjB;;AACA,gBAAI,KAAKzG,WAAL,CAAkB0G,YAAlB,KAAmC;AAAA;AAAA,wDAAkBC,WAAzD,EAAsE;AAClEF,cAAAA,UAAU,GAAGgC,OAAO,CAAC7B,YAAR,CAAqB5I,WAArB,EAAmC6I,WAAnC,CAA+CnD,MAA5D;AACH,aAFD,MAEO,IAAI,KAAK1D,WAAL,CAAkB0G,YAAlB,KAAmC;AAAA;AAAA,wDAAkBI,UAAzD,EAAqE;AACxEL,cAAAA,UAAU,GAAG,IAAb;AACH,aAFM,MAEA,IAAI,KAAKzG,WAAL,CAAkB0G,YAAlB,KAAmC;AAAA;AAAA,wDAAkBK,aAAzD,EAAwE;AAC3EN,cAAAA,UAAU,GAAGO,IAAI,CAAC5B,GAAL,CAAS,KAAKpF,WAAL,CAAkBa,OAAlB,CAA2BwE,GAApC,EAAwC,KAAKrF,WAAL,CAAkBa,OAAlB,CAA2BuE,GAAnE,IAA0EqD,OAAO,CAAC7B,YAAR,CAAqB5I,WAArB,EAAmC6I,WAAnC,CAA+CnD,MAAtI;AACH;;AACD,iBAAKxD,qBAAL,GAA6BuG,UAA7B;AACH;AACJ;;AAE2B,cAAdmB,cAAc,CAACT,IAAD,EAAe;AACvC,cAAI,CAAC,KAAKpH,aAAN,IAAuB,KAAKI,aAAL,CAAmB6B,MAAnB,KAA8B,CAAzD,EAA4D;AAE5D,cAAI,CAAC,KAAK3B,gBAAV,EAA4B;AAE5B,eAAKA,gBAAL,GAAwB,IAAxB;;AACA,cAAI;AACA,kBAAM2H,eAAyB,GAAG,EAAlC;;AACA,iBAAK,IAAI3B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKlG,aAAL,CAAmB6B,MAAvC,EAA+CqE,CAAC,EAAhD,EAAoD;AAChD,oBAAMqC,YAAY,GAAG,KAAKvI,aAAL,CAAmBkG,CAAnB,CAArB;;AACA,kBAAIqC,YAAY,CAAChH,QAAb,CAAsBC,CAAtB,GAA0B+G,YAAY,CAAChF,MAAb,GAAsB,CAAhD,GAAoDyD,IAApD,GAA2D;AAAA;AAAA,0CAAUxD,iBAAzE,EAA4F;AACxF;AACH,eAFD,MAEO;AAAA;;AACH,sBAAMQ,IAAI,GAAG;AAAA;AAAA,oCAAMvB,MAAN,CAAaiB,YAAb,CAA0B;AAAA;AAAA,oCAAMjB,MAAN,CAAakB,iBAAvC,EAA0D4E,YAAY,CAACtE,IAAvE,CAAb;AACA,sBAAMuE,YAAY,GAAG,MAAM,KAAK3C,mBAAL,CACvB7B,IADuB,EACjBlG,EAAE,CAACyK,YAAY,CAAChH,QAAb,CAAsBsC,CAAvB,EAA0B0E,YAAY,CAAChH,QAAb,CAAsBC,CAAhD,CADe,EACqC1D,EAAE,CAACyK,YAAY,CAACzE,KAAb,CAAmBD,CAApB,EAAuB0E,YAAY,CAACzE,KAAb,CAAmBtC,CAA1C,CADvC,EAEvB+G,YAAY,CAACxE,QAFU,EAEAmC,CAFA,CAA3B;AAGA;AAAA;AAAA,0CAAS,cAAT,EAA0B,IAAD,uBAAI,KAAKtF,IAAL,CAAUkB,MAAd,oCAAI,oBAAkBA,MAAtB,qBAAI,oBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,0BAAyByG,YAAY,CAACzG,IAAK,QAAO,KAAKnC,aAAL,CAAoBmC,IAAK,SAAQmE,CAAE,EAAnK;AACA2B,gBAAAA,eAAe,CAAClG,IAAhB,CAAqBuE,CAArB;AACH;AACJ;;AAED,iBAAK,IAAIA,CAAC,GAAG2B,eAAe,CAAChG,MAAhB,GAAyB,CAAtC,EAAyCqE,CAAC,IAAI,CAA9C,EAAiDA,CAAC,EAAlD,EAAsD;AAClD,mBAAKlG,aAAL,CAAmBsH,MAAnB,CAA0BO,eAAe,CAAC3B,CAAD,CAAzC,EAA8C,CAA9C;AACH;AACJ,WAnBD,CAmBE,OAAO8B,KAAP,EAAc;AACZ;AAAA;AAAA,sCAAS,cAAT,EAA0B,yBAAwBA,KAAM,EAAxD;AACH,WArBD,SAqBU;AACN,iBAAK9H,gBAAL,GAAwB,KAAxB;AACH;;AAED,eAAK,IAAIgG,CAAC,GAAG,KAAKjG,sBAAL,CAA4B4B,MAA5B,GAAqC,CAAlD,EAAqDqE,CAAC,IAAI,CAA1D,EAA6DA,CAAC,EAA9D,EAAkE;AAC9D,kBAAMtF,IAAI,GAAG,KAAKX,sBAAL,CAA4BiG,CAA5B,CAAb;;AAEA,gBAAI,CAACtF,IAAI,CAAC6H,OAAV,EAAmB;AACf,mBAAKxI,sBAAL,CAA4BqH,MAA5B,CAAmCpB,CAAnC,EAAsC,CAAtC;;AACA;AACH;;AAED,kBAAMwC,IAAI,GAAG9H,IAAI,CAAC6F,YAAL;AAAA;AAAA,mDAAb;;AACA,gBAAI,CAACiC,IAAL,EAAW;AACP,mBAAKzI,sBAAL,CAA4BqH,MAA5B,CAAmCpB,CAAnC,EAAsC,CAAtC;;AACA;AACH;;AAED,gBAAIwC,IAAI,CAACC,MAAL,KAAgB;AAAA;AAAA,kDAAeC,QAA/B,IAA4C5B,IAAI,GAAGpG,IAAI,CAACW,QAAL,CAAcC,CAAtB,IAA4B;AAAA;AAAA,wCAAU2F,YAArF,EAAmG;AAC/FuB,cAAAA,IAAI,CAACG,aAAL;;AACA,kBAAIH,IAAI,CAACI,MAAL,KAAgB,KAApB,EAA2B;AACvB,qBAAK7B,KAAL,GAAa,CAAb;AACH;;AACD,mBAAKhH,sBAAL,CAA4BqH,MAA5B,CAAmCpB,CAAnC,EAAsC,CAAtC;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACgC,cAAdtC,cAAc,CACxBmF,eADwB,EAExBxH,QAFwB,EAGxBuC,KAHwB,EAIxBC,QAJwB,EAKxB2D,UALwB,EAMxBzF,MANwB,EAOJ;AACpB,cAAI,CAAC8G,eAAe,CAAC3G,QAAhB,CAAyB,cAAzB,CAAL,EAA+C;AAAA;;AAC3C;AAAA;AAAA,sCAAS,cAAT,EAA0B,IAAD,uBAAI,KAAKxB,IAAL,CAAUkB,MAAd,oCAAI,oBAAkBA,MAAtB,qBAAI,oBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,iBAAgBgH,eAAgB,EAA9G;AACA,mBAAO,IAAP;AACH;;AAED,gBAAMhH,IAAI,GAAG;AAAA;AAAA,wCAAWQ,eAAX,CAA2BwG,eAA3B,CAAb;AACA,cAAIC,WAAW,GAAG;AAAA;AAAA,kCAAQC,cAAR,CAAuBC,oBAAvB,CAA4CC,GAA5C,CACdnK,iBADc,EAEd+C,IAFc,CAAlB;;AAKA,cAAIiH,WAAJ,EAAiB;AAAA;;AACb;AACA;AAAA;AAAA,sCAAS,cAAT,EAA0B,IAAD,uBAAI,KAAKpI,IAAL,CAAUkB,MAAd,oCAAI,oBAAkBA,MAAtB,qBAAI,oBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,SAAQ/C,iBAAkB,gBAAe+C,IAAK,EAA5H;AACH,WAHD,MAGO;AAAA;;AACH,kBAAMqH,WAAW,GAAG,MAAM;AAAA;AAAA,gCAAM3G,MAAN,CAAaC,SAAb,CAAuBqG,eAAvB,EAAwCnL,WAAxC,CAA1B;AACA;AAAA;AAAA,sCAAS,cAAT,EAA0B,IAAD,uBAAI,KAAKgD,IAAL,CAAUkB,MAAd,oCAAI,oBAAkBA,MAAtB,qBAAI,oBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,kBAAiBA,IAAK,QAAO2F,UAAU,CAAC3F,IAAK,SAAQE,MAAO,EAA1I;AACA+G,YAAAA,WAAW,GAAG,IAAIvL,IAAJ,EAAd;AACAuL,YAAAA,WAAW,CAACjH,IAAZ,GAAmBA,IAAnB;AACA,kBAAMsH,aAAa,GAAGL,WAAW,CAACM,YAAZ,CAAyB3L,MAAzB,CAAtB;AACA0L,YAAAA,aAAa,CAACD,WAAd,GAA4BA,WAA5B;AACA,kBAAMG,WAAW,GAAGP,WAAW,CAACvC,YAAZ,CAAyB5I,WAAzB,CAApB;;AACA,gBAAI0L,WAAW,IAAIH,WAAnB,EAAgC;AAC5B,oBAAMI,IAAI,GAAGJ,WAAW,CAACK,YAAzB;AACAF,cAAAA,WAAW,CAACG,cAAZ,CAA2BF,IAAI,CAACG,KAAhC,EAAuCH,IAAI,CAACjG,MAA5C,EAF4B,CAG5B;AACH;;AACD,kBAAMqG,QAAQ,GAAGZ,WAAW,CAACM,YAAZ;AAAA;AAAA,mEAAjB;AACAM,YAAAA,QAAQ,CAACC,IAAT,CAAc7K,iBAAd;AACH;;AAEDgK,UAAAA,WAAW,CAACnI,WAAZ,CAAwBU,QAAQ,CAACsC,CAAjC,EAAoCtC,QAAQ,CAACC,CAA7C,EAAgD,CAAhD;AACAwH,UAAAA,WAAW,CAACc,QAAZ,CAAqBhG,KAAK,CAACD,CAA3B,EAA8BC,KAAK,CAACtC,CAApC;AACAwH,UAAAA,WAAW,CAACe,oBAAZ,CAAiC,CAAjC,EAAoC,CAApC,EAAuChG,QAAvC;AAEA2D,UAAAA,UAAU,CAACsC,QAAX,CAAoBhB,WAApB;AACAA,UAAAA,WAAW,CAAC/F,eAAZ,CAA4BhB,MAA5B;AAEA,iBAAO+G,WAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAC0C,cAAxB7E,wBAAwB,CAClC8F,UADkC,EAElC1I,QAFkC,EAGlCuC,KAHkC,EAIlCC,QAJkC,EAKlC2D,UALkC,EAMlCzF,MANkC,EAOrB;AACb,gBAAMF,IAAI,GAAG;AAAA;AAAA,wCAAWQ,eAAX,CAA2B0H,UAA3B,EAAsC,EAAtC,CAAb;AACA,cAAIjB,WAAW,GAAG;AAAA;AAAA,kCAAQC,cAAR,CAAuBC,oBAAvB,CAA4CC,GAA5C,CACdnK,iBADc,EAEd+C,IAFc,CAAlB;;AAIA,cAAI,CAACiH,WAAL,EAAkB;AAAA;;AACd,kBAAMpD,MAAM,GAAG,MAAM;AAAA;AAAA,gCAAMnD,MAAN,CAAaC,SAAb,CAAuBuH,UAAvB,EAAmCvM,MAAnC,CAArB;AACAsL,YAAAA,WAAW,GAAGzL,WAAW,CAACqI,MAAD,CAAzB;AACA,kBAAMgE,QAAQ,GAAGZ,WAAW,CAACM,YAAZ;AAAA;AAAA,mEAAjB;AACAM,YAAAA,QAAQ,CAACC,IAAT,CAAc7K,iBAAd;AACA;AAAA;AAAA,sCAAS,cAAT,EAA0B,IAAD,uBAAI,KAAK4B,IAAL,CAAUkB,MAAd,oCAAI,oBAAkBA,MAAtB,qBAAI,oBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,qBAAoBA,IAAK,QAAO2F,UAAU,CAAC3F,IAAK,SAAQE,MAAO,EAA7I;AACH,WAND,MAMO;AAAA;;AACH;AAAA;AAAA,sCAAS,cAAT,EAA0B,IAAD,uBAAI,KAAKrB,IAAL,CAAUkB,MAAd,oCAAI,oBAAkBA,MAAtB,qBAAI,oBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,SAAQ/C,iBAAkB,mBAAkB+C,IAAK,EAA/H;AACH;;AAEDiH,UAAAA,WAAW,CAACnI,WAAZ,CAAwBU,QAAQ,CAACsC,CAAjC,EAAoCtC,QAAQ,CAACC,CAA7C,EAAgD,CAAhD;AACAwH,UAAAA,WAAW,CAACc,QAAZ,CAAqBhG,KAAK,CAACD,CAA3B,EAA8BC,KAAK,CAACtC,CAApC;AACAwH,UAAAA,WAAW,CAACe,oBAAZ,CAAiC,CAAjC,EAAoC,CAApC,EAAuChG,QAAvC;AACA2D,UAAAA,UAAU,CAACsC,QAAX,CAAoBhB,WAApB;AACAA,UAAAA,WAAW,CAAC/F,eAAZ,CAA4BhB,MAA5B;AAEA,iBAAO+G,WAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACqC,cAAnBnD,mBAAmB,CAC7BoE,UAD6B,EAE7B1I,QAF6B,EAG7BuC,KAH6B,EAI7BC,QAJ6B,EAK7B9B,MAL6B,EAMhB;AACb,gBAAMF,IAAI,GAAG;AAAA;AAAA,wCAAWQ,eAAX,CAA2B0H,UAA3B,EAAsC,EAAtC,CAAb;AACA,cAAIzB,YAAY,GAAG;AAAA;AAAA,kCAAQS,cAAR,CAAuBC,oBAAvB,CAA4CC,GAA5C,CACflK,iBADe,EAEf8C,IAFe,CAAnB;;AAIA,cAAI,CAACyG,YAAL,EAAmB;AAAA;;AACf,kBAAM5C,MAAM,GAAG,MAAM;AAAA;AAAA,gCAAMnD,MAAN,CAAaC,SAAb,CAAuBuH,UAAvB,EAAmCvM,MAAnC,CAArB;AACA8K,YAAAA,YAAY,GAAGjL,WAAW,CAACqI,MAAD,CAA1B;AACA,kBAAMsE,eAAe,GAAG1B,YAAY,CAACc,YAAb;AAAA;AAAA,mDAAxB;AACAY,YAAAA,eAAe,CAACL,IAAhB,CAAqB5K,iBAArB;AACA;AAAA;AAAA,sCAAS,cAAT,EAA0B,IAAD,uBAAI,KAAK2B,IAAL,CAAUkB,MAAd,oCAAI,oBAAkBA,MAAtB,qBAAI,oBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,cAAaA,IAAK,QAAO,KAAKnC,aAAL,CAAoBmC,IAAK,SAAQE,MAAO,EAA/I;AACH,WAND,MAMO;AAAA;;AACH;AAAA;AAAA,sCAAS,cAAT,EAA0B,IAAD,uBAAI,KAAKrB,IAAL,CAAUkB,MAAd,oCAAI,oBAAkBA,MAAtB,qBAAI,oBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,SAAQ9C,iBAAkB,YAAW8C,IAAK,EAAxH;AACH;;AAEDyG,UAAAA,YAAY,CAAC3H,WAAb,CAAyBU,QAAQ,CAACsC,CAAlC,EAAqCtC,QAAQ,CAACC,CAA9C,EAAiD,CAAjD;AACAgH,UAAAA,YAAY,CAACsB,QAAb,CAAsBhG,KAAK,CAACD,CAA5B,EAA+BC,KAAK,CAACtC,CAArC;AACAgH,UAAAA,YAAY,CAACuB,oBAAb,CAAkC,CAAlC,EAAqC,CAArC,EAAwChG,QAAxC;AACA,eAAKnE,aAAL,CAAoBoK,QAApB,CAA6BxB,YAA7B;AACAA,UAAAA,YAAY,CAACvF,eAAb,CAA6BhB,MAA7B;AAEA,iBAAOuG,YAAP;AACH;;AAEOnC,QAAAA,cAAc,CAAC8D,IAAD,EAAcC,IAAd,EAAkC;AACpD,gBAAM1E,KAAK,GAAG,KAAK/F,WAAL,CAAkBuI,QAAlB,CAA2BrG,MAA3B,GAAoC,KAAK/B,cAAL,CAAoB+B,MAAtE;AACA,gBAAM+D,MAAM,GAAG,KAAK9F,cAAL,CAAoB4F,KAApB,CAAf;AACA,gBAAM2E,UAAU,GAAGzE,MAAM,CAAC7D,IAA1B;AACA,cAAInB,IAAI,GAAG;AAAA;AAAA,kCAAQqI,cAAR,CAAuBC,oBAAvB,CAA4CC,GAA5C,CAAgDpK,gBAAhD,EAAkEsL,UAAlE,CAAX;;AACA,cAAI,CAACzJ,IAAL,EAAW;AACPA,YAAAA,IAAI,GAAGrD,WAAW,CAAC,KAAKuC,cAAL,CAAoB4F,KAApB,CAAD,CAAlB;AACA,kBAAMkE,QAAQ,GAAGhJ,IAAI,CAAC0I,YAAL;AAAA;AAAA,mEAAjB;AACAM,YAAAA,QAAQ,CAACC,IAAT,CAAc9K,gBAAd;AACH,WAJD,MAIO;AAAA;;AACH;AAAA;AAAA,sCAAS,cAAT,EAA0B,IAAD,uBAAI,KAAK6B,IAAL,CAAUkB,MAAd,oCAAI,oBAAkBA,MAAtB,qBAAI,oBAA0BC,IAAK,IAAG,KAAKnB,IAAL,CAAUmB,IAAK,SAAQhD,gBAAiB,mBAAkBgD,IAAK,EAA9H;AACH;;AACD,eAAKpC,WAAL,CAAkBqK,QAAlB,CAA2BpJ,IAA3B;AACAA,UAAAA,IAAI,CAACC,WAAL,CAAiBsJ,IAAjB,EAAuBC,IAAvB,EAA6B,CAA7B;AACA,iBAAOxJ,IAAP;AACH;;AAEM0J,QAAAA,gBAAgB,CAACC,MAAD,EAAwC;AAC3D,eAAK,IAAI7I,KAAT,IAAkB,KAAKrB,MAAvB,EAA+B;AAC3B,gBAAIqB,KAAK,CAAC6I,MAAN,IAAgBA,MAApB,EAA4B;AACxB,qBAAO7I,KAAP;AACH;AACJ;;AACD,iBAAO,IAAP;AACH;;AAhuBwC,O", "sourcesContent": ["import { _decorator, Component, instantiate, js, JsonAsset, log, Node, Prefab, Sprite, SpriteFrame, UITransform, v2, Vec2, view } from \"cc\";\r\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\r\nimport { LayerSplicingMode, LevelDataEvent, LevelDataLayer, LevelDataScroll, LevelDataTerrain } from \"db://assets/bundles/common/script/leveldata/leveldata\";\r\nimport { LevelEventRun } from \"./LevelEventRun\"\r\nimport { Tools } from \"db://assets/bundles/common/script/game/utils/Tools\";\r\nimport { GameIns } from \"db://assets/bundles/common/script/game/GameIns\";\r\nimport { logDebug, logError, logInfo } from \"db://assets/scripts/utils/Logger\";\r\nimport { GameConst } from \"db://assets/scripts/core/base/GameConst\";\r\nimport { LevelNodeCheckOutScreen } from \"./LevelNodeCheckOutScreen\";\r\nimport { LevelUtils } from \"./LevelUtils\";\r\nimport { EmittierStatus, EmittierTerrain } from \"db://assets/bundles/common/script/game/dyncTerrain/EmittierTerrain\";\r\nimport { LevelLayer } from \"./LevelLayer\";\r\n\r\nconst { ccclass } = _decorator;\r\n\r\nconst SCROLL_POOL_NAME = \"scroll_pool\";\r\nconst TERRAIN_POOL_NAME = \"terrain_pool\";\r\nconst EMIITER_POOL_NAME = \"emittier_pool\";\r\n\r\nconst TerrainsNodeName = \"terrains\";\r\nconst DynamicNodeName = \"dynamic\";\r\nconst ScrollsNodeName = \"scrolls\";\r\nconst EmittiersNodeName = \"emittiers\";\r\n\r\nconst jsonRootPath: string = 'game/level/background/Prefab/Config/';\r\n\r\ninterface TerrainInfo {\r\n    data: LevelDataTerrain;\r\n    parentNode: Node;\r\n    zIndex: number; // 在父节点中的顺序，用于层级排序\r\n}\r\n\r\n@ccclass('LevelLayerUI')\r\nexport class LevelLayerUI extends LevelLayer {\r\n    private _offSetY: number = 0; // 当前关卡的偏移量\r\n\r\n    private terrainsNode: Node | null = null;\r\n    private dynamicNode: Node | null = null;\r\n    private scrollsNode: Node | null = null;\r\n    private emittiersNode: Node | null = null;\r\n\r\n    private _scorllData: LevelDataScroll | null = null;\r\n    private _scorllPrefabs: Prefab[] = []; // 已经加载的\r\n    private _lastScrollNodeHeight: number = 0;\r\n\r\n    private _emittierInfo: LevelDataTerrain[] = [];\r\n    private _inactiveEmittierNodes: Node[] = []; // 未激活的发射器节点列表\r\n    private _insEmittierLock: boolean = false;\r\n\r\n    // 当前关卡的地形信息，用于动态实例化\r\n    private _terrainsInfo: TerrainInfo[] = []; // 地形基础元素，单个terrain根据Y坐标排序（背景、滚动层、发射器单独处理）\r\n    private _insTerrainLock: boolean = false;\r\n\r\n    private events: LevelDataEvent[] = [];\r\n    private eventRunners: LevelEventRun[] = [];\r\n\r\n    onLoad(): void {\r\n\r\n    }\r\n\r\n    public async initByLevelData(data: LevelDataLayer, offSetY: number, bFirstLoad: boolean): Promise<void> {\r\n        this._offSetY = offSetY;\r\n        this.node.setPosition(0, offSetY, 0);\r\n\r\n        this.terrainsNode = LevelUtils.getOrAddNode(this.node, TerrainsNodeName);\r\n        this.dynamicNode = LevelUtils.getOrAddNode(this.node, DynamicNodeName);\r\n        this.scrollsNode = LevelUtils.getOrAddNode(this.node, ScrollsNodeName);\r\n        this.emittiersNode = LevelUtils.getOrAddNode(this.node, EmittiersNodeName);\r\n\r\n        this._scorllPrefabs = [];\r\n        this._terrainsInfo = [];\r\n        this._emittierInfo = [];\r\n        this._inactiveEmittierNodes = [];\r\n        this._insTerrainLock = false;\r\n        this._insEmittierLock = false;\r\n        // 首关基础地形只加载一屏半的资源，剩余的动态加载\r\n        // ------------------------------------------\r\n        await this._initTerrainsByLevelData(data, bFirstLoad);\r\n        await this._initDynamicsByLevelData(data, bFirstLoad);\r\n        await this._initEmittierLevelData(data, bFirstLoad);\r\n        // ------------------------------------------\r\n        await this._initScorllsByLevelData(data, bFirstLoad);\r\n        \r\n        this._sortTerrainsInfo();\r\n        \r\n        this.events = [...data.events]\r\n        this.events.sort((a, b) => a.position.y - b.position.y);\r\n        this.events.forEach((event) => {\r\n            this.eventRunners.push(new LevelEventRun(event, this));\r\n        });\r\n    }\r\n\r\n    private async _initTerrainsByLevelData(data: LevelDataLayer, bFirstLoad: boolean): Promise<void> {\r\n        if (!data || this.terrainsNode === null || data.terrains.length === 0) { return; }  \r\n\r\n        logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name} 固定地形初始化：${data.remark}`);\r\n        let zIndex = 0;\r\n        for (const terrain of data.terrains){\r\n            if (bFirstLoad) {\r\n                if (terrain.type.length > 0) {\r\n                    if (terrain.type.endsWith('.json')) {\r\n                        const json_path = jsonRootPath + terrain.type.replace('.json', '');\r\n                        logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name}] jsonData : ${LevelUtils.extractPathPart(json_path)}`);\r\n                        const jsonAsset = await MyApp.resMgr.loadAsync(json_path, JsonAsset);\r\n                        const jsonData = jsonAsset.json;\r\n                        if (jsonData!.terrains && Array.isArray(jsonData!.terrains)) {\r\n                            let subZIndex = 0;\r\n                            const subParentNode = LevelUtils.getOrAddNode(this.terrainsNode!, `terr_${zIndex}`); \r\n                            logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name}] init terrain 创建节点 subParentNode: ${subParentNode.name} 父节点：${subParentNode.parent!.name}`);\r\n                            subParentNode.setSiblingIndex(zIndex);\r\n                            for (const t of jsonData!.terrains) {\r\n                                const terrainData = Object.assign(new LevelDataTerrain(), t);\r\n                                const terrainPosY = terrain.position.y + terrainData.position.y;\r\n                                if ((terrainPosY - terrainData.height / 2 + this.node.position.y) <= GameConst.VIEWPORT_LOAD_POS) {\r\n                                    if (terrainData.type.endsWith('.png')) {\r\n                                        const png_path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrainData.type.replace('.png', ''));\r\n                                        logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name}] 静态元素(通过json配置) png : ${LevelUtils.extractPathPart(png_path)}`);\r\n                                        await this._createPngNode(\r\n                                            png_path, v2(terrain.position.x + terrainData.position.x, terrainPosY), v2(terrainData.scale.x, terrainData.scale.y),\r\n                                            terrainData.rotation, subParentNode, subZIndex);\r\n                                    } else {\r\n                                        const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrainData.uuid);\r\n                                        const prefabNode = await this._createTerrainPrefabNode(\r\n                                            path, v2(terrain.position.x + terrainData.position.x, terrainPosY), v2(terrainData.scale.x, terrainData.scale.y),\r\n                                            terrainData.rotation, subParentNode, subZIndex);      \r\n                                        logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name}] 静态元素(通过json配置) prefab : ${prefabNode!.name}`);\r\n                                    }\r\n                                } else {\r\n                                    terrainData.position.y = terrainPosY;\r\n                                    terrainData.position.x += terrain.position.x;\r\n                                    terrainData.subParentNode = subParentNode;\r\n                                    this._addTerrainInfo(terrainData, subParentNode, subZIndex);\r\n                                }\r\n                                subZIndex++;                             \r\n                            }\r\n                        } else {\r\n                            logError(\"LevelLayerUI\", \"JSON data has no terrains array\");\r\n                        }\r\n                    } else if (terrain.type.endsWith('.png')) {      \r\n                        if (((terrain.position.y - terrain.height / 2) + this.node.position.y) <= GameConst.VIEWPORT_LOAD_POS) { \r\n                            const png_path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrain.type.replace('.png', ''));\r\n                            await this._createPngNode(\r\n                                png_path, v2(terrain.position.x, terrain.position.y), v2(terrain.scale.x, terrain.scale.y),\r\n                                terrain.rotation, this.terrainsNode!, zIndex);\r\n                            logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name}] 静态元素 png : ${LevelUtils.extractPathPart(png_path)}`);\r\n                        } else {\r\n                            this._addTerrainInfo(terrain, this.terrainsNode!, zIndex);\r\n                        }\r\n                    }\r\n                } else {\r\n                    if ((terrain.position.y - terrain.height / 2 + this.node.position.y) <= GameConst.VIEWPORT_LOAD_POS) {\r\n                        const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrain.uuid);\r\n                        const prefabNode = await this._createTerrainPrefabNode(\r\n                            path, v2(terrain.position.x, terrain.position.y), v2(terrain.scale.x, terrain.scale.y),\r\n                            terrain.rotation, this.terrainsNode!, zIndex);             \r\n                        logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name}] 静态元素 prefab Node: ${prefabNode!.name}`);\r\n                    } else {\r\n                        this._addTerrainInfo(terrain, this.terrainsNode!, zIndex);\r\n                    }\r\n                }  \r\n            } else {\r\n                this._addTerrainInfo(terrain, this.terrainsNode!, zIndex);\r\n            }\r\n            zIndex++;        \r\n        }\r\n    }\r\n\r\n    private async _initDynamicsByLevelData(data: LevelDataLayer, bFirstLoad: boolean): Promise<void> {\r\n        if (!data || this.dynamicNode === null || data.dynamics.length === 0) { return; } \r\n        \r\n        logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name} 随机地形初始化：${data.remark}`);\r\n        let zIndex = 0;\r\n        for (const dynamics of data.dynamics) { \r\n            let weights: number[] = [];\r\n            dynamics.group.forEach((dynamic) => {\r\n                weights.push(dynamic.weight);\r\n            });\r\n            const dynaIndex = Tools.getRandomIndexByWeights(weights, GameIns.battleManager.random());\r\n            const dynamic = dynamics.group[dynaIndex];\r\n\r\n            weights = [];\r\n            dynamic.terrains.forEach((terrain) => {\r\n                weights.push(terrain.weight);\r\n            });\r\n            const terrainIndex = Tools.getRandomIndexByWeights(weights, GameIns.battleManager.random());\r\n            const terrain = dynamic.terrains[terrainIndex];\r\n            \r\n            const randomOffsetX = GameIns.battleManager.random() * (terrain.offSetX.max - terrain.offSetX.min) + terrain.offSetX.min;   \r\n            const randomOffsetY = GameIns.battleManager.random() * (terrain.offSetY.max - terrain.offSetY.min) + terrain.offSetY.min; \r\n            \r\n            if (!this.node.parent || !this.node.parent.parent) {\r\n                console.log(\"LevelLayerUI\", \"this.node.parent || !this.node.parent.parent\");\r\n                return;\r\n            }\r\n            if (bFirstLoad) {\r\n                if (terrain.type.length > 0) {\r\n                    logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name}] 随机出的地形 : ${LevelUtils.extractPathPart(MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrain.uuid))}`);\r\n                    if (terrain.type.endsWith('.json')) {\r\n                        const json_path = jsonRootPath + terrain.type.replace('.json', '');\r\n                        logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name}] jsonData : ${LevelUtils.extractPathPart(json_path)}`);\r\n                        const jsonAsset = await MyApp.resMgr.loadAsync(json_path, JsonAsset);\r\n                        const jsonData = jsonAsset.json;\r\n                        if (jsonData!.terrains && Array.isArray(jsonData!.terrains)) {\r\n                            let subZIndex = 0;\r\n                            const subParentNode = LevelUtils.getOrAddNode(this.dynamicNode!, `dyna_${zIndex}`);\r\n                            logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name}] init dyna 创建节点 subParentNode: ${subParentNode.name} 父节点：${subParentNode.parent!.name}`);\r\n                            subParentNode.setSiblingIndex(zIndex);\r\n                            for (const t of jsonData!.terrains) {\r\n                                const terrainData = Object.assign(new LevelDataTerrain(), t);  \r\n                                //logDebug(\"LevelLayerUI\", `jsonData.terrains: ${JSON.stringify(terrainData)}`);\r\n                                const terrainPosY = dynamic.position.y + randomOffsetY + terrainData.position.y;\r\n                                const curOffPosY = terrainPosY - terrainData.height / 2;\r\n                                const curPosY = curOffPosY + this.node.position.y;\r\n                                logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name}] 父节点坐标：${this.node.position.y} 子节偏移坐标：${curOffPosY}`);\r\n                                if ((curPosY) <= GameConst.VIEWPORT_LOAD_POS) {\r\n                                    if (terrainData.type.endsWith('.png')) {\r\n                                        const png_path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrainData.type.replace('.png', ''));\r\n                                        logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name}] 随机元素(通过json配置) png : ${LevelUtils.extractPathPart(png_path)}`);\r\n                                        await this._createPngNode(\r\n                                            png_path, v2(dynamic.position.x + randomOffsetX + terrainData.position.x, terrainPosY), v2(terrainData.scale.x, terrainData.scale.y),\r\n                                            terrainData.rotation, subParentNode, subZIndex);\r\n                                    } else {\r\n                                        const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrainData.uuid); \r\n                                        const prefabNode = await this._createTerrainPrefabNode(\r\n                                            path, v2(dynamic.position.x + randomOffsetX + terrainData.position.x, terrainPosY), v2(terrainData.scale.x, terrainData.scale.y),\r\n                                            terrainData.rotation, subParentNode, subZIndex);\r\n                                        logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name}] 随机元素(通过json配置) prefab : ${prefabNode.name}`);\r\n                                    }\r\n                                } else {\r\n                                    this._addTerrainInfo({\r\n                                        uuid: terrainData.uuid,\r\n                                        type: terrainData.type,\r\n                                        height: terrainData.height,\r\n                                        position: v2(dynamic.position.x + randomOffsetX + terrainData.position.x, terrainPosY),\r\n                                        scale: v2(terrainData.scale.x, terrainData.scale.y),\r\n                                        rotation: terrainData.rotation,\r\n                                    }, subParentNode, subZIndex);\r\n                                }\r\n                                subZIndex++;\r\n                            }\r\n                        } else {\r\n                            logError(\"LevelLayerUI\", \"JSON data has no terrains array\");\r\n                        }\r\n                    } else if (terrain.type.endsWith('.png')) {       \r\n                        if ((dynamic.position.y - terrain.height / 2 + randomOffsetY + this.node.position.y) <= GameConst.VIEWPORT_LOAD_POS) {\r\n                            const png_path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrain.type.replace('.png', ''));\r\n                            logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name}] 随机元素 png : ${LevelUtils.extractPathPart(png_path)}`);\r\n                            await this._createPngNode(\r\n                                png_path, v2(dynamic.position.x + randomOffsetX, dynamic.position.y + randomOffsetY), v2(dynamic.scale.x, dynamic.scale.y),\r\n                                dynamic.rotation, this.dynamicNode!, zIndex);\r\n                        } else {\r\n                            this._addTerrainInfo({\r\n                                uuid: terrain.uuid,\r\n                                type: terrain.type,\r\n                                height: terrain.height,\r\n                                position: v2(dynamic.position.x + randomOffsetX , dynamic.position.y + randomOffsetY),\r\n                                scale: v2(dynamic.scale.x, dynamic.scale.y),\r\n                                rotation: dynamic.rotation,\r\n                            }, this.dynamicNode!, zIndex);\r\n                        }\r\n                    }\r\n                } else {\r\n                    if ((dynamic.position.y + randomOffsetX - terrain.height / 2 + this.node.position.y) <= GameConst.VIEWPORT_LOAD_POS) { \r\n                        const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrain.uuid)\r\n                        const prefabNode = await this._createTerrainPrefabNode(\r\n                            path, v2(dynamic.position.x + randomOffsetX, dynamic.position.y + randomOffsetY), v2(dynamic.scale.x, dynamic.scale.y), \r\n                            dynamic.rotation, this.dynamicNode!, zIndex);\r\n                        logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name}] 随机元素 prefab : ${prefabNode.name}`);\r\n                    } else {\r\n                        this._addTerrainInfo({\r\n                            uuid: terrain.uuid,\r\n                            type: '',\r\n                            height: terrain.height,\r\n                            position: v2(dynamic.position.x + randomOffsetX, dynamic.position.y + randomOffsetY),\r\n                            scale: v2(dynamic.scale.x, dynamic.scale.y),\r\n                            rotation: dynamic.rotation,\r\n                        }, this.dynamicNode!, zIndex);\r\n                    }\r\n                }\r\n            } else {\r\n                this._addTerrainInfo({\r\n                    uuid: terrain.uuid,\r\n                    type: terrain.type,\r\n                    height: terrain.height,\r\n                    position: v2(dynamic.position.x + randomOffsetX, dynamic.position.y + randomOffsetY),\r\n                    scale: v2(dynamic.scale.x, dynamic.scale.y),\r\n                    rotation: dynamic.rotation,\r\n                }, this.dynamicNode!, zIndex);\r\n            }\r\n\r\n            zIndex++;\r\n        }\r\n    }\r\n\r\n    private async _initEmittierLevelData(data: LevelDataLayer, bFristLevel: boolean): Promise<void> {\r\n        if (!data || this.emittiersNode === null || data.emittiers.length === 0) { return; } \r\n \r\n        let index = 0;\r\n        for (const emittier of data.emittiers) {\r\n            if (bFristLevel) {\r\n                if (emittier.position.y - emittier.height / 2 + this.node.position.y < GameConst.VIEWPORT_LOAD_POS) {\r\n                    const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, emittier.uuid);\r\n                    const prefab = await this._createEmittierNode(\r\n                        path, v2(emittier.position.x, emittier.position.y), v2(emittier.scale.x, emittier.scale.y), \r\n                        emittier.rotation, index);\r\n                    logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name}] 发射器 prefab : ${prefab.name}`);\r\n                } else {\r\n                    this._addEmittierInfo(emittier);\r\n                }\r\n            } else {\r\n                this._addEmittierInfo(emittier);\r\n            }\r\n            index++;  \r\n        }\r\n\r\n        this._emittierInfo.sort((a, b) => {\r\n            return a.position.y - b.position.y;\r\n        });\r\n    }\r\n\r\n    public async _initScorllsByLevelData(data: LevelDataLayer, bFristLevel: boolean):Promise<void> {\r\n        if (!data || this.scrollsNode === null || data.scrolls.length === 0) { return; } \r\n\r\n        // 根据权重随机出一个滚动组\r\n        const weights: number[] = [];\r\n        data.scrolls.forEach(element => {\r\n            weights.push(element.weight);\r\n        });\r\n        const srocllIndex = Tools.getRandomIndexByWeights(weights, GameIns.battleManager.random());\r\n        this._scorllData = data.scrolls[srocllIndex];\r\n\r\n        for (let i = 0; i < this._scorllData.uuids.length; i++) {\r\n            const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, this._scorllData.uuids[i]);\r\n            const prefab = await MyApp.resMgr.loadAsync(path, Prefab);\r\n            this._scorllPrefabs.push(prefab);\r\n        }\r\n        \r\n        var offSetY = 0;\r\n        var halfHeight = 0;\r\n        while (this._scorllPrefabs.length > 0 && (offSetY - halfHeight) < GameConst.VIEWPORT_LOAD_POS) {\r\n            const randomOffsetX = GameIns.battleManager.random() * (this._scorllData.offSetX!.max - this._scorllData.offSetX!.min) + this._scorllData.offSetX!.min;\r\n            const node = this._addScrollNode(randomOffsetX, offSetY);\r\n            var nodeHeight = 0;\r\n            if (this._scorllData.splicingMode === LayerSplicingMode.node_height) {    \r\n                nodeHeight = node.getComponent(UITransform)!.contentSize.height;\r\n            } else if (this._scorllData.splicingMode === LayerSplicingMode.fix_height) {\r\n                nodeHeight = 1334;\r\n            } else if (this._scorllData.splicingMode === LayerSplicingMode.random_height) {\r\n                nodeHeight = Math.max(this._scorllData.offSetY!.min,this._scorllData.offSetY!.max) + node.getComponent(UITransform)!.contentSize.height;\r\n            }\r\n            \r\n            offSetY += nodeHeight;\r\n            halfHeight = nodeHeight / 2;\r\n            this._lastScrollNodeHeight = nodeHeight;\r\n        }\r\n    }\r\n\r\n    public tick(deltaTime: number): void {\r\n        let posY = this.node.position.y;\r\n        posY -= deltaTime * this.speed;\r\n        this.node.setPosition(0, posY, 0);\r\n\r\n        // 说明: event的激活，是从进入世界范围开始。\r\n        // 又：因为游戏是从上往下滚动，因为我们用一个固定大小变量定义上边界，一旦进入这个范围，就激活。\r\n        const scrollY =  -posY + GameConst.VIEWPORT_TOP;\r\n        // console.log('LevelLayerUI tick scrollY', scrollY, -posY, this._offSetY, GameConst.VIEWPORT_TOP);\r\n        for (let i = 0; i < this.eventRunners.length; i++) {\r\n            const eventRunner = this.eventRunners[i];\r\n            eventRunner.tick(scrollY);\r\n            if (eventRunner.isTriggered) {\r\n                // 条件已触发\r\n                this.eventRunners.splice(i, 1);\r\n                i--;\r\n            }\r\n        }\r\n\r\n        if (!this._insTerrainLock) {\r\n            this._checkDynaTerrain(posY);\r\n        }\r\n        this._checkScroll();\r\n        if (this._insEmittierLock) {\r\n            this._checkEmittier(posY);\r\n        }\r\n    }\r\n\r\n    private _sortTerrainsInfo() {\r\n        this._terrainsInfo.sort((a, b) => {\r\n            return a.data.position.y - b.data.position.y;\r\n        });\r\n        \r\n        logInfo(\"LevelLayerUI\", `${this.node.name} 地形元素数量: ${this._terrainsInfo.length}`);\r\n        /*this._terrainsInfo.forEach((terrain, index) => {\r\n            logInfo(\"LevelLayerUI\", `[${index}] Y坐标: ${terrain.data.position.y} 元素类型：${terrain.parentNode} uuid:${terrain.data.uuid} json:${terrain.data.type}`);\r\n        });*/\r\n    }\r\n\r\n    private _addTerrainInfo(\r\n        terrainData: {uuid: string, type: string, height: number, position: Vec2, scale: Vec2, rotation: number},\r\n        parentNode: Node,\r\n        zIndex: number\r\n    ) {\r\n        let attr = '';\r\n        let elemName = '';\r\n        if (terrainData.type.length > 0) {\r\n            attr = '图片';\r\n            elemName = LevelUtils.extractPathPart(MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrainData.type.replace('.png', '')));\r\n        } else {\r\n            attr = '预制体';\r\n            elemName = LevelUtils.extractPathPart(MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrainData.uuid),'');\r\n        }\r\n        logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name}] 添加到预加载地形组 类型: ${attr} 元素: ${elemName}  父节点：${parentNode.name}  zIndex: ${zIndex}`);\r\n        this._terrainsInfo.push({\r\n            data: terrainData,\r\n            parentNode: parentNode,\r\n            zIndex: zIndex\r\n        });\r\n    }\r\n\r\n    private _addEmittierInfo( terrainData: LevelDataTerrain ) {\r\n        this._emittierInfo.push(terrainData);\r\n        logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name}] 添加到预加载发射器组 元素: ${LevelUtils.extractPathPart(MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrainData.uuid))}`);\r\n    }\r\n\r\n    // 动态实例化场景元素，当元素的位置在一个屏幕以上位置时，就实例化\r\n    private async _checkDynaTerrain(curPosY: number) {\r\n        if (!this.node.parent || !this.node.parent.parent) return;\r\n\r\n        if (this._insTerrainLock) return;\r\n\r\n        this._insTerrainLock = true;\r\n        try {\r\n            const indicesToRemove: number[] = [];\r\n            const newTerrainsInfo: TerrainInfo[] = [];\r\n            for (let i = 0; i < this._terrainsInfo.length; i++) {\r\n                const terrainInfo = this._terrainsInfo[i];\r\n\r\n                // 因为列表排过序，先简单判断一次只要保存的元素第一个不在预判屏幕内，就跳出循环，因为后续的元素也都不会在预判屏幕内\r\n                // 逻辑后面会根据每个节点的高度再仔细判断一次\r\n                if (terrainInfo.data.position.y - terrainInfo.data.height / 2 + curPosY > GameConst.VIEWPORT_LOAD_POS) {\r\n                    break;\r\n                }\r\n\r\n                if (terrainInfo.data.type.length > 0) {\r\n                    if (terrainInfo.data.type.endsWith('.json')) { \r\n                        const json_path = jsonRootPath + terrainInfo.data.type.replace('.json', '');\r\n                        const jsonAsset = await MyApp.resMgr.loadAsync(json_path, JsonAsset);\r\n                        const jsonData = jsonAsset.json;\r\n                        if (jsonData!.terrains && Array.isArray(jsonData!.terrains)) {\r\n                            let subZIndex = 0;\r\n                            const subParentNode = LevelUtils.getOrAddNode(terrainInfo.parentNode!, `dyna_${terrainInfo.zIndex}`);\r\n                            logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name}] _instantiate 创建节点 subParentNode: ${subParentNode.name} 父节点：${subParentNode.parent!.name}`);\r\n                            subParentNode.setSiblingIndex(terrainInfo.zIndex);\r\n                            for (const t of jsonData!.terrains) {\r\n                                const terrainData = Object.assign(new LevelDataTerrain(), t);\r\n                                if ((terrainInfo.data.position.y + terrainData.position.y - terrainData.height / 2 + curPosY) <= GameConst.VIEWPORT_LOAD_POS) {\r\n                                    if (terrainData.type.endsWith('.png')) {\r\n                                        const png_path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrainData.type.replace('.png', ''));\r\n                                        logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name}] 随机元素 png 路径: ${png_path} index: ${i}`);\r\n                                        await this._createPngNode(\r\n                                            png_path, v2(terrainInfo.data.position.x + terrainData.position.x, terrainInfo.data.position.y + terrainData.position.y), v2(terrainData.scale.x, terrainData.scale.y),\r\n                                            terrainData.rotation, subParentNode!, subZIndex);\r\n                                    } else {\r\n                                        const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrainData.uuid);\r\n                                        const prefabNode = await this._createTerrainPrefabNode(\r\n                                            path, v2(terrainInfo.data.position.x + terrainData.position.x, terrainInfo.data.position.y + terrainData.position.y), v2(terrainData.scale.x, terrainData.scale.y),\r\n                                            terrainData.rotation, subParentNode!, subZIndex);\r\n                                        logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name}] 实时创建元素(通过json配置) prefab Node: ${prefabNode.name} index: ${i}`);\r\n                                    }\r\n                                } else {\r\n                                    const name = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrainData.uuid);\r\n                                    logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name}] push TerrainsInfo name: ${name} parentName: ${subParentNode.name} index: ${i} subZIndex: ${subZIndex}`);\r\n                                    newTerrainsInfo.push({\r\n                                        data: {\r\n                                            uuid: terrainData.uuid,\r\n                                            type: terrainData.type,\r\n                                            height: terrainData.height,\r\n                                            position: v2(terrainInfo.data.position.x + terrainData.position.x, terrainInfo.data.position.y + terrainData.position.y),\r\n                                            scale: v2(terrainData.scale.x, terrainData.scale.y),\r\n                                            rotation: terrainData.rotation\r\n                                        },\r\n                                        parentNode: subParentNode,\r\n                                        zIndex: subZIndex\r\n                                    });\r\n                                }\r\n                                subZIndex++;\r\n                            }\r\n                            \r\n                            indicesToRemove.push(i);\r\n                        }\r\n                    } else if (terrainInfo.data.type.endsWith('.png')) {\r\n                    if ((terrainInfo.data.position.y - terrainInfo.data.height / 2 + curPosY) <= GameConst.VIEWPORT_LOAD_POS) {\r\n                            const png_path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrainInfo.data.type.replace('.png', ''));\r\n                            await this._createPngNode(\r\n                                png_path, v2(terrainInfo.data.position.x, terrainInfo.data.position.y), v2(terrainInfo.data.scale.x, terrainInfo.data.scale.y),\r\n                                terrainInfo.data.rotation, terrainInfo.parentNode!, terrainInfo.zIndex);\r\n                            indicesToRemove.push(i);\r\n                        }\r\n                    }\r\n                } else {\r\n                    if ((terrainInfo.data.position.y - terrainInfo.data.height / 2 + curPosY) <= GameConst.VIEWPORT_LOAD_POS) {\r\n                        const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, terrainInfo.data.uuid);\r\n                        const prefabNode = await this._createTerrainPrefabNode(\r\n                            path, v2(terrainInfo.data.position.x, terrainInfo.data.position.y), v2(terrainInfo.data.scale.x, terrainInfo.data.scale.y),\r\n                            terrainInfo.data.rotation, terrainInfo.parentNode!, terrainInfo.zIndex);\r\n                        logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name}] 实时创建元素 prefab Node: ${prefabNode.name} 父节点：${terrainInfo.parentNode!.name} 节点顺序：${terrainInfo.zIndex}`);\r\n                        indicesToRemove.push(i);\r\n                    }\r\n                }\r\n            }\r\n            for (let i = indicesToRemove.length - 1; i >= 0; i--) {\r\n                this._terrainsInfo.splice(indicesToRemove[i], 1);\r\n            }\r\n            if (newTerrainsInfo.length > 0) { \r\n                this._terrainsInfo.push(...newTerrainsInfo);\r\n                this._sortTerrainsInfo();\r\n            }\r\n        } catch (error) {\r\n            logError(\"LevelLayerUI\", `_instantiate Terrain error: ${error}`);\r\n        } finally {\r\n            this._insTerrainLock = false;\r\n        }\r\n    }\r\n\r\n    private _checkScroll(): void {\r\n        if (!this.scrollsNode || this._scorllPrefabs.length === 0) return;\r\n\r\n        const lastChild = this.scrollsNode.children[this.scrollsNode.children.length - 1];\r\n        const lastChildTransform = lastChild.getComponent(UITransform);\r\n\r\n        if (!lastChildTransform) return;\r\n\r\n        const lastChildTop = this.node.position.y + lastChild.position.y + this._lastScrollNodeHeight / 2;\r\n\r\n        if (lastChildTop < GameConst.VIEWPORT_TOP) {\r\n            const newY = lastChild.position.y + this._lastScrollNodeHeight;\r\n            const randomOffsetX = GameIns.battleManager.random() * (this._scorllData!.offSetX!.max - this._scorllData!.offSetX!.min) + this._scorllData!.offSetX!.min;\r\n            const newNode = this._addScrollNode(randomOffsetX, newY);\r\n            var nodeHeight = 0;\r\n            if (this._scorllData!.splicingMode === LayerSplicingMode.node_height) {    \r\n                nodeHeight = newNode.getComponent(UITransform)!.contentSize.height;\r\n            } else if (this._scorllData!.splicingMode === LayerSplicingMode.fix_height) {\r\n                nodeHeight = 1334;\r\n            } else if (this._scorllData!.splicingMode === LayerSplicingMode.random_height) {\r\n                nodeHeight = Math.max(this._scorllData!.offSetY!.min,this._scorllData!.offSetY!.max) + newNode.getComponent(UITransform)!.contentSize.height;\r\n            }\r\n            this._lastScrollNodeHeight = nodeHeight;\r\n        }\r\n    }\r\n\r\n    private async _checkEmittier(posY: number) {\r\n        if (!this.emittiersNode || this._emittierInfo.length === 0) return;\r\n\r\n        if (!this._insEmittierLock) return;\r\n        \r\n        this._insEmittierLock = true;\r\n        try {\r\n            const indicesToRemove: number[] = [];\r\n            for (let i = 0; i < this._emittierInfo.length; i++) {\r\n                const emittierInfo = this._emittierInfo[i];\r\n                if (emittierInfo.position.y - emittierInfo.height / 2 + posY > GameConst.VIEWPORT_LOAD_POS) {\r\n                    break;\r\n                } else {\r\n                    const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, emittierInfo.uuid);\r\n                    const emittierNode = await this._createEmittierNode(\r\n                        path, v2(emittierInfo.position.x, emittierInfo.position.y), v2(emittierInfo.scale.x, emittierInfo.scale.y),\r\n                        emittierInfo.rotation, i);\r\n                    logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name}] 实时创建发射器 prefab Node: ${emittierNode.name} 父节点：${this.emittiersNode!.name} 节点顺序：${i}`);\r\n                    indicesToRemove.push(i);\r\n                }\r\n            }\r\n\r\n            for (let i = indicesToRemove.length - 1; i >= 0; i--) {\r\n                this._emittierInfo.splice(indicesToRemove[i], 1);\r\n            }\r\n        } catch (error) {\r\n            logError(\"LevelLayerUI\", `_checkEmittier error: ${error}`);\r\n        } finally {\r\n            this._insEmittierLock = false;\r\n        }\r\n\r\n        for (let i = this._inactiveEmittierNodes.length - 1; i >= 0; i--) {\r\n            const node = this._inactiveEmittierNodes[i];\r\n            \r\n            if (!node.isValid) {\r\n                this._inactiveEmittierNodes.splice(i, 1);\r\n                continue;\r\n            }\r\n            \r\n            const comp = node.getComponent(EmittierTerrain);\r\n            if (!comp) {\r\n                this._inactiveEmittierNodes.splice(i, 1);\r\n                continue;\r\n            }\r\n            \r\n            if (comp.status === EmittierStatus.inactive && (posY + node.position.y) <= GameConst.VIEWPORT_TOP) {\r\n                comp.startEmittier();\r\n                if (comp.follow === false) {\r\n                    this.speed = 0;\r\n                }\r\n                this._inactiveEmittierNodes.splice(i, 1);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 创建并配置 PNG 类型的地形节点\r\n     * @param spriteFramePath - SpriteFrame 资源路径\r\n     * @param position - 节点位置\r\n     * @param scale - 节点缩放\r\n     * @param rotation - 节点旋转角度\r\n     * @param parentNode - 父节点\r\n     * @param zIndex - 在父节点中的层级顺序\r\n     * @returns 返回创建好的地形节点\r\n     */\r\n    private async _createPngNode(\r\n        spriteFramePath: string,\r\n        position: Vec2,\r\n        scale: Vec2,\r\n        rotation: number,\r\n        parentNode: Node,\r\n        zIndex: number\r\n    ): Promise<Node | null> {\r\n        if (!spriteFramePath.endsWith('/spriteFrame')) {\r\n            logError(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name}] 非标准 png 路径: ${spriteFramePath}`);\r\n            return null;\r\n        }\r\n\r\n        const name = LevelUtils.extractPathPart(spriteFramePath);\r\n        let terrainNode = GameIns.gameMapManager.mapObjectPoolManager.get(\r\n            TERRAIN_POOL_NAME, \r\n            name\r\n        );\r\n\r\n        if (terrainNode) {\r\n            // 2. 如果从对象池获取到节点，直接配置属性\r\n            logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name}] 从对象池${TERRAIN_POOL_NAME}获取 PNG 元素节点: ${name}`);\r\n        } else {\r\n            const spriteFrame = await MyApp.resMgr.loadAsync(spriteFramePath, SpriteFrame);\r\n            logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name}] 创建 PNG 元素节点: ${name} 父节点：${parentNode.name} 节点顺序：${zIndex}`);\r\n            terrainNode = new Node();\r\n            terrainNode.name = name;\r\n            const terrainSprite = terrainNode.addComponent(Sprite);\r\n            terrainSprite.spriteFrame = spriteFrame;\r\n            const uiTransform = terrainNode.getComponent(UITransform);\r\n            if (uiTransform && spriteFrame) {\r\n                const size = spriteFrame.originalSize;\r\n                uiTransform.setContentSize(size.width, size.height);\r\n                //logDebug(\"LevelLayerUI\", `PNG 尺寸: ${size.width}x${size.height}`);\r\n            } \r\n            const checkOut = terrainNode.addComponent(LevelNodeCheckOutScreen);\r\n            checkOut.init(TERRAIN_POOL_NAME);\r\n        }\r\n\r\n        terrainNode.setPosition(position.x, position.y, 0);\r\n        terrainNode.setScale(scale.x, scale.y);\r\n        terrainNode.setRotationFromEuler(0, 0, rotation);\r\n        \r\n        parentNode.addChild(terrainNode);\r\n        terrainNode.setSiblingIndex(zIndex);\r\n        \r\n        return terrainNode;\r\n    }\r\n\r\n    /**\r\n     * 创建并配置 Prefab 类型的地形节点\r\n     * @param prefabPath - Prefab 资源路径\r\n     * @param position - 节点位置\r\n     * @param scale - 节点缩放\r\n     * @param rotation - 节点旋转角度\r\n     * @param parentNode - 父节点\r\n     * @param zIndex - 在父节点中的层级顺序\r\n     * @returns 返回创建好的地形节点\r\n     */\r\n    private async _createTerrainPrefabNode(\r\n        prefabPath: string,\r\n        position: Vec2,\r\n        scale: Vec2,\r\n        rotation: number,\r\n        parentNode: Node,\r\n        zIndex: number\r\n    ): Promise<Node> {\r\n        const name = LevelUtils.extractPathPart(prefabPath,'');\r\n        let terrainNode = GameIns.gameMapManager.mapObjectPoolManager.get(\r\n            TERRAIN_POOL_NAME, \r\n            name\r\n        );\r\n        if (!terrainNode) {\r\n            const prefab = await MyApp.resMgr.loadAsync(prefabPath, Prefab);\r\n            terrainNode = instantiate(prefab);\r\n            const checkOut = terrainNode.addComponent(LevelNodeCheckOutScreen);\r\n            checkOut.init(TERRAIN_POOL_NAME);\r\n            logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name}] 创建 Prefab 元素节点: ${name} 父节点：${parentNode.name} 节点顺序：${zIndex}`);\r\n        } else {\r\n            logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name}] 从对象池${TERRAIN_POOL_NAME}获取 Prefab 元素节点: ${name}`);\r\n        }\r\n\r\n        terrainNode.setPosition(position.x, position.y, 0);\r\n        terrainNode.setScale(scale.x, scale.y);\r\n        terrainNode.setRotationFromEuler(0, 0, rotation);\r\n        parentNode.addChild(terrainNode);\r\n        terrainNode.setSiblingIndex(zIndex);\r\n        \r\n        return terrainNode;\r\n    }\r\n\r\n    /**\r\n     * 创建并配置 Prefab 类型的地形节点\r\n     * @param prefabPath - Prefab 资源路径\r\n     * @param position - 节点位置\r\n     * @param scale - 节点缩放\r\n     * @param rotation - 节点旋转角度\r\n     * @param zIndex - 在父节点中的层级顺序\r\n     * @returns 返回创建好的地形节点\r\n     */\r\n    private async _createEmittierNode(\r\n        prefabPath: string,\r\n        position: Vec2,\r\n        scale: Vec2,\r\n        rotation: number,\r\n        zIndex: number\r\n    ): Promise<Node> {\r\n        const name = LevelUtils.extractPathPart(prefabPath,'');\r\n        let emittierNode = GameIns.gameMapManager.mapObjectPoolManager.get(\r\n            EMIITER_POOL_NAME, \r\n            name\r\n        );\r\n        if (!emittierNode) {\r\n            const prefab = await MyApp.resMgr.loadAsync(prefabPath, Prefab);\r\n            emittierNode = instantiate(prefab);\r\n            const emittierTerrain = emittierNode.addComponent(EmittierTerrain);\r\n            emittierTerrain.init(EMIITER_POOL_NAME);\r\n            logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name}] 创建发射器节点: ${name} 父节点：${this.emittiersNode!.name} 节点顺序：${zIndex}`);\r\n        } else {\r\n            logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name}] 从对象池${EMIITER_POOL_NAME}获取发射器节点: ${name}`);\r\n        }\r\n\r\n        emittierNode.setPosition(position.x, position.y, 0);\r\n        emittierNode.setScale(scale.x, scale.y);\r\n        emittierNode.setRotationFromEuler(0, 0, rotation);\r\n        this.emittiersNode!.addChild(emittierNode);\r\n        emittierNode.setSiblingIndex(zIndex);\r\n        \r\n        return emittierNode;\r\n    }\r\n\r\n    private _addScrollNode(xPos:number, yPos: number): Node {\r\n        const index = this.scrollsNode!.children.length % this._scorllPrefabs.length;\r\n        const prefab = this._scorllPrefabs[index];\r\n        const prefabName = prefab.name;\r\n        let node = GameIns.gameMapManager.mapObjectPoolManager.get(SCROLL_POOL_NAME, prefabName);\r\n        if (!node) {\r\n            node = instantiate(this._scorllPrefabs[index]);\r\n            const checkOut = node.addComponent(LevelNodeCheckOutScreen);\r\n            checkOut.init(SCROLL_POOL_NAME);\r\n        } else {\r\n            logDebug(\"LevelLayerUI\", `[${this.node.parent?.parent?.name}:${this.node.name}] 从对象池${SCROLL_POOL_NAME}获取 Prefab 元素节点: ${name}`);\r\n        }\r\n        this.scrollsNode!.addChild(node);\r\n        node.setPosition(xPos, yPos, 0);\r\n        return node;\r\n    }\r\n\r\n    public getEventByElemID(elemID: string): LevelDataEvent | null {\r\n        for (let event of this.events) {\r\n            if (event.elemID == elemID) {\r\n                return event;\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n}\r\n"]}
import { _decorator, But<PERSON>, EditBox } from 'cc';
import { MyApp } from 'db://assets/bundles/common/script/app/MyApp';
import { logDebug, logError } from '../../../../../scripts/utils/Logger';
import csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';
import { DevLoginData } from '../../platformsdk/DevLoginData';

import { BundleName } from 'db://assets/bundles/common/script/const/BundleConst';
import { DataMgr } from 'db://assets/bundles/common/script/data/DataManager';
import { initBundle } from 'db://assets/scripts/core/base/Bundle';
import { BaseUI, UILayer, UIMgr } from '../../../../../scripts/core/base/UIMgr';
import { uiSelect } from '../common/components/SelectList/uiSelect';
import { GameIns } from '../../game/GameIns';
const { ccclass, property } = _decorator;

@ccclass('DevLoginUI')
export class DevLoginUI extends BaseUI {
    public static getUrl(): string { return "prefab/ui/DevLoginUI" };
    public static getLayer(): UILayer { return UILayer.Top }
    public static getBundleName(): string { return BundleName.Common }
    static needLogin = true;

    @property(Button)
    loginButton: Button = null!;
    @property(EditBox)
    usernameEditBox: EditBox = null!;
    @property(EditBox)
    passwordEditBox: EditBox = null!;

    @property(uiSelect)
    serverSelect: uiSelect = null!;

    async onHide(...args: any[]): Promise<void> {
    }

    async onShow(...args: any[]): Promise<void> {
        this.loginButton.node.on(Button.EventType.CLICK, this.onLoginButtonClick, this);
        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GET_ROLE, this.onGetRole, this);

        DevLoginData.serverList.forEach((value, key) => {
            this.serverSelect.itemDatas.push(key)
            this.serverSelect.itemDatas.push(key + "1")
        });
        this.serverSelect.setChooseItemData(DevLoginData.instance.servername)
        this.serverSelect.onChooseItem = (itemData: string) => {
            logDebug("LoginUI", `choose server ${itemData}`)
            DevLoginData.instance.servername = itemData;
        }
        this.usernameEditBox.string = DevLoginData.instance.user;
        this.passwordEditBox.string = DevLoginData.instance.password;
    }

    async onClose(...args: any[]): Promise<void> {
        MyApp.netMgr.unregisterHandler(csproto.cs.CS_CMD.CS_CMD_GET_ROLE, this.onGetRole, this)
    }

    onLoginButtonClick() {
        var username = this.usernameEditBox.string;
        var password = this.passwordEditBox.string;
        DevLoginData.instance.user = username;
        DevLoginData.instance.password = password;

        MyApp.netMgr.connect();
    }

    onGetRole(msg: csproto.cs.IS2CMsg) {
        UIMgr.closeUI(DevLoginUI);
        DevLoginUI.needLogin = false;
        initBundle(BundleName.Gm);
    }
}
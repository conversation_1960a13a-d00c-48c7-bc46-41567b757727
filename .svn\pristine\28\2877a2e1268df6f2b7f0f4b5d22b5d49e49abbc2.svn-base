"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.methods = void 0;
exports.load = load;
exports.unload = unload;
const path_1 = require("path");
// @ts-ignore
module.paths.push((0, path_1.join)(Editor.App.path, 'node_modules'));
// 当前版本需要在 module.paths 修改后才能正常使用 cc 模块
// 并且如果希望正常显示 cc 的定义，需要手动将 engine 文件夹里的 cc.d.ts 添加到插件的 tsconfig 里
// 当前版本的 cc 定义文件可以在当前项目的 temp/declarations/cc.d.ts 找到
const cc_1 = require("cc");
const { _utils } = cc_1.Prefab;
function load() { }
;
function unload() { }
;
exports.methods = {
    saveLevel() {
        console.log('saveLevel in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if (levelEditorUI) {
            levelEditorUI.save = true;
        }
    },
    playLevel() {
        console.log('playLevel in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if (levelEditorUI) {
            var play = levelEditorUI.play;
            levelEditorUI.play = !play;
        }
    },
    levelStart() {
        console.log('levelStart in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if (levelEditorUI) {
            levelEditorUI.progress = 0;
        }
    },
    levelEnd() {
        console.log('levelEnd in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if (levelEditorUI) {
            levelEditorUI.progress = 1;
        }
    },
    instantiatePrefab(component_uuid, prefabUuid) {
        // console.log('instantiatePrefab:', component_uuid, prefabUuid);
        var _a, _b, _c;
        let targetNode = (_a = cc_1.director.getScene()) === null || _a === void 0 ? void 0 : _a.getChildByUuid(component_uuid);
        if (!targetNode) {
            targetNode = (_b = cc_1.director.getScene()) === null || _b === void 0 ? void 0 : _b.getChildByName('Canvas');
        }
        if (targetNode) {
            // console.log("Canvas node found: ", targetNode.getComponent("EmitterEditor"));
            // @ts-ignore
            Editor.Message.request('scene', 'execute-component-method', {
                uuid: (_c = targetNode.getComponent("EmitterEditor")) === null || _c === void 0 ? void 0 : _c.uuid,
                name: 'instantiatePrefab',
                args: [prefabUuid]
            });
        }
    },
    // async saveToPrefab(component_uuid:string, nodeName: string, prefabUuid: string) {
    //     // console.log('saveToPrefab:', component_uuid, nodeName, prefabUuid);
    //     const scene = director.getScene();
    //     const target = scene!.getChildByPath(`Canvas/${nodeName}`);
    //     if (!target) {
    //         console.error("node not found:", nodeName);
    //         return;
    //     }
    //     cce.Prefab.createPrefabAssetFromNode(target.uuid, prefabUuid);
    // },
    async createBulletPrefab(sourceAssetUuid) {
        // @ts-ignore
        const sourceAssetInfo = await Editor.Message.request('asset-db', 'query-asset-info', sourceAssetUuid);
        if (!sourceAssetInfo)
            return;
        // 需要判断是spriteList还是单个sprite
        // TODO: 当sourceAssetUuid是一个目录的时候，下面这里的路径可能存在bug。还没有验证。
        // TODO: 这个目录后续可能调整到bundle目录
        const targetPrefabDir = 'db://assets/resources/game/prefabs/bullet/';
        if (sourceAssetInfo.importer === 'sprite-atlas') {
            cc_1.assetManager.loadAny({ uuid: sourceAssetUuid }, (err, asset) => {
                if (err) {
                    console.error(err);
                    return;
                }
                const spriteList = asset;
                for (let spriteFrame of spriteList.getSpriteFrames()) {
                    const targetPrefabName = spriteFrame.name;
                    const targetPrefabPath = targetPrefabDir + targetPrefabName + '.prefab';
                    createNewBullet(targetPrefabName, targetPrefabPath, spriteFrame, spriteList);
                }
            });
        }
        else if (sourceAssetInfo.importer === 'sprite-frame') {
            cc_1.assetManager.loadAny({ uuid: sourceAssetUuid }, (err, asset) => {
                if (err) {
                    console.error(err);
                    return;
                }
                const spriteFrame = asset;
                const targetPrefabName = spriteFrame.name;
                const targetPrefabPath = targetPrefabDir + targetPrefabName + '.prefab';
                createNewBullet(targetPrefabName, targetPrefabPath, spriteFrame, null);
            });
        }
        else {
            console.warn('Skipping unknown asset type:', sourceAssetInfo.importer);
        }
    },
    async createLevelPrefab(sourceAssetUuid) {
        // @ts-ignore
        const sourceAssetInfo = await Editor.Message.request('asset-db', 'query-asset-info', sourceAssetUuid);
        if (!sourceAssetInfo)
            return;
        // remove /x/x/*.* -> /x/x/
        let targetPrefabDir = sourceAssetInfo.path.replace('/Texture/', '/Prefab/');
        const targetPrefabPath = targetPrefabDir.substring(0, targetPrefabDir.lastIndexOf('/')) + '.prefab';
        const targetPrefabName = targetPrefabDir.substring(targetPrefabDir.lastIndexOf('/') + 1);
        if (sourceAssetInfo.importer === 'sprite-frame') {
            cc_1.assetManager.loadAny({ uuid: sourceAssetUuid }, (err, asset) => {
                if (err) {
                    console.error(err);
                    return;
                }
                // console.log(`Level Prefab will be created at path ${targetPrefabPath}`);
                const spriteFrame = asset;
                if (spriteFrame) {
                    createNewLevelObject(targetPrefabName, targetPrefabPath, spriteFrame); // 这个是创建prefab，不是prefab里面的节点，所以不需要parent
                }
                else {
                    console.warn('Asset is not a sprite-frame:', sourceAssetInfo);
                }
            });
        }
        else {
            console.warn('Skipping unknown asset type:', sourceAssetInfo.importer);
        }
    },
    async createMainPlanePrefab(sourceAssetUuid) {
        console.log(`createMainPlanePrefab uuid:[${sourceAssetUuid}]`);
        // @ts-ignore
        const sourceAssetInfo = await Editor.Message.request('asset-db', 'query-asset-info', sourceAssetUuid);
        if (!sourceAssetInfo) {
            console.error('query-asset-info failed:', sourceAssetUuid);
            return;
        }
        const planeId = cc_1.path.basename(sourceAssetInfo.path);
        const prefabPath = `db://assets/resources/game/prefabs/plane/mainplane/${planeId}.prefab`;
        const temp = new cc_1.Node("temp");
        cc_1.director.getScene().addChild(temp);
        const rootNode = new cc_1.Node(planeId);
        temp.addChild(rootNode);
        const spineNode = new cc_1.Node("spine");
        rootNode.addChild(spineNode);
        const spine = spineNode.addComponent(cc_1.sp.Skeleton);
        spine.skeletonData = await new Promise((resolve, reject) => {
            cc_1.assetManager.loadAny({ uuid: sourceAssetUuid }, (err, asset) => {
                if (err) {
                    console.error(err);
                    reject(err);
                }
                else {
                    console.log("createMainPlanePrefab load spine success");
                    resolve(asset);
                }
            });
        });
        const colliderNode = new cc_1.Node("collider");
        rootNode.addChild(colliderNode);
        const collider = colliderNode.addComponent(cc_1.BoxCollider2D);
        collider.size = new cc_1.Size(128, 128);
        try {
            await cce.Prefab.createPrefabAssetFromNode(rootNode.uuid, prefabPath);
            console.log(`main plane prefab created: ${prefabPath}`);
            temp.removeFromParent();
            Editor.Message.request('asset-db', 'refresh-asset', prefabPath);
        }
        catch (e) {
            console.error('Failed to create level prefab:', e, ', on asset: ', prefabPath);
        }
    },
    async createEnemyPlanePrefab(sourceAssetUuid) {
        console.log(`createEnemyPlanePrefab uuid:[${sourceAssetUuid}]`);
        // @ts-ignore
        const sourceAssetInfo = await Editor.Message.request('asset-db', 'query-asset-info', sourceAssetUuid);
        if (!sourceAssetInfo) {
            console.error('query-asset-info failed:', sourceAssetUuid);
            return;
        }
        const planeId = cc_1.path.basename(sourceAssetInfo.path);
        const prefabPath = `db://assets/resources/game/prefabs/plane/enemyplane/${planeId}.prefab`;
        const temp = new cc_1.Node("temp");
        cc_1.director.getScene().addChild(temp);
        const rootNode = new cc_1.Node(planeId);
        temp.addChild(rootNode);
        const spineNode = new cc_1.Node("spine");
        rootNode.addChild(spineNode);
        const spine = spineNode.addComponent(cc_1.sp.Skeleton);
        spine.skeletonData = await new Promise((resolve, reject) => {
            cc_1.assetManager.loadAny({ uuid: sourceAssetUuid }, (err, asset) => {
                if (err) {
                    console.error(err);
                    reject(err);
                }
                else {
                    resolve(asset);
                }
            });
        });
        const colliderNode = new cc_1.Node("collider");
        rootNode.addChild(colliderNode);
        const collider = colliderNode.addComponent(cc_1.BoxCollider2D);
        collider.size = spineNode.getComponent(cc_1.UITransform).contentSize;
        try {
            await cce.Prefab.createPrefabAssetFromNode(rootNode.uuid, prefabPath);
            console.log(`main plane prefab created: ${prefabPath}`);
            temp.removeFromParent();
            Editor.Message.request('asset-db', 'refresh-asset', prefabPath);
        }
        catch (e) {
            console.error('Failed to create level prefab:', e, ', on asset: ', prefabPath);
        }
    },
    // 阵型相关: addFormationPoint & saveFormationGroup
    addFormationPoint(formationGroupUuid) {
        const { director } = require('cc');
        let formationEditor = findComponentByUuid(director.getScene(), formationGroupUuid, 'FormationEditor');
        if (formationEditor) {
            // @ts-ignore
            formationEditor.addNewPoint(0, 0);
        }
        else {
            console.error('Formation group not found:', formationGroupUuid);
        }
    },
    async saveFormationGroup(formationGroupUuid) {
        const { director } = require('cc');
        let formationEditor = findComponentByUuid(director.getScene(), formationGroupUuid, 'FormationEditor');
        if (formationEditor) {
            // @ts-ignore
            const jsonData = formationEditor.formationData;
            // save this as JsonAsset
            if (jsonData === null) {
                // @ts-ignore
                let file = await Editor.Dialog.save({
                    path: Editor.Project.path + '/assets/resources/game/level/wave/formation/',
                    filters: [
                        { name: 'Json', extensions: ['json'] },
                    ],
                });
                if (file.canceled || !file.filePath) {
                    return;
                }
                // @ts-ignore
                Editor.Message.request('asset-db', 'create-asset', file.filePath, formationEditor.save()).then((res) => {
                    if (res) {
                        cc_1.assetManager.loadAny({ uuid: res.uuid }, (err, asset) => {
                            if (err) {
                                console.error(err);
                            }
                            else {
                                // @ts-ignore 
                                formationEditor.formationData = asset;
                            }
                        });
                    }
                });
            }
            else {
                // @ts-ignore
                Editor.Message.request('asset-db', 'save-asset', jsonData.uuid, formationEditor.save()).then((res) => {
                    if (res) {
                        cc_1.assetManager.loadAny({ uuid: res.uuid }, (err, asset) => {
                            if (err) {
                                console.error(err);
                            }
                            else {
                                // @ts-ignore 
                                formationEditor.formationData = asset;
                            }
                        });
                    }
                });
            }
        }
        else {
            console.error('Formation group not found:', formationGroupUuid);
        }
    },
    // 路径相关: addPathPoint & savePath & clearPath
    addPathPoint(pathEditorUuid) {
        const { director } = require('cc');
        let pathEditor = findComponentByUuid(director.getScene(), pathEditorUuid, 'PathEditor');
        if (pathEditor) {
            // @ts-ignore
            pathEditor.addNewPoint(0, 0);
        }
        else {
            console.error('Path editor not found:', pathEditorUuid);
        }
    },
    async savePath(pathEditorUuid) {
        const { director } = require('cc');
        let pathEditor = findComponentByUuid(director.getScene(), pathEditorUuid, 'PathEditor');
        if (pathEditor) {
            // @ts-ignore
            const jsonData = pathEditor.pathData;
            // save this as JsonAsset
            if (jsonData === null) {
                // @ts-ignore
                let file = await Editor.Dialog.save({
                    path: Editor.Project.path + '/assets/resources/game/level/wave/path/',
                    filters: [
                        { name: 'Json', extensions: ['json'] },
                    ],
                });
                if (file.canceled || !file.filePath) {
                    return;
                }
                // @ts-ignore
                Editor.Message.request('asset-db', 'create-asset', file.filePath, pathEditor.save()).then((res) => {
                    if (res) {
                        cc_1.assetManager.loadAny({ uuid: res.uuid }, (err, asset) => {
                            if (err) {
                                console.error(err);
                            }
                            else {
                                // @ts-ignore
                                pathEditor.pathData = asset;
                            }
                        });
                    }
                });
            }
            else {
                // @ts-ignore
                Editor.Message.request('asset-db', 'save-asset', jsonData.uuid, pathEditor.save()).then((res) => {
                    if (res) {
                        cc_1.assetManager.loadAny({ uuid: res.uuid }, (err, asset) => {
                            if (err) {
                                console.error(err);
                            }
                            else {
                                // @ts-ignore
                                pathEditor.pathData = asset;
                            }
                        });
                    }
                });
            }
        }
        else {
            console.error('Path editor not found:', pathEditorUuid);
        }
    },
    clearPath(pathEditorUuid) {
        const { director } = require('cc');
        let pathEditor = findComponentByUuid(director.getScene(), pathEditorUuid, 'PathEditor');
        if (pathEditor) {
            // @ts-ignore
            pathEditor.node.removeAllChildren();
            // @ts-ignore
            pathEditor.updateCurve();
        }
        else {
            console.error('Path editor not found:', pathEditorUuid);
        }
    },
    // 加载阵型数据到FormationEditor
    loadFormationData(assetUuid) {
        const { director } = require('cc');
        const scene = director.getScene();
        // 查找FormationEditor组件
        let formationEditor = scene === null || scene === void 0 ? void 0 : scene.getComponentInChildren('FormationEditor');
        if (formationEditor) {
            if (assetUuid && assetUuid != '') {
                // 加载资源并设置到FormationEditor
                cc_1.assetManager.loadAny({ uuid: assetUuid }, (err, asset) => {
                    if (err) {
                        console.error('Failed to load formation asset:', err);
                    }
                    else {
                        // @ts-ignore
                        formationEditor.formationData = asset;
                        console.log('Formation data loaded:', asset);
                    }
                });
            }
        }
        else {
            console.error('FormationEditor component not found in scene');
        }
    },
    // 加载路径数据到PathEditor
    loadPathData(assetUuid) {
        const { director } = require('cc');
        const scene = director.getScene();
        // 查找PathEditor组件
        let pathEditor = scene === null || scene === void 0 ? void 0 : scene.getComponentInChildren('PathEditor');
        if (pathEditor) {
            if (assetUuid && assetUuid != '') {
                // 加载资源并设置到PathEditor
                cc_1.assetManager.loadAny({ uuid: assetUuid }, (err, asset) => {
                    if (err) {
                        console.error('Failed to load path asset:', err);
                    }
                    else {
                        // @ts-ignore
                        pathEditor.pathData = asset;
                        console.log('Path data loaded:', asset);
                    }
                });
            }
        }
        else {
            console.error('PathEditor component not found in scene');
        }
    },
    //@region 关卡编辑器：事件节点相关的功能
    createEventNode() {
        const root = getEventNodeParent();
        if (root == null)
            return;
        let node = new cc_1.Node("event");
        node.parent = root;
        node.addComponent('LevelEditorEventUI');
        node.setPosition(0, 0, 0);
    },
    copyEventNode() {
        const root = getEventNodeParent();
        if (root == null)
            return;
        const selectedNodes = Editor.Selection.getSelected('node');
        if (selectedNodes.length == 0) {
            console.log("copyEventNode no node selected");
            return;
        }
        Editor.Selection.clear('node');
        selectedNodes.forEach((nodeUuid) => {
            let node = root.getChildByUuid(nodeUuid);
            if (node) {
                let newNode = (0, cc_1.instantiate)(node);
                newNode.parent = root;
                newNode.name = node.name + "_copy";
                newNode.setPosition(0, 0, 0);
                const eventSource = node.getComponent('LevelEditorEventUI');
                const eventTarget = newNode.getComponent('LevelEditorEventUI');
                // how to call LevelEditorEventUI.copyFrom? which is not part of this editor extension
                // @ts-ignore
                eventTarget.copyFrom(eventSource);
                Editor.Selection.select('node', [newNode.uuid]);
            }
        });
    },
    exportEventNode() {
        const selectedNodes = Editor.Selection.getSelected('node');
        if (selectedNodes.length == 0) {
            console.log("copyEventNode no node selected");
            return;
        }
        const root = getEventNodeParent();
        if (root == null)
            return;
        let events = [];
        selectedNodes.forEach((nodeUuid) => {
            let node = root.getChildByUuid(nodeUuid);
            if (node) {
                const eventSource = node.getComponent('LevelEditorEventUI');
                if (eventSource) {
                    events.push(eventSource);
                }
            }
        });
        if (events.length == 0) {
            console.log("exportEventNode no event found");
            return;
        }
    },
    sortEventNode() {
        const root = getEventNodeParent();
        if (root == null)
            return;
        const selectedNodes = Editor.Selection.getSelected('node');
        if (selectedNodes.length == 0) {
            console.log("copyEventNode no node selected");
            return;
        }
        let nodes = [];
        selectedNodes.forEach((nodeUuid) => {
            let node = root.getChildByUuid(nodeUuid);
            if (node) {
                nodes.push(node);
            }
        });
        nodes.sort((a, b) => {
            return a.position.y - b.position.y;
        });
        let siblingIndices = [];
        nodes.forEach((node) => {
            siblingIndices.push(node.getSiblingIndex());
        });
        siblingIndices.sort((a, b) => {
            return a - b;
        });
        siblingIndices.forEach((index, i) => {
            nodes[i].setSiblingIndex(index);
        });
    },
    addEventEndTag() {
        const root = getEventNodeParent();
        if (root == null)
            return;
        let node = new cc_1.Node("LevelFinish");
        node.parent = root;
        const eventUIComp = node.addComponent('LevelEditorEventUI');
        // @ts-ignore
        eventUIComp.setToSpecialEvent(eLevelSpecialEvent.SectionFinish);
    },
    //@region 关卡编辑器：事件节点相关的功能
    createWavePrefab() {
        const scene = cc_1.director.getScene();
        if (!scene) {
            console.error("Scene not found");
            return;
        }
        let node = new cc_1.Node('wave_prefab_' + Math.floor(Math.random() * 1000000));
        node.parent = scene;
        node.setPosition(new cc_1.Vec3(0, 0, 0));
        node.addComponent('Wave');
        Editor.Selection.clear('node');
        Editor.Selection.select('node', [node.uuid]);
        cce.Prefab.createPrefabAssetFromNode(node.uuid, `db://assets/resources/game/prefabs/wave/${node.name}.prefab`);
    },
    // 路径编辑器相关
    flipPathHorizontal() {
        const { director } = require('cc');
        const scene = director.getScene();
        if (scene == null)
            return;
        if (scene.name != "PathEditor") {
            console.warn("flipPathHorizontal not in PathEditor scene, current scene:", scene.name);
        }
        let pathEditor = scene.getComponentInChildren('PathEditor');
        if (pathEditor == null) {
            console.error("PathEditor component not found in scene");
            return;
        }
        // @ts-ignore
        pathEditor.flipHorizontal();
    },
    flipPathVertical() {
        const { director } = require('cc');
        const scene = director.getScene();
        if (scene == null)
            return;
        if (scene.name != "PathEditor") {
            console.warn("flipPathVertical not in PathEditor scene, current scene:", scene.name);
        }
        let pathEditor = scene.getComponentInChildren('PathEditor');
        if (pathEditor == null) {
            console.error("PathEditor component not found in scene");
            return;
        }
        // @ts-ignore
        pathEditor.flipVertical();
    },
    fitPathToCircle() {
        const { director } = require('cc');
        const scene = director.getScene();
        if (scene == null)
            return;
        if (scene.name != "PathEditor") {
            console.warn("fitPathToCircle not in PathEditor scene, current scene:", scene.name);
        }
        let pathEditor = scene.getComponentInChildren('PathEditor');
        if (pathEditor == null) {
            console.error("PathEditor component not found in scene");
            return;
        }
        // @ts-ignore
        pathEditor.fitToCircle();
    },
};
function findComponentByUuid(node, uuid, className) {
    if (!node)
        return null;
    // 检查当前节点是否有指定的组件，如果有且UUID匹配则返回
    const component = node.getComponent(className);
    if (component) {
        // console.log(`${node.name} has component ${className}, uuid: ${component.uuid}, target: ${uuid}`);
        if (component.uuid === uuid) {
            return component;
        }
    }
    // 无论当前节点是否有组件，都要搜索子节点
    for (let i = 0; i < node.children.length; i++) {
        let found = findComponentByUuid(node.children[i], uuid, className);
        if (found) {
            return found;
        }
    }
    return null;
}
async function createNewBullet(prefabName, prefabPath, sourceSprite, sourceSpriteList) {
    const scene = cc_1.director.getScene();
    const target = scene.getChildByName('Canvas');
    if (!target) {
        console.error("Canvas node not found");
        return;
    }
    let node = new cc_1.Node(prefabName);
    node.parent = target;
    node.setPosition(new cc_1.Vec3(0, 0, 0));
    let uiTransform = node.addComponent(cc_1.UITransform); // Ensure it has a transform component
    node.addComponent('FBoxCollider');
    let movable = node.addComponent('DefaultMove');
    node.addComponent('Bullet');
    node.addComponent('cc.BoxCollider2D');
    let sprite = node.addComponent(cc_1.Sprite);
    if (sourceSpriteList) {
        sprite.spriteFrame = sourceSprite;
        sprite.spriteAtlas = sourceSpriteList;
    }
    if (sourceSprite) {
        uiTransform.contentSize = new cc_1.Size(sourceSprite.rect.width, sourceSprite.rect.height);
    }
    const nodeUuid = node.uuid;
    try {
        await cce.Prefab.createPrefabAssetFromNode(nodeUuid, prefabPath);
        console.log(`Bullet prefab created: ${prefabPath}`);
        Editor.Message.request('asset-db', 'refresh-asset', prefabPath);
    }
    catch (e) {
        console.error('Failed to create bullet prefab:', e);
    }
}
async function createNewLevelObject(prefabName, prefabPath, sourceSprite) {
    const scene = cc_1.director.getScene();
    let node = new cc_1.Node(prefabName);
    scene.addChild(node);
    let uiTransform = node.addComponent(cc_1.UITransform); // Ensure it has a transform component
    let sprite = node.addComponent(cc_1.Sprite);
    if (sourceSprite) {
        sprite.spriteFrame = sourceSprite;
        uiTransform.contentSize = new cc_1.Size(sourceSprite.rect.width, sourceSprite.rect.height);
    }
    const nodeUuid = node.uuid;
    try {
        await cce.Prefab.createPrefabAssetFromNode(nodeUuid, prefabPath);
        console.log(`Level prefab created: ${prefabPath}`);
        Editor.Message.request('asset-db', 'refresh-asset', prefabPath);
    }
    catch (e) {
        console.error('Failed to create level prefab:', e, ', on asset: ', prefabPath);
    }
}
function getEventNodeParent() {
    const scene = cc_1.director.getScene();
    if (!scene) {
        console.error("Scene not found");
        return null;
    }
    const node = (0, cc_1.find)("Canvas/EventNodeParent");
    if (!node) {
        console.error("EventNodeParent not found");
        return null;
    }
    return node;
}
//# sourceMappingURL=data:application/json;base64,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
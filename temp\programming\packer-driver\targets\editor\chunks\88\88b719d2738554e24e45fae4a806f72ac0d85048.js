System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Node, LevelLayerUI, LevelBackgroundLayerUI, LevelUtils, _dec, _class, _crd, ccclass, property, LevelBaseUI;

  function _reportPossibleCrUseOfLevelData(extras) {
    _reporterNs.report("LevelData", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataBackgroundLayer(extras) {
    _reporterNs.report("LevelDataBackgroundLayer", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataLayer(extras) {
    _reporterNs.report("LevelDataLayer", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelLayerUI(extras) {
    _reporterNs.report("LevelLayerUI", "./LevelLayerUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelBackgroundLayerUI(extras) {
    _reporterNs.report("LevelBackgroundLayerUI", "./LevelBackgroundLayerUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelUtils(extras) {
    _reporterNs.report("LevelUtils", "./LevelUtils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelLayer(extras) {
    _reporterNs.report("LevelLayer", "./LevelLayer", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Node = _cc.Node;
    }, function (_unresolved_2) {
      LevelLayerUI = _unresolved_2.LevelLayerUI;
    }, function (_unresolved_3) {
      LevelBackgroundLayerUI = _unresolved_3.LevelBackgroundLayerUI;
    }, function (_unresolved_4) {
      LevelUtils = _unresolved_4.LevelUtils;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "154e0mDh+hNJbitYOMQLQ2O", "LevelBaseUI", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'Prefab']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("LevelBaseUI", LevelBaseUI = (_dec = ccclass('LevelBaseUI'), _dec(_class = class LevelBaseUI extends Component {
        constructor(...args) {
          super(...args);
          this._curLevelIndex = -1;
          // 当前关卡索引
          this._totalTime = 10;
          // 当前关卡的时长
          this._speed = 0;
          // 当前关卡的速度
          this._preLevelOffsetY = 0;
          // 上一关的关卡偏移量
          this._backgroundLayerNode = null;
          this._floorLayersNode = null;
          this._skyLayersNode = null;
          this._backgroundLayer = null;
          this._floorLayers = [];
          this._skyLayers = [];
        }

        get backgroundLayer() {
          return this._backgroundLayer;
        }

        get floorLayers() {
          return this._floorLayers;
        }

        get skyLayers() {
          return this._skyLayers;
        }

        get TotalTime() {
          return this._totalTime;
        }

        onLoad() {}

        async levelPrefab(levelData, levelInfo) {
          this._backgroundLayerNode = (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
            error: Error()
          }), LevelUtils) : LevelUtils).getOrAddNode(this.node, "BackgroundLayer");
          this._floorLayersNode = (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
            error: Error()
          }), LevelUtils) : LevelUtils).getOrAddNode(this.node, "FloorLayers");
          this._skyLayersNode = (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
            error: Error()
          }), LevelUtils) : LevelUtils).getOrAddNode(this.node, "SkyLayers");

          if (levelInfo.bFirstLoad) {
            await this._initByLevelData(levelData, levelInfo);
          } else {
            this._initByLevelData(levelData, levelInfo);
          }
        }

        switchLevel(time, levelIndex) {
          this._totalTime = time; // 释放上一关资源

          if (this._curLevelIndex !== -1) {
            this._removeNode(this._backgroundLayerNode, `level_${this._curLevelIndex}`);

            this._removeNode(this._floorLayersNode, `level_${this._curLevelIndex}`);

            this._removeNode(this._skyLayersNode, `level_${this._curLevelIndex}`);
          }

          this._curLevelIndex = levelIndex;
        }

        _removeNode(parentNode, name) {
          var node = parentNode.getChildByName(name);

          if (node) {
            node.removeFromParent();
          }
        }

        async _initByLevelData(data, levelInfo) {
          const levelBackground = (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
            error: Error()
          }), LevelUtils) : LevelUtils).getOrAddNode(this._backgroundLayerNode, `level_${levelInfo.levelIndex}`);
          const levelFloor = (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
            error: Error()
          }), LevelUtils) : LevelUtils).getOrAddNode(this._floorLayersNode, `level_${levelInfo.levelIndex}`);
          const levelSky = (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
            error: Error()
          }), LevelUtils) : LevelUtils).getOrAddNode(this._skyLayersNode, `level_${levelInfo.levelIndex}`);
          await this._initBackgroundLayer(levelBackground, data.backgroundLayer, levelInfo.bFirstLoad);
          await this._initLayers(levelFloor, this.floorLayers, data.floorLayers, levelInfo.bFirstLoad);
          await this._initLayers(levelSky, this.skyLayers, data.skyLayers, levelInfo.bFirstLoad);
        }

        async _initBackgroundLayer(parentNode, dataLayer, bFirstLoad) {
          if (dataLayer.backgrounds.length > 0) {
            let node = new Node('bg_0');
            const backgroundLayer = node.addComponent(_crd && LevelBackgroundLayerUI === void 0 ? (_reportPossibleCrUseOfLevelBackgroundLayerUI({
              error: Error()
            }), LevelBackgroundLayerUI) : LevelBackgroundLayerUI);
            parentNode.addChild(node);
            backgroundLayer.speed = dataLayer.speed;
            await backgroundLayer.initByLevelData(dataLayer, this._preLevelOffsetY, bFirstLoad);
            this._backgroundLayer = backgroundLayer;
          }
        }

        async _initLayers(parentNode, layers, dataLayers, bFirstLoad) {
          for (let i = 0; i < dataLayers.length; i++) {
            const layer = dataLayers[i];

            let levelLayer = this._addLayer(parentNode, `layer_${i}`);

            levelLayer.speed = layer.speed;
            await levelLayer.initByLevelData(layer, this._preLevelOffsetY, bFirstLoad);
            layers.push(levelLayer);
          }
        }

        _addLayer(parentNode, name) {
          var layerNode = new Node(name);
          var layerCom = layerNode.addComponent(_crd && LevelLayerUI === void 0 ? (_reportPossibleCrUseOfLevelLayerUI({
            error: Error()
          }), LevelLayerUI) : LevelLayerUI);
          parentNode.addChild(layerNode);
          return layerCom;
        }

        tick(deltaTime) {
          const layerUI = this.backgroundLayer; //!.node?.getComponent<LevelBackgroundLayerUI>(LevelBackgroundLayerUI);

          if (layerUI) {
            layerUI.tick(deltaTime);
          }

          this.floorLayers.forEach(layer => {
            const layerUI = layer; //.node?.getComponent<LevelLayerUI>(LevelLayerUI);

            if (layerUI) {
              layerUI.tick(deltaTime);
            }
          });
          this.skyLayers.forEach(layer => {
            const layerUI = layer; //.node?.getComponent<LevelLayerUI>(LevelLayerUI);

            if (layerUI) {
              layerUI.tick(deltaTime);
            }
          });
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=88b719d2738554e24e45fae4a806f72ac0d85048.js.map
import { _decorator, instantiate, Color, Component, UITransform, RichText, Vec3, Graphics, CCObject, VerticalTextAlignment, HorizontalTextAlignment } from 'cc';
const { ccclass, playOnFocus, executeInEditMode, property, disallowMultiple, menu, requireComponent  } = _decorator;
import { EDITOR } from 'cc/env';
import { FormationGroup, FormationPoint, SpawnGroup } from 'db://assets/bundles/common/script/game/data/WaveData';

@ccclass('FormationPointEditor')
@menu("怪物/编辑器/阵型点")
@requireComponent(Graphics)
@executeInEditMode(true)
@disallowMultiple(true)
export class FormationPointEditor extends Component {
    private _graphics: Graphics|null = null;
    public get graphics(): Graphics {
        if (!this._graphics) {
            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);
        }

        return this._graphics;
    }

    private _richText: RichText|null = null;
    public get richText(): RichText {
        if (!this._richText) {
            this._richText = this.node.getComponent(RichText) || this.node.addComponent(RichText);
            // this._richText.hideFlags = CCObject.Flags.AllHideMasks;
            this._richText.fontSize = 16;
            this._richText.verticalAlign = VerticalTextAlignment.CENTER;
            this._richText.horizontalAlign = HorizontalTextAlignment.CENTER;
            this._richText.fontColor = Color.BLACK;
        }

        return this._richText;
    }

    private _cachedIndex: number = -1;    
    private selected: boolean = false;
    public onFocusInEditor(): void {
        this.selected = true;
    }
    public onLostFocusInEditor(): void {
        this.selected = false;
    }

    public get formationPoint(): FormationPoint {
        let point = new FormationPoint();
        point.x = this.node.position.x;
        point.y = this.node.position.y;
        return point;
    }

    public set formationPoint(value: FormationPoint) {
        this.node.position = new Vec3(value.x, value.y, 0);
    }

    public update(dt: number) {
        const graphics = this.graphics;
        graphics.clear();
        
        const color = this.selected ? Color.YELLOW : Color.WHITE;
        graphics.fillColor = color;
        graphics.lineWidth = 2;
        graphics.circle(0, 0, 10);
        graphics.fill();
        graphics.stroke();

        const siblingIndex = this.node.getSiblingIndex();
        if (siblingIndex !== this._cachedIndex) {
            this._cachedIndex = siblingIndex;
            this.node.name = `Point_${siblingIndex}`;
            this.richText.string = `${siblingIndex}`;
            const uiTrans = this.getComponent(UITransform);
            uiTrans!.setContentSize(100, 100); // 让这个点好选中
        }
    }
}
System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, SingletonBase, GameMapRun, MyApp, logInfo, logWarn, GameIns, GameEnum, MapObjectPoolManager, _dec, _class2, _crd, ccclass, eRAND_STRATEGY, GameMapManager;

  function _reportPossibleCrUseOfSingletonBase(extras) {
    _reporterNs.report("SingletonBase", "db://assets/scripts/core/base/SingletonBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameMapRun(extras) {
    _reporterNs.report("GameMapRun", "db://assets/bundles/common/script/game/ui/map/GameMapRun", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResChapter(extras) {
    _reporterNs.report("ResChapter", "db://assets/bundles/common/script/autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfrandStrategy(extras) {
    _reporterNs.report("randStrategy", "db://assets/bundles/common/script/autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/bundles/common/script/app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogInfo(extras) {
    _reporterNs.report("logInfo", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogWarn(extras) {
    _reporterNs.report("logWarn", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "db://assets/bundles/common/script/game/GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "db://assets/bundles/common/script/game/const/GameEnum", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      SingletonBase = _unresolved_2.SingletonBase;
    }, function (_unresolved_3) {
      GameMapRun = _unresolved_3.default;
    }, function (_unresolved_4) {
      MyApp = _unresolved_4.MyApp;
    }, function (_unresolved_5) {
      logInfo = _unresolved_5.logInfo;
      logWarn = _unresolved_5.logWarn;
    }, function (_unresolved_6) {
      GameIns = _unresolved_6.GameIns;
    }, function (_unresolved_7) {
      GameEnum = _unresolved_7.GameEnum;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "e42a3IMbkJElKjYBn5UkQU3", "GameMapManager", undefined);

      __checkObsolete__(['_decorator', 'Node']);

      ({
        ccclass
      } = _decorator);

      eRAND_STRATEGY = /*#__PURE__*/function (eRAND_STRATEGY) {
        eRAND_STRATEGY[eRAND_STRATEGY["WEIGHT_PRUE"] = 1] = "WEIGHT_PRUE";
        eRAND_STRATEGY[eRAND_STRATEGY["WEIGHT_NO_REPEAT"] = 2] = "WEIGHT_NO_REPEAT";
        eRAND_STRATEGY[eRAND_STRATEGY["ORDER"] = 3] = "ORDER";
        return eRAND_STRATEGY;
      }(eRAND_STRATEGY || {});

      MapObjectPoolManager = class MapObjectPoolManager {
        constructor() {
          this._pools = new Map();
        }

        /**
         * 获取特定类型的节点
         * @param poolName 对象池名称（如"Backgrounds"）
         * @param prefabName 预制体名称（如"ForestBackground"）
         */
        get(poolName, prefabName) {
          const typePool = this._pools.get(poolName);

          if (!typePool) return null;
          const pool = typePool.get(prefabName);

          if (pool && pool.length > 0) {
            const node = pool.pop();
            node.active = true;
            return node;
          }

          return null;
        }
        /**
         * 回收节点
         * @param poolName 对象池名称
         * @param node 要回收的节点
         */


        put(poolName, node) {
          if (!node) return;
          const prefabName = node.name;
          node.active = false;
          node.removeFromParent();

          if (!this._pools.has(poolName)) {
            this._pools.set(poolName, new Map());
          }

          const typePool = this._pools.get(poolName);

          if (!typePool.has(prefabName)) {
            typePool.set(prefabName, []);
          }

          typePool.get(prefabName).push(node);
        } // 清空指定对象池


        clearPool(poolName) {
          if (this._pools.has(poolName)) {
            const typePool = this._pools.get(poolName);

            typePool.forEach(pool => {
              pool.forEach(node => node.destroy());
            });

            this._pools.delete(poolName);
          }
        } // 清空所有对象池


        clearAll() {
          this._pools.forEach((typePool, poolName) => {
            typePool.forEach(pool => {
              pool.forEach(node => node.destroy());
            });
          });

          this._pools.clear();
        }

      };

      _export("GameMapManager", GameMapManager = (_dec = ccclass('GameMapManager'), _dec(_class2 = class GameMapManager extends (_crd && SingletonBase === void 0 ? (_reportPossibleCrUseOfSingletonBase({
        error: Error()
      }), SingletonBase) : SingletonBase) {
        get chapterConfig() {
          return this._chapterConfig;
        }

        // 随机出来的关卡列表（这个列表长度可能会大于章节表中的关卡数量）
        get levelList() {
          return this._levelList;
        }

        get mapObjectPoolManager() {
          return this._objectPoolManager;
        }

        constructor() {
          super();
          this._chapterID = 0;
          this._chapterConfig = null;
          this._levelList = [];
          this._objectPoolManager = new MapObjectPoolManager();
        }

        start() {}

        async preLoad(chapterID) {
          this._chapterID = chapterID;

          this._initLevelList();

          await (_crd && GameMapRun === void 0 ? (_reportPossibleCrUseOfGameMapRun({
            error: Error()
          }), GameMapRun) : GameMapRun).instance.initData(this._levelList);
        }

        switchSectionState(state) {
          (_crd && GameMapRun === void 0 ? (_reportPossibleCrUseOfGameMapRun({
            error: Error()
          }), GameMapRun) : GameMapRun).instance.switchSectionState(state);
        }
        /**
         * 更新游戏逻辑
         * @param {number} dt 每帧的时间间隔
         */


        updateGameLogic(dt) {
          (_crd && GameMapRun === void 0 ? (_reportPossibleCrUseOfGameMapRun({
            error: Error()
          }), GameMapRun) : GameMapRun).instance.updateGameLogic(dt);
        }

        reset() {
          this._objectPoolManager.clearAll();

          (_crd && GameMapRun === void 0 ? (_reportPossibleCrUseOfGameMapRun({
            error: Error()
          }), GameMapRun) : GameMapRun).instance.reset();
        }

        clear() {
          (_crd && GameMapRun === void 0 ? (_reportPossibleCrUseOfGameMapRun({
            error: Error()
          }), GameMapRun) : GameMapRun).instance.clear();
        }

        async reInitLevelList() {
          this._initLevelList();

          await (_crd && GameMapRun === void 0 ? (_reportPossibleCrUseOfGameMapRun({
            error: Error()
          }), GameMapRun) : GameMapRun).instance.initData(this._levelList);
          (_crd && GameMapRun === void 0 ? (_reportPossibleCrUseOfGameMapRun({
            error: Error()
          }), GameMapRun) : GameMapRun).instance.switchSectionState((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).eSECTION_STATE.RESET);
        } // 根据策略随机出关卡列表


        _initLevelList() {
          this._chapterConfig = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbResChapter.get(this._chapterID);
          ;

          if (this._chapterConfig == null) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("GameMapManager", `can not find chapter config by id ${this._chapterID}`);
            return;
          } // 随机出关卡组


          const levelGroupList = this._randomSelection(this._chapterConfig.strategyList, this._chapterConfig.levelGroupCount, this._chapterConfig.strategy);

          if (levelGroupList.length === 0) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)('GameMapManager', " levelGroupList is null");
            return;
          } // 随机出关卡


          this._levelList = [];

          for (const levelGroupID of levelGroupList) {
            const levelGroupData = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).lubanTables.TbResLevelGroup.get(levelGroupID);

            if (levelGroupData == null) {
              (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
                error: Error()
              }), logWarn) : logWarn)('GameMapManager', " levelGroupData is null");
              continue;
            }

            this._levelList.push(...this._randomSelection(levelGroupData.normSTList, levelGroupData.normLevelCount, levelGroupData.normLevelST));

            this._levelList.push(...this._randomSelection(levelGroupData.bossSTList, levelGroupData.bossLevelCount, levelGroupData.bossLevelST));
          }

          (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
            error: Error()
          }), logInfo) : logInfo)('GameMapManager', ` _levelList: ${this._levelList}`);
        }
        /**
         * 策略：
         * 1.严格按权重比例随机选择元素
         * 2.严格按权重比例随机选择元素，不重复
         * 3.按顺序选择元素
         * @param STList 带权重的元素数组
         * @param count 需要选择的元素数量
         * @returns 选中元素的ID数组
         */


        _randomSelection(STList, count, strategy) {
          if (STList.length === 0 || count <= 0) return [];
          const results = [];

          if (strategy === eRAND_STRATEGY.WEIGHT_PRUE) {
            // 计算总权重
            const totalWeight = STList.reduce((sum, item) => sum + item.Weight, 0); // 如果所有权重都为0，则转为均匀随机

            if (totalWeight === 0) {
              for (let i = 0; i < count; i++) {
                const randomIndex = Math.floor((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).battleManager.random() * STList.length);
                results.push(STList[randomIndex].ID);
              }

              return results;
            } // 严格按权重比例随机选择


            for (let i = 0; i < count; i++) {
              // 生成[0, totalWeight)区间的随机数
              const randomValue = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.random() * totalWeight; // 遍历查找随机数对应的元素

              let cumulativeWeight = 0;

              for (const item of STList) {
                cumulativeWeight += item.Weight;

                if (randomValue < cumulativeWeight) {
                  results.push(item.ID);
                  break;
                }
              }
            }
          } else if (strategy === eRAND_STRATEGY.WEIGHT_NO_REPEAT) {
            // 计算总权重
            const totalWeight = STList.reduce((sum, item) => sum + item.Weight, 0); // 如果所有权重都为0，则转为均匀随机

            if (totalWeight === 0) {
              for (let i = 0; i < count; i++) {
                let randomIndex = Math.floor((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).battleManager.random() * STList.length); // 避免重复选择相同的ID

                if (i > 0 && STList[randomIndex].ID === results[i - 1]) {
                  // 如果与上一次选择的相同，选择下一个（循环）
                  randomIndex = (randomIndex + 1) % STList.length;
                }

                results.push(STList[randomIndex].ID);
              }

              return results;
            } // 创建副本以避免修改原始数据


            const tempList = [...STList];
            let lastSelectedId = -1;

            for (let i = 0; i <= count; i++) {
              // 如果上一次选择的ID存在，且它在当前列表中，调整其位置
              if (lastSelectedId !== -1) {
                const lastSelectedIndex = tempList.findIndex(item => item.ID === lastSelectedId);

                if (lastSelectedIndex !== -1 && lastSelectedIndex < tempList.length - 1) {
                  // 将上一次选择的ID与下一个元素交换位置
                  [tempList[lastSelectedIndex], tempList[lastSelectedIndex + 1]] = [tempList[lastSelectedIndex + 1], tempList[lastSelectedIndex]];
                }
              } // 生成[0, totalWeight)区间的随机数


              const randomValue = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.random() * totalWeight; // 遍历查找随机数对应的元素

              let cumulativeWeight = 0;
              let selectedIndex = -1;

              for (let j = 0; j < tempList.length; j++) {
                cumulativeWeight += tempList[j].Weight;

                if (randomValue < cumulativeWeight) {
                  selectedIndex = j;
                  break;
                }
              } // 如果未找到有效索引，选择最后一个元素


              if (selectedIndex === -1) {
                selectedIndex = tempList.length - 1;
              } // 获取选中的ID


              const selectedId = tempList[selectedIndex].ID;
              results.push(selectedId); // 更新上一次选择的ID

              lastSelectedId = selectedId;
            }
          } else if (strategy === eRAND_STRATEGY.ORDER) {
            // 按顺序选择元素，遇到ID为0时从数组开头重新开始
            let currentIndex = 0;

            for (let i = 0; i < count; i++) {
              // 如果当前元素的ID为0，则重置到数组开头
              if (STList[currentIndex].ID === 0) {
                currentIndex = 0; // 如果数组开头的元素ID也为0，则跳过所有ID为0的元素

                while (currentIndex < STList.length && STList[currentIndex].ID === 0) {
                  currentIndex++;
                } // 如果所有元素ID都为0，则无法选择，跳出循环


                if (currentIndex >= STList.length) {
                  break;
                }
              } // 选择当前元素


              results.push(STList[currentIndex].ID); // 移动到下一个元素

              currentIndex++; // 如果到达数组末尾，回到开头

              if (currentIndex >= STList.length) {
                currentIndex = 0;
              }
            }
          }

          return results;
        }

      }) || _class2));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=ef17f0fe450d2bbcc54bc7560be5d0c8aed84332.js.map
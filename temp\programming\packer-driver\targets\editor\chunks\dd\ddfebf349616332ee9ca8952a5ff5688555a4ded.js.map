{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/event/DataEvent.ts"], "names": ["DataEvent", "ItemsRefresh", "EquipSlotRefresh", "GamePvpGetAward", "GamePvpGetList", "GamePvpMatchSuc", "BattleItemClick", "RogueSelectClick", "TaskRefresh", "RoleBaseAttrChange", "RoleExtAttrChange", "MailRefresh", "FriendRefresh", "FusionCellClick"], "mappings": ";;;;;;;;;;;;;;2BAAaA,S,GAAY;AACrBC,QAAAA,YAAY,EAAE,wBADO;AAErBC,QAAAA,gBAAgB,EAAE,4BAFG;AAGrBC,QAAAA,eAAe,EAAE,2BAHI;AAIrBC,QAAAA,cAAc,EAAE,0BAJK;AAKrBC,QAAAA,eAAe,EAAE,2BALI;AAMrBC,QAAAA,eAAe,EAAE,2BANI;AAOrBC,QAAAA,gBAAgB,EAAE,4BAPG;AAQrBC,QAAAA,WAAW,EAAE,uBARQ;AASrBC,QAAAA,kBAAkB,EAAE,8BATC;AAUrBC,QAAAA,iBAAiB,EAAE,6BAVE;AAWrBC,QAAAA,WAAW,EAAE,uBAXQ;AAYrBC,QAAAA,aAAa,EAAE,yBAZM;AAarBC,QAAAA,eAAe,EAAE;AAbI,O", "sourcesContent": ["export const DataEvent = {\n    ItemsRefresh: 'DataEvent_ItemsRefresh',\n    EquipSlotRefresh: 'DataEvent_EquipSlotRefresh',\n    GamePvpGetAward: 'DataEvent_GamePvpGetAward',\n    GamePvpGetList: 'DataEvent_GamePvpGetList',\n    GamePvpMatchSuc: 'DataEvent_GamePvpMatchSuc',\n    BattleItemClick: 'DataEvent_BattleItemClick',\n    RogueSelectClick: 'DataEvent_RogueSelectClick',\n    TaskRefresh: 'DataEvent_TaskRefresh',\n    RoleBaseAttrChange: 'DataEvent_RoleBaseAttrChange',\n    RoleExtAttrChange: 'DataEvent_RoleExtAttrChange',\n    MailRefresh: 'DataEvent_MailRefresh',\n    FriendRefresh: 'DataEvent_FriendRefresh',\n    FusionCellClick: 'DataEvent_FusionCellClick',\n}"]}

import { director, Rect } from "cc";
import { logWarn } from "db://assets/scripts/utils/Logger";
import Long from "long";
import { GameConst } from "../../../../../scripts/core/base/GameConst";
import { SingletonBase } from "../../../../../scripts/core/base/SingletonBase";
import { UIMgr } from "../../../../../scripts/core/base/UIMgr";
import { MyApp } from "../../app/MyApp";
import { ModeType, ResGameMode } from "../../autogen/luban/schema";
import { DataMgr } from "../../data/DataManager";
import EventManager, { EventMgr } from "../../event/EventManager";
import { HomeUIEvent } from "../../event/HomeUIEvent";
import { GameReviveUI } from "../../ui/gameui/game/GameReviveUI";
import { MBoomUI } from "../../ui/gameui/game/MBoomUI";
import { LoadingUI } from "../../ui/gameui/LoadingUI";
import { BottomUI } from "../../ui/home/<USER>";
import { HomeUI } from "../../ui/home/<USER>";
import { TopUI } from "../../ui/home/<USER>";
import { BulletSystem } from "../bullet/BulletSystem";
import { GameEvent } from "../event/GameEvent";
import { GameIns } from "../GameIns";
import { lcgRand } from "../utils/Rand";
import { GameMain } from "../scenes/GameMain";
import { RogueUI } from "../../ui/gameui/game/RogueUI";
import { GameStartInfo } from "./GameStartInfo";
import { GameEnum } from "../const/GameEnum";
import { BundleName } from "../../const/BundleConst";

export class BattleManager extends SingletonBase<BattleManager> {

    initBattleEnd: boolean = false;
    isGameStart: boolean = false;
    animSpeed: number = 1;

    startInfo: GameStartInfo|null = null;

    _modeConfig: ResGameMode | null = null;
    curLevel: number = 0;//小阶段

    public get modeConfig(): ResGameMode | null { return this._modeConfig; }

    _loadTotal = 0;
    _loadCount = 0;
    _rand: lcgRand = new lcgRand();

    constructor() {
        super();
        EventManager.Instance.on(GameEvent.onNetGameStart, this.onNetGameStart, this);
        EventManager.Instance.on(GameEvent.onNetGameOver, this.onNetGameOver, this);
    }

    //战斗开始接口
    startGameByMode(startInfo:GameStartInfo) {
        this.startInfo = startInfo;
        this._rand.seed = Long.fromNumber(startInfo.randSeed);
        let modeConfig = MyApp.lubanTables.TbResGameMode.get(startInfo.modeID);
        if (modeConfig == null) {
            logWarn("BattleManager", `can not find mode config by id ${startInfo.modeID}`);
            return;
        }
        this._modeConfig = modeConfig;
        this.curLevel = startInfo.curLevel;

        DataMgr.gameLogic.cmdGameStart(startInfo.modeID);
    }

    async onNetGameStart() {
        await UIMgr.openUI(LoadingUI);
        EventMgr.emit(HomeUIEvent.Leave);
        await MyApp.resMgr.loadBundle(BundleName.GameFight);
        await new Promise<void>((resolve) => {
            GameIns.gameResManager.preloadCommon(()=>{
                resolve();
            }, null)
        })
        console.log("ybgg BattleManager onNetGameStart gameResManager preloadCommon done");
        GameIns.mainPlaneManager.setPlaneData(this.startInfo!.mainPlane);

        director.loadScene("Game");
    }

    onNetGameOver() {

    }

    isSectionFinish() {
        // todo
        return GameIns.waveManager.isAllWaveCompleted;
    }

    clear() {
        GameIns.enemyManager.clear();
        GameIns.bossManager.clear();
        GameIns.waveManager.reset();
        GameIns.mainPlaneManager.clear();
        GameIns.gameMapManager.clear();
        GameIns.hurtEffectManager.clear();
        GameIns.gameStateManager.reset();
        BulletSystem.destroy();
        GameIns.rogueManager.clear();
        this.isGameStart = false;
    }

    subReset() {
        this.animSpeed = 1;
        this.isGameStart = false;
        this.initBattleEnd = false;

        GameIns.mainPlaneManager.subReset();
        GameIns.gameStateManager.reset();
        GameIns.waveManager.reset();
        GameIns.enemyManager.subReset();
        GameIns.bossManager.subReset();
        //GameIns.gameMapManager.reset();
    }

    /**
     * 检查所有资源是否加载完成
     */
    async checkLoadFinish() {
        this._loadCount++;
        // let loadingUI = UIMgr.get(LoadingUI)
        // loadingUI.updateProgress(this._loadCount / this._loadTotal)
        if (this._loadCount >= this._loadTotal) {
            await UIMgr.closeUI(LoadingUI)
            EventManager.Instance.emit(GameEvent.GameLoadEnd)
            this.initBattle();
        }
    }

    addLoadCount(count: number) {
        this._loadTotal += count;
    }

    startLoading() {
        GameIns.rogueManager.reset();
        GameIns.mainPlaneManager.preload();
        GameIns.hurtEffectManager.preLoad();//伤害特效资源
        GameIns.gameMapManager.preLoad(this._modeConfig!.chapterID);//地图资源
        GameIns.enemyManager.preLoad();//敌人资源
        GameIns.bossManager.preLoad();//boss资源
    }

    initBattle() {
        GameIns.mainPlaneManager.mainPlane!.planeIn();
        BulletSystem.init(new Rect(0, 0, GameConst.ViewBattleWidth, GameConst.ViewHeight));
    }

    onPlaneIn() {
        let endCallback = () => {
            this.initBattleEnd = true;
            EventManager.Instance.emit(GameEvent.GameMainPlaneIn)
        }
        // if (this._modeConfig!.rogueFirst > 0){
            UIMgr.openUI(RogueUI, 10001,(buffIDs:number[])=>{
                buffIDs.forEach(buffID => {
                    GameIns.mainPlaneManager.mainPlane!.buffComp.ApplyBuff(false, buffID);
                })
                endCallback();
            },3)
        // }else{
        //     endCallback();
        // }


    }

    beginBattle() {
        if (this.initBattleEnd && !this.isGameStart) {
            this.isGameStart = true;
            EventManager.Instance.emit(GameEvent.GameStart)
            GameIns.waveManager.gameStart();
            GameIns.gameStateManager.gameStart();

            GameIns.mainPlaneManager.mainPlane!.begine();
        }
    }

    /**
     * 更新游戏逻辑
     * @param {number} dt 每帧的时间间隔
     */
    updateGameLogic(dt: number) {
        dt = dt * this.animSpeed;
        if (GameIns.gameStateManager.isGameOver()) {
            if (GameIns.gamePlaneManager) {
                GameIns.gamePlaneManager.enemyTarget = null;
            }
            return;
        }

        if (GameIns.gameStateManager.isInBattle() || GameIns.gameStateManager.isGameWillOver()) {
            GameIns.gameDataManager.gameTime += dt;
            GameIns.gameMapManager.updateGameLogic(dt)
            GameIns.gamePlaneManager.updateGameLogic(dt);
            GameIns.mainPlaneManager.updateGameLogic(dt);
            GameIns.waveManager.updateGameLogic(dt);
            GameIns.enemyManager.updateGameLogic(dt);
            GameIns.bossManager.updateGameLogic(dt);
            GameIns.gameStateManager.updateGameLogic(dt);

            //子弹发射器系统
            BulletSystem.tick(dt);

            GameIns.fColliderManager.updateGameLogic(dt);
        } else if (GameIns.gamePlaneManager) {
            GameIns.gamePlaneManager.enemyTarget = null;
        }
    }

    setTouchState(isTouch: boolean) {
        if (isTouch) {
            this.beginBattle();
            this.animSpeed = 1;
        } else {
            this.animSpeed = 0.2;
        }
        GameIns.enemyManager.setAnimSpeed(this.animSpeed);
        GameIns.bossManager.setAnimSpeed(this.animSpeed);
        GameIns.mainPlaneManager.mainPlane!.setAnimSpeed(this.animSpeed);
        GameMain.instance?.GameFightUI!.setTouchState(isTouch);
    }

    /**
     * 战斗复活逻辑
     */
    relifeBattle() {
        GameIns.gameStateManager.gameResume();
        GameIns.mainPlaneManager.revive();
    }

    setGameEnd(isWin: boolean) {
        if (isWin) {
            if (this.checkNextlevel()) {//判断是否有下一关
                GameIns.gameMapManager.switchSectionState(GameEnum.eSECTION_STATE.FINISH);
                UIMgr.openUI(RogueUI, (buffIDs:number[]) => {
                    buffIDs.forEach(buffID => {
                        GameIns.mainPlaneManager.mainPlane!.buffComp.ApplyBuff(false, buffID);
                    })
                    this.startNextBattle();
                });
                return;
            }
        } else {
            GameIns.gameStateManager.gamePause();
            if (GameIns.mainPlaneManager.checkCanRevive()) {// 判断是否可以复活
                UIMgr.openUI(GameReviveUI);
                return;
            }
        }
        GameIns.mainPlaneManager.mainPlane!.hpNode!.active = false;
        this.endBattle();
        GameMain.instance!.showGameResult(isWin);
        DataMgr.gameLogic.cmdGameEnd(GameIns.gameDataManager.getGameResultData(), GameIns.gameDataManager.getGameLevelResultData());
    }

    checkNextlevel() {
        if (this._modeConfig!.modeType == ModeType.ENDLESS) {
            // 如果关卡全部跑完，重新随机一次关卡列表
            if (this.curLevel + 1 >= GameIns.gameMapManager.chapterConfig!.levelCount) 
            {
                GameIns.gameMapManager.reInitLevelList();
            }
            return true;
        }
        return this.curLevel + 1 <= GameIns.gameMapManager.chapterConfig!.levelCount;
    }
    /**
     * 继续下一场战斗
     */
    startNextBattle() {
        this.subReset();
        this.curLevel += 1;
        this.initBattle();
        GameIns.gameMapManager.switchSectionState(GameEnum.eSECTION_STATE.START);
    }

    /**
     * 结束战斗
     */
    endBattle() {
        BulletSystem.destroy(false, false);
        GameMain.instance?.GameFightUI!.reset();
        GameIns.gameStateManager.gameOver();
    }


    async quitBattle() {
        this.clear();
        UIMgr.closeUI(MBoomUI)
        await UIMgr.openUI(HomeUI)
        await UIMgr.openUI(BottomUI)
        await UIMgr.openUI(TopUI)
        director.loadScene("Main");
    }

    bossChangeFinish(tip: string) {
        // const bossEnterDialog = GameIns.uiManager.getDialog(BossEnterDialog.default);
        // if (bossEnterDialog) {
        //     bossEnterDialog.node.active = true;
        //     GameIns.mainPlaneManager.moveAble = false;
        //     bossEnterDialog.showTips(bossName);
        // }
    }

    bossWillEnter() {
        // GameIns.mainPlaneManager.mainPlane!.setFireEnable(false);
        // GameIns.mainPlaneManager.moveAble = false;
    }
    /**
     * 开始Boss战斗
     */
    bossFightStart() {
        GameIns.mainPlaneManager.mainPlane!.setFireEnable(true);
        GameIns.mainPlaneManager.mainPlane!.setMoveAble(true);
        GameIns.bossManager.bossFightStart();
    }

    random(): number {
        return this._rand.random();
    }
}
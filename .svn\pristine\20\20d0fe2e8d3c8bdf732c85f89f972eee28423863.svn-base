import { _decorator, Component, Node } from 'cc';

const { ccclass, property } = _decorator;

@ccclass('LevelUtils')
export class LevelUtils {
    public static getOrAddNode(node_parent: Node, name: string): Node {
        var node = node_parent.getChildByName(name);
        if (node == null) {
            node = new Node(name);
            node_parent.addChild(node);
        }
        return node;
    }

    /**
     * 从资源路径中提取特定部分
     * @param path - 资源路径
     * @param suffix - 要移除的后缀（默认为 '/spriteFrame'）
     * @returns 返回路径中指定部分
     */
    public static extractPathPart(path: string, suffix: string = '/spriteFrame'): string {
        // 移除指定后缀
        const cleanPath = path.endsWith(suffix) 
            ? path.substring(0, path.length - suffix.length)
            : path;
        
        // 获取最后一部分
        const parts = cleanPath.split('/');
        return parts[parts.length - 1];
    }
}


 
System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, UITransform, Vec3, GameConst, logDebug, logError, GameIns, _dec, _class, _crd, ccclass, property, LevelNodeCheckOutScreen;

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "db://assets/scripts/core/base/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogDebug(extras) {
    _reporterNs.report("logDebug", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogError(extras) {
    _reporterNs.report("logError", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "db://assets/bundles/common/script/game/GameIns", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      UITransform = _cc.UITransform;
      Vec3 = _cc.Vec3;
    }, function (_unresolved_2) {
      GameConst = _unresolved_2.GameConst;
    }, function (_unresolved_3) {
      logDebug = _unresolved_3.logDebug;
      logError = _unresolved_3.logError;
    }, function (_unresolved_4) {
      GameIns = _unresolved_4.GameIns;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "b9ca6dUOAZD+Jb5nPNykTEf", "LevelNodeCheckOutScreen", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'UITransform', 'Vec3']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("LevelNodeCheckOutScreen", LevelNodeCheckOutScreen = (_dec = ccclass('LevelNodeCheckOutScreen'), _dec(_class = class LevelNodeCheckOutScreen extends Component {
        constructor() {
          super(...arguments);
          this._remove_thresHold = 0;
          this._height = 0;
          this._worldPos = new Vec3();
          this._poolName = '';
        }

        init(poolName) {
          this._poolName = poolName;
          var uiTransform = this.node.getComponent(UITransform);

          if (uiTransform) {
            this._height = uiTransform.height;
          } else {
            (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
              error: Error()
            }), logError) : logError)('LevelNodeCheckOutScreen', "\u8282\u70B9" + this.node.name + "\u7F3A\u5C11UITransform\u7EC4\u4EF6");
          }

          this._remove_thresHold = (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).BATTLE_VIEW_BOTTOM;
        }

        update(deltaTime) {
          if (this.node.isValid === false || this.node.active === false) return;
          if (this._height === 0) return;
          this.node.getWorldPosition(this._worldPos);
          var topPosition = this._worldPos.y + this._height / 2;

          if (topPosition < this._remove_thresHold) {
            this._recycleNode();
          }
        }

        _recycleNode() {
          if (this._poolName.length > 0) {
            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)('LevelNodeCheckOutScreen', " \u8282\u70B9" + this.node.name + "\u5DF2\u56DE\u6536\u5230\u5BF9\u8C61\u6C60" + this._poolName);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gameMapManager.mapObjectPoolManager.put(this._poolName, this.node);
          } else {
            // 没有指定对象池时直接销毁
            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)('LevelNodeCheckOutScreen', "\u8282\u70B9" + this.node.name + "\u5DF2\u9500\u6BC1");
            this.node.destroy();
          }
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=9afbbdab570a4771717e85d7d7cc22b0552ce61a.js.map
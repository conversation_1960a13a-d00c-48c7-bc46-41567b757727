System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "cc/env", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10", "__unresolved_11", "__unresolved_12", "__unresolved_13", "__unresolved_14", "__unresolved_15", "__unresolved_16", "__unresolved_17"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, instantiate, Node, Prefab, size, UIOpacity, Vec3, view, MyApp, AttributeConst, GameConst, Plane, EDITOR, PlaneBase, Bullet, Emitter, <PERSON>BoxCollider, ColliderGroupType, GameResourceList, GameIns, eEntityTag, EffectLayer, EnemyPlaneBase, MainPlaneDebug, MainPlaneStat, GameFightUI, _dec, _dec2, _dec3, _dec4, _class, _class2, _descriptor, _descriptor2, _descriptor3, _crd, ccclass, property, MainPlane;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/bundles/common/script/app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAttributeConst(extras) {
    _reporterNs.report("AttributeConst", "db://assets/bundles/common/script/const/AttributeConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAttributeData(extras) {
    _reporterNs.report("AttributeData", "db://assets/bundles/common/script/data/base/AttributeData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../../../../../../scripts/core/base/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMainPlaneData(extras) {
    _reporterNs.report("MainPlaneData", "db://assets/bundles/common/script/data/plane/MainPlaneData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlane(extras) {
    _reporterNs.report("Plane", "db://assets/bundles/common/script/ui/Plane", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneBase(extras) {
    _reporterNs.report("PlaneBase", "db://assets/bundles/common/script/game/ui/plane/PlaneBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBullet(extras) {
    _reporterNs.report("Bullet", "../../../bullet/Bullet", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitter(extras) {
    _reporterNs.report("Emitter", "../../../bullet/Emitter", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFBoxCollider(extras) {
    _reporterNs.report("FBoxCollider", "../../../collider-system/FBoxCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFCollider(extras) {
    _reporterNs.report("FCollider", "../../../collider-system/FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfColliderGroupType(extras) {
    _reporterNs.report("ColliderGroupType", "../../../collider-system/FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameResourceList(extras) {
    _reporterNs.report("GameResourceList", "../../../const/GameResourceList", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEntityTag(extras) {
    _reporterNs.report("eEntityTag", "../../base/Entity", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEffectLayer(extras) {
    _reporterNs.report("EffectLayer", "../../layer/EffectLayer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyPlaneBase(extras) {
    _reporterNs.report("EnemyPlaneBase", "../enemy/EnemyPlaneBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMainPlaneDebug(extras) {
    _reporterNs.report("MainPlaneDebug", "./MainPlaneDebug", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMainPlaneStat(extras) {
    _reporterNs.report("MainPlaneStat", "./MainPlaneStat", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameFightUI(extras) {
    _reporterNs.report("GameFightUI", "../../layer/GameFightUI", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      instantiate = _cc.instantiate;
      Node = _cc.Node;
      Prefab = _cc.Prefab;
      size = _cc.size;
      UIOpacity = _cc.UIOpacity;
      Vec3 = _cc.Vec3;
      view = _cc.view;
    }, function (_unresolved_2) {
      MyApp = _unresolved_2.MyApp;
    }, function (_unresolved_3) {
      AttributeConst = _unresolved_3.AttributeConst;
    }, function (_unresolved_4) {
      GameConst = _unresolved_4.GameConst;
    }, function (_unresolved_5) {
      Plane = _unresolved_5.Plane;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }, function (_unresolved_6) {
      PlaneBase = _unresolved_6.default;
    }, function (_unresolved_7) {
      Bullet = _unresolved_7.Bullet;
    }, function (_unresolved_8) {
      Emitter = _unresolved_8.Emitter;
    }, function (_unresolved_9) {
      FBoxCollider = _unresolved_9.default;
    }, function (_unresolved_10) {
      ColliderGroupType = _unresolved_10.ColliderGroupType;
    }, function (_unresolved_11) {
      GameResourceList = _unresolved_11.default;
    }, function (_unresolved_12) {
      GameIns = _unresolved_12.GameIns;
    }, function (_unresolved_13) {
      eEntityTag = _unresolved_13.eEntityTag;
    }, function (_unresolved_14) {
      EffectLayer = _unresolved_14.default;
    }, function (_unresolved_15) {
      EnemyPlaneBase = _unresolved_15.default;
    }, function (_unresolved_16) {
      MainPlaneDebug = _unresolved_16.default;
    }, function (_unresolved_17) {
      MainPlaneStat = _unresolved_17.default;
    }, function (_unresolved_18) {
      GameFightUI = _unresolved_18.GameFightUI;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "81e41b6hKRNqYJbS6uK9LDf", "MainPlane", undefined);

      __checkObsolete__(['_decorator', 'instantiate', 'Node', 'Prefab', 'size', 'UIOpacity', 'Vec3', 'view']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("MainPlane", MainPlane = (_dec = ccclass("MainPlane"), _dec2 = property(Node), _dec3 = property(Node), _dec4 = property(Node), _dec(_class = (_class2 = class MainPlane extends (_crd && PlaneBase === void 0 ? (_reportPossibleCrUseOfPlaneBase({
        error: Error()
      }), PlaneBase) : PlaneBase) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "planeParent", _descriptor, this);

          _initializerDefineProperty(this, "NodeEmitter", _descriptor2, this);

          _initializerDefineProperty(this, "InvincibleNode", _descriptor3, this);

          this.m_moveEnable = true;
          // 是否允许移动
          this.emitterComp = null;
          // 发射器
          this._hurtActTime = 0;
          // 受伤动画时间
          this._hurtActDuration = 0.5;
          // 受伤动画持续时间
          this._planeData = null;
          //飞机数据
          this._plane = null;
          //飞机显示节点
          this._fireEnable = true;
          //是否允许射击
          this._unColliderTime = 0;
          //无敌时间
          this.statData = new (_crd && MainPlaneStat === void 0 ? (_reportPossibleCrUseOfMainPlaneStat({
            error: Error()
          }), MainPlaneStat) : MainPlaneStat)();
          this._maxMoveSpeedX = 2400;
          // 水平方向最大移动速度
          this._maxMoveSpeedY = 4000;
          // 垂直方向最大移动速度
          this._screenRatio = 1;
          this._battleWidth = 0;
          // 实际战斗宽度
          this._targetPosition = new Vec3();
          // 目标位置
          this._lastPosition = new Vec3();
          // 上一帧位置
          this.hpRecoveryTime = 0;
          this._nuclearNum = 0;
        }

        get nuclearNum() {
          return this._nuclearNum;
        }

        addNuclear(num) {
          this._nuclearNum += num;
        }

        addScore(num) {
          this.statData.score += num;
        }

        onLoad() {
          // 计算屏幕适配比例
          this._calculateScreenRatio(); // 初始化位置


          this.node.getPosition(this._lastPosition);

          this._targetPosition.set(this._lastPosition);
        } // 纯表现层业务，请勿将逻辑代码写到这里


        update(dt) {
          dt = dt * (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.animSpeed;
          this._hurtActTime += dt;

          if (this._unColliderTime > 0) {
            this._unColliderTime -= dt;

            if (this._unColliderTime <= 0) {
              this.cancelUncollide();
            }
          }

          this.smoothMoveToTarget(dt);
        }

        updateGameLogic(dt) {
          super.updateGameLogic(dt);

          while (this.hpRecoveryTime <= (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameDataManager.gameTime) {
            this.hpRecoveryTime += 1;
            let hpRecovery = this.attribute.getHPRecovery();
            this.addHp(hpRecovery);
          }
        }

        async initPlane(planeData) {
          this._planeData = planeData;
          this.resetPlane();
          this._nuclearNum = planeData.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).NuclearMax); //加载飞机显示

          const prefab = await (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadAsync(planeData.recoursePrefab, Prefab);
          let plane = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).planeMgr.getPlane(planeData, prefab);
          this._plane = plane.getComponent(_crd && Plane === void 0 ? (_reportPossibleCrUseOfPlane({
            error: Error()
          }), Plane) : Plane);
          this.planeParent.addChild(plane);
          this.collideComp = this.getComponentInChildren(_crd && FBoxCollider === void 0 ? (_reportPossibleCrUseOfFBoxCollider({
            error: Error()
          }), FBoxCollider) : FBoxCollider);
          this.collideComp.init(this, size(128, 128)); // 初始化碰撞组件

          this.collideComp.groupType = (_crd && ColliderGroupType === void 0 ? (_reportPossibleCrUseOfColliderGroupType({
            error: Error()
          }), ColliderGroupType) : ColliderGroupType).PLAYER;
          this.addTag((_crd && eEntityTag === void 0 ? (_reportPossibleCrUseOfeEntityTag({
            error: Error()
          }), eEntityTag) : eEntityTag).Player); // 临时: 设置飞机发射组件
          // this.setEmitter();

          super.init();

          if (EDITOR) {
            this.node.addComponent(_crd && MainPlaneDebug === void 0 ? (_reportPossibleCrUseOfMainPlaneDebug({
              error: Error()
            }), MainPlaneDebug) : MainPlaneDebug);
          }
        }

        resetPlane() {
          var _this$_plane;

          (_this$_plane = this._plane) == null || _this$_plane.reset(); // 禁用射击

          this.setFireEnable(false);
          this.setMoveAble(false);
          this.colliderEnabled = false;
          this.isDead = false;
          this.curHp = this.maxHp;
          this.updateHpUI();
          this.hpRecoveryTime = 0;
          const targetY = -view.getVisibleSize().height / 2 * 0.3;
          this.node.setPosition(0, targetY);

          this._targetPosition.set(0, targetY, 0);

          this._lastPosition.set(0, targetY, 0);
        }

        updateHpUI() {
          (_crd && GameFightUI === void 0 ? (_reportPossibleCrUseOfGameFightUI({
            error: Error()
          }), GameFightUI) : GameFightUI).instance.updatePlayerUI();
        }

        setEmitter() {
          //后期根据飞机的数据，加载不同的发送组件预制体
          let path = (_crd && GameResourceList === void 0 ? (_reportPossibleCrUseOfGameResourceList({
            error: Error()
          }), GameResourceList) : GameResourceList).EmitterPrefabPath + "Emitter_main_01";
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadAsync(path, Prefab).then(prefab => {
            var _this$NodeEmitter;

            let node = instantiate(prefab);
            (_this$NodeEmitter = this.NodeEmitter) == null || _this$NodeEmitter.addChild(node);
            node.setPosition(0, 0);
            this.emitterComp = node.getComponent(_crd && Emitter === void 0 ? (_reportPossibleCrUseOfEmitter({
              error: Error()
            }), Emitter) : Emitter);
            this.emitterComp.setEntity(this);
            this.emitterComp.setIsActive(this._fireEnable);
            this.emitterComp.emitterId = 1000001;
          });
        }
        /**
         * 主飞机入场动画
         */


        planeIn() {
          this.node.getComponent(UIOpacity).opacity = 0;
          this.scheduleOnce(() => {
            var _this$_plane2;

            this.node.getComponent(UIOpacity).opacity = 255;
            (_this$_plane2 = this._plane) == null || _this$_plane2.onEnter(() => {
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.onPlaneIn();
            });
          }, 0.7);
        }
        /**
         * 碰撞处理
         * @param {Object} collision 碰撞对象
         */


        onCollide(collision) {
          let damage = 0;

          if (collision.entity instanceof (_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
            error: Error()
          }), Bullet) : Bullet)) {
            if (this.attribute.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
              error: Error()
            }), AttributeConst) : AttributeConst).StatusImmuneBulletHurt) == 0) {
              damage = collision.entity.calcDamage(this);
            }

            if (damage > 0) {
              this.hurt(damage);
            }
          } else if (collision.entity instanceof (_crd && EnemyPlaneBase === void 0 ? (_reportPossibleCrUseOfEnemyPlaneBase({
            error: Error()
          }), EnemyPlaneBase) : EnemyPlaneBase)) {
            this.collisionPlane(collision.entity);
          }
        }
        /**
         * 控制飞机移动
         * @param {number} localX
         * @param {number} touchY
         */


        onControl(localX, touchY) {
          if (!this.isDead && this.m_moveEnable) {
            var _this$_plane3;

            // 将设计分辨率坐标转换为世界坐标
            const worldPos = this._convertTouchToWorld(localX, touchY);

            let isLeft = localX < this.node.position.x;
            (_this$_plane3 = this._plane) == null || _this$_plane3.onMoveCommand(isLeft); // 获取战斗边界

            const halfWidth = this._battleWidth / 2;
            const halfHeight = (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
              error: Error()
            }), GameConst) : GameConst).ViewHeight / 2; // 限制飞机在战斗区域内

            let posX = Math.min(halfWidth, Math.max(-halfWidth, localX));
            let posY = Math.min(halfHeight, Math.max(-halfHeight, touchY));

            this._targetPosition.set(posX, posY, 0); //this.node.setPosition(posX, posY);
            // 调试日志
            //logInfo('MainPlane', ` 接收位置: (${localX},${touchY}) 限制后: (${posX},${posY}) 边界: X:±${halfWidth}, Y:±${halfHeight} `);

          }
        }
        /**
         * 平滑移动到目标位置
         * @param dt 帧时间（秒）
         */


        smoothMoveToTarget(dt) {
          if (!this.m_moveEnable || this.isDead) return; // 获取当前位置

          this.node.getPosition(this._lastPosition); // 计算当前位置到目标位置的距离

          const dx = this._targetPosition.x - this._lastPosition.x;
          const dy = this._targetPosition.y - this._lastPosition.y;
          const distance = Math.sqrt(dx * dx + dy * dy); // 计算最大允许移动距离（分别应用X和Y方向的速度限制）

          const maxMoveX = this._maxMoveSpeedX * dt;
          const maxMoveY = this._maxMoveSpeedY * dt; // 应用移动速度限制（分别处理X和Y方向）

          let newX = this._lastPosition.x;
          let newY = this._lastPosition.y; // X方向移动限制

          if (Math.abs(dx) > maxMoveX) {
            newX += Math.sign(dx) * maxMoveX;
          } else {
            newX = this._targetPosition.x;
          } // Y方向移动限制


          if (Math.abs(dy) > maxMoveY) {
            newY += Math.sign(dy) * maxMoveY;
          } else {
            newY = this._targetPosition.y;
          } // 设置新位置


          this.node.setPosition(newX, newY);
        }

        begine(isContinue = false) {
          this.setFireEnable(true);
          this.setMoveAble(true);

          if (isContinue) {
            this.setUncollideByTime(2);
          } else {
            this.cancelUncollide();
          }
        }

        revive() {
          this.node.active = true;
          this.begine(true);
        } //实现父类的方法


        playHurtAnim() {
          if (this._hurtActTime > this._hurtActDuration) {
            this._hurtActTime = 0; // 显示红屏效果

            (_crd && EffectLayer === void 0 ? (_reportPossibleCrUseOfEffectLayer({
              error: Error()
            }), EffectLayer) : EffectLayer).instance.showRedScreen();
          }
        }

        toDie() {
          if (!super.toDie()) {
            return false;
          }

          this.node.active = false;
          this.resetPlane();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.setGameEnd(false);
          return true;
        }

        get collisionLevel() {
          var _config;

          return ((_config = this._planeData.config) == null ? void 0 : _config.collideLevel) || 0;
        }

        get collisionHurt() {
          var _config2;

          return ((_config2 = this._planeData.config) == null ? void 0 : _config2.collideDamage) || 0;
        }

        get attribute() {
          return this._planeData;
        }

        getAttack() {
          return this._planeData.getAttack();
        }

        setMoveAble(enable) {
          this.m_moveEnable = enable;
        }

        setFireEnable(enable) {
          this._fireEnable = enable;

          if (this.emitterComp) {
            this.emitterComp.setIsActive(enable);
          }
        } //设置无敌状态


        setUncollideByTime(time) {
          this._unColliderTime = time;
          this.colliderEnabled = false;
          this.InvincibleNode.active = true;
        } //取消无敌状态


        cancelUncollide() {
          this.colliderEnabled = true;
          this.InvincibleNode.active = false;
        }

        setAnimSpeed(speed) {
          if (this._plane) {
            this._plane.setAnimSpeed(speed);
          }
        }

        get pickDiamondNum() {
          return this.statData.pickDiamond;
        }

        get killEnemyNum() {
          return this.statData.killEnemy;
        }

        get usedNuclearNum() {
          return this.statData.usedNuclear;
        }

        get usedSuperNum() {
          return this.statData.usedSuper;
        }
        /**
         * 计算屏幕适配比例
         */


        _calculateScreenRatio() {
          const visibleSize = view.getVisibleSize();
          this._screenRatio = visibleSize.width / (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).designWidth; // 计算实际战斗宽度（考虑宽屏适配）

          this._battleWidth = (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).ViewBattleWidth * this._screenRatio; //logInfo('CameraMove', `屏幕比例: ${this._screenRatio}, 战斗宽度: ${this._battleWidth}`);
        }
        /**
         * 将触摸坐标转换为世界坐标
         * @param touchX 触摸点X坐标（0-750）
         * @param touchY 触摸点Y坐标（0-1334）
         * @returns 世界坐标
         */


        _convertTouchToWorld(touchX, touchY) {
          // 获取实际屏幕尺寸
          const visibleSize = view.getVisibleSize(); // 计算设计分辨率到实际屏幕的比例

          const scaleX = visibleSize.width / (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).designWidth;
          const scaleY = visibleSize.height / (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).designHeight; // 将设计坐标转换为屏幕坐标

          const screenX = touchX * scaleX;
          const screenY = touchY * scaleY; // 将屏幕坐标转换为世界坐标
          // 世界坐标原点在屏幕中心，X范围：[-battleWidth/2, battleWidth/2]

          const worldX = screenX - visibleSize.width / 2;
          const worldY = screenY - visibleSize.height / 2; // 添加调试日志
          //logInfo('MainPlane', `转换: 设计(${touchX},${touchY}) -> 屏幕(${screenX},${screenY}) -> 世界(${worldX},${worldY})`);

          return new Vec3(worldX, worldY, 0);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "planeParent", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "NodeEmitter", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "InvincibleNode", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=292e7980528cd20afe6c7cb284e621f693f957da.js.map
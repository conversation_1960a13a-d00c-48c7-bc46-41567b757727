import { _decorator, Enum, CCFloat, Component, JsonAsset, Node, Prefab, Slider, Vec3, ValueType, CCBoolean, CCString, Asset, resources, assetManager, AssetManager, Sprite, SpriteFrame, SpriteAtlas, math, instantiate, Vec2, CCInteger, AudioClip, BitMask } from 'cc';
const { ccclass, property, executeInEditMode } = _decorator;

import { LevelDataEvent } from 'db://assets/bundles/common/script/leveldata/leveldata';
import { LevelDataEventTrigger, LevelDataEventTriggerType } from 'db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTrigger';
import { LevelDataEventTriggerLog } from 'db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerLog';
import { LevelDataEventTriggerAudio } from 'db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerAudio';
import { LevelDataEventTriggerWave, LevelDataEventWaveGroup } from 'db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerWave';
import { LevelDataEventTriggerSpecialEvent, eLevelSpecialEvent } from 'db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerSpecialEvent';
import { newTrigger } from 'db://assets/bundles/common/script/leveldata/trigger/newTrigger';
import { LevelDataEventCondtionType } from 'db://assets/bundles/common/script/leveldata/condition/LevelDataEventCondtion';
import { LevelDataEventCondtionWave } from 'db://assets/bundles/common/script/leveldata/condition/LevelDataEventCondtionWave';
import { Wave } from 'db://assets/bundles/common/script/game/wave/Wave';
import { WavePreview } from './preview/WavePreview';

import { LevelEditorElemUI } from './LevelEditorElemUI';
import { LevelEditorWaveGroup } from './LevelEditorWaveParam';
import { LevelEditorCondition } from './LevelEditorCondition';
import GameMapRun from 'db://assets/bundles/common/script/game/ui/map/GameMapRun';

@ccclass('LevelEditorEventTrigger')
export class LevelEditorEventTrigger{
    public _index = 0;
    public data : LevelDataEventTrigger = new LevelDataEventTriggerLog();

    @property({
        type:Enum(LevelDataEventTriggerType),
    })
    public get type(): LevelDataEventTriggerType{
        return this.data._type;
    }
    public set type(value: LevelDataEventTriggerType) {
        if (this.data._type != value) {
            this.data = newTrigger({_type: value});
        }
    }

    @property({
        type :CCString,
        visible () {
            return this.type == LevelDataEventTriggerType.Log ;
        }
    })
    public get message(): string {
        return (this.data as LevelDataEventTriggerLog).message;
    }
    public set message(value: string) {
        (this.data as LevelDataEventTriggerLog).message = value;
    }

    public _audio: AudioClip|null = null;
    @property({
        type :AudioClip,
        visible () {
            return this.type == LevelDataEventTriggerType.Audio;
        }
    })
    public get audio(): AudioClip|null {
        return this._audio;
    }
    public set audio(value: AudioClip|null) {
        this._audio = value;
        if (value) {
            (this.data as LevelDataEventTriggerAudio).audioUUID = value.uuid;
        } else {
            (this.data as LevelDataEventTriggerAudio).audioUUID = "";
        }
    }

    public _waveGroup: LevelEditorWaveGroup[] = []; 
    @property({
        type: [LevelEditorWaveGroup],
        displayName: "波次组随机",
        visible () {
            return this.type == LevelDataEventTriggerType.Wave;
        }
    })
    public get waveGroup(): LevelEditorWaveGroup[] {
        return this._waveGroup;
    }
    public set waveGroup(value: LevelEditorWaveGroup[]) {
        this._waveGroup = value;
        if (value) {
            (this.data as LevelDataEventTriggerWave).waveGroup = [];
            value.forEach((waveGroup) => {
                let levelDataWaveGroup = new LevelDataEventWaveGroup();
                if (waveGroup.wavePrefab && waveGroup.wavePrefab.length > 0) {
                    waveGroup.wavePrefab.forEach((prefab) => {
                        levelDataWaveGroup.waveUUID.push(prefab ? prefab.uuid : "");
                    });
                }
                levelDataWaveGroup.weight = waveGroup.weight;
                (this.data as LevelDataEventTriggerWave).waveGroup.push(levelDataWaveGroup);
            });
        } else {
            (this.data as LevelDataEventTriggerWave).waveGroup = [];
        }
    }

    @property({
        type: Enum(eLevelSpecialEvent),
        visible () {
            return this.type == LevelDataEventTriggerType.SpecialEvent;
        }
    })
    public get eventType(): eLevelSpecialEvent {
        return (this.data as LevelDataEventTriggerSpecialEvent).eventType;
    }
    public set eventType(value: eLevelSpecialEvent) {
        (this.data as LevelDataEventTriggerSpecialEvent).eventType = value;
    }
}

@ccclass('LevelEditorEventUI')
@executeInEditMode()
export class LevelEditorEventUI extends LevelEditorElemUI {
    @property([LevelEditorCondition])
    public conditions: LevelEditorCondition[] = [];
    @property([LevelEditorEventTrigger])
    public triggers: LevelEditorEventTrigger[] = [];

    public update(dt: number): void {
        for (let i = 0; i < this.conditions.length; i++) {
            const cond = this.conditions[i];
            cond._index = i;
            if (cond.type == LevelDataEventCondtionType.Wave 
                && (cond.data as LevelDataEventCondtionWave).targetElemID != "" 
                && cond._targetElem == null) {
                const elems = this.node.scene.getComponentsInChildren(LevelEditorElemUI);
                for (let elem of elems) {
                    if (elem.elemID == (cond.data as LevelDataEventCondtionWave).targetElemID) {
                        cond._targetElem = elem;
                        break;
                    }
                }
            }
        }
    }

    public initByLevelData(data: LevelDataEvent) {
        super.initByLevelData(data)
        if (data.conditions) {
            for (let i = 0; i < data.conditions.length; i++) {
                const condition = new LevelEditorCondition();
                condition._index = i;
                condition.data = data.conditions[i];
                this.conditions.push(condition);
            }
        }
        if (data.triggers) {
            // clear wave childrens 
            this.node.removeAllChildren();

            for (let i = 0; i < data.triggers.length; i++) {
                const trigger = new LevelEditorEventTrigger();
                trigger._index = i;
                trigger.data = data.triggers[i];
                if (trigger.data._type == LevelDataEventTriggerType.Audio) {
                    let uuid = (trigger.data as LevelDataEventTriggerAudio).audioUUID;
                    if (uuid != "") {
                        assetManager.loadAny({uuid:uuid}, (err, audio:AudioClip) => {
                            if (err) {
                                console.error("LevelEditorEventUI initByLevelData load audio err", err);
                                return;
                            }
                            trigger._audio = audio;
                        });
                    }
                }
                if (trigger.data._type == LevelDataEventTriggerType.Wave) {
                    let waveTrigger = trigger.data as LevelDataEventTriggerWave;
                    waveTrigger.waveGroup.forEach((waveGroup) => {
                        let editorWaveGroup = new LevelEditorWaveGroup();
                        waveGroup.waveUUID.forEach((uuid) => {
                            assetManager.loadAny({uuid:uuid}, (err, prefab:Prefab) => {
                                if (err) {
                                    console.error("LevelEditorEventUI initByLevelData load wave prefab err", err);
                                    return;
                                }
                                editorWaveGroup.wavePrefab.push(prefab);

                                // create wave
                                const waveNode = instantiate(prefab);
                                this.node.addChild(waveNode);
                                this.setupWave(waveNode.getComponent(Wave));
                            });
                        });
                        editorWaveGroup.weight = waveGroup.weight;
                        trigger.waveGroup.push(editorWaveGroup);
                    });
                }
                this.triggers.push(trigger);
            }
        }
    }

    public fillLevelData(data: LevelDataEvent) {
        super.fillLevelData(data)
        data.conditions = []
        this.conditions.forEach((cond) => {
            if (cond != null) {
                data.conditions.push(cond.data);
            }
        })
        this.triggers.forEach((trigger) => {
            if (trigger != null) {
                data.triggers.push(trigger.data);
            }
        })
    }

    private setupWave(wave: Wave|null): void {
        if (wave == null) 
            return;

        wave.setupInEditor();
    }

    private _isActive = false;
    public play(bPlay: boolean, progress: number) {
        if (bPlay) {
            const posY = this.node.position.y - GameMapRun.VIEWPORT_TOP;
            // console.log('play event', this.node.position.y, progress, GameMapRun.VIEWPORT_TOP);
            if (posY <= progress) {
                if (!this._isActive) {
                    this.triggers.forEach((trigger) => {
                        this.performTrigger(trigger.data);
                    })
                    this._isActive = true;
                }
            }
        }
        else {
            if (this._isActive) {
                this.resetPlay();
                this._isActive = false;
            }
        }
    }

    private resetPlay() {
        // WavePreview.instance?.clearPreview();
    }

    private performTrigger(trigger: LevelDataEventTrigger) {
        switch (trigger._type) {
            case LevelDataEventTriggerType.Log:
                console.log("LevelEditorEventUI", "trigger log", (trigger as LevelDataEventTriggerLog).message);
                break;
            case LevelDataEventTriggerType.Audio:
                break;
            case LevelDataEventTriggerType.Wave:
                // Do Wave logic
                this.triggerWave(trigger as LevelDataEventTriggerWave);
                break;
            default:
                break;
        }
    }

    private triggerWave(triggerWave: LevelDataEventTriggerWave) {
        // console.log('Trigger wave!! , ', triggerWave.waveGroup);
        // 这里直接把所有wave都搞出来
        triggerWave.waveGroup.forEach((waveGroup) => {
            waveGroup.waveUUID.forEach((uuid) => {
                assetManager.loadAny({uuid:uuid}, (err, prefab:Prefab) => {
                    if (err) {
                        console.error("LevelEditorEventUI initByLevelData load wave prefab err", err);
                        return;
                    }

                    // create wave
                    const waveNode = instantiate(prefab);
                    const position = this.node.position;
                    // WavePreview.instance?.addPreview(waveNode.getComponent(Wave)!, position.x, position.y);
                });
            });
        });
    }
}
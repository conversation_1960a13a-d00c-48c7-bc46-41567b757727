"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.onCreateMenu = onCreateMenu;
exports.onHierarchyCreateMenu = onHierarchyCreateMenu;
exports.onHierarchyNodeMenu = onHierarchyNodeMenu;
exports.onAssetMenu = onAssetMenu;
exports.getAssetUuidByPath = getAssetUuidByPath;
exports.getAssetUuidsByPath = getAssetUuidsByPath;
function onCreateMenu(assetInfo) {
    return [
        {
            label: '飞机游戏',
            submenu: [
                {
                    label: '创建波次',
                    click() {
                        createWavePrefab(assetInfo);
                    },
                }
            ],
        },
    ];
}
;
function onHierarchyCreateMenu() {
    return [
        {
            label: '关卡',
            submenu: [
                {
                    label: '创建event',
                    click() {
                        createEventNode();
                    },
                },
                {
                    label: '添加结束标记',
                    click() {
                        // @ts-ignore
                        Editor.Message.request('scene', 'execute-scene-script', {
                            name: 'level-editor',
                            method: 'addEventEndTag',
                            args: []
                        });
                    },
                }
            ],
        },
    ];
}
function onHierarchyNodeMenu() {
    return [
        {
            label: '路径编辑器',
            submenu: [
                {
                    label: '左右翻转',
                    click() {
                        // @ts-ignore
                        Editor.Message.request('scene', 'execute-scene-script', {
                            name: 'level-editor',
                            method: 'flipPathHorizontal',
                            args: []
                        });
                    },
                },
                {
                    label: '上下翻转',
                    click() {
                        // @ts-ignore
                        Editor.Message.request('scene', 'execute-scene-script', {
                            name: 'level-editor',
                            method: 'flipPathVertical',
                            args: []
                        });
                    },
                },
                {
                    label: '圆形拟合',
                    click() {
                        // @ts-ignore
                        Editor.Message.request('scene', 'execute-scene-script', {
                            name: 'level-editor',
                            method: 'fitPathToCircle',
                            args: []
                        });
                    },
                },
            ],
        },
        {
            label: '关卡编辑器',
            submenu: [
                {
                    label: '复制选中的event(s)',
                    click() {
                        copyEventNode();
                    },
                },
                {
                    label: '排列选中的event(s)',
                    click() {
                        // @ts-ignore
                        Editor.Message.request('scene', 'execute-scene-script', {
                            name: 'level-editor',
                            method: 'sortEventNode',
                            args: []
                        });
                    },
                },
            ],
        }
    ];
}
function onAssetMenu(assetInfo) {
    const submenu = [
        {
            label: '创建关卡Prefab',
            //enabled: assetInfo.isDirectory,
            click() {
                createLevelPrefab(assetInfo);
            },
        },
        {
            label: '创建子弹prefab',
            //enabled: !assetInfo.isDirectory,
            click() {
                createBulletPrefabs(assetInfo);
            },
        },
    ];
    // 检查是否是阵型JSON文件
    if (!assetInfo.isDirectory &&
        assetInfo.url.includes('assets/resources/game/level/wave/formation') &&
        assetInfo.url.endsWith('.json')) {
        submenu.push({
            label: '编辑阵型',
            click() {
                editFormation(assetInfo);
            },
        });
    }
    // 检查是否是路径JSON文件
    if (!assetInfo.isDirectory &&
        assetInfo.url.includes('assets/resources/game/level/wave/path') &&
        assetInfo.url.endsWith('.json')) {
        submenu.push({
            label: '编辑路径',
            click() {
                editPath(assetInfo);
            },
        });
    }
    if (!assetInfo.isDirectory &&
        assetInfo.url.includes('assets/resources/game/spine/plane/mainplane/') &&
        assetInfo.url.endsWith('.json')) {
        submenu.push({
            label: '创建自机prefab',
            click() {
                createMainPlanePrefab(assetInfo);
            },
        });
    }
    if (!assetInfo.isDirectory &&
        assetInfo.url.includes('assets/resources/game/spine/plane/enemyplane/') &&
        assetInfo.url.endsWith('.json')) {
        submenu.push({
            label: '创建敌机prefab',
            click() {
                createEnemyPlanePrefab(assetInfo);
            },
        });
    }
    return [
        {
            label: '飞机游戏',
            submenu: submenu,
        },
    ];
}
;
function getAssetUuidByPath(path) {
    return new Promise((resolve, reject) => {
        // @ts-ignore
        Editor.Message.request('asset-db', 'query-uuid', path).then((res) => {
            resolve(res);
        }).catch((err) => {
            console.error('Failed to query uuid:', err);
            reject(err);
        });
    });
}
function getAssetUuidsByPath(path) {
    return new Promise((resolve, reject) => {
        // @ts-ignore
        Editor.Message.request('asset-db', 'query-assets', { pattern: path + '/**' }).then((res) => {
            const arr = Array.isArray(res) ? res : (Array.isArray(res === null || res === void 0 ? void 0 : res[0]) ? res[0] : []);
            const assets = arr
                .filter((a) => a && !a.isDirectory)
                .map((a) => ({
                name: String(a.name || ''),
                path: a.path || '',
                uuid: a.uuid || ''
            }))
                .filter(p => p.name)
                .sort((a, b) => a.name.localeCompare(b.name));
            resolve(assets.map(a => a.uuid)); // 只需要uuid即可，不需要其他信息了。
        }).catch((err) => {
            console.error('Failed to query assets:', err);
            reject(err);
        });
    });
}
function createWavePrefab(assetInfo) {
    // @ts-ignore
    Editor.Message.request('scene', 'execute-scene-script', {
        name: 'level-editor',
        method: 'createWavePrefab',
        args: []
    });
}
function createLevelPrefab(assetInfo) {
    console.log(assetInfo);
    if (assetInfo.isDirectory) {
        getAssetUuidsByPath(assetInfo.url).then((uuids) => {
            uuids.forEach((uuid) => {
                // @ts-ignore
                Editor.Message.request('scene', 'execute-scene-script', {
                    name: 'level-editor',
                    method: 'createLevelPrefab',
                    args: [uuid]
                });
            });
        });
    }
    else {
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'level-editor',
            method: 'createLevelPrefab',
            args: [assetInfo.uuid]
        });
    }
}
function createBulletPrefabs(assetInfo) {
    if (assetInfo.isDirectory) {
        getAssetUuidsByPath(assetInfo.url).then((uuids) => {
            uuids.forEach((uuid) => {
                // @ts-ignore
                Editor.Message.request('scene', 'execute-scene-script', {
                    name: 'level-editor',
                    method: 'createBulletPrefab',
                    args: [uuid]
                });
            });
        });
    }
    else {
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'level-editor',
            method: 'createBulletPrefab',
            args: [assetInfo.uuid]
        });
    }
}
function createMainPlanePrefab(assetInfo) {
    console.log("create mainplane from spine assetInfo", assetInfo);
    // @ts-ignore
    Editor.Message.request('scene', 'execute-scene-script', {
        name: 'level-editor',
        method: 'createMainPlanePrefab',
        args: [assetInfo.uuid]
    });
}
function createEnemyPlanePrefab(assetInfo) {
    console.log("create enemyplane from spine assetInfo", assetInfo);
    // @ts-ignore
    Editor.Message.request('scene', 'execute-scene-script', {
        name: 'level-editor',
        method: 'createEnemyPlanePrefab',
        args: [assetInfo.uuid]
    });
}
function editFormation(assetInfo) {
    console.log('编辑阵型:', assetInfo);
    // 打开FormationEditor场景 Uuid = aa842f3a-8aea-42c2-a480-968aade3dfef
    getAssetUuidByPath('db://assets/editor/FormationEditor.scene').then((uuid) => {
        // @ts-ignore
        Editor.Message.request('scene', 'open-scene', uuid).then(() => {
            // 场景打开后，加载选中的阵型JSON文件到FormationEditor组件
            // @ts-ignore
            Editor.Message.request('scene', 'execute-scene-script', {
                name: 'level-editor',
                method: 'loadFormationData',
                args: [assetInfo.uuid]
            });
        }).catch((err) => {
            console.error('Failed to open FormationEditor scene:', err);
        });
    });
}
function editPath(assetInfo) {
    console.log('编辑路径:', assetInfo);
    // 打开PathEditor场景
    getAssetUuidByPath('db://assets/editor/PathEditor.scene').then((uuid) => {
        // @ts-ignore
        Editor.Message.request('scene', 'open-scene', uuid).then(() => {
            // 场景打开后，加载选中的路径JSON文件到PathEditor组件
            // @ts-ignore
            Editor.Message.request('scene', 'execute-scene-script', {
                name: 'level-editor',
                method: 'loadPathData',
                args: [assetInfo.uuid]
            });
        }).catch((err) => {
            console.error('Failed to open PathEditor scene:', err);
        });
    });
}
// 关卡编辑器-关卡节点
function createEventNode() {
    // @ts-ignore
    Editor.Message.request('scene', 'execute-scene-script', {
        name: 'level-editor',
        method: 'createEventNode',
        args: []
    });
}
function copyEventNode() {
    // @ts-ignore
    Editor.Message.request('scene', 'execute-scene-script', {
        name: 'level-editor',
        method: 'copyEventNode',
        args: []
    });
}
function matchEventNodeByWave() {
    // @ts-ignore
    Editor.Message.request('scene', 'execute-scene-script', {
        name: 'level-editor',
        method: 'matchEventNodeByWave',
        args: []
    });
}
//# sourceMappingURL=data:application/json;base64,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
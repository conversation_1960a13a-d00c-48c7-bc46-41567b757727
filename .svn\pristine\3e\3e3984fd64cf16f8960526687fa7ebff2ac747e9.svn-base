import { _decorator, Label, Node, Slider } from 'cc';
import { BundleName } from 'db://assets/bundles/common/script/const/BundleConst';
import { BaseUI, UILayer, UIMgr, UIOpt } from 'db://assets/scripts/core/base/UIMgr';
import { ButtonPlus } from '../common/components/button/ButtonPlus';
const { ccclass, property } = _decorator;

@ccclass('PKBuyUI')
export class PKBuyUI extends BaseUI {

    @property(Slider)
    slider: Slider | null = null;
    @property(ButtonPlus)
    btnReduce: ButtonPlus | null = null;
    @property(ButtonPlus)
    btnAdd: ButtonPlus | null = null;
    @property(ButtonPlus)
    btnMax: ButtonPlus | null = null;
    @property(ButtonPlus)

    btnBuy: ButtonPlus | null = null;
    @property(Label)
    lblPrice: Label | null = null;

    @property(Node)
    itemQuaIcon: Node | null = null;
    @property(Label)
    itemName: Label | null = null;

    @property(Label)
    buyTimes: Label | null = null;

    maxVal: number = 10;
    curVal: number = 1;

    public static getUrl(): string { return "prefab/ui/PKBuyUI"; }
    public static getLayer(): UILayer { return UILayer.Default }
    public static getBundleName(): string { return BundleName.HomePK; }
    public static getUIOption(): UIOpt { return { isClickBgCloseUI: true } }
    protected onLoad(): void {
        this.btnReduce!.addClick(this.onReduceClick, this);
        this.btnAdd!.addClick(this.onAddClick, this);
        this.btnMax!.addClick(this.onMaxClick, this);
        this.btnBuy!.addClick(this.onBuyClick, this);

        this.slider!.node.on('slide', this.onSliderValueChanged, this);
    }
    onReduceClick() {
        this.curVal--;
        this.curVal = Math.max(1, this.curVal);
        this.itemName!.string = "购买：" + this.curVal.toString();
        this.updateSliderProgress();
    }
    onAddClick() {
        this.curVal++;
        this.curVal = Math.min(this.maxVal, this.curVal);
        this.itemName!.string = "购买：" + this.curVal.toString();
        this.updateSliderProgress();
    }
    onMaxClick() {
        this.curVal = this.maxVal;
        this.itemName!.string = "购买：" + this.curVal.toString();
        this.updateSliderProgress();
    }

    onSliderValueChanged(slider: Slider) {
        if (this.maxVal <= 1) {
            this.curVal = 1;
            this.itemName!.string = "购买：" + this.curVal.toString();
            return;
        }
        let val = Math.round(slider.progress * (this.maxVal - 1)) + 1;
        val = Math.max(1, Math.min(this.maxVal, val));
        this.curVal = val;
        this.itemName!.string = "购买：" + this.curVal.toString();
    }
    private updateSliderProgress() {
        if (this.slider) {
            if (this.maxVal <= 1) {
                this.slider.progress = 1;
                return;
            }
            this.slider.progress = (this.curVal - 1) / (this.maxVal - 1);
        }
    }
    onBuyClick() {

    }
    async closeUI() {
        UIMgr.closeUI(PKBuyUI);
    }
    async onShow(): Promise<void> {

    }
    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {
    }
    protected onDestroy(): void {

    }
}



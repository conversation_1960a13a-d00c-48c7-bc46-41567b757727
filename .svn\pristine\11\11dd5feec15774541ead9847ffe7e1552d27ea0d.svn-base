import { _decorator, Component, Prefab, Node, instantiate, UITransform } from 'cc';
import { LevelDataBackgroundLayer } from 'db://assets/bundles/common/script/leveldata/leveldata';
import { LevelUtils } from './LevelUtils';
import { MyApp } from 'db://assets/bundles/common/script/app/MyApp';
import { GameConst } from 'db://assets/scripts/core/base/GameConst';
import { GameIns } from '../../GameIns';
import { LevelNodeCheckOutScreen } from './LevelNodeCheckOutScreen';
import { LevelLayer } from './LevelLayer';

const { ccclass, property } = _decorator;

const BackgroundsNodeName = "backgrounds";
const BACKGROUND_POOL_NAME = "background_pool";

@ccclass('LevelBackgroundLayerUI')
export class LevelBackgroundLayerUI extends LevelLayer {
    private _backgrounds: Prefab[] = [];
    private _offSetY: number = 0; // 当前关卡的偏移量

    private _backgroundsNode: Node | null = null;

    protected onLoad(): void {
        
    }

    public async initByLevelData(data: LevelDataBackgroundLayer, offSetY: number, bFirstLoad: boolean): Promise<void> {
        this._offSetY = offSetY;
        this.node.setPosition(0, offSetY, 0);

        this._backgroundsNode = LevelUtils.getOrAddNode(this.node, BackgroundsNodeName);
        this._backgrounds = [];

        await this._initBackgrounds(data, bFirstLoad);
    }

    private async _initBackgrounds(data: LevelDataBackgroundLayer, bFirstLoad: boolean): Promise<void> {
        for (let i = 0; i < data.backgrounds.length; i++) {
            const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, data.backgrounds[i]); 
            const prefab = await MyApp.resMgr.loadAsync(path, Prefab);
            this._backgrounds.push(prefab);
        }

        if (bFirstLoad) {
            let offSetY = 0;
            let halfHeight = 0;
        
            while (this._backgrounds.length > 0 && (offSetY - halfHeight) < GameConst.VIEWPORT_LOAD_POS) {
                const node = this._addBackground(offSetY);
                const nodeHeight = node.getComponent(UITransform)!.height;
                offSetY += nodeHeight;
                halfHeight = nodeHeight / 2;
            }
        }
    }

    public tick(deltaTime: number): void {
        let posY = this.node.position.y;
        posY -= deltaTime * this.speed;
        this.node.setPosition(0, posY, 0);

        this._checkBackgrounds();
    }

    private _checkBackgrounds(): void {
        if (!this._backgroundsNode || this._backgrounds.length === 0) {
            return;
        }
        
        // 获取最后一个背景节点
        const lastChild = this._backgroundsNode.children[this._backgroundsNode.children.length - 1];
        const lastChildTransform = lastChild.getComponent(UITransform);
        
        if (!lastChildTransform) {
            return;
        }
        
        // 计算最后一个背景节点的顶部位置（世界坐标） 
        const lastBackgroundHeight = lastChildTransform.height;
        const lastChildTop = this.node.position.y + lastChild.position.y + lastBackgroundHeight / 2;
        
        // 如果最后一个背景的顶部位置小于加载阈值，添加新背景
        if (lastChildTop < GameConst.VIEWPORT_LOAD_POS) {
            const newY = lastChild.position.y + lastBackgroundHeight;
            const newNode = this._addBackground(newY);
        }
    }
    
    private _addBackground(yPos: number): Node {
        const index = this._backgroundsNode!.children.length % this._backgrounds.length;
        const prefab = this._backgrounds[index];
        const prefabName = prefab.name;
        let node = GameIns.gameMapManager.mapObjectPoolManager.get(BACKGROUND_POOL_NAME, prefabName);
        if (!node) {
            node = instantiate(this._backgrounds[index]);
            const checkOut = node.addComponent(LevelNodeCheckOutScreen);
            checkOut.init(BACKGROUND_POOL_NAME);
        }

        this._backgroundsNode!.addChild(node);
        node.setPosition(0, yPos, 0);
        return node;
    }
}



{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/FriendRankCellUI.ts"], "names": ["_decorator", "Component", "Label", "Sprite", "AvatarIcon", "ccclass", "property", "FriendRankCellUI", "setData", "idx", "lblRank", "string", "toString", "lblName", "lblScore", "spPlane", "spriteFrame", "start", "update", "deltaTime", "onDestroy"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;;AAC9BC,MAAAA,U,iBAAAA,U;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBN,U;;kCAGjBO,gB,WADZF,OAAO,CAAC,kBAAD,C,UAGHC,QAAQ,CAACJ,KAAD,C,UAERI,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ,CAACJ,KAAD,C,UAERI,QAAQ,CAACJ,KAAD,C,UAERI,QAAQ,CAACH,MAAD,C,2BAXb,MACaI,gBADb,SACsCN,SADtC,CACgD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAa5CO,QAAAA,OAAO,CAACC,GAAD,EAAa;AAChB,eAAKC,OAAL,CAAcC,MAAd,GAAuBF,GAAG,CAACG,QAAJ,EAAvB;AACA,eAAKC,OAAL,CAAcF,MAAd,GAAuB,MAAvB;AACA,eAAKG,QAAL,CAAeH,MAAf,GAAwB,KAAxB;AACA,eAAKI,OAAL,CAAcC,WAAd,GAA4B,IAA5B;AACH;;AAEDC,QAAAA,KAAK,GAAG,CAEP;;AAEDC,QAAAA,MAAM,CAACC,SAAD,EAAoB,CAEzB;;AAESC,QAAAA,SAAS,GAAS,CAE3B;;AA9B2C,O;;;;;iBAGpB,I;;;;;;;iBAEQ,I;;;;;;;iBAER,I;;;;;;;iBAEC,I;;;;;;;iBAEA,I", "sourcesContent": ["import { _decorator, Component, Label, Sprite } from 'cc';\r\nimport { AvatarIcon } from '../common/components/base/AvatarIcon';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('FriendRankCellUI')\r\nexport class FriendRankCellUI extends Component {\r\n\r\n    @property(Label)\r\n    lblRank: Label | null = null;\r\n    @property(AvatarIcon)\r\n    avatarIcon: AvatarIcon | null = null;\r\n    @property(Label)\r\n    lblName: Label | null = null;\r\n    @property(Label)\r\n    lblScore: Label | null = null;\r\n    @property(Sprite)\r\n    spPlane: Sprite | null = null;\r\n\r\n    setData(idx: number){\r\n        this.lblRank!.string = idx.toString();\r\n        this.lblName!.string = \"name\";\r\n        this.lblScore!.string = \"分数：\";\r\n        this.spPlane!.spriteFrame = null;\r\n    }\r\n\r\n    start() {\r\n\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n\r\n    }\r\n\r\n    protected onDestroy(): void {\r\n\r\n    }\r\n\r\n}\r\n\r\n\r\n"]}
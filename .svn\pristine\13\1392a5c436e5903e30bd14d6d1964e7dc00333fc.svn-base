import { Emitter } from "./Emitter";
import { Bullet } from "./Bullet";
import { eEmitterCondition, eBulletCondition } from "../data/bullet/EventConditionType";
import { eBulletAction, eEmitterAction } from "../data/bullet/EventActionType";
import { IEventCondition, Condition<PERSON>hain } from "db://assets/bundles/common/script/game/eventgroup/IEventCondition";
import { IEventAction } from "db://assets/bundles/common/script/game/eventgroup/IEventAction";
import { EventGroupBase } from "db://assets/bundles/common/script/game/eventgroup/IEventGroup";
import { IEventGroupContext } from "db://assets/bundles/common/script/game/eventgroup/IEventGroupContext";
import { EventGroupData, EventActionData, EventConditionData } from "../data/bullet/EventGroupData";
import * as emitter_cond from "./conditions/EmitterEventConditions";
import * as bullet_cond from "./conditions/BulletEventConditions";
import * as emitter_act from "./actions/EmitterEventActions";
import * as bullet_act from "./actions/BulletEventActions";
import { BulletSystem } from "./BulletSystem";
import PlaneBase from "db://assets/bundles/common/script/game/ui/plane/PlaneBase";

// context for running condition & action
export class EventGroupContext implements IEventGroupContext {
    emitter: Emitter | null = null;
    bullet: Bullet | null = null;
    playerPlane: PlaneBase | null = null;

    reset(): void {
        this.emitter = null;
        this.bullet = null;
        this.playerPlane = null;
    }
}

// Updated EventGroup
export enum eEventGroupStatus {
    Idle,       // not active
    Waiting,    // waiting for conditions to be met
    Active,     // conditions are met, now ticking actions
    Stopped     // stopped
}

export class EventGroup extends EventGroupBase<EventGroupContext, EventGroupData> {
    tryStart(): boolean {
        if (super.tryStart()) {
            BulletSystem.onCreateEventGroup(this);
            return true;
        }

        return false;
    }

    tryStop(): boolean {
        if (super.tryStop()) {
            BulletSystem.onDestroyEventGroup(this);
            return true;
        }

        return false;
    }

    protected buildConditions(): ConditionChain<EventGroupContext> {
        const chain = new ConditionChain<EventGroupContext>();
        this.data.conditions.forEach((condData, index) => {
            const condition = ConditionFactory.create(condData);
            if (condition) {
                condition.onLoad(this.context);
                chain.conditions.push(condition);
            }
        });
        return chain;
    }

    protected buildActions(): IEventAction<EventGroupContext>[] {
        return this.data.actions.map(actionData => {
            let action = ActionFactory.create(actionData);
            return action;
        });
    }
}

// Factory pattern for conditions & actions
class ConditionFactory {
    static create(data: EventConditionData): IEventCondition<EventGroupContext> {
        switch (data.type) {
            case eEmitterCondition.Emitter_Active:
                return new emitter_cond.EmitterCondition_Active(data);
            case eEmitterCondition.Emitter_InitialDelay:
                return new emitter_cond.EmitterCondition_InitialDelay(data);
            case eEmitterCondition.Emitter_Prewarm:
                return new emitter_cond.EmitterCondition_Prewarm(data);
            case eEmitterCondition.Emitter_PrewarmDuration:
                return new emitter_cond.EmitterCondition_PrewarmDuration(data);
            case eEmitterCondition.Emitter_Duration:
                return new emitter_cond.EmitterCondition_Duration(data);
            case eEmitterCondition.Emitter_ElapsedTime:
                return new emitter_cond.EmitterCondition_ElapsedTime(data);
            case eEmitterCondition.Emitter_Loop:
                return new emitter_cond.EmitterCondition_Loop(data);
            case eEmitterCondition.Emitter_LoopInterval:
                return new emitter_cond.EmitterCondition_LoopInterval(data);
            case eEmitterCondition.Emitter_EmitInterval:
                return new emitter_cond.EmitterCondition_EmitInterval(data);
            case eEmitterCondition.Emitter_PerEmitCount:
                return new emitter_cond.EmitterCondition_PerEmitCount(data);
            case eEmitterCondition.Emitter_PerEmitInterval:
                return new emitter_cond.EmitterCondition_PerEmitInterval(data);
            case eEmitterCondition.Emitter_PerEmitOffsetX:
                return new emitter_cond.EmitterCondition_PerEmitOffsetX(data);
            case eEmitterCondition.Emitter_Angle:
                return new emitter_cond.EmitterCondition_Angle(data);
            case eEmitterCondition.Emitter_Count:
                return new emitter_cond.EmitterCondition_Count(data);
            case eEmitterCondition.Bullet_Duration:
                return new emitter_cond.EmitterCondition_BulletDuration(data);
            case eEmitterCondition.Bullet_Speed:
                return new emitter_cond.EmitterCondition_BulletSpeed(data);
            case eEmitterCondition.Bullet_Acceleration:
                return new emitter_cond.EmitterCondition_BulletAcceleration(data);
            case eEmitterCondition.Bullet_AccelerationAngle:
                return new emitter_cond.EmitterCondition_BulletAccelerationAngle(data);
            case eEmitterCondition.Bullet_Orientation:
                return new emitter_cond.EmitterCondition_BulletOrientation(data);
            case eEmitterCondition.Bullet_OrientationParam:
                return new emitter_cond.EmitterCondition_BulletOrientationParam(data);
            case eEmitterCondition.Bullet_TrackingTarget:
                return new emitter_cond.EmitterCondition_BulletTrackingTarget(data);
            case eEmitterCondition.Bullet_Destructive:
                return new emitter_cond.EmitterCondition_BulletDestructive(data);
            case eEmitterCondition.Bullet_DestructiveOnHit:
                return new emitter_cond.EmitterCondition_BulletDestructiveOnHit(data);
            // case eEmitterCondition.Bullet_Sprite:
            //     return new emitter_cond.EmitterCondition_BulletSprite(data);
            case eEmitterCondition.Bullet_Scale:
                return new emitter_cond.EmitterCondition_BulletScale(data);
            case eEmitterCondition.Bullet_ColorR:
                return new emitter_cond.EmitterCondition_BulletColorR(data);
            case eEmitterCondition.Bullet_ColorG:
                return new emitter_cond.EmitterCondition_BulletColorG(data);
            case eEmitterCondition.Bullet_ColorB:
                return new emitter_cond.EmitterCondition_BulletColorB(data);
            case eEmitterCondition.Bullet_DefaultFacing:
                return new emitter_cond.EmitterCondition_BulletDefaultFacing(data);
            case eEmitterCondition.Player_ActLevel:
                return new emitter_cond.EmitterCondition_PlayerActLevel(data);  
            case eEmitterCondition.Player_PosX:
                return new emitter_cond.EmitterCondition_PlayerPosX(data);  
            case eEmitterCondition.Player_PosY:
                return new emitter_cond.EmitterCondition_PlayerPosY(data);
            case eEmitterCondition.Player_LifePercent:
                return new emitter_cond.EmitterCondition_PlayerLifePercent(data);
            case eEmitterCondition.Player_GainBuff:
                return new emitter_cond.EmitterCondition_PlayerGainBuff(data);
            
            // ... bullet cases
            case eBulletCondition.Bullet_Duration:
                return new bullet_cond.BulletCondition_Duration(data);
            case eBulletCondition.Bullet_ElapsedTime:
                return new bullet_cond.BulletCondition_ElapsedTime(data);
            case eBulletCondition.Bullet_PosX:
                return new bullet_cond.BulletCondition_PosX(data);
            case eBulletCondition.Bullet_PosY:
                return new bullet_cond.BulletCondition_PosY(data);
            case eBulletCondition.Bullet_Speed:
                return new bullet_cond.BulletCondition_Speed(data);
            case eBulletCondition.Bullet_SpeedAngle:
                return new bullet_cond.BulletCondition_SpeedAngle(data);
            case eBulletCondition.Bullet_Acceleration:
                return new bullet_cond.BulletCondition_Acceleration(data);
            case eBulletCondition.Bullet_AccelerationAngle:
                return new bullet_cond.BulletCondition_AccelerationAngle(data);
            case eBulletCondition.Bullet_Scale:
                return new bullet_cond.BulletCondition_Scale(data);
            case eBulletCondition.Bullet_ColorR:
                return new bullet_cond.BulletCondition_ColorR(data);
            case eBulletCondition.Bullet_ColorG:
                return new bullet_cond.BulletCondition_ColorG(data);
            case eBulletCondition.Bullet_ColorB:
                return new bullet_cond.BulletCondition_ColorB(data);
            case eBulletCondition.Bullet_Orientation:
                return new bullet_cond.BulletCondition_Orientation(data);
            case eBulletCondition.Bullet_OrientationParam:
                return new bullet_cond.BulletCondition_OrientationParam(data);
            case eBulletCondition.Bullet_TrackingTarget:
                return new bullet_cond.BulletCondition_TrackingTarget(data);
            case eBulletCondition.Bullet_Destructive:
                return new bullet_cond.BulletCondition_Destructive(data);
            case eBulletCondition.Bullet_DestructiveOnHit:
                return new bullet_cond.BulletCondition_DestructiveOnHit(data);
            case eBulletCondition.Bullet_DistanceToPlayer:
                return new bullet_cond.BulletCondition_DistanceToPlayer(data);
            default:
                throw new Error(`Unknown condition type: ${data.type}`);
        }
    }
}

class ActionFactory {
    static create(data: EventActionData): IEventAction<EventGroupContext> {
        switch (data.type) {
            case eEmitterAction.Emitter_Active:
                return new emitter_act.EmitterAction_Active(data);
            case eEmitterAction.Emitter_InitialDelay:
                return new emitter_act.EmitterAction_InitialDelay(data);
            case eEmitterAction.Emitter_Prewarm:
                return new emitter_act.EmitterAction_Prewarm(data);
            case eEmitterAction.Emitter_PrewarmDuration:
                return new emitter_act.EmitterAction_PrewarmDuration(data);
            case eEmitterAction.Emitter_Duration:
                return new emitter_act.EmitterAction_Duration(data);
            case eEmitterAction.Emitter_ElapsedTime:
                return new emitter_act.EmitterAction_ElapsedTime(data);
            case eEmitterAction.Emitter_Loop:
                return new emitter_act.EmitterAction_Loop(data);
            case eEmitterAction.Emitter_LoopInterval:
                return new emitter_act.EmitterAction_LoopInterval(data);
            case eEmitterAction.Emitter_EmitInterval:
                return new emitter_act.EmitterAction_EmitInterval(data);
            case eEmitterAction.Emitter_PerEmitCount:
                return new emitter_act.EmitterAction_PerEmitCount(data);
            case eEmitterAction.Emitter_PerEmitInterval:
                return new emitter_act.EmitterAction_PerEmitInterval(data);
            case eEmitterAction.Emitter_PerEmitOffsetX:
                return new emitter_act.EmitterAction_PerEmitOffsetX(data);
            case eEmitterAction.Emitter_Angle:
                return new emitter_act.EmitterAction_Angle(data);
            case eEmitterAction.Emitter_Count:
                return new emitter_act.EmitterAction_Count(data);
            case eEmitterAction.Bullet_Duration:
                return new emitter_act.EmitterAction_BulletDuration(data);
            case eEmitterAction.Bullet_Damage:
                return new emitter_act.EmitterAction_BulletDamage(data);
            case eEmitterAction.Bullet_Speed:
                return new emitter_act.EmitterAction_BulletSpeed(data);
            case eEmitterAction.Bullet_SpeedAngle:
                return new emitter_act.EmitterAction_BulletSpeedAngle(data);
            case eEmitterAction.Bullet_Acceleration:
                return new emitter_act.EmitterAction_BulletAcceleration(data);
            case eEmitterAction.Bullet_AccelerationAngle:
                return new emitter_act.EmitterAction_BulletAccelerationAngle(data);
            case eEmitterAction.Bullet_Scale:
                return new emitter_act.EmitterAction_BulletScale(data);
            case eEmitterAction.Bullet_ColorR:
                return new emitter_act.EmitterAction_BulletColorR(data);
            case eEmitterAction.Bullet_ColorG:
                return new emitter_act.EmitterAction_BulletColorG(data);
            case eEmitterAction.Bullet_ColorB:
                return new emitter_act.EmitterAction_BulletColorB(data);
            case eEmitterAction.Bullet_Orientation:
                return new emitter_act.EmitterAction_BulletOrientation(data);
            case eEmitterAction.Bullet_OrientationParam:
                return new emitter_act.EmitterAction_BulletOrientationParam(data);
            case eEmitterAction.Bullet_MaxTrackingDuration:
                return new emitter_act.EmitterAction_BulletMaxTrackingDuration(data);
            case eEmitterAction.Bullet_MaxTrackingAngle:
                return new emitter_act.EmitterAction_BulletMaxTrackingAngle(data);
            case eEmitterAction.Bullet_Destructive:
                return new emitter_act.EmitterAction_BulletDestructive(data);
            case eEmitterAction.Bullet_DestructiveOnHit:
                return new emitter_act.EmitterAction_BulletDestructiveOnHit(data);
            // ... bullet cases
            case eBulletAction.Bullet_Duration:
                return new bullet_act.BulletAction_Duration(data);
            case eBulletAction.Bullet_ElapsedTime:
                return new bullet_act.BulletAction_ElapsedTime(data);
            case eBulletAction.Bullet_PosX:
                return new bullet_act.BulletAction_PosX(data);
            case eBulletAction.Bullet_PosY:
                return new bullet_act.BulletAction_PosY(data);
            case eBulletAction.Bullet_Speed:
                return new bullet_act.BulletAction_Speed(data);
            case eBulletAction.Bullet_SpeedAngle:
                return new bullet_act.BulletAction_SpeedAngle(data);
            case eBulletAction.Bullet_Acceleration:
                return new bullet_act.BulletAction_Acceleration(data);
            case eBulletAction.Bullet_AccelerationAngle:
                return new bullet_act.BulletAction_AccelerationAngle(data);
            case eBulletAction.Bullet_Scale:
                return new bullet_act.BulletAction_Scale(data);
            case eBulletAction.Bullet_ColorR:
                return new bullet_act.BulletAction_ColorR(data);
            case eBulletAction.Bullet_ColorG:
                return new bullet_act.BulletAction_ColorG(data);
            case eBulletAction.Bullet_ColorB:
                return new bullet_act.BulletAction_ColorB(data);
            case eBulletAction.Bullet_Orientation:
                return new bullet_act.BulletAction_Orientation(data);
            case eBulletAction.Bullet_OrientationParam:
                return new bullet_act.BulletAction_OrientationParam(data);
            case eBulletAction.Bullet_MaxTrackingDuration:
                return new bullet_act.BulletAction_MaxTrackingDuration(data);
            case eBulletAction.Bullet_MaxTrackingAngle:
                return new bullet_act.BulletAction_MaxTrackingAngle(data);
            case eBulletAction.Bullet_Destructive:
                return new bullet_act.BulletAction_Destructive(data);
            case eBulletAction.Bullet_DestructiveOnHit:
                return new bullet_act.BulletAction_DestructiveOnHit(data);
            default:
                throw new Error(`Unknown action type: ${data.type}`);
        }
    }
}

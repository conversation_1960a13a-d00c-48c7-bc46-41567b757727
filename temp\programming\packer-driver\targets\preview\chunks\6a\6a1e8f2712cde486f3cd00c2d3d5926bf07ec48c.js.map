{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/game/MBoomUI.ts"], "names": ["_decorator", "v3", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "logDebug", "GameIns", "Drag<PERSON><PERSON><PERSON>", "BundleName", "ccclass", "property", "MBoomUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getBundleName", "GameFight", "start", "node", "position", "getComponent", "addClick", "onClick", "mainPlaneManager", "mainPlane", "CastSkill", "onShow", "onHide", "onClose"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,E,OAAAA,E;;AAEZC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;;AACRC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;yBAGjBU,O,WADZF,OAAO,CAAC,SAAD,C,gBAAR,MACaE,OADb;AAAA;AAAA,4BACoC;AACZ,eAANC,MAAM,GAAW;AAAE,iBAAO,gBAAP;AAA0B;;AACrC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAwB;;AACjC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,SAAlB;AAA6B;;AAE3DC,QAAAA,KAAK,GAAS;AACpB,eAAKC,IAAL,CAAUC,QAAV,GAAqBjB,EAAE,CAAC,CAAC,GAAF,EAAO,CAAC,GAAR,EAAa,CAAb,CAAvB;AACA,eAAKkB,YAAL;AAAA;AAAA,wCAA+BC,QAA/B,CAAwC,KAAKC,OAA7C,EAAsD,IAAtD;AACH;;AAEDA,QAAAA,OAAO,GAAG;AAAA;;AACN;AAAA;AAAA,oCAAS,SAAT,EAAoB,SAApB,EAA+B,QAA/B;AACA;AAAA;AAAA,kCAAQC,gBAAR,CAAyBC,SAAzB,mCAAoCC,SAApC,CAA8C,CAA9C;AACH;;AAEKC,QAAAA,MAAM,GAAkB;AAAA;AAC7B;;AAEKC,QAAAA,MAAM,GAAgC;AAAA;AAC3C;;AAEKC,QAAAA,OAAO,GAAgC;AAAA;AAC5C;;AAtB+B,O", "sourcesContent": ["import { _decorator, v3 } from 'cc';\n\nimport { Base<PERSON>, UILayer } from '../../../../../../scripts/core/base/UIMgr';\nimport { logDebug } from '../../../../../../scripts/utils/Logger';\nimport { GameIns } from '../../../game/GameIns';\nimport { DragButton } from '../../common/components/button/DragButton';\nimport { BundleName } from '../../../const/BundleConst';\nconst { ccclass, property } = _decorator;\n\n@ccclass('MBoomUI')\nexport class MBoomUI extends BaseUI {\n    public static getUrl(): string { return \"prefab/MBoomUI\"; }\n    public static getLayer(): UILayer { return UILayer.Default }\n    public static getBundleName(): string { return BundleName.GameFight }\n\n    protected start(): void {\n        this.node.position = v3(-230, -400, 0)\n        this.getComponent(DragButton)!.addClick(this.onClick, this)\n    }\n\n    onClick() {\n        logDebug(\"MBoom<PERSON>\", \"onClick\", \"aaaaaa\")\n        GameIns.mainPlaneManager.mainPlane?.CastSkill(1)\n    }\n\n    async onShow(): Promise<void> {\n    }\n\n    async onHide(...args: any[]): Promise<void> {\n    }\n\n    async onClose(...args: any[]): Promise<void> {\n    }\n}"]}
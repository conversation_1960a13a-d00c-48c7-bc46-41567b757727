System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Label, Sprite, BundleName, ButtonPlus, BaseUI, UILayer, UIMgr, AvatarIcon, List, FriendFusionUI, FriendRankCellUI, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _dec10, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _descriptor9, _crd, ccclass, property, FriendRankUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "db://assets/bundles/common/script/const/BundleConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "db://assets/bundles/common/script/ui/common/components/button/ButtonPlus", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIOpt(extras) {
    _reporterNs.report("UIOpt", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAvatarIcon(extras) {
    _reporterNs.report("AvatarIcon", "../common/components/base/AvatarIcon", _context.meta, extras);
  }

  function _reportPossibleCrUseOfList(extras) {
    _reporterNs.report("List", "../common/components/list/List", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFriendFusionUI(extras) {
    _reporterNs.report("FriendFusionUI", "./FriendFusionUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFriendRankCellUI(extras) {
    _reporterNs.report("FriendRankCellUI", "./FriendRankCellUI", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Label = _cc.Label;
      Sprite = _cc.Sprite;
    }, function (_unresolved_2) {
      BundleName = _unresolved_2.BundleName;
    }, function (_unresolved_3) {
      ButtonPlus = _unresolved_3.ButtonPlus;
    }, function (_unresolved_4) {
      BaseUI = _unresolved_4.BaseUI;
      UILayer = _unresolved_4.UILayer;
      UIMgr = _unresolved_4.UIMgr;
    }, function (_unresolved_5) {
      AvatarIcon = _unresolved_5.AvatarIcon;
    }, function (_unresolved_6) {
      List = _unresolved_6.default;
    }, function (_unresolved_7) {
      FriendFusionUI = _unresolved_7.FriendFusionUI;
    }, function (_unresolved_8) {
      FriendRankCellUI = _unresolved_8.FriendRankCellUI;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "4c78dGiQU5JmoaeHU2KJwkN", "FriendRankUI", undefined);

      __checkObsolete__(['_decorator', 'Label', 'Node', 'Sprite']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("FriendRankUI", FriendRankUI = (_dec = ccclass('FriendRankUI'), _dec2 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec3 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec4 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec5 = property(_crd && List === void 0 ? (_reportPossibleCrUseOfList({
        error: Error()
      }), List) : List), _dec6 = property(Label), _dec7 = property(_crd && AvatarIcon === void 0 ? (_reportPossibleCrUseOfAvatarIcon({
        error: Error()
      }), AvatarIcon) : AvatarIcon), _dec8 = property(Label), _dec9 = property(Label), _dec10 = property(Sprite), _dec(_class = (_class2 = class FriendRankUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "btnClose", _descriptor, this);

          _initializerDefineProperty(this, "btnEndless", _descriptor2, this);

          _initializerDefineProperty(this, "btnPK", _descriptor3, this);

          _initializerDefineProperty(this, "list", _descriptor4, this);

          _initializerDefineProperty(this, "selfRank", _descriptor5, this);

          _initializerDefineProperty(this, "selfAvatar", _descriptor6, this);

          _initializerDefineProperty(this, "selfName", _descriptor7, this);

          _initializerDefineProperty(this, "selfScore", _descriptor8, this);

          _initializerDefineProperty(this, "selfPlane", _descriptor9, this);
        }

        static getUrl() {
          return "prefab/ui/FriendRankUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Default;
        }

        static getBundleName() {
          return (_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).HomePK;
        }

        static getUIOption() {
          return {
            isClickBgCloseUI: true
          };
        }

        onLoad() {
          this.btnClose.addClick(this.onCloseClick, this);
          this.btnEndless.addClick(this.onEndlessClick, this);
          this.btnPK.addClick(this.onPKClick, this);
          this.list.numItems = 10;
          this.setSelfData({
            rank: 1,
            name: "小师妹",
            score: 10000,
            avatar: "",
            plane: ""
          });
        }

        async onCloseClick() {
          await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).closeUI(FriendRankUI);
        }

        async onEndlessClick() {
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).closeUI(FriendRankUI);
          await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && FriendFusionUI === void 0 ? (_reportPossibleCrUseOfFriendFusionUI({
            error: Error()
          }), FriendFusionUI) : FriendFusionUI);
        }

        async onPKClick() {}

        async onShow() {}

        async onHide() {}

        async onClose() {}

        onDestroy() {}

        setSelfData(data) {
          this.selfRank.string = data.rank.toString();
          this.selfName.string = data.name;
          this.selfScore.string = data.score.toString();
        }

        onListRender(listItem, row) {
          const cell = listItem.getComponent(_crd && FriendRankCellUI === void 0 ? (_reportPossibleCrUseOfFriendRankCellUI({
            error: Error()
          }), FriendRankCellUI) : FriendRankCellUI);

          if (cell !== null) {
            cell.setData(row + 1);
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "btnClose", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "btnEndless", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "btnPK", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "list", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "selfRank", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "selfAvatar", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "selfName", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor8 = _applyDecoratedDescriptor(_class2.prototype, "selfScore", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor9 = _applyDecoratedDescriptor(_class2.prototype, "selfPlane", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=7c002720e7cdb5e631175d01c4daf5b9fea091a7.js.map
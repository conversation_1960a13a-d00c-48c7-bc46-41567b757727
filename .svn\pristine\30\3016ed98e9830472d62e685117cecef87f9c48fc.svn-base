import { _decorator, Component, Node, Prefab } from "cc";
import { LevelData, LevelDataBackgroundLayer, LevelDataLayer } from "db://assets/bundles/common/script/leveldata/leveldata";
import { LevelLayerUI } from "./LevelLayerUI";
import { logError } from "db://assets/scripts/utils/Logger";
import { LevelBackgroundLayerUI } from "./LevelBackgroundLayerUI";
import { LevelUtils } from "./LevelUtils";
import { LevelLayer } from "./LevelLayer";

const { ccclass, property } = _decorator;

@ccclass('LevelBaseUI')
export class LevelBaseUI extends Component {
    private _curLevelIndex: number = -1; // 当前关卡索引
    private _totalTime: number = 10; // 当前关卡的时长
    private _speed: number = 0; // 当前关卡的速度
    private _preLevelOffsetY: number = 0; // 上一关的关卡偏移量

    private _backgroundLayerNode: Node | null = null;
    private _floorLayersNode: Node | null = null;
    private _skyLayersNode: Node | null = null;

    private _backgroundLayer: LevelLayer | null = null;
    private _floorLayers: LevelLayer[] = [];
    private _skyLayers: LevelLayer[] = [];

    public get backgroundLayer(): LevelLayer {
        return this._backgroundLayer!;
    }

    public get floorLayers(): LevelLayer[] {
        return this._floorLayers;
    }
    public get skyLayers(): LevelLayer[] {
        return this._skyLayers;
    }

    public get TotalTime(): number {
        return this._totalTime;
    }

    protected onLoad(): void {
    }

    public async levelPrefab(levelData: LevelData, levelInfo: {bFirstLoad: boolean, levelIndex: number }): Promise<void> {
        this._backgroundLayerNode = LevelUtils.getOrAddNode(this.node, "BackgroundLayer");
        this._floorLayersNode = LevelUtils.getOrAddNode(this.node, "FloorLayers");
        this._skyLayersNode = LevelUtils.getOrAddNode(this.node, "SkyLayers");

        if (levelInfo.bFirstLoad) {
            await this._initByLevelData(levelData, levelInfo);
        } else {
            this._initByLevelData(levelData, levelInfo);
        }
    }

    public switchLevel(time: number, levelIndex: number): void {
        this._totalTime = time;

        // 释放上一关资源
        if (this._curLevelIndex !== -1) {
            this._removeNode(this._backgroundLayerNode!, `level_${this._curLevelIndex}`);
            this._removeNode(this._floorLayersNode!, `level_${this._curLevelIndex}`);
            this._removeNode(this._skyLayersNode!, `level_${this._curLevelIndex}`);
        }

        this._curLevelIndex = levelIndex;
    }

    private _removeNode(parentNode: Node, name: string): void {
        var node = parentNode.getChildByName(name);
        if (node) {
            node.removeFromParent();
        }
    }

    public async _initByLevelData(data: LevelData, levelInfo: {bFirstLoad: boolean, levelIndex: number }): Promise<void> {
        const levelBackground = LevelUtils.getOrAddNode(this._backgroundLayerNode!, `level_${levelInfo.levelIndex}`);
        const levelFloor = LevelUtils.getOrAddNode(this._floorLayersNode!, `level_${levelInfo.levelIndex}`);
        const levelSky = LevelUtils.getOrAddNode(this._skyLayersNode!, `level_${levelInfo.levelIndex}`);

        await this._initBackgroundLayer(levelBackground, data.backgroundLayer, levelInfo.bFirstLoad);
        await this._initLayers(levelFloor, this.floorLayers, data.floorLayers, levelInfo.bFirstLoad);
        await this._initLayers(levelSky, this.skyLayers, data.skyLayers, levelInfo.bFirstLoad);
    }

    private async _initBackgroundLayer(parentNode: Node, dataLayer: LevelDataBackgroundLayer, bFirstLoad: boolean): Promise<void> {
        if (dataLayer.backgrounds.length > 0) {
            let node = new Node('bg_0');
            const backgroundLayer = node.addComponent(LevelBackgroundLayerUI);
            parentNode.addChild(node);
            backgroundLayer.speed = dataLayer.speed;
            await backgroundLayer!.initByLevelData(dataLayer, this._preLevelOffsetY, bFirstLoad);
            this._backgroundLayer = backgroundLayer;
        }
    }

    private async _initLayers(parentNode: Node, layers: LevelLayer[], dataLayers: LevelDataLayer[], bFirstLoad: boolean): Promise<void> {
        for (let i = 0; i < dataLayers.length; i++) {
            const layer = dataLayers[i];
            let levelLayer = this._addLayer(parentNode, `layer_${i}`);
            levelLayer!.speed = layer.speed;
            await levelLayer!.initByLevelData(layer, this._preLevelOffsetY, bFirstLoad);
            layers.push(levelLayer!);
        }
    }

    private _addLayer(parentNode: Node, name: string): LevelLayerUI {
        var layerNode = new Node(name);
        var layerCom = layerNode.addComponent<LevelLayerUI>(LevelLayerUI);
        parentNode.addChild(layerNode);
        return layerCom;
    }

    public tick(deltaTime: number): void {
        const layerUI = this.backgroundLayer;//!.node?.getComponent<LevelBackgroundLayerUI>(LevelBackgroundLayerUI);
        if (layerUI) {
            layerUI.tick(deltaTime);
        }

        this.floorLayers.forEach((layer) => {
            const layerUI = layer;//.node?.getComponent<LevelLayerUI>(LevelLayerUI);
            if (layerUI) {
                layerUI.tick(deltaTime);
            }
        });
        
        this.skyLayers.forEach((layer) => {
            const layerUI = layer;//.node?.getComponent<LevelLayerUI>(LevelLayerUI);
            if (layerUI) {
                layerUI.tick(deltaTime);
            }
        });
    }
}
{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/match/MatchResultUI.ts"], "names": ["_decorator", "BundleName", "ButtonPlus", "HomeUI", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "ccclass", "property", "MatchResultUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getBundleName", "homeMatch", "getUIOption", "isClickBgCloseUI", "onLoad", "btnExchange", "addClick", "onExchangeClick", "btnSurpass", "onSurpassClick", "btnSuspend", "onSuspendClick", "btnFightBack", "onFightBackClick", "btnGetReward", "onGetRewardClick", "switchBtn", "type", "node", "active", "closeUI", "openUI", "onShow", "onHide", "onClose", "onDestroy"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;;;;;;;;OAEpB;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;;+BAGjBS,a,WADZF,OAAO,CAAC,eAAD,C,UAGHC,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,2BAXb,MACaC,aADb;AAAA;AAAA,4BAC0C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAalB,eAANC,MAAM,GAAW;AAAE,iBAAO,yBAAP;AAAmC;;AAC9C,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAwB;;AACjC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,SAAlB;AAA8B;;AAC7C,eAAXC,WAAW,GAAU;AAAE,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AAAmC;;AAC9DC,QAAAA,MAAM,GAAS;AACrB,eAAKC,WAAL,CAAkBC,QAAlB,CAA2B,KAAKC,eAAhC,EAAiD,IAAjD;AACA,eAAKC,UAAL,CAAiBF,QAAjB,CAA0B,KAAKG,cAA/B,EAA+C,IAA/C;AACA,eAAKC,UAAL,CAAiBJ,QAAjB,CAA0B,KAAKK,cAA/B,EAA+C,IAA/C;AACA,eAAKC,YAAL,CAAmBN,QAAnB,CAA4B,KAAKO,gBAAjC,EAAmD,IAAnD;AACA,eAAKC,YAAL,CAAmBR,QAAnB,CAA4B,KAAKS,gBAAjC,EAAmD,IAAnD;AACA,eAAKC,SAAL,CAAe,CAAf;AACH;;AAEDA,QAAAA,SAAS,CAACC,IAAD,EAAe;AACpB,eAAKZ,WAAL,CAAkBa,IAAlB,CAAuBC,MAAvB,GAAgCF,IAAI,IAAI,CAAxC;AACA,eAAKT,UAAL,CAAiBU,IAAjB,CAAsBC,MAAtB,GAA+BF,IAAI,IAAI,CAAvC;AACA,eAAKP,UAAL,CAAiBQ,IAAjB,CAAsBC,MAAtB,GAA+BF,IAAI,IAAI,CAAvC;AACA,eAAKL,YAAL,CAAmBM,IAAnB,CAAwBC,MAAxB,GAAiCF,IAAI,IAAI,CAAzC;AACA,eAAKH,YAAL,CAAmBI,IAAnB,CAAwBC,MAAxB,GAAiCF,IAAI,IAAI,CAAzC;AACH;;AAGKV,QAAAA,eAAe,GAAG;AAAA;AACpB;AAAA;AAAA,gCAAMa,OAAN,CAAcxB,aAAd;AACA,kBAAM;AAAA;AAAA,gCAAMyB,MAAN;AAAA;AAAA,iCAAN;AAFoB;AAGvB;;AACKZ,QAAAA,cAAc,GAAG;AAAA;AACnB;AAAA;AAAA,gCAAMW,OAAN,CAAcxB,aAAd;AACA,kBAAM;AAAA;AAAA,gCAAMyB,MAAN;AAAA;AAAA,iCAAN;AAFmB;AAGtB;;AACKV,QAAAA,cAAc,GAAG;AAAA;AACnB;AAAA;AAAA,gCAAMS,OAAN,CAAcxB,aAAd;AACA,kBAAM;AAAA;AAAA,gCAAMyB,MAAN;AAAA;AAAA,iCAAN;AAFmB;AAGtB;;AACKR,QAAAA,gBAAgB,GAAG;AAAA;AACrB;AAAA;AAAA,gCAAMO,OAAN,CAAcxB,aAAd;AACA,kBAAM;AAAA;AAAA,gCAAMyB,MAAN;AAAA;AAAA,iCAAN;AAFqB;AAGxB;;AACKN,QAAAA,gBAAgB,GAAG;AAAA;AACrB;AAAA;AAAA,gCAAMK,OAAN,CAAcxB,aAAd;AACA,kBAAM;AAAA;AAAA,gCAAMyB,MAAN;AAAA;AAAA,iCAAN;AAFqB;AAGxB;;AAEKC,QAAAA,MAAM,GAAkB;AAAA;AAE7B;;AACKC,QAAAA,MAAM,GAAkB;AAAA;AAC7B;;AACKC,QAAAA,OAAO,GAAkB;AAAA;AAC9B;;AACSC,QAAAA,SAAS,GAAS,CAC3B;;AAhEqC,O;;;;;iBAGL,I;;;;;;;iBAED,I;;;;;;;iBAEA,I;;;;;;;iBAEE,I;;;;;;;iBAEA,I", "sourcesContent": ["import { _decorator } from 'cc';\r\nimport { BundleName } from 'db://assets/bundles/common/script/const/BundleConst';\r\nimport { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';\r\nimport { HomeUI } from 'db://assets/bundles/common/script/ui/home/<USER>';\r\nimport { BaseUI, UILayer, UIMgr, UIOpt } from 'db://assets/scripts/core/base/UIMgr';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('MatchResultUI')\r\nexport class MatchResultUI extends BaseUI {\r\n\r\n    @property(ButtonPlus)\r\n    btnExchange: ButtonPlus | null = null;\r\n    @property(ButtonPlus)\r\n    btnSurpass: ButtonPlus | null = null;\r\n    @property(ButtonPlus)\r\n    btnSuspend: ButtonPlus | null = null;\r\n    @property(ButtonPlus)\r\n    btnFightBack: ButtonPlus | null = null;\r\n    @property(ButtonPlus)\r\n    btnGetReward: ButtonPlus | null = null;\r\n\r\n    public static getUrl(): string { return \"prefab/ui/MatchResultUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Default }\r\n    public static getBundleName(): string { return BundleName.homeMatch; }\r\n    public static getUIOption(): UIOpt { return { isClickBgCloseUI: true } }\r\n    protected onLoad(): void {\r\n        this.btnExchange!.addClick(this.onExchangeClick, this);\r\n        this.btnSurpass!.addClick(this.onSurpassClick, this);\r\n        this.btnSuspend!.addClick(this.onSuspendClick, this);\r\n        this.btnFightBack!.addClick(this.onFightBackClick, this);\r\n        this.btnGetReward!.addClick(this.onGetRewardClick, this);\r\n        this.switchBtn(1);\r\n    }\r\n\r\n    switchBtn(type: number) {\r\n        this.btnExchange!.node.active = type == 1;\r\n        this.btnSurpass!.node.active = type == 1;\r\n        this.btnSuspend!.node.active = type == 2;\r\n        this.btnFightBack!.node.active = type == 2;\r\n        this.btnGetReward!.node.active = type == 3;\r\n    }\r\n\r\n\r\n    async onExchangeClick() {\r\n        UIMgr.closeUI(MatchResultUI);\r\n        await UIMgr.openUI(HomeUI)\r\n    }\r\n    async onSurpassClick() {\r\n        UIMgr.closeUI(MatchResultUI);\r\n        await UIMgr.openUI(HomeUI)\r\n    }\r\n    async onSuspendClick() {\r\n        UIMgr.closeUI(MatchResultUI);\r\n        await UIMgr.openUI(HomeUI)\r\n    }\r\n    async onFightBackClick() {\r\n        UIMgr.closeUI(MatchResultUI);\r\n        await UIMgr.openUI(HomeUI)\r\n    }\r\n    async onGetRewardClick() {\r\n        UIMgr.closeUI(MatchResultUI);\r\n        await UIMgr.openUI(HomeUI)\r\n    }\r\n\r\n    async onShow(): Promise<void> {\r\n\r\n    }\r\n    async onHide(): Promise<void> {\r\n    }\r\n    async onClose(): Promise<void> {\r\n    }\r\n    protected onDestroy(): void {\r\n    }\r\n}\r\n\r\n\r\n"]}
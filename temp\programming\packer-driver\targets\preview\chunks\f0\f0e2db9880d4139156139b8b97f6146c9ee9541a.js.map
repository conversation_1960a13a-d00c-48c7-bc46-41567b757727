{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/core/base/ResManager.ts"], "names": ["ResManager", "AnimationClip", "<PERSON><PERSON>", "assetManager", "AudioClip", "Font", "ImageAsset", "js", "JsonAsset", "Material", "<PERSON><PERSON>", "Prefab", "resources", "sp", "SpriteFrame", "Texture2D", "warn", "IMgr", "defaultBundleName", "instance", "_instance", "maxConcurrency", "downloader", "value", "maxRequestsPerFrame", "maxRetryCount", "retryInterval", "loadRemote", "url", "options", "onComplete", "getBundle", "name", "bundles", "get", "loadBundle", "Promise", "resolve", "reject", "err", "bundle", "removeBundle", "bundleName", "releaseAll", "preload", "paths", "type", "onProgress", "args", "Array", "parseLoadResArgs", "loadByArgs", "preloadAsync", "data", "message", "preloadDir", "dir", "load", "loadAsync", "asset", "loadDir", "release", "path", "undefined", "releasePrefabtDepsRecursively", "releaseDir", "infos", "getDirWithPath", "map", "info", "uuid", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "b", "getAssetInfo", "decRef", "assets", "releaseAssetByForce", "releaseAsset", "debugLogReleasedAsset", "refCount", "content", "SkeletonData", "console", "log", "pathsOut", "typeOut", "onProgressOut", "onCompleteOut", "isValidType", "isChildClassOf", "loadByBundleAndArgs", "dump", "for<PERSON>ach", "key", "count"], "mappings": ";;;0OAgCaA,U;;;;;;;;;;;;;;;;;;;AAhCOC,MAAAA,a,OAAAA,a;AAAeC,MAAAA,K,OAAAA,K;AAAqBC,MAAAA,Y,OAAAA,Y;AAAqBC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,E,OAAAA,E;AAAyBC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;AACpMC,MAAAA,I,iBAAAA,I;;;;;;;;;AAyBT;AACA;AACA;AACA;AACA;AACA;4BACajB,U,GAAN,MAAMA,UAAN;AAAA;AAAA,wBAA8B;AAAA;AAAA;AACjC;;AACA;AAFiC,eAGjCkB,iBAHiC,GAGL,WAHK;AAAA;;AAMd,mBAARC,QAAQ,GAAG;AAClB,cAAI,KAAKC,SAAT,EAAoB;AAChB,mBAAO,KAAKA,SAAZ;AACH;;AAED,eAAKA,SAAL,GAAiB,IAAIpB,UAAJ,EAAjB;AACA,iBAAO,KAAKoB,SAAZ;AACH;AAED;;;AACkB,YAAdC,cAAc,GAAG;AACjB,iBAAOlB,YAAY,CAACmB,UAAb,CAAwBD,cAA/B;AACH;;AACiB,YAAdA,cAAc,CAACE,KAAD,EAAQ;AACtBpB,UAAAA,YAAY,CAACmB,UAAb,CAAwBD,cAAxB,GAAyCE,KAAzC;AACH;AAED;;;AACuB,YAAnBC,mBAAmB,GAAG;AACtB,iBAAOrB,YAAY,CAACmB,UAAb,CAAwBE,mBAA/B;AACH;;AACsB,YAAnBA,mBAAmB,CAACD,KAAD,EAAQ;AAC3BpB,UAAAA,YAAY,CAACmB,UAAb,CAAwBE,mBAAxB,GAA8CD,KAA9C;AACH;AAED;;;AACiB,YAAbE,aAAa,GAAG;AAChB,iBAAOtB,YAAY,CAACmB,UAAb,CAAwBG,aAA/B;AACH;;AACgB,YAAbA,aAAa,CAACF,KAAD,EAAQ;AACrBpB,UAAAA,YAAY,CAACmB,UAAb,CAAwBG,aAAxB,GAAwCF,KAAxC;AACH;AAED;;;AACiB,YAAbG,aAAa,GAAG;AAChB,iBAAOvB,YAAY,CAACmB,UAAb,CAAwBI,aAA/B;AACH;;AACgB,YAAbA,aAAa,CAACH,KAAD,EAAQ;AACrBpB,UAAAA,YAAY,CAACmB,UAAb,CAAwBI,aAAxB,GAAwCH,KAAxC;AACH,SA7CgC,CA+CjC;;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGII,QAAAA,UAAU,CAAkBC,GAAlB,EAAmD;AACzD,cAAIC,OAA8B,GAAG,IAArC;AACA,cAAIC,UAA4B,GAAG,IAAnC;;AACA,cAAI,sDAAe,CAAnB,EAAsB;AAClBD,YAAAA,OAAO,mDAAP;AACAC,YAAAA,UAAU,mDAAV;AACH,WAHD,MAIK;AACDA,YAAAA,UAAU,mDAAV;AACH;;AACD3B,UAAAA,YAAY,CAACwB,UAAb,CAA2BC,GAA3B,EAAgCC,OAAhC,EAAyCC,UAAzC;AACH,SAhFgC,CAiFjC;AAEA;;AAEA;AACJ;AACA;AACA;;;AACIC,QAAAA,SAAS,CAACC,IAAD,EAAe;AACpB,iBAAO7B,YAAY,CAAC8B,OAAb,CAAqBC,GAArB,CAAyBF,IAAzB,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIG,QAAAA,UAAU,CAACH,IAAD,EAA6C;AACnD,iBAAO,IAAII,OAAJ,CAAiC,CAACC,OAAD,EAAUC,MAAV,KAAqB;AACzDnC,YAAAA,YAAY,CAACgC,UAAb,CAAwBH,IAAxB,EAA8B,CAACO,GAAD,EAAMC,MAAN,KAAsC;AAChE,kBAAID,GAAJ,EAAS;AACLF,gBAAAA,OAAO,CAAC,IAAD,CAAP;AACA;AACH;;AACDA,cAAAA,OAAO,CAACG,MAAD,CAAP;AACH,aAND;AAOH,WARM,CAAP;AASH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,YAAY,CAACC,UAAD,EAAqB;AAC7B,cAAIF,MAAM,GAAGrC,YAAY,CAAC8B,OAAb,CAAqBC,GAArB,CAAyBQ,UAAzB,CAAb;;AACA,cAAIF,MAAJ,EAAY;AACRA,YAAAA,MAAM,CAACG,UAAP;AACAxC,YAAAA,YAAY,CAACsC,YAAb,CAA0BD,MAA1B;AACH;AACJ,SAzHgC,CA0HjC;AAEA;;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AASII,QAAAA,OAAO,CACHF,UADG,EAEHG,KAFG,EAGHC,IAHG,EAIHC,UAJG,EAKHjB,UALG,EAML;AACE,cAAIkB,IAAgC,GAAG,IAAvC;;AACA,cAAI,OAAOH,KAAP,KAAiB,QAAjB,IAA6BA,KAAK,YAAYI,KAAlD,EAAyD;AACrDD,YAAAA,IAAI,GAAG,KAAKE,gBAAL,CAAsBL,KAAtB,EAA6BC,IAA7B,EAAmCC,UAAnC,EAA+CjB,UAA/C,CAAP;AACAkB,YAAAA,IAAI,CAACR,MAAL,GAAcE,UAAd;AACH,WAHD,MAIK;AACDM,YAAAA,IAAI,GAAG,KAAKE,gBAAL,CAAsBR,UAAtB,EAAkCG,KAAlC,EAAyCC,IAAzC,EAA+CC,UAA/C,CAAP;AACAC,YAAAA,IAAI,CAACR,MAAL,GAAc,KAAKtB,iBAAnB;AACH;;AACD8B,UAAAA,IAAI,CAACJ,OAAL,GAAe,IAAf;AACA,eAAKO,UAAL,CAAgBH,IAAhB;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AAKII,QAAAA,YAAY,CAAkBV,UAAlB,EACRG,KADQ,EAERC,IAFQ,EAEsF;AAC9F,iBAAO,IAAIV,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;AACpC,iBAAKM,OAAL,CAAaF,UAAb,EAAyBG,KAAzB,EAAgCC,IAAhC,EAAsC,CAACP,GAAD,EAAoBc,IAApB,KAAuD;AACzF,kBAAId,GAAJ,EAAS;AACLvB,gBAAAA,IAAI,CAACuB,GAAG,CAACe,OAAL,CAAJ;AACH;;AACDjB,cAAAA,OAAO,CAACgB,IAAD,CAAP;AACH,aALD;AAMH,WAPM,CAAP;AAQH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AASIE,QAAAA,UAAU,CACNb,UADM,EAENc,GAFM,EAGNV,IAHM,EAINC,UAJM,EAKNjB,UALM,EAMR;AACE,cAAIkB,IAA4B,GAAG,IAAnC;;AACA,cAAI,OAAOQ,GAAP,KAAe,QAAnB,EAA6B;AACzBR,YAAAA,IAAI,GAAG,KAAKE,gBAAL,CAAsBM,GAAtB,EAA2BV,IAA3B,EAAiCC,UAAjC,EAA6CjB,UAA7C,CAAP;AACAkB,YAAAA,IAAI,CAACR,MAAL,GAAcE,UAAd;AACH,WAHD,MAIK;AACDM,YAAAA,IAAI,GAAG,KAAKE,gBAAL,CAAsBR,UAAtB,EAAkCc,GAAlC,EAAuCV,IAAvC,EAA6CC,UAA7C,CAAP;AACAC,YAAAA,IAAI,CAACR,MAAL,GAAc,KAAKtB,iBAAnB;AACH;;AACD8B,UAAAA,IAAI,CAACQ,GAAL,GAAWR,IAAI,CAACH,KAAhB;AACAG,UAAAA,IAAI,CAACJ,OAAL,GAAe,IAAf;AACA,eAAKO,UAAL,CAAgBH,IAAhB;AACH,SA3NgC,CA4NjC;AAEA;;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAUIS,QAAAA,IAAI,CACAf,UADA,EAEAG,KAFA,EAGAC,IAHA,EAIAC,UAJA,EAKAjB,UALA,EAMF;AACE,cAAIkB,IAA4B,GAAG,IAAnC;;AACA,cAAI,OAAOH,KAAP,KAAiB,QAAjB,IAA6BA,KAAK,YAAYI,KAAlD,EAAyD;AACrDD,YAAAA,IAAI,GAAG,KAAKE,gBAAL,CAAsBL,KAAtB,EAA6BC,IAA7B,EAAmCC,UAAnC,EAA+CjB,UAA/C,CAAP;AACAkB,YAAAA,IAAI,CAACR,MAAL,GAAcE,UAAd;AACH,WAHD,MAIK;AACDM,YAAAA,IAAI,GAAG,KAAKE,gBAAL,CAAsBR,UAAtB,EAAkCG,KAAlC,EAAyCC,IAAzC,EAA+CC,UAA/C,CAAP;AACAC,YAAAA,IAAI,CAACR,MAAL,GAAc,KAAKtB,iBAAnB;AACH;;AACD,eAAKiC,UAAL,CAAgBH,IAAhB;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AAKIU,QAAAA,SAAS,CAAkBhB,UAAlB,EACLG,KADK,EAELC,IAFK,EAEkE;AACvE,iBAAO,IAAIV,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;AACpC,iBAAKmB,IAAL,CAAUf,UAAV,EAAsBG,KAAtB,EAA6BC,IAA7B,EAAmC,CAACP,GAAD,EAAoBoB,KAApB,KAAiC;AAChE,kBAAIpB,GAAJ,EAAS;AACLvB,gBAAAA,IAAI,CAACuB,GAAG,CAACe,OAAL,CAAJ;AACH;;AACDjB,cAAAA,OAAO,CAACsB,KAAD,CAAP;AACH,aALD;AAMH,WAPM,CAAP;AAQH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAUIC,QAAAA,OAAO,CACHlB,UADG,EAEHc,GAFG,EAGHV,IAHG,EAIHC,UAJG,EAKHjB,UALG,EAML;AACE,cAAIkB,IAA4B,GAAG,IAAnC;;AACA,cAAI,OAAOQ,GAAP,KAAe,QAAnB,EAA6B;AACzBR,YAAAA,IAAI,GAAG,KAAKE,gBAAL,CAAsBM,GAAtB,EAA2BV,IAA3B,EAAiCC,UAAjC,EAA6CjB,UAA7C,CAAP;AACAkB,YAAAA,IAAI,CAACR,MAAL,GAAcE,UAAd;AACH,WAHD,MAIK;AACDM,YAAAA,IAAI,GAAG,KAAKE,gBAAL,CAAsBR,UAAtB,EAAkCc,GAAlC,EAAuCV,IAAvC,EAA6CC,UAA7C,CAAP;AACAC,YAAAA,IAAI,CAACR,MAAL,GAAc,KAAKtB,iBAAnB;AACH;;AACD8B,UAAAA,IAAI,CAACQ,GAAL,GAAWR,IAAI,CAACH,KAAhB;AACA,eAAKM,UAAL,CAAgBH,IAAhB;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIa,QAAAA,OAAO,CAACC,IAAD,EAAepB,UAAf,EAAoC;AACvC,cAAIA,UAAU,IAAIqB,SAAlB,EAA6BrB,UAAU,GAAG,KAAKxB,iBAAlB;AAE7B,cAAMsB,MAAM,GAAGrC,YAAY,CAAC4B,SAAb,CAAuBW,UAAvB,CAAf;;AACA,cAAIF,MAAJ,EAAY;AACR,gBAAMmB,KAAK,GAAGnB,MAAM,CAACN,GAAP,CAAW4B,IAAX,CAAd;;AACA,gBAAIH,KAAJ,EAAW;AACP,mBAAKK,6BAAL,CAAmCtB,UAAnC,EAA+CiB,KAA/C;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIM,QAAAA,UAAU,CAACH,IAAD,EAAepB,UAAf,EAA4D;AAAA,cAA7CA,UAA6C;AAA7CA,YAAAA,UAA6C,GAAxB,KAAKxB,iBAAmB;AAAA;;AAClE,cAAMsB,MAAkC,GAAGrC,YAAY,CAAC4B,SAAb,CAAuBW,UAAvB,CAA3C;;AACA,cAAIF,MAAJ,EAAY;AACR,gBAAI0B,KAAK,GAAG1B,MAAM,CAAC2B,cAAP,CAAsBL,IAAtB,CAAZ;;AACA,gBAAII,KAAJ,EAAW;AACPA,cAAAA,KAAK,CAACE,GAAN,CAAWC,IAAD,IAAU;AAChB,qBAAKL,6BAAL,CAAmCtB,UAAnC,EAA+C2B,IAAI,CAACC,IAApD;AACH,eAFD;AAGH;;AAED,gBAAIR,IAAI,IAAI,EAAR,IAAcpB,UAAU,IAAI,WAAhC,EAA6C;AACzCvC,cAAAA,YAAY,CAACsC,YAAb,CAA0BD,MAA1B;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACI+B,QAAAA,YAAY,CAAC7B,UAAD,EAAqB4B,IAArB,EAA2C;AACnD,cAAIE,CAAC,GAAG,KAAKzC,SAAL,CAAeW,UAAf,CAAR;AACA,cAAI2B,IAAI,GAAGG,CAAC,CAACC,YAAF,CAAeH,IAAf,CAAX;AACA,cAAID,IAAI,KAAK,IAAb,EAAmB,OAAO,EAAP,CAHgC,CAInD;;AACA,iBAAOA,IAAI,CAACP,IAAZ;AACH;AAED;;;AACQE,QAAAA,6BAA6B,CAACtB,UAAD,EAAqB4B,IAArB,EAA2C;AAC5E,cAAIA,IAAI,YAAYpE,KAApB,EAA2B;AACvBoE,YAAAA,IAAI,CAACI,MAAL,GADuB,CAEvB;AACA;AACH,WAJD,MAKK;AACD,gBAAMf,KAAK,GAAGxD,YAAY,CAACwE,MAAb,CAAoBzC,GAApB,CAAwBoC,IAAxB,CAAd;;AACA,gBAAIX,KAAJ,EAAW;AACPA,cAAAA,KAAK,CAACe,MAAN,GADO,CAEP;AACA;AACH;AACJ;AACJ;;AAEDE,QAAAA,mBAAmB,CAACjB,KAAD,EAAe;AAC9BxD,UAAAA,YAAY,CAAC0E,YAAb,CAA0BlB,KAA1B;AACH;;AAEOmB,QAAAA,qBAAqB,CAACpC,UAAD,EAAqBiB,KAArB,EAAmC;AAC5D,cAAIA,KAAK,CAACoB,QAAN,IAAkB,CAAtB,EAAyB;AACrB,gBAAIjB,IAAI,GAAG,KAAKS,YAAL,CAAkB7B,UAAlB,EAA8BiB,KAAK,CAACW,IAApC,CAAX;AACA,gBAAIU,OAAe,GAAG,EAAtB;;AACA,gBAAIrB,KAAK,YAAYnD,SAArB,EAAgC;AAC5BwE,cAAAA,OAAO,GAAG,mBAAmBlB,IAA7B;AACH,aAFD,MAGK,IAAIH,KAAK,YAAYhD,MAArB,EAA6B;AAC9BqE,cAAAA,OAAO,GAAG,qBAAqBlB,IAA/B;AACH,aAFI,MAGA,IAAIH,KAAK,YAAY7C,WAArB,EAAkC;AACnCkE,cAAAA,OAAO,GAAG,0BAA0BlB,IAApC;AACH,aAFI,MAGA,IAAIH,KAAK,YAAY5C,SAArB,EAAgC;AACjCiE,cAAAA,OAAO,GAAG,wBAAwBlB,IAAlC;AACH,aAFI,MAGA,IAAIH,KAAK,YAAYrD,UAArB,EAAiC;AAClC0E,cAAAA,OAAO,GAAG,yBAAyBlB,IAAnC;AACH,aAFI,MAGA,IAAIH,KAAK,YAAYvD,SAArB,EAAgC;AACjC4E,cAAAA,OAAO,GAAG,wBAAwBlB,IAAlC;AACH,aAFI,MAGA,IAAIH,KAAK,YAAY1D,aAArB,EAAoC;AACrC+E,cAAAA,OAAO,GAAG,4BAA4BlB,IAAtC;AACH,aAFI,MAGA,IAAIH,KAAK,YAAYtD,IAArB,EAA2B;AAC5B2E,cAAAA,OAAO,GAAG,mBAAmBlB,IAA7B;AACH,aAFI,MAGA,IAAIH,KAAK,YAAYlD,QAArB,EAA+B;AAChCuE,cAAAA,OAAO,GAAG,uBAAuBlB,IAAjC;AACH,aAFI,MAGA,IAAIH,KAAK,YAAYjD,IAArB,EAA2B;AAC5BsE,cAAAA,OAAO,GAAG,mBAAmBlB,IAA7B;AACH,aAFI,MAGA,IAAIH,KAAK,YAAY9C,EAAE,CAACoE,YAAxB,EAAsC;AACvCD,cAAAA,OAAO,GAAG,oBAAoBlB,IAA9B;AACH,aAFI,MAGA;AACDkB,cAAAA,OAAO,GAAG,iBAAiBlB,IAA3B;AACH;;AACDoB,YAAAA,OAAO,CAACC,GAAR,CAAYH,OAAZ;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACI9C,QAAAA,GAAG,CAAkB4B,IAAlB,EAAgChB,IAAhC,EAAqDJ,UAArD,EAA4G;AAAA,cAAvDA,UAAuD;AAAvDA,YAAAA,UAAuD,GAAlC,KAAKxB,iBAA6B;AAAA;;AAC3G,cAAIsB,MAA2B,GAAGrC,YAAY,CAAC4B,SAAb,CAAuBW,UAAvB,CAAlC;AACA,iBAAOF,MAAM,CAACN,GAAP,CAAW4B,IAAX,EAAiBhB,IAAjB,CAAP;AACH,SA1cgC,CA2cjC;;;AAEQI,QAAAA,gBAAgB,CACpBL,KADoB,EAEpBC,IAFoB,EAGpBC,UAHoB,EAIpBjB,UAJoB,EAKtB;AACE,cAAIsD,QAAa,GAAGvC,KAApB;AACA,cAAIwC,OAAY,GAAGvC,IAAnB;AACA,cAAIwC,aAAkB,GAAGvC,UAAzB;AACA,cAAIwC,aAAkB,GAAGzD,UAAzB;;AACA,cAAIA,UAAU,KAAKiC,SAAnB,EAA8B;AAC1B,gBAAMyB,WAAW,GAAGjF,EAAE,CAACkF,cAAH,CAAkB3C,IAAlB,EAAqC5C,KAArC,CAApB;;AACA,gBAAI6C,UAAJ,EAAgB;AACZwC,cAAAA,aAAa,GAAGxC,UAAhB;;AACA,kBAAIyC,WAAJ,EAAiB;AACbF,gBAAAA,aAAa,GAAG,IAAhB;AACH;AACJ,aALD,MAMK,IAAIvC,UAAU,KAAKgB,SAAf,IAA4B,CAACyB,WAAjC,EAA8C;AAC/CD,cAAAA,aAAa,GAAGzC,IAAhB;AACAwC,cAAAA,aAAa,GAAG,IAAhB;AACAD,cAAAA,OAAO,GAAG,IAAV;AACH;;AACD,gBAAItC,UAAU,KAAKgB,SAAf,IAA4B,CAACyB,WAAjC,EAA8C;AAC1CF,cAAAA,aAAa,GAAGxC,IAAhB;AACAuC,cAAAA,OAAO,GAAG,IAAV;AACH;AACJ;;AACD,iBAAO;AAAExC,YAAAA,KAAK,EAAEuC,QAAT;AAAmBtC,YAAAA,IAAI,EAAEuC,OAAzB;AAAkCtC,YAAAA,UAAU,EAAEuC,aAA9C;AAA6DxD,YAAAA,UAAU,EAAEyD;AAAzE,WAAP;AACH;;AAEOG,QAAAA,mBAAmB,CAAkBlD,MAAlB,EAA+CQ,IAA/C,EAA4E;AACnG,cAAIA,IAAI,CAACQ,GAAT,EAAc;AACV,gBAAIR,IAAI,CAACJ,OAAT,EAAkB;AACdJ,cAAAA,MAAM,CAACe,UAAP,CAAkBP,IAAI,CAACH,KAAvB,EAAwCG,IAAI,CAACF,IAA7C,EAAmDE,IAAI,CAACD,UAAxD,EAAoEC,IAAI,CAAClB,UAAzE;AACH,aAFD,MAGK;AACDU,cAAAA,MAAM,CAACoB,OAAP,CAAeZ,IAAI,CAACH,KAApB,EAAqCG,IAAI,CAACF,IAA1C,EAAgDE,IAAI,CAACD,UAArD,EAAiEC,IAAI,CAAClB,UAAtE;AACH;AACJ,WAPD,MAQK;AACD,gBAAIkB,IAAI,CAACJ,OAAT,EAAkB;AACdJ,cAAAA,MAAM,CAACI,OAAP,CAAeI,IAAI,CAACH,KAApB,EAAkCG,IAAI,CAACF,IAAvC,EAA6CE,IAAI,CAACD,UAAlD,EAA8DC,IAAI,CAAClB,UAAnE;AACH,aAFD,MAGK;AACDU,cAAAA,MAAM,CAACiB,IAAP,CAAYT,IAAI,CAACH,KAAjB,EAA+BG,IAAI,CAACF,IAApC,EAA0CE,IAAI,CAACD,UAA/C,EAA2DC,IAAI,CAAClB,UAAhE;AACH;AACJ;AACJ;;AAEaqB,QAAAA,UAAU,CAAkBH,IAAlB,EAAyC;AAAA;;AAAA;AAC7D,gBAAIA,IAAI,CAACR,MAAT,EAAiB;AACb,kBAAIA,MAAM,GAAGrC,YAAY,CAAC8B,OAAb,CAAqBC,GAArB,CAAyBc,IAAI,CAACR,MAA9B,CAAb,CADa,CAEb;;AACA,kBAAIA,MAAJ,EAAY;AACR,gBAAA,KAAI,CAACkD,mBAAL,CAAyBlD,MAAzB,EAAiCQ,IAAjC;AACH,eAFD,CAGA;AAHA,mBAIK;AACDR,gBAAAA,MAAM,SAAS,KAAI,CAACL,UAAL,CAAgBa,IAAI,CAACR,MAArB,CAAf;AACA,oBAAIA,MAAJ,EAAY,KAAI,CAACkD,mBAAL,CAAyBlD,MAAzB,EAAiCQ,IAAjC;AACf;AACJ,aAXD,CAYA;AAZA,iBAaK;AACD,cAAA,KAAI,CAAC0C,mBAAL,CAAyB9E,SAAzB,EAAoCoC,IAApC;AACH;AAhB4D;AAiBhE;AAED;;;AACA2C,QAAAA,IAAI,GAAG;AACHxF,UAAAA,YAAY,CAACwE,MAAb,CAAoBiB,OAApB,CAA4B,CAACrE,KAAD,EAAesE,GAAf,KAA+B;AACvDX,YAAAA,OAAO,CAACC,GAAR,+BAAoB5D,KAAK,CAACwD,QAA1B,EAAsC5E,YAAY,CAACwE,MAAb,CAAoBzC,GAApB,CAAwB2D,GAAxB,CAAtC;AACH,WAFD;AAGAX,UAAAA,OAAO,CAACC,GAAR,2CAAsBhF,YAAY,CAACwE,MAAb,CAAoBmB,KAA1C;AACH;;AAxhBgC,O;;AAAxB9F,MAAAA,U,CAIMoB,S", "sourcesContent": ["import { __private, AnimationClip, Asset, AssetManager, assetManager, Atlas, AudioClip, Font, ImageAsset, js, JsonAsset, Material, Mesh, Prefab, resources, sp, Sprite, SpriteAtlas, SpriteFrame, Texture2D, warn } from \"cc\";\r\nimport { IMgr } from \"./IMgr\";\r\n\r\nexport type AssetType<T = Asset> = __private.__types_globals__Constructor<T> | null;\r\nexport type Paths = string | string[];\r\nexport type ProgressCallback = ((finished: number, total: number, item: AssetManager.RequestItem) => void) | null;\r\nexport type CompleteCallback = any;\r\nexport type IRemoteOptions = { [k: string]: any; ext?: string; } | null;\r\n\r\ninterface ILoadResArgs<T extends Asset> {\r\n    /** 资源包名 */\r\n    bundle?: string;\r\n    /** 资源文件夹名 */\r\n    dir?: string;\r\n    /** 资源路径 */\r\n    paths: Paths;\r\n    /** 资源类型 */\r\n    type: AssetType<T>;\r\n    /** 资源加载进度 */\r\n    onProgress: ProgressCallback;\r\n    /** 资源加载完成 */\r\n    onComplete: CompleteCallback;\r\n    /** 是否为预加载 */\r\n    preload?: boolean;\r\n}\r\n\r\n/** \r\n * 游戏资源管理\r\n * 1、加载默认resources文件夹中资源\r\n * 2、加载默认bundle远程资源\r\n * 3、主动传递bundle名时，优先加载传递bundle名资源包中的资源\r\n */\r\nexport class ResManager extends IMgr {\r\n    //#region 资源配置数据\r\n    /** 全局默认加载的资源包名 */\r\n    defaultBundleName: string = \"resources\";\r\n    private static _instance: ResManager;\r\n\r\n    static get instance() {\r\n        if (this._instance) {\r\n            return this._instance;\r\n        }\r\n\r\n        this._instance = new ResManager();\r\n        return this._instance;\r\n    }\r\n\r\n    /** 下载时的最大并发数 - 项目设置 -> 项目数据 -> 资源下载并发数，设置默认值；初始值为15 */\r\n    get maxConcurrency() {\r\n        return assetManager.downloader.maxConcurrency;\r\n    }\r\n    set maxConcurrency(value) {\r\n        assetManager.downloader.maxConcurrency = value;\r\n    }\r\n\r\n    /** 下载时每帧可以启动的最大请求数 - 默认值为15 */\r\n    get maxRequestsPerFrame() {\r\n        return assetManager.downloader.maxRequestsPerFrame;\r\n    }\r\n    set maxRequestsPerFrame(value) {\r\n        assetManager.downloader.maxRequestsPerFrame = value;\r\n    }\r\n\r\n    /** 失败重试次数 - 默认值为0 */\r\n    get maxRetryCount() {\r\n        return assetManager.downloader.maxRetryCount;\r\n    }\r\n    set maxRetryCount(value) {\r\n        assetManager.downloader.maxRetryCount = value;\r\n    }\r\n\r\n    /** 重试的间隔时间，单位为毫秒 - 默认值为2000毫秒 */\r\n    get retryInterval() {\r\n        return assetManager.downloader.retryInterval;\r\n    }\r\n    set retryInterval(value) {\r\n        assetManager.downloader.retryInterval = value;\r\n    }\r\n\r\n    //#region 加载远程资源\r\n    /**\r\n     * 加载远程资源\r\n     * @param url           资源地址\r\n     * @param options       资源参数，例：{ ext: \".png\" }\r\n     * @param onComplete    加载完成回调\r\n     * @example\r\n    var opt: IRemoteOptions = { ext: \".png\" };\r\n    var onComplete = (err: Error | null, data: ImageAsset) => {\r\n        const texture = new Texture2D();\r\n        texture.image = data;\r\n        \r\n        const spriteFrame = new SpriteFrame();\r\n        spriteFrame.texture = texture;\r\n        \r\n        var sprite = this.sprite.addComponent(Sprite);\r\n        sprite.spriteFrame = spriteFrame;\r\n    }\r\n    ResManager.loadRemote<ImageAsset>(this.url, opt, onComplete);\r\n     */\r\n    loadRemote<T extends Asset>(url: string, options: IRemoteOptions | null, onComplete?: CompleteCallback): void;\r\n    loadRemote<T extends Asset>(url: string, onComplete?: CompleteCallback): void;\r\n    loadRemote<T extends Asset>(url: string, ...args: any): void {\r\n        let options: IRemoteOptions | null = null;\r\n        let onComplete: CompleteCallback = null;\r\n        if (args.length == 2) {\r\n            options = args[0];\r\n            onComplete = args[1];\r\n        }\r\n        else {\r\n            onComplete = args[0];\r\n        }\r\n        assetManager.loadRemote<T>(url, options, onComplete);\r\n    }\r\n    //#endregion\r\n\r\n    //#region 资源包管理\r\n\r\n    /**\r\n     * 获取资源包\r\n     * @param name 资源包名\r\n     */\r\n    getBundle(name: string) {\r\n        return assetManager.bundles.get(name);\r\n    }\r\n\r\n    /**\r\n     * 加载资源包\r\n     * @param name       资源地址\r\n     * @example\r\n        await ResManager.loadBundle(name);\r\n     */\r\n    loadBundle(name: string): Promise<AssetManager.Bundle> {\r\n        return new Promise<AssetManager.Bundle>((resolve, reject) => {\r\n            assetManager.loadBundle(name, (err, bundle: AssetManager.Bundle) => {\r\n                if (err) {\r\n                    resolve(null!);\r\n                    return;\r\n                }\r\n                resolve(bundle);\r\n            });\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 释放资源包与包中所有资源\r\n     * @param bundleName 资源地址\r\n     */\r\n    removeBundle(bundleName: string) {\r\n        let bundle = assetManager.bundles.get(bundleName);\r\n        if (bundle) {\r\n            bundle.releaseAll();\r\n            assetManager.removeBundle(bundle);\r\n        }\r\n    }\r\n    //#endregion\r\n\r\n    //#region 预加载资源\r\n    /**\r\n     * 加载一个资源\r\n     * @param bundleName    远程包名\r\n     * @param paths         资源路径\r\n     * @param type          资源类型\r\n     * @param onProgress    加载进度回调\r\n     * @param onComplete    加载完成回调\r\n     */\r\n    preload<T extends Asset>(bundleName: string, paths: Paths, type: AssetType<T>, onProgress: ProgressCallback, onComplete: CompleteCallback): void;\r\n    preload<T extends Asset>(bundleName: string, paths: Paths, onProgress: ProgressCallback, onComplete: CompleteCallback): void;\r\n    preload<T extends Asset>(bundleName: string, paths: Paths, onComplete?: CompleteCallback): void;\r\n    preload<T extends Asset>(bundleName: string, paths: Paths, type: AssetType<T>, onComplete?: CompleteCallback): void;\r\n    preload<T extends Asset>(paths: Paths, type: AssetType<T>, onProgress: ProgressCallback, onComplete: CompleteCallback): void;\r\n    preload<T extends Asset>(paths: Paths, onProgress: ProgressCallback, onComplete: CompleteCallback): void;\r\n    preload<T extends Asset>(paths: Paths, onComplete?: CompleteCallback): void;\r\n    preload<T extends Asset>(paths: Paths, type: AssetType<T>, onComplete?: CompleteCallback): void;\r\n    preload<T extends Asset>(\r\n        bundleName: string,\r\n        paths?: Paths | AssetType<T> | ProgressCallback | CompleteCallback,\r\n        type?: AssetType<T> | ProgressCallback | CompleteCallback,\r\n        onProgress?: ProgressCallback | CompleteCallback,\r\n        onComplete?: CompleteCallback,\r\n    ) {\r\n        let args: ILoadResArgs<Asset> | null = null;\r\n        if (typeof paths === \"string\" || paths instanceof Array) {\r\n            args = this.parseLoadResArgs(paths, type, onProgress, onComplete);\r\n            args.bundle = bundleName;\r\n        }\r\n        else {\r\n            args = this.parseLoadResArgs(bundleName, paths, type, onProgress);\r\n            args.bundle = this.defaultBundleName;\r\n        }\r\n        args.preload = true;\r\n        this.loadByArgs(args);\r\n    }\r\n\r\n    /**\r\n     * 异步加载一个资源\r\n     * @param bundleName    远程包名\r\n     * @param paths         资源路径\r\n     * @param type          资源类型\r\n     */\r\n    preloadAsync<T extends Asset>(bundleName: string, paths: Paths, type: AssetType<T>): Promise<AssetManager.RequestItem>;\r\n    preloadAsync<T extends Asset>(bundleName: string, paths: Paths): Promise<AssetManager.RequestItem>;\r\n    preloadAsync<T extends Asset>(paths: Paths, type: AssetType<T>): Promise<AssetManager.RequestItem>;\r\n    preloadAsync<T extends Asset>(paths: Paths): Promise<AssetManager.RequestItem>;\r\n    preloadAsync<T extends Asset>(bundleName: string,\r\n        paths?: Paths | AssetType<T> | ProgressCallback | CompleteCallback,\r\n        type?: AssetType<T> | ProgressCallback | CompleteCallback): Promise<AssetManager.RequestItem> {\r\n        return new Promise((resolve, reject) => {\r\n            this.preload(bundleName, paths, type, (err: Error | null, data: AssetManager.RequestItem) => {\r\n                if (err) {\r\n                    warn(err.message);\r\n                }\r\n                resolve(data);\r\n            });\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 预加载文件夹中的资源\r\n     * @param bundleName    远程包名\r\n     * @param dir           文件夹名\r\n     * @param type          资源类型\r\n     * @param onProgress    加载进度回调\r\n     * @param onComplete    加载完成回调\r\n     */\r\n    preloadDir<T extends Asset>(bundleName: string, dir: string, type: AssetType<T>, onProgress: ProgressCallback, onComplete: CompleteCallback): void;\r\n    preloadDir<T extends Asset>(bundleName: string, dir: string, onProgress: ProgressCallback, onComplete: CompleteCallback): void;\r\n    preloadDir<T extends Asset>(bundleName: string, dir: string, onComplete?: CompleteCallback): void;\r\n    preloadDir<T extends Asset>(bundleName: string, dir: string, type: AssetType<T>, onComplete?: CompleteCallback): void;\r\n    preloadDir<T extends Asset>(dir: string, type: AssetType<T>, onProgress: ProgressCallback, onComplete: CompleteCallback): void;\r\n    preloadDir<T extends Asset>(dir: string, onProgress: ProgressCallback, onComplete: CompleteCallback): void;\r\n    preloadDir<T extends Asset>(dir: string, onComplete?: CompleteCallback): void;\r\n    preloadDir<T extends Asset>(dir: string, type: AssetType<T>, onComplete?: CompleteCallback): void;\r\n    preloadDir<T extends Asset>(\r\n        bundleName: string,\r\n        dir?: string | AssetType<T> | ProgressCallback | CompleteCallback,\r\n        type?: AssetType<T> | ProgressCallback | CompleteCallback,\r\n        onProgress?: ProgressCallback | CompleteCallback,\r\n        onComplete?: CompleteCallback,\r\n    ) {\r\n        let args: ILoadResArgs<T> | null = null;\r\n        if (typeof dir === \"string\") {\r\n            args = this.parseLoadResArgs(dir, type, onProgress, onComplete);\r\n            args.bundle = bundleName;\r\n        }\r\n        else {\r\n            args = this.parseLoadResArgs(bundleName, dir, type, onProgress);\r\n            args.bundle = this.defaultBundleName;\r\n        }\r\n        args.dir = args.paths as string;\r\n        args.preload = true;\r\n        this.loadByArgs(args);\r\n    }\r\n    //#endregion\r\n\r\n    //#region 资源加载、获取、释放\r\n    /**\r\n     * 加载一个资源\r\n     * @param bundleName    远程包名\r\n     * @param paths         资源路径\r\n     * @param type          资源类型\r\n     * @param onProgress    加载进度回调\r\n     * @param onComplete    加载完成回调\r\n     * @example\r\n    ResManager.load(\"spine_path\", sp.SkeletonData, (err: Error | null, sd: sp.SkeletonData) => {\r\n\r\n    });\r\n     */\r\n    load<T extends Asset>(bundleName: string, paths: Paths, type: AssetType<T>, onProgress: ProgressCallback, onComplete: CompleteCallback): void;\r\n    load<T extends Asset>(bundleName: string, paths: Paths, onProgress: ProgressCallback, onComplete: CompleteCallback): void;\r\n    load<T extends Asset>(bundleName: string, paths: Paths, onComplete?: CompleteCallback): void;\r\n    load<T extends Asset>(bundleName: string, paths: Paths, type: AssetType<T>, onComplete?: CompleteCallback): void;\r\n    load<T extends Asset>(paths: Paths, type: AssetType<T>, onProgress: ProgressCallback, onComplete: CompleteCallback): void;\r\n    load<T extends Asset>(paths: Paths, onProgress: ProgressCallback, onComplete: CompleteCallback): void;\r\n    load<T extends Asset>(paths: Paths, onComplete?: CompleteCallback): void;\r\n    load<T extends Asset>(paths: Paths, type: AssetType<T>, onComplete?: CompleteCallback): void;\r\n    load<T extends Asset>(\r\n        bundleName: string,\r\n        paths?: Paths | AssetType<T> | ProgressCallback | CompleteCallback,\r\n        type?: AssetType<T> | ProgressCallback | CompleteCallback,\r\n        onProgress?: ProgressCallback | CompleteCallback,\r\n        onComplete?: CompleteCallback,\r\n    ) {\r\n        let args: ILoadResArgs<T> | null = null;\r\n        if (typeof paths === \"string\" || paths instanceof Array) {\r\n            args = this.parseLoadResArgs(paths, type, onProgress, onComplete);\r\n            args.bundle = bundleName;\r\n        }\r\n        else {\r\n            args = this.parseLoadResArgs(bundleName, paths, type, onProgress);\r\n            args.bundle = this.defaultBundleName;\r\n        }\r\n        this.loadByArgs(args);\r\n    }\r\n\r\n    /**\r\n     * 异步加载一个资源\r\n     * @param bundleName    远程包名\r\n     * @param paths         资源路径\r\n     * @param type          资源类型\r\n     */\r\n    loadAsync<T extends Asset>(bundleName: string, paths: Paths, type: AssetType<T>): Promise<T>;\r\n    loadAsync<T extends Asset>(bundleName: string, paths: Paths): Promise<T>;\r\n    loadAsync<T extends Asset>(paths: Paths, type: AssetType<T>): Promise<T>;\r\n    loadAsync<T extends Asset>(paths: Paths): Promise<T>;\r\n    loadAsync<T extends Asset>(bundleName: string,\r\n        paths?: Paths | AssetType<T> | ProgressCallback | CompleteCallback,\r\n        type?: AssetType<T> | ProgressCallback | CompleteCallback): Promise<T> {\r\n        return new Promise((resolve, reject) => {\r\n            this.load(bundleName, paths, type, (err: Error | null, asset: T) => {\r\n                if (err) {\r\n                    warn(err.message);\r\n                }\r\n                resolve(asset);\r\n            });\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 加载文件夹中的资源\r\n     * @param bundleName    远程包名\r\n     * @param dir           文件夹名\r\n     * @param type          资源类型\r\n     * @param onProgress    加载进度回调\r\n     * @param onComplete    加载完成回调\r\n     * @example\r\n    // 加载进度事件\r\n    var onProgressCallback = (finished: number, total: number, item: any) => {\r\n        console.log(\"资源加载进度\", finished, total);\r\n    }\r\n\r\n    // 加载完成事件\r\n    var onCompleteCallback = () => {\r\n        console.log(\"资源加载完成\");\r\n    }\r\n    ResManager.loadDir(\"game\", onProgressCallback, onCompleteCallback);\r\n     */\r\n    loadDir<T extends Asset>(bundleName: string, dir: string, type: AssetType<T>, onProgress: ProgressCallback, onComplete: CompleteCallback): void;\r\n    loadDir<T extends Asset>(bundleName: string, dir: string, onProgress: ProgressCallback, onComplete: CompleteCallback): void;\r\n    loadDir<T extends Asset>(bundleName: string, dir: string, onComplete?: CompleteCallback): void;\r\n    loadDir<T extends Asset>(bundleName: string, dir: string, type: AssetType<T>, onComplete?: CompleteCallback): void;\r\n    loadDir<T extends Asset>(dir: string, type: AssetType<T>, onProgress: ProgressCallback, onComplete: CompleteCallback): void;\r\n    loadDir<T extends Asset>(dir: string, onProgress: ProgressCallback, onComplete: CompleteCallback): void;\r\n    loadDir<T extends Asset>(dir: string, onComplete?: CompleteCallback): void;\r\n    loadDir<T extends Asset>(dir: string, type: AssetType<T>, onComplete?: CompleteCallback): void;\r\n    loadDir<T extends Asset>(\r\n        bundleName: string,\r\n        dir?: string | AssetType<T> | ProgressCallback | CompleteCallback,\r\n        type?: AssetType<T> | ProgressCallback | CompleteCallback,\r\n        onProgress?: ProgressCallback | CompleteCallback,\r\n        onComplete?: CompleteCallback,\r\n    ) {\r\n        let args: ILoadResArgs<T> | null = null;\r\n        if (typeof dir === \"string\") {\r\n            args = this.parseLoadResArgs(dir, type, onProgress, onComplete);\r\n            args.bundle = bundleName;\r\n        }\r\n        else {\r\n            args = this.parseLoadResArgs(bundleName, dir, type, onProgress);\r\n            args.bundle = this.defaultBundleName;\r\n        }\r\n        args.dir = args.paths as string;\r\n        this.loadByArgs(args);\r\n    }\r\n\r\n    /**\r\n     * 通过资源相对路径释放资源\r\n     * @param path          资源路径\r\n     * @param bundleName    远程资源包名\r\n     */\r\n    release(path: string, bundleName?: string) {\r\n        if (bundleName == undefined) bundleName = this.defaultBundleName;\r\n\r\n        const bundle = assetManager.getBundle(bundleName);\r\n        if (bundle) {\r\n            const asset = bundle.get(path);\r\n            if (asset) {\r\n                this.releasePrefabtDepsRecursively(bundleName, asset);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 通过相对文件夹路径删除所有文件夹中资源\r\n     * @param path          资源文件夹路径\r\n     * @param bundleName    远程资源包名\r\n     */\r\n    releaseDir(path: string, bundleName: string = this.defaultBundleName) {\r\n        const bundle: AssetManager.Bundle | null = assetManager.getBundle(bundleName);\r\n        if (bundle) {\r\n            var infos = bundle.getDirWithPath(path);\r\n            if (infos) {\r\n                infos.map((info) => {\r\n                    this.releasePrefabtDepsRecursively(bundleName, info.uuid);\r\n                });\r\n            }\r\n\r\n            if (path == \"\" && bundleName != \"resources\") {\r\n                assetManager.removeBundle(bundle);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 获取资源路径\r\n     * @param bundleName 资源包名\r\n     * @param uuid       资源唯一编号\r\n     * @returns \r\n     */\r\n    getAssetPath(bundleName: string, uuid: string): string {\r\n        let b = this.getBundle(bundleName)!;\r\n        let info = b.getAssetInfo(uuid)!;\r\n        if (info === null) return \"\";\r\n        //@ts-ignore\r\n        return info.path;\r\n    }\r\n\r\n    /** 释放预制依赖资源 */\r\n    private releasePrefabtDepsRecursively(bundleName: string, uuid: string | Asset) {\r\n        if (uuid instanceof Asset) {\r\n            uuid.decRef();\r\n            // assetManager.releaseAsset(uuid);\r\n            // this.debugLogReleasedAsset(bundleName, uuid);\r\n        }\r\n        else {\r\n            const asset = assetManager.assets.get(uuid);\r\n            if (asset) {\r\n                asset.decRef();\r\n                // assetManager.releaseAsset(asset);\r\n                // this.debugLogReleasedAsset(bundleName, asset);\r\n            }\r\n        }\r\n    }\r\n\r\n    releaseAssetByForce(asset: Asset) {\r\n        assetManager.releaseAsset(asset);\r\n    }\r\n\r\n    private debugLogReleasedAsset(bundleName: string, asset: Asset) {\r\n        if (asset.refCount == 0) {\r\n            let path = this.getAssetPath(bundleName, asset.uuid);\r\n            let content: string = \"\";\r\n            if (asset instanceof JsonAsset) {\r\n                content = \"【释放资源】Json【路径】\" + path;\r\n            }\r\n            else if (asset instanceof Prefab) {\r\n                content = \"【释放资源】Prefab【路径】\" + path;\r\n            }\r\n            else if (asset instanceof SpriteFrame) {\r\n                content = \"【释放资源】SpriteFrame【路径】\" + path;\r\n            }\r\n            else if (asset instanceof Texture2D) {\r\n                content = \"【释放资源】Texture2D【路径】\" + path;\r\n            }\r\n            else if (asset instanceof ImageAsset) {\r\n                content = \"【释放资源】ImageAsset【路径】\" + path;\r\n            }\r\n            else if (asset instanceof AudioClip) {\r\n                content = \"【释放资源】AudioClip【路径】\" + path;\r\n            }\r\n            else if (asset instanceof AnimationClip) {\r\n                content = \"【释放资源】AnimationClip【路径】\" + path;\r\n            }\r\n            else if (asset instanceof Font) {\r\n                content = \"【释放资源】Font【路径】\" + path;\r\n            }\r\n            else if (asset instanceof Material) {\r\n                content = \"【释放资源】Material【路径】\" + path;\r\n            }\r\n            else if (asset instanceof Mesh) {\r\n                content = \"【释放资源】Mesh【路径】\" + path;\r\n            }\r\n            else if (asset instanceof sp.SkeletonData) {\r\n                content = \"【释放资源】Spine【路径】\" + path;\r\n            }\r\n            else {\r\n                content = \"【释放资源】未知【路径】\" + path;\r\n            }\r\n            console.log(content);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 获取资源\r\n     * @param path          资源路径\r\n     * @param type          资源类型\r\n     * @param bundleName    远程资源包名\r\n     */\r\n    get<T extends Asset>(path: string, type?: AssetType<T>, bundleName: string = this.defaultBundleName): T | null {\r\n        var bundle: AssetManager.Bundle = assetManager.getBundle(bundleName)!;\r\n        return bundle.get(path, type);\r\n    }\r\n    //#endregion\r\n\r\n    private parseLoadResArgs<T extends Asset>(\r\n        paths: Paths,\r\n        type?: AssetType<T> | ProgressCallback | CompleteCallback,\r\n        onProgress?: AssetType<T> | ProgressCallback | CompleteCallback,\r\n        onComplete?: ProgressCallback | CompleteCallback\r\n    ) {\r\n        let pathsOut: any = paths;\r\n        let typeOut: any = type;\r\n        let onProgressOut: any = onProgress;\r\n        let onCompleteOut: any = onComplete;\r\n        if (onComplete === undefined) {\r\n            const isValidType = js.isChildClassOf(type as AssetType, Asset);\r\n            if (onProgress) {\r\n                onCompleteOut = onProgress as CompleteCallback;\r\n                if (isValidType) {\r\n                    onProgressOut = null;\r\n                }\r\n            }\r\n            else if (onProgress === undefined && !isValidType) {\r\n                onCompleteOut = type as CompleteCallback;\r\n                onProgressOut = null;\r\n                typeOut = null;\r\n            }\r\n            if (onProgress !== undefined && !isValidType) {\r\n                onProgressOut = type as ProgressCallback;\r\n                typeOut = null;\r\n            }\r\n        }\r\n        return { paths: pathsOut, type: typeOut, onProgress: onProgressOut, onComplete: onCompleteOut };\r\n    }\r\n\r\n    private loadByBundleAndArgs<T extends Asset>(bundle: AssetManager.Bundle, args: ILoadResArgs<T>): void {\r\n        if (args.dir) {\r\n            if (args.preload) {\r\n                bundle.preloadDir(args.paths as string, args.type, args.onProgress, args.onComplete);\r\n            }\r\n            else {\r\n                bundle.loadDir(args.paths as string, args.type, args.onProgress, args.onComplete);\r\n            }\r\n        }\r\n        else {\r\n            if (args.preload) {\r\n                bundle.preload(args.paths as any, args.type, args.onProgress, args.onComplete);\r\n            }\r\n            else {\r\n                bundle.load(args.paths as any, args.type, args.onProgress, args.onComplete);\r\n            }\r\n        }\r\n    }\r\n\r\n    private async loadByArgs<T extends Asset>(args: ILoadResArgs<T>) {\r\n        if (args.bundle) {\r\n            let bundle = assetManager.bundles.get(args.bundle);\r\n            // 获取缓存中的资源包\r\n            if (bundle) {\r\n                this.loadByBundleAndArgs(bundle, args);\r\n            }\r\n            // 自动加载资源包\r\n            else {\r\n                bundle = await this.loadBundle(args.bundle);\r\n                if (bundle) this.loadByBundleAndArgs(bundle, args);\r\n            }\r\n        }\r\n        // 默认资源包\r\n        else {\r\n            this.loadByBundleAndArgs(resources, args);\r\n        }\r\n    }\r\n\r\n    /** 打印缓存中所有资源信息 */\r\n    dump() {\r\n        assetManager.assets.forEach((value: Asset, key: string) => {\r\n            console.log(`引用数量:${value.refCount}`, assetManager.assets.get(key));\r\n        })\r\n        console.log(`当前资源总数:${assetManager.assets.count}`);\r\n    }\r\n}"]}
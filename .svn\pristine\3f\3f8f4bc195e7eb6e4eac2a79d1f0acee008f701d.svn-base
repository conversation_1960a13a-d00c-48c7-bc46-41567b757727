import { EventActionBase } from "db://assets/bundles/common/script/game/eventgroup/IEventAction";
import { EnemyEventGroupContext } from "../EventGroupCom";

class EnemyActionBase extends EventActionBase<EnemyEventGroupContext> {
}

export class EnemyAction_Speed extends EnemyActionBase {
    protected resetStartValue(context: EnemyEventGroupContext): void {
        
    }

    protected onExecuteInternal(context: EnemyEventGroupContext, value: number): void {
        
    }
}

export class EnemyAction_SpeedAngle extends EnemyActionBase {
    protected resetStartValue(context: EnemyEventGroupContext): void {
        
    }

    protected onExecuteInternal(context: EnemyEventGroupContext, value: number): void {
        
    }
}

export class EnemyAction_Acceleration extends EnemyActionBase {
    protected resetStartValue(context: EnemyEventGroupContext): void {
        
    }

    protected onExecuteInternal(context: EnemyEventGroupContext, value: number): void {
        
    }
}

export class EnemyAction_AccelerationAngle extends EnemyActionBase {
    protected resetStartValue(context: EnemyEventGroupContext): void {
        
    }

    protected onExecuteInternal(context: EnemyEventGroupContext, value: number): void {
        
    }
}

export class EnemyAction_Color_R extends EnemyActionBase {
    protected resetStartValue(context: EnemyEventGroupContext): void {
        
    }

    protected onExecuteInternal(context: EnemyEventGroupContext, value: number): void {
        
    }
}

export class EnemyAction_Color_G extends EnemyActionBase {
    protected resetStartValue(context: EnemyEventGroupContext): void {
        
    }

    protected onExecuteInternal(context: EnemyEventGroupContext, value: number): void {
        
    }
}

export class EnemyAction_Color_B extends EnemyActionBase {
    protected resetStartValue(context: EnemyEventGroupContext): void {
        
    }

    protected onExecuteInternal(context: EnemyEventGroupContext, value: number): void {
        
    }
}

export class EnemyAction_Scale_X extends EnemyActionBase {
    protected resetStartValue(context: EnemyEventGroupContext): void {
        
    }

    protected onExecuteInternal(context: EnemyEventGroupContext, value: number): void {
        
    }
}

export class EnemyAction_Scale_Y extends EnemyActionBase {
    protected resetStartValue(context: EnemyEventGroupContext): void {
        
    }

    protected onExecuteInternal(context: EnemyEventGroupContext, value: number): void {
        
    }
}

export class EnemyAction_TiltSpeed extends EnemyActionBase {
    protected resetStartValue(context: EnemyEventGroupContext): void {
        
    }

    protected onExecuteInternal(context: EnemyEventGroupContext, value: number): void {
        
    }
}

export class EnemyAction_TiltOffset extends EnemyActionBase {
    protected resetStartValue(context: EnemyEventGroupContext): void {
        
    }

    protected onExecuteInternal(context: EnemyEventGroupContext, value: number): void {
        
    }
}

export class EnemyAction_RotateSpeed extends EnemyActionBase {
    protected resetStartValue(context: EnemyEventGroupContext): void {
        
    }

    protected onExecuteInternal(context: EnemyEventGroupContext, value: number): void {
        
    }
}

export class EnemyAction_SelectLevel extends EnemyActionBase {
    protected resetStartValue(context: EnemyEventGroupContext): void {
        
    }

    protected onExecuteInternal(context: EnemyEventGroupContext, value: number): void {
        
    }
}

export class EnemyAction_ImmuneBulletDamage extends EnemyActionBase {
    protected resetStartValue(context: EnemyEventGroupContext): void {
        
    }

    protected onExecuteInternal(context: EnemyEventGroupContext, value: number): void {
        
    }
}

export class EnemyAction_ImmuneCollideDamage extends EnemyActionBase {
    protected resetStartValue(context: EnemyEventGroupContext): void {
        
    }

    protected onExecuteInternal(context: EnemyEventGroupContext, value: number): void {
        
    }
}

export class EnemyAction_IgnoreBullet extends EnemyActionBase {
    protected resetStartValue(context: EnemyEventGroupContext): void {
        
    }

    protected onExecuteInternal(context: EnemyEventGroupContext, value: number): void {
        
    }
}

export class EnemyAction_IgnoreCollide extends EnemyActionBase {
    protected resetStartValue(context: EnemyEventGroupContext): void {
        
    }

    protected onExecuteInternal(context: EnemyEventGroupContext, value: number): void {
        
    }
}

export class EnemyAction_ImmuneNuke extends EnemyActionBase {
    protected resetStartValue(context: EnemyEventGroupContext): void {
        
    }

    protected onExecuteInternal(context: EnemyEventGroupContext, value: number): void {
        
    }
}

export class EnemyAction_ImmuneActiveSkill extends EnemyActionBase {
    protected resetStartValue(context: EnemyEventGroupContext): void {
        
    }

    protected onExecuteInternal(context: EnemyEventGroupContext, value: number): void {
        
    }
}

export class EnemyAction_Invincible extends EnemyActionBase {
    protected resetStartValue(context: EnemyEventGroupContext): void {
        
    }

    protected onExecuteInternal(context: EnemyEventGroupContext, value: number): void {
        
    }
}
{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/move/DefaultMove.ts"], "names": ["_decorator", "misc", "MoveBase", "eOrientationType", "degreesToRadians", "radiansToDegrees", "ccclass", "property", "executeInEditMode", "<PERSON><PERSON>ult<PERSON>ove", "_trackingTimeLimit", "_trackingAngleLimit", "_targetingDelegate", "_target", "isTrackingTarget", "target", "tick", "dt", "_isMovable", "updateTracking", "angleRadians", "speedAngle", "velocityX", "speed", "Math", "cos", "velocityY", "sin", "acceleration", "accelerationRadians", "accelerationAngle", "accelerationX", "accelerationY", "sqrt", "atan2", "_basePosition", "x", "y", "_position", "set", "updateTilting", "node", "setPosition", "checkVisibility", "updateOrientation", "getDesiredOrientation", "orientationType", "Path", "Target", "targetPos", "position", "currentPos", "Fixed", "orientationParam", "Rotate", "orientation", "setRotationFromEuler", "forwardOrientation", "isDead", "directionX", "directionY", "distanceSqr", "desiredAngle", "angleDiff", "normalizedAngleDiff", "abs", "trackingStrength", "maxTurnRate", "turnSpeed", "turnAmount", "min", "sign", "setTargetingDelegate", "func", "stopTracking", "setTrackingDuration", "duration", "setTrackingMaxTurnAngle", "angle", "setMovable", "movable", "getPosition"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAA6BC,MAAAA,I,OAAAA,I;;AACnBC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,gB,iBAAAA,gB;;;;;;;;;OAEvB;AAAEC,QAAAA,gBAAF;AAAoBC,QAAAA;AAApB,O,GAAyCJ,I;OACzC;AAAEK,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CR,U;;6BAMpCS,W,WAFZH,OAAO,CAAC,aAAD,C,gBACPE,iB,UADD,MAEaC,WAFb;AAAA;AAAA,gCAE0C;AAAA;AAAA;AAEtC;AAFsC,eAG5BC,kBAH4B,GAGC,CAHD;AAGU;AAHV,eAI5BC,mBAJ4B,GAIE,GAJF;AAIU;AAJV,eAK5BC,kBAL4B,GAKmB,IALnB;AAK0B;AAL1B,eAM5BC,OAN4B,GAMA,IANA;AAAA;;AACX,YAAhBC,gBAAgB,GAAG;AAAE,iBAAO,KAAKJ,kBAAL,GAA0B,CAAjC;AAAqC;;AAKd;AACtC,YAANK,MAAM,GAAG;AAAE,iBAAO,KAAKF,OAAZ;AAAsB;;AAErCG,QAAAA,IAAI,CAACC,EAAD,EAAmB;AAC1B,cAAI,CAAC,KAAKC,UAAV,EAAsB,OADI,CAG1B;;AACA,cAAI,KAAKJ,gBAAT,EAA2B;AACvB,iBAAKK,cAAL,CAAoBF,EAApB;AACH;;AAED,cAAMG,YAAY,GAAGhB,gBAAgB,CAAC,KAAKiB,UAAN,CAArC,CAR0B,CAS1B;;AACA,cAAIC,SAAS,GAAG,KAAKC,KAAL,GAAaC,IAAI,CAACC,GAAL,CAASL,YAAT,CAA7B;AACA,cAAIM,SAAS,GAAG,KAAKH,KAAL,GAAaC,IAAI,CAACG,GAAL,CAASP,YAAT,CAA7B,CAX0B,CAa1B;;AACA,cAAI,KAAKQ,YAAL,KAAsB,CAA1B,EAA6B;AACzB,gBAAMC,mBAAmB,GAAGzB,gBAAgB,CAAC,KAAK0B,iBAAN,CAA5C;AACA,gBAAMC,aAAa,GAAG,KAAKH,YAAL,GAAoBJ,IAAI,CAACC,GAAL,CAASI,mBAAT,CAA1C;AACA,gBAAMG,aAAa,GAAG,KAAKJ,YAAL,GAAoBJ,IAAI,CAACG,GAAL,CAASE,mBAAT,CAA1C,CAHyB,CAIzB;;AACAP,YAAAA,SAAS,IAAIS,aAAa,GAAGd,EAA7B;AACAS,YAAAA,SAAS,IAAIM,aAAa,GAAGf,EAA7B;AACH,WArByB,CAuB1B;;;AACA,eAAKM,KAAL,GAAaC,IAAI,CAACS,IAAL,CAAUX,SAAS,GAAGA,SAAZ,GAAwBI,SAAS,GAAGA,SAA9C,CAAb;AACA,eAAKL,UAAL,GAAkBhB,gBAAgB,CAACmB,IAAI,CAACU,KAAL,CAAWR,SAAX,EAAsBJ,SAAtB,CAAD,CAAlC,CAzB0B,CA2B1B;;AACA,cAAIA,SAAS,KAAK,CAAd,IAAmBI,SAAS,KAAK,CAArC,EAAwC;AACpC;AACA,iBAAKS,aAAL,CAAmBC,CAAnB,IAAwBd,SAAS,GAAGL,EAApC;AACA,iBAAKkB,aAAL,CAAmBE,CAAnB,IAAwBX,SAAS,GAAGT,EAApC,CAHoC,CAKpC;;AACA,iBAAKqB,SAAL,CAAeC,GAAf,CAAmB,KAAKJ,aAAxB;;AACA,iBAAKK,aAAL,CAAmBpC,gBAAgB,CAAC,KAAKiB,UAAN,CAAnC,EAAsDJ,EAAtD,EAA0D,KAAKqB,SAA/D;AAEA,iBAAKG,IAAL,CAAUC,WAAV,CAAsB,KAAKJ,SAA3B;AACA,iBAAKK,eAAL;AAEA,iBAAKC,iBAAL,CAAuB3B,EAAvB;AACH;AACJ;;AAES4B,QAAAA,qBAAqB,CAAC5B,EAAD,EAAqB;AAChD,kBAAQ,KAAK6B,eAAb;AACI,iBAAK;AAAA;AAAA,sDAAiBC,IAAtB;AACI,qBAAO,KAAK1B,UAAZ;;AACJ,iBAAK;AAAA;AAAA,sDAAiB2B,MAAtB;AACI,kBAAI,KAAKjC,MAAT,EAAiB;AACb,oBAAMkC,SAAS,GAAG,KAAKlC,MAAL,CAAY0B,IAAZ,CAAiBS,QAAnC;AACA,oBAAMC,UAAU,GAAG,KAAKhB,aAAxB;AACA,uBAAO9B,gBAAgB,CAACmB,IAAI,CAACU,KAAL,CAAWe,SAAS,CAACZ,CAAV,GAAcc,UAAU,CAACd,CAApC,EAAuCY,SAAS,CAACb,CAAV,GAAce,UAAU,CAACf,CAAhE,CAAD,CAAvB;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,sDAAiBgB,KAAtB;AACI,qBAAO,KAAKC,gBAAZ;;AACJ,iBAAK;AAAA;AAAA,sDAAiBC,MAAtB;AACI,qBAAO,KAAKC,WAAL,GAAmB,KAAKF,gBAAL,GAAwBpC,EAAlD;AAbR;;AAeA,iBAAO,KAAKsC,WAAZ;AACH;;AAESX,QAAAA,iBAAiB,CAAC3B,EAAD,EAAmB;AAC1C,eAAKsC,WAAL,GAAmB,KAAKV,qBAAL,CAA2B5B,EAA3B,CAAnB;AACA,eAAKwB,IAAL,CAAUe,oBAAV,CAA+B,CAA/B,EAAkC,CAAlC,EAAqC,KAAKD,WAAL,GAAmB,KAAKE,kBAA7D;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACctC,QAAAA,cAAc,CAACF,EAAD,EAAmB;AACvC,cAAI,CAAC,KAAKJ,OAAN,IAAiB,KAAKA,OAAL,CAAa6C,MAAlC,EACA;AACI,iBAAK7C,OAAL,GAAe,KAAKD,kBAAL,GAA0B,KAAKA,kBAAL,EAA1B,GAAsD,IAArE,CADJ,CAEI;;AACA,gBAAI,CAAC,KAAKC,OAAV,EAAmB;AACtB,WANsC,CAQvC;;;AACA,cAAI,KAAKH,kBAAL,GAA0B,CAA9B,EAAiC;AAC7B,iBAAKA,kBAAL,IAA2BO,EAAE,GAAG,IAAhC,CAD6B,CACS;;AACtC,gBAAI,KAAKP,kBAAL,IAA2B,CAA/B,EAAkC;AAC9B;AACH;AACJ;;AAED,cAAMuC,SAAS,GAAG,KAAKpC,OAAL,CAAa4B,IAAb,CAAkBS,QAApC;AACA,cAAMC,UAAU,GAAG,KAAKb,SAAxB,CAjBuC,CAmBvC;;AACA,cAAMqB,UAAU,GAAGV,SAAS,CAACb,CAAV,GAAce,UAAU,CAACf,CAA5C;AACA,cAAMwB,UAAU,GAAGX,SAAS,CAACZ,CAAV,GAAcc,UAAU,CAACd,CAA5C;AACA,cAAMwB,WAAW,GAAGF,UAAU,GAAGA,UAAb,GAA0BC,UAAU,GAAGA,UAA3D;;AAEA,cAAIC,WAAW,GAAG,CAAlB,EAAqB;AACjB;AACA,gBAAMC,YAAY,GAAGzD,gBAAgB,CAACmB,IAAI,CAACU,KAAL,CAAW0B,UAAX,EAAuBD,UAAvB,CAAD,CAArC,CAFiB,CAIjB;;AACA,gBAAMI,SAAS,GAAGD,YAAY,GAAG,KAAKzC,UAAtC,CALiB,CAMjB;;AACA,gBAAM2C,mBAAmB,GAAI,CAACD,SAAS,GAAG,GAAb,IAAoB,GAArB,GAA4B,GAAxD,CAPiB,CASjB;;AACA,gBAAIvC,IAAI,CAACyC,GAAL,CAASD,mBAAT,IAAgC,KAAKrD,mBAAzC,EAA8D;AAC1D;AACH,aAZgB,CAcjB;;;AACA,gBAAMuD,gBAAgB,GAAG,GAAzB,CAfiB,CAea;;AAC9B,gBAAMC,WAAW,GAAG,KAAKC,SAAzB,CAhBiB,CAgBmB;;AACpC,gBAAMC,UAAU,GAAG7C,IAAI,CAAC8C,GAAL,CAAS9C,IAAI,CAACyC,GAAL,CAASD,mBAAT,CAAT,EAAwCG,WAAW,GAAGlD,EAAtD,IAA4DO,IAAI,CAAC+C,IAAL,CAAUP,mBAAV,CAA/E;AAEA,iBAAK3C,UAAL,IAAmBgD,UAAU,GAAGH,gBAAhC;AACH;AACJ;AAED;AACJ;AACA;AACI;AACA;AACA;AACA;;;AACOM,QAAAA,oBAAoB,CAACC,IAAD,EAA8C;AACrE,eAAK7D,kBAAL,GAA0B6D,IAA1B;AACA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACWC,QAAAA,YAAY,GAAS;AACxB,eAAKhE,kBAAL,GAA0B,CAA1B;AACH;AAED;AACJ;AACA;AACA;;;AACWiE,QAAAA,mBAAmB,CAACC,QAAD,EAAgC;AACtD,eAAKlE,kBAAL,GAA0BkE,QAA1B;AACA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;AACA;;;AACWC,QAAAA,uBAAuB,CAACC,KAAD,EAA6B;AACvD,eAAKnE,mBAAL,GAA2BmE,KAA3B;AACA,iBAAO,IAAP;AACH;;AAEMC,QAAAA,UAAU,CAACC,OAAD,EAAgC;AAC7C,eAAK9D,UAAL,GAAkB8D,OAAlB;;AAEA,cAAI,KAAK9D,UAAT,EAAqB;AACjB;AACA,iBAAKuB,IAAL,CAAUwC,WAAV,CAAsB,KAAK9C,aAA3B,EAFiB,CAGjB;AACA;AACH;;AAED,iBAAO,IAAP;AACH;;AAjLqC,O", "sourcesContent": ["import { _decorator, Component, Enum, misc, Node, UITransform, Vec2, Vec3 } from 'cc';\r\nimport { IMovable, MoveBase, eOrientationType } from './IMovable';\r\nimport PlaneBase from '../ui/plane/PlaneBase';\r\nconst { degreesToRadians, radiansToDegrees } = misc;\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\nexport type TargetingDelegate = () => PlaneBase | null;\r\n\r\n@ccclass('DefaultMove')\r\n@executeInEditMode\r\nexport class DefaultMove extends MoveBase {\r\n    public get isTrackingTarget() { return this._trackingTimeLimit > 0; }\r\n    // 追踪配置\r\n    protected _trackingTimeLimit: number = 0;       // 追踪持续时间（毫秒，0表示无限制）\r\n    protected _trackingAngleLimit: number = 180;    // 最大转向角度（度，180表示可以完全掉头）\r\n    protected _targetingDelegate: TargetingDelegate | null = null;  // 选择目标委托\r\n    protected _target: PlaneBase | null = null;            // 追踪的目标节点\r\n    public get target() { return this._target; }\r\n\r\n    public tick(dt: number): void {\r\n        if (!this._isMovable) return;\r\n\r\n        // 处理追踪逻辑\r\n        if (this.isTrackingTarget) {\r\n            this.updateTracking(dt);\r\n        }\r\n\r\n        const angleRadians = degreesToRadians(this.speedAngle);\r\n        // Convert speed and angle to velocity vector\r\n        let velocityX = this.speed * Math.cos(angleRadians);\r\n        let velocityY = this.speed * Math.sin(angleRadians);\r\n\r\n        // Convert acceleration and angle to acceleration vector\r\n        if (this.acceleration !== 0) {\r\n            const accelerationRadians = degreesToRadians(this.accelerationAngle);\r\n            const accelerationX = this.acceleration * Math.cos(accelerationRadians);\r\n            const accelerationY = this.acceleration * Math.sin(accelerationRadians);\r\n            // Update velocity vector: v = v + a * dt\r\n            velocityX += accelerationX * dt;\r\n            velocityY += accelerationY * dt;\r\n        }\r\n\r\n        // Convert back to speed and angle\r\n        this.speed = Math.sqrt(velocityX * velocityX + velocityY * velocityY);\r\n        this.speedAngle = radiansToDegrees(Math.atan2(velocityY, velocityX));\r\n\r\n        // Update position: p = p + v * dt\r\n        if (velocityX !== 0 || velocityY !== 0) {\r\n            // Update base position (main movement path)\r\n            this._basePosition.x += velocityX * dt;\r\n            this._basePosition.y += velocityY * dt;\r\n\r\n            // Start with base position\r\n            this._position.set(this._basePosition);\r\n            this.updateTilting(degreesToRadians(this.speedAngle), dt, this._position);\r\n\r\n            this.node.setPosition(this._position);\r\n            this.checkVisibility();\r\n\r\n            this.updateOrientation(dt);\r\n        }\r\n    }\r\n\r\n    protected getDesiredOrientation(dt: number): number {\r\n        switch (this.orientationType) {\r\n            case eOrientationType.Path:\r\n                return this.speedAngle;\r\n            case eOrientationType.Target:\r\n                if (this.target) {\r\n                    const targetPos = this.target.node.position;\r\n                    const currentPos = this._basePosition;\r\n                    return radiansToDegrees(Math.atan2(targetPos.y - currentPos.y, targetPos.x - currentPos.x));\r\n                }\r\n                break;\r\n            case eOrientationType.Fixed:\r\n                return this.orientationParam;\r\n            case eOrientationType.Rotate:\r\n                return this.orientation + this.orientationParam * dt;\r\n        }\r\n        return this.orientation;\r\n    }\r\n\r\n    protected updateOrientation(dt: number): void {\r\n        this.orientation = this.getDesiredOrientation(dt);\r\n        this.node.setRotationFromEuler(0, 0, this.orientation + this.forwardOrientation);\r\n    }\r\n\r\n    /**\r\n     * 更新追踪逻辑\r\n     * @param dt 时间增量（秒）\r\n     * @returns 追踪结果，包含新的速度向量，如果停止追踪则返回null\r\n     */\r\n    protected updateTracking(dt: number): void {\r\n        if (!this._target || this._target.isDead) \r\n        {\r\n            this._target = this._targetingDelegate ? this._targetingDelegate() : null;\r\n            // still null\r\n            if (!this._target) return;\r\n        }\r\n\r\n        // 检查是否应该停止追踪\r\n        if (this._trackingTimeLimit > 0) {\r\n            this._trackingTimeLimit -= dt * 1000; // dt是秒，转换为毫秒\r\n            if (this._trackingTimeLimit <= 0) {\r\n                return;\r\n            }\r\n        }\r\n\r\n        const targetPos = this._target.node.position;\r\n        const currentPos = this._position;\r\n\r\n        // Calculate direction to target\r\n        const directionX = targetPos.x - currentPos.x;\r\n        const directionY = targetPos.y - currentPos.y;\r\n        const distanceSqr = directionX * directionX + directionY * directionY;\r\n\r\n        if (distanceSqr > 0) {\r\n            // Calculate desired angle to target\r\n            const desiredAngle = radiansToDegrees(Math.atan2(directionY, directionX));\r\n\r\n            // Smoothly adjust speedAngle toward target\r\n            const angleDiff = desiredAngle - this.speedAngle;\r\n            // Normalize angle difference to [-180, 180] range\r\n            const normalizedAngleDiff = ((angleDiff + 180) % 360) - 180;\r\n\r\n            // 检查转向角度限制\r\n            if (Math.abs(normalizedAngleDiff) > this._trackingAngleLimit) {\r\n                return;\r\n            }\r\n\r\n            // Apply tracking adjustment\r\n            const trackingStrength = 1.0; // Can be made configurable\r\n            const maxTurnRate = this.turnSpeed; // degrees per second\r\n            const turnAmount = Math.min(Math.abs(normalizedAngleDiff), maxTurnRate * dt) * Math.sign(normalizedAngleDiff);\r\n\r\n            this.speedAngle += turnAmount * trackingStrength;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Set the target\r\n     */\r\n    // public setTarget(target: Entity | null): DefaultMove {\r\n    //     this._target = target ? target.node : null;\r\n    //     return this;\r\n    // }\r\n    public setTargetingDelegate(func: TargetingDelegate | null): DefaultMove {\r\n        this._targetingDelegate = func;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set whether to track the target\r\n     */\r\n    public stopTracking(): void {\r\n        this._trackingTimeLimit = 0;\r\n    }\r\n\r\n    /**\r\n     * 设置追踪持续时间\r\n     * @param duration 持续时间（毫秒）\r\n     */\r\n    public setTrackingDuration(duration: number): DefaultMove {\r\n        this._trackingTimeLimit = duration;\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * 设置最大转向角度\r\n     * @param angle 最大角度（度）\r\n     */\r\n    public setTrackingMaxTurnAngle(angle: number): DefaultMove {\r\n        this._trackingAngleLimit = angle;\r\n        return this;\r\n    }\r\n\r\n    public setMovable(movable: boolean): DefaultMove {\r\n        this._isMovable = movable;\r\n\r\n        if (this._isMovable) {\r\n            // Initialize base position to current node position\r\n            this.node.getPosition(this._basePosition);\r\n            // 重置可见性状态\r\n            // this._wasVisible = this._isVisible = false;\r\n        }\r\n\r\n        return this;\r\n    }\r\n}"]}
import { _decorator, Component, Sprite, UITransform, Vec2 } from 'cc';
import { LevelDataTerrain } from "db://assets/bundles/common/script/leveldata/leveldata";
const { ccclass, menu, executeInEditMode } = _decorator;

@ccclass('LevelEditorPrefabParse')
@executeInEditMode()
@menu('地形系统/预制体导出Json')
export class LevelEditorPrefabParse extends Component {

    protected onLoad(): void {
        //console.log("LevelEditorPrefabParse onLoad");
    }

    protected onDestroy(): void {
        //console.log("LevelEditorPrefabParse onDestroy");
    }

    protected onDisable(): void {
        // @ts-ignore  
        console.log("LevelEditorPrefabParse begin - 预制体: ", this.node._prefab!.asset?.name, "uuid:", this.node._prefab!.asset?.uuid);
        let terrains: LevelDataTerrain[] = [];
        this.node.children.forEach((nodeChild) => {
            let type = '';
            // 获取子节点的预制体信息
            // @ts-ignore
            const childPrefab = nodeChild._prefab;
            
            // 检查子节点是否是独立的预制体实例
            // @ts-ignore
            const isChildPrefabInstance = childPrefab && childPrefab.asset && (childPrefab.asset.uuid !== this.node._prefab?.asset?.uuid);
            
            if (!isChildPrefabInstance) {
                // 不是独立的预制体实例（是父预制体的一部分）
                console.log("该节点不是独立的预制体:", nodeChild.name);
                const com = nodeChild.getComponent(Sprite);
                
                if (com && com.spriteFrame) {
                    type = `${com.spriteFrame.uuid}.png`;
                    console.log("sprite uuid:", type);
                }
            }
            
            if (nodeChild.getComponent(UITransform) === null) {
                // @ts-ignore  
                console.error("LevelEditorPrefabParse", `预制体：${this.node._prefab!.asset?.name}有节点丢失，需要修正`);
            }
            terrains.push({
                // @ts-ignore
                uuid: nodeChild._prefab.asset.uuid,
                type: type,
                height: nodeChild.getComponent(UITransform)!.contentSize.height,
                position: new Vec2(nodeChild.position.x, nodeChild.position.y),
                scale: new Vec2(nodeChild.scale.x, nodeChild.scale.y),  
                rotation: nodeChild.rotation.z,
            });
        });
        console.log("LevelEditorPrefabParse end - 预制体: ", this.node.name);

        const data = {
            terrains: terrains
        };

        this._exportDataAsJson(data);
    }

    /**
     * 将数据导出为JSON文件
     * @param data 要导出的数据
     */
    private async _exportDataAsJson(data: { terrains: LevelDataTerrain[] }) {
        const jsonData = JSON.stringify(data, null, 2);
        
        // @ts-ignore
        const fileName = `${this.node._prefab?.asset?.name}.json`;
        const assetPath = `db://assets/resources/game/level/background/Prefab/Config/${fileName}`;
        
        //console.log("LevelPrefabParse _exportDataAsJson", assetPath);
        // @ts-ignore
        const sourceAssetInfo = await Editor.Message.request('asset-db', 'query-asset-info', assetPath);
        if (sourceAssetInfo === null) {
            //console.error('查询资源信息失败:');
            this._createAsset(assetPath, jsonData);
        } else {
            //console.log('导出预制体配置信息:', sourceAssetInfo);
            this._saveAsset(sourceAssetInfo!.uuid, jsonData);
        }
    } 

    /**
     * 创建新资源
     */
    private async _createAsset(assetPath: string, jsonData: string) {
        // @ts-ignore
        var createRsp = await Editor.Message.request('asset-db', 'create-asset', assetPath, jsonData);
    }
    
    /**
     * 更新现有资源
     */
    private async _saveAsset(uuid: string, jsonData: string) {
        // @ts-ignore
        const rsp = await Editor.Message.send('asset-db', 'save-asset', uuid, jsonData);
        
        this._refreshAssetDb();
    }
    
    /**
     * 刷新资源数据库
     */
    private async _refreshAssetDb() {
        // @ts-ignore
        Editor.Message.send('asset-db', 'refresh-asset', `db://assets/resources/game/level/background/Prefab/Config`);
    }
}


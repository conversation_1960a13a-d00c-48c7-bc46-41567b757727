import { EventConditionBase, Comparer, eCompareOp } from "db://assets/bundles/common/script/game/eventgroup/IEventCondition";
import { EventGroupContext } from "../EventGroup";

export class BulletConditionBase extends EventConditionBase<EventGroupContext> {
}

export class BulletCondition_Duration extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        // Custom evaluation logic for active condition
        return Comparer.compare(context.bullet!.prop.duration.value, this._targetValue, this.data.compareOp);
    }
}

export class BulletCondition_ElapsedTime extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        return Comparer.compare(context.bullet!.elapsedTime, this._targetValue, this.data.compareOp);
    }
}

export class BulletCondition_PosX extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        return Comparer.compare(context.bullet!.node.position.x, this._targetValue, this.data.compareOp);
    }
}

export class BulletCondition_PosY extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        return Comparer.compare(context.bullet!.node.position.y, this._targetValue, this.data.compareOp);
    }
}

export class BulletCondition_Damage extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        // return Comparer.compare(context.bullet!.damage.value, this._targetValue, this.data.compareOp);
        return false;
    }
}

export class BulletCondition_Speed extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        return Comparer.compare(context.bullet!.prop.speed.value, this._targetValue, this.data.compareOp);
    }
}

export class BulletCondition_SpeedAngle extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        return Comparer.compare(context.bullet!.prop.speedAngle.value, this._targetValue, this.data.compareOp);
    }
}

export class BulletCondition_Acceleration extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        return Comparer.compare(context.bullet!.prop.acceleration.value, this._targetValue, this.data.compareOp);
    }
}

export class BulletCondition_AccelerationAngle extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        return Comparer.compare(context.bullet!.prop.accelerationAngle.value, this._targetValue, this.data.compareOp);
    }
}

export class BulletCondition_Scale extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        return Comparer.compare(context.bullet!.node.scale.x, this._targetValue, this.data.compareOp);
    }
}

export class BulletCondition_ColorR extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        return Comparer.compare(context.bullet!.prop.color.value.r, this._targetValue, this.data.compareOp);
    }
}

export class BulletCondition_ColorG extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        return Comparer.compare(context.bullet!.prop.color.value.g, this._targetValue, this.data.compareOp);
    }
}

export class BulletCondition_ColorB extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        return Comparer.compare(context.bullet!.prop.color.value.b, this._targetValue, this.data.compareOp);
    }
}

export class BulletCondition_Orientation extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {        
        return Comparer.compare(context.bullet!.prop.orientation.value, this._targetValue, this.data.compareOp);
    }
}

export class BulletCondition_OrientationParam extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {        
        return Comparer.compare(context.bullet!.prop.orientationParam.value, this._targetValue, this.data.compareOp);
    }
}

export class BulletCondition_TrackingTarget extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        switch (this.data.compareOp) {
            case eCompareOp.Equal:
                if (this._targetValue === 1) {
                    return context.bullet!.prop.maxTrackingDuration.value > 0 ? true : false;
                } else {
                    return context.bullet!.prop.maxTrackingDuration.value <= 0 ? true : false;
                }
            case eCompareOp.NotEqual:
                if (this._targetValue === 1) {
                    return context.bullet!.prop.maxTrackingDuration.value <= 0 ? true : false;
                } else {
                    return context.bullet!.prop.maxTrackingDuration.value > 0 ? true : false;
                }
            default:
                return false;
        }
    }
}

export class BulletCondition_Destructive extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        switch (this.data.compareOp) {
            case eCompareOp.Equal:
                return context.bullet!.prop.isDestructive.value === (this._targetValue === 1) ? true : false;
            case eCompareOp.NotEqual:
                return context.bullet!.prop.isDestructive.value !== (this._targetValue === 1) ? true : false;
            default:
                return false;
        }
    }
}

export class BulletCondition_DestructiveOnHit extends BulletConditionBase {
    public evaluate(context: EventGroupContext): boolean {
        switch (this.data.compareOp) {
            case eCompareOp.Equal:
                return context.bullet!.prop.isDestructiveOnHit.value === (this._targetValue === 1) ? true : false;
            case eCompareOp.NotEqual:
                return context.bullet!.prop.isDestructiveOnHit.value !== (this._targetValue === 1) ? true : false;
            default:
                return false;
        }
    }
}

export class BulletCondition_DistanceToPlayer extends BulletConditionBase {
    public onLoad(context: EventGroupContext): void {
        super.onLoad(context);
        // 转成距离平方
        this._targetValue = this._targetValue * this._targetValue;
    }
    public evaluate(context: EventGroupContext): boolean {
        return Comparer.compare(context.bullet!.getSquaredDistanceToPlayer(), this._targetValue, this.data.compareOp);
    }
}
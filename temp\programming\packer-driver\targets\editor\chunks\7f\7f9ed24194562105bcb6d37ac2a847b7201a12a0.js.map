{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/plane/PlaneEditor.ts"], "names": ["_decorator", "Component", "Prefab", "Enum", "Rect", "JsonAsset", "EnemyEnum", "PlaneBase", "EnemyPlane", "EnemyData", "MyApp", "PathData", "BundleName", "BulletSystem", "ResManager", "GameConst", "ccclass", "property", "executeInEditMode", "PlaneEditor", "type", "displayName", "_pathData", "_planeList", "onLoad", "GetInstance", "init", "pathAsset", "fromJSON", "json", "planeMgr", "load", "instance", "loadBundle", "<PERSON><PERSON>", "lubanMgr", "ViewBattleWidth", "ViewHeight", "createEnemyPlane", "enemyPlaneID", "then", "plane", "onEnemyCreated", "catch", "error", "console", "update", "deltaTime", "tick", "for<PERSON>ach", "updateGameLogic", "id", "Promise", "resolve", "reject", "enemyData", "prefab", "resMgr", "loadAsync", "recoursePrefab", "enemyPlane", "initPlane", "initMove", "moveCom", "speed", "initPath", "push"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,S,OAAAA,S;;AACjDC,MAAAA,S,iBAAAA,S;;AACFC,MAAAA,S;;AACAC,MAAAA,U;;AACEC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,kBAAAA,U;;AACAC,MAAAA,S,kBAAAA,S;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2ClB,U;;6BAIpCmB,W,WAFZH,OAAO,CAAC,aAAD,C,UAGHC,QAAQ,CAAC;AAACG,QAAAA,IAAI,EAAEjB,IAAI;AAAA;AAAA,mCAAX;AAAwBkB,QAAAA,WAAW,EAAE;AAArC,OAAD,C,UAGRJ,QAAQ,CAAC;AAACG,QAAAA,IAAI,EAAEf,SAAP;AAAkBgB,QAAAA,WAAW,EAAE;AAA/B,OAAD,C,UAGRJ,QAAQ,CAAC;AAACG,QAAAA,IAAI;AAAA;AAAA,oCAAL;AAAmBC,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,UAGRJ,QAAQ,CAAC;AAACG,QAAAA,IAAI;AAAA;AAAA,kCAAL;AAAkBC,QAAAA,WAAW,EAAE;AAA/B,OAAD,C,2BAZb,MAEaF,WAFb,SAEiClB,SAFjC,CAE2C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAa/BqB,SAb+B,GAaJ,IAbI;AAAA,eAc/BC,UAd+B,GAcL,EAdK;AAAA;;AAgBvCC,QAAAA,MAAM,GAAG;AACL;AAAA;AAAA,8BAAMC,WAAN,GAAoBC,IAApB;;AACA,cAAI,KAAKC,SAAT,EAAoB;AAChB,iBAAKL,SAAL,GAAiB;AAAA;AAAA,sCAASM,QAAT,CAAkB,KAAKD,SAAL,CAAeE,IAAjC,CAAjB;AACH;;AACD,eAAKH,IAAL;AACH;;AAES,cAAJA,IAAI,GAAG;AACT,gBAAM;AAAA;AAAA,8BAAMI,QAAN,CAAeC,IAAf,EAAN;AACA,gBAAM;AAAA;AAAA,wCAAWC,QAAX,CAAoBC,UAApB,CAA+B;AAAA;AAAA,wCAAWC,KAA1C,CAAN,CAFS,CAE8C;;AACvD,gBAAM;AAAA;AAAA,8BAAMC,QAAN,CAAeJ,IAAf,EAAN;AAEA;AAAA;AAAA,4CAAaL,IAAb,CAAkB,IAAItB,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe;AAAA;AAAA,sCAAUgC,eAAzB,EAA0C;AAAA;AAAA,sCAAUC,UAApD,CAAlB;AACA,eAAKC,gBAAL,CAAsB,KAAKC,YAA3B,EAAyCC,IAAzC,CAA+CC,KAAD,IAAW;AACrD,iBAAKC,cAAL,CAAoBD,KAApB;AACH,WAFD,EAEGE,KAFH,CAEUC,KAAD,IAAW;AAChBC,YAAAA,OAAO,CAACD,KAAR,CAAc,0BAAd,EAA0CA,KAA1C;AACH,WAJD;AAMH;;AAEDE,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtB;AAAA;AAAA,4CAAaC,IAAb,CAAkBD,SAAlB;;AAEA,eAAKxB,UAAL,CAAgB0B,OAAhB,CAAyBR,KAAD,IAAW;AAC/BA,YAAAA,KAAK,CAACS,eAAN,CAAsBH,SAAtB;AACH,WAFD;AAGH;;AAE6B,cAAhBT,gBAAgB,CAACa,EAAD,EAAkC;AAC5D,iBAAO,IAAIC,OAAJ,CAAY,OAAOC,OAAP,EAAgBC,MAAhB,KAA2B;AAC1C,gBAAIC,SAAS,GAAG;AAAA;AAAA,wCAAcJ,EAAd,CAAhB;AACA,kBAAMK,MAAM,GAAG,MAAM;AAAA;AAAA,gCAAMC,MAAN,CAAaC,SAAb,CAAuBH,SAAS,CAACI,cAAjC,EAAiDzD,MAAjD,CAArB;;AACA,gBAAI,CAACsD,MAAL,EAAa;AACTF,cAAAA,MAAM,CAAC,uBAAD,CAAN;AACA;AACH;;AAED,gBAAI,CAAC,KAAKM,UAAV,EAAsB;AAClBN,cAAAA,MAAM,CAAC,wCAAD,CAAN;AACA;AACH;;AACD,iBAAKM,UAAL,CAAgBC,SAAhB,CAA0BN,SAA1B,EAAqCC,MAArC;AACAH,YAAAA,OAAO,CAAC,KAAKO,UAAN,CAAP;AACH,WAdM,CAAP;AAeH;;AAEOlB,QAAAA,cAAc,CAACD,KAAD,EAAoB;AACtCA,UAAAA,KAAK,CAACqB,QAAN,CAAe,CAAf,EAAkB,CAAC,GAAnB,EAAwB,CAAC,EAAzB;AACArB,UAAAA,KAAK,CAACsB,OAAN,CAAeC,KAAf,GAAuB,CAAvB;;AACA,cAAI,KAAK1C,SAAT,EAAoB;AAChBmB,YAAAA,KAAK,CAACwB,QAAN,CAAe,CAAf,EAAkB,CAAlB,EAAqB,KAAK3C,SAA1B;AACH;;AAED,eAAKC,UAAL,CAAgB2C,IAAhB,CAAqBzB,KAArB;AACH;;AAxEsC,O;;;;;iBAET,C;;;;;;;iBAGK,I;;;;;;;iBAGE,I;;;;;;;iBAGA,I", "sourcesContent": ["import { _decorator, Component, Node, Prefab, Enum, Rect, JsonAsset } from 'cc';\r\nimport { EnemyEnum } from '../enum-gen/EnemyEnum';\r\nimport PlaneBase from 'db://assets/bundles/common/script/game/ui/plane/PlaneBase';\r\nimport EnemyPlane from 'db://assets/bundles/common/script/game/ui/plane/enemy/EnemyPlane';\r\nimport { EnemyData } from 'db://assets/bundles/common/script/game/data/EnemyData';\r\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\r\nimport { PathData } from 'db://assets/bundles/common/script/game/data/PathData';\r\nimport { BundleName } from 'db://assets/bundles/common/script/const/BundleConst';\r\nimport { BulletSystem } from 'db://assets/bundles/common/script/game/bullet/BulletSystem';\r\nimport { ResManager } from 'db://assets/scripts/core/base/ResManager';\r\nimport { GameConst } from 'db://assets/scripts/core/base/GameConst'\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\n@ccclass('PlaneEditor')\r\n// @executeInEditMode(true)\r\nexport class PlaneEditor extends Component {\r\n    @property({type: Enum(EnemyEnum), displayName: \"敌机ID\"})\r\n    public enemyPlaneID: number = 0;\r\n\r\n    @property({type: JsonAsset, displayName: \"路径\"})\r\n    public pathAsset: JsonAsset|null = null;\r\n\r\n    @property({type: EnemyPlane, displayName: \"敌机节点\"})\r\n    public enemyPlane: EnemyPlane|null = null;\r\n\r\n    @property({type: PlaneBase, displayName: \"玩家飞机\"})\r\n    public playerPlane: PlaneBase|null = null;\r\n\r\n    private _pathData: PathData|null = null;\r\n    private _planeList: PlaneBase[] = [];\r\n\r\n    onLoad() {\r\n        MyApp.GetInstance().init();\r\n        if (this.pathAsset) {\r\n            this._pathData = PathData.fromJSON(this.pathAsset.json);\r\n        }\r\n        this.init();\r\n    }\r\n\r\n    async init() {\r\n        await MyApp.planeMgr.load();\r\n        await ResManager.instance.loadBundle(BundleName.Luban) //优先加载完配置\r\n        await MyApp.lubanMgr.load();\r\n        \r\n        BulletSystem.init(new Rect(0, 0, GameConst.ViewBattleWidth, GameConst.ViewHeight));\r\n        this.createEnemyPlane(this.enemyPlaneID).then((plane) => {\r\n            this.onEnemyCreated(plane);\r\n        }).catch((error) => {\r\n            console.error(\"createEnemyPlane error: \", error);\r\n        });\r\n\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n        BulletSystem.tick(deltaTime);\r\n        \r\n        this._planeList.forEach((plane) => {\r\n            plane.updateGameLogic(deltaTime);\r\n        });\r\n    }\r\n\r\n    private async createEnemyPlane(id: number): Promise<EnemyPlane> {\r\n        return new Promise(async (resolve, reject) => {\r\n            let enemyData = new EnemyData(id);\r\n            const prefab = await MyApp.resMgr.loadAsync(enemyData.recoursePrefab, Prefab);\r\n            if (!prefab) {\r\n                reject(\"Failed to load prefab\");\r\n                return;\r\n            }\r\n            \r\n            if (!this.enemyPlane) {\r\n                reject(\"EnemyPlane component not found on node\");\r\n                return;\r\n            }\r\n            this.enemyPlane.initPlane(enemyData, prefab);\r\n            resolve(this.enemyPlane);\r\n        });\r\n    }\r\n\r\n    private onEnemyCreated(plane: EnemyPlane) {\r\n        plane.initMove(0, -200, -90);\r\n        plane.moveCom!.speed = 1;\r\n        if (this._pathData) {\r\n            plane.initPath(0, 0, this._pathData);\r\n        }\r\n\r\n        this._planeList.push(plane);\r\n    }\r\n}\r\n"]}
{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/plane/PlaneEditor.ts"], "names": ["_decorator", "Component", "Prefab", "Enum", "JsonAsset", "EnemyEnum", "PlaneBase", "EnemyPlane", "EnemyData", "MyApp", "ccclass", "property", "executeInEditMode", "PlaneEditor", "type", "displayName", "_enemyPlane", "onLoad", "GetInstance", "init", "createEnemyPlane", "enemyPlaneID", "update", "deltaTime", "updateGameLogic", "id", "enemyData", "prefab", "resMgr", "loadAsync", "recoursePrefab", "node", "getComponent", "console", "log", "initPlane"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,S,OAAAA,S;;AAC3CC,MAAAA,S,iBAAAA,S;;AACFC,MAAAA,S;;AACAC,MAAAA,U;;AACEC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,K,iBAAAA,K;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CZ,U;;6BAIpCa,W,WAFZH,OAAO,CAAC,aAAD,C,UAGHC,QAAQ,CAAC;AAACG,QAAAA,IAAI,EAAEX,IAAI;AAAA;AAAA,mCAAX;AAAwBY,QAAAA,WAAW,EAAE;AAArC,OAAD,C,UAGRJ,QAAQ,CAAC;AAACG,QAAAA,IAAI,EAAEV,SAAP;AAAkBW,QAAAA,WAAW,EAAE;AAA/B,OAAD,C,UAGRJ,QAAQ,CAAC;AAACG,QAAAA,IAAI;AAAA;AAAA,kCAAL;AAAkBC,QAAAA,WAAW,EAAE;AAA/B,OAAD,C,2BATb,MAEaF,WAFb,SAEiCZ,SAFjC,CAE2C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAU/Be,WAV+B,GAUA,IAVA;AAAA;;AAYvCC,QAAAA,MAAM,GAAG;AACL;AAAA;AAAA,8BAAMC,WAAN,GAAoBC,IAApB;AACA,eAAKC,gBAAL,CAAsB,KAAKC,YAA3B;AACH;;AAEDC,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtB,cAAI,KAAKP,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBQ,eAAjB,CAAiCD,SAAjC;AACH;AACJ;;AAE6B,cAAhBH,gBAAgB,CAACK,EAAD,EAAa;AACvC,cAAIC,SAAS,GAAG;AAAA;AAAA,sCAAcD,EAAd,CAAhB;AACA,gBAAME,MAAM,GAAG,MAAM;AAAA;AAAA,8BAAMC,MAAN,CAAaC,SAAb,CAAuBH,SAAS,CAACI,cAAjC,EAAiD5B,MAAjD,CAArB;AACA,eAAKc,WAAL,GAAmB,KAAKe,IAAL,CAAUC,YAAV;AAAA;AAAA,uCAAnB;;AACA,cAAI,CAAC,KAAKhB,WAAV,EAAuB;AACnBiB,YAAAA,OAAO,CAACC,GAAR,CAAY,UAAZ;AACA;AACH;;AAED,eAAKlB,WAAL,CAAiBmB,SAAjB,CAA2BT,SAA3B,EAAsCC,MAAtC;AACH;;AAjCsC,O;;;;;iBAET,C;;;;;;;iBAGK,I;;;;;;;iBAGE,I", "sourcesContent": ["import { _decorator, Component, Node, Prefab, Enum, JsonAsset } from 'cc';\r\nimport { EnemyEnum } from '../enum-gen/EnemyEnum';\r\nimport PlaneBase from 'db://assets/bundles/common/script/game/ui/plane/PlaneBase';\r\nimport EnemyPlane from 'db://assets/bundles/common/script/game/ui/plane/enemy/EnemyPlane';\r\nimport { EnemyData } from 'db://assets/bundles/common/script/game/data/EnemyData';\r\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\n@ccclass('PlaneEditor')\r\n// @executeInEditMode(true)\r\nexport class PlaneEditor extends Component {\r\n    @property({type: Enum(EnemyEnum), displayName: \"敌机ID\"})\r\n    public enemyPlaneID: number = 0;\r\n\r\n    @property({type: JsonAsset, displayName: \"路径\"})\r\n    public pathAsset: JsonAsset|null = null;\r\n\r\n    @property({type: PlaneBase, displayName: \"玩家飞机\"})\r\n    public playerPlane: PlaneBase|null = null;\r\n\r\n    private _enemyPlane: EnemyPlane|null = null;\r\n\r\n    onLoad() {\r\n        MyApp.GetInstance().init();\r\n        this.createEnemyPlane(this.enemyPlaneID);\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n        if (this._enemyPlane) {\r\n            this._enemyPlane.updateGameLogic(deltaTime);\r\n        }\r\n    }\r\n\r\n    private async createEnemyPlane(id: number) {\r\n        let enemyData = new EnemyData(id);\r\n        const prefab = await MyApp.resMgr.loadAsync(enemyData.recoursePrefab, Prefab);\r\n        this._enemyPlane = this.node.getComponent(EnemyPlane);\r\n        if (!this._enemyPlane) {\r\n            console.log(\"no plane\"); \r\n            return;\r\n        }\r\n\r\n        this._enemyPlane.initPlane(enemyData, prefab);\r\n    }\r\n}\r\n"]}
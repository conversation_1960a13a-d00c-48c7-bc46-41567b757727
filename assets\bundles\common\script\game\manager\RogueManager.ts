
/**
 * 肉鸽数据管理
 */

import { SingletonBase } from "db://assets/scripts/core/base/SingletonBase";
import { ResWorldGroup } from "../../autogen/luban/schema";
import { MyApp } from "../../app/MyApp";
import { instantiate, Prefab, Node, NodePool } from "cc";
import { RogueItem } from "../../ui/gameui/game/RogueItem";
import { BundleName } from "../../const/BundleConst";

export class RogueManager extends SingletonBase<RogueManager> {
    _rougeGroupConfig: { [key: number]: ResWorldGroup[] } = {};

    private _selectedHistory: Map<number, number> = new Map(); // 词条ID -> 选取次数
    private _typeSelectedCount: Map<number, number> = new Map(); // 词条类型 -> 选取次数
    private _rogueItemPool: NodePool = new NodePool();

    constructor() {
        super();
        this.initRougeGroupConfig()
    }

    initRougeGroupConfig() {
        let list = MyApp.lubanTables.TbResWordGroup.getDataList();
        for (let v of list) {
            if (!this._rougeGroupConfig[v.GroupID]) {
                this._rougeGroupConfig[v.GroupID] = [];
            }
            this._rougeGroupConfig[v.GroupID].push(v);
        }
    }

    public reset(): void {
        this._selectedHistory.clear();
        this._typeSelectedCount.clear();
    }

    clear(){
        this.reset();
        this._rogueItemPool.clear();
    }

    getSeLectedHistory(): Map<number, number> {
        return this._selectedHistory;
    }

    async setRogueItem(parent: Node, wordId: number, lv: number = 0) {
        let rogue: RogueItem;
        // Try to get from pool first
        if (this._rogueItemPool.size() > 0) {
            const node = this._rogueItemPool.get();
            rogue = node!.getComponent(RogueItem)!;
        } else {
            // Create new if pool is empty
            rogue = await this.getRogueItem();
        }

        rogue.node.parent = parent!;
        rogue.initRogueById(wordId, lv)
    }

    async getRogueItem() {
        let rogueItem = await MyApp.resMgr.loadAsync(BundleName.GameFight,"prefab/RogueItem", Prefab)
        let node = instantiate(rogueItem!) as Node;
        return node!.getComponent(RogueItem)!
    }
    public recycleRogueItems(parentNode: Node): void {
        if (!parentNode) return;

        for (let i = parentNode.children.length - 1; i >= 0; i--) {
            const childNode = parentNode.children[i];
            const rogueItem = childNode.getComponent(RogueItem);

            if (rogueItem) {
                rogueItem.reset?.();
                this._rogueItemPool.put(childNode);
            }
        }
    }

    public clearRogueItemPool(): void {
        this._rogueItemPool.clear();
    }
    /**
     * 随机获取词条
     * @param groupId 词条组ID
     * @param count 随机数量，默认3个
     * @returns 随机到的词条列表
     */
    public randomWords(groupId: number, count: number = 3): ResWorldGroup[] {
        const list = this._rougeGroupConfig[groupId];
        if (!list || list.length === 0) {
            console.warn(`词条组 ${groupId} 不存在或为空`);
            return [];
        }

        // 按优先级分组
        const priorityGroups = this.groupByPriority(list);

        const result: ResWorldGroup[] = [];
        let remainingCount = count;

        // 按优先级从低到高依次随机
        const priorities = Object.keys(priorityGroups).map(Number).sort((a, b) => a - b);

        for (const priority of priorities) {
            if (remainingCount <= 0) break;

            const availableWords = this.getAvailableWords(priorityGroups[priority]);
            if (availableWords.length === 0) continue;

            // 逐个随机，而不是一次性返回所有
            const wordsForThisPriority = this.randomFromPriorityGroupOneByOne(
                availableWords,
                Math.min(remainingCount, availableWords.length)
            );

            result.push(...wordsForThisPriority);
            remainingCount -= wordsForThisPriority.length;
        }

        return result;
    }

    /**
     * 按优先级分组
     */
    private groupByPriority(words: ResWorldGroup[]): { [priority: number]: ResWorldGroup[] } {
        const groups: { [priority: number]: ResWorldGroup[] } = {};

        for (const word of words) {
            if (!groups[word.priority]) {
                groups[word.priority] = [];
            }
            groups[word.priority].push(word);
        }

        return groups;
    }

    /**
     * 获取可用的词条（考虑次数限制）
     */
    private getAvailableWords(words: ResWorldGroup[]): ResWorldGroup[] {
        return words.filter(word => {
            // 检查次数限制
            if (word.times === -1) return true; // -1表示无限次
            if (word.times === 0) return false; // 0表示不可选
            const selectedCount = this._selectedHistory.get(word.ID) || 0;
            return selectedCount < word.times;
        });
    }

    /**
     * 从指定优先级组中逐个随机选择词条（每次随机后重新计算权重）
     */
    private randomFromPriorityGroupOneByOne(words: ResWorldGroup[], count: number): ResWorldGroup[] {
        if (words.length <= count) {
            // 如果数量不够，返回所有可用的，但需要打乱顺序
            return this.shuffleArray([...words]);
        }

        const selected: ResWorldGroup[] = [];
        const availableWords = [...words];

        for (let i = 0; i < count && availableWords.length > 0; i++) {
            // 每次随机都重新计算权重（考虑类型修正）
            const weightedWords = availableWords.map(word => {
                let weight = word.weight;

                // 如果之前选取过该类型，加上类型权重修正
                if (word.type && word.typWeightFix) {
                    const typeSelectedCount = this._typeSelectedCount.get(word.type) || 0;
                    if (typeSelectedCount > 0) {
                        weight += word.typWeightFix;
                    }
                }

                return { word, weight };
            });

            // 权重随机选择一个词条
            const selectedWord = this.selectByWeight(weightedWords);
            if (selectedWord) {
                selected.push(selectedWord);

                // 从可用列表中移除已选中的词条
                const index = availableWords.findIndex(w => w.ID === selectedWord.ID);
                if (index !== -1) {
                    availableWords.splice(index, 1);
                }
            }
        }

        return selected;
    }

    /**
     * 根据权重随机选择一个词条
     */
    private selectByWeight(weightedWords: { word: ResWorldGroup, weight: number }[]): ResWorldGroup | null {
        if (weightedWords.length === 0) return null;

        const totalWeight = weightedWords.reduce((sum, item) => sum + item.weight, 0);
        let random = Math.random() * totalWeight;

        for (const item of weightedWords) {
            random -= item.weight;
            if (random <= 0) {
                return item.word;
            }
        }

        // 如果由于浮点数精度问题没有选中，返回最后一个
        return weightedWords[weightedWords.length - 1].word;
    }

    /**
     * 打乱数组顺序
     */
    private shuffleArray<T>(array: T[]): T[] {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }

    selectRogue(selectedWords: ResWorldGroup[]) {
        // 更新选取记录
        this.updateSelectionHistory(selectedWords);
    }


    /**
     * 更新选取记录
     */
    private updateSelectionHistory(selectedWords: ResWorldGroup[]): void {
        for (const word of selectedWords) {
            // 更新词条选取次数
            const currentCount = this._selectedHistory.get(word.ID) || 0;
            this._selectedHistory.set(word.ID, currentCount + 1);

            // 更新类型选取次数
            if (word.type) {
                const typeCount = this._typeSelectedCount.get(word.type) || 0;
                this._typeSelectedCount.set(word.type, typeCount + 1);
            }
        }
    }

    /**
     * 获取词条选取次数
     */
    public getWordSelectionCount(wordId: number): number {
        return this._selectedHistory.get(wordId) || 0;
    }

    /**
     * 获取类型选取次数
     */
    public getTypeSelectionCount(wordType: number): number {
        return this._typeSelectedCount.get(wordType) || 0;
    }

    public coverToBuffIds(wordGroupId:number[]){
        let buffIDs:number[] = [];
        wordGroupId.forEach(key=>{
            let config = MyApp.lubanTables.TbResWordGroup.get(key);
            let wordConfig = MyApp.lubanTables.TbResWord.get(config!.wordId)
            buffIDs.push(wordConfig!.buffID);
        })
        return buffIDs;
    }
}
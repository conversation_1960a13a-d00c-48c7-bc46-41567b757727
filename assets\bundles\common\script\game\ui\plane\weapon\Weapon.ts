import { _decorator, instantiate, Component, Node, Prefab, Enum, CCInteger } from "cc";
const { ccclass, property } = _decorator;

import { eEmitterStatus, Emitter } from "db://assets/bundles/common/script/game/bullet/Emitter";
import Entity from "db://assets/bundles/common/script/game/ui/base/Entity";
import { ResEmitter } from "db://assets/bundles/common/script/autogen/luban/schema";
import { MyApp } from "db://assets/bundles/common/script/app/MyApp";
import PlaneBase from "../PlaneBase";
import { EmitterEnum } from "db://assets/editor/enum-gen/EmitterEnum";

export enum eWeaponUseCond {
    Immediate,        // 立刻启用
    DelayTime,        // 延迟一段时间启用
    WeaponDestroyed,  // 前置武器被销毁
    // TODO: TargetDistance, // 目标距离在范围内
    // TODO: TargetAngle,    // 目标角度在范围内
}

export const enum eWeaponState {
    Aiming,     // 玩家飞机不需要这个状态, 只有敌机需要
    Emitting,   // 发射中
    None,       // 无任何状态:后面看需求是否增加Destroyed等状态
}

/**
 * 武器后续可能也需要继承自PlaneBase, 因为武器可能也需要血量，扣血等逻辑。
 */
@ccclass('Weapon')
export class Weapon extends Entity {
    @property({ type: CCInteger, displayName: "转动速度(角度/秒)" })
    public readonly angleSpeed: number = 60; // 角速度
    @property({ type: CCInteger, displayName: "锁定目标时间(ms)" })
    public readonly aimingTime: number = 2000; // 锁定目标时间，单位ms
    @property({ type: Enum(eWeaponUseCond), displayName: "启用条件"})
    public readonly useCondition: eWeaponUseCond = eWeaponUseCond.Immediate;
    @property({ type: CCInteger, displayName: "延迟启用时间(ms)", 
        visible() {
            // @ts-ignore
            return this.useCondition === eWeaponUseCond.DelayTime;
        }
    })
    public readonly delayTime: number = 0; // 延迟时间，单位ms

    // 注意这里的Node不能替换成Weapon，因为cocos在ccclass里不能引用自己。
    @property({ type: [Node], displayName: "目标武器", 
        visible() { 
            // @ts-ignore
            return this.useCondition === eWeaponUseCond.WeaponDestroyed 
        } 
    })
    public targetWeaponNodes: Node[] = [];

    @property({ type: Enum(EmitterEnum), displayName: "发射器" })
    public readonly emitterID: number = 0;

    private m_emitterConfig: ResEmitter | undefined = undefined;
    private m_emitterPrefab: Prefab | null = null; // 武器发射器

    private m_state: eWeaponState = eWeaponState.None;
    private m_stateElapsedTime: number = 0; // 当前状态持续时间
    private m_target: Entity | null = null; // 武器目标
    private m_owner: Entity | null = null;  // 武器归属
    private m_targetWeapons: Weapon[] = [];
    private m_emitter: Emitter | null = null;

    init() {
        this.m_state = eWeaponState.None;
        // if (this.emitter) {
        //     this.emitter.changeStatus(eEmitterStatus.None);
        // }
        if (this.useCondition === eWeaponUseCond.WeaponDestroyed) {
            this.m_targetWeapons = this.node.getComponentsInChildren(Weapon);
        }
    }

    private initEmitter() {
        if (!this.m_emitter) {
            this.loadEmitterByID();
        } else {
            // 非首次启用了，后面看是直接切到预热还是要算启用延迟
            this.m_emitter?.changeStatus(eEmitterStatus.Prewarm);
        }
    }

    private loadEmitterByID() {
        if (this.emitterID > 0 && MyApp.GetInstance() && MyApp.lubanMgr) {
            this.m_emitterConfig = MyApp.lubanTables.TbResEmitter.get(this.emitterID);
            if (this.m_emitterConfig) {
                MyApp.resMgr.load(this.m_emitterConfig.prefab, Prefab, (error: any, prefab: Prefab) => {
                    if (error) {
                        console.error("Weapon load emitter prefab err", error);
                        return;
                    }

                    this.m_emitterPrefab = prefab;
                    this.createEmitter();
                });
            }
        } else {
            console.error("Weapon load emitter err, no emitter id");
        }
    }

    private createEmitter() {
        if (this.m_emitterPrefab) {
            const node = instantiate(this.m_emitterPrefab);
            this.node.addChild(node);
            node.setPosition(0, 0);
            this.m_emitter = node.getComponent(Emitter);
            if (!this.m_emitter) {
                console.warn("Weapon create emitter err, no emitter component");
                return;
            }

            this.m_emitter.setEntity(this.m_owner as PlaneBase);
            this.m_emitter.setIsActive(true);
        }
    }

    public get state(): eWeaponState {
        return this.m_state;
    }

    public set state(value: eWeaponState) {
        if (this.m_state === value) {
            return;
        }

        this.m_state = value;
        this.m_stateElapsedTime = 0;
    }

    public setOwner(owner: Entity): Weapon {
        this.m_owner = owner;
        return this;
    }

    public setTarget(target: Entity | null): Weapon {
        this.m_target = target;
        if (this.useCondition === eWeaponUseCond.Immediate) {
            this.activate();
        }

        return this;
    }

    public updateGameLogic(dt: number) {
        this.m_stateElapsedTime += dt;
        switch (this.m_state) {
            case eWeaponState.Aiming:
                this.tickAiming(dt);
                break;
            case eWeaponState.Emitting:
                //this.tickEmitting(dt);
                break;
            case eWeaponState.None:
                this.tickNone(dt);
                break;
        }
    }

    // 激活武器
    private activate() {
        this.state = this.aimingTime > 0 ? eWeaponState.Aiming : eWeaponState.Emitting;
        this.initEmitter();
    }

    private tickAiming(dt: number) {
        if (this.m_stateElapsedTime >= this.aimingTime) {
            this.state = eWeaponState.Emitting;
        }

        this.turnToTarget(dt);
    }

    private tickEmitting(dt: number) {
        //if (this.m_emitter && this.m_emitter.status !== eEmitterStatus.Emitting) {
        //    this.m_emitter.changeStatus(eEmitterStatus.Emitting);
        //}
    }

    private tickNone(dt: number) {
        if (this.useCondition === eWeaponUseCond.WeaponDestroyed) {
            for (let weapon of this.m_targetWeapons) {
                if (!weapon.isDead) {
                    return;
                }
            }
            this.activate();
        } else if (this.useCondition === eWeaponUseCond.DelayTime) {
            if (this.m_stateElapsedTime >= this.delayTime) {
                this.activate();
            }
        }
    }

    private turnToTarget(dt: number) {
        if (!this.m_target || !this.m_owner) {
            return;
        }
        const ownerNode = this.m_owner.node;
        const targetNode = this.m_target.node;
        const angle = ownerNode.angle;
        const targetAngle = Math.atan2(targetNode.worldPosition.y - ownerNode.worldPosition.y, targetNode.worldPosition.x - ownerNode.worldPosition.x) * 180 / Math.PI - 90;
        let deltaAngle = targetAngle - angle;
        if (deltaAngle > 180) {
            deltaAngle -= 360;
        } else if (deltaAngle < -180) {
            deltaAngle += 360;
        }
        const maxDelta = this.angleSpeed * dt / 1000;
        if (Math.abs(deltaAngle) <= maxDelta) {
            ownerNode.angle = targetAngle;
        }
        else {
            ownerNode.angle += Math.sign(deltaAngle) * maxDelta;
        }
    }
}
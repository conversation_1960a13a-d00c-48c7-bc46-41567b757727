import { _decorator, Component, Graphics, UITransform, Color, CCInteger, Vec2, CCFloat, Enum, RichText, CCObject, VerticalTextAlignment, HorizontalTextAlignment } from 'cc';
import { EDITOR } from 'cc/env';
import { PathPoint } from 'db://assets/bundles/common/script/game/data/PathData';
import { eOrientationType } from 'db://assets/bundles/common/script/game/move/IMovable';
import { PathEditor } from './PathEditor';

const { ccclass, property, executeInEditMode, disallowMultiple, menu, requireComponent } = _decorator;

@ccclass('PathPointEditor')
@menu("怪物/编辑器/路径点")
@requireComponent(Graphics)
@requireComponent(UITransform)
@executeInEditMode(true)
@disallowMultiple(true)
export class PathPointEditor extends Component {
    // @property({ type: CCFloat, displayName: "点大小" })
    public pointSize: number = 20;

    @property({ type: CCFloat, displayName: "平滑程度", range: [0, 1], step: 0.1, slide: true, tooltip: "0=尖锐转角,1=最大平滑" })
    public get smoothness(): number {
        return this._pathPoint.smoothness;
    }
    public set smoothness(value: number) {
        this._pathPoint.smoothness = value;
    }
    @property({ type: CCInteger, displayName: "速度", tooltip: "飞机在此点的速度" })
    public get speed(): number {
        return this._pathPoint.speed;
    }
    public set speed(value: number) {
        this._pathPoint.speed = value;
    }
    @property({ type: CCInteger, displayName: "停留时间", tooltip: "飞机到达此点后停留时间（毫秒）" })
    public get stayDuration(): number {
        return this._pathPoint.stayDuration;
    }
    public set stayDuration(value: number) {
        this._pathPoint.stayDuration = value;
    }

    @property({ type: Enum(eOrientationType), displayName: "朝向类型", tooltip: "飞机在此点的朝向" })
    public get orientationType(): number {
        return this._pathPoint.orientationType;
    }
    public set orientationType(value: number) {
        this._pathPoint.orientationType = value;
    }

    @property({ type: CCInteger, displayName: "朝向参数", tooltip: "固定朝向时：角度值(0-360度)；其他类型暂未使用" })
    public get orientationParam(): number {
        return this._pathPoint.orientationParam;
    }
    public set orientationParam(value: number) {
        this._pathPoint.orientationParam = value;
    }

    private _pathEditor: PathEditor|null = null;
    public get pathEditor(): PathEditor|null {
        if (this._pathEditor === null && this.node.parent) {
            this._pathEditor = this.node.parent.getComponent(PathEditor);
        }
        return this._pathEditor;
    }

    private _richText: RichText|null = null;
    public get richText(): RichText {
        if (!this._richText) {
            this._richText = this.node.getComponent(RichText) || this.node.addComponent(RichText);
            // this._richText.hideFlags = CCObject.Flags.AllHideMasks;
            this._richText.fontSize = 16;
            this._richText.verticalAlign = VerticalTextAlignment.CENTER;
            this._richText.horizontalAlign = HorizontalTextAlignment.CENTER;
            this._richText.fontColor = Color.BLACK;
        }

        return this._richText;
    }

    private _pathPoint: PathPoint = new PathPoint();
    private _cachedIndex: number = -1;
    private selected: boolean = false;
    public onFocusInEditor(): void {
        this.selected = true;
    }
    public onLostFocusInEditor(): void {
        this.selected = false;
    }

    public get pathPoint(): PathPoint {
        // 同步节点位置到路径点数据
        this._pathPoint.position = new Vec2(this.node.position.x, this.node.position.y);
        return this._pathPoint;
    }

    public set pathPoint(value: PathPoint) {
        this._pathPoint = value;
        // 同步路径点数据到节点位置
        this.node.setPosition(this._pathPoint.x, this._pathPoint.y, 0);
    }
    
    public updateDisplay() {
        let graphics = this.getComponent(Graphics) || this.addComponent(Graphics);
        if (graphics === null) return;

        // 更新路径点位置
        this._pathPoint.position.x = this.node.position.x;
        this._pathPoint.position.y = this.node.position.y;

        const siblings = this.node.parent ? this.node.parent.children : [];
        PathEditor.drawPathPoint(
            graphics,
            this._pathPoint,
            this.selected,
            this.pointSize,
            this._cachedIndex,
            siblings.length,
            this.pathEditor?.startIdx || 0,
            this.pathEditor?.endIdx || -1,
            siblings
        );
    }

    public update(_dt: number) {
        // 检查是否需要重绘
        const siblingIndex = this.node.getSiblingIndex();

        // 检查索引是否改变
        if (siblingIndex !== this._cachedIndex) {
            this._cachedIndex = siblingIndex;
            this.node.name = `Point_${siblingIndex}`;
            this.richText.string = `${siblingIndex}`;
            const uiTrans = this.getComponent(UITransform);
            uiTrans!.setContentSize(100, 100); // 让这个点好选中
        }

        this.updateDisplay();
    }
}
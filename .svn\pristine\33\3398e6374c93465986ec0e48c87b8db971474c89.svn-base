import { _decorator } from "cc";
import RP<PERSON><PERSON>culator, { RP<PERSON>ontext, RPNProgram, SerializableRPNProgram } from "db://assets/bundles/common/script/game/utils/RPN";
const { ccclass, property } = _decorator;

export enum eExpressionType {
    RPN, Seq, Value
}

@ccclass('ExpressionValue')
export class ExpressionValue {
    @property({ visible: false })
    public type: eExpressionType = eExpressionType.Value;
    @property({ visible: false })
    public value: number = 0;
    @property({ visible: false })
    public expression: string = '';
    @property({ visible: false })
    public values: number[] = [];

    // Serializable compiled program for editor storage
    @property({ type: SerializableRPNProgram, visible: false })
    private serializedProgram: SerializableRPNProgram | null = null;

    // Runtime program (not serialized)
    private program: RPNProgram | null = null;
    private seqIndex: number = 0;

    static calc = new RPNCalculator();

    @property({ displayName: '值' })
    public get raw(): string {
        switch (this.type) {
            case eExpressionType.RPN:
                return this.expression;
            case eExpressionType.Seq:
                return this.values.join(',');
            case eExpressionType.Value:
                return this.value.toString();
            default:
                return '';
        }
    }

    public set raw(value: string) {
        if (!value) return;
        
        // clear seqIndex
        this.seqIndex = 0;
        // 替换全角为半角
        this.expression = value.replace(/[\uff01-\uff5e]/g, (ch) => String.fromCharCode(ch.charCodeAt(0) - 0xfee0));
        // if includes any 'letters' then it is a expression
        const isExpression = /[a-zA-Z]/.test(value);
        if (isExpression) {
            this.type = eExpressionType.RPN;
            // Compile and store both runtime and serializable versions
            this.program = ExpressionValue.calc.compile(this.expression);
            this.serializedProgram = SerializableRPNProgram.fromRPNProgram(this.program);
        }
        else if (value.includes(',')) {
            this.type = eExpressionType.Seq;
            this.values = value.split(',').map((item) => parseFloat(item)).filter((item) => !isNaN(item));
        }
        else {
            this.type = eExpressionType.Value;
            // try parse number
            const parsed = parseFloat(value);
            if (!isNaN(parsed)) {
                this.value = parsed;
            }
        }
    }

    public get isFixedValue(): boolean {
        return this.type === eExpressionType.Value;
    }

    public eval(context: RPNContext | null = null, reset: boolean = false): number {
        if (this.type === eExpressionType.RPN) {
            if (!this.program && this.serializedProgram?.isCompiled()) {
                this.program = this.serializedProgram.toRPNProgram(ExpressionValue.calc.registry);
            }

            if (this.program) {
                const result = this.program.evaluate(context || {});
                return typeof result === 'number' ? result : (result ? 1 : 0); // return 0 if false else 1
            }
            return this.value;
        }
        else if (this.type === eExpressionType.Seq) {
            if (this.seqIndex >= this.values.length || reset) {
                this.seqIndex = 0;
            }
            // console.warn('seqIndex: ', this.seqIndex, ', ', this.values[this.seqIndex]);
            return this.values[this.seqIndex++];
        }
        else {
            return this.value;
        }
    }

    // 用来清理下状态
    public reset() {
        this.seqIndex = 0;
    }

    // Get debug info about the compiled program
    public getDebugInfo(): any {
        if (this.type == eExpressionType.Value) {
            return { type: 'number', value: this.value };
        }

        return {
            type: this.type,
            seq: this.seqIndex,
            value: this.value
        };
    }

    constructor(str?: string) {
        this.raw = str || '';
        this.seqIndex = 0;
    }

    public toJSON() {
        return {
            type: this.type,
            value: this.value,
            expression: this.expression,
            values: this.values,
            serializedProgram: this.serializedProgram
        };
    }
}
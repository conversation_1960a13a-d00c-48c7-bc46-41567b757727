System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, _dec, _class, _crd, ccclass, property, LevelLayer;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "4ee77JD5idCXJtEz0IyfaVM", "LevelLayer", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Prefab', 'Node', 'instantiate', 'UITransform']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("LevelLayer", LevelLayer = (_dec = ccclass('LevelLayer'), _dec(_class = class LevelLayer extends Component {
        constructor(...args) {
          super(...args);
          this.speed = 0;
        }

        tick(deltaTime) {// 重写此方法
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=35cbb2d8c2f07189fbabe551255722425f971112.js.map
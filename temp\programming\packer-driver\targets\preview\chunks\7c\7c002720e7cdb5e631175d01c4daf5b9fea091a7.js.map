{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/FriendRankUI.ts"], "names": ["_decorator", "Label", "Sprite", "BundleName", "ButtonPlus", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "AvatarIcon", "List", "FriendFusionUI", "FriendRankCellUI", "ccclass", "property", "FriendRankUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getBundleName", "HomePK", "getUIOption", "isClickBgCloseUI", "onLoad", "btnClose", "addClick", "onCloseClick", "btnEndless", "onEndlessClick", "btnPK", "onPKClick", "list", "numItems", "setSelfData", "rank", "name", "score", "avatar", "plane", "closeUI", "openUI", "onShow", "onHide", "onClose", "onDestroy", "data", "selfRank", "string", "toString", "selfName", "selfScore", "onList<PERSON>ender", "listItem", "row", "cell", "getComponent", "setData"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;AAAaC,MAAAA,M,OAAAA,M;;AACzBC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,U,iBAAAA,U;;AACFC,MAAAA,I;;AACEC,MAAAA,c,iBAAAA,c;;AACAC,MAAAA,gB,iBAAAA,gB;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBb,U;;8BAGjBc,Y,WADZF,OAAO,CAAC,cAAD,C,UAEHC,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,uB,UAGRA,QAAQ,CAACZ,KAAD,C,UAERY,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ,CAACZ,KAAD,C,UAERY,QAAQ,CAACZ,KAAD,C,WAERY,QAAQ,CAACX,MAAD,C,2BAnBb,MACaY,YADb;AAAA;AAAA,4BACyC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAqBjB,eAANC,MAAM,GAAW;AAAE,iBAAO,wBAAP;AAAkC;;AAC7C,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAwB;;AACjC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,MAAlB;AAA2B;;AAC1C,eAAXC,WAAW,GAAU;AAAE,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AAAmC;;AAC9DC,QAAAA,MAAM,GAAS;AACrB,eAAKC,QAAL,CAAeC,QAAf,CAAwB,KAAKC,YAA7B,EAA2C,IAA3C;AACA,eAAKC,UAAL,CAAiBF,QAAjB,CAA0B,KAAKG,cAA/B,EAA+C,IAA/C;AACA,eAAKC,KAAL,CAAYJ,QAAZ,CAAqB,KAAKK,SAA1B,EAAqC,IAArC;AACA,eAAKC,IAAL,CAAWC,QAAX,GAAsB,EAAtB;AACA,eAAKC,WAAL,CAAiB;AAAEC,YAAAA,IAAI,EAAE,CAAR;AAAWC,YAAAA,IAAI,EAAE,KAAjB;AAAwBC,YAAAA,KAAK,EAAE,KAA/B;AAAsCC,YAAAA,MAAM,EAAE,EAA9C;AAAkDC,YAAAA,KAAK,EAAE;AAAzD,WAAjB;AACH;;AAEKZ,QAAAA,YAAY,GAAG;AAAA;AACjB,kBAAM;AAAA;AAAA,gCAAMa,OAAN,CAAcxB,YAAd,CAAN;AADiB;AAEpB;;AACKa,QAAAA,cAAc,GAAG;AAAA;AACnB;AAAA;AAAA,gCAAMW,OAAN,CAAcxB,YAAd;AACA,kBAAM;AAAA;AAAA,gCAAMyB,MAAN;AAAA;AAAA,iDAAN;AAFmB;AAGtB;;AACKV,QAAAA,SAAS,GAAG;AAAA;AAEjB;;AACKW,QAAAA,MAAM,GAAkB;AAAA;AAE7B;;AACKC,QAAAA,MAAM,GAAkB;AAAA;AAC7B;;AACKC,QAAAA,OAAO,GAAkB;AAAA;AAC9B;;AACSC,QAAAA,SAAS,GAAS,CAC3B;;AAEDX,QAAAA,WAAW,CAACY,IAAD,EAAqF;AAC5F,eAAKC,QAAL,CAAeC,MAAf,GAAwBF,IAAI,CAACX,IAAL,CAAUc,QAAV,EAAxB;AACA,eAAKC,QAAL,CAAeF,MAAf,GAAwBF,IAAI,CAACV,IAA7B;AACA,eAAKe,SAAL,CAAgBH,MAAhB,GAAyBF,IAAI,CAACT,KAAL,CAAWY,QAAX,EAAzB;AACH;;AAEDG,QAAAA,YAAY,CAACC,QAAD,EAAiBC,GAAjB,EAA8B;AACtC,cAAMC,IAAI,GAAGF,QAAQ,CAACG,YAAT;AAAA;AAAA,mDAAb;;AACA,cAAID,IAAI,KAAK,IAAb,EAAmB;AACfA,YAAAA,IAAI,CAACE,OAAL,CAAaH,GAAG,GAAG,CAAnB;AACH;AACJ;;AAhEoC,O;;;;;iBAEP,I;;;;;;;iBAEE,I;;;;;;;iBAEL,I;;;;;;;iBAEP,I;;;;;;;iBAGK,I;;;;;;;iBAEO,I;;;;;;;iBAEP,I;;;;;;;iBAEC,I;;;;;;;iBAEC,I", "sourcesContent": ["import { _decorator, Label, Node, Sprite } from 'cc';\r\nimport { BundleName } from 'db://assets/bundles/common/script/const/BundleConst';\r\nimport { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';\r\nimport { BaseUI, UILayer, UIMgr, UIOpt, } from 'db://assets/scripts/core/base/UIMgr';\r\nimport { AvatarIcon } from '../common/components/base/AvatarIcon';\r\nimport List from '../common/components/list/List';\r\nimport { FriendFusionUI } from './FriendFusionUI';\r\nimport { FriendRankCellUI } from './FriendRankCellUI';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('FriendRankUI')\r\nexport class FriendRankUI extends BaseUI {\r\n    @property(ButtonPlus)\r\n    btnClose: ButtonPlus | null = null;\r\n    @property(ButtonPlus)\r\n    btnEndless: ButtonPlus | null = null;\r\n    @property(ButtonPlus)\r\n    btnPK: ButtonPlus | null = null;\r\n    @property(List)\r\n    list: List | null = null;\r\n\r\n    @property(Label)\r\n    selfRank: Label | null = null;\r\n    @property(AvatarIcon)\r\n    selfAvatar: AvatarIcon | null = null;\r\n    @property(Label)\r\n    selfName: Label | null = null;\r\n    @property(Label)\r\n    selfScore: Label | null = null;\r\n    @property(Sprite)\r\n    selfPlane: Sprite | null = null;\r\n\r\n    public static getUrl(): string { return \"prefab/ui/FriendRankUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Default }\r\n    public static getBundleName(): string { return BundleName.HomePK; }\r\n    public static getUIOption(): UIOpt { return { isClickBgCloseUI: true } }\r\n    protected onLoad(): void {\r\n        this.btnClose!.addClick(this.onCloseClick, this);\r\n        this.btnEndless!.addClick(this.onEndlessClick, this);\r\n        this.btnPK!.addClick(this.onPKClick, this);\r\n        this.list!.numItems = 10;\r\n        this.setSelfData({ rank: 1, name: \"小师妹\", score: 10000, avatar: \"\", plane: \"\" });\r\n    }\r\n\r\n    async onCloseClick() {\r\n        await UIMgr.closeUI(FriendRankUI);\r\n    }\r\n    async onEndlessClick() {\r\n        UIMgr.closeUI(FriendRankUI);\r\n        await UIMgr.openUI(FriendFusionUI)\r\n    }\r\n    async onPKClick() {\r\n\r\n    }\r\n    async onShow(): Promise<void> {\r\n\r\n    }\r\n    async onHide(): Promise<void> {\r\n    }\r\n    async onClose(): Promise<void> {\r\n    }\r\n    protected onDestroy(): void {\r\n    }\r\n\r\n    setSelfData(data: { rank: number, name: string, score: number, avatar: string, plane: string }) {\r\n        this.selfRank!.string = data.rank.toString();\r\n        this.selfName!.string = data.name;\r\n        this.selfScore!.string = data.score.toString();\r\n    }\r\n\r\n    onListRender(listItem: Node, row: number) {\r\n        const cell = listItem.getComponent(FriendRankCellUI);\r\n        if (cell !== null) {\r\n            cell.setData(row + 1);\r\n        }\r\n    }\r\n}\r\n\r\n\r\n"]}
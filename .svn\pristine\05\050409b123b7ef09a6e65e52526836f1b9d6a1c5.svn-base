import { _decorator, Label, Node } from 'cc';
import { BundleName } from 'db://assets/bundles/common/script/const/BundleConst';
import { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';
import { BaseUI, UILayer, UIMgr, UIOpt } from 'db://assets/scripts/core/base/UIMgr';
import { DataEvent } from '../../event/DataEvent';
import { EventMgr } from '../../event/EventManager';
import { HomeUIEvent } from '../../event/HomeUIEvent';
import { UITools } from '../../game/utils/UITools';
import { TabPanel } from '../common/components/base/TabPanel';
import { HomeUI } from '../home/<USER>';
import { FriendAddUI } from './FriendAddUI';
import { FriendListUI } from './FriendListUI';
import { FriendStrangerUI } from './FriendStrangerUI';

const { ccclass, property } = _decorator;

const MODE_LIST = 0;
const MODE_ADD = 1;

@ccclass('FriendUI')
export class FriendUI extends BaseUI {

    @property(ButtonPlus)
    btnClose: ButtonPlus | null = null;

    @property(TabPanel)
    tabPanel: TabPanel | null = null;

    @property(Node)
    panelList: Node | null = null;
    @property(Node)
    panelAdd: Node | null = null;

    @property(Node)
    nodeList: Node | null = null;
    @property(ButtonPlus)
    btnGet: ButtonPlus | null = null;
    @property(Label)
    LabelTimes: Label | null = null;
    @property(Label)
    LabelUpdate: Label | null = null;

    @property(Label)
    LabelFriendNum: Label | null = null;

    @property(Node)
    nodeAdd: Node | null = null;
    @property(ButtonPlus)
    btnIgnoreAll: ButtonPlus | null = null;
    @property(ButtonPlus)
    btnAgreeAll: ButtonPlus | null = null;
    @property(ButtonPlus)
    btnRefreshAll: ButtonPlus | null = null;

    @property(FriendListUI)
    friendListUI: FriendListUI | null = null;
    @property(FriendAddUI)
    friendAddUI: FriendAddUI | null = null;
    @property(FriendStrangerUI)
    friendStrangerUI: FriendStrangerUI | null = null;

    public static getUrl(): string { return "prefab/ui/FriendUI"; }
    public static getLayer(): UILayer { return UILayer.Default; }
    public static getBundleName(): string { return BundleName.HomeFriend; }
    public static getUIOption(): UIOpt { return { isClickBgCloseUI: true } }
    protected async onLoad(): Promise<void> {
        UITools.modifyNumber(this.LabelTimes!, "50", "100");
        UITools.modifyNumber(this.LabelFriendNum!, "30", "100");
        this.LabelUpdate!.string = "刷新时间：" + UITools.formatTime(3 * 60 * 60, true);

        this.btnClose!.addClick(this.closeUI, this);
        this.btnGet!.addClick(this.onPower, this);
        this.btnIgnoreAll!.addClick(this.onIgnore, this);
        this.btnAgreeAll!.addClick(this.onAgree, this);
        this.btnRefreshAll!.addClick(this.onRefresh, this);

        this.panelAdd!.active = false;
        this.nodeAdd!.active = false;
        this.tabPanel!.addTabFun((idx: number) => {
            this.nodeList!.active = idx == MODE_LIST;
            this.nodeAdd!.active = idx == MODE_ADD;
            this.panelList!.active = idx == MODE_LIST;
            this.panelAdd!.active = idx == MODE_ADD;
        });

        EventMgr.once(HomeUIEvent.Leave, this.onLeave, this)
        EventMgr.on(DataEvent.FriendRefresh, this.onFriendRefresh, this)
    }
    private onLeave() {
        EventMgr.off(HomeUIEvent.Leave, this.onLeave, this)
        UIMgr.closeUI(FriendUI)
    }
    private onFriendRefresh() {
    }

    async closeUI() {
        UIMgr.closeUI(FriendUI);
        await UIMgr.openUI(HomeUI)
    }
    private onPower() {
    }
    private onIgnore() {
    }
    private onAgree() {
    }
    private onRefresh() {
    }

    async onShow(): Promise<void> {

    }
    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {
    }
    protected onDestroy(): void {
        EventMgr.targetOff(this);
    }
}
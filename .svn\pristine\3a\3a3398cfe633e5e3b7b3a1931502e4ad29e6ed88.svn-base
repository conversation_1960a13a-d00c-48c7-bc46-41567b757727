import { DEBUG } from "cc/env";
import csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';
import { MarqueeUI } from "db://assets/bundles/common/script/ui/common/MarqueeUI";
import { PopupUI } from "db://assets/bundles/common/script/ui/common/PopupUI";
import { ToastUI } from "db://assets/bundles/common/script/ui/common/ToastUI";
import { UIMgr } from "db://assets/scripts/core/base/UIMgr";
import { LevelUPUI } from "db://assets/bundles/common/script/ui/common/LevelUPUI";

// 定义回调函数类型
type Callback = () => void;

export class MessageBox {
  public static clearData() {
    this.contentLevelUP.length = 0;
    this.contentPopup.length = 0;
    this.contentMarquee.length = 0;
    this.showingLevelUP = false;
    this.showingPopup = false;
    this.showingMarquee = false;
  }
  //#region LevelUPUI  
  private static showingLevelUP: boolean = false;
  private static contentLevelUP: csproto.cs.IS2CLevelUp[] = [];
  private static async ShowNextLevelUP() {
    if (this.showingLevelUP || this.contentLevelUP.length === 0) {
      return;
    }
    //gm //addxp 55555
    this.showingLevelUP = true;
    const data = this.contentLevelUP.shift();
    await UIMgr.openUI(LevelUPUI, data);
    let ui = UIMgr.get(LevelUPUI);
    if (ui) {
      ui.toClose = this.closeLevelUP.bind(this);
    }
  }
  private static async closeLevelUP() {
    await UIMgr.closeUI(LevelUPUI);
    this.showingLevelUP = false;
    this.ShowNextLevelUP();
  }
  public static showLevelUP(data: csproto.cs.IS2CLevelUp) {
    this.contentLevelUP.push(data);
    this.ShowNextLevelUP();
  }
  //#endregion
  //#region PopupUI
  private static showingPopup: boolean = false;
  private static contentPopup: string[] = [];
  private static confirmCallbacks: (Callback | undefined)[] = []; // 确认回调数组
  private static cancelCallbacks: (Callback | undefined)[] = [];  // 取消回调数组

  private static async ShowNextPopup() {
    if (this.showingPopup || this.contentPopup.length === 0) {
      return;
    }
    this.showingPopup = true;
    const content = this.contentPopup.shift();
    const onConfirm = this.confirmCallbacks.shift();
    const onCancel = this.cancelCallbacks.shift();
    await UIMgr.openUI(PopupUI, content, onConfirm, onCancel);
    let ui = UIMgr.get(PopupUI);
    if (ui) {
      ui.toClose = this.closePopup.bind(this);
    }
  }

  private static async closePopup() {
    await UIMgr.closeUI(PopupUI);
    this.showingPopup = false;
    this.ShowNextPopup();
  }

  // 强制显示确认+取消按钮，不需要取消函数回调，不传onCancel
  public static confirm(content: string, onConfirm: Callback, onCancel?: Callback) {
    const cancelHandler = onCancel || (() => { });
    this.show(content, onConfirm, cancelHandler);
  }

  // 只显示确认按钮，调用这个
  public static show(content: string, onConfirm?: Callback, onCancel?: Callback): void {
    this.contentPopup.push(content);
    this.confirmCallbacks.push(onConfirm);
    this.cancelCallbacks.push(onCancel);
    this.ShowNextPopup();
  }
  //#endregion
  //#region MarqueeUI
  private static showingMarquee: boolean = false;
  private static contentMarquee: string[] = [];
  public static marquee(content: string) {
    this.contentMarquee.push(content);
    this.ShowNextMarquee();
  }
  private static async ShowNextMarquee() {
    if (this.showingMarquee || this.contentMarquee.length === 0) {
      return;
    }
    this.showingMarquee = true;
    const content = this.contentMarquee.shift();
    await UIMgr.openUI(MarqueeUI, content);
    let ui = UIMgr.get(MarqueeUI);
    if (ui) {
      ui.toClose = this.closeMarquee.bind(this);
    }
  }

  private static async closeMarquee() {
    await UIMgr.closeUI(MarqueeUI);
    this.showingMarquee = false;
    this.ShowNextMarquee();
  }

  public static toast(content: string) {
    UIMgr.openUI(ToastUI, content);
  }
  //#endregion
  //#region DEBUG
  public static testShow(content: string) {
    if (DEBUG) {
      this.show(content);
    }
  }
  public static testToast(content: string) {
    if (DEBUG) {
      this.toast(content);
    }
  }
  public static errorCode(ret_code: csproto.comm.RET_CODE) {
    if (DEBUG) {
      this.show("错误码：\n" + ret_code! + "\n" + csproto.comm.RET_CODE[ret_code!]);
    }
  }
  //#endregion
}

System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Label, Sprite, DataMgr, DataEvent, EventMgr, AvatarIcon, MyApp, UITools, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _crd, ccclass, property, FriendFusionCellUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfDataMgr(extras) {
    _reporterNs.report("DataMgr", "../../data/DataManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataEvent(extras) {
    _reporterNs.report("DataEvent", "../../event/DataEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "../../event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAvatarIcon(extras) {
    _reporterNs.report("AvatarIcon", "../common/components/base/AvatarIcon", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUITools(extras) {
    _reporterNs.report("UITools", "../../game/utils/UITools", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Label = _cc.Label;
      Sprite = _cc.Sprite;
    }, function (_unresolved_2) {
      DataMgr = _unresolved_2.DataMgr;
    }, function (_unresolved_3) {
      DataEvent = _unresolved_3.DataEvent;
    }, function (_unresolved_4) {
      EventMgr = _unresolved_4.EventMgr;
    }, function (_unresolved_5) {
      AvatarIcon = _unresolved_5.AvatarIcon;
    }, function (_unresolved_6) {
      MyApp = _unresolved_6.MyApp;
    }, function (_unresolved_7) {
      UITools = _unresolved_7.UITools;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "a7d11yEdkJEsZ2R24RUYXdp", "FriendFusionCellUI", undefined);

      __checkObsolete__(['_decorator', 'Color', 'Component', 'Label', 'math', 'Sprite', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("FriendFusionCellUI", FriendFusionCellUI = (_dec = ccclass('FriendFusionCellUI'), _dec2 = property(Label), _dec3 = property(Sprite), _dec4 = property(_crd && AvatarIcon === void 0 ? (_reportPossibleCrUseOfAvatarIcon({
        error: Error()
      }), AvatarIcon) : AvatarIcon), _dec5 = property(Label), _dec6 = property(Label), _dec7 = property(Sprite), _dec8 = property(Sprite), _dec(_class = (_class2 = class FriendFusionCellUI extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "lblAdd", _descriptor, this);

          _initializerDefineProperty(this, "spAdd", _descriptor2, this);

          _initializerDefineProperty(this, "avatarIcon", _descriptor3, this);

          _initializerDefineProperty(this, "lblName", _descriptor4, this);

          _initializerDefineProperty(this, "lblScore", _descriptor5, this);

          _initializerDefineProperty(this, "spPlane", _descriptor6, this);

          _initializerDefineProperty(this, "bg", _descriptor7, this);

          this.selectIdx = 0;
        }

        setData(idx, selectIdx) {
          this.selectIdx = selectIdx;
          this.lblAdd.string = idx.toString();
          this.lblName.string = "name";
          this.lblScore.string = "分数：";
          this.spPlane.spriteFrame = null;
          this.updateSelect();
        }

        updateSelect() {
          if (this.selectIdx == (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).pk.selectFusionIdx) {
            //this.getComponentInChildren(Sprite)!.color = Color.BLUE;
            (_crd && UITools === void 0 ? (_reportPossibleCrUseOfUITools({
              error: Error()
            }), UITools) : UITools).modifyStateSprite(this.bg, 0);
          } else {
            (_crd && UITools === void 0 ? (_reportPossibleCrUseOfUITools({
              error: Error()
            }), UITools) : UITools).modifyStateSprite(this.bg, 1); //this.getComponentInChildren(Sprite)!.color = math.color("#C3D69F");
          }
        }

        onClick() {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).audioMgr.playSound("click", 5);

          if (this.selectIdx == (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).pk.selectFusionIdx) {
            (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
              error: Error()
            }), DataMgr) : DataMgr).pk.selectFusionIdx = -1;
          } else {
            (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
              error: Error()
            }), DataMgr) : DataMgr).pk.selectFusionIdx = this.selectIdx;
          }

          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).emit((_crd && DataEvent === void 0 ? (_reportPossibleCrUseOfDataEvent({
            error: Error()
          }), DataEvent) : DataEvent).FusionCellClick, this.selectIdx);
        }

        start() {}

        update(deltaTime) {}

        onDestroy() {}

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "lblAdd", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "spAdd", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "avatarIcon", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "lblName", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "lblScore", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "spPlane", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "bg", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=e83c7a319634418d315d6a60c41849b28529e140.js.map
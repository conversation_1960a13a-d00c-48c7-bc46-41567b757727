{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/BattleManager.ts"], "names": ["BattleManager", "director", "Rect", "log<PERSON>arn", "<PERSON>", "GameConst", "SingletonBase", "UIMgr", "MyApp", "ModeType", "DataMgr", "EventManager", "EventMgr", "HomeUIEvent", "GameReviveUI", "MBoomUI", "LoadingUI", "BottomUI", "HomeUI", "TopUI", "BulletSystem", "GameEvent", "GameIns", "lcgRand", "GameMain", "RogueUI", "GameEnum", "BundleName", "modeConfig", "_modeConfig", "constructor", "initBattleEnd", "isGameStart", "animSpeed", "startInfo", "curLevel", "_loadTotal", "_loadCount", "_rand", "Instance", "on", "onNetGameStart", "onNetGameOver", "startGameByMode", "seed", "fromNumber", "randSeed", "lubanTables", "TbResGameMode", "get", "modeID", "gameLogic", "cmdGameStart", "openUI", "emit", "Leave", "resMgr", "loadBundle", "GameFight", "Promise", "resolve", "gameResManager", "preloadCommon", "console", "log", "mainPlaneManager", "setPlaneData", "mainPlane", "loadScene", "isSectionFinish", "waveManager", "isAllWaveCompleted", "clear", "enemyManager", "boss<PERSON><PERSON><PERSON>", "reset", "gameMapManager", "hurtEffectManager", "gameStateManager", "destroy", "rogueManager", "subReset", "checkLoadFinish", "closeUI", "GameLoadEnd", "initBattle", "addLoadCount", "count", "startLoading", "preload", "preLoad", "chapterID", "planeIn", "init", "ViewBattleWidth", "ViewHeight", "onPlaneIn", "endCallback", "GameMainPlaneIn", "buffIDs", "for<PERSON>ach", "buff<PERSON>", "buff<PERSON><PERSON>p", "A<PERSON><PERSON><PERSON><PERSON>", "beginBattle", "GameStart", "gameStart", "begine", "updateGameLogic", "dt", "isGameOver", "gamePlaneManager", "enemyTarget", "isInBattle", "isGameWillOver", "gameDataManager", "gameTime", "tick", "fColliderManager", "setTouchState", "is<PERSON><PERSON>ch", "setAnimSpeed", "instance", "GameFightUI", "relifeBattle", "gameResume", "revive", "setGameEnd", "isWin", "checkNextlevel", "switchSectionState", "eSECTION_STATE", "FINISH", "startNextBattle", "gamePause", "checkCanRevive", "hpNode", "active", "endBattle", "showGameResult", "cmdGameEnd", "getGameResultData", "getGameLevelResultData", "modeType", "ENDLESS", "chapterConfig", "levelCount", "reInitLevelList", "START", "gameOver", "quitBattle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tip", "bossWillEnter", "bossFightStart", "setFireEnable", "setMoveAble", "random"], "mappings": ";;;6VA4BaA,a;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA3BJC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,I,OAAAA,I;;AACVC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,I;;AACEC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,Y;AAAgBC,MAAAA,Q,iBAAAA,Q;;AACdC,MAAAA,W,kBAAAA,W;;AACAC,MAAAA,Y,kBAAAA,Y;;AACAC,MAAAA,O,kBAAAA,O;;AACAC,MAAAA,S,kBAAAA,S;;AACAC,MAAAA,Q,kBAAAA,Q;;AACAC,MAAAA,M,kBAAAA,M;;AACAC,MAAAA,K,kBAAAA,K;;AACAC,MAAAA,Y,kBAAAA,Y;;AACAC,MAAAA,S,kBAAAA,S;;AACAC,MAAAA,O,kBAAAA,O;;AACAC,MAAAA,O,kBAAAA,O;;AACAC,MAAAA,Q,kBAAAA,Q;;AACAC,MAAAA,O,kBAAAA,O;;AAEAC,MAAAA,Q,kBAAAA,Q;;AACAC,MAAAA,U,kBAAAA,U;;;;;;;;;+BAEI3B,a,GAAN,MAAMA,aAAN;AAAA;AAAA,0CAAyD;AASvC;AAEA,YAAV4B,UAAU,GAAuB;AAAE,iBAAO,KAAKC,WAAZ;AAA0B;;AAMxEC,QAAAA,WAAW,GAAG;AACV;AADU,eAfdC,aAec,GAfW,KAeX;AAAA,eAddC,WAcc,GAdS,KAcT;AAAA,eAbdC,SAac,GAbM,CAaN;AAAA,eAXdC,SAWc,GAXkB,IAWlB;AAAA,eATdL,WASc,GAToB,IASpB;AAAA,eARdM,QAQc,GARK,CAQL;AAAA,eAJdC,UAIc,GAJD,CAIC;AAAA,eAHdC,UAGc,GAHD,CAGC;AAAA,eAFdC,KAEc,GAFG;AAAA;AAAA,mCAEH;AAEV;AAAA;AAAA,4CAAaC,QAAb,CAAsBC,EAAtB,CAAyB;AAAA;AAAA,sCAAUC,cAAnC,EAAmD,KAAKA,cAAxD,EAAwE,IAAxE;AACA;AAAA;AAAA,4CAAaF,QAAb,CAAsBC,EAAtB,CAAyB;AAAA;AAAA,sCAAUE,aAAnC,EAAkD,KAAKA,aAAvD,EAAsE,IAAtE;AACH,SArB2D,CAuB5D;;;AACAC,QAAAA,eAAe,CAACT,SAAD,EAA0B;AACrC,eAAKA,SAAL,GAAiBA,SAAjB;AACA,eAAKI,KAAL,CAAWM,IAAX,GAAkB;AAAA;AAAA,4BAAKC,UAAL,CAAgBX,SAAS,CAACY,QAA1B,CAAlB;AACA,cAAIlB,UAAU,GAAG;AAAA;AAAA,8BAAMmB,WAAN,CAAkBC,aAAlB,CAAgCC,GAAhC,CAAoCf,SAAS,CAACgB,MAA9C,CAAjB;;AACA,cAAItB,UAAU,IAAI,IAAlB,EAAwB;AACpB;AAAA;AAAA,oCAAQ,eAAR,EAA0B,kCAAiCM,SAAS,CAACgB,MAAO,EAA5E;AACA;AACH;;AACD,eAAKrB,WAAL,GAAmBD,UAAnB;AACA,eAAKO,QAAL,GAAgBD,SAAS,CAACC,QAA1B;AAEA;AAAA;AAAA,kCAAQgB,SAAR,CAAkBC,YAAlB,CAA+BlB,SAAS,CAACgB,MAAzC;AACH;;AAEmB,cAAdT,cAAc,GAAG;AACnB,gBAAM;AAAA;AAAA,8BAAMY,MAAN;AAAA;AAAA,qCAAN;AACA;AAAA;AAAA,oCAASC,IAAT,CAAc;AAAA;AAAA,0CAAYC,KAA1B;AACA,gBAAM;AAAA;AAAA,8BAAMC,MAAN,CAAaC,UAAb,CAAwB;AAAA;AAAA,wCAAWC,SAAnC,CAAN;AACA,gBAAM,IAAIC,OAAJ,CAAmBC,OAAD,IAAa;AACjC;AAAA;AAAA,oCAAQC,cAAR,CAAuBC,aAAvB,CAAqC,MAAI;AACrCF,cAAAA,OAAO;AACV,aAFD,EAEG,IAFH;AAGH,WAJK,CAAN;AAKAG,UAAAA,OAAO,CAACC,GAAR,CAAY,qEAAZ;AACA;AAAA;AAAA,kCAAQC,gBAAR,CAAyBC,YAAzB,CAAsC,KAAKhC,SAAL,CAAgBiC,SAAtD;AAEAlE,UAAAA,QAAQ,CAACmE,SAAT,CAAmB,MAAnB;AACH;;AAED1B,QAAAA,aAAa,GAAG,CAEf;;AAED2B,QAAAA,eAAe,GAAG;AACd;AACA,iBAAO;AAAA;AAAA,kCAAQC,WAAR,CAAoBC,kBAA3B;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJ;AAAA;AAAA,kCAAQC,YAAR,CAAqBD,KAArB;AACA;AAAA;AAAA,kCAAQE,WAAR,CAAoBF,KAApB;AACA;AAAA;AAAA,kCAAQF,WAAR,CAAoBK,KAApB;AACA;AAAA;AAAA,kCAAQV,gBAAR,CAAyBO,KAAzB;AACA;AAAA;AAAA,kCAAQI,cAAR,CAAuBJ,KAAvB;AACA;AAAA;AAAA,kCAAQK,iBAAR,CAA0BL,KAA1B;AACA;AAAA;AAAA,kCAAQM,gBAAR,CAAyBH,KAAzB;AACA;AAAA;AAAA,4CAAaI,OAAb;AACA;AAAA;AAAA,kCAAQC,YAAR,CAAqBR,KAArB;AACA,eAAKxC,WAAL,GAAmB,KAAnB;AACH;;AAEDiD,QAAAA,QAAQ,GAAG;AACP,eAAKhD,SAAL,GAAiB,CAAjB;AACA,eAAKD,WAAL,GAAmB,KAAnB;AACA,eAAKD,aAAL,GAAqB,KAArB;AAEA;AAAA;AAAA,kCAAQkC,gBAAR,CAAyBgB,QAAzB;AACA;AAAA;AAAA,kCAAQH,gBAAR,CAAyBH,KAAzB;AACA;AAAA;AAAA,kCAAQL,WAAR,CAAoBK,KAApB;AACA;AAAA;AAAA,kCAAQF,YAAR,CAAqBQ,QAArB;AACA;AAAA;AAAA,kCAAQP,WAAR,CAAoBO,QAApB,GATO,CAUP;AACH;AAED;AACJ;AACA;;;AACyB,cAAfC,eAAe,GAAG;AACpB,eAAK7C,UAAL,GADoB,CAEpB;AACA;;AACA,cAAI,KAAKA,UAAL,IAAmB,KAAKD,UAA5B,EAAwC;AACpC,kBAAM;AAAA;AAAA,gCAAM+C,OAAN;AAAA;AAAA,uCAAN;AACA;AAAA;AAAA,8CAAa5C,QAAb,CAAsBe,IAAtB,CAA2B;AAAA;AAAA,wCAAU8B,WAArC;AACA,iBAAKC,UAAL;AACH;AACJ;;AAEDC,QAAAA,YAAY,CAACC,KAAD,EAAgB;AACxB,eAAKnD,UAAL,IAAmBmD,KAAnB;AACH;;AAEDC,QAAAA,YAAY,GAAG;AACX;AAAA;AAAA,kCAAQR,YAAR,CAAqBL,KAArB;AACA;AAAA;AAAA,kCAAQV,gBAAR,CAAyBwB,OAAzB;AACA;AAAA;AAAA,kCAAQZ,iBAAR,CAA0Ba,OAA1B,GAHW,CAGyB;;AACpC;AAAA;AAAA,kCAAQd,cAAR,CAAuBc,OAAvB,CAA+B,KAAK7D,WAAL,CAAkB8D,SAAjD,EAJW,CAIiD;;AAC5D;AAAA;AAAA,kCAAQlB,YAAR,CAAqBiB,OAArB,GALW,CAKoB;;AAC/B;AAAA;AAAA,kCAAQhB,WAAR,CAAoBgB,OAApB,GANW,CAMmB;AACjC;;AAEDL,QAAAA,UAAU,GAAG;AACT;AAAA;AAAA,kCAAQpB,gBAAR,CAAyBE,SAAzB,CAAoCyB,OAApC;AACA;AAAA;AAAA,4CAAaC,IAAb,CAAkB,IAAI3F,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe;AAAA;AAAA,sCAAU4F,eAAzB,EAA0C;AAAA;AAAA,sCAAUC,UAApD,CAAlB;AACH;;AAEDC,QAAAA,SAAS,GAAG;AACR,cAAIC,WAAW,GAAG,MAAM;AACpB,iBAAKlE,aAAL,GAAqB,IAArB;AACA;AAAA;AAAA,8CAAaQ,QAAb,CAAsBe,IAAtB,CAA2B;AAAA;AAAA,wCAAU4C,eAArC;AACH,WAHD,CADQ,CAKR;;;AACI;AAAA;AAAA,8BAAM7C,MAAN;AAAA;AAAA,kCAAsB,KAAtB,EAA6B8C,OAAD,IAAoB;AAC5CA,YAAAA,OAAO,CAACC,OAAR,CAAgBC,MAAM,IAAI;AACtB;AAAA;AAAA,sCAAQpC,gBAAR,CAAyBE,SAAzB,CAAoCmC,QAApC,CAA6CC,SAA7C,CAAuD,KAAvD,EAA8DF,MAA9D;AACH,aAFD;AAGAJ,YAAAA,WAAW;AACd,WALD,EAKE,CALF,EANI,CAYR;AACA;AACA;AAGH;;AAEDO,QAAAA,WAAW,GAAG;AACV,cAAI,KAAKzE,aAAL,IAAsB,CAAC,KAAKC,WAAhC,EAA6C;AACzC,iBAAKA,WAAL,GAAmB,IAAnB;AACA;AAAA;AAAA,8CAAaO,QAAb,CAAsBe,IAAtB,CAA2B;AAAA;AAAA,wCAAUmD,SAArC;AACA;AAAA;AAAA,oCAAQnC,WAAR,CAAoBoC,SAApB;AACA;AAAA;AAAA,oCAAQ5B,gBAAR,CAAyB4B,SAAzB;AAEA;AAAA;AAAA,oCAAQzC,gBAAR,CAAyBE,SAAzB,CAAoCwC,MAApC;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,eAAe,CAACC,EAAD,EAAa;AACxBA,UAAAA,EAAE,GAAGA,EAAE,GAAG,KAAK5E,SAAf;;AACA,cAAI;AAAA;AAAA,kCAAQ6C,gBAAR,CAAyBgC,UAAzB,EAAJ,EAA2C;AACvC,gBAAI;AAAA;AAAA,oCAAQC,gBAAZ,EAA8B;AAC1B;AAAA;AAAA,sCAAQA,gBAAR,CAAyBC,WAAzB,GAAuC,IAAvC;AACH;;AACD;AACH;;AAED,cAAI;AAAA;AAAA,kCAAQlC,gBAAR,CAAyBmC,UAAzB,MAAyC;AAAA;AAAA,kCAAQnC,gBAAR,CAAyBoC,cAAzB,EAA7C,EAAwF;AACpF;AAAA;AAAA,oCAAQC,eAAR,CAAwBC,QAAxB,IAAoCP,EAApC;AACA;AAAA;AAAA,oCAAQjC,cAAR,CAAuBgC,eAAvB,CAAuCC,EAAvC;AACA;AAAA;AAAA,oCAAQE,gBAAR,CAAyBH,eAAzB,CAAyCC,EAAzC;AACA;AAAA;AAAA,oCAAQ5C,gBAAR,CAAyB2C,eAAzB,CAAyCC,EAAzC;AACA;AAAA;AAAA,oCAAQvC,WAAR,CAAoBsC,eAApB,CAAoCC,EAApC;AACA;AAAA;AAAA,oCAAQpC,YAAR,CAAqBmC,eAArB,CAAqCC,EAArC;AACA;AAAA;AAAA,oCAAQnC,WAAR,CAAoBkC,eAApB,CAAoCC,EAApC;AACA;AAAA;AAAA,oCAAQ/B,gBAAR,CAAyB8B,eAAzB,CAAyCC,EAAzC,EARoF,CAUpF;;AACA;AAAA;AAAA,8CAAaQ,IAAb,CAAkBR,EAAlB;AAEA;AAAA;AAAA,oCAAQS,gBAAR,CAAyBV,eAAzB,CAAyCC,EAAzC;AACH,WAdD,MAcO,IAAI;AAAA;AAAA,kCAAQE,gBAAZ,EAA8B;AACjC;AAAA;AAAA,oCAAQA,gBAAR,CAAyBC,WAAzB,GAAuC,IAAvC;AACH;AACJ;;AAEDO,QAAAA,aAAa,CAACC,OAAD,EAAmB;AAAA;;AAC5B,cAAIA,OAAJ,EAAa;AACT,iBAAKhB,WAAL;AACA,iBAAKvE,SAAL,GAAiB,CAAjB;AACH,WAHD,MAGO;AACH,iBAAKA,SAAL,GAAiB,GAAjB;AACH;;AACD;AAAA;AAAA,kCAAQwC,YAAR,CAAqBgD,YAArB,CAAkC,KAAKxF,SAAvC;AACA;AAAA;AAAA,kCAAQyC,WAAR,CAAoB+C,YAApB,CAAiC,KAAKxF,SAAtC;AACA;AAAA;AAAA,kCAAQgC,gBAAR,CAAyBE,SAAzB,CAAoCsD,YAApC,CAAiD,KAAKxF,SAAtD;AACA;AAAA;AAAA,oCAASyF,QAAT,uBAAmBC,WAAnB,CAAgCJ,aAAhC,CAA8CC,OAA9C;AACH;AAED;AACJ;AACA;;;AACII,QAAAA,YAAY,GAAG;AACX;AAAA;AAAA,kCAAQ9C,gBAAR,CAAyB+C,UAAzB;AACA;AAAA;AAAA,kCAAQ5D,gBAAR,CAAyB6D,MAAzB;AACH;;AAEDC,QAAAA,UAAU,CAACC,KAAD,EAAiB;AACvB,cAAIA,KAAJ,EAAW;AACP,gBAAI,KAAKC,cAAL,EAAJ,EAA2B;AAAC;AACxB;AAAA;AAAA,sCAAQrD,cAAR,CAAuBsD,kBAAvB,CAA0C;AAAA;AAAA,wCAASC,cAAT,CAAwBC,MAAlE;AACA;AAAA;AAAA,kCAAM/E,MAAN;AAAA;AAAA,sCAAuB8C,OAAD,IAAsB;AACxCA,gBAAAA,OAAO,CAACC,OAAR,CAAgBC,MAAM,IAAI;AACtB;AAAA;AAAA,0CAAQpC,gBAAR,CAAyBE,SAAzB,CAAoCmC,QAApC,CAA6CC,SAA7C,CAAuD,KAAvD,EAA8DF,MAA9D;AACH,iBAFD;AAGA,qBAAKgC,eAAL;AACH,eALD;AAMA;AACH;AACJ,WAXD,MAWO;AACH;AAAA;AAAA,oCAAQvD,gBAAR,CAAyBwD,SAAzB;;AACA,gBAAI;AAAA;AAAA,oCAAQrE,gBAAR,CAAyBsE,cAAzB,EAAJ,EAA+C;AAAC;AAC5C;AAAA;AAAA,kCAAMlF,MAAN;AAAA;AAAA;AACA;AACH;AACJ;;AACD;AAAA;AAAA,kCAAQY,gBAAR,CAAyBE,SAAzB,CAAoCqE,MAApC,CAA4CC,MAA5C,GAAqD,KAArD;AACA,eAAKC,SAAL;AACA;AAAA;AAAA,oCAAShB,QAAT,CAAmBiB,cAAnB,CAAkCX,KAAlC;AACA;AAAA;AAAA,kCAAQ7E,SAAR,CAAkByF,UAAlB,CAA6B;AAAA;AAAA,kCAAQzB,eAAR,CAAwB0B,iBAAxB,EAA7B,EAA0E;AAAA;AAAA,kCAAQ1B,eAAR,CAAwB2B,sBAAxB,EAA1E;AACH;;AAEDb,QAAAA,cAAc,GAAG;AACb,cAAI,KAAKpG,WAAL,CAAkBkH,QAAlB,IAA8B;AAAA;AAAA,oCAASC,OAA3C,EAAoD;AAChD;AACA,gBAAI,KAAK7G,QAAL,GAAgB,CAAhB,IAAqB;AAAA;AAAA,oCAAQyC,cAAR,CAAuBqE,aAAvB,CAAsCC,UAA/D,EACA;AACI;AAAA;AAAA,sCAAQtE,cAAR,CAAuBuE,eAAvB;AACH;;AACD,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAKhH,QAAL,GAAgB,CAAhB,IAAqB;AAAA;AAAA,kCAAQyC,cAAR,CAAuBqE,aAAvB,CAAsCC,UAAlE;AACH;AACD;AACJ;AACA;;;AACIb,QAAAA,eAAe,GAAG;AACd,eAAKpD,QAAL;AACA,eAAK9C,QAAL,IAAiB,CAAjB;AACA,eAAKkD,UAAL;AACA;AAAA;AAAA,kCAAQT,cAAR,CAAuBsD,kBAAvB,CAA0C;AAAA;AAAA,oCAASC,cAAT,CAAwBiB,KAAlE;AACH;AAED;AACJ;AACA;;;AACIV,QAAAA,SAAS,GAAG;AAAA;;AACR;AAAA;AAAA,4CAAa3D,OAAb,CAAqB,KAArB,EAA4B,KAA5B;AACA;AAAA;AAAA,oCAAS2C,QAAT,wBAAmBC,WAAnB,CAAgChD,KAAhC;AACA;AAAA;AAAA,kCAAQG,gBAAR,CAAyBuE,QAAzB;AACH;;AAGe,cAAVC,UAAU,GAAG;AACf,eAAK9E,KAAL;AACA;AAAA;AAAA,8BAAMW,OAAN;AAAA;AAAA;AACA,gBAAM;AAAA;AAAA,8BAAM9B,MAAN;AAAA;AAAA,+BAAN;AACA,gBAAM;AAAA;AAAA,8BAAMA,MAAN;AAAA;AAAA,mCAAN;AACA,gBAAM;AAAA;AAAA,8BAAMA,MAAN;AAAA;AAAA,6BAAN;AACApD,UAAAA,QAAQ,CAACmE,SAAT,CAAmB,MAAnB;AACH;;AAEDmF,QAAAA,gBAAgB,CAACC,GAAD,EAAc,CAC1B;AACA;AACA;AACA;AACA;AACA;AACH;;AAEDC,QAAAA,aAAa,GAAG,CACZ;AACA;AACH;AACD;AACJ;AACA;;;AACIC,QAAAA,cAAc,GAAG;AACb;AAAA;AAAA,kCAAQzF,gBAAR,CAAyBE,SAAzB,CAAoCwF,aAApC,CAAkD,IAAlD;AACA;AAAA;AAAA,kCAAQ1F,gBAAR,CAAyBE,SAAzB,CAAoCyF,WAApC,CAAgD,IAAhD;AACA;AAAA;AAAA,kCAAQlF,WAAR,CAAoBgF,cAApB;AACH;;AAEDG,QAAAA,MAAM,GAAW;AACb,iBAAO,KAAKvH,KAAL,CAAWuH,MAAX,EAAP;AACH;;AApS2D,O", "sourcesContent": ["\r\nimport { director, Rect } from \"cc\";\r\nimport { logWarn } from \"db://assets/scripts/utils/Logger\";\r\nimport Long from \"long\";\r\nimport { GameConst } from \"../../../../../scripts/core/base/GameConst\";\r\nimport { SingletonBase } from \"../../../../../scripts/core/base/SingletonBase\";\r\nimport { UIMgr } from \"../../../../../scripts/core/base/UIMgr\";\r\nimport { MyApp } from \"../../app/MyApp\";\r\nimport { ModeType, ResGameMode } from \"../../autogen/luban/schema\";\r\nimport { DataMgr } from \"../../data/DataManager\";\r\nimport EventManager, { EventMgr } from \"../../event/EventManager\";\r\nimport { HomeUIEvent } from \"../../event/HomeUIEvent\";\r\nimport { GameReviveUI } from \"../../ui/gameui/game/GameReviveUI\";\r\nimport { MBoomUI } from \"../../ui/gameui/game/MBoomUI\";\r\nimport { LoadingUI } from \"../../ui/gameui/LoadingUI\";\r\nimport { BottomUI } from \"../../ui/home/<USER>\";\r\nimport { HomeUI } from \"../../ui/home/<USER>\";\r\nimport { TopUI } from \"../../ui/home/<USER>\";\r\nimport { BulletSystem } from \"../bullet/BulletSystem\";\r\nimport { GameEvent } from \"../event/GameEvent\";\r\nimport { GameIns } from \"../GameIns\";\r\nimport { lcgRand } from \"../utils/Rand\";\r\nimport { GameMain } from \"../scenes/GameMain\";\r\nimport { RogueUI } from \"../../ui/gameui/game/RogueUI\";\r\nimport { GameStartInfo } from \"./GameStartInfo\";\r\nimport { GameEnum } from \"../const/GameEnum\";\r\nimport { BundleName } from \"../../const/BundleConst\";\r\n\r\nexport class BattleManager extends SingletonBase<BattleManager> {\r\n\r\n    initBattleEnd: boolean = false;\r\n    isGameStart: boolean = false;\r\n    animSpeed: number = 1;\r\n\r\n    startInfo: GameStartInfo|null = null;\r\n\r\n    _modeConfig: ResGameMode | null = null;\r\n    curLevel: number = 0;//小阶段\r\n\r\n    public get modeConfig(): ResGameMode | null { return this._modeConfig; }\r\n\r\n    _loadTotal = 0;\r\n    _loadCount = 0;\r\n    _rand: lcgRand = new lcgRand();\r\n\r\n    constructor() {\r\n        super();\r\n        EventManager.Instance.on(GameEvent.onNetGameStart, this.onNetGameStart, this);\r\n        EventManager.Instance.on(GameEvent.onNetGameOver, this.onNetGameOver, this);\r\n    }\r\n\r\n    //战斗开始接口\r\n    startGameByMode(startInfo:GameStartInfo) {\r\n        this.startInfo = startInfo;\r\n        this._rand.seed = Long.fromNumber(startInfo.randSeed);\r\n        let modeConfig = MyApp.lubanTables.TbResGameMode.get(startInfo.modeID);\r\n        if (modeConfig == null) {\r\n            logWarn(\"BattleManager\", `can not find mode config by id ${startInfo.modeID}`);\r\n            return;\r\n        }\r\n        this._modeConfig = modeConfig;\r\n        this.curLevel = startInfo.curLevel;\r\n\r\n        DataMgr.gameLogic.cmdGameStart(startInfo.modeID);\r\n    }\r\n\r\n    async onNetGameStart() {\r\n        await UIMgr.openUI(LoadingUI);\r\n        EventMgr.emit(HomeUIEvent.Leave);\r\n        await MyApp.resMgr.loadBundle(BundleName.GameFight);\r\n        await new Promise<void>((resolve) => {\r\n            GameIns.gameResManager.preloadCommon(()=>{\r\n                resolve();\r\n            }, null)\r\n        })\r\n        console.log(\"ybgg BattleManager onNetGameStart gameResManager preloadCommon done\");\r\n        GameIns.mainPlaneManager.setPlaneData(this.startInfo!.mainPlane);\r\n\r\n        director.loadScene(\"Game\");\r\n    }\r\n\r\n    onNetGameOver() {\r\n\r\n    }\r\n\r\n    isSectionFinish() {\r\n        // todo\r\n        return GameIns.waveManager.isAllWaveCompleted;\r\n    }\r\n\r\n    clear() {\r\n        GameIns.enemyManager.clear();\r\n        GameIns.bossManager.clear();\r\n        GameIns.waveManager.reset();\r\n        GameIns.mainPlaneManager.clear();\r\n        GameIns.gameMapManager.clear();\r\n        GameIns.hurtEffectManager.clear();\r\n        GameIns.gameStateManager.reset();\r\n        BulletSystem.destroy();\r\n        GameIns.rogueManager.clear();\r\n        this.isGameStart = false;\r\n    }\r\n\r\n    subReset() {\r\n        this.animSpeed = 1;\r\n        this.isGameStart = false;\r\n        this.initBattleEnd = false;\r\n\r\n        GameIns.mainPlaneManager.subReset();\r\n        GameIns.gameStateManager.reset();\r\n        GameIns.waveManager.reset();\r\n        GameIns.enemyManager.subReset();\r\n        GameIns.bossManager.subReset();\r\n        //GameIns.gameMapManager.reset();\r\n    }\r\n\r\n    /**\r\n     * 检查所有资源是否加载完成\r\n     */\r\n    async checkLoadFinish() {\r\n        this._loadCount++;\r\n        // let loadingUI = UIMgr.get(LoadingUI)\r\n        // loadingUI.updateProgress(this._loadCount / this._loadTotal)\r\n        if (this._loadCount >= this._loadTotal) {\r\n            await UIMgr.closeUI(LoadingUI)\r\n            EventManager.Instance.emit(GameEvent.GameLoadEnd)\r\n            this.initBattle();\r\n        }\r\n    }\r\n\r\n    addLoadCount(count: number) {\r\n        this._loadTotal += count;\r\n    }\r\n\r\n    startLoading() {\r\n        GameIns.rogueManager.reset();\r\n        GameIns.mainPlaneManager.preload();\r\n        GameIns.hurtEffectManager.preLoad();//伤害特效资源\r\n        GameIns.gameMapManager.preLoad(this._modeConfig!.chapterID);//地图资源\r\n        GameIns.enemyManager.preLoad();//敌人资源\r\n        GameIns.bossManager.preLoad();//boss资源\r\n    }\r\n\r\n    initBattle() {\r\n        GameIns.mainPlaneManager.mainPlane!.planeIn();\r\n        BulletSystem.init(new Rect(0, 0, GameConst.ViewBattleWidth, GameConst.ViewHeight));\r\n    }\r\n\r\n    onPlaneIn() {\r\n        let endCallback = () => {\r\n            this.initBattleEnd = true;\r\n            EventManager.Instance.emit(GameEvent.GameMainPlaneIn)\r\n        }\r\n        // if (this._modeConfig!.rogueFirst > 0){\r\n            UIMgr.openUI(RogueUI, 10001,(buffIDs:number[])=>{\r\n                buffIDs.forEach(buffID => {\r\n                    GameIns.mainPlaneManager.mainPlane!.buffComp.ApplyBuff(false, buffID);\r\n                })\r\n                endCallback();\r\n            },3)\r\n        // }else{\r\n        //     endCallback();\r\n        // }\r\n\r\n\r\n    }\r\n\r\n    beginBattle() {\r\n        if (this.initBattleEnd && !this.isGameStart) {\r\n            this.isGameStart = true;\r\n            EventManager.Instance.emit(GameEvent.GameStart)\r\n            GameIns.waveManager.gameStart();\r\n            GameIns.gameStateManager.gameStart();\r\n\r\n            GameIns.mainPlaneManager.mainPlane!.begine();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param {number} dt 每帧的时间间隔\r\n     */\r\n    updateGameLogic(dt: number) {\r\n        dt = dt * this.animSpeed;\r\n        if (GameIns.gameStateManager.isGameOver()) {\r\n            if (GameIns.gamePlaneManager) {\r\n                GameIns.gamePlaneManager.enemyTarget = null;\r\n            }\r\n            return;\r\n        }\r\n\r\n        if (GameIns.gameStateManager.isInBattle() || GameIns.gameStateManager.isGameWillOver()) {\r\n            GameIns.gameDataManager.gameTime += dt;\r\n            GameIns.gameMapManager.updateGameLogic(dt)\r\n            GameIns.gamePlaneManager.updateGameLogic(dt);\r\n            GameIns.mainPlaneManager.updateGameLogic(dt);\r\n            GameIns.waveManager.updateGameLogic(dt);\r\n            GameIns.enemyManager.updateGameLogic(dt);\r\n            GameIns.bossManager.updateGameLogic(dt);\r\n            GameIns.gameStateManager.updateGameLogic(dt);\r\n\r\n            //子弹发射器系统\r\n            BulletSystem.tick(dt);\r\n\r\n            GameIns.fColliderManager.updateGameLogic(dt);\r\n        } else if (GameIns.gamePlaneManager) {\r\n            GameIns.gamePlaneManager.enemyTarget = null;\r\n        }\r\n    }\r\n\r\n    setTouchState(isTouch: boolean) {\r\n        if (isTouch) {\r\n            this.beginBattle();\r\n            this.animSpeed = 1;\r\n        } else {\r\n            this.animSpeed = 0.2;\r\n        }\r\n        GameIns.enemyManager.setAnimSpeed(this.animSpeed);\r\n        GameIns.bossManager.setAnimSpeed(this.animSpeed);\r\n        GameIns.mainPlaneManager.mainPlane!.setAnimSpeed(this.animSpeed);\r\n        GameMain.instance?.GameFightUI!.setTouchState(isTouch);\r\n    }\r\n\r\n    /**\r\n     * 战斗复活逻辑\r\n     */\r\n    relifeBattle() {\r\n        GameIns.gameStateManager.gameResume();\r\n        GameIns.mainPlaneManager.revive();\r\n    }\r\n\r\n    setGameEnd(isWin: boolean) {\r\n        if (isWin) {\r\n            if (this.checkNextlevel()) {//判断是否有下一关\r\n                GameIns.gameMapManager.switchSectionState(GameEnum.eSECTION_STATE.FINISH);\r\n                UIMgr.openUI(RogueUI, (buffIDs:number[]) => {\r\n                    buffIDs.forEach(buffID => {\r\n                        GameIns.mainPlaneManager.mainPlane!.buffComp.ApplyBuff(false, buffID);\r\n                    })\r\n                    this.startNextBattle();\r\n                });\r\n                return;\r\n            }\r\n        } else {\r\n            GameIns.gameStateManager.gamePause();\r\n            if (GameIns.mainPlaneManager.checkCanRevive()) {// 判断是否可以复活\r\n                UIMgr.openUI(GameReviveUI);\r\n                return;\r\n            }\r\n        }\r\n        GameIns.mainPlaneManager.mainPlane!.hpNode!.active = false;\r\n        this.endBattle();\r\n        GameMain.instance!.showGameResult(isWin);\r\n        DataMgr.gameLogic.cmdGameEnd(GameIns.gameDataManager.getGameResultData(), GameIns.gameDataManager.getGameLevelResultData());\r\n    }\r\n\r\n    checkNextlevel() {\r\n        if (this._modeConfig!.modeType == ModeType.ENDLESS) {\r\n            // 如果关卡全部跑完，重新随机一次关卡列表\r\n            if (this.curLevel + 1 >= GameIns.gameMapManager.chapterConfig!.levelCount) \r\n            {\r\n                GameIns.gameMapManager.reInitLevelList();\r\n            }\r\n            return true;\r\n        }\r\n        return this.curLevel + 1 <= GameIns.gameMapManager.chapterConfig!.levelCount;\r\n    }\r\n    /**\r\n     * 继续下一场战斗\r\n     */\r\n    startNextBattle() {\r\n        this.subReset();\r\n        this.curLevel += 1;\r\n        this.initBattle();\r\n        GameIns.gameMapManager.switchSectionState(GameEnum.eSECTION_STATE.START);\r\n    }\r\n\r\n    /**\r\n     * 结束战斗\r\n     */\r\n    endBattle() {\r\n        BulletSystem.destroy(false, false);\r\n        GameMain.instance?.GameFightUI!.reset();\r\n        GameIns.gameStateManager.gameOver();\r\n    }\r\n\r\n\r\n    async quitBattle() {\r\n        this.clear();\r\n        UIMgr.closeUI(MBoomUI)\r\n        await UIMgr.openUI(HomeUI)\r\n        await UIMgr.openUI(BottomUI)\r\n        await UIMgr.openUI(TopUI)\r\n        director.loadScene(\"Main\");\r\n    }\r\n\r\n    bossChangeFinish(tip: string) {\r\n        // const bossEnterDialog = GameIns.uiManager.getDialog(BossEnterDialog.default);\r\n        // if (bossEnterDialog) {\r\n        //     bossEnterDialog.node.active = true;\r\n        //     GameIns.mainPlaneManager.moveAble = false;\r\n        //     bossEnterDialog.showTips(bossName);\r\n        // }\r\n    }\r\n\r\n    bossWillEnter() {\r\n        // GameIns.mainPlaneManager.mainPlane!.setFireEnable(false);\r\n        // GameIns.mainPlaneManager.moveAble = false;\r\n    }\r\n    /**\r\n     * 开始Boss战斗\r\n     */\r\n    bossFightStart() {\r\n        GameIns.mainPlaneManager.mainPlane!.setFireEnable(true);\r\n        GameIns.mainPlaneManager.mainPlane!.setMoveAble(true);\r\n        GameIns.bossManager.bossFightStart();\r\n    }\r\n\r\n    random(): number {\r\n        return this._rand.random();\r\n    }\r\n}"]}
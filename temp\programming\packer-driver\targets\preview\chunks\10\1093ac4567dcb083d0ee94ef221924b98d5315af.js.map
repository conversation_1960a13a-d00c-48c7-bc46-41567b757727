{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/mail/MailCellUI.ts"], "names": ["_decorator", "<PERSON><PERSON>", "Component", "Label", "Sprite", "MyApp", "MessageBox", "DataMgr", "ccclass", "property", "MailCellUI", "itemID", "mailInfo", "start", "update", "deltaTime", "onButtonClick", "show", "mail", "cmdMailGetAttachment", "guid", "cmdMailDelete", "setData", "attachments", "length", "item_id", "item", "lubanTables", "TbResItem", "get", "mailTitle", "string", "title"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;;AACtCC,MAAAA,K,iBAAAA,K;;AAGAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,O,iBAAAA,O;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;4BAGjBU,U,WADZF,OAAO,CAAC,YAAD,C,UAGHC,QAAQ,CAACL,MAAD,C,UAGRK,QAAQ,CAACN,KAAD,C,UAGRM,QAAQ,CAACN,KAAD,C,UAGRM,QAAQ,CAACR,MAAD,C,2BAZb,MACaS,UADb,SACgCR,SADhC,CAC0C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eActCS,MAdsC,GAcrB,CAdqB;AAAA,eAgBtCC,QAhBsC,GAgBA,IAhBA;AAAA;;AAkBtCC,QAAAA,KAAK,GAAG,CAEP;;AAEDC,QAAAA,MAAM,CAACC,SAAD,EAAoB,CAEzB;;AACDC,QAAAA,aAAa,GAAG;AACZ;AAAA;AAAA,wCAAWC,IAAX,CAAgB,UAAU,KAAKN,MAA/B;;AACA,cAAI,KAAKA,MAAL,GAAc,CAAlB,EAAqB;AACjB;AAAA;AAAA,oCAAQO,IAAR,CAAaC,oBAAb,CAAkC,CAAC,KAAKP,QAAL,CAAeQ,IAAhB,CAAlC;AACH,WAFD,MAEO;AACH;AAAA;AAAA,oCAAQF,IAAR,CAAaG,aAAb,CAA2B,CAAC,KAAKT,QAAL,CAAeQ,IAAhB,CAA3B;AACH;AACJ;;AACME,QAAAA,OAAO,CAACV,QAAD,EAAqC;AAC/C,eAAKA,QAAL,GAAgBA,QAAhB;;AACA,cAAI,CAACA,QAAQ,CAACW,WAAV,IAAyBX,QAAQ,CAACW,WAAT,CAAqBC,MAArB,KAAgC,CAA7D,EAAgE;AAC5D,iBAAKb,MAAL,GAAc,CAAd;AACH,WAFD,MAEO;AACH,iBAAKA,MAAL,GAAcC,QAAQ,CAACW,WAAT,CAAsB,CAAtB,EAAyBE,OAAvC;AACH;;AACD,cAAIC,IAAyB,GAAG;AAAA;AAAA,8BAAMC,WAAN,CAAkBC,SAAlB,CAA4BC,GAA5B,CAAgC,KAAKlB,MAArC,CAAhC;AACA,eAAKmB,SAAL,CAAgBC,MAAhB,GAAyB,CAAAnB,QAAQ,QAAR,YAAAA,QAAQ,CAAEoB,KAAV,KAAmB,EAA5C;AACH;;AA1CqC,O;;;;;iBAGZ,I;;;;;;;iBAGA,I;;;;;;;iBAGE,I;;;;;;;iBAGF,I", "sourcesContent": ["import { _decorator, Button, Component, Label, Sprite } from 'cc';\r\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\r\nimport { ResItem } from 'db://assets/bundles/common/script/autogen/luban/schema';\r\nimport csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';\r\nimport { MessageBox } from 'db://assets/bundles/common/script/ui/common/components/messagebox/MessageBox';\r\nimport { DataMgr } from '../../data/DataManager';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('MailCellUI')\r\nexport class MailCellUI extends Component {\r\n\r\n    @property(Sprite)\r\n    mailIcon: Sprite | null = null;\r\n\r\n    @property(Label)\r\n    mailTitle: Label | null = null;\r\n\r\n    @property(Label)\r\n    mailContent: Label | null = null;\r\n\r\n    @property(Button)\r\n    btnClick: Button | null = null;\r\n\r\n    itemID: number = 0;\r\n\r\n    mailInfo: csproto.comm.IMail | null = null;\r\n\r\n    start() {\r\n\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n\r\n    }\r\n    onButtonClick() {\r\n        MessageBox.show('物品ID：' + this.itemID);\r\n        if (this.itemID > 0) {\r\n            DataMgr.mail.cmdMailGetAttachment([this.mailInfo!.guid!]);\r\n        } else {\r\n            DataMgr.mail.cmdMailDelete([this.mailInfo!.guid!]);\r\n        }\r\n    }\r\n    public setData(mailInfo: csproto.comm.IMail): void {\r\n        this.mailInfo = mailInfo;\r\n        if (!mailInfo.attachments || mailInfo.attachments.length === 0) {\r\n            this.itemID = 0;\r\n        } else {\r\n            this.itemID = mailInfo.attachments![0].item_id!;\r\n        }\r\n        let item: ResItem | undefined = MyApp.lubanTables.TbResItem.get(this.itemID);\r\n        this.mailTitle!.string = mailInfo?.title || \"\";\r\n    }\r\n}\r\n\r\n\r\n"]}
{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/RogueManager.ts"], "names": ["RogueManager", "instantiate", "Prefab", "NodePool", "SingletonBase", "MyApp", "RogueItem", "BundleName", "constructor", "_rougeGroupConfig", "_selectedHistory", "Map", "_typeSelectedCount", "_rogueItemPool", "initRougeGroupConfig", "list", "lubanTables", "TbResWordGroup", "getDataList", "v", "GroupID", "push", "reset", "clear", "getSeLectedHistory", "setRogueItem", "parent", "wordId", "lv", "rogue", "size", "node", "get", "getComponent", "getRogueItem", "initRogueById", "rogueItem", "resMgr", "loadAsync", "GameFight", "recycleRogueItems", "parentNode", "i", "children", "length", "childNode", "put", "clearRogueItemPool", "randomWords", "groupId", "count", "console", "warn", "priorityGroups", "groupByPriority", "result", "remainingCount", "priorities", "Object", "keys", "map", "Number", "sort", "a", "b", "priority", "availableWords", "getAvailableWords", "wordsForThisPriority", "randomFromPriorityGroupOneByOne", "Math", "min", "words", "groups", "word", "filter", "times", "selectedCount", "ID", "shuffle<PERSON><PERSON><PERSON>", "selected", "weightedWords", "weight", "type", "typWeightFix", "typeSelectedCount", "<PERSON><PERSON><PERSON>", "selectByWeight", "index", "findIndex", "w", "splice", "totalWeight", "reduce", "sum", "item", "random", "array", "shuffled", "j", "floor", "selectRogue", "<PERSON><PERSON><PERSON><PERSON>", "updateSelectionHistory", "currentCount", "set", "typeCount", "getWordSelectionCount", "getTypeSelectionCount", "wordType", "coverToBuffIds", "wordGroupId", "buffIDs", "for<PERSON>ach", "key", "config", "wordConfig", "TbResWord", "buff<PERSON>"], "mappings": ";;;2JAYaA,Y;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAJJC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,M,OAAAA,M;AAAcC,MAAAA,Q,OAAAA,Q;;AAH3BC,MAAAA,a,iBAAAA,a;;AAEAC,MAAAA,K,iBAAAA,K;;AAEAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,U,iBAAAA,U;;;;;;AATT;AACA;AACA;;;;;8BASaP,Y,GAAN,MAAMA,YAAN;AAAA;AAAA,0CAAuD;AAO1DQ,QAAAA,WAAW,GAAG;AACV;AADU,eANdC,iBAMc,GAN0C,EAM1C;AAAA,eAJNC,gBAIM,GAJkC,IAAIC,GAAJ,EAIlC;AAJ6C;AAI7C,eAHNC,kBAGM,GAHoC,IAAID,GAAJ,EAGpC;AAH+C;AAG/C,eAFNE,cAEM,GAFqB,IAAIV,QAAJ,EAErB;AAEV,eAAKW,oBAAL;AACH;;AAEDA,QAAAA,oBAAoB,GAAG;AACnB,cAAIC,IAAI,GAAG;AAAA;AAAA,8BAAMC,WAAN,CAAkBC,cAAlB,CAAiCC,WAAjC,EAAX;;AACA,eAAK,IAAIC,CAAT,IAAcJ,IAAd,EAAoB;AAChB,gBAAI,CAAC,KAAKN,iBAAL,CAAuBU,CAAC,CAACC,OAAzB,CAAL,EAAwC;AACpC,mBAAKX,iBAAL,CAAuBU,CAAC,CAACC,OAAzB,IAAoC,EAApC;AACH;;AACD,iBAAKX,iBAAL,CAAuBU,CAAC,CAACC,OAAzB,EAAkCC,IAAlC,CAAuCF,CAAvC;AACH;AACJ;;AAEMG,QAAAA,KAAK,GAAS;AACjB,eAAKZ,gBAAL,CAAsBa,KAAtB;;AACA,eAAKX,kBAAL,CAAwBW,KAAxB;AACH;;AAEDA,QAAAA,KAAK,GAAE;AACH,eAAKD,KAAL;;AACA,eAAKT,cAAL,CAAoBU,KAApB;AACH;;AAEDC,QAAAA,kBAAkB,GAAwB;AACtC,iBAAO,KAAKd,gBAAZ;AACH;;AAEiB,cAAZe,YAAY,CAACC,MAAD,EAAeC,MAAf,EAA+BC,EAAU,GAAG,CAA5C,EAA+C;AAC7D,cAAIC,KAAJ,CAD6D,CAE7D;;AACA,cAAI,KAAKhB,cAAL,CAAoBiB,IAApB,KAA6B,CAAjC,EAAoC;AAChC,kBAAMC,IAAI,GAAG,KAAKlB,cAAL,CAAoBmB,GAApB,EAAb;;AACAH,YAAAA,KAAK,GAAGE,IAAI,CAAEE,YAAN;AAAA;AAAA,uCAAR;AACH,WAHD,MAGO;AACH;AACAJ,YAAAA,KAAK,GAAG,MAAM,KAAKK,YAAL,EAAd;AACH;;AAEDL,UAAAA,KAAK,CAACE,IAAN,CAAWL,MAAX,GAAoBA,MAApB;AACAG,UAAAA,KAAK,CAACM,aAAN,CAAoBR,MAApB,EAA4BC,EAA5B;AACH;;AAEiB,cAAZM,YAAY,GAAG;AACjB,cAAIE,SAAS,GAAG,MAAM;AAAA;AAAA,8BAAMC,MAAN,CAAaC,SAAb,CAAuB;AAAA;AAAA,wCAAWC,SAAlC,EAA4C,kBAA5C,EAAgErC,MAAhE,CAAtB;AACA,cAAI6B,IAAI,GAAG9B,WAAW,CAACmC,SAAD,CAAtB;AACA,iBAAOL,IAAI,CAAEE,YAAN;AAAA;AAAA,qCAAP;AACH;;AACMO,QAAAA,iBAAiB,CAACC,UAAD,EAAyB;AAC7C,cAAI,CAACA,UAAL,EAAiB;;AAEjB,eAAK,IAAIC,CAAC,GAAGD,UAAU,CAACE,QAAX,CAAoBC,MAApB,GAA6B,CAA1C,EAA6CF,CAAC,IAAI,CAAlD,EAAqDA,CAAC,EAAtD,EAA0D;AACtD,kBAAMG,SAAS,GAAGJ,UAAU,CAACE,QAAX,CAAoBD,CAApB,CAAlB;AACA,kBAAMN,SAAS,GAAGS,SAAS,CAACZ,YAAV;AAAA;AAAA,uCAAlB;;AAEA,gBAAIG,SAAJ,EAAe;AACXA,cAAAA,SAAS,CAACd,KAAV,YAAAc,SAAS,CAACd,KAAV;;AACA,mBAAKT,cAAL,CAAoBiC,GAApB,CAAwBD,SAAxB;AACH;AACJ;AACJ;;AAEME,QAAAA,kBAAkB,GAAS;AAC9B,eAAKlC,cAAL,CAAoBU,KAApB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACWyB,QAAAA,WAAW,CAACC,OAAD,EAAkBC,KAAa,GAAG,CAAlC,EAAsD;AACpE,gBAAMnC,IAAI,GAAG,KAAKN,iBAAL,CAAuBwC,OAAvB,CAAb;;AACA,cAAI,CAAClC,IAAD,IAASA,IAAI,CAAC6B,MAAL,KAAgB,CAA7B,EAAgC;AAC5BO,YAAAA,OAAO,CAACC,IAAR,CAAc,OAAMH,OAAQ,SAA5B;AACA,mBAAO,EAAP;AACH,WALmE,CAOpE;;;AACA,gBAAMI,cAAc,GAAG,KAAKC,eAAL,CAAqBvC,IAArB,CAAvB;AAEA,gBAAMwC,MAAuB,GAAG,EAAhC;AACA,cAAIC,cAAc,GAAGN,KAArB,CAXoE,CAapE;;AACA,gBAAMO,UAAU,GAAGC,MAAM,CAACC,IAAP,CAAYN,cAAZ,EAA4BO,GAA5B,CAAgCC,MAAhC,EAAwCC,IAAxC,CAA6C,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,GAAGC,CAA3D,CAAnB;;AAEA,eAAK,MAAMC,QAAX,IAAuBR,UAAvB,EAAmC;AAC/B,gBAAID,cAAc,IAAI,CAAtB,EAAyB;AAEzB,kBAAMU,cAAc,GAAG,KAAKC,iBAAL,CAAuBd,cAAc,CAACY,QAAD,CAArC,CAAvB;AACA,gBAAIC,cAAc,CAACtB,MAAf,KAA0B,CAA9B,EAAiC,SAJF,CAM/B;;AACA,kBAAMwB,oBAAoB,GAAG,KAAKC,+BAAL,CACzBH,cADyB,EAEzBI,IAAI,CAACC,GAAL,CAASf,cAAT,EAAyBU,cAAc,CAACtB,MAAxC,CAFyB,CAA7B;AAKAW,YAAAA,MAAM,CAAClC,IAAP,CAAY,GAAG+C,oBAAf;AACAZ,YAAAA,cAAc,IAAIY,oBAAoB,CAACxB,MAAvC;AACH;;AAED,iBAAOW,MAAP;AACH;AAED;AACJ;AACA;;;AACYD,QAAAA,eAAe,CAACkB,KAAD,EAAkE;AACrF,gBAAMC,MAA+C,GAAG,EAAxD;;AAEA,eAAK,MAAMC,IAAX,IAAmBF,KAAnB,EAA0B;AACtB,gBAAI,CAACC,MAAM,CAACC,IAAI,CAACT,QAAN,CAAX,EAA4B;AACxBQ,cAAAA,MAAM,CAACC,IAAI,CAACT,QAAN,CAAN,GAAwB,EAAxB;AACH;;AACDQ,YAAAA,MAAM,CAACC,IAAI,CAACT,QAAN,CAAN,CAAsB5C,IAAtB,CAA2BqD,IAA3B;AACH;;AAED,iBAAOD,MAAP;AACH;AAED;AACJ;AACA;;;AACYN,QAAAA,iBAAiB,CAACK,KAAD,EAA0C;AAC/D,iBAAOA,KAAK,CAACG,MAAN,CAAaD,IAAI,IAAI;AACxB;AACA,gBAAIA,IAAI,CAACE,KAAL,KAAe,CAAC,CAApB,EAAuB,OAAO,IAAP,CAFC,CAEY;;AACpC,gBAAIF,IAAI,CAACE,KAAL,KAAe,CAAnB,EAAsB,OAAO,KAAP,CAHE,CAGY;;AACpC,kBAAMC,aAAa,GAAG,KAAKnE,gBAAL,CAAsBsB,GAAtB,CAA0B0C,IAAI,CAACI,EAA/B,KAAsC,CAA5D;AACA,mBAAOD,aAAa,GAAGH,IAAI,CAACE,KAA5B;AACH,WANM,CAAP;AAOH;AAED;AACJ;AACA;;;AACYP,QAAAA,+BAA+B,CAACG,KAAD,EAAyBtB,KAAzB,EAAyD;AAC5F,cAAIsB,KAAK,CAAC5B,MAAN,IAAgBM,KAApB,EAA2B;AACvB;AACA,mBAAO,KAAK6B,YAAL,CAAkB,CAAC,GAAGP,KAAJ,CAAlB,CAAP;AACH;;AAED,gBAAMQ,QAAyB,GAAG,EAAlC;AACA,gBAAMd,cAAc,GAAG,CAAC,GAAGM,KAAJ,CAAvB;;AAEA,eAAK,IAAI9B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGQ,KAAJ,IAAagB,cAAc,CAACtB,MAAf,GAAwB,CAArD,EAAwDF,CAAC,EAAzD,EAA6D;AACzD;AACA,kBAAMuC,aAAa,GAAGf,cAAc,CAACN,GAAf,CAAmBc,IAAI,IAAI;AAC7C,kBAAIQ,MAAM,GAAGR,IAAI,CAACQ,MAAlB,CAD6C,CAG7C;;AACA,kBAAIR,IAAI,CAACS,IAAL,IAAaT,IAAI,CAACU,YAAtB,EAAoC;AAChC,sBAAMC,iBAAiB,GAAG,KAAKzE,kBAAL,CAAwBoB,GAAxB,CAA4B0C,IAAI,CAACS,IAAjC,KAA0C,CAApE;;AACA,oBAAIE,iBAAiB,GAAG,CAAxB,EAA2B;AACvBH,kBAAAA,MAAM,IAAIR,IAAI,CAACU,YAAf;AACH;AACJ;;AAED,qBAAO;AAAEV,gBAAAA,IAAF;AAAQQ,gBAAAA;AAAR,eAAP;AACH,aAZqB,CAAtB,CAFyD,CAgBzD;;AACA,kBAAMI,YAAY,GAAG,KAAKC,cAAL,CAAoBN,aAApB,CAArB;;AACA,gBAAIK,YAAJ,EAAkB;AACdN,cAAAA,QAAQ,CAAC3D,IAAT,CAAciE,YAAd,EADc,CAGd;;AACA,oBAAME,KAAK,GAAGtB,cAAc,CAACuB,SAAf,CAAyBC,CAAC,IAAIA,CAAC,CAACZ,EAAF,KAASQ,YAAY,CAACR,EAApD,CAAd;;AACA,kBAAIU,KAAK,KAAK,CAAC,CAAf,EAAkB;AACdtB,gBAAAA,cAAc,CAACyB,MAAf,CAAsBH,KAAtB,EAA6B,CAA7B;AACH;AACJ;AACJ;;AAED,iBAAOR,QAAP;AACH;AAED;AACJ;AACA;;;AACYO,QAAAA,cAAc,CAACN,aAAD,EAAiF;AACnG,cAAIA,aAAa,CAACrC,MAAd,KAAyB,CAA7B,EAAgC,OAAO,IAAP;AAEhC,gBAAMgD,WAAW,GAAGX,aAAa,CAACY,MAAd,CAAqB,CAACC,GAAD,EAAMC,IAAN,KAAeD,GAAG,GAAGC,IAAI,CAACb,MAA/C,EAAuD,CAAvD,CAApB;AACA,cAAIc,MAAM,GAAG1B,IAAI,CAAC0B,MAAL,KAAgBJ,WAA7B;;AAEA,eAAK,MAAMG,IAAX,IAAmBd,aAAnB,EAAkC;AAC9Be,YAAAA,MAAM,IAAID,IAAI,CAACb,MAAf;;AACA,gBAAIc,MAAM,IAAI,CAAd,EAAiB;AACb,qBAAOD,IAAI,CAACrB,IAAZ;AACH;AACJ,WAXkG,CAanG;;;AACA,iBAAOO,aAAa,CAACA,aAAa,CAACrC,MAAd,GAAuB,CAAxB,CAAb,CAAwC8B,IAA/C;AACH;AAED;AACJ;AACA;;;AACYK,QAAAA,YAAY,CAAIkB,KAAJ,EAAqB;AACrC,gBAAMC,QAAQ,GAAG,CAAC,GAAGD,KAAJ,CAAjB;;AACA,eAAK,IAAIvD,CAAC,GAAGwD,QAAQ,CAACtD,MAAT,GAAkB,CAA/B,EAAkCF,CAAC,GAAG,CAAtC,EAAyCA,CAAC,EAA1C,EAA8C;AAC1C,kBAAMyD,CAAC,GAAG7B,IAAI,CAAC8B,KAAL,CAAW9B,IAAI,CAAC0B,MAAL,MAAiBtD,CAAC,GAAG,CAArB,CAAX,CAAV;AACA,aAACwD,QAAQ,CAACxD,CAAD,CAAT,EAAcwD,QAAQ,CAACC,CAAD,CAAtB,IAA6B,CAACD,QAAQ,CAACC,CAAD,CAAT,EAAcD,QAAQ,CAACxD,CAAD,CAAtB,CAA7B;AACH;;AACD,iBAAOwD,QAAP;AACH;;AAEDG,QAAAA,WAAW,CAACC,aAAD,EAAiC;AACxC;AACA,eAAKC,sBAAL,CAA4BD,aAA5B;AACH;AAGD;AACJ;AACA;;;AACYC,QAAAA,sBAAsB,CAACD,aAAD,EAAuC;AACjE,eAAK,MAAM5B,IAAX,IAAmB4B,aAAnB,EAAkC;AAC9B;AACA,kBAAME,YAAY,GAAG,KAAK9F,gBAAL,CAAsBsB,GAAtB,CAA0B0C,IAAI,CAACI,EAA/B,KAAsC,CAA3D;;AACA,iBAAKpE,gBAAL,CAAsB+F,GAAtB,CAA0B/B,IAAI,CAACI,EAA/B,EAAmC0B,YAAY,GAAG,CAAlD,EAH8B,CAK9B;;;AACA,gBAAI9B,IAAI,CAACS,IAAT,EAAe;AACX,oBAAMuB,SAAS,GAAG,KAAK9F,kBAAL,CAAwBoB,GAAxB,CAA4B0C,IAAI,CAACS,IAAjC,KAA0C,CAA5D;;AACA,mBAAKvE,kBAAL,CAAwB6F,GAAxB,CAA4B/B,IAAI,CAACS,IAAjC,EAAuCuB,SAAS,GAAG,CAAnD;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACWC,QAAAA,qBAAqB,CAAChF,MAAD,EAAyB;AACjD,iBAAO,KAAKjB,gBAAL,CAAsBsB,GAAtB,CAA0BL,MAA1B,KAAqC,CAA5C;AACH;AAED;AACJ;AACA;;;AACWiF,QAAAA,qBAAqB,CAACC,QAAD,EAA2B;AACnD,iBAAO,KAAKjG,kBAAL,CAAwBoB,GAAxB,CAA4B6E,QAA5B,KAAyC,CAAhD;AACH;;AAEMC,QAAAA,cAAc,CAACC,WAAD,EAAsB;AACvC,cAAIC,OAAgB,GAAG,EAAvB;AACAD,UAAAA,WAAW,CAACE,OAAZ,CAAoBC,GAAG,IAAE;AACrB,gBAAIC,MAAM,GAAG;AAAA;AAAA,gCAAMnG,WAAN,CAAkBC,cAAlB,CAAiCe,GAAjC,CAAqCkF,GAArC,CAAb;AACA,gBAAIE,UAAU,GAAG;AAAA;AAAA,gCAAMpG,WAAN,CAAkBqG,SAAlB,CAA4BrF,GAA5B,CAAgCmF,MAAM,CAAExF,MAAxC,CAAjB;AACAqF,YAAAA,OAAO,CAAC3F,IAAR,CAAa+F,UAAU,CAAEE,MAAzB;AACH,WAJD;AAKA,iBAAON,OAAP;AACH;;AAxQyD,O", "sourcesContent": ["\r\n/**\r\n * 肉鸽数据管理\r\n */\r\n\r\nimport { SingletonBase } from \"db://assets/scripts/core/base/SingletonBase\";\r\nimport { ResWorldGroup } from \"../../autogen/luban/schema\";\r\nimport { MyApp } from \"../../app/MyApp\";\r\nimport { instantiate, Prefab, Node, NodePool } from \"cc\";\r\nimport { RogueItem } from \"../../ui/gameui/game/RogueItem\";\r\nimport { BundleName } from \"../../const/BundleConst\";\r\n\r\nexport class RogueManager extends SingletonBase<RogueManager> {\r\n    _rougeGroupConfig: { [key: number]: ResWorldGroup[] } = {};\r\n\r\n    private _selectedHistory: Map<number, number> = new Map(); // 词条ID -> 选取次数\r\n    private _typeSelectedCount: Map<number, number> = new Map(); // 词条类型 -> 选取次数\r\n    private _rogueItemPool: NodePool = new NodePool();\r\n\r\n    constructor() {\r\n        super();\r\n        this.initRougeGroupConfig()\r\n    }\r\n\r\n    initRougeGroupConfig() {\r\n        let list = MyApp.lubanTables.TbResWordGroup.getDataList();\r\n        for (let v of list) {\r\n            if (!this._rougeGroupConfig[v.GroupID]) {\r\n                this._rougeGroupConfig[v.GroupID] = [];\r\n            }\r\n            this._rougeGroupConfig[v.GroupID].push(v);\r\n        }\r\n    }\r\n\r\n    public reset(): void {\r\n        this._selectedHistory.clear();\r\n        this._typeSelectedCount.clear();\r\n    }\r\n\r\n    clear(){\r\n        this.reset();\r\n        this._rogueItemPool.clear();\r\n    }\r\n\r\n    getSeLectedHistory(): Map<number, number> {\r\n        return this._selectedHistory;\r\n    }\r\n\r\n    async setRogueItem(parent: Node, wordId: number, lv: number = 0) {\r\n        let rogue: RogueItem;\r\n        // Try to get from pool first\r\n        if (this._rogueItemPool.size() > 0) {\r\n            const node = this._rogueItemPool.get();\r\n            rogue = node!.getComponent(RogueItem)!;\r\n        } else {\r\n            // Create new if pool is empty\r\n            rogue = await this.getRogueItem();\r\n        }\r\n\r\n        rogue.node.parent = parent!;\r\n        rogue.initRogueById(wordId, lv)\r\n    }\r\n\r\n    async getRogueItem() {\r\n        let rogueItem = await MyApp.resMgr.loadAsync(BundleName.GameFight,\"prefab/RogueItem\", Prefab)\r\n        let node = instantiate(rogueItem!) as Node;\r\n        return node!.getComponent(RogueItem)!\r\n    }\r\n    public recycleRogueItems(parentNode: Node): void {\r\n        if (!parentNode) return;\r\n\r\n        for (let i = parentNode.children.length - 1; i >= 0; i--) {\r\n            const childNode = parentNode.children[i];\r\n            const rogueItem = childNode.getComponent(RogueItem);\r\n\r\n            if (rogueItem) {\r\n                rogueItem.reset?.();\r\n                this._rogueItemPool.put(childNode);\r\n            }\r\n        }\r\n    }\r\n\r\n    public clearRogueItemPool(): void {\r\n        this._rogueItemPool.clear();\r\n    }\r\n    /**\r\n     * 随机获取词条\r\n     * @param groupId 词条组ID\r\n     * @param count 随机数量，默认3个\r\n     * @returns 随机到的词条列表\r\n     */\r\n    public randomWords(groupId: number, count: number = 3): ResWorldGroup[] {\r\n        const list = this._rougeGroupConfig[groupId];\r\n        if (!list || list.length === 0) {\r\n            console.warn(`词条组 ${groupId} 不存在或为空`);\r\n            return [];\r\n        }\r\n\r\n        // 按优先级分组\r\n        const priorityGroups = this.groupByPriority(list);\r\n\r\n        const result: ResWorldGroup[] = [];\r\n        let remainingCount = count;\r\n\r\n        // 按优先级从低到高依次随机\r\n        const priorities = Object.keys(priorityGroups).map(Number).sort((a, b) => a - b);\r\n\r\n        for (const priority of priorities) {\r\n            if (remainingCount <= 0) break;\r\n\r\n            const availableWords = this.getAvailableWords(priorityGroups[priority]);\r\n            if (availableWords.length === 0) continue;\r\n\r\n            // 逐个随机，而不是一次性返回所有\r\n            const wordsForThisPriority = this.randomFromPriorityGroupOneByOne(\r\n                availableWords,\r\n                Math.min(remainingCount, availableWords.length)\r\n            );\r\n\r\n            result.push(...wordsForThisPriority);\r\n            remainingCount -= wordsForThisPriority.length;\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * 按优先级分组\r\n     */\r\n    private groupByPriority(words: ResWorldGroup[]): { [priority: number]: ResWorldGroup[] } {\r\n        const groups: { [priority: number]: ResWorldGroup[] } = {};\r\n\r\n        for (const word of words) {\r\n            if (!groups[word.priority]) {\r\n                groups[word.priority] = [];\r\n            }\r\n            groups[word.priority].push(word);\r\n        }\r\n\r\n        return groups;\r\n    }\r\n\r\n    /**\r\n     * 获取可用的词条（考虑次数限制）\r\n     */\r\n    private getAvailableWords(words: ResWorldGroup[]): ResWorldGroup[] {\r\n        return words.filter(word => {\r\n            // 检查次数限制\r\n            if (word.times === -1) return true; // -1表示无限次\r\n            if (word.times === 0) return false; // 0表示不可选\r\n            const selectedCount = this._selectedHistory.get(word.ID) || 0;\r\n            return selectedCount < word.times;\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 从指定优先级组中逐个随机选择词条（每次随机后重新计算权重）\r\n     */\r\n    private randomFromPriorityGroupOneByOne(words: ResWorldGroup[], count: number): ResWorldGroup[] {\r\n        if (words.length <= count) {\r\n            // 如果数量不够，返回所有可用的，但需要打乱顺序\r\n            return this.shuffleArray([...words]);\r\n        }\r\n\r\n        const selected: ResWorldGroup[] = [];\r\n        const availableWords = [...words];\r\n\r\n        for (let i = 0; i < count && availableWords.length > 0; i++) {\r\n            // 每次随机都重新计算权重（考虑类型修正）\r\n            const weightedWords = availableWords.map(word => {\r\n                let weight = word.weight;\r\n\r\n                // 如果之前选取过该类型，加上类型权重修正\r\n                if (word.type && word.typWeightFix) {\r\n                    const typeSelectedCount = this._typeSelectedCount.get(word.type) || 0;\r\n                    if (typeSelectedCount > 0) {\r\n                        weight += word.typWeightFix;\r\n                    }\r\n                }\r\n\r\n                return { word, weight };\r\n            });\r\n\r\n            // 权重随机选择一个词条\r\n            const selectedWord = this.selectByWeight(weightedWords);\r\n            if (selectedWord) {\r\n                selected.push(selectedWord);\r\n\r\n                // 从可用列表中移除已选中的词条\r\n                const index = availableWords.findIndex(w => w.ID === selectedWord.ID);\r\n                if (index !== -1) {\r\n                    availableWords.splice(index, 1);\r\n                }\r\n            }\r\n        }\r\n\r\n        return selected;\r\n    }\r\n\r\n    /**\r\n     * 根据权重随机选择一个词条\r\n     */\r\n    private selectByWeight(weightedWords: { word: ResWorldGroup, weight: number }[]): ResWorldGroup | null {\r\n        if (weightedWords.length === 0) return null;\r\n\r\n        const totalWeight = weightedWords.reduce((sum, item) => sum + item.weight, 0);\r\n        let random = Math.random() * totalWeight;\r\n\r\n        for (const item of weightedWords) {\r\n            random -= item.weight;\r\n            if (random <= 0) {\r\n                return item.word;\r\n            }\r\n        }\r\n\r\n        // 如果由于浮点数精度问题没有选中，返回最后一个\r\n        return weightedWords[weightedWords.length - 1].word;\r\n    }\r\n\r\n    /**\r\n     * 打乱数组顺序\r\n     */\r\n    private shuffleArray<T>(array: T[]): T[] {\r\n        const shuffled = [...array];\r\n        for (let i = shuffled.length - 1; i > 0; i--) {\r\n            const j = Math.floor(Math.random() * (i + 1));\r\n            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];\r\n        }\r\n        return shuffled;\r\n    }\r\n\r\n    selectRogue(selectedWords: ResWorldGroup[]) {\r\n        // 更新选取记录\r\n        this.updateSelectionHistory(selectedWords);\r\n    }\r\n\r\n\r\n    /**\r\n     * 更新选取记录\r\n     */\r\n    private updateSelectionHistory(selectedWords: ResWorldGroup[]): void {\r\n        for (const word of selectedWords) {\r\n            // 更新词条选取次数\r\n            const currentCount = this._selectedHistory.get(word.ID) || 0;\r\n            this._selectedHistory.set(word.ID, currentCount + 1);\r\n\r\n            // 更新类型选取次数\r\n            if (word.type) {\r\n                const typeCount = this._typeSelectedCount.get(word.type) || 0;\r\n                this._typeSelectedCount.set(word.type, typeCount + 1);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 获取词条选取次数\r\n     */\r\n    public getWordSelectionCount(wordId: number): number {\r\n        return this._selectedHistory.get(wordId) || 0;\r\n    }\r\n\r\n    /**\r\n     * 获取类型选取次数\r\n     */\r\n    public getTypeSelectionCount(wordType: number): number {\r\n        return this._typeSelectedCount.get(wordType) || 0;\r\n    }\r\n\r\n    public coverToBuffIds(wordGroupId:number[]){\r\n        let buffIDs:number[] = [];\r\n        wordGroupId.forEach(key=>{\r\n            let config = MyApp.lubanTables.TbResWordGroup.get(key);\r\n            let wordConfig = MyApp.lubanTables.TbResWord.get(config!.wordId)\r\n            buffIDs.push(wordConfig!.buffID);\r\n        })\r\n        return buffIDs;\r\n    }\r\n}"]}
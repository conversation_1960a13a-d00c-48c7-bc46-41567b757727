{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/wave/PathEditor.ts"], "names": ["_decorator", "Enum", "Node", "Color", "Component", "UITransform", "JsonAsset", "CCInteger", "Graphics", "Vec2", "Vec3", "ePathType", "PathData", "PathPoint", "PathPointEditor", "eOrientationType", "ccclass", "executeInEditMode", "property", "disallowMultiple", "menu", "requireComponent", "PathEditor", "type", "displayName", "visible", "_pathDataObj", "points", "length", "_graphics", "_showDirectionArrow", "_pathData", "_cachedChildrenCount", "graphics", "node", "getComponent", "addComponent", "pathData", "value", "reload", "pathName", "name", "pathType", "startIdx", "endIdx", "isClosed", "closed", "onLoad", "uiTrans", "setContentSize", "fromJSON", "json", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "point", "addPoint", "save", "pointEditors", "getComponentsInChildren", "map", "editor", "pathPoint", "JSON", "stringify", "pointNode", "parent", "setPosition", "x", "y", "pointEditor", "addNewPoint", "updateCurve", "flipHorizontal", "getSelectedPoints", "center", "calculateCenter", "pos", "position", "toVec2", "flipVertical", "fitToCircle", "radius", "calculateRadius", "firstPoint", "firstPos", "getPosition", "startAngle", "Math", "atan2", "angleStep", "PI", "i", "angle", "cos", "sin", "selected", "Editor", "Selection", "getSelected", "console", "log", "uuid", "getChildByUuid", "push", "sort", "a", "b", "getSiblingIndex", "totalX", "totalY", "totalCount", "totalDistance", "distance", "update", "_dt", "childrenCount", "children", "ZERO", "drawPath", "clear", "Custom", "drawCustomPath", "drawCirclePath", "centerX", "centerY", "strokeColor", "curveColor", "lineWidth", "arc", "stroke", "subdivided", "getSubdividedPoints", "showSegments", "drawSegmentedPath", "drawUniformPath", "endPoint", "prevPoint", "direction", "drawPathDirectionArrow", "color", "width", "moveTo", "lineTo", "t", "interpolateColor", "GREEN", "RED", "lastColor", "color1", "color2", "max", "min", "r", "g", "arrowHeadLength", "arrowHeadAngle", "arrowStartX", "arrowStartY", "leftX", "leftY", "rightX", "rightY", "drawPathPoint", "pointSize", "index", "siblings", "clearGraphics", "YELLOW", "getDefaultColorByIndex", "fillColor", "BLACK", "circle", "fill", "smoothness", "speed", "drawOrientationArrow", "drawPathPointAtPosition", "drawSmoothness", "drawOrientation", "drawOrientationArrowAtPosition", "currentIndex", "<PERSON><PERSON><PERSON><PERSON>", "arrowAngle", "calculateArrowAngle", "getArrowColorByOrientationType", "orientationType", "endX", "endY", "count", "WHITE", "Path", "calculateMovementDirection", "Target", "calculatePlayerDirection", "Fixed", "orientationParam", "undefined", "nextPoint", "currentPoint", "playerX", "playerY", "BLUE", "MAGENTA"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;;AAG7FC,MAAAA,S,iBAAAA,S;AAAWC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,S,iBAAAA,S;;AACrBC,MAAAA,e,iBAAAA,e;;AACAC,MAAAA,gB,iBAAAA,gB;;;;;;;;;OAJH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,iBAAX;AAA8BC,QAAAA,QAA9B;AAAwCC,QAAAA,gBAAxC;AAA0DC,QAAAA,IAA1D;AAAgEC,QAAAA;AAAhE,O,GAAqFrB,U;;4BAW9EsB,U,WALZN,OAAO,CAAC,YAAD,C,UACPI,IAAI,CAAC,aAAD,C,UACJC,gBAAgB,CAACb,QAAD,C,UAChBS,iBAAiB,CAAC,IAAD,C,UACjBE,gBAAgB,CAAC,IAAD,C,UAUZD,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEjB,SAAR;AAAmBkB,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,UASRN,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAQRN,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEtB,IAAI;AAAA;AAAA,mCAAZ;AAAyBuB,QAAAA,WAAW,EAAE;AAAtC,OAAD,C,UAQRN,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEhB,SAAR;AAAmBiB,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,WAQRN,QAAQ,CAAC;AAAEK,QAAAA,IAAI,EAAEhB,SAAR;AAAmBiB,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,WAQRN,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE,MAAf;;AAAuBC,QAAAA,OAAO,GAAG;AACvC;AACA,iBAAO,KAAKC,YAAL,CAAkBC,MAAlB,CAAyBC,MAAzB,IAAmC,CAA1C;AACH;;AAHS,OAAD,C,WAWRV,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,WAGRN,QAAQ,CAAC;AAAEM,QAAAA,WAAW,EAAE;AAAf,OAAD,C,uFArEb,MAKaF,UALb,SAKgClB,SALhC,CAK0C;AAAA;AAAA;AAAA,eAC9ByB,SAD8B,GACD,IADC;;AAAA;;AAAA;;AAiEA;AAjEA,eAmE9BC,mBAnE8B,GAmEC,IAnED;AAAA,eAoE9BC,SApE8B,GAoEA,IApEA;AAAA,eAqE9BL,YArE8B,GAqEL;AAAA;AAAA,qCArEK;AAAA,eAsE9BM,oBAtE8B,GAsEC,CAtED;AAAA;;AAEnB,YAARC,QAAQ,GAAa;AAC5B,cAAI,CAAC,KAAKJ,SAAV,EAAqB;AACjB,iBAAKA,SAAL,GAAiB,KAAKK,IAAL,CAAUC,YAAV,CAAuB3B,QAAvB,KAAoC,KAAK0B,IAAL,CAAUE,YAAV,CAAuB5B,QAAvB,CAArD;AACH;;AACD,iBAAO,KAAKqB,SAAZ;AACH;;AAGkB,YAARQ,QAAQ,CAACC,KAAD,EAAmB;AAClC,eAAKP,SAAL,GAAiBO,KAAjB;AACA,eAAKC,MAAL;AACH;;AACkB,YAARF,QAAQ,GAAqB;AACpC,iBAAO,KAAKN,SAAZ;AACH;;AAGkB,YAARS,QAAQ,GAAW;AAC1B,iBAAO,KAAKd,YAAL,CAAkBe,IAAzB;AACH;;AACkB,YAARD,QAAQ,CAACF,KAAD,EAAgB;AAC/B,eAAKZ,YAAL,CAAkBe,IAAlB,GAAyBH,KAAzB;AACH;;AAGkB,YAARI,QAAQ,GAAG;AAClB,iBAAO,KAAKhB,YAAL,CAAkBgB,QAAzB;AACH;;AACkB,YAARA,QAAQ,CAACJ,KAAD,EAAmB;AAClC,eAAKZ,YAAL,CAAkBgB,QAAlB,GAA6BJ,KAA7B;AACH;;AAGkB,YAARK,QAAQ,GAAG;AAClB,iBAAO,KAAKjB,YAAL,CAAkBiB,QAAzB;AACH;;AACkB,YAARA,QAAQ,CAACL,KAAD,EAAgB;AAC/B,eAAKZ,YAAL,CAAkBiB,QAAlB,GAA6BL,KAA7B;AACH;;AAGgB,YAANM,MAAM,GAAG;AAChB,iBAAO,KAAKlB,YAAL,CAAkBkB,MAAzB;AACH;;AACgB,YAANA,MAAM,CAACN,KAAD,EAAgB;AAC7B,eAAKZ,YAAL,CAAkBkB,MAAlB,GAA2BN,KAA3B;AACH;;AAMkB,YAARO,QAAQ,GAAY;AAC3B,iBAAO,KAAKnB,YAAL,CAAkBoB,MAAzB;AACH;;AACkB,YAARD,QAAQ,CAACP,KAAD,EAAiB;AAChC,eAAKZ,YAAL,CAAkBoB,MAAlB,GAA2BR,KAA3B;AACH;;AAaDS,QAAAA,MAAM,GAAG;AACL,gBAAMC,OAAO,GAAG,KAAKb,YAAL,CAAkB9B,WAAlB,CAAhB;AACA2C,UAAAA,OAAO,CAAEC,cAAT,CAAwB,CAAxB,EAA2B,CAA3B,EAFK,CAE0B;AAClC;;AAEMV,QAAAA,MAAM,GAAG;AACZ,cAAI,CAAC,KAAKR,SAAV,EAAqB;AAErB,gBAAMM,QAAQ,GAAG;AAAA;AAAA,oCAASa,QAAT,CAAkB,KAAKnB,SAAL,CAAeoB,IAAjC,CAAjB;AACA,eAAKzB,YAAL,GAAoBW,QAApB;AAEA,eAAKH,IAAL,CAAUkB,iBAAV;;AACA,cAAI,KAAK1B,YAAL,IAAqB,KAAKA,YAAL,CAAkBC,MAAlB,CAAyBC,MAAzB,GAAkC,CAA3D,EAA8D;AAC1D,iBAAKF,YAAL,CAAkBC,MAAlB,CAAyB0B,OAAzB,CAAkCC,KAAD,IAAqB;AAClD,mBAAKC,QAAL,CAAcD,KAAd;AACH,aAFD;AAGH;AACJ;;AAEME,QAAAA,IAAI,GAAW;AAClB;AACA,gBAAMC,YAAY,GAAG,KAAKC,uBAAL;AAAA;AAAA,iDAArB;AACA,eAAKhC,YAAL,CAAkBC,MAAlB,GAA2B8B,YAAY,CAACE,GAAb,CAAkBC,MAAD,IAAYA,MAAM,CAACC,SAApC,CAA3B;AACA,iBAAOC,IAAI,CAACC,SAAL,CAAe,KAAKrC,YAApB,EAAkC,IAAlC,EAAwC,CAAxC,CAAP;AACH;;AAEM6B,QAAAA,QAAQ,CAACD,KAAD,EAAyB;AACpC,gBAAMU,SAAS,GAAG,IAAI9D,IAAJ,EAAlB;AACA8D,UAAAA,SAAS,CAACC,MAAV,GAAmB,KAAK/B,IAAxB;AACA8B,UAAAA,SAAS,CAACE,WAAV,CAAsBZ,KAAK,CAACa,CAA5B,EAA+Bb,KAAK,CAACc,CAArC,EAAwC,CAAxC;AAEA,gBAAMC,WAAW,GAAGL,SAAS,CAAC5B,YAAV;AAAA;AAAA,iDAApB;AACAiC,UAAAA,WAAW,CAACR,SAAZ,GAAwBP,KAAxB;AACA,iBAAOU,SAAP;AACH;;AAEMM,QAAAA,WAAW,CAACH,CAAD,EAAYC,CAAZ,EAA6B;AAC3C,gBAAMd,KAAK,GAAG;AAAA;AAAA,sCAAca,CAAd,EAAiBC,CAAjB,CAAd;AACA,gBAAMlC,IAAI,GAAG,KAAKqB,QAAL,CAAcD,KAAd,CAAb;AACA,eAAKiB,WAAL;AACA,iBAAOrC,IAAP;AACH;;AAEMsC,QAAAA,cAAc,GAAG;AACpB,gBAAM7C,MAAM,GAAG,KAAK8C,iBAAL,EAAf,CADoB,CAEpB;;AACA,gBAAMC,MAAM,GAAG,KAAKC,eAAL,CAAqBhD,MAArB,CAAf;AACAA,UAAAA,MAAM,CAAC0B,OAAP,CAAgBgB,WAAD,IAAiB;AAC5B,kBAAMO,GAAG,GAAGP,WAAW,CAACnC,IAAZ,CAAiB2C,QAAjB,CAA0BC,MAA1B,EAAZ;AACA,kBAAMX,CAAC,GAAGO,MAAM,CAACP,CAAP,GAAW,CAAX,GAAeS,GAAG,CAACT,CAA7B;AACAE,YAAAA,WAAW,CAACnC,IAAZ,CAAiBgC,WAAjB,CAA6BC,CAA7B,EAAgCS,GAAG,CAACR,CAApC,EAAuC,CAAvC;AACH,WAJD;AAKH;;AAEMW,QAAAA,YAAY,GAAG;AAClB,gBAAMpD,MAAM,GAAG,KAAK8C,iBAAL,EAAf,CADkB,CAElB;;AACA,gBAAMC,MAAM,GAAG,KAAKC,eAAL,CAAqBhD,MAArB,CAAf;AACAA,UAAAA,MAAM,CAAC0B,OAAP,CAAgBgB,WAAD,IAAiB;AAC5B,kBAAMO,GAAG,GAAGP,WAAW,CAACnC,IAAZ,CAAiB2C,QAAjB,CAA0BC,MAA1B,EAAZ;AACA,kBAAMV,CAAC,GAAGM,MAAM,CAACN,CAAP,GAAW,CAAX,GAAeQ,GAAG,CAACR,CAA7B;AACAC,YAAAA,WAAW,CAACnC,IAAZ,CAAiBgC,WAAjB,CAA6BU,GAAG,CAACT,CAAjC,EAAoCC,CAApC,EAAuC,CAAvC;AACH,WAJD;AAKH;;AAEMY,QAAAA,WAAW,GAAG;AACjB;AACA,gBAAMrD,MAAM,GAAG,KAAK8C,iBAAL,EAAf;AACA,cAAI9C,MAAM,CAACC,MAAP,GAAgB,CAApB,EAAuB;AAEvB,gBAAM8C,MAAM,GAAG,KAAKC,eAAL,CAAqBhD,MAArB,CAAf;AACA,gBAAMsD,MAAM,GAAG,KAAKC,eAAL,CAAqBvD,MAArB,EAA6B+C,MAA7B,CAAf,CANiB,CAQjB;;AACA,gBAAMS,UAAU,GAAGxD,MAAM,CAAC,CAAD,CAAzB;AACA,gBAAMyD,QAAQ,GAAGD,UAAU,CAACjD,IAAX,CAAgBmD,WAAhB,EAAjB,CAViB,CAWjB;;AACA,gBAAMC,UAAU,GAAGC,IAAI,CAACC,KAAL,CAAWJ,QAAQ,CAAChB,CAAT,GAAaM,MAAM,CAACN,CAA/B,EAAkCgB,QAAQ,CAACjB,CAAT,GAAaO,MAAM,CAACP,CAAtD,CAAnB,CAZiB,CAajB;;AACA,gBAAMsB,SAAS,GAAIF,IAAI,CAACG,EAAL,GAAU,CAAX,GAAgB/D,MAAM,CAACC,MAAzC;;AAEA,eAAK,IAAI+D,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGhE,MAAM,CAACC,MAA3B,EAAmC+D,CAAC,EAApC,EAAwC;AACpC,kBAAMC,KAAK,GAAGN,UAAU,GAAGG,SAAS,GAAGE,CAAvC;AACA,kBAAMxB,CAAC,GAAGO,MAAM,CAACP,CAAP,GAAWoB,IAAI,CAACM,GAAL,CAASD,KAAT,IAAkBX,MAAvC;AACA,kBAAMb,CAAC,GAAGM,MAAM,CAACN,CAAP,GAAWmB,IAAI,CAACO,GAAL,CAASF,KAAT,IAAkBX,MAAvC;AACAtD,YAAAA,MAAM,CAACgE,CAAD,CAAN,CAAUzD,IAAV,CAAegC,WAAf,CAA2BC,CAA3B,EAA8BC,CAA9B,EAAiC,CAAjC;AACH;AACJ;;AAEOK,QAAAA,iBAAiB,GAAsB;AAC3C,gBAAMsB,QAAQ,GAAGC,MAAM,CAACC,SAAP,CAAiBC,WAAjB,CAA6B,MAA7B,CAAjB;;AACA,cAAI,CAACH,QAAD,IAAaA,QAAQ,CAACnE,MAAT,KAAoB,CAArC,EAAwC;AACpCuE,YAAAA,OAAO,CAACC,GAAR,CAAY,kBAAZ;AACA,mBAAO,EAAP;AACH;;AACD,cAAIzE,MAAyB,GAAG,EAAhC;AACAoE,UAAAA,QAAQ,CAAC1C,OAAT,CAAkBgD,IAAD,IAAU;AACvB,kBAAMnE,IAAI,GAAG,KAAKA,IAAL,CAAUoE,cAAV,CAAyBD,IAAzB,CAAb;;AACA,gBAAInE,IAAJ,EAAU;AACN,oBAAMmC,WAAW,GAAGnC,IAAI,CAACC,YAAL;AAAA;AAAA,qDAApB;;AACA,kBAAIkC,WAAJ,EAAiB;AACb1C,gBAAAA,MAAM,CAAC4E,IAAP,CAAYlC,WAAZ;AACH;AACJ;AACJ,WARD;AASA1C,UAAAA,MAAM,CAAC6E,IAAP,CAAY,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAACvE,IAAF,CAAOyE,eAAP,KAA2BD,CAAC,CAACxE,IAAF,CAAOyE,eAAP,EAAjD;AACA,iBAAOhF,MAAP;AACH;;AAEOgD,QAAAA,eAAe,CAAChD,MAAD,EAAkC;AACrD,cAAIiF,MAAM,GAAG,CAAb;AACA,cAAIC,MAAM,GAAG,CAAb;AACA,cAAIC,UAAU,GAAG,CAAjB;AACAnF,UAAAA,MAAM,CAAC0B,OAAP,CAAgBgB,WAAD,IAAiB;AAC5ByC,YAAAA,UAAU;AACV,kBAAMlC,GAAG,GAAGP,WAAW,CAACnC,IAAZ,CAAiB2C,QAAjB,CAA0BC,MAA1B,EAAZ;AACA8B,YAAAA,MAAM,IAAIhC,GAAG,CAACT,CAAd;AACA0C,YAAAA,MAAM,IAAIjC,GAAG,CAACR,CAAd;AACH,WALD;AAMA,iBAAO,IAAI3D,IAAJ,CAASmG,MAAM,GAAGE,UAAlB,EAA8BD,MAAM,GAAGC,UAAvC,CAAP;AACH;;AAEO5B,QAAAA,eAAe,CAACvD,MAAD,EAA4B+C,MAA5B,EAAkD;AACrE,cAAIqC,aAAa,GAAG,CAApB;AACApF,UAAAA,MAAM,CAAC0B,OAAP,CAAgBgB,WAAD,IAAiB;AAC5B,kBAAMO,GAAG,GAAGP,WAAW,CAACnC,IAAZ,CAAiB2C,QAAjB,CAA0BC,MAA1B,EAAZ;AACAiC,YAAAA,aAAa,IAAItG,IAAI,CAACuG,QAAL,CAAcpC,GAAd,EAAmBF,MAAnB,CAAjB;AACH,WAHD;AAIA,iBAAOqC,aAAa,GAAGpF,MAAM,CAACC,MAA9B;AACH;;AAEM2C,QAAAA,WAAW,GAAG;AACjB;AACA,gBAAMd,YAAY,GAAG,KAAKC,uBAAL;AAAA;AAAA,iDAArB;AACA,eAAKhC,YAAL,CAAkBC,MAAlB,GAA2B8B,YAAY,CAACE,GAAb,CAAkBC,MAAD,IAAYA,MAAM,CAACC,SAApC,CAA3B;AACH;;AAEMoD,QAAAA,MAAM,CAACC,GAAD,EAAc;AACvB,gBAAMC,aAAa,GAAG,KAAKjF,IAAL,CAAUkF,QAAV,CAAmBxF,MAAzC;;AACA,cAAIuF,aAAa,KAAK,KAAKnF,oBAA3B,EAAiD;AAC7C,iBAAKA,oBAAL,GAA4BmF,aAA5B;AACH,WAJsB,CAMvB;;;AACA,eAAKjF,IAAL,CAAU2C,QAAV,GAAqBnE,IAAI,CAAC2G,IAA1B;AACA,eAAK9C,WAAL;AACA,eAAK+C,QAAL;AACH;;AAEOA,QAAAA,QAAQ,GAAG;AACf,gBAAMrF,QAAQ,GAAG,KAAKA,QAAtB;AACAA,UAAAA,QAAQ,CAACsF,KAAT;;AACA,cAAI,KAAK7F,YAAL,CAAkBgB,QAAlB,KAA+B;AAAA;AAAA,sCAAU8E,MAA7C,EAAqD;AACjD,iBAAKC,cAAL;AACH,WAFD,MAEO;AACH,iBAAKC,cAAL;AACH;AACJ;;AAEOA,QAAAA,cAAc,GAAG;AACrB,gBAAMzF,QAAQ,GAAG,KAAKA,QAAtB;AACA,gBAAMI,QAAQ,GAAG,KAAKX,YAAtB;AACA,gBAAMuD,MAAM,GAAG5C,QAAQ,CAAC4C,MAAxB;AACA,gBAAM0C,OAAO,GAAGtF,QAAQ,CAACsF,OAAzB;AACA,gBAAMC,OAAO,GAAGvF,QAAQ,CAACuF,OAAzB;AACA,gBAAMtC,UAAU,GAAGjD,QAAQ,CAACiD,UAA5B,CANqB,CAQrB;;AACArD,UAAAA,QAAQ,CAAC4F,WAAT,GAAuB,KAAKC,UAA5B;AACA7F,UAAAA,QAAQ,CAAC8F,SAAT,GAAqB,CAArB;AACA9F,UAAAA,QAAQ,CAAC+F,GAAT,CAAaL,OAAb,EAAsBC,OAAtB,EAA+B3C,MAA/B,EAAuCK,UAAvC,EAAmDA,UAAU,GAAGC,IAAI,CAACG,EAAL,GAAU,CAA1E,EAA6E,KAA7E;AACAzD,UAAAA,QAAQ,CAACgG,MAAT;AACH;;AAEOR,QAAAA,cAAc,GAAG;AACrB;AACA,cAAI,KAAK/F,YAAL,CAAkBC,MAAlB,CAAyBC,MAAzB,GAAkC,CAAtC,EAAyC;AAEzC,gBAAMK,QAAQ,GAAG,KAAKA,QAAtB;;AACA,gBAAMiG,UAAU,GAAG,KAAKxG,YAAL,CAAkByG,mBAAlB,CAAsC,IAAtC,CAAnB;;AACA,cAAID,UAAU,CAACtG,MAAX,GAAoB,CAAxB,EAA2B;AACvB,gBAAI,KAAKwG,YAAT,EAAuB;AACnB9G,cAAAA,UAAU,CAAC+G,iBAAX,CAA6BpG,QAA7B,EAAuCiG,UAAvC,EAAmD,KAAKxG,YAAL,CAAkBoB,MAArE,EAA6E,CAA7E;AACH,aAFD,MAEO;AACHxB,cAAAA,UAAU,CAACgH,eAAX,CAA2BrG,QAA3B,EAAqCiG,UAArC,EAAiD,KAAKJ,UAAtD,EAAkE,KAAKpG,YAAL,CAAkBoB,MAApF,EAA4F,CAA5F;AACH,aALsB,CAOvB;;;AACA,gBAAI,KAAKhB,mBAAL,IAA4B,CAAC,KAAKJ,YAAL,CAAkBoB,MAAnD,EAA2D;AACvD,oBAAMyF,QAAQ,GAAGL,UAAU,CAACA,UAAU,CAACtG,MAAX,GAAoB,CAArB,CAA3B;AACA,kBAAI4G,SAAS,GAAGN,UAAU,CAACA,UAAU,CAACtG,MAAX,GAAoB,CAArB,CAA1B;;AACA,kBAAIsG,UAAU,CAACtG,MAAX,IAAqB,CAAzB,EAA4B;AACxB4G,gBAAAA,SAAS,GAAGN,UAAU,CAACA,UAAU,CAACtG,MAAX,GAAoB,CAArB,CAAtB;AACH;;AACD,oBAAM6G,SAAS,GAAGlD,IAAI,CAACC,KAAL,CAAW+C,QAAQ,CAACnE,CAAT,GAAaoE,SAAS,CAACpE,CAAlC,EAAqCmE,QAAQ,CAACpE,CAAT,GAAaqE,SAAS,CAACrE,CAA5D,CAAlB;AACA7C,cAAAA,UAAU,CAACoH,sBAAX,CAAkCzG,QAAlC,EAA4CsG,QAAQ,CAAC1D,QAArD,EAA+D4D,SAA/D,EAA0E,KAAK/G,YAAL,CAAkBoB,MAA5F;AACH;AACJ;;AAED,cAAI,KAAKsF,YAAT,EAAuB;AACnBjC,YAAAA,OAAO,CAACC,GAAR,CAAY,4BAAZ,EAA0C8B,UAAU,CAACtG,MAArD;AACH;AACJ;AAED;AACJ;AACA;;;AAC0B,eAAf0G,eAAe,CAACrG,QAAD,EAAqBiG,UAArB,EAA8CS,KAA9C,EAA4D7F,MAA5D,EAA6E8F,KAAY,GAAG,CAA5F,EAA+F;AACjH3G,UAAAA,QAAQ,CAAC4F,WAAT,GAAuBc,KAAvB;AACA1G,UAAAA,QAAQ,CAAC8F,SAAT,GAAqBa,KAArB;AAEA3G,UAAAA,QAAQ,CAAC4G,MAAT,CAAgBX,UAAU,CAAC,CAAD,CAAV,CAAc/D,CAA9B,EAAiC+D,UAAU,CAAC,CAAD,CAAV,CAAc9D,CAA/C;;AACA,eAAK,IAAIuB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGuC,UAAU,CAACtG,MAA/B,EAAuC+D,CAAC,EAAxC,EAA4C;AACxC1D,YAAAA,QAAQ,CAAC6G,MAAT,CAAgBZ,UAAU,CAACvC,CAAD,CAAV,CAAcxB,CAA9B,EAAiC+D,UAAU,CAACvC,CAAD,CAAV,CAAcvB,CAA/C;AACH,WAPgH,CASjH;;;AACA,cAAItB,MAAJ,EAAY;AACRb,YAAAA,QAAQ,CAAC6G,MAAT,CAAgBZ,UAAU,CAAC,CAAD,CAAV,CAAc/D,CAA9B,EAAiC+D,UAAU,CAAC,CAAD,CAAV,CAAc9D,CAA/C;AACH;;AAEDnC,UAAAA,QAAQ,CAACgG,MAAT;AACH;AAED;AACJ;AACA;;;AAC4B,eAAjBI,iBAAiB,CAACpG,QAAD,EAAqBiG,UAArB,EAA8CpF,MAA9C,EAA+D8F,KAAY,GAAG,CAA9E,EAAiF;AACrG3G,UAAAA,QAAQ,CAAC8F,SAAT,GAAqBa,KAArB;AAEA,cAAIV,UAAU,CAACtG,MAAX,GAAoB,CAAxB,EAA2B,OAH0E,CAKrG;;AACA,eAAK,IAAI+D,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGuC,UAAU,CAACtG,MAAX,GAAoB,CAAxC,EAA2C+D,CAAC,EAA5C,EAAgD;AAC5C,kBAAMoD,CAAC,GAAGpD,CAAC,IAAIuC,UAAU,CAACtG,MAAX,GAAoB,CAAxB,CAAX,CAD4C,CACL;AAEvC;;AACA,kBAAM+G,KAAK,GAAG,KAAKK,gBAAL,CAAsB7I,KAAK,CAAC8I,KAA5B,EAAmC9I,KAAK,CAAC+I,GAAzC,EAA8CH,CAA9C,CAAd;AACA9G,YAAAA,QAAQ,CAAC4F,WAAT,GAAuBc,KAAvB,CAL4C,CAO5C;;AACA1G,YAAAA,QAAQ,CAAC4G,MAAT,CAAgBX,UAAU,CAACvC,CAAD,CAAV,CAAcxB,CAA9B,EAAiC+D,UAAU,CAACvC,CAAD,CAAV,CAAcvB,CAA/C;AACAnC,YAAAA,QAAQ,CAAC6G,MAAT,CAAgBZ,UAAU,CAACvC,CAAC,GAAG,CAAL,CAAV,CAAkBxB,CAAlC,EAAqC+D,UAAU,CAACvC,CAAC,GAAG,CAAL,CAAV,CAAkBvB,CAAvD;AACAnC,YAAAA,QAAQ,CAACgG,MAAT,GAV4C,CAY5C;;AACA,kBAAMQ,SAAS,GAAGlD,IAAI,CAACC,KAAL,CAAW0C,UAAU,CAACvC,CAAC,GAAG,CAAL,CAAV,CAAkBvB,CAAlB,GAAsB8D,UAAU,CAACvC,CAAD,CAAV,CAAcvB,CAA/C,EAAkD8D,UAAU,CAACvC,CAAC,GAAG,CAAL,CAAV,CAAkBxB,CAAlB,GAAsB+D,UAAU,CAACvC,CAAD,CAAV,CAAcxB,CAAtF,CAAlB;AACA7C,YAAAA,UAAU,CAACoH,sBAAX,CAAkCzG,QAAlC,EAA4CiG,UAAU,CAACvC,CAAC,GAAG,CAAL,CAAV,CAAkBd,QAA9D,EAAwE4D,SAAxE,EAAmF3F,MAAnF;AACH,WArBoG,CAuBrG;;;AACA,cAAIA,MAAM,IAAIoF,UAAU,CAACtG,MAAX,GAAoB,CAAlC,EAAqC;AACjC,kBAAMuH,SAAS,GAAG,KAAKH,gBAAL,CAAsB7I,KAAK,CAAC8I,KAA5B,EAAmC9I,KAAK,CAAC+I,GAAzC,EAA8C,GAA9C,CAAlB;AACAjH,YAAAA,QAAQ,CAAC4F,WAAT,GAAuBsB,SAAvB;AACAlH,YAAAA,QAAQ,CAAC4G,MAAT,CAAgBX,UAAU,CAACA,UAAU,CAACtG,MAAX,GAAoB,CAArB,CAAV,CAAkCuC,CAAlD,EAAqD+D,UAAU,CAACA,UAAU,CAACtG,MAAX,GAAoB,CAArB,CAAV,CAAkCwC,CAAvF;AACAnC,YAAAA,QAAQ,CAAC6G,MAAT,CAAgBZ,UAAU,CAAC,CAAD,CAAV,CAAc/D,CAA9B,EAAiC+D,UAAU,CAAC,CAAD,CAAV,CAAc9D,CAA/C;AACAnC,YAAAA,QAAQ,CAACgG,MAAT;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;AACA;;;AAC2B,eAAhBe,gBAAgB,CAACI,MAAD,EAAgBC,MAAhB,EAA+BN,CAA/B,EAAiD;AACpEA,UAAAA,CAAC,GAAGxD,IAAI,CAAC+D,GAAL,CAAS,CAAT,EAAY/D,IAAI,CAACgE,GAAL,CAAS,CAAT,EAAYR,CAAZ,CAAZ,CAAJ,CADoE,CACnC;;AAEjC,gBAAMS,CAAC,GAAGJ,MAAM,CAACI,CAAP,GAAW,CAACH,MAAM,CAACG,CAAP,GAAWJ,MAAM,CAACI,CAAnB,IAAwBT,CAA7C;AACA,gBAAMU,CAAC,GAAGL,MAAM,CAACK,CAAP,GAAW,CAACJ,MAAM,CAACI,CAAP,GAAWL,MAAM,CAACK,CAAnB,IAAwBV,CAA7C;AACA,gBAAMrC,CAAC,GAAG0C,MAAM,CAAC1C,CAAP,GAAW,CAAC2C,MAAM,CAAC3C,CAAP,GAAW0C,MAAM,CAAC1C,CAAnB,IAAwBqC,CAA7C;AACA,gBAAMtC,CAAC,GAAG2C,MAAM,CAAC3C,CAAP,GAAW,CAAC4C,MAAM,CAAC5C,CAAP,GAAW2C,MAAM,CAAC3C,CAAnB,IAAwBsC,CAA7C;AAEA,iBAAO,IAAI5I,KAAJ,CAAUqJ,CAAV,EAAaC,CAAb,EAAgB/C,CAAhB,EAAmBD,CAAnB,CAAP;AACH;AAED;AACJ;AACA;;;AACiC,eAAtBiC,sBAAsB,CAACzG,QAAD,EAAqB4C,QAArB,EAAqC4D,SAArC,EAAwD3F,MAAxD,EAAyElB,MAAc,GAAG,EAA1F,EAA8FgH,KAAa,GAAG,CAA9G,EAAiH;AAC1I;AACA,cAAI9F,MAAJ,EAAY,OAF8H,CAI1I;;AACA,gBAAM4G,eAAe,GAAG9H,MAAxB;AACA,gBAAM+H,cAAc,GAAGpE,IAAI,CAACG,EAAL,GAAU,CAAjC,CAN0I,CAMtG;AAEpC;;AACAzD,UAAAA,QAAQ,CAAC4F,WAAT,GAAuB1H,KAAK,CAAC+I,GAA7B;AACAjH,UAAAA,QAAQ,CAAC8F,SAAT,GAAqBa,KAArB,CAV0I,CAY1I;;AACA,gBAAMgB,WAAW,GAAG/E,QAAQ,CAACV,CAA7B;AACA,gBAAM0F,WAAW,GAAGhF,QAAQ,CAACT,CAA7B,CAd0I,CAgB1I;;AACA,gBAAM0F,KAAK,GAAGF,WAAW,GAAGrE,IAAI,CAACM,GAAL,CAAS4C,SAAS,GAAGkB,cAArB,IAAuCD,eAAnE;AACA,gBAAMK,KAAK,GAAGF,WAAW,GAAGtE,IAAI,CAACO,GAAL,CAAS2C,SAAS,GAAGkB,cAArB,IAAuCD,eAAnE;AACA,gBAAMM,MAAM,GAAGJ,WAAW,GAAGrE,IAAI,CAACM,GAAL,CAAS4C,SAAS,GAAGkB,cAArB,IAAuCD,eAApE;AACA,gBAAMO,MAAM,GAAGJ,WAAW,GAAGtE,IAAI,CAACO,GAAL,CAAS2C,SAAS,GAAGkB,cAArB,IAAuCD,eAApE,CApB0I,CAsB1I;AACA;;AACAzH,UAAAA,QAAQ,CAAC4G,MAAT,CAAgBe,WAAhB,EAA6BC,WAA7B;AACA5H,UAAAA,QAAQ,CAAC6G,MAAT,CAAgBgB,KAAhB,EAAuBC,KAAvB;AACA9H,UAAAA,QAAQ,CAACgG,MAAT,GA1B0I,CA4B1I;;AACAhG,UAAAA,QAAQ,CAAC4G,MAAT,CAAgBe,WAAhB,EAA6BC,WAA7B;AACA5H,UAAAA,QAAQ,CAAC6G,MAAT,CAAgBkB,MAAhB,EAAwBC,MAAxB;AACAhI,UAAAA,QAAQ,CAACgG,MAAT;AACH;AAED;AACJ;AACA;;;AACwB,eAAbiC,aAAa,CAACjI,QAAD,EAAqB4B,SAArB,EAA2CkC,QAA3C,EAA8DoE,SAAiB,GAAG,EAAlF,EAAsFC,KAAtF,EAAqGtD,UAArG,EAAyHnE,QAAgB,GAAG,CAA5I,EAA+IC,MAAc,GAAG,CAAC,CAAjK,EAAoKyH,QAApK,EAAuLC,aAAsB,GAAG,IAAhN,EAAsN;AACtO,cAAIA,aAAJ,EAAmB;AACfrI,YAAAA,QAAQ,CAACsF,KAAT;AACH,WAHqO,CAKtO;;;AACA,gBAAMoB,KAAK,GAAG5C,QAAQ,GAAG5F,KAAK,CAACoK,MAAT,GAAkB,KAAKC,sBAAL,CAA4BJ,KAA5B,EAAmCtD,UAAnC,EAA+CnE,QAA/C,EAAyDC,MAAzD,CAAxC;AACAX,UAAAA,QAAQ,CAACwI,SAAT,GAAqB9B,KAArB;AACA1G,UAAAA,QAAQ,CAAC4F,WAAT,GAAuB1H,KAAK,CAACuK,KAA7B;AACAzI,UAAAA,QAAQ,CAAC8F,SAAT,GAAqB,CAArB;AAEA9F,UAAAA,QAAQ,CAAC0I,MAAT,CAAgB,CAAhB,EAAmB,CAAnB,EAAsBR,SAAtB;AACAlI,UAAAA,QAAQ,CAAC2I,IAAT;AACA3I,UAAAA,QAAQ,CAACgG,MAAT,GAbsO,CAetO;;AACA,cAAIpE,SAAS,CAACgH,UAAV,GAAuB,CAA3B,EAA8B;AAC1B5I,YAAAA,QAAQ,CAAC4F,WAAT,GAAuB1H,KAAK,CAAC8I,KAA7B;AACAhH,YAAAA,QAAQ,CAAC8F,SAAT,GAAqB,CAArB;AACA,kBAAM9C,MAAM,GAAGkF,SAAS,GAAG,CAAZ,GAAgBtG,SAAS,CAACgH,UAAV,GAAuB,EAAtD;AACA5I,YAAAA,QAAQ,CAAC0I,MAAT,CAAgB,CAAhB,EAAmB,CAAnB,EAAsB1F,MAAtB;AACAhD,YAAAA,QAAQ,CAACgG,MAAT;AACH,WAtBqO,CAwBtO;;;AACA,cAAIpE,SAAS,CAACiH,KAAV,GAAkB,CAAtB,EAAyB;AACrB,iBAAKC,oBAAL,CAA0B9I,QAA1B,EAAoC4B,SAApC,EAA+CwG,QAAQ,IAAI,IAA3D,EAAiED,KAAjE;AACH;AACJ;AAED;AACJ;AACA;;;AACkC,eAAvBY,uBAAuB,CAAC/I,QAAD,EAAqB4B,SAArB,EAA2CM,CAA3C,EAAsDC,CAAtD,EAAiE2B,QAAjE,EAAoFoE,SAAiB,GAAG,EAAxG,EAA4GC,KAA5G,EAA2HtD,UAA3H,EAA+InE,QAAgB,GAAG,CAAlK,EAAqKC,MAAc,GAAG,CAAC,CAAvL,EAC1BqI,cAAuB,GAAG,IADA,EACMC,eAAwB,GAAG,IADjC,EAE5B;AACE;AACA,gBAAMvC,KAAK,GAAG5C,QAAQ,GAAG5F,KAAK,CAACoK,MAAT,GAAkB,KAAKC,sBAAL,CAA4BJ,KAA5B,EAAmCtD,UAAnC,EAA+CnE,QAA/C,EAAyDC,MAAzD,CAAxC;AACAX,UAAAA,QAAQ,CAACwI,SAAT,GAAqB9B,KAArB;AACA1G,UAAAA,QAAQ,CAAC4F,WAAT,GAAuB1H,KAAK,CAACuK,KAA7B;AACAzI,UAAAA,QAAQ,CAAC8F,SAAT,GAAqB,CAArB;AAEA9F,UAAAA,QAAQ,CAAC0I,MAAT,CAAgBxG,CAAhB,EAAmBC,CAAnB,EAAsB+F,SAAtB;AACAlI,UAAAA,QAAQ,CAAC2I,IAAT;AACA3I,UAAAA,QAAQ,CAACgG,MAAT,GATF,CAWE;;AACA,cAAIpE,SAAS,CAACgH,UAAV,GAAuB,CAAvB,IAA4BI,cAAhC,EAAgD;AAC5ChJ,YAAAA,QAAQ,CAAC4F,WAAT,GAAuB1H,KAAK,CAAC8I,KAA7B;AACAhH,YAAAA,QAAQ,CAAC8F,SAAT,GAAqB,CAArB;AACA,kBAAM9C,MAAM,GAAGkF,SAAS,GAAG,CAAZ,GAAgBtG,SAAS,CAACgH,UAAV,GAAuB,EAAtD;AACA5I,YAAAA,QAAQ,CAAC0I,MAAT,CAAgBxG,CAAhB,EAAmBC,CAAnB,EAAsBa,MAAtB;AACAhD,YAAAA,QAAQ,CAACgG,MAAT;AACH,WAlBH,CAoBE;;;AACA,cAAIpE,SAAS,CAACiH,KAAV,GAAkB,CAAlB,IAAuBI,eAA3B,EAA4C;AACxC,iBAAKC,8BAAL,CAAoClJ,QAApC,EAA8C4B,SAA9C,EAAyD,IAAzD,EAA+DuG,KAA/D,EAAsEjG,CAAtE,EAAyEC,CAAzE;AACH;AACJ;AAED;AACJ;AACA;;;AACyC,eAA9B+G,8BAA8B,CAAClJ,QAAD,EAAqB4B,SAArB,EAA2CwG,QAA3C,EAAoEe,YAApE,EAA0FjH,CAA1F,EAAqGC,CAArG,EAAgH;AACjJ,gBAAMiH,WAAW,GAAG9F,IAAI,CAACgE,GAAL,CAAS1F,SAAS,CAACiH,KAAV,GAAkB,EAA3B,EAA+B,GAA/B,CAApB;AACA,gBAAMQ,UAAU,GAAG,KAAKC,mBAAL,CAAyB1H,SAAzB,EAAoCwG,QAApC,EAA8Ce,YAA9C,CAAnB,CAFiJ,CAIjJ;;AACAnJ,UAAAA,QAAQ,CAAC4F,WAAT,GAAuB,KAAK2D,8BAAL,CAAoC3H,SAAS,CAAC4H,eAA9C,CAAvB;AACAxJ,UAAAA,QAAQ,CAAC8F,SAAT,GAAqB,CAArB,CANiJ,CAQjJ;;AACA,gBAAM2D,IAAI,GAAGvH,CAAC,GAAGoB,IAAI,CAACM,GAAL,CAASyF,UAAT,IAAuBD,WAAxC;AACA,gBAAMM,IAAI,GAAGvH,CAAC,GAAGmB,IAAI,CAACO,GAAL,CAASwF,UAAT,IAAuBD,WAAxC,CAViJ,CAYjJ;;AACApJ,UAAAA,QAAQ,CAAC4G,MAAT,CAAgB1E,CAAhB,EAAmBC,CAAnB;AACAnC,UAAAA,QAAQ,CAAC6G,MAAT,CAAgB4C,IAAhB,EAAsBC,IAAtB,EAdiJ,CAgBjJ;;AACA,gBAAMjC,eAAe,GAAG,CAAxB;AACA,gBAAMC,cAAc,GAAGpE,IAAI,CAACG,EAAL,GAAU,CAAjC,CAlBiJ,CAkB7G;;AAEpC,gBAAMoE,KAAK,GAAG4B,IAAI,GAAGnG,IAAI,CAACM,GAAL,CAASyF,UAAU,GAAG3B,cAAtB,IAAwCD,eAA7D;AACA,gBAAMK,KAAK,GAAG4B,IAAI,GAAGpG,IAAI,CAACO,GAAL,CAASwF,UAAU,GAAG3B,cAAtB,IAAwCD,eAA7D;AACA,gBAAMM,MAAM,GAAG0B,IAAI,GAAGnG,IAAI,CAACM,GAAL,CAASyF,UAAU,GAAG3B,cAAtB,IAAwCD,eAA9D;AACA,gBAAMO,MAAM,GAAG0B,IAAI,GAAGpG,IAAI,CAACO,GAAL,CAASwF,UAAU,GAAG3B,cAAtB,IAAwCD,eAA9D;AAEAzH,UAAAA,QAAQ,CAAC4G,MAAT,CAAgBiB,KAAhB,EAAuBC,KAAvB;AACA9H,UAAAA,QAAQ,CAAC6G,MAAT,CAAgB4C,IAAhB,EAAsBC,IAAtB;AACA1J,UAAAA,QAAQ,CAAC6G,MAAT,CAAgBkB,MAAhB,EAAwBC,MAAxB;AAEAhI,UAAAA,QAAQ,CAACgG,MAAT;AACH;AAED;AACJ;AACA;;;AACiC,eAAtBuC,sBAAsB,CAACJ,KAAD,EAAgBwB,KAAhB,EAA+BjJ,QAAgB,GAAG,CAAlD,EAAqDC,MAAc,GAAG,CAAC,CAAvE,EAAiF;AAC1G,cAAIA,MAAM,KAAK,CAAC,CAAhB,EAAmBA,MAAM,GAAGgJ,KAAK,GAAG,CAAjB,CADuF,CAG1G;;AACA,cAAIxB,KAAK,KAAKzH,QAAd,EAAwB,OAAOxC,KAAK,CAAC8I,KAAb,CAAxB,CACA;AADA,eAEK,IAAImB,KAAK,KAAKxH,MAAd,EAAsB,OAAOzC,KAAK,CAAC+I,GAAb,CAAtB,CACL;AADK,eAEA,OAAO/I,KAAK,CAAC0L,KAAb;AACR;AAED;AACJ;AACA;;;AAC+B,eAApBd,oBAAoB,CAAC9I,QAAD,EAAqB4B,SAArB,EAA2CwG,QAA3C,EAAoEe,YAApE,EAA2F;AAClH,gBAAMC,WAAW,GAAG9F,IAAI,CAACgE,GAAL,CAAS1F,SAAS,CAACiH,KAAV,GAAkB,EAA3B,EAA+B,GAA/B,CAApB;AACA,gBAAMQ,UAAU,GAAG,KAAKC,mBAAL,CAAyB1H,SAAzB,EAAoCwG,QAApC,EAA8Ce,YAA9C,CAAnB,CAFkH,CAIlH;;AACAnJ,UAAAA,QAAQ,CAAC4F,WAAT,GAAuB,KAAK2D,8BAAL,CAAoC3H,SAAS,CAAC4H,eAA9C,CAAvB;AACAxJ,UAAAA,QAAQ,CAAC8F,SAAT,GAAqB,CAArB,CANkH,CAQlH;;AACA,gBAAM2D,IAAI,GAAGnG,IAAI,CAACM,GAAL,CAASyF,UAAT,IAAuBD,WAApC;AACA,gBAAMM,IAAI,GAAGpG,IAAI,CAACO,GAAL,CAASwF,UAAT,IAAuBD,WAApC,CAVkH,CAYlH;;AACApJ,UAAAA,QAAQ,CAAC4G,MAAT,CAAgB,CAAhB,EAAmB,CAAnB;AACA5G,UAAAA,QAAQ,CAAC6G,MAAT,CAAgB4C,IAAhB,EAAsBC,IAAtB,EAdkH,CAgBlH;;AACA,gBAAMjC,eAAe,GAAG,CAAxB;AACA,gBAAMC,cAAc,GAAGpE,IAAI,CAACG,EAAL,GAAU,CAAjC,CAlBkH,CAkB9E;;AAEpC,gBAAMoE,KAAK,GAAG4B,IAAI,GAAGnG,IAAI,CAACM,GAAL,CAASyF,UAAU,GAAG3B,cAAtB,IAAwCD,eAA7D;AACA,gBAAMK,KAAK,GAAG4B,IAAI,GAAGpG,IAAI,CAACO,GAAL,CAASwF,UAAU,GAAG3B,cAAtB,IAAwCD,eAA7D;AACA,gBAAMM,MAAM,GAAG0B,IAAI,GAAGnG,IAAI,CAACM,GAAL,CAASyF,UAAU,GAAG3B,cAAtB,IAAwCD,eAA9D;AACA,gBAAMO,MAAM,GAAG0B,IAAI,GAAGpG,IAAI,CAACO,GAAL,CAASwF,UAAU,GAAG3B,cAAtB,IAAwCD,eAA9D;AAEAzH,UAAAA,QAAQ,CAAC4G,MAAT,CAAgBiB,KAAhB,EAAuBC,KAAvB;AACA9H,UAAAA,QAAQ,CAAC6G,MAAT,CAAgB4C,IAAhB,EAAsBC,IAAtB;AACA1J,UAAAA,QAAQ,CAAC6G,MAAT,CAAgBkB,MAAhB,EAAwBC,MAAxB;AAEAhI,UAAAA,QAAQ,CAACgG,MAAT;AACH;AAED;AACJ;AACA;;;AAC8B,eAAnBsD,mBAAmB,CAAC1H,SAAD,EAAuBwG,QAAvB,EAAgDe,YAAhD,EAA+E;AACrG,kBAAQvH,SAAS,CAAC4H,eAAlB;AACI,iBAAK;AAAA;AAAA,sDAAiBK,IAAtB;AACI,qBAAO,KAAKC,0BAAL,CAAgClI,SAAhC,EAA2CwG,QAA3C,EAAqDe,YAArD,CAAP;;AAEJ,iBAAK;AAAA;AAAA,sDAAiBY,MAAtB;AACI,qBAAO,KAAKC,wBAAL,CAA8BpI,SAA9B,CAAP;;AAEJ,iBAAK;AAAA;AAAA,sDAAiBqI,KAAtB;AACI;AACA,qBAAQrI,SAAS,CAACsI,gBAAV,GAA6B5G,IAAI,CAACG,EAAnC,GAAyC,GAAhD;;AAEJ;AACI,qBAAO,CAAP;AAAU;AAZlB;AAcH;AAED;AACJ;AACA;;;AACqC,eAA1BqG,0BAA0B,CAAClI,SAAD,EAAuBwG,QAAvB,EAAgDe,YAAhD,EAA+E;AAC5G,cAAI,CAACf,QAAD,IAAaA,QAAQ,CAACzI,MAAT,IAAmB,CAApC,EAAuC,OAAO,CAAP,CADqE,CAG5G;;AACA,cAAIwJ,YAAY,KAAKgB,SAArB,EAAgC;AAC5BhB,YAAAA,YAAY,GAAG,CAAC,CAAhB;;AACA,iBAAK,IAAIzF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG0E,QAAQ,CAACzI,MAA7B,EAAqC+D,CAAC,EAAtC,EAA0C;AACtC,oBAAMtB,WAAW,GAAGgG,QAAQ,CAAC1E,CAAD,CAAR,CAAYxD,YAAZ;AAAA;AAAA,qDAApB;;AACA,kBAAIkC,WAAW,IAAIA,WAAW,CAACR,SAAZ,KAA0BA,SAA7C,EAAwD;AACpDuH,gBAAAA,YAAY,GAAGzF,CAAf;AACA;AACH;AACJ;AACJ;;AAED,cAAIyF,YAAY,KAAK,CAAC,CAAlB,IAAuBA,YAAY,KAAKgB,SAA5C,EAAuD,OAAO,CAAP,CAfqD,CAiB5G;;AACA,cAAIhB,YAAY,KAAK,CAAjB,IAAsBf,QAAQ,CAACzI,MAAT,GAAkB,CAA5C,EAA+C;AAC3C,kBAAMyK,SAAS,GAAGhC,QAAQ,CAAC,CAAD,CAAR,CAAYxF,QAA9B;AACA,kBAAMyH,YAAY,GAAGjC,QAAQ,CAACe,YAAD,CAAR,CAAuBvG,QAA5C;AACA,mBAAOU,IAAI,CAACC,KAAL,CAAW6G,SAAS,CAACjI,CAAV,GAAckI,YAAY,CAAClI,CAAtC,EAAyCiI,SAAS,CAAClI,CAAV,GAAcmI,YAAY,CAACnI,CAApE,CAAP;AACH,WAJD,CAKA;AALA,eAMK,IAAIiH,YAAY,KAAKf,QAAQ,CAACzI,MAAT,GAAkB,CAAnC,IAAwCyI,QAAQ,CAACzI,MAAT,GAAkB,CAA9D,EAAiE;AAClE,kBAAM4G,SAAS,GAAG6B,QAAQ,CAACe,YAAY,GAAG,CAAhB,CAAR,CAA2BvG,QAA7C;AACA,kBAAMyH,YAAY,GAAGjC,QAAQ,CAACe,YAAD,CAAR,CAAuBvG,QAA5C;AACA,mBAAOU,IAAI,CAACC,KAAL,CAAW8G,YAAY,CAAClI,CAAb,GAAiBoE,SAAS,CAACpE,CAAtC,EAAyCkI,YAAY,CAACnI,CAAb,GAAiBqE,SAAS,CAACrE,CAApE,CAAP;AACH,WAJI,CAKL;AALK,eAMA,IAAIiH,YAAY,GAAG,CAAf,IAAoBA,YAAY,GAAGf,QAAQ,CAACzI,MAAT,GAAkB,CAAzD,EAA4D;AAC7D,kBAAM4G,SAAS,GAAG6B,QAAQ,CAACe,YAAY,GAAG,CAAhB,CAAR,CAA2BvG,QAA7C;AACA,kBAAMwH,SAAS,GAAGhC,QAAQ,CAACe,YAAY,GAAG,CAAhB,CAAR,CAA2BvG,QAA7C;AACA,mBAAOU,IAAI,CAACC,KAAL,CAAW6G,SAAS,CAACjI,CAAV,GAAcoE,SAAS,CAACpE,CAAnC,EAAsCiI,SAAS,CAAClI,CAAV,GAAcqE,SAAS,CAACrE,CAA9D,CAAP;AACH;;AAED,iBAAO,CAAP,CApC4G,CAoClG;AACb;AAED;AACJ;AACA;;;AACmC,eAAxB8H,wBAAwB,CAACpI,SAAD,EAA+B;AAC1D;AACA;AACA,gBAAM0I,OAAO,GAAG,CAAhB;AACA,gBAAMC,OAAO,GAAG,CAAC,GAAjB;AAEA,iBAAOjH,IAAI,CAACC,KAAL,CAAWgH,OAAO,GAAG3I,SAAS,CAACO,CAA/B,EAAkCmI,OAAO,GAAG1I,SAAS,CAACM,CAAtD,CAAP;AACH;AAED;AACJ;AACA;;;AACyC,eAA9BqH,8BAA8B,CAACC,eAAD,EAAiC;AAClE,kBAAQA,eAAR;AACI,iBAAK;AAAA;AAAA,sDAAiBK,IAAtB;AACI,qBAAO3L,KAAK,CAACsM,IAAb;AAAwB;;AAE5B,iBAAK;AAAA;AAAA,sDAAiBT,MAAtB;AACI,qBAAO7L,KAAK,CAACuM,OAAb;AAAwB;;AAE5B,iBAAK;AAAA;AAAA,sDAAiBR,KAAtB;AACI,qBAAO,IAAI/L,KAAJ,CAAU,GAAV,EAAe,GAAf,EAAoB,CAApB,EAAuB,GAAvB,CAAP;AAAqC;;AAEzC;AACI,qBAAOA,KAAK,CAACsM,IAAb;AAAwB;AAXhC;AAaH;;AAjnBqC,O;;;;;iBA8DXtM,KAAK,CAAC0L,K;;;;;;;iBAGF,K", "sourcesContent": ["import { _decorator, Enum, Node, Color, Component, UITransform, JsonAsset, CCInteger, Graphics, Vec2, Vec3 } from 'cc';\r\nconst { ccclass, executeInEditMode, property, disallowMultiple, menu, requireComponent } = _decorator;\r\nimport { EDITOR } from 'cc/env';\r\nimport { ePathType, PathData, PathPoint } from 'db://assets/bundles/common/script/game/data/PathData';\r\nimport { PathPointEditor } from './PathPointEditor';\r\nimport { eOrientationType } from 'db://assets/bundles/common/script/game/move/IMovable';\r\n\r\n@ccclass('PathEditor')\r\n@menu(\"怪物/编辑器/路径编辑\")\r\n@requireComponent(Graphics)\r\n@executeInEditMode(true)\r\n@disallowMultiple(true)\r\nexport class PathEditor extends Component {\r\n    private _graphics: Graphics | null = null;\r\n    public get graphics(): Graphics {\r\n        if (!this._graphics) {\r\n            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);\r\n        }\r\n        return this._graphics;\r\n    }\r\n\r\n    @property({ type: JsonAsset, displayName: \"路径数据\" })\r\n    public set pathData(value: JsonAsset) {\r\n        this._pathData = value;\r\n        this.reload();\r\n    }\r\n    public get pathData(): JsonAsset | null {\r\n        return this._pathData;\r\n    }\r\n\r\n    @property({ displayName: \"路径名称\" })\r\n    public get pathName(): string {\r\n        return this._pathDataObj.name;\r\n    }\r\n    public set pathName(value: string) {\r\n        this._pathDataObj.name = value;\r\n    }\r\n\r\n    @property({ type: Enum(ePathType), displayName: \"路径类型\"})\r\n    public get pathType() {\r\n        return this._pathDataObj.pathType;\r\n    }\r\n    public set pathType(value: ePathType) {\r\n        this._pathDataObj.pathType = value;\r\n    }\r\n\r\n    @property({ type: CCInteger, displayName: '起始点'})\r\n    public get startIdx() {\r\n        return this._pathDataObj.startIdx;\r\n    }\r\n    public set startIdx(value: number) {\r\n        this._pathDataObj.startIdx = value;\r\n    }\r\n\r\n    @property({ type: CCInteger, displayName: '结束点(-1代表默认最后个点)'})\r\n    public get endIdx() {\r\n        return this._pathDataObj.endIdx;\r\n    }\r\n    public set endIdx(value: number) {\r\n        this._pathDataObj.endIdx = value;\r\n    }\r\n\r\n    @property({ displayName: \"是否闭合\", visible() {\r\n        // @ts-ignore\r\n        return this._pathDataObj.points.length >= 3;\r\n    }})\r\n    public get isClosed(): boolean {\r\n        return this._pathDataObj.closed;\r\n    }\r\n    public set isClosed(value: boolean) {\r\n        this._pathDataObj.closed = value;\r\n    }\r\n\r\n    @property({ displayName: \"曲线颜色\" })\r\n    public curveColor: Color = Color.WHITE;\r\n\r\n    @property({ displayName: \"显示细分线段\" })\r\n    public showSegments: boolean = false; // 是否使用不同颜色来绘制不同的细分线段\r\n\r\n    private _showDirectionArrow: boolean = true;\r\n    private _pathData: JsonAsset | null = null;\r\n    private _pathDataObj: PathData = new PathData();\r\n    private _cachedChildrenCount: number = 0;\r\n\r\n    onLoad() {\r\n        const uiTrans = this.getComponent(UITransform);\r\n        uiTrans!.setContentSize(1, 1); // 让这个点不好选中\r\n    }\r\n\r\n    public reload() {\r\n        if (!this._pathData) return;\r\n\r\n        const pathData = PathData.fromJSON(this._pathData.json);\r\n        this._pathDataObj = pathData;\r\n\r\n        this.node.removeAllChildren();\r\n        if (this._pathDataObj && this._pathDataObj.points.length > 0) {\r\n            this._pathDataObj.points.forEach((point:PathPoint) => {\r\n                this.addPoint(point);\r\n            });\r\n        }\r\n    }\r\n\r\n    public save(): string {\r\n        // 收集所有路径点数据\r\n        const pointEditors = this.getComponentsInChildren(PathPointEditor);\r\n        this._pathDataObj.points = pointEditors.map((editor) => editor.pathPoint);\r\n        return JSON.stringify(this._pathDataObj, null, 2);\r\n    }\r\n\r\n    public addPoint(point: PathPoint): Node {\r\n        const pointNode = new Node();\r\n        pointNode.parent = this.node;\r\n        pointNode.setPosition(point.x, point.y, 0);\r\n\r\n        const pointEditor = pointNode.addComponent(PathPointEditor);\r\n        pointEditor.pathPoint = point;\r\n        return pointNode;\r\n    }\r\n\r\n    public addNewPoint(x: number, y: number): Node {\r\n        const point = new PathPoint(x, y);\r\n        const node = this.addPoint(point);\r\n        this.updateCurve();\r\n        return node;\r\n    }\r\n\r\n    public flipHorizontal() {\r\n        const points = this.getSelectedPoints();\r\n        // 计算选中点的中心点，然后反转\r\n        const center = this.calculateCenter(points);\r\n        points.forEach((pointEditor) => {\r\n            const pos = pointEditor.node.position.toVec2();\r\n            const x = center.x * 2 - pos.x;\r\n            pointEditor.node.setPosition(x, pos.y, 0);\r\n        });\r\n    }\r\n\r\n    public flipVertical() {\r\n        const points = this.getSelectedPoints();\r\n        // 计算选中点的中心点，然后反转\r\n        const center = this.calculateCenter(points);\r\n        points.forEach((pointEditor) => {\r\n            const pos = pointEditor.node.position.toVec2();\r\n            const y = center.y * 2 - pos.y;\r\n            pointEditor.node.setPosition(pos.x, y, 0);\r\n        });\r\n    }\r\n\r\n    public fitToCircle() {\r\n        // 将选中点拟合到一个圆上\r\n        const points = this.getSelectedPoints();\r\n        if (points.length < 3) return;\r\n\r\n        const center = this.calculateCenter(points);\r\n        const radius = this.calculateRadius(points, center);\r\n\r\n        // 第一个点位置保持不变，以第一个点到圆心的方向作为起始角度\r\n        const firstPoint = points[0];\r\n        const firstPos = firstPoint.node.getPosition();\r\n        // 计算第一个点相对于圆心的角度作为起始角度\r\n        const startAngle = Math.atan2(firstPos.y - center.y, firstPos.x - center.x);\r\n        // 计算角度间隔（平分圆周）\r\n        const angleStep = (Math.PI * 2) / points.length;\r\n        \r\n        for (let i = 0; i < points.length; i++) {\r\n            const angle = startAngle + angleStep * i;\r\n            const x = center.x + Math.cos(angle) * radius;\r\n            const y = center.y + Math.sin(angle) * radius;\r\n            points[i].node.setPosition(x, y, 0);\r\n        }\r\n    }\r\n\r\n    private getSelectedPoints(): PathPointEditor[] {\r\n        const selected = Editor.Selection.getSelected('node');\r\n        if (!selected || selected.length === 0) {\r\n            console.log(\"no node selected\");\r\n            return [];\r\n        }\r\n        let points: PathPointEditor[] = []\r\n        selected.forEach((uuid) => {\r\n            const node = this.node.getChildByUuid(uuid);\r\n            if (node) {\r\n                const pointEditor = node.getComponent(PathPointEditor);\r\n                if (pointEditor) {\r\n                    points.push(pointEditor);\r\n                }\r\n            }\r\n        });\r\n        points.sort((a, b) => a.node.getSiblingIndex() - b.node.getSiblingIndex());\r\n        return points;\r\n    }\r\n\r\n    private calculateCenter(points: PathPointEditor[]): Vec2 {\r\n        let totalX = 0;\r\n        let totalY = 0;\r\n        let totalCount = 0;\r\n        points.forEach((pointEditor) => {\r\n            totalCount++;\r\n            const pos = pointEditor.node.position.toVec2();\r\n            totalX += pos.x;\r\n            totalY += pos.y;\r\n        });\r\n        return new Vec2(totalX / totalCount, totalY / totalCount);\r\n    }\r\n\r\n    private calculateRadius(points: PathPointEditor[], center: Vec2): number {\r\n        let totalDistance = 0;\r\n        points.forEach((pointEditor) => {\r\n            const pos = pointEditor.node.position.toVec2();\r\n            totalDistance += Vec2.distance(pos, center);\r\n        });\r\n        return totalDistance / points.length;\r\n    }\r\n\r\n    public updateCurve() {\r\n        // 收集当前所有点的数据\r\n        const pointEditors = this.getComponentsInChildren(PathPointEditor);\r\n        this._pathDataObj.points = pointEditors.map((editor) => editor.pathPoint);\r\n    }\r\n\r\n    public update(_dt: number) {\r\n        const childrenCount = this.node.children.length;\r\n        if (childrenCount !== this._cachedChildrenCount) {\r\n            this._cachedChildrenCount = childrenCount;\r\n        }\r\n\r\n        // 把自己的位置锁定在原点，避免出现绘制偏移\r\n        this.node.position = Vec3.ZERO;\r\n        this.updateCurve();\r\n        this.drawPath();\r\n    }\r\n\r\n    private drawPath() {\r\n        const graphics = this.graphics;\r\n        graphics.clear();\r\n        if (this._pathDataObj.pathType === ePathType.Custom) {\r\n            this.drawCustomPath();\r\n        } else {\r\n            this.drawCirclePath();\r\n        }\r\n    }\r\n\r\n    private drawCirclePath() {\r\n        const graphics = this.graphics;\r\n        const pathData = this._pathDataObj;\r\n        const radius = pathData.radius;\r\n        const centerX = pathData.centerX;\r\n        const centerY = pathData.centerY;\r\n        const startAngle = pathData.startAngle;\r\n\r\n        // draw using graphics\r\n        graphics.strokeColor = this.curveColor;\r\n        graphics.lineWidth = 8;\r\n        graphics.arc(centerX, centerY, radius, startAngle, startAngle + Math.PI * 2, false);\r\n        graphics.stroke();\r\n    }\r\n    \r\n    private drawCustomPath() {\r\n        // console.log(`drawPath points length: `, this._pathDataObj.points.length);\r\n        if (this._pathDataObj.points.length < 2) return;\r\n\r\n        const graphics = this.graphics;\r\n        const subdivided = this._pathDataObj.getSubdividedPoints(true);\r\n        if (subdivided.length > 1) {\r\n            if (this.showSegments) {\r\n                PathEditor.drawSegmentedPath(graphics, subdivided, this._pathDataObj.closed, 8);\r\n            } else {\r\n                PathEditor.drawUniformPath(graphics, subdivided, this.curveColor, this._pathDataObj.closed, 8);\r\n            }\r\n\r\n            // 绘制路径终点的方向箭头（仅对非闭合路径）\r\n            if (this._showDirectionArrow && !this._pathDataObj.closed) {\r\n                const endPoint = subdivided[subdivided.length - 1];\r\n                let prevPoint = subdivided[subdivided.length - 2];\r\n                if (subdivided.length >= 5) {\r\n                    prevPoint = subdivided[subdivided.length - 5];\r\n                }\r\n                const direction = Math.atan2(endPoint.y - prevPoint.y, endPoint.x - prevPoint.x);\r\n                PathEditor.drawPathDirectionArrow(graphics, endPoint.position, direction, this._pathDataObj.closed);\r\n            }\r\n        }\r\n        \r\n        if (this.showSegments) {\r\n            console.log('subdivided points length: ', subdivided.length);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 绘制统一颜色的路径\r\n     */\r\n    static drawUniformPath(graphics: Graphics, subdivided: PathPoint[], color: Color, closed: boolean, width:number = 5) {\r\n        graphics.strokeColor = color;\r\n        graphics.lineWidth = width;\r\n\r\n        graphics.moveTo(subdivided[0].x, subdivided[0].y);\r\n        for (let i = 1; i < subdivided.length; i++) {\r\n            graphics.lineTo(subdivided[i].x, subdivided[i].y);\r\n        }\r\n\r\n        // 如果是闭合路径，连接回起点\r\n        if (closed) {\r\n            graphics.lineTo(subdivided[0].x, subdivided[0].y);\r\n        }\r\n\r\n        graphics.stroke();\r\n    }\r\n\r\n    /**\r\n     * 绘制分段着色的路径 - 每个细分段用不同颜色\r\n     */\r\n    static drawSegmentedPath(graphics: Graphics, subdivided: PathPoint[], closed: boolean, width:number = 5) {\r\n        graphics.lineWidth = width;\r\n\r\n        if (subdivided.length < 2) return;\r\n\r\n        // 为每个细分段绘制不同的颜色\r\n        for (let i = 0; i < subdivided.length - 1; i++) {\r\n            const t = i / (subdivided.length - 1); // 计算当前位置的归一化参数 [0,1]\r\n\r\n            // 从绿色到红色的颜色插值\r\n            const color = this.interpolateColor(Color.GREEN, Color.RED, t);\r\n            graphics.strokeColor = color;\r\n\r\n            // 绘制当前段\r\n            graphics.moveTo(subdivided[i].x, subdivided[i].y);\r\n            graphics.lineTo(subdivided[i + 1].x, subdivided[i + 1].y);\r\n            graphics.stroke();\r\n\r\n            // draw arrow head\r\n            const direction = Math.atan2(subdivided[i + 1].y - subdivided[i].y, subdivided[i + 1].x - subdivided[i].x);\r\n            PathEditor.drawPathDirectionArrow(graphics, subdivided[i + 1].position, direction, closed);\r\n        }\r\n\r\n        // 如果是闭合路径，绘制最后一段回到起点\r\n        if (closed && subdivided.length > 2) {\r\n            const lastColor = this.interpolateColor(Color.GREEN, Color.RED, 1.0);\r\n            graphics.strokeColor = lastColor;\r\n            graphics.moveTo(subdivided[subdivided.length - 1].x, subdivided[subdivided.length - 1].y);\r\n            graphics.lineTo(subdivided[0].x, subdivided[0].y);\r\n            graphics.stroke();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 颜色插值函数\r\n     * @param color1 起始颜色\r\n     * @param color2 结束颜色\r\n     * @param t 插值参数 [0,1]\r\n     */\r\n    static interpolateColor(color1: Color, color2: Color, t: number): Color {\r\n        t = Math.max(0, Math.min(1, t)); // 确保t在[0,1]范围内\r\n\r\n        const r = color1.r + (color2.r - color1.r) * t;\r\n        const g = color1.g + (color2.g - color1.g) * t;\r\n        const b = color1.b + (color2.b - color1.b) * t;\r\n        const a = color1.a + (color2.a - color1.a) * t;\r\n\r\n        return new Color(r, g, b, a);\r\n    }\r\n\r\n    /**\r\n     * 绘制路径方向箭头\r\n     */\r\n    static drawPathDirectionArrow(graphics: Graphics, position: Vec2, direction: number, closed: boolean, length: number = 30, width: number = 5) {\r\n        // 如果是闭合路径，不绘制箭头（因为没有明确的终点）\r\n        if (closed) return;\r\n\r\n        // 箭头参数\r\n        const arrowHeadLength = length;\r\n        const arrowHeadAngle = Math.PI / 6; // 30度，适中的角度\r\n\r\n        // 设置箭头样式\r\n        graphics.strokeColor = Color.RED;\r\n        graphics.lineWidth = width;\r\n\r\n        // 计算箭头起点（从路径终点开始）\r\n        const arrowStartX = position.x;\r\n        const arrowStartY = position.y;\r\n\r\n        // 计算箭头两条线段的端点\r\n        const leftX = arrowStartX - Math.cos(direction - arrowHeadAngle) * arrowHeadLength;\r\n        const leftY = arrowStartY - Math.sin(direction - arrowHeadAngle) * arrowHeadLength;\r\n        const rightX = arrowStartX - Math.cos(direction + arrowHeadAngle) * arrowHeadLength;\r\n        const rightY = arrowStartY - Math.sin(direction + arrowHeadAngle) * arrowHeadLength;\r\n\r\n        // 绘制箭头的两条线段（\">\"形状）\r\n        // 第一条线段：从箭头尖端到左侧\r\n        graphics.moveTo(arrowStartX, arrowStartY);\r\n        graphics.lineTo(leftX, leftY);\r\n        graphics.stroke();\r\n\r\n        // 第二条线段：从箭头尖端到右侧\r\n        graphics.moveTo(arrowStartX, arrowStartY);\r\n        graphics.lineTo(rightX, rightY);\r\n        graphics.stroke();\r\n    }\r\n\r\n    /**\r\n     * 绘制路径点（在节点本地坐标系中，用于 PathPointEditor）\r\n     */\r\n    static drawPathPoint(graphics: Graphics, pathPoint: PathPoint, selected: boolean, pointSize: number = 20, index: number, totalCount: number, startIdx: number = 0, endIdx: number = -1, siblings?: Node[], clearGraphics: boolean = true) {\r\n        if (clearGraphics) {\r\n            graphics.clear();\r\n        }\r\n\r\n        // 绘制点（在本地坐标系中心）\r\n        const color = selected ? Color.YELLOW : this.getDefaultColorByIndex(index, totalCount, startIdx, endIdx);\r\n        graphics.fillColor = color;\r\n        graphics.strokeColor = Color.BLACK;\r\n        graphics.lineWidth = 5;\r\n\r\n        graphics.circle(0, 0, pointSize);\r\n        graphics.fill();\r\n        graphics.stroke();\r\n\r\n        // 绘制平滑程度指示器\r\n        if (pathPoint.smoothness > 0) {\r\n            graphics.strokeColor = Color.GREEN;\r\n            graphics.lineWidth = 5;\r\n            const radius = pointSize + 5 + pathPoint.smoothness * 10;\r\n            graphics.circle(0, 0, radius);\r\n            graphics.stroke();\r\n        }\r\n\r\n        // 绘制朝向指示器（箭头）\r\n        if (pathPoint.speed > 0) {\r\n            this.drawOrientationArrow(graphics, pathPoint, siblings || null, index);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 在指定位置绘制路径点（用于 WavePreview 等需要在统一 Graphics 上绘制多个点的场景）\r\n     */\r\n    static drawPathPointAtPosition(graphics: Graphics, pathPoint: PathPoint, x: number, y: number, selected: boolean, pointSize: number = 20, index: number, totalCount: number, startIdx: number = 0, endIdx: number = -1,\r\n        drawSmoothness: boolean = true, drawOrientation: boolean = true\r\n    ) {\r\n        // 绘制点\r\n        const color = selected ? Color.YELLOW : this.getDefaultColorByIndex(index, totalCount, startIdx, endIdx);\r\n        graphics.fillColor = color;\r\n        graphics.strokeColor = Color.BLACK;\r\n        graphics.lineWidth = 5;\r\n\r\n        graphics.circle(x, y, pointSize);\r\n        graphics.fill();\r\n        graphics.stroke();\r\n\r\n        // 绘制平滑程度指示器\r\n        if (pathPoint.smoothness > 0 && drawSmoothness) {\r\n            graphics.strokeColor = Color.GREEN;\r\n            graphics.lineWidth = 5;\r\n            const radius = pointSize + 5 + pathPoint.smoothness * 10;\r\n            graphics.circle(x, y, radius);\r\n            graphics.stroke();\r\n        }\r\n\r\n        // 绘制朝向指示器（箭头）\r\n        if (pathPoint.speed > 0 && drawOrientation) {\r\n            this.drawOrientationArrowAtPosition(graphics, pathPoint, null, index, x, y);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 在指定位置绘制朝向箭头\r\n     */\r\n    static drawOrientationArrowAtPosition(graphics: Graphics, pathPoint: PathPoint, siblings: Node[] | null, currentIndex: number, x: number, y: number) {\r\n        const arrowLength = Math.min(pathPoint.speed / 10, 100);\r\n        const arrowAngle = this.calculateArrowAngle(pathPoint, siblings, currentIndex);\r\n\r\n        // 根据朝向类型设置不同颜色\r\n        graphics.strokeColor = this.getArrowColorByOrientationType(pathPoint.orientationType);\r\n        graphics.lineWidth = 3;\r\n\r\n        // 计算箭头终点\r\n        const endX = x + Math.cos(arrowAngle) * arrowLength;\r\n        const endY = y + Math.sin(arrowAngle) * arrowLength;\r\n\r\n        // 绘制箭头主线\r\n        graphics.moveTo(x, y);\r\n        graphics.lineTo(endX, endY);\r\n\r\n        // 绘制箭头头部\r\n        const arrowHeadLength = 8;\r\n        const arrowHeadAngle = Math.PI / 6; // 30度\r\n\r\n        const leftX = endX - Math.cos(arrowAngle - arrowHeadAngle) * arrowHeadLength;\r\n        const leftY = endY - Math.sin(arrowAngle - arrowHeadAngle) * arrowHeadLength;\r\n        const rightX = endX - Math.cos(arrowAngle + arrowHeadAngle) * arrowHeadLength;\r\n        const rightY = endY - Math.sin(arrowAngle + arrowHeadAngle) * arrowHeadLength;\r\n\r\n        graphics.moveTo(leftX, leftY);\r\n        graphics.lineTo(endX, endY);\r\n        graphics.lineTo(rightX, rightY);\r\n\r\n        graphics.stroke();\r\n    }\r\n\r\n    /**\r\n     * 根据索引获取默认颜色\r\n     */\r\n    static getDefaultColorByIndex(index: number, count: number, startIdx: number = 0, endIdx: number = -1): Color {\r\n        if (endIdx === -1) endIdx = count - 1;\r\n\r\n        // 起点\r\n        if (index === startIdx) return Color.GREEN;\r\n        // 终点\r\n        else if (index === endIdx) return Color.RED;\r\n        // 中间点\r\n        else return Color.WHITE;\r\n    }\r\n\r\n    /**\r\n     * 绘制朝向箭头\r\n     */\r\n    static drawOrientationArrow(graphics: Graphics, pathPoint: PathPoint, siblings: Node[] | null, currentIndex?: number) {\r\n        const arrowLength = Math.min(pathPoint.speed / 10, 100);\r\n        const arrowAngle = this.calculateArrowAngle(pathPoint, siblings, currentIndex);\r\n\r\n        // 根据朝向类型设置不同颜色\r\n        graphics.strokeColor = this.getArrowColorByOrientationType(pathPoint.orientationType);\r\n        graphics.lineWidth = 3;\r\n\r\n        // 计算箭头终点\r\n        const endX = Math.cos(arrowAngle) * arrowLength;\r\n        const endY = Math.sin(arrowAngle) * arrowLength;\r\n\r\n        // 绘制箭头主线\r\n        graphics.moveTo(0, 0);\r\n        graphics.lineTo(endX, endY);\r\n\r\n        // 绘制箭头头部\r\n        const arrowHeadLength = 8;\r\n        const arrowHeadAngle = Math.PI / 6; // 30度\r\n\r\n        const leftX = endX - Math.cos(arrowAngle - arrowHeadAngle) * arrowHeadLength;\r\n        const leftY = endY - Math.sin(arrowAngle - arrowHeadAngle) * arrowHeadLength;\r\n        const rightX = endX - Math.cos(arrowAngle + arrowHeadAngle) * arrowHeadLength;\r\n        const rightY = endY - Math.sin(arrowAngle + arrowHeadAngle) * arrowHeadLength;\r\n\r\n        graphics.moveTo(leftX, leftY);\r\n        graphics.lineTo(endX, endY);\r\n        graphics.lineTo(rightX, rightY);\r\n\r\n        graphics.stroke();\r\n    }\r\n\r\n    /**\r\n     * 根据朝向类型计算箭头角度\r\n     */\r\n    static calculateArrowAngle(pathPoint: PathPoint, siblings: Node[] | null, currentIndex?: number): number {\r\n        switch (pathPoint.orientationType) {\r\n            case eOrientationType.Path:\r\n                return this.calculateMovementDirection(pathPoint, siblings, currentIndex);\r\n\r\n            case eOrientationType.Target:\r\n                return this.calculatePlayerDirection(pathPoint);\r\n\r\n            case eOrientationType.Fixed:\r\n                // orientationParam作为固定角度（度）\r\n                return (pathPoint.orientationParam * Math.PI) / 180;\r\n\r\n            default:\r\n                return 0; // 默认向右\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 计算移动方向\r\n     */\r\n    static calculateMovementDirection(pathPoint: PathPoint, siblings: Node[] | null, currentIndex?: number): number {\r\n        if (!siblings || siblings.length <= 1) return 0;\r\n\r\n        // 如果没有提供currentIndex，尝试找到当前点在siblings中的索引\r\n        if (currentIndex === undefined) {\r\n            currentIndex = -1;\r\n            for (let i = 0; i < siblings.length; i++) {\r\n                const pointEditor = siblings[i].getComponent(PathPointEditor);\r\n                if (pointEditor && pointEditor.pathPoint === pathPoint) {\r\n                    currentIndex = i;\r\n                    break;\r\n                }\r\n            }\r\n        }\r\n\r\n        if (currentIndex === -1 || currentIndex === undefined) return 0;\r\n\r\n        // 如果是第一个点，使用到下一个点的方向\r\n        if (currentIndex === 0 && siblings.length > 1) {\r\n            const nextPoint = siblings[1].position;\r\n            const currentPoint = siblings[currentIndex].position;\r\n            return Math.atan2(nextPoint.y - currentPoint.y, nextPoint.x - currentPoint.x);\r\n        }\r\n        // 如果是最后一个点，使用从上一个点的方向\r\n        else if (currentIndex === siblings.length - 1 && siblings.length > 1) {\r\n            const prevPoint = siblings[currentIndex - 1].position;\r\n            const currentPoint = siblings[currentIndex].position;\r\n            return Math.atan2(currentPoint.y - prevPoint.y, currentPoint.x - prevPoint.x);\r\n        }\r\n        // 中间点，使用前后两点的平均方向\r\n        else if (currentIndex > 0 && currentIndex < siblings.length - 1) {\r\n            const prevPoint = siblings[currentIndex - 1].position;\r\n            const nextPoint = siblings[currentIndex + 1].position;\r\n            return Math.atan2(nextPoint.y - prevPoint.y, nextPoint.x - prevPoint.x);\r\n        }\r\n\r\n        return 0; // 默认向右\r\n    }\r\n\r\n    /**\r\n     * 计算朝向玩家的方向\r\n     */\r\n    static calculatePlayerDirection(pathPoint: PathPoint): number {\r\n        // 假设玩家在屏幕底部中央 (0, -400)\r\n        // 在实际游戏中，这应该从游戏状态获取玩家位置\r\n        const playerX = 0;\r\n        const playerY = -400;\r\n\r\n        return Math.atan2(playerY - pathPoint.y, playerX - pathPoint.x);\r\n    }\r\n\r\n    /**\r\n     * 根据朝向类型获取箭头颜色\r\n     */\r\n    static getArrowColorByOrientationType(orientationType: number): Color {\r\n        switch (orientationType) {\r\n            case eOrientationType.Path:\r\n                return Color.BLUE;      // 蓝色：跟随移动方向\r\n\r\n            case eOrientationType.Target:\r\n                return Color.MAGENTA;   // 紫色：朝向玩家\r\n\r\n            case eOrientationType.Fixed:\r\n                return new Color(255, 165, 0, 255);  // 橙色：固定朝向\r\n\r\n            default:\r\n                return Color.BLUE;      // 默认蓝色\r\n        }\r\n    }\r\n}"]}
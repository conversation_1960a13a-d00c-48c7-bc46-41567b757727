System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Label, BundleName, ButtonPlus, BaseUI, UILayer, UIMgr, UITools, List, MatchCellUI, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _crd, ccclass, property, MatchRankUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "db://assets/bundles/common/script/const/BundleConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "db://assets/bundles/common/script/ui/common/components/button/ButtonPlus", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIOpt(extras) {
    _reporterNs.report("UIOpt", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUITools(extras) {
    _reporterNs.report("UITools", "../../game/utils/UITools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfList(extras) {
    _reporterNs.report("List", "../common/components/list/List", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMatchCellUI(extras) {
    _reporterNs.report("MatchCellUI", "./MatchCellUI", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Label = _cc.Label;
    }, function (_unresolved_2) {
      BundleName = _unresolved_2.BundleName;
    }, function (_unresolved_3) {
      ButtonPlus = _unresolved_3.ButtonPlus;
    }, function (_unresolved_4) {
      BaseUI = _unresolved_4.BaseUI;
      UILayer = _unresolved_4.UILayer;
      UIMgr = _unresolved_4.UIMgr;
    }, function (_unresolved_5) {
      UITools = _unresolved_5.UITools;
    }, function (_unresolved_6) {
      List = _unresolved_6.default;
    }, function (_unresolved_7) {
      MatchCellUI = _unresolved_7.MatchCellUI;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "61b55HqQj5OhZrZfRYb3ZAw", "MatchRankUI", undefined);

      __checkObsolete__(['_decorator', 'Label', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("MatchRankUI", MatchRankUI = (_dec = ccclass('MatchRankUI'), _dec2 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec3 = property(Label), _dec4 = property(_crd && List === void 0 ? (_reportPossibleCrUseOfList({
        error: Error()
      }), List) : List), _dec5 = property(Label), _dec6 = property(Label), _dec(_class = (_class2 = class MatchRankUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "btnGo", _descriptor, this);

          _initializerDefineProperty(this, "lblTime", _descriptor2, this);

          this.leftTimes = 0;

          _initializerDefineProperty(this, "list", _descriptor3, this);

          _initializerDefineProperty(this, "nameArr", _descriptor4, this);

          _initializerDefineProperty(this, "scoreArr", _descriptor5, this);

          this.data = [];
        }

        static getUrl() {
          return "prefab/ui/MatchRankUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Default;
        }

        static getBundleName() {
          return (_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).homeMatch;
        }

        static getUIOption() {
          return {
            isClickBgCloseUI: true
          };
        }

        onLoad() {
          this.btnGo.addClick(this.onGoClick, this);
          this.leftTimes = Math.floor(Date.now() / 1000) + 3600 * 20 + 50 * 60 + 10;

          for (let i = 0; i < 10; i++) {
            this.data.push({
              rank: i,
              name: "小师妹",
              score: 10000 + i
            });
          }

          for (let i = 0; i < 3; i++) {
            this.nameArr[i].string = this.data[i].name;
            this.scoreArr[i].string = this.data[i].score.toString();
          }

          this.list.numItems = this.data.length - 3;
        }

        async onGoClick() {
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).closeUI(MatchRankUI);
        }

        async onShow() {}

        async onHide() {}

        async onClose() {}

        update(dt) {
          let left = this.leftTimes - Math.floor(Date.now() / 1000);

          if (left < 0) {
            return;
          }

          this.lblTime.string = "剩余时间：" + (_crd && UITools === void 0 ? (_reportPossibleCrUseOfUITools({
            error: Error()
          }), UITools) : UITools).formatTime(left, true);
        }

        onDestroy() {}

        onListRender(listItem, row) {
          let dataIndex = row + 3;

          if (dataIndex < this.data.length) {
            const cell = listItem.getComponent(_crd && MatchCellUI === void 0 ? (_reportPossibleCrUseOfMatchCellUI({
              error: Error()
            }), MatchCellUI) : MatchCellUI);

            if (cell !== null) {
              cell.setInfo(this.data[dataIndex].rank, this.data[dataIndex].name, this.data[dataIndex].score);
            }
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "btnGo", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "lblTime", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "list", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "nameArr", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "scoreArr", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=631663451fa14c8835b694632d5a08efb2b43d6d.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.onCreateMenu = onCreateMenu;
exports.onAssetMenu = onAssetMenu;
function onCreateMenu(assetInfo) {
    return [
        {
            label: '飞机游戏',
            submenu: [
                {
                    label: '创建波次',
                    click() {
                        console.log('wave');
                        console.log(assetInfo);
                    },
                },
                {
                    label: '创建阵型',
                    click() {
                        console.log('formation');
                        console.log(assetInfo);
                    },
                },
            ],
        },
    ];
}
;
function onAssetMenu(assetInfo) {
    const submenu = [
        {
            label: '创建关卡Prefab',
            //enabled: assetInfo.isDirectory,
            click() {
                createLevelPrefab(assetInfo);
            },
        },
        {
            label: '创建子弹prefab',
            //enabled: !assetInfo.isDirectory,
            click() {
                createBulletPrefabs(assetInfo);
            },
        },
    ];
    // 检查是否是阵型JSON文件
    if (!assetInfo.isDirectory &&
        assetInfo.url.includes('assets/resources/game/level/wave/formation') &&
        assetInfo.url.endsWith('.json')) {
        submenu.push({
            label: '编辑阵型',
            click() {
                editFormation(assetInfo);
            },
        });
    }
    // 检查是否是路径JSON文件
    if (!assetInfo.isDirectory &&
        assetInfo.url.includes('assets/resources/game/level/wave/path') &&
        assetInfo.url.endsWith('.json')) {
        submenu.push({
            label: '编辑路径',
            click() {
                editPath(assetInfo);
            },
        });
    }
    if (!assetInfo.isDirectory &&
        assetInfo.url.includes('assets/resources/game/spine/plane/mainplane/') &&
        assetInfo.url.endsWith('.json')) {
        submenu.push({
            label: '创建自机prefab',
            click() {
                createMainPlanePrefab(assetInfo);
            },
        });
    }
    if (!assetInfo.isDirectory &&
        assetInfo.url.includes('assets/resources/game/spine/plane/enemyplane/') &&
        assetInfo.url.endsWith('.json')) {
        submenu.push({
            label: '创建敌机prefab',
            click() {
                createEnemyPlanePrefab(assetInfo);
            },
        });
    }
    return [
        {
            label: '飞机游戏',
            submenu: submenu,
        },
    ];
}
;
function getAssetUuidByPath(path) {
    return new Promise((resolve, reject) => {
        // @ts-ignore
        Editor.Message.request('asset-db', 'query-uuid', path).then((res) => {
            resolve(res);
        }).catch((err) => {
            console.error('Failed to query uuid:', err);
            reject(err);
        });
    });
}
function getAssetUuidsByPath(path) {
    return new Promise((resolve, reject) => {
        // @ts-ignore
        Editor.Message.request('asset-db', 'query-assets', { pattern: path + '/**' }).then((res) => {
            const arr = Array.isArray(res) ? res : (Array.isArray(res === null || res === void 0 ? void 0 : res[0]) ? res[0] : []);
            const assets = arr
                .filter((a) => a && !a.isDirectory)
                .map((a) => ({
                name: String(a.name || ''),
                path: a.path || '',
                uuid: a.uuid || ''
            }))
                .filter(p => p.name)
                .sort((a, b) => a.name.localeCompare(b.name));
            resolve(assets.map(a => a.uuid)); // 只需要uuid即可，不需要其他信息了。
        }).catch((err) => {
            console.error('Failed to query assets:', err);
            reject(err);
        });
    });
}
function createLevelPrefab(assetInfo) {
    console.log(assetInfo);
    if (assetInfo.isDirectory) {
        getAssetUuidsByPath(assetInfo.url).then((uuids) => {
            uuids.forEach((uuid) => {
                // @ts-ignore
                Editor.Message.request('scene', 'execute-scene-script', {
                    name: 'level-editor',
                    method: 'createLevelPrefab',
                    args: [uuid]
                });
            });
        });
    }
    else {
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'level-editor',
            method: 'createLevelPrefab',
            args: [assetInfo.uuid]
        });
    }
}
function createBulletPrefabs(assetInfo) {
    if (assetInfo.isDirectory) {
        getAssetUuidsByPath(assetInfo.url).then((uuids) => {
            uuids.forEach((uuid) => {
                // @ts-ignore
                Editor.Message.request('scene', 'execute-scene-script', {
                    name: 'level-editor',
                    method: 'createBulletPrefab',
                    args: [uuid]
                });
            });
        });
    }
    else {
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'level-editor',
            method: 'createBulletPrefab',
            args: [assetInfo.uuid]
        });
    }
}
function createMainPlanePrefab(assetInfo) {
    console.log("create mainplane from spine assetInfo", assetInfo);
    // @ts-ignore
    Editor.Message.request('scene', 'execute-scene-script', {
        name: 'level-editor',
        method: 'createMainPlanePrefab',
        args: [assetInfo.uuid]
    });
}
function createEnemyPlanePrefab(assetInfo) {
    console.log("create enemyplane from spine assetInfo", assetInfo);
    // @ts-ignore
    Editor.Message.request('scene', 'execute-scene-script', {
        name: 'level-editor',
        method: 'createEnemyPlanePrefab',
        args: [assetInfo.uuid]
    });
}
function editFormation(assetInfo) {
    console.log('编辑阵型:', assetInfo);
    // 打开FormationEditor场景 Uuid = aa842f3a-8aea-42c2-a480-968aade3dfef
    getAssetUuidByPath('db://assets/scenes/FormationEditor.scene').then((uuid) => {
        // @ts-ignore
        Editor.Message.request('scene', 'open-scene', uuid).then(() => {
            // 场景打开后，加载选中的阵型JSON文件到FormationEditor组件
            // @ts-ignore
            Editor.Message.request('scene', 'execute-scene-script', {
                name: 'level-editor',
                method: 'loadFormationData',
                args: [assetInfo.uuid]
            });
        }).catch((err) => {
            console.error('Failed to open FormationEditor scene:', err);
        });
    });
}
function editPath(assetInfo) {
    console.log('编辑路径:', assetInfo);
    // 打开PathEditor场景
    getAssetUuidByPath('db://assets/scenes/PathEditor.scene').then((uuid) => {
        // @ts-ignore
        Editor.Message.request('scene', 'open-scene', uuid).then(() => {
            // 场景打开后，加载选中的路径JSON文件到PathEditor组件
            // @ts-ignore
            Editor.Message.request('scene', 'execute-scene-script', {
                name: 'level-editor',
                method: 'loadPathData',
                args: [assetInfo.uuid]
            });
        }).catch((err) => {
            console.error('Failed to open PathEditor scene:', err);
        });
    });
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiYXNzZXRzLW1lbnUuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi9zb3VyY2UvYXNzZXRzLW1lbnUudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7QUFJQSxvQ0FzQkM7QUFFRCxrQ0FvRUM7QUE1RkQsU0FBZ0IsWUFBWSxDQUFDLFNBQXdCO0lBQ25ELE9BQU87UUFDTDtZQUNFLEtBQUssRUFBRSxNQUFNO1lBQ2IsT0FBTyxFQUFFO2dCQUNQO29CQUNFLEtBQUssRUFBRSxNQUFNO29CQUNiLEtBQUs7d0JBQ0gsT0FBTyxDQUFDLEdBQUcsQ0FBQyxNQUFNLENBQUMsQ0FBQzt3QkFDcEIsT0FBTyxDQUFDLEdBQUcsQ0FBQyxTQUFTLENBQUMsQ0FBQztvQkFDekIsQ0FBQztpQkFDRjtnQkFDRDtvQkFDRSxLQUFLLEVBQUUsTUFBTTtvQkFDYixLQUFLO3dCQUNILE9BQU8sQ0FBQyxHQUFHLENBQUMsV0FBVyxDQUFDLENBQUM7d0JBQ3pCLE9BQU8sQ0FBQyxHQUFHLENBQUMsU0FBUyxDQUFDLENBQUM7b0JBQ3pCLENBQUM7aUJBQ0Y7YUFDRjtTQUNGO0tBQ0YsQ0FBQztBQUNKLENBQUM7QUFBQSxDQUFDO0FBRUYsU0FBZ0IsV0FBVyxDQUFDLFNBQXdCO0lBQ2xELE1BQU0sT0FBTyxHQUFHO1FBQ2Q7WUFDRSxLQUFLLEVBQUUsWUFBWTtZQUNuQixpQ0FBaUM7WUFDakMsS0FBSztnQkFDSCxpQkFBaUIsQ0FBQyxTQUFTLENBQUMsQ0FBQztZQUMvQixDQUFDO1NBQ0Y7UUFDRDtZQUNFLEtBQUssRUFBRSxZQUFZO1lBQ25CLGtDQUFrQztZQUNsQyxLQUFLO2dCQUNILG1CQUFtQixDQUFDLFNBQVMsQ0FBQyxDQUFDO1lBQ2pDLENBQUM7U0FDRjtLQUNGLENBQUM7SUFFRixnQkFBZ0I7SUFDaEIsSUFBSSxDQUFDLFNBQVMsQ0FBQyxXQUFXO1FBQ3RCLFNBQVMsQ0FBQyxHQUFHLENBQUMsUUFBUSxDQUFDLDRDQUE0QyxDQUFDO1FBQ3BFLFNBQVMsQ0FBQyxHQUFHLENBQUMsUUFBUSxDQUFDLE9BQU8sQ0FBQyxFQUFFLENBQUM7UUFDcEMsT0FBTyxDQUFDLElBQUksQ0FBQztZQUNYLEtBQUssRUFBRSxNQUFNO1lBQ2IsS0FBSztnQkFDSCxhQUFhLENBQUMsU0FBUyxDQUFDLENBQUM7WUFDM0IsQ0FBQztTQUNGLENBQUMsQ0FBQztJQUNMLENBQUM7SUFFRCxnQkFBZ0I7SUFDaEIsSUFBSSxDQUFDLFNBQVMsQ0FBQyxXQUFXO1FBQ3RCLFNBQVMsQ0FBQyxHQUFHLENBQUMsUUFBUSxDQUFDLHVDQUF1QyxDQUFDO1FBQy9ELFNBQVMsQ0FBQyxHQUFHLENBQUMsUUFBUSxDQUFDLE9BQU8sQ0FBQyxFQUFFLENBQUM7UUFDcEMsT0FBTyxDQUFDLElBQUksQ0FBQztZQUNYLEtBQUssRUFBRSxNQUFNO1lBQ2IsS0FBSztnQkFDSCxRQUFRLENBQUMsU0FBUyxDQUFDLENBQUM7WUFDdEIsQ0FBQztTQUNGLENBQUMsQ0FBQztJQUNMLENBQUM7SUFDRCxJQUFJLENBQUMsU0FBUyxDQUFDLFdBQVc7UUFDdEIsU0FBUyxDQUFDLEdBQUcsQ0FBQyxRQUFRLENBQUMsOENBQThDLENBQUM7UUFDdEUsU0FBUyxDQUFDLEdBQUcsQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLEVBQUUsQ0FBQztRQUNwQyxPQUFPLENBQUMsSUFBSSxDQUFDO1lBQ1gsS0FBSyxFQUFFLFlBQVk7WUFDbkIsS0FBSztnQkFDSCxxQkFBcUIsQ0FBQyxTQUFTLENBQUMsQ0FBQztZQUNuQyxDQUFDO1NBQ0YsQ0FBQyxDQUFDO0lBQ0wsQ0FBQztJQUNELElBQUksQ0FBQyxTQUFTLENBQUMsV0FBVztRQUN0QixTQUFTLENBQUMsR0FBRyxDQUFDLFFBQVEsQ0FBQywrQ0FBK0MsQ0FBQztRQUN2RSxTQUFTLENBQUMsR0FBRyxDQUFDLFFBQVEsQ0FBQyxPQUFPLENBQUMsRUFBRSxDQUFDO1FBQ3BDLE9BQU8sQ0FBQyxJQUFJLENBQUM7WUFDWCxLQUFLLEVBQUUsWUFBWTtZQUNuQixLQUFLO2dCQUNILHNCQUFzQixDQUFDLFNBQVMsQ0FBQyxDQUFDO1lBQ3BDLENBQUM7U0FDRixDQUFDLENBQUM7SUFDTCxDQUFDO0lBRUQsT0FBTztRQUNMO1lBQ0UsS0FBSyxFQUFFLE1BQU07WUFDYixPQUFPLEVBQUUsT0FBTztTQUNqQjtLQUNGLENBQUM7QUFDSixDQUFDO0FBQUEsQ0FBQztBQUVGLFNBQVMsa0JBQWtCLENBQUMsSUFBWTtJQUN0QyxPQUFPLElBQUksT0FBTyxDQUFDLENBQUMsT0FBTyxFQUFFLE1BQU0sRUFBRSxFQUFFO1FBQ3JDLGFBQWE7UUFDYixNQUFNLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxVQUFVLEVBQUUsWUFBWSxFQUFFLElBQUksQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLEdBQVEsRUFBRSxFQUFFO1lBQ3ZFLE9BQU8sQ0FBQyxHQUFHLENBQUMsQ0FBQztRQUNmLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxDQUFDLEdBQVEsRUFBRSxFQUFFO1lBQ3BCLE9BQU8sQ0FBQyxLQUFLLENBQUMsdUJBQXVCLEVBQUUsR0FBRyxDQUFDLENBQUM7WUFDNUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxDQUFDO1FBQ2QsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztBQUNMLENBQUM7QUFFRCxTQUFTLG1CQUFtQixDQUFDLElBQVk7SUFDckMsT0FBTyxJQUFJLE9BQU8sQ0FBQyxDQUFDLE9BQU8sRUFBRSxNQUFNLEVBQUUsRUFBRTtRQUNuQyxhQUFhO1FBQ2IsTUFBTSxDQUFDLE9BQU8sQ0FBQyxPQUFPLENBQUMsVUFBVSxFQUFFLGNBQWMsRUFBRSxFQUFFLE9BQU8sRUFBRSxJQUFJLEdBQUcsS0FBSyxFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxHQUFRLEVBQUUsRUFBRTtZQUM1RixNQUFNLEdBQUcsR0FBVSxLQUFLLENBQUMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsS0FBSyxDQUFDLE9BQU8sQ0FBQyxHQUFHLGFBQUgsR0FBRyx1QkFBSCxHQUFHLENBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQztZQUN0RixNQUFNLE1BQU0sR0FBRyxHQUFHO2lCQUNiLE1BQU0sQ0FBQyxDQUFDLENBQU0sRUFBRSxFQUFFLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLFdBQVcsQ0FBQztpQkFDdkMsR0FBRyxDQUFDLENBQUMsQ0FBTSxFQUFFLEVBQUUsQ0FBQyxDQUFDO2dCQUNkLElBQUksRUFBRSxNQUFNLENBQUMsQ0FBQyxDQUFDLElBQUksSUFBSSxFQUFFLENBQUM7Z0JBQzFCLElBQUksRUFBRSxDQUFDLENBQUMsSUFBSSxJQUFJLEVBQUU7Z0JBQ2xCLElBQUksRUFBRSxDQUFDLENBQUMsSUFBSSxJQUFJLEVBQUU7YUFDckIsQ0FBQyxDQUFDO2lCQUNGLE1BQU0sQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUM7aUJBQ25CLElBQUksQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDO1lBQ2xELE9BQU8sQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxzQkFBc0I7UUFDNUQsQ0FBQyxDQUFDLENBQUMsS0FBSyxDQUFDLENBQUMsR0FBUSxFQUFFLEVBQUU7WUFDbEIsT0FBTyxDQUFDLEtBQUssQ0FBQyx5QkFBeUIsRUFBRSxHQUFHLENBQUMsQ0FBQztZQUM5QyxNQUFNLENBQUMsR0FBRyxDQUFDLENBQUM7UUFDaEIsQ0FBQyxDQUFDLENBQUM7SUFDUCxDQUFDLENBQUMsQ0FBQztBQUNQLENBQUM7QUFFRCxTQUFTLGlCQUFpQixDQUFDLFNBQXdCO0lBQ2pELE9BQU8sQ0FBQyxHQUFHLENBQUMsU0FBUyxDQUFDLENBQUM7SUFFdkIsSUFBSSxTQUFTLENBQUMsV0FBVyxFQUFFLENBQUM7UUFDMUIsbUJBQW1CLENBQUMsU0FBUyxDQUFDLEdBQUcsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLEtBQWUsRUFBRSxFQUFFO1lBQzFELEtBQUssQ0FBQyxPQUFPLENBQUMsQ0FBQyxJQUFZLEVBQUUsRUFBRTtnQkFDN0IsYUFBYTtnQkFDYixNQUFNLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxPQUFPLEVBQUUsc0JBQXNCLEVBQUU7b0JBQ3RELElBQUksRUFBRSxjQUFjO29CQUNwQixNQUFNLEVBQUUsbUJBQW1CO29CQUMzQixJQUFJLEVBQUUsQ0FBQyxJQUFJLENBQUM7aUJBQ2IsQ0FBQyxDQUFDO1lBQ0wsQ0FBQyxDQUFDLENBQUM7UUFDTCxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUM7U0FBTSxDQUFDO1FBQ04sYUFBYTtRQUNiLE1BQU0sQ0FBQyxPQUFPLENBQUMsT0FBTyxDQUFDLE9BQU8sRUFBRSxzQkFBc0IsRUFBRTtZQUN0RCxJQUFJLEVBQUUsY0FBYztZQUNwQixNQUFNLEVBQUUsbUJBQW1CO1lBQzNCLElBQUksRUFBRSxDQUFDLFNBQVMsQ0FBQyxJQUFJLENBQUM7U0FDdkIsQ0FBQyxDQUFDO0lBQ0wsQ0FBQztBQUNILENBQUM7QUFFRCxTQUFTLG1CQUFtQixDQUFDLFNBQXdCO0lBQ2pELElBQUksU0FBUyxDQUFDLFdBQVcsRUFBRSxDQUFDO1FBQ3hCLG1CQUFtQixDQUFDLFNBQVMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxLQUFlLEVBQUUsRUFBRTtZQUN4RCxLQUFLLENBQUMsT0FBTyxDQUFDLENBQUMsSUFBWSxFQUFFLEVBQUU7Z0JBQzFCLGFBQWE7Z0JBQ2QsTUFBTSxDQUFDLE9BQU8sQ0FBQyxPQUFPLENBQUMsT0FBTyxFQUFFLHNCQUFzQixFQUFFO29CQUNwRCxJQUFJLEVBQUUsY0FBYztvQkFDcEIsTUFBTSxFQUFFLG9CQUFvQjtvQkFDNUIsSUFBSSxFQUFFLENBQUMsSUFBSSxDQUFDO2lCQUNmLENBQUMsQ0FBQztZQUNQLENBQUMsQ0FBQyxDQUFDO1FBQ1AsQ0FBQyxDQUFDLENBQUM7SUFDUCxDQUFDO1NBQU0sQ0FBQztRQUNILGFBQWE7UUFDZCxNQUFNLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxPQUFPLEVBQUUsc0JBQXNCLEVBQUU7WUFDcEQsSUFBSSxFQUFFLGNBQWM7WUFDcEIsTUFBTSxFQUFFLG9CQUFvQjtZQUM1QixJQUFJLEVBQUUsQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDO1NBQ3pCLENBQUMsQ0FBQztJQUNQLENBQUM7QUFDTCxDQUFDO0FBRUQsU0FBUyxxQkFBcUIsQ0FBQyxTQUF3QjtJQUNyRCxPQUFPLENBQUMsR0FBRyxDQUFDLHVDQUF1QyxFQUFFLFNBQVMsQ0FBQyxDQUFDO0lBRWhFLGFBQWE7SUFDYixNQUFNLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxPQUFPLEVBQUUsc0JBQXNCLEVBQUU7UUFDdEQsSUFBSSxFQUFFLGNBQWM7UUFDcEIsTUFBTSxFQUFFLHVCQUF1QjtRQUMvQixJQUFJLEVBQUUsQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDO0tBQ3ZCLENBQUMsQ0FBQztBQUNMLENBQUM7QUFDRCxTQUFTLHNCQUFzQixDQUFDLFNBQXdCO0lBQ3RELE9BQU8sQ0FBQyxHQUFHLENBQUMsd0NBQXdDLEVBQUUsU0FBUyxDQUFDLENBQUM7SUFFakUsYUFBYTtJQUNiLE1BQU0sQ0FBQyxPQUFPLENBQUMsT0FBTyxDQUFDLE9BQU8sRUFBRSxzQkFBc0IsRUFBRTtRQUN0RCxJQUFJLEVBQUUsY0FBYztRQUNwQixNQUFNLEVBQUUsd0JBQXdCO1FBQ2hDLElBQUksRUFBRSxDQUFDLFNBQVMsQ0FBQyxJQUFJLENBQUM7S0FDdkIsQ0FBQyxDQUFDO0FBQ0wsQ0FBQztBQUVELFNBQVMsYUFBYSxDQUFDLFNBQXdCO0lBQzdDLE9BQU8sQ0FBQyxHQUFHLENBQUMsT0FBTyxFQUFFLFNBQVMsQ0FBQyxDQUFDO0lBQ2hDLGtFQUFrRTtJQUNsRSxrQkFBa0IsQ0FBQywwQ0FBMEMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLElBQVksRUFBRSxFQUFFO1FBQ25GLGFBQWE7UUFDYixNQUFNLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxPQUFPLEVBQUUsWUFBWSxFQUFFLElBQUksQ0FBQyxDQUFDLElBQUksQ0FBQyxHQUFHLEVBQUU7WUFDNUQsd0NBQXdDO1lBQ3hDLGFBQWE7WUFDYixNQUFNLENBQUMsT0FBTyxDQUFDLE9BQU8sQ0FBQyxPQUFPLEVBQUUsc0JBQXNCLEVBQUU7Z0JBQ3BELElBQUksRUFBRSxjQUFjO2dCQUNwQixNQUFNLEVBQUUsbUJBQW1CO2dCQUMzQixJQUFJLEVBQUUsQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDO2FBQ3pCLENBQUMsQ0FBQztRQUNMLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxDQUFDLEdBQVEsRUFBRSxFQUFFO1lBQ2xCLE9BQU8sQ0FBQyxLQUFLLENBQUMsdUNBQXVDLEVBQUUsR0FBRyxDQUFDLENBQUM7UUFDaEUsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztBQUNMLENBQUM7QUFFRCxTQUFTLFFBQVEsQ0FBQyxTQUF3QjtJQUN4QyxPQUFPLENBQUMsR0FBRyxDQUFDLE9BQU8sRUFBRSxTQUFTLENBQUMsQ0FBQztJQUNoQyxpQkFBaUI7SUFDakIsa0JBQWtCLENBQUMscUNBQXFDLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxJQUFZLEVBQUUsRUFBRTtRQUM5RSxhQUFhO1FBQ2IsTUFBTSxDQUFDLE9BQU8sQ0FBQyxPQUFPLENBQUMsT0FBTyxFQUFFLFlBQVksRUFBRSxJQUFJLENBQUMsQ0FBQyxJQUFJLENBQUMsR0FBRyxFQUFFO1lBQzVELG1DQUFtQztZQUNuQyxhQUFhO1lBQ2IsTUFBTSxDQUFDLE9BQU8sQ0FBQyxPQUFPLENBQUMsT0FBTyxFQUFFLHNCQUFzQixFQUFFO2dCQUNwRCxJQUFJLEVBQUUsY0FBYztnQkFDcEIsTUFBTSxFQUFFLGNBQWM7Z0JBQ3RCLElBQUksRUFBRSxDQUFDLFNBQVMsQ0FBQyxJQUFJLENBQUM7YUFDekIsQ0FBQyxDQUFDO1FBQ0wsQ0FBQyxDQUFDLENBQUMsS0FBSyxDQUFDLENBQUMsR0FBUSxFQUFFLEVBQUU7WUFDbEIsT0FBTyxDQUFDLEtBQUssQ0FBQyxrQ0FBa0MsRUFBRSxHQUFHLENBQUMsQ0FBQztRQUMzRCxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0FBQ0wsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8vIC8vIEB0cy1pZ25vcmVcclxuLy8gaW1wb3J0IHBhY2thZ2VKU09OIGZyb20gJy4uL3BhY2thZ2UuanNvbic7XHJcbmltcG9ydCB7IE1lbnVBc3NldEluZm8sIElBc3NldEluZm8gfSBmcm9tICdAY29jb3MvY3JlYXRvci10eXBlcy9lZGl0b3IvcGFja2FnZXMvcGFja2FnZS1hc3NldC9AdHlwZXMvcHVibGljJztcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBvbkNyZWF0ZU1lbnUoYXNzZXRJbmZvOiBNZW51QXNzZXRJbmZvKSB7XHJcbiAgcmV0dXJuIFtcclxuICAgIHtcclxuICAgICAgbGFiZWw6ICfpo57mnLrmuLjmiI8nLFxyXG4gICAgICBzdWJtZW51OiBbXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgbGFiZWw6ICfliJvlu7rms6LmrKEnLFxyXG4gICAgICAgICAgY2xpY2soKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCd3YXZlJyk7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGFzc2V0SW5mbyk7XHJcbiAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgbGFiZWw6ICfliJvlu7rpmLXlnosnLFxyXG4gICAgICAgICAgY2xpY2soKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdmb3JtYXRpb24nKTtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coYXNzZXRJbmZvKTtcclxuICAgICAgICAgIH0sXHJcbiAgICAgICAgfSxcclxuICAgICAgXSxcclxuICAgIH0sXHJcbiAgXTtcclxufTtcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBvbkFzc2V0TWVudShhc3NldEluZm86IE1lbnVBc3NldEluZm8pIHtcclxuICBjb25zdCBzdWJtZW51ID0gW1xyXG4gICAge1xyXG4gICAgICBsYWJlbDogJ+WIm+W7uuWFs+WNoVByZWZhYicsXHJcbiAgICAgIC8vZW5hYmxlZDogYXNzZXRJbmZvLmlzRGlyZWN0b3J5LFxyXG4gICAgICBjbGljaygpIHtcclxuICAgICAgICBjcmVhdGVMZXZlbFByZWZhYihhc3NldEluZm8pO1xyXG4gICAgICB9LFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgbGFiZWw6ICfliJvlu7rlrZDlvLlwcmVmYWInLFxyXG4gICAgICAvL2VuYWJsZWQ6ICFhc3NldEluZm8uaXNEaXJlY3RvcnksXHJcbiAgICAgIGNsaWNrKCkge1xyXG4gICAgICAgIGNyZWF0ZUJ1bGxldFByZWZhYnMoYXNzZXRJbmZvKTtcclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgXTtcclxuXHJcbiAgLy8g5qOA5p+l5piv5ZCm5piv6Zi15Z6LSlNPTuaWh+S7tlxyXG4gIGlmICghYXNzZXRJbmZvLmlzRGlyZWN0b3J5ICYmXHJcbiAgICAgIGFzc2V0SW5mby51cmwuaW5jbHVkZXMoJ2Fzc2V0cy9yZXNvdXJjZXMvZ2FtZS9sZXZlbC93YXZlL2Zvcm1hdGlvbicpICYmXHJcbiAgICAgIGFzc2V0SW5mby51cmwuZW5kc1dpdGgoJy5qc29uJykpIHtcclxuICAgIHN1Ym1lbnUucHVzaCh7XHJcbiAgICAgIGxhYmVsOiAn57yW6L6R6Zi15Z6LJyxcclxuICAgICAgY2xpY2soKSB7XHJcbiAgICAgICAgZWRpdEZvcm1hdGlvbihhc3NldEluZm8pO1xyXG4gICAgICB9LFxyXG4gICAgfSk7XHJcbiAgfVxyXG5cclxuICAvLyDmo4Dmn6XmmK/lkKbmmK/ot6/lvoRKU09O5paH5Lu2XHJcbiAgaWYgKCFhc3NldEluZm8uaXNEaXJlY3RvcnkgJiZcclxuICAgICAgYXNzZXRJbmZvLnVybC5pbmNsdWRlcygnYXNzZXRzL3Jlc291cmNlcy9nYW1lL2xldmVsL3dhdmUvcGF0aCcpICYmXHJcbiAgICAgIGFzc2V0SW5mby51cmwuZW5kc1dpdGgoJy5qc29uJykpIHtcclxuICAgIHN1Ym1lbnUucHVzaCh7XHJcbiAgICAgIGxhYmVsOiAn57yW6L6R6Lev5b6EJyxcclxuICAgICAgY2xpY2soKSB7XHJcbiAgICAgICAgZWRpdFBhdGgoYXNzZXRJbmZvKTtcclxuICAgICAgfSxcclxuICAgIH0pO1xyXG4gIH1cclxuICBpZiAoIWFzc2V0SW5mby5pc0RpcmVjdG9yeSAmJlxyXG4gICAgICBhc3NldEluZm8udXJsLmluY2x1ZGVzKCdhc3NldHMvcmVzb3VyY2VzL2dhbWUvc3BpbmUvcGxhbmUvbWFpbnBsYW5lLycpICYmXHJcbiAgICAgIGFzc2V0SW5mby51cmwuZW5kc1dpdGgoJy5qc29uJykpIHtcclxuICAgIHN1Ym1lbnUucHVzaCh7XHJcbiAgICAgIGxhYmVsOiAn5Yib5bu66Ieq5py6cHJlZmFiJyxcclxuICAgICAgY2xpY2soKSB7XHJcbiAgICAgICAgY3JlYXRlTWFpblBsYW5lUHJlZmFiKGFzc2V0SW5mbyk7XHJcbiAgICAgIH0sXHJcbiAgICB9KTtcclxuICB9XHJcbiAgaWYgKCFhc3NldEluZm8uaXNEaXJlY3RvcnkgJiZcclxuICAgICAgYXNzZXRJbmZvLnVybC5pbmNsdWRlcygnYXNzZXRzL3Jlc291cmNlcy9nYW1lL3NwaW5lL3BsYW5lL2VuZW15cGxhbmUvJykgJiZcclxuICAgICAgYXNzZXRJbmZvLnVybC5lbmRzV2l0aCgnLmpzb24nKSkge1xyXG4gICAgc3VibWVudS5wdXNoKHtcclxuICAgICAgbGFiZWw6ICfliJvlu7rmlYzmnLpwcmVmYWInLFxyXG4gICAgICBjbGljaygpIHtcclxuICAgICAgICBjcmVhdGVFbmVteVBsYW5lUHJlZmFiKGFzc2V0SW5mbyk7XHJcbiAgICAgIH0sXHJcbiAgICB9KTtcclxuICB9XHJcblxyXG4gIHJldHVybiBbXHJcbiAgICB7XHJcbiAgICAgIGxhYmVsOiAn6aOe5py65ri45oiPJyxcclxuICAgICAgc3VibWVudTogc3VibWVudSxcclxuICAgIH0sXHJcbiAgXTtcclxufTtcclxuXHJcbmZ1bmN0aW9uIGdldEFzc2V0VXVpZEJ5UGF0aChwYXRoOiBzdHJpbmcpOiBQcm9taXNlPHN0cmluZz4ge1xyXG4gIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XHJcbiAgICAvLyBAdHMtaWdub3JlXHJcbiAgICBFZGl0b3IuTWVzc2FnZS5yZXF1ZXN0KCdhc3NldC1kYicsICdxdWVyeS11dWlkJywgcGF0aCkudGhlbigocmVzOiBhbnkpID0+IHtcclxuICAgICAgcmVzb2x2ZShyZXMpO1xyXG4gICAgfSkuY2F0Y2goKGVycjogYW55KSA9PiB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBxdWVyeSB1dWlkOicsIGVycik7XHJcbiAgICAgIHJlamVjdChlcnIpO1xyXG4gICAgfSk7XHJcbiAgfSk7XHJcbn1cclxuXHJcbmZ1bmN0aW9uIGdldEFzc2V0VXVpZHNCeVBhdGgocGF0aDogc3RyaW5nKTogUHJvbWlzZTxzdHJpbmdbXT4ge1xyXG4gICAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHtcclxuICAgICAgICAvLyBAdHMtaWdub3JlXHJcbiAgICAgICAgRWRpdG9yLk1lc3NhZ2UucmVxdWVzdCgnYXNzZXQtZGInLCAncXVlcnktYXNzZXRzJywgeyBwYXR0ZXJuOiBwYXRoICsgJy8qKicgfSkudGhlbigocmVzOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgY29uc3QgYXJyOiBhbnlbXSA9IEFycmF5LmlzQXJyYXkocmVzKSA/IHJlcyA6IChBcnJheS5pc0FycmF5KHJlcz8uWzBdKSA/IHJlc1swXSA6IFtdKTtcclxuICAgICAgICAgICAgY29uc3QgYXNzZXRzID0gYXJyXHJcbiAgICAgICAgICAgICAgICAuZmlsdGVyKChhOiBhbnkpID0+IGEgJiYgIWEuaXNEaXJlY3RvcnkpXHJcbiAgICAgICAgICAgICAgICAubWFwKChhOiBhbnkpID0+ICh7XHJcbiAgICAgICAgICAgICAgICAgICAgbmFtZTogU3RyaW5nKGEubmFtZSB8fCAnJyksXHJcbiAgICAgICAgICAgICAgICAgICAgcGF0aDogYS5wYXRoIHx8ICcnLFxyXG4gICAgICAgICAgICAgICAgICAgIHV1aWQ6IGEudXVpZCB8fCAnJ1xyXG4gICAgICAgICAgICAgICAgfSkpXHJcbiAgICAgICAgICAgICAgICAuZmlsdGVyKHAgPT4gcC5uYW1lKVxyXG4gICAgICAgICAgICAgICAgLnNvcnQoKGEsIGIpID0+IGEubmFtZS5sb2NhbGVDb21wYXJlKGIubmFtZSkpO1xyXG4gICAgICAgICAgICByZXNvbHZlKGFzc2V0cy5tYXAoYSA9PiBhLnV1aWQpKTsgLy8g5Y+q6ZyA6KaBdXVpZOWNs+WPr++8jOS4jemcgOimgeWFtuS7luS/oeaBr+S6huOAglxyXG4gICAgICAgIH0pLmNhdGNoKChlcnI6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gcXVlcnkgYXNzZXRzOicsIGVycik7XHJcbiAgICAgICAgICAgIHJlamVjdChlcnIpO1xyXG4gICAgICAgIH0pO1xyXG4gICAgfSk7XHJcbn1cclxuXHJcbmZ1bmN0aW9uIGNyZWF0ZUxldmVsUHJlZmFiKGFzc2V0SW5mbzogTWVudUFzc2V0SW5mbykge1xyXG4gIGNvbnNvbGUubG9nKGFzc2V0SW5mbyk7XHJcblxyXG4gIGlmIChhc3NldEluZm8uaXNEaXJlY3RvcnkpIHtcclxuICAgIGdldEFzc2V0VXVpZHNCeVBhdGgoYXNzZXRJbmZvLnVybCkudGhlbigodXVpZHM6IHN0cmluZ1tdKSA9PiB7XHJcbiAgICAgIHV1aWRzLmZvckVhY2goKHV1aWQ6IHN0cmluZykgPT4ge1xyXG4gICAgICAgIC8vIEB0cy1pZ25vcmVcclxuICAgICAgICBFZGl0b3IuTWVzc2FnZS5yZXF1ZXN0KCdzY2VuZScsICdleGVjdXRlLXNjZW5lLXNjcmlwdCcsIHtcclxuICAgICAgICAgIG5hbWU6ICdsZXZlbC1lZGl0b3InLFxyXG4gICAgICAgICAgbWV0aG9kOiAnY3JlYXRlTGV2ZWxQcmVmYWInLFxyXG4gICAgICAgICAgYXJnczogW3V1aWRdXHJcbiAgICAgICAgfSk7XHJcbiAgICAgIH0pO1xyXG4gICAgfSk7XHJcbiAgfSBlbHNlIHtcclxuICAgIC8vIEB0cy1pZ25vcmVcclxuICAgIEVkaXRvci5NZXNzYWdlLnJlcXVlc3QoJ3NjZW5lJywgJ2V4ZWN1dGUtc2NlbmUtc2NyaXB0Jywge1xyXG4gICAgICBuYW1lOiAnbGV2ZWwtZWRpdG9yJyxcclxuICAgICAgbWV0aG9kOiAnY3JlYXRlTGV2ZWxQcmVmYWInLFxyXG4gICAgICBhcmdzOiBbYXNzZXRJbmZvLnV1aWRdXHJcbiAgICB9KTtcclxuICB9XHJcbn1cclxuXHJcbmZ1bmN0aW9uIGNyZWF0ZUJ1bGxldFByZWZhYnMoYXNzZXRJbmZvOiBNZW51QXNzZXRJbmZvKSB7XHJcbiAgICBpZiAoYXNzZXRJbmZvLmlzRGlyZWN0b3J5KSB7XHJcbiAgICAgICAgZ2V0QXNzZXRVdWlkc0J5UGF0aChhc3NldEluZm8udXJsKS50aGVuKCh1dWlkczogc3RyaW5nW10pID0+IHtcclxuICAgICAgICAgICAgdXVpZHMuZm9yRWFjaCgodXVpZDogc3RyaW5nKSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgLy8gQHRzLWlnbm9yZVxyXG4gICAgICAgICAgICAgICAgRWRpdG9yLk1lc3NhZ2UucmVxdWVzdCgnc2NlbmUnLCAnZXhlY3V0ZS1zY2VuZS1zY3JpcHQnLCB7XHJcbiAgICAgICAgICAgICAgICAgICAgbmFtZTogJ2xldmVsLWVkaXRvcicsXHJcbiAgICAgICAgICAgICAgICAgICAgbWV0aG9kOiAnY3JlYXRlQnVsbGV0UHJlZmFiJyxcclxuICAgICAgICAgICAgICAgICAgICBhcmdzOiBbdXVpZF1cclxuICAgICAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICB9KTtcclxuICAgICAgICB9KTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgICAgIC8vIEB0cy1pZ25vcmVcclxuICAgICAgICBFZGl0b3IuTWVzc2FnZS5yZXF1ZXN0KCdzY2VuZScsICdleGVjdXRlLXNjZW5lLXNjcmlwdCcsIHtcclxuICAgICAgICAgICAgbmFtZTogJ2xldmVsLWVkaXRvcicsXHJcbiAgICAgICAgICAgIG1ldGhvZDogJ2NyZWF0ZUJ1bGxldFByZWZhYicsXHJcbiAgICAgICAgICAgIGFyZ3M6IFthc3NldEluZm8udXVpZF1cclxuICAgICAgICB9KTtcclxuICAgIH1cclxufVxyXG5cclxuZnVuY3Rpb24gY3JlYXRlTWFpblBsYW5lUHJlZmFiKGFzc2V0SW5mbzogTWVudUFzc2V0SW5mbykge1xyXG4gIGNvbnNvbGUubG9nKFwiY3JlYXRlIG1haW5wbGFuZSBmcm9tIHNwaW5lIGFzc2V0SW5mb1wiLCBhc3NldEluZm8pO1xyXG4gIFxyXG4gIC8vIEB0cy1pZ25vcmVcclxuICBFZGl0b3IuTWVzc2FnZS5yZXF1ZXN0KCdzY2VuZScsICdleGVjdXRlLXNjZW5lLXNjcmlwdCcsIHtcclxuICAgIG5hbWU6ICdsZXZlbC1lZGl0b3InLFxyXG4gICAgbWV0aG9kOiAnY3JlYXRlTWFpblBsYW5lUHJlZmFiJyxcclxuICAgIGFyZ3M6IFthc3NldEluZm8udXVpZF1cclxuICB9KTtcclxufVxyXG5mdW5jdGlvbiBjcmVhdGVFbmVteVBsYW5lUHJlZmFiKGFzc2V0SW5mbzogTWVudUFzc2V0SW5mbykge1xyXG4gIGNvbnNvbGUubG9nKFwiY3JlYXRlIGVuZW15cGxhbmUgZnJvbSBzcGluZSBhc3NldEluZm9cIiwgYXNzZXRJbmZvKTtcclxuICBcclxuICAvLyBAdHMtaWdub3JlXHJcbiAgRWRpdG9yLk1lc3NhZ2UucmVxdWVzdCgnc2NlbmUnLCAnZXhlY3V0ZS1zY2VuZS1zY3JpcHQnLCB7XHJcbiAgICBuYW1lOiAnbGV2ZWwtZWRpdG9yJyxcclxuICAgIG1ldGhvZDogJ2NyZWF0ZUVuZW15UGxhbmVQcmVmYWInLFxyXG4gICAgYXJnczogW2Fzc2V0SW5mby51dWlkXVxyXG4gIH0pO1xyXG59XHJcblxyXG5mdW5jdGlvbiBlZGl0Rm9ybWF0aW9uKGFzc2V0SW5mbzogTWVudUFzc2V0SW5mbykge1xyXG4gIGNvbnNvbGUubG9nKCfnvJbovpHpmLXlnos6JywgYXNzZXRJbmZvKTtcclxuICAvLyDmiZPlvIBGb3JtYXRpb25FZGl0b3LlnLrmma8gVXVpZCA9IGFhODQyZjNhLThhZWEtNDJjMi1hNDgwLTk2OGFhZGUzZGZlZlxyXG4gIGdldEFzc2V0VXVpZEJ5UGF0aCgnZGI6Ly9hc3NldHMvc2NlbmVzL0Zvcm1hdGlvbkVkaXRvci5zY2VuZScpLnRoZW4oKHV1aWQ6IHN0cmluZykgPT4ge1xyXG4gICAgLy8gQHRzLWlnbm9yZVxyXG4gICAgRWRpdG9yLk1lc3NhZ2UucmVxdWVzdCgnc2NlbmUnLCAnb3Blbi1zY2VuZScsIHV1aWQpLnRoZW4oKCkgPT4ge1xyXG4gICAgICAvLyDlnLrmma/miZPlvIDlkI7vvIzliqDovb3pgInkuK3nmoTpmLXlnotKU09O5paH5Lu25YiwRm9ybWF0aW9uRWRpdG9y57uE5Lu2XHJcbiAgICAgIC8vIEB0cy1pZ25vcmVcclxuICAgICAgRWRpdG9yLk1lc3NhZ2UucmVxdWVzdCgnc2NlbmUnLCAnZXhlY3V0ZS1zY2VuZS1zY3JpcHQnLCB7XHJcbiAgICAgICAgICBuYW1lOiAnbGV2ZWwtZWRpdG9yJyxcclxuICAgICAgICAgIG1ldGhvZDogJ2xvYWRGb3JtYXRpb25EYXRhJyxcclxuICAgICAgICAgIGFyZ3M6IFthc3NldEluZm8udXVpZF1cclxuICAgICAgfSk7XHJcbiAgICB9KS5jYXRjaCgoZXJyOiBhbnkpID0+IHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gb3BlbiBGb3JtYXRpb25FZGl0b3Igc2NlbmU6JywgZXJyKTtcclxuICAgIH0pO1xyXG4gIH0pO1xyXG59XHJcblxyXG5mdW5jdGlvbiBlZGl0UGF0aChhc3NldEluZm86IE1lbnVBc3NldEluZm8pIHtcclxuICBjb25zb2xlLmxvZygn57yW6L6R6Lev5b6EOicsIGFzc2V0SW5mbyk7XHJcbiAgLy8g5omT5byAUGF0aEVkaXRvcuWcuuaZr1xyXG4gIGdldEFzc2V0VXVpZEJ5UGF0aCgnZGI6Ly9hc3NldHMvc2NlbmVzL1BhdGhFZGl0b3Iuc2NlbmUnKS50aGVuKCh1dWlkOiBzdHJpbmcpID0+IHtcclxuICAgIC8vIEB0cy1pZ25vcmVcclxuICAgIEVkaXRvci5NZXNzYWdlLnJlcXVlc3QoJ3NjZW5lJywgJ29wZW4tc2NlbmUnLCB1dWlkKS50aGVuKCgpID0+IHtcclxuICAgICAgLy8g5Zy65pmv5omT5byA5ZCO77yM5Yqg6L296YCJ5Lit55qE6Lev5b6ESlNPTuaWh+S7tuWIsFBhdGhFZGl0b3Lnu4Tku7ZcclxuICAgICAgLy8gQHRzLWlnbm9yZVxyXG4gICAgICBFZGl0b3IuTWVzc2FnZS5yZXF1ZXN0KCdzY2VuZScsICdleGVjdXRlLXNjZW5lLXNjcmlwdCcsIHtcclxuICAgICAgICAgIG5hbWU6ICdsZXZlbC1lZGl0b3InLFxyXG4gICAgICAgICAgbWV0aG9kOiAnbG9hZFBhdGhEYXRhJyxcclxuICAgICAgICAgIGFyZ3M6IFthc3NldEluZm8udXVpZF1cclxuICAgICAgfSk7XHJcbiAgICB9KS5jYXRjaCgoZXJyOiBhbnkpID0+IHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gb3BlbiBQYXRoRWRpdG9yIHNjZW5lOicsIGVycik7XHJcbiAgICB9KTtcclxuICB9KTtcclxufVxyXG4iXX0=
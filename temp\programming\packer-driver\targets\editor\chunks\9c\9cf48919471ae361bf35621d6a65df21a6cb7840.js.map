{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelBackgroundLayerUI.ts"], "names": ["_decorator", "Prefab", "instantiate", "UITransform", "LevelUtils", "MyApp", "GameConst", "GameIns", "LevelNodeCheckOutScreen", "<PERSON><PERSON><PERSON><PERSON>", "ccclass", "property", "BackgroundsNodeName", "BACKGROUND_POOL_NAME", "LevelBackgroundLayerUI", "_backgrounds", "_offSetY", "_backgroundsNode", "onLoad", "initByLevelData", "data", "offSetY", "bFirstLoad", "node", "setPosition", "getOrAddNode", "_initBackgrounds", "i", "backgrounds", "length", "path", "resMgr", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultBundleName", "prefab", "loadAsync", "push", "halfHeight", "VIEWPORT_LOAD_POS", "_addBackground", "nodeHeight", "getComponent", "height", "tick", "deltaTime", "posY", "position", "y", "speed", "_checkBackgrounds", "<PERSON><PERSON><PERSON><PERSON>", "children", "lastChildTransform", "lastBackgroundHeight", "lastChildTop", "newY", "newNode", "yPos", "index", "prefabName", "name", "gameMapManager", "mapObjectPoolManager", "get", "checkOut", "addComponent", "init", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAuBC,MAAAA,M,OAAAA,M;AAAcC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,W,OAAAA,W;;AAElDC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,uB,iBAAAA,uB;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;AAExBY,MAAAA,mB,GAAsB,a;AACtBC,MAAAA,oB,GAAuB,iB;;wCAGhBC,sB,WADZJ,OAAO,CAAC,wBAAD,C,gBAAR,MACaI,sBADb;AAAA;AAAA,oCACuD;AAAA;AAAA;AAAA,eAC3CC,YAD2C,GAClB,EADkB;AAAA,eAE3CC,QAF2C,GAExB,CAFwB;AAErB;AAFqB,eAI3CC,gBAJ2C,GAIX,IAJW;AAAA;;AAMzCC,QAAAA,MAAM,GAAS,CAExB;;AAE2B,cAAfC,eAAe,CAACC,IAAD,EAAiCC,OAAjC,EAAkDC,UAAlD,EAAsF;AAC9G,eAAKN,QAAL,GAAgBK,OAAhB;AACA,eAAKE,IAAL,CAAUC,WAAV,CAAsB,CAAtB,EAAyBH,OAAzB,EAAkC,CAAlC;AAEA,eAAKJ,gBAAL,GAAwB;AAAA;AAAA,wCAAWQ,YAAX,CAAwB,KAAKF,IAA7B,EAAmCX,mBAAnC,CAAxB;AACA,eAAKG,YAAL,GAAoB,EAApB;AAEA,gBAAM,KAAKW,gBAAL,CAAsBN,IAAtB,EAA4BE,UAA5B,CAAN;AACH;;AAE6B,cAAhBI,gBAAgB,CAACN,IAAD,EAAiCE,UAAjC,EAAqE;AAC/F,eAAK,IAAIK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGP,IAAI,CAACQ,WAAL,CAAiBC,MAArC,EAA6CF,CAAC,EAA9C,EAAkD;AAC9C,kBAAMG,IAAI,GAAG;AAAA;AAAA,gCAAMC,MAAN,CAAaC,YAAb,CAA0B;AAAA;AAAA,gCAAMD,MAAN,CAAaE,iBAAvC,EAA0Db,IAAI,CAACQ,WAAL,CAAiBD,CAAjB,CAA1D,CAAb;AACA,kBAAMO,MAAM,GAAG,MAAM;AAAA;AAAA,gCAAMH,MAAN,CAAaI,SAAb,CAAuBL,IAAvB,EAA6B7B,MAA7B,CAArB;;AACA,iBAAKc,YAAL,CAAkBqB,IAAlB,CAAuBF,MAAvB;AACH;;AAED,cAAIZ,UAAJ,EAAgB;AACZ,gBAAID,OAAO,GAAG,CAAd;AACA,gBAAIgB,UAAU,GAAG,CAAjB;;AAEA,mBAAO,KAAKtB,YAAL,CAAkBc,MAAlB,GAA2B,CAA3B,IAAiCR,OAAO,GAAGgB,UAAX,GAAyB;AAAA;AAAA,wCAAUC,iBAA1E,EAA6F;AACzF,oBAAMf,IAAI,GAAG,KAAKgB,cAAL,CAAoBlB,OAApB,CAAb;;AACA,oBAAMmB,UAAU,GAAGjB,IAAI,CAACkB,YAAL,CAAkBtC,WAAlB,EAAgCuC,MAAnD;AACArB,cAAAA,OAAO,IAAImB,UAAX;AACAH,cAAAA,UAAU,GAAGG,UAAU,GAAG,CAA1B;AACH;AACJ;AACJ;;AAEMG,QAAAA,IAAI,CAACC,SAAD,EAA0B;AACjC,cAAIC,IAAI,GAAG,KAAKtB,IAAL,CAAUuB,QAAV,CAAmBC,CAA9B;AACAF,UAAAA,IAAI,IAAID,SAAS,GAAG,KAAKI,KAAzB;AACA,eAAKzB,IAAL,CAAUC,WAAV,CAAsB,CAAtB,EAAyBqB,IAAzB,EAA+B,CAA/B;;AAEA,eAAKI,iBAAL;AACH;;AAEOA,QAAAA,iBAAiB,GAAS;AAC9B,cAAI,CAAC,KAAKhC,gBAAN,IAA0B,KAAKF,YAAL,CAAkBc,MAAlB,KAA6B,CAA3D,EAA8D;AAC1D;AACH,WAH6B,CAK9B;;;AACA,gBAAMqB,SAAS,GAAG,KAAKjC,gBAAL,CAAsBkC,QAAtB,CAA+B,KAAKlC,gBAAL,CAAsBkC,QAAtB,CAA+BtB,MAA/B,GAAwC,CAAvE,CAAlB;AACA,gBAAMuB,kBAAkB,GAAGF,SAAS,CAACT,YAAV,CAAuBtC,WAAvB,CAA3B;;AAEA,cAAI,CAACiD,kBAAL,EAAyB;AACrB;AACH,WAX6B,CAa9B;;;AACA,gBAAMC,oBAAoB,GAAGD,kBAAkB,CAACV,MAAhD;AACA,gBAAMY,YAAY,GAAG,KAAK/B,IAAL,CAAUuB,QAAV,CAAmBC,CAAnB,GAAuBG,SAAS,CAACJ,QAAV,CAAmBC,CAA1C,GAA8CM,oBAAoB,GAAG,CAA1F,CAf8B,CAiB9B;;AACA,cAAIC,YAAY,GAAG;AAAA;AAAA,sCAAUhB,iBAA7B,EAAgD;AAC5C,kBAAMiB,IAAI,GAAGL,SAAS,CAACJ,QAAV,CAAmBC,CAAnB,GAAuBM,oBAApC;;AACA,kBAAMG,OAAO,GAAG,KAAKjB,cAAL,CAAoBgB,IAApB,CAAhB;AACH;AACJ;;AAEOhB,QAAAA,cAAc,CAACkB,IAAD,EAAqB;AACvC,gBAAMC,KAAK,GAAG,KAAKzC,gBAAL,CAAuBkC,QAAvB,CAAgCtB,MAAhC,GAAyC,KAAKd,YAAL,CAAkBc,MAAzE;AACA,gBAAMK,MAAM,GAAG,KAAKnB,YAAL,CAAkB2C,KAAlB,CAAf;AACA,gBAAMC,UAAU,GAAGzB,MAAM,CAAC0B,IAA1B;AACA,cAAIrC,IAAI,GAAG;AAAA;AAAA,kCAAQsC,cAAR,CAAuBC,oBAAvB,CAA4CC,GAA5C,CAAgDlD,oBAAhD,EAAsE8C,UAAtE,CAAX;;AACA,cAAI,CAACpC,IAAL,EAAW;AACPA,YAAAA,IAAI,GAAGrB,WAAW,CAAC,KAAKa,YAAL,CAAkB2C,KAAlB,CAAD,CAAlB;AACA,kBAAMM,QAAQ,GAAGzC,IAAI,CAAC0C,YAAL;AAAA;AAAA,mEAAjB;AACAD,YAAAA,QAAQ,CAACE,IAAT,CAAcrD,oBAAd;AACH;;AAED,eAAKI,gBAAL,CAAuBkD,QAAvB,CAAgC5C,IAAhC;;AACAA,UAAAA,IAAI,CAACC,WAAL,CAAiB,CAAjB,EAAoBiC,IAApB,EAA0B,CAA1B;AACA,iBAAOlC,IAAP;AACH;;AAtFkD,O", "sourcesContent": ["import { _decorator, Component, Prefab, Node, instantiate, UITransform } from 'cc';\r\nimport { LevelDataBackgroundLayer } from 'db://assets/bundles/common/script/leveldata/leveldata';\r\nimport { LevelUtils } from './LevelUtils';\r\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\r\nimport { GameConst } from 'db://assets/scripts/core/base/GameConst';\r\nimport { GameIns } from '../../GameIns';\r\nimport { LevelNodeCheckOutScreen } from './LevelNodeCheckOutScreen';\r\nimport { LevelLayer } from './LevelLayer';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\nconst BackgroundsNodeName = \"backgrounds\";\r\nconst BACKGROUND_POOL_NAME = \"background_pool\";\r\n\r\n@ccclass('LevelBackgroundLayerUI')\r\nexport class LevelBackgroundLayerUI extends LevelLayer {\r\n    private _backgrounds: Prefab[] = [];\r\n    private _offSetY: number = 0; // 当前关卡的偏移量\r\n\r\n    private _backgroundsNode: Node | null = null;\r\n\r\n    protected onLoad(): void {\r\n        \r\n    }\r\n\r\n    public async initByLevelData(data: LevelDataBackgroundLayer, offSetY: number, bFirstLoad: boolean): Promise<void> {\r\n        this._offSetY = offSetY;\r\n        this.node.setPosition(0, offSetY, 0);\r\n\r\n        this._backgroundsNode = LevelUtils.getOrAddNode(this.node, BackgroundsNodeName);\r\n        this._backgrounds = [];\r\n\r\n        await this._initBackgrounds(data, bFirstLoad);\r\n    }\r\n\r\n    private async _initBackgrounds(data: LevelDataBackgroundLayer, bFirstLoad: boolean): Promise<void> {\r\n        for (let i = 0; i < data.backgrounds.length; i++) {\r\n            const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, data.backgrounds[i]); \r\n            const prefab = await MyApp.resMgr.loadAsync(path, Prefab);\r\n            this._backgrounds.push(prefab);\r\n        }\r\n\r\n        if (bFirstLoad) {\r\n            let offSetY = 0;\r\n            let halfHeight = 0;\r\n        \r\n            while (this._backgrounds.length > 0 && (offSetY - halfHeight) < GameConst.VIEWPORT_LOAD_POS) {\r\n                const node = this._addBackground(offSetY);\r\n                const nodeHeight = node.getComponent(UITransform)!.height;\r\n                offSetY += nodeHeight;\r\n                halfHeight = nodeHeight / 2;\r\n            }\r\n        }\r\n    }\r\n\r\n    public tick(deltaTime: number): void {\r\n        let posY = this.node.position.y;\r\n        posY -= deltaTime * this.speed;\r\n        this.node.setPosition(0, posY, 0);\r\n\r\n        this._checkBackgrounds();\r\n    }\r\n\r\n    private _checkBackgrounds(): void {\r\n        if (!this._backgroundsNode || this._backgrounds.length === 0) {\r\n            return;\r\n        }\r\n        \r\n        // 获取最后一个背景节点\r\n        const lastChild = this._backgroundsNode.children[this._backgroundsNode.children.length - 1];\r\n        const lastChildTransform = lastChild.getComponent(UITransform);\r\n        \r\n        if (!lastChildTransform) {\r\n            return;\r\n        }\r\n        \r\n        // 计算最后一个背景节点的顶部位置（世界坐标） \r\n        const lastBackgroundHeight = lastChildTransform.height;\r\n        const lastChildTop = this.node.position.y + lastChild.position.y + lastBackgroundHeight / 2;\r\n        \r\n        // 如果最后一个背景的顶部位置小于加载阈值，添加新背景\r\n        if (lastChildTop < GameConst.VIEWPORT_LOAD_POS) {\r\n            const newY = lastChild.position.y + lastBackgroundHeight;\r\n            const newNode = this._addBackground(newY);\r\n        }\r\n    }\r\n    \r\n    private _addBackground(yPos: number): Node {\r\n        const index = this._backgroundsNode!.children.length % this._backgrounds.length;\r\n        const prefab = this._backgrounds[index];\r\n        const prefabName = prefab.name;\r\n        let node = GameIns.gameMapManager.mapObjectPoolManager.get(BACKGROUND_POOL_NAME, prefabName);\r\n        if (!node) {\r\n            node = instantiate(this._backgrounds[index]);\r\n            const checkOut = node.addComponent(LevelNodeCheckOutScreen);\r\n            checkOut.init(BACKGROUND_POOL_NAME);\r\n        }\r\n\r\n        this._backgroundsNode!.addChild(node);\r\n        node.setPosition(0, yPos, 0);\r\n        return node;\r\n    }\r\n}\r\n\r\n\r\n"]}
{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/dyncTerrain/EmittierTerrain.ts"], "names": ["EmittierElem", "_decorator", "assetManager", "CCBoolean", "CCFloat", "CCInteger", "Component", "Enum", "instantiate", "Prefab", "Vec3", "EDITOR", "LayerEmittierStrategy", "LayerEmittierType", "LayerRandomRange", "GameIns", "logDebug", "LevelNodeCheckOutScreen", "ccclass", "property", "executeInEditMode", "menu", "EMIITER_POOL_NAME", "LayerEmittierTypeZh", "Infinite", "Duration", "Count", "Event", "EmittierStatus", "SerializableRandomRange", "type", "displayName", "toLayerRandomRange", "min", "max", "fromLayerRandomRange", "range", "_velocity", "init", "speed", "angle", "rad", "Math", "PI", "x", "cos", "y", "sin", "tick", "dt", "pos", "node", "position", "clone", "add", "multiplyScalar", "setPosition", "EmittierTerrain", "visible", "tooltip", "_status", "inactive", "_activeElements", "_curDelay", "_curValue", "_curEmiIndex", "_deltaTime", "_lastEmitTime", "_emitCount", "_nextInterval", "_initialDelayPassed", "_poolName", "bfollow", "value", "_bfollow", "typeZh", "status", "follow", "onLoad", "_resetData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadAny", "uuid", "emittier", "err", "prefab", "emitterNode", "<PERSON><PERSON><PERSON><PERSON>", "poolName", "startEmittier", "active", "initDelay", "delayModify", "random", "valueModify", "battleManager", "dtMs", "_updateEmitter", "_updateActiveElements", "delaySeconds", "_<PERSON><PERSON><PERSON><PERSON>", "i", "length", "elem", "<PERSON><PERSON><PERSON><PERSON>", "splice", "emittierElements", "currentTime", "strategy", "Random", "floor", "Sequence", "elemPrefab", "elemNode", "gameMapManager", "mapObjectPoolManager", "get", "name", "cheekOut", "addComponent", "offsetX", "offSetX", "angleModify", "speedModify", "elemComponent", "getComponent", "push", "interval", "intervalModify", "_checkEmittierEnd", "end", "_destroyEmitter", "_destroyAllElements", "destroy", "_recycleNode", "play", "bPlay", "onDestroy", "put"], "mappings": ";;;oSA2CaA,Y;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA3CJC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,Y,OAAAA,Y;AAAsBC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;AAAqBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,M,OAAAA,M;AAAqBC,MAAAA,I,OAAAA,I;;AAC9HC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,qB,iBAAAA,qB;AAAuBC,MAAAA,iB,iBAAAA,iB;AAAmBC,MAAAA,gB,iBAAAA,gB;;AAC1CC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,uB,iBAAAA,uB;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA,iBAArB;AAAwCC,QAAAA;AAAxC,O,GAAgDpB,U;AAEhDqB,MAAAA,iB,GAAoB,e;;AAErBC,MAAAA,mB,aAAAA,mB;AAAAA,QAAAA,mB,CAAAA,mB,mBACI;AAAA;AAAA,oDAAkBC,Q;AADtBD,QAAAA,mB,CAAAA,mB,+BAEM;AAAA;AAAA,oDAAkBE,Q;AAFxBF,QAAAA,mB,CAAAA,mB,+BAGM;AAAA;AAAA,oDAAkBG,K;AAHxBH,QAAAA,mB,CAAAA,mB,+BAIM;AAAA;AAAA,oDAAkBI,K;eAJxBJ,mB;QAAAA,mB;;gCAOOK,c,0BAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;eAAAA,c;;;yCAQCC,uB,WADZX,OAAO,CAAC,yBAAD,C,UAEHC,QAAQ,CAAC;AAACW,QAAAA,IAAI,EAAE1B,OAAP;AAAgB2B,QAAAA,WAAW,EAAE;AAA7B,OAAD,C,UAGRZ,QAAQ,CAAC;AAACW,QAAAA,IAAI,EAAE1B,OAAP;AAAgB2B,QAAAA,WAAW,EAAE;AAA7B,OAAD,C,2BALb,MACaF,uBADb,CACqC;AAAA;AAAA;;AAAA;AAAA;;AAOjC;AACAG,QAAAA,kBAAkB,GAAqB;AACnC,iBAAO;AAAA;AAAA,oDAAqB,KAAKC,GAA1B,EAA+B,KAAKC,GAApC,CAAP;AACH;;AAEDC,QAAAA,oBAAoB,CAACC,KAAD,EAAgC;AAChD,eAAKH,GAAL,GAAWG,KAAK,CAACH,GAAjB;AACA,eAAKC,GAAL,GAAWE,KAAK,CAACF,GAAjB;AACH;;AAfgC,O;;;;;iBAEZ,C;;;;;;;iBAGA,C;;;;8BAaZlC,Y,GAAN,MAAMA,YAAN,SAA2BM,SAA3B,CAAqC;AAAA;AAAA;AAAA,eAChC+B,SADgC,GACd,IAAI3B,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,CADc;AAAA;;AAGxC;AACJ;AACA;AACA;AACA;AACW4B,QAAAA,IAAI,CAACC,KAAD,EAAgBC,KAAhB,EAAqC;AAC5C;AACA,gBAAMC,GAAG,GAAGD,KAAK,GAAGE,IAAI,CAACC,EAAb,GAAkB,GAA9B,CAF4C,CAI5C;;AACA,eAAKN,SAAL,CAAeO,CAAf,GAAmBF,IAAI,CAACG,GAAL,CAASJ,GAAT,IAAgBF,KAAnC;AACA,eAAKF,SAAL,CAAeS,CAAf,GAAmBJ,IAAI,CAACK,GAAL,CAASN,GAAT,IAAgBF,KAAnC;AACH;;AAEDS,QAAAA,IAAI,CAACC,EAAD,EAAmB;AACnB;AACA,gBAAMC,GAAG,GAAG,KAAKC,IAAL,CAAUC,QAAV,CAAmBC,KAAnB,EAAZ;AACAH,UAAAA,GAAG,CAACI,GAAJ,CAAQ,KAAKjB,SAAL,CAAegB,KAAf,GAAuBE,cAAvB,CAAsCN,EAAtC,CAAR;AACA,eAAKE,IAAL,CAAUK,WAAV,CAAsBN,GAAtB;AACH;;AAtBuC,O;;iCA4B/BO,e,YAHZvC,OAAO,CAAC,iBAAD,C,UACPE,iBAAiB,E,UACjBC,IAAI,CAAC,YAAD,C,UAGAF,QAAQ,CAAC;AAACuC,QAAAA,OAAO,EAAE;AAAV,OAAD,C,UAERvC,QAAQ,CAAC;AAACW,QAAAA,IAAI,EAAE3B,SAAP;AAAkB4B,QAAAA,WAAW,EAAE;AAA/B,OAAD,C,UAGRZ,QAAQ,CAAC;AAACW,QAAAA,IAAI,EAAErB,MAAP;AAAesB,QAAAA,WAAW,EAAE;AAA5B,OAAD,C,WAERZ,QAAQ,CAAC;AAACW,QAAAA,IAAI,EAAEvB,IAAI;AAAA;AAAA,2DAAX;AAAoCwB,QAAAA,WAAW,EAAE;AAAjD,OAAD,C,WAERZ,QAAQ,CAAC;AAACW,QAAAA,IAAI,EAAE,CAACrB,MAAD,CAAP;AAAiBsB,QAAAA,WAAW,EAAE;AAA9B,OAAD,C,WAERZ,QAAQ,CAAC;AAACW,QAAAA,IAAI,EAAEvB,IAAI,CAACgB,mBAAD,CAAX;AAAkCQ,QAAAA,WAAW,EAAC;AAA9C,OAAD,C,WAGRZ,QAAQ,CAAC;AAACuC,QAAAA,OAAO,EAAE;AAAV,OAAD,C,WAERvC,QAAQ,CAAC;AAACW,QAAAA,IAAI,EAAE1B,OAAP;AAAgB2B,QAAAA,WAAW,EAAE,KAA7B;AAAoC4B,QAAAA,OAAO,EAAE;AAA7C,OAAD,C,WAERxC,QAAQ,CAAC;AAACW,QAAAA,IAAI,EAAED,uBAAP;AAAgCE,QAAAA,WAAW,EAAE;AAA7C,OAAD,C,WAERZ,QAAQ,CAAC;AAACW,QAAAA,IAAI,EAAEzB,SAAP;AAAkB0B,QAAAA,WAAW,EAAE,UAA/B;AAA2CE,QAAAA,GAAG,EAAE;AAAhD,OAAD,C,WAERd,QAAQ,CAAC;AAACW,QAAAA,IAAI,EAAED,uBAAP;AAAgCE,QAAAA,WAAW,EAAE;AAA7C,OAAD,C,WAERZ,QAAQ,CAAC;AAACW,QAAAA,IAAI,EAAEzB,SAAP;AAAkB0B,QAAAA,WAAW,EAAE;AAA/B,OAAD,C,WAERZ,QAAQ,CAAC;AAACW,QAAAA,IAAI,EAAED,uBAAP;AAAgCE,QAAAA,WAAW,EAAE;AAA7C,OAAD,C,WAERZ,QAAQ,CAAC;AAACW,QAAAA,IAAI,EAAEzB,SAAP;AAAkB0B,QAAAA,WAAW,EAAE,aAA/B;AAA6CE,QAAAA,GAAG,EAAE,CAAlD;AAAqDC,QAAAA,GAAG,EAAE;AAA1D,OAAD,C,WAERf,QAAQ,CAAC;AAACW,QAAAA,IAAI,EAAED,uBAAP;AAAgCE,QAAAA,WAAW,EAAE;AAA7C,OAAD,C,WAERZ,QAAQ,CAAC;AAACW,QAAAA,IAAI,EAAE1B,OAAP;AAAgB2B,QAAAA,WAAW,EAAE;AAA7B,OAAD,C,WAERZ,QAAQ,CAAC;AAACW,QAAAA,IAAI,EAAED,uBAAP;AAAgCE,QAAAA,WAAW,EAAE;AAA7C,OAAD,C,WAERZ,QAAQ,CAAC;AAACW,QAAAA,IAAI,EAAED,uBAAP;AAAgCE,QAAAA,WAAW,EAAC;AAA5C,OAAD,C,6DAzCb,MAGa0B,eAHb,SAGqCnD,SAHrC,CAG+C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAmBjB;AAnBiB;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAyCnCsD,OAzCmC,GAyCThC,cAAc,CAACiC,QAzCN;AAAA,eA0CnCC,eA1CmC,GA0CD,EA1CC;AAAA,eA4CnCC,SA5CmC,GA4Cf,CA5Ce;AA4CZ;AA5CY,eA6CnCC,SA7CmC,GA6Cf,CA7Ce;AA6CZ;AA7CY,eA8CnCC,YA9CmC,GA8CZ,CA9CY;AA8CT;AA9CS,eAgDnCC,UAhDmC,GAgDtB,CAhDsB;AAgDnB;AAhDmB,eAiDnCC,aAjDmC,GAiDnB,CAjDmB;AAiDhB;AAjDgB,eAkDnCC,UAlDmC,GAkDd,CAlDc;AAkDX;AAlDW,eAmDnCC,aAnDmC,GAmDX,CAnDW;AAmDR;AAnDQ,eAoDnCC,mBApDmC,GAoDJ,KApDI;AAoDG;AApDH,eAsDnCC,SAtDmC,GAsDf,EAtDe;AAAA;;AAKzB,YAAPC,OAAO,CAACC,KAAD,EAAiB;AAAE,eAAKC,QAAL,GAAgBD,KAAhB;AAAwB;;AAC3C,YAAPD,OAAO,GAAY;AAAE,iBAAO,KAAKE,QAAZ;AAAuB;;AAQtC,YAANC,MAAM,GAAwB;AAAE,iBAAO,KAAK7C,IAAZ;AAAoD;;AAC9E,YAAN6C,MAAM,CAACF,KAAD,EAA6B;AAAE,eAAK3C,IAAL,GAAY2C,KAAZ;AAAmD;;AAyClF,YAANG,MAAM,GAAmB;AAChC,iBAAO,KAAKhB,OAAZ;AACH;;AAEgB,YAANiB,MAAM,GAAY;AACzB,iBAAO,KAAKH,QAAZ;AACH;;AAESI,QAAAA,MAAM,GAAS;AACrB,eAAKC,UAAL;;AAEA,cAAIpE,MAAJ,EAAY;AACR,iBAAKwC,IAAL,CAAU6B,iBAAV;AACA9E,YAAAA,YAAY,CAAC+E,OAAb,CAAqB;AAAEC,cAAAA,IAAI,EAAE,KAAKC,QAAL,CAAeD;AAAvB,aAArB,EAAoD,CAACE,GAAD,EAAMC,MAAN,KAAyB;AACzE,kBAAID,GAAJ,EAAS;AACL;AACH,eAFD,MAEO;AAEH,sBAAME,WAAW,GAAG9E,WAAW,CAAC6E,MAAD,CAA/B;AACA,qBAAKlC,IAAL,CAAUoC,QAAV,CAAmBD,WAAnB;AACH;AACJ,aARD;AASH;AACJ;;AAEDhD,QAAAA,IAAI,CAACkD,QAAD,EAAmB;AACnB,eAAKjB,SAAL,GAAiBiB,QAAjB;AACA,eAAK5B,OAAL,GAAehC,cAAc,CAACiC,QAA9B;;AACA,eAAKkB,UAAL;AACH;;AAEMU,QAAAA,aAAa,GAAS;AACzB,eAAK7B,OAAL,GAAehC,cAAc,CAAC8D,MAA9B;;AACA,cAAI/E,MAAJ,EAAY;AACR,iBAAKoD,SAAL,GAAiB,KAAK4B,SAAL,GAAiB,KAAKC,WAAL,CAAkB3D,GAAnC,GAAyCS,IAAI,CAACmD,MAAL,MAAiB,KAAKD,WAAL,CAAkB1D,GAAlB,GAAwB,KAAK0D,WAAL,CAAkB3D,GAA3D,CAA1D;AACA,iBAAK+B,SAAL,GAAiB,KAAKS,KAAL,GAAa/B,IAAI,CAACmD,MAAL,MAAiB,KAAKC,WAAL,CAAkB5D,GAAlB,GAAwB,KAAK4D,WAAL,CAAkB7D,GAA3D,CAA9B;AACH,WAHD,MAGO;AACH,iBAAK8B,SAAL,GAAiB,KAAK4B,SAAL,GAAiB,KAAKC,WAAL,CAAkB3D,GAAnC,GAAyC;AAAA;AAAA,oCAAQ8D,aAAR,CAAsBF,MAAtB,MAAkC,KAAKD,WAAL,CAAkB1D,GAAlB,GAAwB,KAAK0D,WAAL,CAAkB3D,GAA5E,CAA1D;AACA,iBAAK+B,SAAL,GAAiB,KAAKS,KAAL,GAAa,KAAKqB,WAAL,CAAkB7D,GAA/B,GAAqC;AAAA;AAAA,oCAAQ8D,aAAR,CAAsBF,MAAtB,MAAkC,KAAKC,WAAL,CAAkB5D,GAAlB,GAAwB,KAAK4D,WAAL,CAAkB7D,GAA5E,CAAtD;AACH;AACJ;;AAEMe,QAAAA,IAAI,CAACC,EAAD,EAAmB;AAC1B,cAAI,KAAKW,OAAL,KAAiBhC,cAAc,CAAC8D,MAApC,EAA4C;AAE5C,gBAAMM,IAAI,GAAG/C,EAAE,GAAG,IAAlB,CAH0B,CAI1B;;AACA,eAAKgD,cAAL,CAAoBD,IAApB;;AACA,eAAKE,qBAAL,CAA2BjD,EAA3B;AACH;AAED;AACJ;AACA;AACA;;;AACYgD,QAAAA,cAAc,CAAChD,EAAD,EAAmB;AACrC,eAAKiB,UAAL,IAAmBjB,EAAnB;;AAEA,cAAI,CAAC,KAAKqB,mBAAV,EAA+B;AAC3B,kBAAM6B,YAAY,GAAG,KAAKpC,SAA1B;;AACA,gBAAI,KAAKG,UAAL,IAAmBiC,YAAvB,EAAqC;AACjC,mBAAK7B,mBAAL,GAA2B,IAA3B;AACA,mBAAKJ,UAAL,GAAkB,CAAlB,CAFiC,CAEZ;AACxB;;AACD;AACH,WAVoC,CAYrC;;;AACA,eAAKkC,YAAL;AACH;AAED;AACJ;AACA;AACA;;;AACYF,QAAAA,qBAAqB,CAACjD,EAAD,EAAmB;AAC5C;AACA,eAAK,IAAIoD,CAAC,GAAG,KAAKvC,eAAL,CAAqBwC,MAArB,GAA8B,CAA3C,EAA8CD,CAAC,IAAI,CAAnD,EAAsDA,CAAC,EAAvD,EAA2D;AACvD,kBAAME,IAAI,GAAG,KAAKzC,eAAL,CAAqBuC,CAArB,CAAb,CADuD,CAGvD;;AACA,gBAAI,CAACE,IAAI,CAACC,OAAV,EAAmB;AACf;AACA,mBAAK1C,eAAL,CAAqB2C,MAArB,CAA4BJ,CAA5B,EAA+B,CAA/B;;AACA;AACH,aARsD,CAUvD;;;AACAE,YAAAA,IAAI,CAACvD,IAAL,CAAUC,EAAV;AACH;AACJ;;AAEOmD,QAAAA,YAAY,GACpB;AACI,cAAI,CAAC,KAAKjB,QAAN,IAAkB,KAAKuB,gBAAL,CAAsBJ,MAAtB,KAAiC,CAAvD,EAA0D;AAE1D,gBAAMK,WAAW,GAAG,KAAKzC,UAAzB,CAHJ,CAKI;;AACA,cAAIyC,WAAW,GAAG,KAAKxC,aAAnB,GAAmC,KAAKE,aAA5C,EAA2D,OAN/D,CAQI;;AACA,cAAI,KAAKuC,QAAL,KAAkB;AAAA;AAAA,8DAAsBC,MAA5C,EAAoD;AAChD,gBAAIlG,MAAJ,EAAY;AACR,mBAAKsD,YAAL,GAAoBvB,IAAI,CAACoE,KAAL,CAAWpE,IAAI,CAACmD,MAAL,KAAgB,KAAKa,gBAAL,CAAsBJ,MAAjD,CAApB;AACH,aAFD,MAEO;AACH,mBAAKrC,YAAL,GAAoBvB,IAAI,CAACoE,KAAL,CAAW;AAAA;AAAA,sCAAQf,aAAR,CAAsBF,MAAtB,KAAiC,KAAKa,gBAAL,CAAsBJ,MAAlE,CAApB;AACH;AACJ,WAND,MAMO,IAAI,KAAKM,QAAL,KAAkB;AAAA;AAAA,8DAAsBG,QAA5C,EAAsD;AACzD,iBAAK9C,YAAL,GAAoB,KAAKG,UAAL,GAAkB,KAAKsC,gBAAL,CAAsBJ,MAA5D;AACH;;AACD,gBAAMU,UAAU,GAAG,KAAKN,gBAAL,CAAsB,KAAKzC,YAA3B,CAAnB;AAEA,cAAIgD,QAAQ,GAAG;AAAA;AAAA,kCAAQC,cAAR,CAAuBC,oBAAvB,CAA4CC,GAA5C,CACX9F,iBADW,EAEX0F,UAAU,CAACK,IAFA,CAAf;;AAKA,cAAIJ,QAAJ,EAAc;AACV;AAAA;AAAA,sCAAS,iBAAT,EAA4B,cAAaD,UAAU,CAACK,IAAK,EAAzD;AACH,WAFD,MAEO;AACHJ,YAAAA,QAAQ,GAAGzG,WAAW,CAACwG,UAAD,CAAtB;AACA,kBAAMM,QAAQ,GAAGL,QAAQ,CAACM,YAAT;AAAA;AAAA,mEAAjB;AACAD,YAAAA,QAAQ,CAAChF,IAAT,CAAchB,iBAAd;AACH,WA/BL,CAiCI;;;AACA,cAAIkG,OAAO,GAAG,CAAd;;AACA,cAAI7G,MAAJ,EAAY;AACR6G,YAAAA,OAAO,GAAG,KAAKC,OAAL,CAAcxF,GAAd,GAAoBS,IAAI,CAACmD,MAAL,MAAiB,KAAK4B,OAAL,CAAcvF,GAAd,GAAoB,KAAKuF,OAAL,CAAcxF,GAAnD,CAA9B;AACA;AAAA;AAAA,sCAAS,iBAAT,EAA4B,eAAcuF,OAAQ,EAAlD;AACH,WAHD,MAGO;AACHA,YAAAA,OAAO,GAAG;AAAA;AAAA,oCAAQzB,aAAR,CAAsBF,MAAtB,MAAkC,KAAK4B,OAAL,CAAcvF,GAAd,GAAoB,KAAKuF,OAAL,CAAcxF,GAApE,CAAV;AACH;;AACDgF,UAAAA,QAAQ,CAACzD,WAAT,CAAqB,KAAKL,IAAL,CAAUC,QAAV,CAAmBR,CAAnB,GAAuB4E,OAA5C,EAAqD,CAArD,EAAwD,CAAxD,EAzCJ,CA2CI;;AACA,cAAIhF,KAAK,GAAG,CAAZ;;AACA,cAAI7B,MAAJ,EAAY;AACR6B,YAAAA,KAAK,GAAG,KAAKA,KAAL,GAAa,KAAKkF,WAAL,CAAkBzF,GAA/B,GAAqCS,IAAI,CAACmD,MAAL,MAAiB,KAAK6B,WAAL,CAAkBxF,GAAlB,GAAwB,KAAKwF,WAAL,CAAkBzF,GAA3D,CAA7C;AACH,WAFD,MAEO;AACHO,YAAAA,KAAK,GAAG,KAAKA,KAAL,GAAa,KAAKkF,WAAL,CAAkBzF,GAA/B,GAAqC;AAAA;AAAA,oCAAQ8D,aAAR,CAAsBF,MAAtB,MAAkC,KAAK6B,WAAL,CAAkBxF,GAAlB,GAAwB,KAAKwF,WAAL,CAAkBzF,GAA5E,CAA7C;AACH,WAjDL,CAmDI;;;AACA,cAAIM,KAAK,GAAG,CAAZ;;AACA,cAAI5B,MAAJ,EAAY;AACR4B,YAAAA,KAAK,GAAG,KAAKA,KAAL,GAAa,KAAKoF,WAAL,CAAkB1F,GAA/B,GAAqCS,IAAI,CAACmD,MAAL,MAAiB,KAAK8B,WAAL,CAAkBzF,GAAlB,GAAwB,KAAKyF,WAAL,CAAkB1F,GAA3D,CAA7C;AACA;AAAA;AAAA,sCAAS,iBAAT,EAA4B,cAAaM,KAAM,GAA/C;AACH,WAHD,MAGO;AACHA,YAAAA,KAAK,GAAG,KAAKA,KAAL,GAAa,KAAKoF,WAAL,CAAkB1F,GAA/B,GAAqC;AAAA;AAAA,oCAAQ8D,aAAR,CAAsBF,MAAtB,MAAkC,KAAK8B,WAAL,CAAkBzF,GAAlB,GAAwB,KAAKyF,WAAL,CAAkB1F,GAA5E,CAA7C;AACH,WA1DL,CA4DI;;;AACA,cAAI2F,aAAa,GAAGX,QAAQ,CAACY,YAAT,CAAsB7H,YAAtB,CAApB;;AACA,cAAI,CAAC4H,aAAL,EAAoB;AAChBA,YAAAA,aAAa,GAAGX,QAAQ,CAACM,YAAT,CAAsBvH,YAAtB,CAAhB;AACH,WAhEL,CAkEI;;;AACA4H,UAAAA,aAAa,CAACtF,IAAd,CAAmBC,KAAnB,EAA0BC,KAA1B,EAnEJ,CAqEI;;AACA,eAAKW,IAAL,CAAUoC,QAAV,CAAmB0B,QAAnB,EAtEJ,CAwEI;;AACA,eAAKnD,eAAL,CAAqBgE,IAArB,CAA0BF,aAA1B,EAzEJ,CA2EI;;;AACA,eAAKxD,UAAL;AACA,eAAKD,aAAL,GAAqBwC,WAArB,CA7EJ,CA+EI;;AACA,cAAIhG,MAAJ,EAAY;AACR,iBAAK0D,aAAL,GAAqB,KAAK0D,QAAL,GAAgB,KAAKC,cAAL,CAAqB/F,GAArC,GAA2CS,IAAI,CAACmD,MAAL,MAAiB,KAAKmC,cAAL,CAAqB9F,GAArB,GAA2B,KAAK8F,cAAL,CAAqB/F,GAAjE,CAAhE;AACA;AAAA;AAAA,sCAAS,iBAAT,EAA4B,gBAAe,KAAKoC,aAAc,EAA9D;AACH,WAHD,MAGO;AACH,iBAAKA,aAAL,GAAqB,KAAK0D,QAAL,GAAgB,KAAKC,cAAL,CAAqB/F,GAArC,GAA2C;AAAA;AAAA,oCAAQ8D,aAAR,CAAsBF,MAAtB,MAAkC,KAAKmC,cAAL,CAAqB9F,GAArB,GAA2B,KAAK8F,cAAL,CAAqB/F,GAAlF,CAAhE;AACH,WArFL,CAuFI;;;AACA,eAAKgG,iBAAL;AACH;;AAEOA,QAAAA,iBAAiB,GAAS;AAC9B,cAAI,CAAC,KAAK9C,QAAV,EAAoB;;AAEpB,kBAAQ,KAAKrD,IAAb;AACI,iBAAK;AAAA;AAAA,wDAAkBL,QAAvB;AACI;AACA,kBAAI,KAAKyC,UAAL,IAAmB,KAAKF,SAA5B,EAAuC;AACnC,qBAAKJ,OAAL,GAAehC,cAAc,CAACsG,GAA9B;AACH;;AACD;;AAEJ,iBAAK;AAAA;AAAA,wDAAkBxG,KAAvB;AACI;AACA,kBAAI,KAAK0C,UAAL,IAAmB,KAAKJ,SAA5B,EAAuC;AACnC,qBAAKJ,OAAL,GAAehC,cAAc,CAACsG,GAA9B;AACH;;AACD;;AAEJ,iBAAK;AAAA;AAAA,wDAAkBvG,KAAvB;AACI;AACA;AACA;AAEJ;AApBJ;;AAuBA,cAAI,KAAKiC,OAAL,KAAiBhC,cAAc,CAACsG,GAApC,EAAwC;AACpC,iBAAKC,eAAL;AACH;AACJ;;AAEOA,QAAAA,eAAe,GAAS;AAC5B,eAAKC,mBAAL;;AAEA,cAAI,CAACzH,MAAL,EAAa;AACT,iBAAKwC,IAAL,CAAUkF,OAAV;AACH,WAFD,MAEO;AACH;AACA,iBAAKtD,UAAL;;AACA,iBAAKuD,YAAL;AACH;AACJ;;AAEOvD,QAAAA,UAAU,GAAS;AACvB,eAAKnB,OAAL,GAAehC,cAAc,CAACiC,QAA9B;AACA,eAAKO,UAAL,GAAkB,CAAlB;AACA,eAAKE,mBAAL,GAA2B,KAA3B;AACA,eAAKR,eAAL,GAAuB,EAAvB;AACA,eAAKI,UAAL,GAAkB,CAAlB;AACA,eAAKC,aAAL,GAAqB,CAArB;AACA,eAAKE,aAAL,GAAqB,CAArB;AACA,eAAKL,SAAL,GAAiB,CAAjB;AACA,eAAKD,SAAL,GAAiB,CAAjB;AACA,eAAKE,YAAL,GAAoB,CAApB;AACH;;AAEMsE,QAAAA,IAAI,CAACC,KAAD,EAAuB;AAC9B,cAAI7H,MAAJ,EAAY;AACR,gBAAI6H,KAAJ,EAAW;AACP,mBAAK5E,OAAL,GAAehC,cAAc,CAAC8D,MAA9B;AACA,mBAAKD,aAAL;AACA;AAAA;AAAA,wCAAS,iBAAT,EAA2B,eAA3B;AACH,aAJD,MAIO;AACH,mBAAK7B,OAAL,GAAehC,cAAc,CAACiC,QAA9B;;AACA,mBAAKsE,eAAL;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACYC,QAAAA,mBAAmB,GAAS;AAChC;AACA,eAAK,MAAM7B,IAAX,IAAmB,KAAKzC,eAAxB,EAAyC;AACrC,gBAAIyC,IAAI,CAACC,OAAT,EAAkB;AACdD,cAAAA,IAAI,CAACpD,IAAL,CAAUkF,OAAV;AACH;AACJ,WAN+B,CAQhC;;;AACA,eAAKvE,eAAL,GAAuB,EAAvB;AACH;;AAES2E,QAAAA,SAAS,GAAS;AACxB,eAAKtF,IAAL,CAAU6B,iBAAV;AACH;;AAEOsD,QAAAA,YAAY,GAAS;AACzB,cAAI,KAAK/D,SAAT,EAAoB;AAChB;AAAA;AAAA,sCAAS,iBAAT,EAA4B,MAAK,KAAKpB,IAAL,CAAUkE,IAAK,UAAS,KAAK9C,SAAU,EAAxE;AACA;AAAA;AAAA,oCAAQ2C,cAAR,CAAuBC,oBAAvB,CAA4CuB,GAA5C,CAAgD,KAAKnE,SAArD,EAAgE,KAAKpB,IAArE;AACH,WAHD,MAGO;AACH;AACA;AAAA;AAAA,sCAAS,iBAAT,EAA6B,KAAI,KAAKA,IAAL,CAAUkE,IAAK,KAAhD;AACA,iBAAKlE,IAAL,CAAUkF,OAAV;AACH;AACJ;;AAjV0C,O;;;;;iBAGf,K;;;;;;;iBAKK,I;;;;;;;iBAEf;AAAA;AAAA,8DAAsBxB,M;;;;;;;iBAEJ,E;;;;;;;iBAKtB;AAAA;AAAA,sDAAkBrF,Q;;;;;;;iBAET,C;;;;;;;iBAEuB,IAAIK,uBAAJ,E;;;;;;;iBAEnB,C;;;;;;;iBAEmB,IAAIA,uBAAJ,E;;;;;;;iBAEpB,C;;;;;;;iBAEuB,IAAIA,uBAAJ,E;;;;;;;iBAE1B,C;;;;;;;iBAEuB,IAAIA,uBAAJ,E;;;;;;;iBAEvB,C;;;;;;;iBAEuB,IAAIA,uBAAJ,E;;;;;;;iBAEJ,IAAIA,uBAAJ,E", "sourcesContent": ["import { _decorator, assetManager, Camera, CCBoolean, CC<PERSON>loat, CC<PERSON><PERSON>ger, Component, director, Enum, instantiate, Prefab, UITransform, Vec3, view } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { LayerEmittierStrategy, LayerEmittierType, LayerRandomRange } from '../../leveldata/leveldata';\r\nimport { GameIns } from '../GameIns';\r\nimport { logDebug } from 'db://assets/scripts/utils/Logger';\r\nimport { LevelNodeCheckOutScreen } from '../ui/map/LevelNodeCheckOutScreen';\r\nconst { ccclass, property, executeInEditMode, menu} = _decorator;\r\n\r\nconst EMIITER_POOL_NAME = \"emittier_pool\";\r\n\r\nenum LayerEmittierTypeZh {\r\n    无限 = LayerEmittierType.Infinite,\r\n    持续时间 = LayerEmittierType.Duration,\r\n    发射次数 = LayerEmittierType.Count,\r\n    监听事件 = LayerEmittierType.Event\r\n}\r\n\r\nexport enum EmittierStatus {\r\n    inactive,\r\n    active,\r\n    pause,\r\n    end\r\n}\r\n\r\n@ccclass('SerializableRandomRange')\r\nexport class SerializableRandomRange {\r\n    @property({type: CCFloat, displayName: \"最小值\"})\r\n    public min: number = 0;\r\n    \r\n    @property({type: CCFloat, displayName: \"最大值\"})\r\n    public max: number = 0;\r\n    \r\n    // 提供转换方法\r\n    toLayerRandomRange(): LayerRandomRange {\r\n        return new LayerRandomRange(this.min, this.max);\r\n    }\r\n    \r\n    fromLayerRandomRange(range: LayerRandomRange): void {\r\n        this.min = range.min;\r\n        this.max = range.max;\r\n    }\r\n}\r\n\r\nexport class EmittierElem extends Component {\r\n    private _velocity: Vec3 = new Vec3(0, 0, 0);\r\n    \r\n    /**\r\n     * 初始化元素运动参数\r\n     * @param speed 运动速度\r\n     * @param angle 运动角度（度）\r\n     */\r\n    public init(speed: number, angle: number): void {\r\n        // 将角度转换为弧度\r\n        const rad = angle * Math.PI / 180;\r\n        \r\n        // 计算速度分量\r\n        this._velocity.x = Math.cos(rad) * speed;\r\n        this._velocity.y = Math.sin(rad) * speed;\r\n    }\r\n    \r\n    tick(dt: number): void {\r\n        // 更新位置\r\n        const pos = this.node.position.clone();\r\n        pos.add(this._velocity.clone().multiplyScalar(dt));\r\n        this.node.setPosition(pos);\r\n    }\r\n}\r\n\r\n@ccclass('EmittierTerrain')\r\n@executeInEditMode()\r\n@menu('地形系统/地形发射器')\r\nexport class EmittierTerrain extends Component {\r\n\r\n    @property({visible: false})\r\n    private _bfollow: boolean = false;\r\n    @property({type: CCBoolean, displayName: \"是否跟随层级移动\"})\r\n    public set bfollow(value: boolean) { this._bfollow = value; }\r\n    public get bfollow(): boolean { return this._bfollow; }\r\n    @property({type: Prefab, displayName: \"发射器\"})\r\n    public emittier: Prefab | null = null;\r\n    @property({type: Enum(LayerEmittierStrategy), displayName: \"发射策略\"})\r\n    public strategy = LayerEmittierStrategy.Random;\r\n    @property({type: [Prefab], displayName: \"发射元素组\"})\r\n    public emittierElements: Prefab[] = [];\r\n    @property({type: Enum(LayerEmittierTypeZh), displayName:\"发射器类型\",})\r\n    public get typeZh(): LayerEmittierTypeZh { return this.type as unknown as LayerEmittierTypeZh;}\r\n    public set typeZh(value: LayerEmittierTypeZh) { this.type = value as unknown as LayerEmittierType;}\r\n    @property({visible: false})\r\n    public type = LayerEmittierType.Infinite;\r\n    @property({type: CCFloat, displayName: \"效果值\", tooltip: \"值对应类型:Infinite为无限,Duration为持续时间(ms),Count为发射次数,Event为监听事件\"})\r\n    public value: number = 0; // 根据type决定用途，Infinite为无限，Duration为持续时间，Count为发射次数，Event为监听事件\r\n    @property({type: SerializableRandomRange, displayName: \"效果值校正\"})\r\n    public valueModify: SerializableRandomRange = new SerializableRandomRange(); \r\n    @property({type: CCInteger, displayName: \"初始延迟(ms)\", min: 0})\r\n    public initDelay: number = 0;\r\n    @property({type: SerializableRandomRange, displayName: \"延迟校正(ms)\"})\r\n    public delayModify: SerializableRandomRange = new SerializableRandomRange();\r\n    @property({type: CCInteger, displayName: \"发射间隔(ms)\"})\r\n    public interval: number = 0;\r\n    @property({type: SerializableRandomRange, displayName: \"间隔校正(ms)\"})\r\n    public intervalModify: SerializableRandomRange = new SerializableRandomRange();\r\n    @property({type: CCInteger, displayName: \"发射角度(0-360)\",min: 0, max: 360})\r\n    public angle: number = 0;\r\n    @property({type: SerializableRandomRange, displayName: \"角度校正(0-360)\"})\r\n    public angleModify: SerializableRandomRange = new SerializableRandomRange();     \r\n    @property({type: CCFloat, displayName: \"发射速度\"})\r\n    public speed: number = 0;\r\n    @property({type: SerializableRandomRange, displayName: \"速度校正\"})\r\n    public speedModify: SerializableRandomRange = new SerializableRandomRange();\r\n    @property({type: SerializableRandomRange, displayName:\"X偏移范围\"})\r\n    public offSetX: SerializableRandomRange = new SerializableRandomRange();\r\n\r\n    private _status: EmittierStatus = EmittierStatus.inactive;\r\n    private _activeElements: EmittierElem[] = [];\r\n\r\n    private _curDelay: number = 0; // 当前延迟\r\n    private _curValue: number = 0; // 当前效果值\r\n    private _curEmiIndex: number = 0; // 当前发射器索引\r\n\r\n    private _deltaTime = 0; // 发射器运行总时间\r\n    private _lastEmitTime = 0; // 上次发射时间\r\n    private _emitCount: number = 0; // 已发射元素计数\r\n    private _nextInterval: number = 0; // 下一次发射的间隔时间\r\n    private _initialDelayPassed: boolean = false; // 初始延迟是否已过\r\n\r\n    private _poolName: string = '';\r\n\r\n    public get status(): EmittierStatus {\r\n        return this._status;\r\n    }\r\n\r\n    public get follow(): boolean {\r\n        return this._bfollow;\r\n    }\r\n\r\n    protected onLoad(): void {\r\n        this._resetData();\r\n\r\n        if (EDITOR) {\r\n            this.node.removeAllChildren();\r\n            assetManager.loadAny({ uuid: this.emittier!.uuid }, (err, prefab: Prefab) => {\r\n                if (err) {\r\n                    return;\r\n                } else {\r\n                    \r\n                    const emitterNode = instantiate(prefab);    \r\n                    this.node.addChild(emitterNode);\r\n                }\r\n            });\r\n        }\r\n    }\r\n\r\n    init(poolName: string) {\r\n        this._poolName = poolName;\r\n        this._status = EmittierStatus.inactive;\r\n        this._resetData();\r\n    }\r\n\r\n    public startEmittier(): void {\r\n        this._status = EmittierStatus.active;\r\n        if (EDITOR) {\r\n            this._curDelay = this.initDelay + this.delayModify!.min + Math.random() * (this.delayModify!.max - this.delayModify!.min);\r\n            this._curValue = this.value + Math.random() * (this.valueModify!.max - this.valueModify!.min);\r\n        } else {\r\n            this._curDelay = this.initDelay + this.delayModify!.min + GameIns.battleManager.random() * (this.delayModify!.max - this.delayModify!.min);\r\n            this._curValue = this.value + this.valueModify!.min + GameIns.battleManager.random() * (this.valueModify!.max - this.valueModify!.min);\r\n        }\r\n    }\r\n\r\n    public tick(dt: number): void {\r\n        if (this._status !== EmittierStatus.active) return;\r\n\r\n        const dtMs = dt * 1000;\r\n        // 运行发射器\r\n        this._updateEmitter(dtMs);\r\n        this._updateActiveElements(dt);\r\n    }\r\n\r\n    /**\r\n     * 更新发射器自身状态\r\n     * @param dt 增量时间（豪秒）\r\n     */\r\n    private _updateEmitter(dt: number): void {\r\n        this._deltaTime += dt;\r\n        \r\n        if (!this._initialDelayPassed) {\r\n            const delaySeconds = this._curDelay;\r\n            if (this._deltaTime >= delaySeconds) {\r\n                this._initialDelayPassed = true;\r\n                this._deltaTime = 0; // 重置时间，开始发射循环\r\n            }\r\n            return;\r\n        }\r\n        \r\n        // 运行发射器\r\n        this._runEmittier();\r\n    }\r\n\r\n    /**\r\n     * 更新所有已发射的元素\r\n     * @param dt 增量时间（秒）\r\n     */\r\n    private _updateActiveElements(dt: number): void {\r\n        // 遍历所有元素并更新\r\n        for (let i = this._activeElements.length - 1; i >= 0; i--) {\r\n            const elem = this._activeElements[i];\r\n            \r\n            // 检查元素是否已被销毁\r\n            if (!elem.isValid) {\r\n                // 从数组中移除已销毁的元素\r\n                this._activeElements.splice(i, 1);\r\n                continue;\r\n            }\r\n            \r\n            // 更新元素状态\r\n            elem.tick(dt);\r\n        }\r\n    }\r\n\r\n    private _runEmittier(): void \r\n    {\r\n        if (!this.emittier || this.emittierElements.length === 0) return;\r\n        \r\n        const currentTime = this._deltaTime;\r\n        \r\n        // 检查是否达到发射间隔\r\n        if (currentTime - this._lastEmitTime < this._nextInterval) return;\r\n        \r\n        // 随机选择要发射的元素\r\n        if (this.strategy === LayerEmittierStrategy.Random) { \r\n            if (EDITOR) {\r\n                this._curEmiIndex = Math.floor(Math.random() * this.emittierElements.length);\r\n            } else {\r\n                this._curEmiIndex = Math.floor(GameIns.battleManager.random() * this.emittierElements.length);\r\n            }\r\n        } else if (this.strategy === LayerEmittierStrategy.Sequence) {\r\n            this._curEmiIndex = this._emitCount % this.emittierElements.length;\r\n        }\r\n        const elemPrefab = this.emittierElements[this._curEmiIndex];\r\n\r\n        let elemNode = GameIns.gameMapManager.mapObjectPoolManager.get(\r\n            EMIITER_POOL_NAME, \r\n            elemPrefab.name\r\n        );\r\n        \r\n        if (elemNode) {\r\n            logDebug('EmittierTerrain',` 从对象池获取发射体 ${elemPrefab.name}`);\r\n        } else {   \r\n            elemNode = instantiate(elemPrefab);    \r\n            const cheekOut = elemNode.addComponent(LevelNodeCheckOutScreen);\r\n            cheekOut.init(EMIITER_POOL_NAME);\r\n        }\r\n        \r\n        // 设置初始位置\r\n        let offsetX = 0;\r\n        if (EDITOR) {\r\n            offsetX = this.offSetX!.min + Math.random() * (this.offSetX!.max - this.offSetX!.min);\r\n            logDebug('EmittierTerrain',` 随机地形 x坐标偏移：${offsetX}`);\r\n        } else {\r\n            offsetX = GameIns.battleManager.random() * (this.offSetX!.max - this.offSetX!.min);\r\n        }\r\n        elemNode.setPosition(this.node.position.x + offsetX, 0, 0);\r\n        \r\n        // 设置初始角度\r\n        let angle = 0;\r\n        if (EDITOR) {\r\n            angle = this.angle + this.angleModify!.min + Math.random() * (this.angleModify!.max - this.angleModify!.min);\r\n        } else {\r\n            angle = this.angle + this.angleModify!.min + GameIns.battleManager.random() * (this.angleModify!.max - this.angleModify!.min);\r\n        }\r\n        \r\n        // 设置初始速度\r\n        let speed = 0;\r\n        if (EDITOR) {\r\n            speed = this.speed + this.speedModify!.min + Math.random() * (this.speedModify!.max - this.speedModify!.min);\r\n            logDebug('EmittierTerrain',` 随机地形 发射速度：${speed} `);\r\n        } else {\r\n            speed = this.speed + this.speedModify!.min + GameIns.battleManager.random() * (this.speedModify!.max - this.speedModify!.min);\r\n        }\r\n        \r\n        // 获取或添加EmittierElem组件\r\n        let elemComponent = elemNode.getComponent(EmittierElem);\r\n        if (!elemComponent) {\r\n            elemComponent = elemNode.addComponent(EmittierElem);\r\n        }\r\n        \r\n        // 初始化元素运动\r\n        elemComponent.init(speed, angle);\r\n        \r\n        // 添加到场景\r\n        this.node.addChild(elemNode);\r\n\r\n        // 添加到活动元素列表\r\n        this._activeElements.push(elemComponent);\r\n         \r\n        // 更新发射计数和时间\r\n        this._emitCount++;\r\n        this._lastEmitTime = currentTime;\r\n        \r\n        // 计算下一次发射的间隔时间\r\n        if (EDITOR) {\r\n            this._nextInterval = this.interval + this.intervalModify!.min + Math.random() * (this.intervalModify!.max - this.intervalModify!.min);\r\n            logDebug('EmittierTerrain',` 随机地形 下次发射间隔：${this._nextInterval}`);\r\n        } else {\r\n            this._nextInterval = this.interval + this.intervalModify!.min + GameIns.battleManager.random() * (this.intervalModify!.max - this.intervalModify!.min);            \r\n        }\r\n        \r\n        // 检查发射器是否结束\r\n        this._checkEmittierEnd();\r\n    }\r\n\r\n    private _checkEmittierEnd(): void {\r\n        if (!this.emittier) return;\r\n        \r\n        switch (this.type) {\r\n            case LayerEmittierType.Duration:\r\n                // 持续时间结束\r\n                if (this._deltaTime >= this._curValue) {\r\n                    this._status = EmittierStatus.end;\r\n                }\r\n                break;\r\n                \r\n            case LayerEmittierType.Count:\r\n                // 发射次数达到\r\n                if (this._emitCount >= this._curValue) {\r\n                    this._status = EmittierStatus.end;\r\n                }\r\n                break;\r\n                \r\n            case LayerEmittierType.Event:\r\n                // 事件触发结束（需要外部实现）\r\n                // 这里可以添加事件监听逻辑\r\n                break;\r\n                \r\n            // 无限类型不需要结束检查\r\n        }\r\n\r\n        if (this._status === EmittierStatus.end){\r\n            this._destroyEmitter();\r\n        }\r\n    }\r\n\r\n    private _destroyEmitter(): void {\r\n        this._destroyAllElements();\r\n\r\n        if (!EDITOR) {\r\n            this.node.destroy();\r\n        } else {\r\n            // 在编辑器中，我们只重置状态，不实际销毁节点\r\n            this._resetData();\r\n            this._recycleNode();\r\n        }\r\n    }\r\n\r\n    private _resetData(): void {\r\n        this._status = EmittierStatus.inactive;\r\n        this._emitCount = 0;\r\n        this._initialDelayPassed = false;\r\n        this._activeElements = [];\r\n        this._deltaTime = 0;\r\n        this._lastEmitTime = 0;\r\n        this._nextInterval = 0;\r\n        this._curValue = 0;\r\n        this._curDelay = 0;\r\n        this._curEmiIndex = 0;\r\n    }\r\n\r\n    public play(bPlay: boolean): void {\r\n        if (EDITOR) {\r\n            if (bPlay) {\r\n                this._status = EmittierStatus.active;\r\n                this.startEmittier();\r\n                logDebug('EmittierTerrain',\"play emittier\");\r\n            } else {\r\n                this._status = EmittierStatus.inactive;\r\n                this._destroyEmitter();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 销毁所有已发射的元素\r\n     */\r\n    private _destroyAllElements(): void {\r\n        // 销毁所有元素节点\r\n        for (const elem of this._activeElements) {\r\n            if (elem.isValid) {\r\n                elem.node.destroy();\r\n            }\r\n        }\r\n        \r\n        // 清空元素列表\r\n        this._activeElements = [];\r\n    }\r\n\r\n    protected onDestroy(): void {\r\n        this.node.removeAllChildren();\r\n    }\r\n\r\n    private _recycleNode(): void {\r\n        if (this._poolName) {\r\n            logDebug('EmittierTerrain',` 节点${this.node.name}已回收到对象池${this._poolName}`);\r\n            GameIns.gameMapManager.mapObjectPoolManager.put(this._poolName, this.node);\r\n        } else {\r\n            // 没有指定对象池时直接销毁\r\n            logDebug('EmittierTerrain', `节点${this.node.name}已销毁`);\r\n            this.node.destroy();\r\n        }\r\n    }\r\n}\r\n\r\n"]}
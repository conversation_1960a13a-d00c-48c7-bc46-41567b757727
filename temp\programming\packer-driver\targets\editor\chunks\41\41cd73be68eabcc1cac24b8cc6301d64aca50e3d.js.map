{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/preview/WavePreview.ts"], "names": ["_decorator", "Graphics", "Color", "Component", "Vec3", "instantiate", "assetManager", "CCObject", "Wave", "LubanMgr", "EnemyPlane", "eMoveEvent", "PathData", "PathEditor", "LevelEditorEventUI", "ccclass", "property", "executeInEditMode", "menu", "requireComponent", "WavePreview", "_luban", "_cachedParentSelection", "_cachedWorldPos", "_wave", "_graphics", "_parentEvent", "_lineColor", "RED", "isPreviewing", "luban", "onLoad", "node", "parent", "getComponent", "addComponent", "isSelected", "reset", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "update", "dt", "worldPos", "worldPosition", "shouldDraw", "equals", "set", "parentSelected", "tickPreviewDraw", "tickPreview", "setLineColor", "color", "pathAsset", "waveData", "path", "fromJSON", "json", "subdivided", "getSubdividedPoints", "isParentSelected", "isWaveActive", "isSpawnCompleted", "waveElapsedTime", "GREEN", "YELLOW", "length", "drawUniformPath", "closed", "endPoint", "prevPoint", "direction", "Math", "atan2", "y", "x", "drawPathDirectionArrow", "position", "drawPathPoints", "points", "i", "point", "drawPathPointAtPosition", "startIdx", "endIdx", "tickActivePlane", "activePlane", "for<PERSON>ach", "plane", "moveCom", "tick", "triggerPreview", "posX", "posY", "console", "log", "waveName", "setCreatePlaneDelegate", "planeId", "pos", "angle", "createDummyPlane", "trigger", "dtInMiliseconds", "clearPreview", "clearPool", "destroy", "planePool", "wave", "pop", "active", "initMove", "initPath", "includes", "push", "dummy_plane_uuid", "loadAny", "uuid", "err", "prefab", "error", "canvas", "scene", "getChildByName", "planeNode", "hideFlags", "Flags", "AllHideMasks", "<PERSON><PERSON><PERSON><PERSON>", "removeAllListeners", "on", "onBecomeInvisible", "filter", "p"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACSA,MAAAA,U,OAAAA,U;AAA0BC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,Y,OAAAA,Y;AAAcC,MAAAA,Q,OAAAA,Q;;AAG7FC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,Q,iBAAAA,Q;;AAEFC,MAAAA,U;;AAGEC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,kB,iBAAAA,kB;;;;;;;;;OAXH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA,iBAArB;AAAwCC,QAAAA,IAAxC;AAA8CC,QAAAA;AAA9C,O,GAAmEnB,U;;AAazE;6BAKaoB,W,WAJZL,OAAO,CAAC,aAAD,C,UACPG,IAAI,CAAC,aAAD,C,UACJC,gBAAgB,CAAClB,QAAD,C,UAChBgB,iBAAiB,E,wEAHlB,MAIaG,WAJb,SAIiCjB,SAJjC,CAI2C;AAAA;AAAA;AAAA,eAC/BkB,MAD+B,GACP,IADO;AAAA,eAS/BC,sBAT+B,GASG,KATH;AAAA,eAU/BC,eAV+B,GAUP,IAAInB,IAAJ,EAVO;AAAA,eAW/BoB,KAX+B,GAWZ,IAXY;AAAA,eAY/BC,SAZ+B,GAYJ,IAZI;AAAA,eAa/BC,YAb+B,GAamB,IAbnB;AAAA,eA6C/BC,UA7C+B,GA6CXzB,KAAK,CAAC0B,GA7CK;AAmGvC;AAnGuC,eAoG/BC,YApG+B,GAoGP,KApGO;AAAA;;AAEvB,YAALC,KAAK,GAAkB;AAC9B,cAAI,KAAKT,MAAL,IAAe,IAAnB,EAAyB;AACrB,iBAAKA,MAAL,GAAc;AAAA;AAAA,uCAAd;AACH;;AACD,iBAAO,KAAKA,MAAZ;AACH;;AAODU,QAAAA,MAAM,GAAG;AAAA;;AACL,eAAKP,KAAL,GAAa,KAAKQ,IAAL,CAAUC,MAAV,CAAkBC,YAAlB;AAAA;AAAA,2BAAb;AACA,eAAKT,SAAL,GAAiB,KAAKS,YAAL,CAAkBjC,QAAlB,KAA+B,KAAKkC,YAAL,CAAkBlC,QAAlB,CAAhD;AACA,eAAKyB,YAAL,cAAoB,KAAKM,IAAL,CAAUC,MAAV,CAAkBA,MAAtC,qBAAoB,QAA0BC,YAA1B;AAAA;AAAA,uDAApB;AACA,eAAKZ,sBAAL,GAA8B,4BAAKI,YAAL,wCAAmBU,UAAnB,OAAmC,KAAjE;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJ,eAAKL,IAAL,CAAUM,iBAAV;AACH;;AAEDC,QAAAA,MAAM,CAACC,EAAD,EAAa;AAAA;;AACf,gBAAMC,QAAQ,GAAG,KAAKT,IAAL,CAAUC,MAAV,CAAkBS,aAAnC;AAEA,cAAIC,UAAU,GAAG,KAAjB;;AACA,cAAI,CAAC,KAAKpB,eAAL,CAAqBqB,MAArB,CAA4BH,QAA5B,CAAL,EAA4C;AACxC,iBAAKlB,eAAL,CAAqBsB,GAArB,CAAyBJ,QAAzB;;AACAE,YAAAA,UAAU,GAAG,IAAb;AACH;;AACD,gBAAMG,cAAc,GAAG,6BAAKpB,YAAL,yCAAmBU,UAAnB,OAAmC,KAA1D;;AACA,cAAI,KAAKd,sBAAL,IAA+BwB,cAAnC,EAAmD;AAC/C,iBAAKxB,sBAAL,GAA8BwB,cAA9B;AACAH,YAAAA,UAAU,GAAG,IAAb;AACH;;AAED,cAAIA,UAAJ,EAAgB;AACZ,iBAAKI,eAAL;AACH;;AACD,eAAKC,WAAL,CAAiBR,EAAjB;AACH;;AAGMS,QAAAA,YAAY,CAACC,KAAD,EAAe;AAC9B,eAAKvB,UAAL,GAAkBuB,KAAlB;AACH;;AAEOH,QAAAA,eAAe,GAAG;AAAA;;AACtB,gBAAMI,SAAS,kBAAG,KAAK3B,KAAR,qBAAG,YAAY4B,QAAZ,CAAqBD,SAAvC;;AACA,cAAIA,SAAJ,EAAe;AAAA;;AACX;AACA,kBAAME,IAAI,GAAG;AAAA;AAAA,sCAASC,QAAT,CAAkBH,SAAS,CAACI,IAA5B,CAAb;AACA,kBAAMC,UAAU,GAAGH,IAAI,CAACI,mBAAL,EAAnB;AAEA,kBAAMC,gBAAgB,0BAAG,KAAKhC,YAAR,qBAAG,oBAAmBU,UAAnB,EAAzB;AACA,kBAAMuB,YAAY,GAAG,KAAK9B,YAAL,IAAqB,CAAC,KAAKL,KAAL,CAAYoC,gBAAlC,IAAsD,KAAKpC,KAAL,CAAYqC,eAAZ,GAA8B,CAAzG;AACA,kBAAMX,KAAK,GAAGS,YAAY,GAAGzD,KAAK,CAAC4D,KAAT,GAAiBJ,gBAAgB,GAAGxD,KAAK,CAAC6D,MAAT,GAAkB,KAAKpC,UAAlF;;AACA,gBAAI6B,UAAU,CAACQ,MAAX,GAAoB,CAAxB,EAA2B;AACvB;AAAA;AAAA,4CAAWC,eAAX,CAA2B,KAAKxC,SAAhC,EAA4C+B,UAA5C,EAAwDN,KAAxD,EAA+DG,IAAI,CAACa,MAApE,EAA4E,EAA5E;AACA,oBAAMC,QAAQ,GAAGX,UAAU,CAACA,UAAU,CAACQ,MAAX,GAAoB,CAArB,CAA3B;AACA,kBAAII,SAAS,GAAGZ,UAAU,CAACA,UAAU,CAACQ,MAAX,GAAoB,CAArB,CAA1B;;AACA,kBAAIR,UAAU,CAACQ,MAAX,IAAqB,CAAzB,EAA4B;AACxBI,gBAAAA,SAAS,GAAGZ,UAAU,CAACA,UAAU,CAACQ,MAAX,GAAoB,CAArB,CAAtB;AACH;;AACD,oBAAMK,SAAS,GAAGC,IAAI,CAACC,KAAL,CAAWJ,QAAQ,CAACK,CAAT,GAAaJ,SAAS,CAACI,CAAlC,EAAqCL,QAAQ,CAACM,CAAT,GAAaL,SAAS,CAACK,CAA5D,CAAlB;AACA;AAAA;AAAA,4CAAWC,sBAAX,CAAkC,KAAKjD,SAAvC,EAAmD0C,QAAQ,CAACQ,QAA5D,EAAsEN,SAAtE,EAAiFhB,IAAI,CAACa,MAAtF;AACH,aAjBU,CAkBX;;;AACA,iBAAKU,cAAL,CAAoBvB,IAApB;AACH;AACJ;;AAEOuB,QAAAA,cAAc,CAACvB,IAAD,EAAiB;AACnC,cAAI,CAACA,IAAI,CAACwB,MAAN,IAAgBxB,IAAI,CAACwB,MAAL,CAAYb,MAAZ,KAAuB,CAA3C,EAA8C,OADX,CAGnC;;AACA,eAAK,IAAIc,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGzB,IAAI,CAACwB,MAAL,CAAYb,MAAhC,EAAwCc,CAAC,EAAzC,EAA6C;AACzC,kBAAMC,KAAK,GAAG1B,IAAI,CAACwB,MAAL,CAAYC,CAAZ,CAAd;AAEA;AAAA;AAAA,0CAAWE,uBAAX,CACI,KAAKvD,SADT,EAEIsD,KAFJ,EAGIA,KAAK,CAACN,CAHV,EAIIM,KAAK,CAACP,CAJV,EAKI,KALJ,EAKW;AACP,cANJ,EAMW;AACPM,YAAAA,CAPJ,EAQIzB,IAAI,CAACwB,MAAL,CAAYb,MARhB,EASIX,IAAI,CAAC4B,QATT,EAUI5B,IAAI,CAAC6B,MAVT,EAWI,KAXJ,EAYI,KAZJ;AAcH;AACJ;;AAOqB,eAAfC,eAAe,CAAC3C,EAAD,EAAa;AAC/BpB,UAAAA,WAAW,CAACgE,WAAZ,CAAwBC,OAAxB,CAAiCC,KAAD,IAAW;AACvCA,YAAAA,KAAK,CAACC,OAAN,CAAeC,IAAf,CAAoBhD,EAApB;AACH,WAFD;AAGH;;AAEDiD,QAAAA,cAAc,CAACC,IAAD,EAAeC,IAAf,EAA6B;AAAA;;AACvCC,UAAAA,OAAO,CAACC,GAAR,CAAY,gCAAZ,EAA8CH,IAA9C,EAAoDC,IAApD,kBAA0D,KAAKnE,KAA/D,qBAA0D,aAAYsE,QAAtE;;AACA,cAAI,CAAC,KAAKtE,KAAV,EAAiB;AACb;AACH;;AAED,eAAKA,KAAL,CAAWuE,sBAAX,CAAkC,OAAOC,OAAP,EAAwBC,GAAxB,EAAmCC,KAAnC,KAAqD;AACnF,iBAAKC,gBAAL,CAAsB,KAAK3E,KAA3B,EAAmCwE,OAAnC,EAA4CC,GAA5C,EAAiDC,KAAjD;AACH,WAFD;;AAGA,eAAK1E,KAAL,CAAW4E,OAAX,CAAmBV,IAAnB,EAAyBC,IAAzB;;AACA,eAAK9D,YAAL,GAAoB,IAApB;AACH;;AAEDmB,QAAAA,WAAW,CAACR,EAAD,EAAa;AACpB,cAAI,CAAC,KAAKX,YAAV,EAAwB;AACpB;AACH;;AAED,gBAAMwE,eAAe,GAAG7D,EAAE,GAAG,IAA7B;;AACA,cAAI,KAAKhB,KAAT,EAAgB;AACZ,iBAAKA,KAAL,CAAWgE,IAAX,CAAgBa,eAAhB;AACH;AACJ;;AAEDC,QAAAA,YAAY,GAAG;AACX;AACA,eAAKzE,YAAL,GAAoB,KAApB,CAFW,CAIX;;AACA,eAAKG,IAAL,CAAUM,iBAAV;AACH;;AAEe,eAATiE,SAAS,GAAG;AACfnF,UAAAA,WAAW,CAACgE,WAAZ,CAAwBC,OAAxB,CAAiCC,KAAD,IAAW;AACvCA,YAAAA,KAAK,CAACtD,IAAN,CAAWwE,OAAX;AACH,WAFD;AAGApF,UAAAA,WAAW,CAACqF,SAAZ,CAAsBpB,OAAtB,CAA+BC,KAAD,IAAW;AACrCA,YAAAA,KAAK,CAACtD,IAAN,CAAWwE,OAAX;AACH,WAFD;AAGApF,UAAAA,WAAW,CAACqF,SAAZ,GAAwB,EAAxB;AACH;;AAEON,QAAAA,gBAAgB,CAACO,IAAD,EAAaV,OAAb,EAA8BC,GAA9B,EAAyCC,KAAzC,EAAwD;AAC5E;AACA;AACA,cAAIZ,KAAsB,GAAG,IAA7B;;AACA,cAAIlE,WAAW,CAACqF,SAAZ,CAAsBzC,MAAtB,GAA+B,CAAnC,EAAsC;AAClC;AACAsB,YAAAA,KAAK,GAAGlE,WAAW,CAACqF,SAAZ,CAAsBE,GAAtB,EAAR;AACArB,YAAAA,KAAK,CAACtD,IAAN,CAAW4E,MAAX,GAAoB,IAApB;AACAtB,YAAAA,KAAK,CAACuB,QAAN,CAAeZ,GAAG,CAACxB,CAAnB,EAAsBwB,GAAG,CAACzB,CAA1B,EAA6B0B,KAA7B;;AACA,gBAAIQ,IAAI,CAACrD,IAAT,EAAe;AACXiC,cAAAA,KAAK,CAACwB,QAAN,CAAeb,GAAG,CAACxB,CAAnB,EAAsBwB,GAAG,CAACzB,CAA1B,EAA6BkC,IAAI,CAACrD,IAAlC;AACH,aAPiC,CAQlC;;;AACA,gBAAI,CAACjC,WAAW,CAACgE,WAAZ,CAAwB2B,QAAxB,CAAiCzB,KAAjC,CAAL,EAA8C;AAC1ClE,cAAAA,WAAW,CAACgE,WAAZ,CAAwB4B,IAAxB,CAA6B1B,KAA7B;AACH;AACJ,WAZD,MAaK;AACD,kBAAM2B,gBAAwB,GAAG,sCAAjC;AACA3G,YAAAA,YAAY,CAAC4G,OAAb,CAAqB;AAACC,cAAAA,IAAI,EAACF;AAAN,aAArB,EAA8C,OAAOG,GAAP,EAAYC,MAAZ,KAA8B;AACxE,kBAAID,GAAJ,EAAS;AACLxB,gBAAAA,OAAO,CAAC0B,KAAR,CAAc,8CAAd,EAA8DF,GAA9D;AACA;AACH;;AACD,kBAAI;AACA;AACA;AACA;AACA,sBAAMG,MAAM,GAAG,KAAKvF,IAAL,CAAUwF,KAAV,CAAgBC,cAAhB,CAA+B,QAA/B,CAAf;;AACA,oBAAI,CAACF,MAAL,EAAa;AACT3B,kBAAAA,OAAO,CAAC0B,KAAR,CAAc,wCAAd;AACA;AACH;;AACD,sBAAMI,SAAS,GAAGrH,WAAW,CAACgH,MAAD,CAA7B;AACAK,gBAAAA,SAAS,CAACC,SAAV,GAAsBpH,QAAQ,CAACqH,KAAT,CAAeC,YAArC;AACA,sBAAMvC,KAAK,GAAGoC,SAAS,CAAExF,YAAX;AAAA;AAAA,6CAAd;;AACA,oBAAIoD,KAAJ,EAAW;AACPiC,kBAAAA,MAAM,CAACO,QAAP,CAAgBJ,SAAhB,EADO,CAEP;AACA;AACA;AACA;;AACApC,kBAAAA,KAAK,CAACuB,QAAN,CAAeZ,GAAG,CAACxB,CAAnB,EAAsBwB,GAAG,CAACzB,CAA1B,EAA6B0B,KAA7B;AACAZ,kBAAAA,KAAK,CAACC,OAAN,CAAewC,kBAAf;AACAzC,kBAAAA,KAAK,CAACC,OAAN,CAAeyC,EAAf,CAAkB;AAAA;AAAA,gDAAWC,iBAA7B,EAAgD,MAAM;AAClD3C,oBAAAA,KAAK,CAACtD,IAAN,CAAW4E,MAAX,GAAoB,KAApB;AACAtB,oBAAAA,KAAK,CAACC,OAAN,CAAelD,KAAf;AACAjB,oBAAAA,WAAW,CAACgE,WAAZ,GAA0BhE,WAAW,CAACgE,WAAZ,CAAwB8C,MAAxB,CAAgCC,CAAD,IAAOA,CAAC,KAAK7C,KAA5C,CAA1B;AACAlE,oBAAAA,WAAW,CAACqF,SAAZ,CAAsBO,IAAtB,CAA2B1B,KAA3B;AACH,mBALD;;AAMA,sBAAIoB,IAAI,CAACrD,IAAT,EAAe;AACXiC,oBAAAA,KAAK,CAACwB,QAAN,CAAeb,GAAG,CAACxB,CAAnB,EAAsBwB,GAAG,CAACzB,CAA1B,EAA6BkC,IAAI,CAACrD,IAAlC;AACH;;AACD,sBAAI,CAACjC,WAAW,CAACgE,WAAZ,CAAwB2B,QAAxB,CAAiCzB,KAAjC,CAAL,EAA8C;AAC1ClE,oBAAAA,WAAW,CAACgE,WAAZ,CAAwB4B,IAAxB,CAA6B1B,KAA7B;AACH;AACJ,iBApBD,MAoBO;AACHoC,kBAAAA,SAAS,CAAClB,OAAV;AACH;AACJ,eAnCD,CAoCA,OAAOc,KAAP,EAAc;AACV1B,gBAAAA,OAAO,CAAC0B,KAAR,CAAc,kCAAd,EAAkDA,KAAlD;AACH;AACJ,aA5CD;AA6CH;AACJ;;AAzNsC,O,UAsGhClC,W,GAA4B,E,UAC5BqB,S,GAA0B,E", "sourcesContent": ["\r\nimport { _decorator, Node, Prefab, Graphics, Color, Component, Vec2, Vec3, instantiate, assetManager, CCObject } from 'cc';\r\nconst { ccclass, property, executeInEditMode, menu, requireComponent } = _decorator;\r\nimport { WaveData, eSpawnOrder, eWaveCompletion } from 'db://assets/bundles/common/script/game/data/WaveData';\r\nimport { Wave } from 'db://assets/bundles/common/script/game/wave/Wave';\r\nimport { LubanMgr } from 'db://assets/bundles/common/script/luban/LubanMgr';\r\nimport { ObjectPool } from 'db://assets/bundles/common/script/game/bullet/ObjectPool';\r\nimport EnemyPlane from 'db://assets/bundles/common/script/game/ui/plane/enemy/EnemyPlane';\r\nimport { LevelEditorUtils } from '../utils';\r\nimport { EnemyData } from 'db://assets/bundles/common/script/game/data/EnemyData';\r\nimport { eMoveEvent } from 'db://assets/bundles/common/script/game/move/IMovable';\r\nimport { PathData } from 'db://assets/bundles/common/script/game/data/PathData';\r\nimport { PathEditor } from 'db://assets/editor/level/wave/PathEditor';\r\nimport { LevelEditorEventUI } from '../LevelEditorEventUI';\r\n\r\n/// 用来创建和管理波次的所有飞机对象\r\n@ccclass('WavePreview')\r\n@menu(\"怪物/编辑器/波次预览\")\r\n@requireComponent(Graphics)\r\n@executeInEditMode()\r\nexport class WavePreview extends Component {\r\n    private _luban: LubanMgr|null = null;\r\n    public get luban(): LubanMgr|null {\r\n        if (this._luban == null) {\r\n            this._luban = new LubanMgr();\r\n        }\r\n        return this._luban;\r\n    }\r\n\r\n    private _cachedParentSelection: boolean = false;\r\n    private _cachedWorldPos: Vec3 = new Vec3();\r\n    private _wave: Wave|null = null;\r\n    private _graphics: Graphics|null = null;\r\n    private _parentEvent: LevelEditorEventUI|null|undefined = null;\r\n    onLoad() {\r\n        this._wave = this.node.parent!.getComponent(Wave);\r\n        this._graphics = this.getComponent(Graphics) || this.addComponent(Graphics);\r\n        this._parentEvent = this.node.parent!.parent?.getComponent(LevelEditorEventUI);\r\n        this._cachedParentSelection = this._parentEvent?.isSelected() || false;\r\n    }\r\n\r\n    reset() {\r\n        this.node.removeAllChildren();\r\n    }\r\n\r\n    update(dt: number) {\r\n        const worldPos = this.node.parent!.worldPosition;\r\n        \r\n        let shouldDraw = false;\r\n        if (!this._cachedWorldPos.equals(worldPos)) {\r\n            this._cachedWorldPos.set(worldPos);\r\n            shouldDraw = true;\r\n        }\r\n        const parentSelected = this._parentEvent?.isSelected() || false;\r\n        if (this._cachedParentSelection != parentSelected) {\r\n            this._cachedParentSelection = parentSelected;\r\n            shouldDraw = true;\r\n        }\r\n\r\n        if (shouldDraw) {\r\n            this.tickPreviewDraw();\r\n        }\r\n        this.tickPreview(dt);\r\n    }\r\n\r\n    private _lineColor: Color = Color.RED;\r\n    public setLineColor(color: Color) {\r\n        this._lineColor = color;\r\n    }\r\n\r\n    private tickPreviewDraw() {\r\n        const pathAsset = this._wave?.waveData.pathAsset;\r\n        if (pathAsset) {\r\n            // draw path\r\n            const path = PathData.fromJSON(pathAsset.json);\r\n            const subdivided = path.getSubdividedPoints();\r\n\r\n            const isParentSelected = this._parentEvent?.isSelected();\r\n            const isWaveActive = this.isPreviewing && !this._wave!.isSpawnCompleted && this._wave!.waveElapsedTime > 0;\r\n            const color = isWaveActive ? Color.GREEN : isParentSelected ? Color.YELLOW : this._lineColor;\r\n            if (subdivided.length > 1) {\r\n                PathEditor.drawUniformPath(this._graphics!, subdivided, color, path.closed, 10);\r\n                const endPoint = subdivided[subdivided.length - 1];\r\n                let prevPoint = subdivided[subdivided.length - 2];\r\n                if (subdivided.length >= 5) {\r\n                    prevPoint = subdivided[subdivided.length - 5];\r\n                }\r\n                const direction = Math.atan2(endPoint.y - prevPoint.y, endPoint.x - prevPoint.x);\r\n                PathEditor.drawPathDirectionArrow(this._graphics!, endPoint.position, direction, path.closed);\r\n            }\r\n            // draw path points\r\n            this.drawPathPoints(path);\r\n        }\r\n    }\r\n\r\n    private drawPathPoints(path: PathData) {\r\n        if (!path.points || path.points.length === 0) return;\r\n\r\n        // 使用同一个 Graphics 绘制所有路径点\r\n        for (let i = 0; i < path.points.length; i++) {\r\n            const point = path.points[i];\r\n\r\n            PathEditor.drawPathPointAtPosition(\r\n                this._graphics!,\r\n                point,\r\n                point.x,\r\n                point.y,\r\n                false, // 不选中状态\r\n                15,    // 点大小稍小一些\r\n                i,\r\n                path.points.length,\r\n                path.startIdx,\r\n                path.endIdx,\r\n                false,\r\n                false\r\n            );\r\n        }\r\n    }\r\n\r\n    // 这里的wave时编辑器play时，用来动态创建小怪的wave。\r\n    private isPreviewing: boolean = false;\r\n\r\n    static activePlane: EnemyPlane[] = [];\r\n    static planePool: EnemyPlane[] = [];\r\n    static tickActivePlane(dt: number) {\r\n        WavePreview.activePlane.forEach((plane) => {\r\n            plane.moveCom!.tick(dt);\r\n        });\r\n    }\r\n\r\n    triggerPreview(posX: number, posY: number) {\r\n        console.log('WavePreview - triggerPreview: ', posX, posY, this._wave?.waveName);\r\n        if (!this._wave) {\r\n            return;\r\n        }\r\n\r\n        this._wave.setCreatePlaneDelegate(async (planeId: number, pos: Vec2, angle: number) => {\r\n            this.createDummyPlane(this._wave!, planeId, pos, angle);\r\n        });\r\n        this._wave.trigger(posX, posY);\r\n        this.isPreviewing = true;\r\n    }\r\n\r\n    tickPreview(dt: number) {\r\n        if (!this.isPreviewing) {\r\n            return;\r\n        }\r\n\r\n        const dtInMiliseconds = dt * 1000;\r\n        if (this._wave) {\r\n            this._wave.tick(dtInMiliseconds);\r\n        }\r\n    }\r\n\r\n    clearPreview() {\r\n        // console.log('WavePreview - clearPreview: ', this.activePlane.length);\r\n        this.isPreviewing = false;\r\n        \r\n        // just in case\r\n        this.node.removeAllChildren();\r\n    }\r\n\r\n    static clearPool() {\r\n        WavePreview.activePlane.forEach((plane) => {\r\n            plane.node.destroy();\r\n        });\r\n        WavePreview.planePool.forEach((plane) => {\r\n            plane.node.destroy();\r\n        });\r\n        WavePreview.planePool = [];\r\n    }\r\n\r\n    private createDummyPlane(wave: Wave, planeId: number, pos: Vec2, angle: number) {\r\n        // 对应\"assets/editor/level/prefab/dummy_plane\";\r\n        // console.log('WavePreview - createDummyPlane: ', planeId, pos, angle);\r\n        let plane: EnemyPlane|null = null;\r\n        if (WavePreview.planePool.length > 0) {\r\n            // 从对象池里拿一个dummy plane\r\n            plane = WavePreview.planePool.pop()!;\r\n            plane.node.active = true;\r\n            plane.initMove(pos.x, pos.y, angle);\r\n            if (wave.path) {\r\n                plane.initPath(pos.x, pos.y, wave.path);\r\n            } \r\n            // push when not in activePlane\r\n            if (!WavePreview.activePlane.includes(plane)) {\r\n                WavePreview.activePlane.push(plane);\r\n            }\r\n        }\r\n        else {\r\n            const dummy_plane_uuid: string = \"698c56c6-6603-4e69-abaf-421b721ef307\";\r\n            assetManager.loadAny({uuid:dummy_plane_uuid}, async (err, prefab:Prefab) => {\r\n                if (err) {\r\n                    console.error(\"WavePreview createDummyPlane load prefab err\", err);\r\n                    return;\r\n                }\r\n                try {\r\n                    // if (this.luban?.table == null) {\r\n                    //     await this.luban?.initInEditor();\r\n                    // }\r\n                    const canvas = this.node.scene.getChildByName(\"Canvas\")!;\r\n                    if (!canvas) {\r\n                        console.error(\"WavePreview createDummyPlane no canvas\");\r\n                        return;\r\n                    }\r\n                    const planeNode = instantiate(prefab);\r\n                    planeNode.hideFlags = CCObject.Flags.AllHideMasks;\r\n                    const plane = planeNode!.getComponent(EnemyPlane);\r\n                    if (plane) {\r\n                        canvas.addChild(planeNode);\r\n                        // this.node.parent!.addChild(planeNode);\r\n                        // const enemyData = new EnemyData(planeId, this.luban?.table.TbResEnemy.get(planeId));\r\n                        // const prefab = await LevelEditorUtils.loadByPath<Prefab>(enemyData.recoursePrefab);\r\n                        // plane.initPlane(enemyData, prefab!);\r\n                        plane.initMove(pos.x, pos.y, angle);\r\n                        plane.moveCom!.removeAllListeners();\r\n                        plane.moveCom!.on(eMoveEvent.onBecomeInvisible, () => {\r\n                            plane.node.active = false;\r\n                            plane.moveCom!.reset();\r\n                            WavePreview.activePlane = WavePreview.activePlane.filter((p) => p !== plane);\r\n                            WavePreview.planePool.push(plane);\r\n                        });\r\n                        if (wave.path) {\r\n                            plane.initPath(pos.x, pos.y, wave.path);\r\n                        } \r\n                        if (!WavePreview.activePlane.includes(plane)) {\r\n                            WavePreview.activePlane.push(plane);\r\n                        }\r\n                    } else {\r\n                        planeNode.destroy();\r\n                    }\r\n                }\r\n                catch (error) {\r\n                    console.error(\"WavePreview createDummyPlane err\", error);\r\n                }\r\n            });\r\n        }\r\n    }\r\n}"]}
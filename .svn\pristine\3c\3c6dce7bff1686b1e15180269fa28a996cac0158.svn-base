import { _decorator, instantiate, Label, Node, Prefab } from 'cc';
import { MyApp } from 'db://assets/bundles/common/script/app/MyApp';
import { BundleName } from 'db://assets/bundles/common/script/const/BundleConst';
import { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';
import { MessageBox } from 'db://assets/bundles/common/script/ui/common/components/messagebox/MessageBox';
import { BaseUI, UILayer, UIMgr, UIOpt } from 'db://assets/scripts/core/base/UIMgr';
import { DataEvent } from '../../event/DataEvent';
import { EventMgr } from '../../event/EventManager';
import { HomeUIEvent } from '../../event/HomeUIEvent';
import { PlaneUIEvent } from '../../event/PlaneUIEvent';
import { HomeUI } from '../home/<USER>';
import { Tabs } from '../plane/components/back_pack/Tabs';
import { TabStatus } from '../plane/PlaneTypes';

const { ccclass, property } = _decorator;

@ccclass('FriendUI')
export class FriendUI extends BaseUI {

    @property(ButtonPlus)
    btnClose: ButtonPlus | null = null;

    @property(Tabs)
    tabs: Tabs | null = null;

    @property(Node)
    panel1: Node | null = null;
    @property(Node)
    panel2: Node | null = null;
    @property(Node)
    panel3: Node | null = null;

    @property(Node)
    node1: Node | null = null;
    @property(ButtonPlus)
    btnGet: ButtonPlus | null = null;
    @property(Label)
    LabelTimes: Label | null = null;
    @property(Label)
    LabelUpdate: Label | null = null;

    @property(Node)
    node2: Node | null = null;
    @property(ButtonPlus)
    btnIgnoreAll: ButtonPlus | null = null;
    @property(ButtonPlus)
    btnAgreeAll: ButtonPlus | null = null;
    @property(ButtonPlus)
    btnRefreshAll: ButtonPlus | null = null;

    public static getUrl(): string { return "prefab/ui/FriendUI"; }
    public static getLayer(): UILayer { return UILayer.Default; }
    public static getBundleName(): string { return BundleName.HomeFriend; }
    public static getUIOption(): UIOpt { return { isClickBgCloseUI: true } }
    protected async onLoad(): Promise<void> {
        this.tabs!.init();
        this.updateLabelAfterColon(this.LabelTimes!, "50", "100");
        this.updateLabelAfterColon(this.tabs!.tabBagBtn!.getComponentInChildren(Label)!, "30", "100");
        this.updateLabelAfterColon(this.LabelUpdate!,
            ((timestamp: number) => {
                const date = new Date(timestamp);
                const hours = date.getHours().toString().padStart(2, '0');
                const minutes = date.getMinutes().toString().padStart(2, '0');
                return `${hours}时${minutes}分`;
            })(Date.now() + 3 * 60 * 60 * 1000)
        );
        this.btnClose!.addClick(this.closeUI, this);
        this.btnGet!.addClick(this.onPower, this);
        this.btnIgnoreAll!.addClick(this.onIgnore, this);
        this.btnAgreeAll!.addClick(this.onAgree, this);
        this.btnRefreshAll!.addClick(this.onRefresh, this);
        EventMgr.on(PlaneUIEvent.TabChange, this.onTabChange, this);

        this.panel2!.active = false;
        this.panel3!.active = false;
        this.node2!.active = false;

        let prefab = await MyApp.resMgr.loadAsync(BundleName.HomeFriend, "prefab/ui/FriendListUI", Prefab);
        this.panel1!.addChild(instantiate(prefab));
        prefab = await MyApp.resMgr.loadAsync(BundleName.HomeFriend, "prefab/ui/FriendAddUI", Prefab);
        this.panel2!.addChild(instantiate(prefab));
        prefab = await MyApp.resMgr.loadAsync(BundleName.HomeFriend, "prefab/ui/FriendStrangerUI", Prefab);
        this.panel3!.addChild(instantiate(prefab));
        EventMgr.once(HomeUIEvent.Leave, this.onLeave, this)
        EventMgr.on(DataEvent.FriendRefresh, this.onFriendRefresh, this)
    }
    private onLeave() {
        EventMgr.off(HomeUIEvent.Leave, this.onLeave, this)
        UIMgr.closeUI(FriendUI)
    }
    private onFriendRefresh() {
    }
    private onTabChange(tabStatus: TabStatus) {
        if (tabStatus == TabStatus.Bag) {
            this.node1!.active = true;
            this.node2!.active = false;
            this.panel1!.active = true;
            this.panel2!.active = false;
            this.panel3!.active = false;
        } else {
            this.node1!.active = false;
            this.node2!.active = true;
            this.panel1!.active = false;
            this.panel2!.active = true;
            this.panel3!.active = true;
        }
    }
    public updateLabelAfterColon(label: Label, ...args: string[]): void {
        const originalText = label.string;
        let colonIndex = originalText.indexOf(":");
        if (colonIndex === -1) {
            colonIndex = originalText.indexOf("："); // 中文冒号
        }
        let formattedValue: string;
        if (args.length === 1) {
            formattedValue = args[0];
        } else if (args.length === 2) {
            formattedValue = `${args[0]}/${args[1]}`;
        } else if (args.length > 2) {
            formattedValue = args.join(",");
        } else {
            formattedValue = "";
        }
        if (colonIndex === -1) {
            label.string = `${originalText}:${formattedValue}`;
            return;
        }
        const prefix = originalText.substring(0, colonIndex + 1); // 包含冒号
        label.string = `${prefix}${formattedValue}`;
    }

    async closeUI() {
        UIMgr.closeUI(FriendUI);
        await UIMgr.openUI(HomeUI)
    }
    private onPower() {
    }
    private onIgnore() {
    }
    private onAgree() {
    }
    private onRefresh() {
    }

    async onShow(): Promise<void> {

    }
    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {
    }
    protected onDestroy(): void {
        EventMgr.targetOff(this);
    }
}
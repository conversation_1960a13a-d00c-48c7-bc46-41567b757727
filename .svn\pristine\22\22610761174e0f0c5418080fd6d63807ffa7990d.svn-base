import { _decorator, Node, Color, Component, JsonAsset, CCInteger, Graphics, Vec2, Vec3 } from 'cc';
const { ccclass, executeInEditMode, property, disallowMultiple, menu, requireComponent } = _decorator;
import { EDITOR } from 'cc/env';
import { PathData, PathPoint } from 'db://assets/bundles/common/script/game/data/PathData';
import { PathPointEditor } from './PathPointEditor';
import { eOrientationType } from 'db://assets/bundles/common/script/game/move/IMovable';

@ccclass('PathEditor')
@menu("怪物/编辑器/路径编辑")
@requireComponent(Graphics)
@executeInEditMode(true)
@disallowMultiple(true)
export class PathEditor extends Component {
    private _graphics: Graphics | null = null;
    public get graphics(): Graphics {
        if (!this._graphics) {
            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);
        }
        return this._graphics;
    }

    @property({ type: JsonAsset, displayName: "路径数据" })
    public set pathData(value: JsonAsset) {
        this._pathData = value;
        this.reload();
    }
    public get pathData(): JsonAsset | null {
        return this._pathData;
    }

    @property({ displayName: "路径名称" })
    public get pathName(): string {
        return this._pathDataObj.name;
    }
    public set pathName(value: string) {
        this._pathDataObj.name = value;
    }

    @property({ type: CCInteger, displayName: '起始点'})
    public get startIdx() {
        return this._pathDataObj.startIdx;
    }
    public set startIdx(value: number) {
        this._pathDataObj.startIdx = value;
    }

    @property({ type: CCInteger, displayName: '结束点(-1代表默认最后个点)'})
    public get endIdx() {
        return this._pathDataObj.endIdx;
    }
    public set endIdx(value: number) {
        this._pathDataObj.endIdx = value;
    }

    @property({ displayName: "是否闭合", visible() {
        // @ts-ignore
        return this._pathDataObj.points.length >= 3;
    }})
    public get isClosed(): boolean {
        return this._pathDataObj.closed;
    }
    public set isClosed(value: boolean) {
        this._pathDataObj.closed = value;
    }

    @property({ displayName: "曲线颜色" })
    public curveColor: Color = Color.WHITE;

    @property({ displayName: "显示细分线段" })
    public showSegments: boolean = false; // 是否使用不同颜色来绘制不同的细分线段

    private _showDirectionArrow: boolean = true;
    private _pathData: JsonAsset | null = null;
    private _pathDataObj: PathData = new PathData();
    private _cachedChildrenCount: number = 0;

    public reload() {
        if (!this._pathData) return;

        const pathData = PathData.fromJSON(this._pathData.json);
        this._pathDataObj = pathData;

        this.node.removeAllChildren();
        if (this._pathDataObj && this._pathDataObj.points.length > 0) {
            this._pathDataObj.points.forEach((point:PathPoint) => {
                this.addPoint(point);
            });
        }
    }

    public save(): string {
        // 收集所有路径点数据
        const pointEditors = this.getComponentsInChildren(PathPointEditor);
        this._pathDataObj.points = pointEditors.map((editor) => editor.pathPoint);
        return JSON.stringify(this._pathDataObj, null, 2);
    }

    public addPoint(point: PathPoint) {
        const pointNode = new Node();
        pointNode.parent = this.node;
        pointNode.setPosition(point.x, point.y, 0);

        const pointEditor = pointNode.addComponent(PathPointEditor);
        pointEditor.pathPoint = point;
    }

    public addNewPoint(x: number, y: number) {
        const point = new PathPoint(x, y);
        this.addPoint(point);
        this.updateCurve();
    }

    public updateCurve() {
        // 收集当前所有点的数据
        const pointEditors = this.getComponentsInChildren(PathPointEditor);
        this._pathDataObj.points = pointEditors.map((editor) => editor.pathPoint);
    }

    public update(_dt: number) {
        const childrenCount = this.node.children.length;
        if (childrenCount !== this._cachedChildrenCount) {
            this._cachedChildrenCount = childrenCount;
        }

        // 把自己的位置锁定在原点，避免出现绘制偏移
        this.node.position = Vec3.ZERO;
        this.updateCurve();
        this.drawPath();
    }

    private drawPath() {
        const graphics = this.graphics;
        graphics.clear();
        
        // console.log(`drawPath points length: `, this._pathDataObj.points.length);
        if (this._pathDataObj.points.length < 2) return;

        const subdivided = this._pathDataObj.getSubdividedPoints(true);
        if (subdivided.length > 1) {
            if (this.showSegments) {
                PathEditor.drawSegmentedPath(graphics, subdivided, this._pathDataObj.closed, 8);
            } else {
                PathEditor.drawUniformPath(graphics, subdivided, this.curveColor, this._pathDataObj.closed, 8);
            }

            // 绘制路径终点的方向箭头（仅对非闭合路径）
            if (this._showDirectionArrow && !this._pathDataObj.closed) {
                const endPoint = subdivided[subdivided.length - 1];
                let prevPoint = subdivided[subdivided.length - 2];
                if (subdivided.length >= 5) {
                    prevPoint = subdivided[subdivided.length - 5];
                }
                const direction = Math.atan2(endPoint.y - prevPoint.y, endPoint.x - prevPoint.x);
                PathEditor.drawPathDirectionArrow(graphics, endPoint.position, direction, this._pathDataObj.closed);
            }
        }
        
        if (this.showSegments) {
            console.log('subdivided points length: ', subdivided.length);
        }
    }

    /**
     * 绘制统一颜色的路径
     */
    static drawUniformPath(graphics: Graphics, subdivided: PathPoint[], color: Color, closed: boolean, width:number = 5) {
        graphics.strokeColor = color;
        graphics.lineWidth = width;

        graphics.moveTo(subdivided[0].x, subdivided[0].y);
        for (let i = 1; i < subdivided.length; i++) {
            graphics.lineTo(subdivided[i].x, subdivided[i].y);
        }

        // 如果是闭合路径，连接回起点
        if (closed) {
            graphics.lineTo(subdivided[0].x, subdivided[0].y);
        }

        graphics.stroke();
    }

    /**
     * 绘制分段着色的路径 - 每个细分段用不同颜色
     */
    static drawSegmentedPath(graphics: Graphics, subdivided: PathPoint[], closed: boolean, width:number = 5) {
        graphics.lineWidth = width;

        if (subdivided.length < 2) return;

        // 为每个细分段绘制不同的颜色
        for (let i = 0; i < subdivided.length - 1; i++) {
            const t = i / (subdivided.length - 1); // 计算当前位置的归一化参数 [0,1]

            // 从绿色到红色的颜色插值
            const color = this.interpolateColor(Color.GREEN, Color.RED, t);
            graphics.strokeColor = color;

            // 绘制当前段
            graphics.moveTo(subdivided[i].x, subdivided[i].y);
            graphics.lineTo(subdivided[i + 1].x, subdivided[i + 1].y);
            graphics.stroke();

            // draw arrow head
            const direction = Math.atan2(subdivided[i + 1].y - subdivided[i].y, subdivided[i + 1].x - subdivided[i].x);
            PathEditor.drawPathDirectionArrow(graphics, subdivided[i + 1].position, direction, closed);
        }

        // 如果是闭合路径，绘制最后一段回到起点
        if (closed && subdivided.length > 2) {
            const lastColor = this.interpolateColor(Color.GREEN, Color.RED, 1.0);
            graphics.strokeColor = lastColor;
            graphics.moveTo(subdivided[subdivided.length - 1].x, subdivided[subdivided.length - 1].y);
            graphics.lineTo(subdivided[0].x, subdivided[0].y);
            graphics.stroke();
        }
    }

    /**
     * 颜色插值函数
     * @param color1 起始颜色
     * @param color2 结束颜色
     * @param t 插值参数 [0,1]
     */
    static interpolateColor(color1: Color, color2: Color, t: number): Color {
        t = Math.max(0, Math.min(1, t)); // 确保t在[0,1]范围内

        const r = color1.r + (color2.r - color1.r) * t;
        const g = color1.g + (color2.g - color1.g) * t;
        const b = color1.b + (color2.b - color1.b) * t;
        const a = color1.a + (color2.a - color1.a) * t;

        return new Color(r, g, b, a);
    }

    /**
     * 绘制路径方向箭头
     */
    static drawPathDirectionArrow(graphics: Graphics, position: Vec2, direction: number, closed: boolean, length: number = 30, width: number = 5) {
        // 如果是闭合路径，不绘制箭头（因为没有明确的终点）
        if (closed) return;

        // 箭头参数
        const arrowHeadLength = length;
        const arrowHeadAngle = Math.PI / 6; // 30度，适中的角度

        // 设置箭头样式
        graphics.strokeColor = Color.RED;
        graphics.lineWidth = width;

        // 计算箭头起点（从路径终点开始）
        const arrowStartX = position.x;
        const arrowStartY = position.y;

        // 计算箭头两条线段的端点
        const leftX = arrowStartX - Math.cos(direction - arrowHeadAngle) * arrowHeadLength;
        const leftY = arrowStartY - Math.sin(direction - arrowHeadAngle) * arrowHeadLength;
        const rightX = arrowStartX - Math.cos(direction + arrowHeadAngle) * arrowHeadLength;
        const rightY = arrowStartY - Math.sin(direction + arrowHeadAngle) * arrowHeadLength;

        // 绘制箭头的两条线段（">"形状）
        // 第一条线段：从箭头尖端到左侧
        graphics.moveTo(arrowStartX, arrowStartY);
        graphics.lineTo(leftX, leftY);
        graphics.stroke();

        // 第二条线段：从箭头尖端到右侧
        graphics.moveTo(arrowStartX, arrowStartY);
        graphics.lineTo(rightX, rightY);
        graphics.stroke();
    }

    /**
     * 绘制路径点（在节点本地坐标系中，用于 PathPointEditor）
     */
    static drawPathPoint(graphics: Graphics, pathPoint: PathPoint, selected: boolean, pointSize: number = 20, index: number, totalCount: number, startIdx: number = 0, endIdx: number = -1, siblings?: Node[], clearGraphics: boolean = true) {
        if (clearGraphics) {
            graphics.clear();
        }

        // 绘制点（在本地坐标系中心）
        const color = selected ? Color.YELLOW : this.getDefaultColorByIndex(index, totalCount, startIdx, endIdx);
        graphics.fillColor = color;
        graphics.strokeColor = Color.BLACK;
        graphics.lineWidth = 5;

        graphics.circle(0, 0, pointSize);
        graphics.fill();
        graphics.stroke();

        // 绘制平滑程度指示器
        if (pathPoint.smoothness > 0) {
            graphics.strokeColor = Color.GREEN;
            graphics.lineWidth = 5;
            const radius = pointSize + 5 + pathPoint.smoothness * 10;
            graphics.circle(0, 0, radius);
            graphics.stroke();
        }

        // 绘制朝向指示器（箭头）
        if (pathPoint.speed > 0) {
            this.drawOrientationArrow(graphics, pathPoint, siblings || null, index);
        }
    }

    /**
     * 在指定位置绘制路径点（用于 WavePreview 等需要在统一 Graphics 上绘制多个点的场景）
     */
    static drawPathPointAtPosition(graphics: Graphics, pathPoint: PathPoint, x: number, y: number, selected: boolean, pointSize: number = 20, index: number, totalCount: number, startIdx: number = 0, endIdx: number = -1) {
        // 绘制点
        const color = selected ? Color.YELLOW : this.getDefaultColorByIndex(index, totalCount, startIdx, endIdx);
        graphics.fillColor = color;
        graphics.strokeColor = Color.BLACK;
        graphics.lineWidth = 5;

        graphics.circle(x, y, pointSize);
        graphics.fill();
        graphics.stroke();

        // 绘制平滑程度指示器
        if (pathPoint.smoothness > 0) {
            graphics.strokeColor = Color.GREEN;
            graphics.lineWidth = 5;
            const radius = pointSize + 5 + pathPoint.smoothness * 10;
            graphics.circle(x, y, radius);
            graphics.stroke();
        }

        // 绘制朝向指示器（箭头）
        if (pathPoint.speed > 0) {
            this.drawOrientationArrowAtPosition(graphics, pathPoint, null, index, x, y);
        }
    }

    /**
     * 在指定位置绘制朝向箭头
     */
    static drawOrientationArrowAtPosition(graphics: Graphics, pathPoint: PathPoint, siblings: Node[] | null, currentIndex: number, x: number, y: number) {
        const arrowLength = Math.min(pathPoint.speed / 10, 100);
        const arrowAngle = this.calculateArrowAngle(pathPoint, siblings, currentIndex);

        // 根据朝向类型设置不同颜色
        graphics.strokeColor = this.getArrowColorByOrientationType(pathPoint.orientationType);
        graphics.lineWidth = 3;

        // 计算箭头终点
        const endX = x + Math.cos(arrowAngle) * arrowLength;
        const endY = y + Math.sin(arrowAngle) * arrowLength;

        // 绘制箭头主线
        graphics.moveTo(x, y);
        graphics.lineTo(endX, endY);

        // 绘制箭头头部
        const arrowHeadLength = 8;
        const arrowHeadAngle = Math.PI / 6; // 30度

        const leftX = endX - Math.cos(arrowAngle - arrowHeadAngle) * arrowHeadLength;
        const leftY = endY - Math.sin(arrowAngle - arrowHeadAngle) * arrowHeadLength;
        const rightX = endX - Math.cos(arrowAngle + arrowHeadAngle) * arrowHeadLength;
        const rightY = endY - Math.sin(arrowAngle + arrowHeadAngle) * arrowHeadLength;

        graphics.moveTo(leftX, leftY);
        graphics.lineTo(endX, endY);
        graphics.lineTo(rightX, rightY);

        graphics.stroke();
    }

    /**
     * 根据索引获取默认颜色
     */
    static getDefaultColorByIndex(index: number, count: number, startIdx: number = 0, endIdx: number = -1): Color {
        if (endIdx === -1) endIdx = count - 1;

        // 起点
        if (index === startIdx) return Color.GREEN;
        // 终点
        else if (index === endIdx) return Color.RED;
        // 中间点
        else return Color.WHITE;
    }

    /**
     * 绘制朝向箭头
     */
    static drawOrientationArrow(graphics: Graphics, pathPoint: PathPoint, siblings: Node[] | null, currentIndex?: number) {
        const arrowLength = Math.min(pathPoint.speed / 10, 100);
        const arrowAngle = this.calculateArrowAngle(pathPoint, siblings, currentIndex);

        // 根据朝向类型设置不同颜色
        graphics.strokeColor = this.getArrowColorByOrientationType(pathPoint.orientationType);
        graphics.lineWidth = 3;

        // 计算箭头终点
        const endX = Math.cos(arrowAngle) * arrowLength;
        const endY = Math.sin(arrowAngle) * arrowLength;

        // 绘制箭头主线
        graphics.moveTo(0, 0);
        graphics.lineTo(endX, endY);

        // 绘制箭头头部
        const arrowHeadLength = 8;
        const arrowHeadAngle = Math.PI / 6; // 30度

        const leftX = endX - Math.cos(arrowAngle - arrowHeadAngle) * arrowHeadLength;
        const leftY = endY - Math.sin(arrowAngle - arrowHeadAngle) * arrowHeadLength;
        const rightX = endX - Math.cos(arrowAngle + arrowHeadAngle) * arrowHeadLength;
        const rightY = endY - Math.sin(arrowAngle + arrowHeadAngle) * arrowHeadLength;

        graphics.moveTo(leftX, leftY);
        graphics.lineTo(endX, endY);
        graphics.lineTo(rightX, rightY);

        graphics.stroke();
    }

    /**
     * 根据朝向类型计算箭头角度
     */
    static calculateArrowAngle(pathPoint: PathPoint, siblings: Node[] | null, currentIndex?: number): number {
        switch (pathPoint.orientationType) {
            case eOrientationType.Path:
                return this.calculateMovementDirection(pathPoint, siblings, currentIndex);

            case eOrientationType.Target:
                return this.calculatePlayerDirection(pathPoint);

            case eOrientationType.Fixed:
                // orientationParam作为固定角度（度）
                return (pathPoint.orientationParam * Math.PI) / 180;

            default:
                return 0; // 默认向右
        }
    }

    /**
     * 计算移动方向
     */
    static calculateMovementDirection(pathPoint: PathPoint, siblings: Node[] | null, currentIndex?: number): number {
        if (!siblings || siblings.length <= 1) return 0;

        // 如果没有提供currentIndex，尝试找到当前点在siblings中的索引
        if (currentIndex === undefined) {
            currentIndex = -1;
            for (let i = 0; i < siblings.length; i++) {
                const pointEditor = siblings[i].getComponent(PathPointEditor);
                if (pointEditor && pointEditor.pathPoint === pathPoint) {
                    currentIndex = i;
                    break;
                }
            }
        }

        if (currentIndex === -1 || currentIndex === undefined) return 0;

        // 如果是第一个点，使用到下一个点的方向
        if (currentIndex === 0 && siblings.length > 1) {
            const nextPoint = siblings[1].position;
            const currentPoint = siblings[currentIndex].position;
            return Math.atan2(nextPoint.y - currentPoint.y, nextPoint.x - currentPoint.x);
        }
        // 如果是最后一个点，使用从上一个点的方向
        else if (currentIndex === siblings.length - 1 && siblings.length > 1) {
            const prevPoint = siblings[currentIndex - 1].position;
            const currentPoint = siblings[currentIndex].position;
            return Math.atan2(currentPoint.y - prevPoint.y, currentPoint.x - prevPoint.x);
        }
        // 中间点，使用前后两点的平均方向
        else if (currentIndex > 0 && currentIndex < siblings.length - 1) {
            const prevPoint = siblings[currentIndex - 1].position;
            const nextPoint = siblings[currentIndex + 1].position;
            return Math.atan2(nextPoint.y - prevPoint.y, nextPoint.x - prevPoint.x);
        }

        return 0; // 默认向右
    }

    /**
     * 计算朝向玩家的方向
     */
    static calculatePlayerDirection(pathPoint: PathPoint): number {
        // 假设玩家在屏幕底部中央 (0, -400)
        // 在实际游戏中，这应该从游戏状态获取玩家位置
        const playerX = 0;
        const playerY = -400;

        return Math.atan2(playerY - pathPoint.y, playerX - pathPoint.x);
    }

    /**
     * 根据朝向类型获取箭头颜色
     */
    static getArrowColorByOrientationType(orientationType: number): Color {
        switch (orientationType) {
            case eOrientationType.Path:
                return Color.BLUE;      // 蓝色：跟随移动方向

            case eOrientationType.Target:
                return Color.MAGENTA;   // 紫色：朝向玩家

            case eOrientationType.Fixed:
                return new Color(255, 165, 0, 255);  // 橙色：固定朝向

            default:
                return Color.BLUE;      // 默认蓝色
        }
    }
}
{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendUI.ts"], "names": ["_decorator", "Label", "Node", "BundleName", "ButtonPlus", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "DataEvent", "EventMgr", "HomeUIEvent", "UITools", "TabPanel", "HomeUI", "FriendAddUI", "FriendListUI", "FriendStrangerUI", "ccclass", "property", "MODE_LIST", "MODE_ADD", "FriendUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getBundleName", "HomeFriend", "getUIOption", "isClickBgCloseUI", "onLoad", "modifyNumber", "LabelTimes", "LabelFriendNum", "LabelUpdate", "string", "formatTime", "btnClose", "addClick", "closeUI", "btnGet", "onPower", "btnIgnoreAll", "onIgnore", "btnAgreeAll", "onAgree", "btnRefreshAll", "onRefresh", "panelAdd", "active", "nodeAdd", "tabPanel", "addTabFun", "idx", "nodeList", "panelList", "once", "Leave", "onLeave", "on", "FriendRefresh", "onFriendRefresh", "off", "openUI", "onShow", "onHide", "onClose", "onDestroy", "targetOff"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;;AACnBC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,M,kBAAAA,M;;AACAC,MAAAA,W,kBAAAA,W;;AACAC,MAAAA,Y,kBAAAA,Y;;AACAC,MAAAA,gB,kBAAAA,gB;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBlB,U;AAExBmB,MAAAA,S,GAAY,C;AACZC,MAAAA,Q,GAAW,C;;0BAGJC,Q,WADZJ,OAAO,CAAC,UAAD,C,UAGHC,QAAQ;AAAA;AAAA,mC,UAGRA,QAAQ;AAAA;AAAA,+B,UAGRA,QAAQ,CAAChB,IAAD,C,UAERgB,QAAQ,CAAChB,IAAD,C,UAGRgB,QAAQ,CAAChB,IAAD,C,UAERgB,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ,CAACjB,KAAD,C,UAERiB,QAAQ,CAACjB,KAAD,C,WAGRiB,QAAQ,CAACjB,KAAD,C,WAGRiB,QAAQ,CAAChB,IAAD,C,WAERgB,QAAQ;AAAA;AAAA,mC,WAERA,QAAQ;AAAA;AAAA,mC,WAERA,QAAQ;AAAA;AAAA,mC,WAGRA,QAAQ;AAAA;AAAA,uC,WAERA,QAAQ;AAAA;AAAA,qC,WAERA,QAAQ;AAAA;AAAA,+C,2BAvCb,MACaG,QADb;AAAA;AAAA,4BACqC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAyCb,eAANC,MAAM,GAAW;AAAE,iBAAO,oBAAP;AAA8B;;AACzC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAyB;;AAClC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,UAAlB;AAA+B;;AAC9C,eAAXC,WAAW,GAAU;AAAE,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AAAmC;;AAClD,cAANC,MAAM,GAAkB;AACpC;AAAA;AAAA,kCAAQC,YAAR,CAAqB,KAAKC,UAA1B,EAAuC,IAAvC,EAA6C,KAA7C;AACA;AAAA;AAAA,kCAAQD,YAAR,CAAqB,KAAKE,cAA1B,EAA2C,IAA3C,EAAiD,KAAjD;AACA,eAAKC,WAAL,CAAkBC,MAAlB,GAA2B,UAAU;AAAA;AAAA,kCAAQC,UAAR,CAAmB,IAAI,EAAJ,GAAS,EAA5B,EAAgC,IAAhC,CAArC;AAEA,eAAKC,QAAL,CAAeC,QAAf,CAAwB,KAAKC,OAA7B,EAAsC,IAAtC;AACA,eAAKC,MAAL,CAAaF,QAAb,CAAsB,KAAKG,OAA3B,EAAoC,IAApC;AACA,eAAKC,YAAL,CAAmBJ,QAAnB,CAA4B,KAAKK,QAAjC,EAA2C,IAA3C;AACA,eAAKC,WAAL,CAAkBN,QAAlB,CAA2B,KAAKO,OAAhC,EAAyC,IAAzC;AACA,eAAKC,aAAL,CAAoBR,QAApB,CAA6B,KAAKS,SAAlC,EAA6C,IAA7C;AAEA,eAAKC,QAAL,CAAeC,MAAf,GAAwB,KAAxB;AACA,eAAKC,OAAL,CAAcD,MAAd,GAAuB,KAAvB;AACA,eAAKE,QAAL,CAAeC,SAAf,CAA0BC,GAAD,IAAiB;AACtC,iBAAKC,QAAL,CAAeL,MAAf,GAAwBI,GAAG,IAAIjC,SAA/B;AACA,iBAAK8B,OAAL,CAAcD,MAAd,GAAuBI,GAAG,IAAIhC,QAA9B;AACA,iBAAKkC,SAAL,CAAgBN,MAAhB,GAAyBI,GAAG,IAAIjC,SAAhC;AACA,iBAAK4B,QAAL,CAAeC,MAAf,GAAwBI,GAAG,IAAIhC,QAA/B;AACH,WALD;AAOA;AAAA;AAAA,oCAASmC,IAAT,CAAc;AAAA;AAAA,0CAAYC,KAA1B,EAAiC,KAAKC,OAAtC,EAA+C,IAA/C;AACA;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,sCAAUC,aAAtB,EAAqC,KAAKC,eAA1C,EAA2D,IAA3D;AACH;;AACOH,QAAAA,OAAO,GAAG;AACd;AAAA;AAAA,oCAASI,GAAT,CAAa;AAAA;AAAA,0CAAYL,KAAzB,EAAgC,KAAKC,OAArC,EAA8C,IAA9C;AACA;AAAA;AAAA,8BAAMnB,OAAN,CAAcjB,QAAd;AACH;;AACOuC,QAAAA,eAAe,GAAG,CACzB;;AAEY,cAAPtB,OAAO,GAAG;AACZ;AAAA;AAAA,8BAAMA,OAAN,CAAcjB,QAAd;AACA,gBAAM;AAAA;AAAA,8BAAMyC,MAAN;AAAA;AAAA,+BAAN;AACH;;AACOtB,QAAAA,OAAO,GAAG,CACjB;;AACOE,QAAAA,QAAQ,GAAG,CAClB;;AACOE,QAAAA,OAAO,GAAG,CACjB;;AACOE,QAAAA,SAAS,GAAG,CACnB;;AAEW,cAANiB,MAAM,GAAkB,CAE7B;;AACW,cAANC,MAAM,GAAkB,CAC7B;;AACY,cAAPC,OAAO,GAAkB,CAC9B;;AACSC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAjGgC,O;;;;;iBAGH,I;;;;;;;iBAGF,I;;;;;;;iBAGH,I;;;;;;;iBAED,I;;;;;;;iBAGA,I;;;;;;;iBAEI,I;;;;;;;iBAED,I;;;;;;;iBAEC,I;;;;;;;iBAGG,I;;;;;;;iBAGR,I;;;;;;;iBAEW,I;;;;;;;iBAED,I;;;;;;;iBAEE,I;;;;;;;iBAGC,I;;;;;;;iBAEF,I;;;;;;;iBAEU,I", "sourcesContent": ["import { _decorator, Label, Node } from 'cc';\r\nimport { BundleName } from 'db://assets/bundles/common/script/const/BundleConst';\r\nimport { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';\r\nimport { BaseUI, UILayer, UIMgr, UIOpt } from 'db://assets/scripts/core/base/UIMgr';\r\nimport { DataEvent } from '../../event/DataEvent';\r\nimport { EventMgr } from '../../event/EventManager';\r\nimport { HomeUIEvent } from '../../event/HomeUIEvent';\r\nimport { UITools } from '../../game/utils/UITools';\r\nimport { TabPanel } from '../common/components/base/TabPanel';\r\nimport { HomeUI } from '../home/<USER>';\r\nimport { FriendAddUI } from './FriendAddUI';\r\nimport { FriendListUI } from './FriendListUI';\r\nimport { FriendStrangerUI } from './FriendStrangerUI';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\nconst MODE_LIST = 0;\r\nconst MODE_ADD = 1;\r\n\r\n@ccclass('FriendUI')\r\nexport class FriendUI extends BaseUI {\r\n\r\n    @property(ButtonPlus)\r\n    btnClose: ButtonPlus | null = null;\r\n\r\n    @property(TabPanel)\r\n    tabPanel: TabPanel | null = null;\r\n\r\n    @property(Node)\r\n    panelList: Node | null = null;\r\n    @property(Node)\r\n    panelAdd: Node | null = null;\r\n\r\n    @property(Node)\r\n    nodeList: Node | null = null;\r\n    @property(ButtonPlus)\r\n    btnGet: ButtonPlus | null = null;\r\n    @property(Label)\r\n    LabelTimes: Label | null = null;\r\n    @property(Label)\r\n    LabelUpdate: Label | null = null;\r\n\r\n    @property(Label)\r\n    LabelFriendNum: Label | null = null;\r\n\r\n    @property(Node)\r\n    nodeAdd: Node | null = null;\r\n    @property(ButtonPlus)\r\n    btnIgnoreAll: ButtonPlus | null = null;\r\n    @property(ButtonPlus)\r\n    btnAgreeAll: ButtonPlus | null = null;\r\n    @property(ButtonPlus)\r\n    btnRefreshAll: ButtonPlus | null = null;\r\n\r\n    @property(FriendListUI)\r\n    friendListUI: FriendListUI | null = null;\r\n    @property(FriendAddUI)\r\n    friendAddUI: FriendAddUI | null = null;\r\n    @property(FriendStrangerUI)\r\n    friendStrangerUI: FriendStrangerUI | null = null;\r\n\r\n    public static getUrl(): string { return \"prefab/ui/FriendUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Default; }\r\n    public static getBundleName(): string { return BundleName.HomeFriend; }\r\n    public static getUIOption(): UIOpt { return { isClickBgCloseUI: true } }\r\n    protected async onLoad(): Promise<void> {\r\n        UITools.modifyNumber(this.LabelTimes!, \"50\", \"100\");\r\n        UITools.modifyNumber(this.LabelFriendNum!, \"30\", \"100\");\r\n        this.LabelUpdate!.string = \"刷新时间：\" + UITools.formatTime(3 * 60 * 60, true);\r\n\r\n        this.btnClose!.addClick(this.closeUI, this);\r\n        this.btnGet!.addClick(this.onPower, this);\r\n        this.btnIgnoreAll!.addClick(this.onIgnore, this);\r\n        this.btnAgreeAll!.addClick(this.onAgree, this);\r\n        this.btnRefreshAll!.addClick(this.onRefresh, this);\r\n\r\n        this.panelAdd!.active = false;\r\n        this.nodeAdd!.active = false;\r\n        this.tabPanel!.addTabFun((idx: number) => {\r\n            this.nodeList!.active = idx == MODE_LIST;\r\n            this.nodeAdd!.active = idx == MODE_ADD;\r\n            this.panelList!.active = idx == MODE_LIST;\r\n            this.panelAdd!.active = idx == MODE_ADD;\r\n        });\r\n\r\n        EventMgr.once(HomeUIEvent.Leave, this.onLeave, this)\r\n        EventMgr.on(DataEvent.FriendRefresh, this.onFriendRefresh, this)\r\n    }\r\n    private onLeave() {\r\n        EventMgr.off(HomeUIEvent.Leave, this.onLeave, this)\r\n        UIMgr.closeUI(FriendUI)\r\n    }\r\n    private onFriendRefresh() {\r\n    }\r\n\r\n    async closeUI() {\r\n        UIMgr.closeUI(FriendUI);\r\n        await UIMgr.openUI(HomeUI)\r\n    }\r\n    private onPower() {\r\n    }\r\n    private onIgnore() {\r\n    }\r\n    private onAgree() {\r\n    }\r\n    private onRefresh() {\r\n    }\r\n\r\n    async onShow(): Promise<void> {\r\n\r\n    }\r\n    async onHide(): Promise<void> {\r\n    }\r\n    async onClose(): Promise<void> {\r\n    }\r\n    protected onDestroy(): void {\r\n        EventMgr.targetOff(this);\r\n    }\r\n}"]}
System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10", "__unresolved_11", "__unresolved_12"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Label, Node, BundleName, ButtonPlus, BaseUI, UILayer, UIMgr, DataEvent, EventMgr, HomeUIEvent, UITools, TabPanel, HomeUI, FriendAddUI, FriendListUI, FriendStrangerUI, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _dec10, _dec11, _dec12, _dec13, _dec14, _dec15, _dec16, _dec17, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _descriptor9, _descriptor10, _descriptor11, _descriptor12, _descriptor13, _descriptor14, _descriptor15, _descriptor16, _crd, ccclass, property, MODE_LIST, MODE_ADD, FriendUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "db://assets/bundles/common/script/const/BundleConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "db://assets/bundles/common/script/ui/common/components/button/ButtonPlus", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIOpt(extras) {
    _reporterNs.report("UIOpt", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataEvent(extras) {
    _reporterNs.report("DataEvent", "../../event/DataEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "../../event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfHomeUIEvent(extras) {
    _reporterNs.report("HomeUIEvent", "../../event/HomeUIEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUITools(extras) {
    _reporterNs.report("UITools", "../../game/utils/UITools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTabPanel(extras) {
    _reporterNs.report("TabPanel", "../common/components/base/TabPanel", _context.meta, extras);
  }

  function _reportPossibleCrUseOfHomeUI(extras) {
    _reporterNs.report("HomeUI", "../home/<USER>", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFriendAddUI(extras) {
    _reporterNs.report("FriendAddUI", "./FriendAddUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFriendListUI(extras) {
    _reporterNs.report("FriendListUI", "./FriendListUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFriendStrangerUI(extras) {
    _reporterNs.report("FriendStrangerUI", "./FriendStrangerUI", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Label = _cc.Label;
      Node = _cc.Node;
    }, function (_unresolved_2) {
      BundleName = _unresolved_2.BundleName;
    }, function (_unresolved_3) {
      ButtonPlus = _unresolved_3.ButtonPlus;
    }, function (_unresolved_4) {
      BaseUI = _unresolved_4.BaseUI;
      UILayer = _unresolved_4.UILayer;
      UIMgr = _unresolved_4.UIMgr;
    }, function (_unresolved_5) {
      DataEvent = _unresolved_5.DataEvent;
    }, function (_unresolved_6) {
      EventMgr = _unresolved_6.EventMgr;
    }, function (_unresolved_7) {
      HomeUIEvent = _unresolved_7.HomeUIEvent;
    }, function (_unresolved_8) {
      UITools = _unresolved_8.UITools;
    }, function (_unresolved_9) {
      TabPanel = _unresolved_9.TabPanel;
    }, function (_unresolved_10) {
      HomeUI = _unresolved_10.HomeUI;
    }, function (_unresolved_11) {
      FriendAddUI = _unresolved_11.FriendAddUI;
    }, function (_unresolved_12) {
      FriendListUI = _unresolved_12.FriendListUI;
    }, function (_unresolved_13) {
      FriendStrangerUI = _unresolved_13.FriendStrangerUI;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "b1c613MdwlKAaFgsno8SWZR", "FriendUI", undefined);

      __checkObsolete__(['_decorator', 'Label', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);
      MODE_LIST = 0;
      MODE_ADD = 1;

      _export("FriendUI", FriendUI = (_dec = ccclass('FriendUI'), _dec2 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec3 = property(_crd && TabPanel === void 0 ? (_reportPossibleCrUseOfTabPanel({
        error: Error()
      }), TabPanel) : TabPanel), _dec4 = property(Node), _dec5 = property(Node), _dec6 = property(Node), _dec7 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec8 = property(Label), _dec9 = property(Label), _dec10 = property(Label), _dec11 = property(Node), _dec12 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec13 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec14 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec15 = property(_crd && FriendListUI === void 0 ? (_reportPossibleCrUseOfFriendListUI({
        error: Error()
      }), FriendListUI) : FriendListUI), _dec16 = property(_crd && FriendAddUI === void 0 ? (_reportPossibleCrUseOfFriendAddUI({
        error: Error()
      }), FriendAddUI) : FriendAddUI), _dec17 = property(_crd && FriendStrangerUI === void 0 ? (_reportPossibleCrUseOfFriendStrangerUI({
        error: Error()
      }), FriendStrangerUI) : FriendStrangerUI), _dec(_class = (_class2 = class FriendUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "btnClose", _descriptor, this);

          _initializerDefineProperty(this, "tabPanel", _descriptor2, this);

          _initializerDefineProperty(this, "panelList", _descriptor3, this);

          _initializerDefineProperty(this, "panelAdd", _descriptor4, this);

          _initializerDefineProperty(this, "nodeList", _descriptor5, this);

          _initializerDefineProperty(this, "btnGet", _descriptor6, this);

          _initializerDefineProperty(this, "LabelTimes", _descriptor7, this);

          _initializerDefineProperty(this, "LabelUpdate", _descriptor8, this);

          _initializerDefineProperty(this, "LabelFriendNum", _descriptor9, this);

          _initializerDefineProperty(this, "nodeAdd", _descriptor10, this);

          _initializerDefineProperty(this, "btnIgnoreAll", _descriptor11, this);

          _initializerDefineProperty(this, "btnAgreeAll", _descriptor12, this);

          _initializerDefineProperty(this, "btnRefreshAll", _descriptor13, this);

          _initializerDefineProperty(this, "friendListUI", _descriptor14, this);

          _initializerDefineProperty(this, "friendAddUI", _descriptor15, this);

          _initializerDefineProperty(this, "friendStrangerUI", _descriptor16, this);
        }

        static getUrl() {
          return "prefab/ui/FriendUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Default;
        }

        static getBundleName() {
          return (_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).HomeFriend;
        }

        static getUIOption() {
          return {
            isClickBgCloseUI: true
          };
        }

        async onLoad() {
          (_crd && UITools === void 0 ? (_reportPossibleCrUseOfUITools({
            error: Error()
          }), UITools) : UITools).modifyNumber(this.LabelTimes, "50", "100");
          (_crd && UITools === void 0 ? (_reportPossibleCrUseOfUITools({
            error: Error()
          }), UITools) : UITools).modifyNumber(this.LabelFriendNum, "30", "100");
          this.LabelUpdate.string = "刷新时间：" + (_crd && UITools === void 0 ? (_reportPossibleCrUseOfUITools({
            error: Error()
          }), UITools) : UITools).formatTime(3 * 60 * 60, true);
          this.btnClose.addClick(this.closeUI, this);
          this.btnGet.addClick(this.onPower, this);
          this.btnIgnoreAll.addClick(this.onIgnore, this);
          this.btnAgreeAll.addClick(this.onAgree, this);
          this.btnRefreshAll.addClick(this.onRefresh, this);
          this.panelAdd.active = false;
          this.nodeAdd.active = false;
          this.tabPanel.addTabFun(idx => {
            this.nodeList.active = idx == MODE_LIST;
            this.nodeAdd.active = idx == MODE_ADD;
            this.panelList.active = idx == MODE_LIST;
            this.panelAdd.active = idx == MODE_ADD;
          });
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).once((_crd && HomeUIEvent === void 0 ? (_reportPossibleCrUseOfHomeUIEvent({
            error: Error()
          }), HomeUIEvent) : HomeUIEvent).Leave, this.onLeave, this);
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).on((_crd && DataEvent === void 0 ? (_reportPossibleCrUseOfDataEvent({
            error: Error()
          }), DataEvent) : DataEvent).FriendRefresh, this.onFriendRefresh, this);
        }

        onLeave() {
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).off((_crd && HomeUIEvent === void 0 ? (_reportPossibleCrUseOfHomeUIEvent({
            error: Error()
          }), HomeUIEvent) : HomeUIEvent).Leave, this.onLeave, this);
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).closeUI(FriendUI);
        }

        onFriendRefresh() {}

        async closeUI() {
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).closeUI(FriendUI);
          await (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && HomeUI === void 0 ? (_reportPossibleCrUseOfHomeUI({
            error: Error()
          }), HomeUI) : HomeUI);
        }

        onPower() {}

        onIgnore() {}

        onAgree() {}

        onRefresh() {}

        async onShow() {}

        async onHide() {}

        async onClose() {}

        onDestroy() {
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).targetOff(this);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "btnClose", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "tabPanel", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "panelList", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "panelAdd", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "nodeList", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "btnGet", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "LabelTimes", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor8 = _applyDecoratedDescriptor(_class2.prototype, "LabelUpdate", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor9 = _applyDecoratedDescriptor(_class2.prototype, "LabelFriendNum", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor10 = _applyDecoratedDescriptor(_class2.prototype, "nodeAdd", [_dec11], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor11 = _applyDecoratedDescriptor(_class2.prototype, "btnIgnoreAll", [_dec12], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor12 = _applyDecoratedDescriptor(_class2.prototype, "btnAgreeAll", [_dec13], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor13 = _applyDecoratedDescriptor(_class2.prototype, "btnRefreshAll", [_dec14], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor14 = _applyDecoratedDescriptor(_class2.prototype, "friendListUI", [_dec15], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor15 = _applyDecoratedDescriptor(_class2.prototype, "friendAddUI", [_dec16], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor16 = _applyDecoratedDescriptor(_class2.prototype, "friendStrangerUI", [_dec17], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=dba41ee86f97ac7b4f88b8b925886a2c7603bedf.js.map
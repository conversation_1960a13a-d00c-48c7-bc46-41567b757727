2025-10-7 17:33:24-log: Cannot access game frame or container.
2025-10-7 17:33:24-debug: asset-db:require-engine-code (440ms)
2025-10-7 17:33:24-log: meshopt wasm decoder initialized
2025-10-7 17:33:24-log: [bullet]:bullet wasm lib loaded.
2025-10-7 17:33:24-log: [box2d]:box2d wasm lib loaded.
2025-10-7 17:33:24-log: Using legacy pipeline
2025-10-7 17:33:24-log: Cocos Creator v3.8.6
2025-10-7 17:33:24-debug: [Assets Memory track]: asset-db-plugin-register: programming start:81.16MB, end 84.09MB, increase: 2.94MB
2025-10-7 17:33:24-log: Forward render pipeline initialized.
2025-10-7 17:33:24-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:30.81MB, end 80.04MB, increase: 49.23MB
2025-10-7 17:33:25-debug: [Assets Memory track]: asset-db-plugin-register: builder start:84.13MB, end 224.87MB, increase: 140.74MB
2025-10-7 17:33:25-debug: [Assets Memory track]: asset-db-plugin-register: engine-extends start:225.12MB, end 228.27MB, increase: 3.15MB
2025-10-7 17:33:25-debug: [Assets Memory track]: asset-db-plugin-register: project start:81.01MB, end 228.48MB, increase: 147.47MB
2025-10-7 17:33:25-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:80.07MB, end 228.51MB, increase: 148.43MB
2025-10-7 17:33:25-debug: run package(google-play) handler(enable) start
2025-10-7 17:33:25-debug: run package(google-play) handler(enable) success!
2025-10-7 17:33:25-debug: run package(harmonyos-next) handler(enable) success!
2025-10-7 17:33:25-debug: run package(harmonyos-next) handler(enable) start
2025-10-7 17:33:25-debug: run package(huawei-agc) handler(enable) start
2025-10-7 17:33:25-debug: run package(honor-mini-game) handler(enable) start
2025-10-7 17:33:25-debug: run package(honor-mini-game) handler(enable) success!
2025-10-7 17:33:25-debug: run package(huawei-quick-game) handler(enable) start
2025-10-7 17:33:25-debug: run package(huawei-agc) handler(enable) success!
2025-10-7 17:33:25-debug: run package(huawei-quick-game) handler(enable) success!
2025-10-7 17:33:25-debug: run package(ios) handler(enable) start
2025-10-7 17:33:25-debug: run package(ios) handler(enable) success!
2025-10-7 17:33:25-debug: run package(linux) handler(enable) start
2025-10-7 17:33:25-debug: run package(migu-mini-game) handler(enable) start
2025-10-7 17:33:25-debug: run package(mac) handler(enable) start
2025-10-7 17:33:25-debug: run package(mac) handler(enable) success!
2025-10-7 17:33:25-debug: run package(linux) handler(enable) success!
2025-10-7 17:33:25-debug: run package(migu-mini-game) handler(enable) success!
2025-10-7 17:33:25-debug: run package(native) handler(enable) start
2025-10-7 17:33:25-debug: run package(ohos) handler(enable) success!
2025-10-7 17:33:25-debug: run package(native) handler(enable) success!
2025-10-7 17:33:25-debug: run package(ohos) handler(enable) start
2025-10-7 17:33:25-debug: run package(runtime-dev-tools) handler(enable) start
2025-10-7 17:33:25-debug: run package(oppo-mini-game) handler(enable) start
2025-10-7 17:33:25-debug: run package(runtime-dev-tools) handler(enable) success!
2025-10-7 17:33:25-debug: run package(oppo-mini-game) handler(enable) success!
2025-10-7 17:33:25-debug: run package(taobao-mini-game) handler(enable) start
2025-10-7 17:33:25-debug: run package(taobao-mini-game) handler(enable) success!
2025-10-7 17:33:25-debug: run package(vivo-mini-game) handler(enable) start
2025-10-7 17:33:25-debug: run package(vivo-mini-game) handler(enable) success!
2025-10-7 17:33:25-debug: run package(web-desktop) handler(enable) start
2025-10-7 17:33:25-debug: run package(web-mobile) handler(enable) success!
2025-10-7 17:33:25-debug: run package(wechatgame) handler(enable) start
2025-10-7 17:33:25-debug: run package(wechatgame) handler(enable) success!
2025-10-7 17:33:25-debug: run package(web-mobile) handler(enable) start
2025-10-7 17:33:25-debug: run package(web-desktop) handler(enable) success!
2025-10-7 17:33:25-debug: run package(wechatprogram) handler(enable) success!
2025-10-7 17:33:25-debug: run package(windows) handler(enable) start
2025-10-7 17:33:25-debug: run package(wechatprogram) handler(enable) start
2025-10-7 17:33:25-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-10-7 17:33:25-debug: run package(windows) handler(enable) success!
2025-10-7 17:33:25-debug: run package(xiaomi-quick-game) handler(enable) start
2025-10-7 17:33:25-debug: run package(cocos-service) handler(enable) start
2025-10-7 17:33:25-debug: run package(im-plugin) handler(enable) start
2025-10-7 17:33:25-debug: run package(cocos-service) handler(enable) success!
2025-10-7 17:33:25-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-10-7 17:33:25-debug: run package(im-plugin) handler(enable) success!
2025-10-7 17:33:25-debug: asset-db:worker-init: initPlugin (1027ms)
2025-10-7 17:33:25-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-10-7 17:33:25-debug: run package(emitter-editor) handler(enable) start
2025-10-7 17:33:25-debug: run package(emitter-editor) handler(enable) success!
2025-10-7 17:33:25-debug: start refresh asset from db://assets/editor/enum-gen/EmitterEnum.ts...
2025-10-7 17:33:25-debug: refresh asset db://assets/editor/enum-gen success
2025-10-7 17:33:25-debug: start refresh asset from db://assets/editor/enum-gen/EnemyEnum.ts...
2025-10-7 17:33:25-debug: refresh asset db://assets/editor/enum-gen success
2025-10-7 17:33:25-debug: [Assets Memory track]: asset-db:worker-init start:30.80MB, end 225.30MB, increase: 194.50MB
2025-10-7 17:33:25-debug: Run asset db hook programming:beforePreStart ...
2025-10-7 17:33:25-debug: Run asset db hook programming:beforePreStart success!
2025-10-7 17:33:25-debug: Run asset db hook engine-extends:beforePreStart success!
2025-10-7 17:33:25-debug: Run asset db hook engine-extends:beforePreStart ...
2025-10-7 17:33:25-debug: start custom db i18n...
2025-10-7 17:33:25-debug: run package(i18n) handler(enable) start
2025-10-7 17:33:25-debug: run package(i18n) handler(enable) success!
2025-10-7 17:33:25-debug: start asset-db(i18n)...
2025-10-7 17:33:25-debug: run package(level-editor) handler(enable) start
2025-10-7 17:33:25-debug: run package(level-editor) handler(enable) success!
2025-10-7 17:33:25-debug: asset-db:worker-init (1602ms)
2025-10-7 17:33:25-debug: asset-db-hook-programming-beforePreStart (63ms)
2025-10-7 17:33:25-debug: asset-db-hook-engine-extends-beforePreStart (64ms)
2025-10-7 17:33:25-debug: [Assets Memory track]: asset-db:worker-startup-database[i18n] start:226.33MB, end 230.18MB, increase: 3.86MB
2025-10-7 17:33:25-debug: run package(placeholder) handler(enable) start
2025-10-7 17:33:25-debug: run package(placeholder) handler(enable) success!
2025-10-7 17:33:25-debug: asset-db:worker-startup-database[i18n] (38ms)
2025-10-7 17:33:25-debug: Preimport db internal success
2025-10-7 17:33:25-debug: %cImport%c: E:\M2Game\Client\assets\editor\enum-gen\EnemyEnum.ts
background: #aaff85; color: #000;
color: #000;
2025-10-7 17:33:25-debug: %cImport%c: E:\M2Game\Client\assets\editor\enum-gen\EmitterEnum.ts
background: #aaff85; color: #000;
color: #000;
2025-10-7 17:33:25-debug: Preimport db assets success
2025-10-7 17:33:25-debug: Run asset db hook programming:afterPreStart ...
2025-10-7 17:33:25-debug: starting packer-driver...
2025-10-7 17:33:32-debug: initialize scripting environment...
2025-10-7 17:33:32-debug: [[Executor]] prepare before lock
2025-10-7 17:33:32-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-10-7 17:33:32-debug: [[Executor]] prepare after unlock
2025-10-7 17:33:32-debug: Run asset db hook programming:afterPreStart success!
2025-10-7 17:33:32-debug: Run asset db hook engine-extends:afterPreStart ...
2025-10-7 17:33:32-debug: Run asset db hook engine-extends:afterPreStart success!
2025-10-7 17:33:32-debug: [Assets Memory track]: asset-db:worker-init: preStart start:225.32MB, end 233.63MB, increase: 8.31MB
2025-10-7 17:33:32-debug: Start up the 'internal' database...
2025-10-7 17:33:33-debug: asset-db-hook-programming-afterPreStart (7206ms)
2025-10-7 17:33:33-debug: asset-db:worker-effect-data-processing (220ms)
2025-10-7 17:33:33-debug: asset-db-hook-engine-extends-afterPreStart (220ms)
2025-10-7 17:33:33-debug: Start up the 'assets' database...
2025-10-7 17:33:33-debug: asset-db:worker-startup-database[internal] (7619ms)
2025-10-7 17:33:33-debug: lazy register asset handler *
2025-10-7 17:33:33-debug: lazy register asset handler text
2025-10-7 17:33:33-debug: lazy register asset handler spine-data
2025-10-7 17:33:33-debug: lazy register asset handler directory
2025-10-7 17:33:33-debug: lazy register asset handler dragonbones
2025-10-7 17:33:33-debug: lazy register asset handler dragonbones-atlas
2025-10-7 17:33:33-debug: lazy register asset handler json
2025-10-7 17:33:33-debug: lazy register asset handler terrain
2025-10-7 17:33:33-debug: lazy register asset handler typescript
2025-10-7 17:33:33-debug: lazy register asset handler javascript
2025-10-7 17:33:33-debug: lazy register asset handler prefab
2025-10-7 17:33:33-debug: lazy register asset handler sprite-frame
2025-10-7 17:33:33-debug: lazy register asset handler scene
2025-10-7 17:33:33-debug: lazy register asset handler image
2025-10-7 17:33:33-debug: lazy register asset handler sign-image
2025-10-7 17:33:33-debug: lazy register asset handler tiled-map
2025-10-7 17:33:33-debug: lazy register asset handler buffer
2025-10-7 17:33:33-debug: lazy register asset handler alpha-image
2025-10-7 17:33:33-debug: lazy register asset handler texture-cube
2025-10-7 17:33:33-debug: lazy register asset handler erp-texture-cube
2025-10-7 17:33:33-debug: lazy register asset handler render-texture
2025-10-7 17:33:33-debug: lazy register asset handler texture
2025-10-7 17:33:33-debug: lazy register asset handler rt-sprite-frame
2025-10-7 17:33:33-debug: lazy register asset handler gltf-mesh
2025-10-7 17:33:33-debug: lazy register asset handler texture-cube-face
2025-10-7 17:33:33-debug: lazy register asset handler gltf
2025-10-7 17:33:33-debug: lazy register asset handler gltf-skeleton
2025-10-7 17:33:33-debug: lazy register asset handler gltf-animation
2025-10-7 17:33:33-debug: lazy register asset handler gltf-scene
2025-10-7 17:33:33-debug: lazy register asset handler gltf-embeded-image
2025-10-7 17:33:33-debug: lazy register asset handler gltf-material
2025-10-7 17:33:33-debug: lazy register asset handler physics-material
2025-10-7 17:33:33-debug: lazy register asset handler fbx
2025-10-7 17:33:33-debug: lazy register asset handler material
2025-10-7 17:33:33-debug: lazy register asset handler effect
2025-10-7 17:33:33-debug: lazy register asset handler animation-clip
2025-10-7 17:33:33-debug: lazy register asset handler audio-clip
2025-10-7 17:33:33-debug: lazy register asset handler effect-header
2025-10-7 17:33:33-debug: lazy register asset handler animation-mask
2025-10-7 17:33:33-debug: lazy register asset handler animation-graph-variant
2025-10-7 17:33:33-debug: lazy register asset handler animation-graph
2025-10-7 17:33:33-debug: lazy register asset handler ttf-font
2025-10-7 17:33:33-debug: lazy register asset handler bitmap-font
2025-10-7 17:33:33-debug: lazy register asset handler particle
2025-10-7 17:33:33-debug: lazy register asset handler auto-atlas
2025-10-7 17:33:33-debug: lazy register asset handler label-atlas
2025-10-7 17:33:33-debug: lazy register asset handler render-pipeline
2025-10-7 17:33:33-debug: lazy register asset handler sprite-atlas
2025-10-7 17:33:33-debug: lazy register asset handler render-stage
2025-10-7 17:33:33-debug: lazy register asset handler render-flow
2025-10-7 17:33:33-debug: lazy register asset handler instantiation-mesh
2025-10-7 17:33:33-debug: lazy register asset handler instantiation-skeleton
2025-10-7 17:33:33-debug: lazy register asset handler instantiation-material
2025-10-7 17:33:33-debug: lazy register asset handler instantiation-animation
2025-10-7 17:33:33-debug: lazy register asset handler video-clip
2025-10-7 17:33:33-debug: asset-db:worker-startup-database[assets] (7605ms)
2025-10-7 17:33:33-debug: asset-db:ready (11286ms)
2025-10-7 17:33:33-debug: asset-db:start-database (7714ms)
2025-10-7 17:33:33-debug: fix the bug of updateDefaultUserData
2025-10-7 17:33:33-debug: init worker message success
2025-10-7 17:33:33-debug: programming:execute-script (2ms)
2025-10-7 17:33:33-debug: [Build Memory track]: builder:worker-init start:192.14MB, end 206.14MB, increase: 14.00MB
2025-10-7 17:33:33-debug: builder:worker-init (292ms)
2025-10-7 17:34:02-debug: start refresh asset from E:\M2Game\Client\assets\resources\game\prefabs\emitter\Bullet_211100202.prefab...
2025-10-7 17:34:02-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\emitter\Bullet_211100202.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-7 17:34:02-debug: refresh asset E:\M2Game\Client\assets\resources\game\prefabs\emitter success
2025-10-7 17:34:03-debug: start refresh asset from E:\M2Game\Client\assets\resources\game\prefabs\emitter\Bullet_211100101.prefab...
2025-10-7 17:34:03-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\emitter\Bullet_211100101.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-7 17:34:03-debug: refresh asset E:\M2Game\Client\assets\resources\game\prefabs\emitter success
2025-10-7 17:34:03-debug: refresh db internal success
2025-10-7 17:34:03-debug: refresh db i18n success
2025-10-7 17:34:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 17:34:03-debug: refresh db assets success
2025-10-7 17:34:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 17:34:03-debug: asset-db:refresh-all-database (171ms)
2025-10-7 17:34:10-debug: refresh db internal success
2025-10-7 17:34:10-debug: refresh db i18n success
2025-10-7 17:34:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 17:34:10-debug: refresh db assets success
2025-10-7 17:34:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 17:34:10-debug: asset-db:refresh-all-database (164ms)
2025-10-7 17:34:10-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-7 17:34:10-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-7 17:34:20-debug: refresh db internal success
2025-10-7 17:34:20-debug: refresh db i18n success
2025-10-7 17:34:21-debug: refresh db assets success
2025-10-7 17:34:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 17:34:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 17:34:21-debug: asset-db:refresh-all-database (162ms)
2025-10-7 17:34:21-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-7 17:34:21-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-10-7 17:34:24-debug: refresh db internal success
2025-10-7 17:34:24-debug: refresh db i18n success
2025-10-7 17:34:24-debug: refresh db assets success
2025-10-7 17:34:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 17:34:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 17:34:24-debug: asset-db:refresh-all-database (171ms)
2025-10-7 17:34:24-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-7 17:34:24-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-7 17:34:31-debug: refresh db internal success
2025-10-7 17:34:31-debug: refresh db i18n success
2025-10-7 17:34:31-debug: refresh db assets success
2025-10-7 17:34:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 17:34:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 17:34:31-debug: asset-db:refresh-all-database (157ms)
2025-10-7 17:51:05-debug: refresh db internal success
2025-10-7 17:51:05-debug: refresh db i18n success
2025-10-7 17:51:05-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\event\EventGroupCom.ts
background: #aaff85; color: #000;
color: #000;
2025-10-7 17:51:05-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\event\actions\EnemyEventAction.ts
background: #aaff85; color: #000;
color: #000;
2025-10-7 17:51:05-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\event\conditions\EnemyEventCondition.ts
background: #aaff85; color: #000;
color: #000;
2025-10-7 17:51:05-debug: refresh db assets success
2025-10-7 17:51:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 17:51:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 17:51:05-debug: asset-db:refresh-all-database (162ms)
2025-10-7 17:52:39-debug: Query all assets info in project
2025-10-7 17:52:39-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-7 17:52:39-debug: Skip compress image, progress: 0%
2025-10-7 17:52:39-debug: Init all bundles start..., progress: 0%
2025-10-7 17:52:39-debug: Num of bundles: 18..., progress: 0%
2025-10-7 17:52:39-debug: // ---- build task 查询 Asset Bundle ----
2025-10-7 17:52:39-debug: 查询 Asset Bundle start, progress: 0%
2025-10-7 17:52:39-debug: Init bundle root assets start..., progress: 0%
2025-10-7 17:52:39-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-7 17:52:39-debug:   Number of other assets: 2652
2025-10-7 17:52:39-debug:   Number of all scripts: 304
2025-10-7 17:52:39-debug: Init bundle root assets success..., progress: 0%
2025-10-7 17:52:39-debug:   Number of all scenes: 11
2025-10-7 17:52:39-debug: // ---- build task 查询 Asset Bundle ---- (31ms)
2025-10-7 17:52:39-log: run build task 查询 Asset Bundle success in 31 ms√, progress: 5%
2025-10-7 17:52:39-debug: [Build Memory track]: 查询 Asset Bundle start:213.17MB, end 212.48MB, increase: -712.48KB
2025-10-7 17:52:39-debug: 查询 Asset Bundle start, progress: 5%
2025-10-7 17:52:39-debug: // ---- build task 查询 Asset Bundle ----
2025-10-7 17:52:39-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-10-7 17:52:39-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-10-7 17:52:39-debug: [Build Memory track]: 查询 Asset Bundle start:212.51MB, end 212.89MB, increase: 384.38KB
2025-10-7 17:52:39-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-7 17:52:39-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-7 17:52:39-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-10-7 17:52:39-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-10-7 17:52:39-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:212.93MB, end 212.96MB, increase: 37.77KB
2025-10-7 17:52:39-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-7 17:52:39-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-7 17:52:39-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-10-7 17:52:39-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-10-7 17:52:39-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-7 17:52:39-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-7 17:52:39-debug: [Build Memory track]: 填充脚本数据到 settings.json start:213.00MB, end 213.03MB, increase: 34.27KB
2025-10-7 17:52:39-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-10-7 17:52:39-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:213.07MB, end 213.39MB, increase: 333.17KB
2025-10-7 17:52:39-log: run build task 整理部分构建选项内数据到 settings.json success in 3 ms√, progress: 15%
2025-10-7 17:52:41-debug: refresh db internal success
2025-10-7 17:52:41-debug: refresh db i18n success
2025-10-7 17:52:41-debug: refresh db assets success
2025-10-7 17:52:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 17:52:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 17:52:41-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-7 17:52:41-debug: asset-db:refresh-all-database (160ms)
2025-10-7 17:52:41-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-7 17:54:06-debug: refresh db internal success
2025-10-7 17:54:06-debug: refresh db i18n success
2025-10-7 17:54:06-debug: refresh db assets success
2025-10-7 17:54:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 17:54:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 17:54:06-debug: asset-db:refresh-all-database (154ms)
2025-10-7 17:54:06-debug: asset-db:worker-effect-data-processing (2ms)
2025-10-7 17:54:06-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-10-7 18:02:22-debug: refresh db internal success
2025-10-7 18:02:22-debug: refresh db i18n success
2025-10-7 18:02:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\Bullet.ts
background: #aaff85; color: #000;
color: #000;
2025-10-7 18:02:22-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\bullet\BulletData.ts
background: #aaff85; color: #000;
color: #000;
2025-10-7 18:02:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 18:02:22-debug: refresh db assets success
2025-10-7 18:02:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 18:02:22-debug: asset-db:refresh-all-database (211ms)
2025-10-7 18:02:22-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-7 18:02:22-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-7 18:02:46-debug: refresh db internal success
2025-10-7 18:02:46-debug: refresh db i18n success
2025-10-7 18:02:46-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\bullet\BulletData.ts
background: #aaff85; color: #000;
color: #000;
2025-10-7 18:02:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 18:02:46-debug: refresh db assets success
2025-10-7 18:02:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 18:02:46-debug: asset-db:refresh-all-database (192ms)
2025-10-7 18:02:46-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-7 18:02:46-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-10-7 18:02:51-debug: refresh db internal success
2025-10-7 18:02:51-debug: refresh db i18n success
2025-10-7 18:02:51-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\bullet\BulletData.ts
background: #aaff85; color: #000;
color: #000;
2025-10-7 18:02:51-debug: refresh db assets success
2025-10-7 18:02:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 18:02:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 18:02:51-debug: asset-db:refresh-all-database (180ms)
2025-10-7 18:02:51-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-7 18:02:51-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-7 18:03:01-debug: refresh db internal success
2025-10-7 18:03:01-debug: refresh db i18n success
2025-10-7 18:03:01-debug: refresh db assets success
2025-10-7 18:03:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 18:03:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 18:03:01-debug: asset-db:refresh-all-database (189ms)
2025-10-7 18:03:01-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-7 18:03:01-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-7 18:07:46-debug: refresh db internal success
2025-10-7 18:07:46-debug: refresh db i18n success
2025-10-7 18:07:46-debug: refresh db assets success
2025-10-7 18:07:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 18:07:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 18:07:46-debug: asset-db:refresh-all-database (152ms)
2025-10-7 18:07:46-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-7 18:07:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-7 18:12:21-debug: refresh db internal success
2025-10-7 18:12:21-debug: refresh db i18n success
2025-10-7 18:12:21-debug: refresh db assets success
2025-10-7 18:12:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 18:12:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 18:12:21-debug: asset-db:refresh-all-database (169ms)
2025-10-7 18:12:21-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-7 18:12:21-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-7 18:23:40-debug: refresh db internal success
2025-10-7 18:23:40-debug: refresh db i18n success
2025-10-7 18:23:40-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\EventGroup.ts
background: #aaff85; color: #000;
color: #000;
2025-10-7 18:23:40-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\eventgroup\IEventCondition.ts
background: #aaff85; color: #000;
color: #000;
2025-10-7 18:23:40-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\actions\EmitterEventActions.ts
background: #aaff85; color: #000;
color: #000;
2025-10-7 18:23:40-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\actions\BulletEventActions.ts
background: #aaff85; color: #000;
color: #000;
2025-10-7 18:23:40-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\conditions\BulletEventConditions.ts
background: #aaff85; color: #000;
color: #000;
2025-10-7 18:23:40-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\conditions\EmitterEventConditions.ts
background: #aaff85; color: #000;
color: #000;
2025-10-7 18:23:40-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\bullet\EventActionType.ts
background: #aaff85; color: #000;
color: #000;
2025-10-7 18:23:40-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\bullet\EventConditionType.ts
background: #aaff85; color: #000;
color: #000;
2025-10-7 18:23:40-debug: refresh db assets success
2025-10-7 18:23:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 18:23:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 18:23:40-debug: asset-db:refresh-all-database (167ms)
2025-10-7 18:24:15-debug: refresh db internal success
2025-10-7 18:24:15-debug: refresh db i18n success
2025-10-7 18:24:15-debug: refresh db assets success
2025-10-7 18:24:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 18:24:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 18:24:15-debug: asset-db:refresh-all-database (154ms)
2025-10-7 18:27:55-debug: refresh db internal success
2025-10-7 18:27:55-debug: refresh db i18n success
2025-10-7 18:27:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\Bullet.ts
background: #aaff85; color: #000;
color: #000;
2025-10-7 18:27:55-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\leveldata\condition\newCondition.ts
background: #aaff85; color: #000;
color: #000;
2025-10-7 18:27:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 18:27:55-debug: refresh db assets success
2025-10-7 18:27:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 18:27:55-debug: asset-db:refresh-all-database (167ms)
2025-10-7 18:27:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-7 18:28:10-debug: refresh db internal success
2025-10-7 18:28:10-debug: refresh db i18n success
2025-10-7 18:28:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 18:28:10-debug: refresh db assets success
2025-10-7 18:28:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 18:28:10-debug: asset-db:refresh-all-database (192ms)
2025-10-7 18:28:18-debug: refresh db internal success
2025-10-7 18:28:18-debug: refresh db i18n success
2025-10-7 18:28:18-debug: refresh db assets success
2025-10-7 18:28:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 18:28:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 18:28:18-debug: asset-db:refresh-all-database (155ms)
2025-10-7 18:28:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-7 18:28:22-debug: refresh db internal success
2025-10-7 18:28:22-debug: refresh db i18n success
2025-10-7 18:28:22-debug: refresh db assets success
2025-10-7 18:28:22-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 18:28:22-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 18:28:22-debug: asset-db:refresh-all-database (150ms)
2025-10-7 18:28:22-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-7 18:28:22-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-7 18:28:59-debug: start refresh asset from E:\M2Game\Client\assets\resources\game\material...
2025-10-7 18:28:59-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\material
background: #aaff85; color: #000;
color: #000;
2025-10-7 18:28:59-debug: refresh asset E:\M2Game\Client\assets\resources\game success
2025-10-7 18:28:59-debug: %cImport%c: E:\M2Game\Client\assets\resources\game
background: #aaff85; color: #000;
color: #000;
2025-10-7 18:29:25-debug: start refresh asset from E:\M2Game\Client\assets\resources\game\material\plane_base.mtl...
2025-10-7 18:29:25-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\material\plane_base.mtl
background: #aaff85; color: #000;
color: #000;
2025-10-7 18:29:25-debug: refresh asset E:\M2Game\Client\assets\resources\game\material success
2025-10-7 18:29:25-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\material
background: #aaff85; color: #000;
color: #000;
2025-10-7 18:29:49-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\material\plane_base.mtl
background: #aaff85; color: #000;
color: #000;
2025-10-7 18:29:49-debug: asset-db:reimport-assetd4397157-f607-453e-866f-6c3ff1278dc0 (2ms)
2025-10-7 18:29:49-debug: refresh db internal success
2025-10-7 18:29:49-debug: refresh db i18n success
2025-10-7 18:29:49-debug: refresh db assets success
2025-10-7 18:29:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 18:29:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 18:29:49-debug: asset-db:refresh-all-database (162ms)
2025-10-7 18:44:13-debug: refresh db internal success
2025-10-7 18:44:13-debug: refresh db i18n success
2025-10-7 18:44:13-debug: refresh db assets success
2025-10-7 18:44:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 18:44:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 18:44:13-debug: asset-db:refresh-all-database (196ms)
2025-10-7 18:48:37-debug: refresh db internal success
2025-10-7 18:48:37-debug: refresh db i18n success
2025-10-7 18:48:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 18:48:37-debug: refresh db assets success
2025-10-7 18:48:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 18:48:37-debug: asset-db:refresh-all-database (202ms)
2025-10-7 18:49:06-debug: refresh db internal success
2025-10-7 18:49:06-debug: refresh db i18n success
2025-10-7 18:49:06-debug: refresh db assets success
2025-10-7 18:49:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 18:49:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 18:49:06-debug: asset-db:refresh-all-database (156ms)
2025-10-7 18:49:06-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-7 18:49:06-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-7 18:49:11-debug: refresh db internal success
2025-10-7 18:49:11-debug: refresh db i18n success
2025-10-7 18:49:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 18:49:11-debug: refresh db assets success
2025-10-7 18:49:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 18:49:11-debug: asset-db:refresh-all-database (183ms)
2025-10-7 18:49:32-debug: refresh db internal success
2025-10-7 18:49:32-debug: refresh db i18n success
2025-10-7 18:49:32-debug: refresh db assets success
2025-10-7 18:49:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 18:49:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 18:49:32-debug: asset-db:refresh-all-database (158ms)
2025-10-7 18:49:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-7 18:49:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-7 18:49:38-debug: refresh db internal success
2025-10-7 18:49:38-debug: refresh db i18n success
2025-10-7 18:49:38-debug: refresh db assets success
2025-10-7 18:49:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 18:49:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 18:49:38-debug: asset-db:refresh-all-database (162ms)
2025-10-7 18:49:38-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-7 18:52:07-debug: refresh db internal success
2025-10-7 18:52:07-debug: refresh db i18n success
2025-10-7 18:52:07-debug: refresh db assets success
2025-10-7 18:52:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 18:52:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 18:52:07-debug: asset-db:refresh-all-database (201ms)
2025-10-7 18:52:07-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-7 18:52:07-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-7 18:52:10-debug: refresh db internal success
2025-10-7 18:52:10-debug: refresh db i18n success
2025-10-7 18:52:10-debug: refresh db assets success
2025-10-7 18:52:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 18:52:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 18:52:10-debug: asset-db:refresh-all-database (169ms)
2025-10-7 18:52:10-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-7 18:52:16-debug: refresh db internal success
2025-10-7 18:52:16-debug: refresh db i18n success
2025-10-7 18:52:16-debug: refresh db assets success
2025-10-7 18:52:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 18:52:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 18:52:16-debug: asset-db:refresh-all-database (141ms)
2025-10-7 18:52:16-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-7 18:54:18-debug: refresh db internal success
2025-10-7 18:54:18-debug: refresh db i18n success
2025-10-7 18:54:18-debug: refresh db assets success
2025-10-7 18:54:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 18:54:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 18:54:18-debug: asset-db:refresh-all-database (240ms)
2025-10-7 18:54:18-debug: asset-db:worker-effect-data-processing (33ms)
2025-10-7 18:54:18-debug: asset-db-hook-engine-extends-afterRefresh (33ms)
2025-10-7 18:54:18-debug: refresh db internal success
2025-10-7 18:54:18-debug: refresh db i18n success
2025-10-7 18:54:18-debug: refresh db assets success
2025-10-7 18:54:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 18:54:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 18:54:18-debug: asset-db:refresh-all-database (170ms)
2025-10-7 18:54:18-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-7 18:54:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-7 18:54:31-debug: refresh db internal success
2025-10-7 18:54:31-debug: refresh db i18n success
2025-10-7 18:54:31-debug: refresh db assets success
2025-10-7 18:54:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 18:54:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 18:54:31-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-7 18:54:31-debug: asset-db:refresh-all-database (153ms)
2025-10-7 18:54:31-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-7 19:01:18-debug: refresh db internal success
2025-10-7 19:01:18-debug: refresh db i18n success
2025-10-7 19:01:18-debug: refresh db assets success
2025-10-7 19:01:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-7 19:01:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-7 19:01:18-debug: asset-db:refresh-all-database (185ms)
2025-10-8 10:59:29-debug: refresh db internal success
2025-10-8 10:59:29-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\GameMapManager.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:29-debug: refresh db i18n success
2025-10-8 10:59:29-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\LevelUtils.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:29-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\LevelBackgroundLayerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\scenes
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\editor\level
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\scenes\Game.scene
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorPrefabParse.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\const
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\eventgroup
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\GameInsStart.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\scenes
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\GameIns.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\HomeUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\PlaneShowUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\Bullet.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\EventGroup.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\const\GameEnum.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\eventgroup\ExpressionValue.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\BattleManager.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\GameStateManager.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\DefaultMove.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\scenes\GameMain.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\PlaneShowUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\LevelEmittier
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\actions\BulletEventActions.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\actions\EmitterEventActions.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\conditions\BulletEventConditions.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\bullet\BulletData.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\conditions\EmitterEventConditions.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\bullet\EventActionType.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\bullet\EventConditionType.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\GameMapRun.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\LevelBaseUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\LevelLayerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\LevelNodeCheckOutScreen.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_mount_02.json
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_Mount_05.json
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\LevelEmittier\TraainEmittier_HighSky_Clouds_Few.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\LevelEmittier\TraainEmittier_HighSky_Clouds_Lot.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\LevelEmittier\TraainEmittier_HighSky_Fog with Angle.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\LevelEmittier\TraainEmittier_HighSky_Plane.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_mount_02.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\HighSky\RandNod\RandNod_Mount_05.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-8 10:59:30-debug: refresh db assets success
2025-10-8 10:59:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 10:59:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 10:59:30-debug: asset-db:refresh-all-database (423ms)
2025-10-8 11:02:42-debug: refresh db internal success
2025-10-8 11:02:42-debug: refresh db i18n success
2025-10-8 11:02:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\EnemyData.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 11:02:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\enemy\EnemyPlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 11:02:43-debug: refresh db assets success
2025-10-8 11:02:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 11:02:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 11:02:43-debug: asset-db:refresh-all-database (191ms)
2025-10-8 11:05:46-debug: refresh db internal success
2025-10-8 11:05:46-debug: refresh db i18n success
2025-10-8 11:05:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 11:05:46-debug: refresh db assets success
2025-10-8 11:05:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 11:05:46-debug: asset-db:refresh-all-database (157ms)
2025-10-8 11:49:43-debug: refresh db internal success
2025-10-8 11:49:43-debug: refresh db i18n success
2025-10-8 11:49:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\EnemyData.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 11:49:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\enemy\EnemyPlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 11:49:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\enemy\EnemyPlaneProperty.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 11:49:43-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\event\conditions\EnemyEventCondition.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 11:49:43-debug: refresh db assets success
2025-10-8 11:49:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 11:49:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 11:49:43-debug: asset-db:refresh-all-database (294ms)
2025-10-8 11:49:43-debug: asset-db:worker-effect-data-processing (2ms)
2025-10-8 11:49:43-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-10-8 12:36:42-debug: refresh db internal success
2025-10-8 12:36:42-debug: refresh db i18n success
2025-10-8 12:36:42-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\enemy\EnemyPlaneProperty.ts
background: #ffb8b8; color: #000;
color: #000;
2025-10-8 12:36:42-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\base\AttributeData.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 12:36:42-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\EnemyData.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 12:36:42-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\PathMove.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 12:36:42-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\enemy
background: #aaff85; color: #000;
color: #000;
2025-10-8 12:36:42-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\PlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 12:36:42-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\enemy\EnemyPlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 12:36:42-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\event\actions\EnemyEventAction.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 12:36:42-debug: refresh db assets success
2025-10-8 12:36:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 12:36:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 12:36:42-debug: asset-db:refresh-all-database (326ms)
2025-10-8 12:36:42-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-10-8 12:36:42-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 12:39:06-debug: refresh db internal success
2025-10-8 12:39:06-debug: refresh db i18n success
2025-10-8 12:39:06-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\EnemyData.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 12:39:06-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\enemy\EnemyPlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 12:39:06-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\event\conditions\EnemyEventCondition.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 12:39:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 12:39:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 12:39:06-debug: refresh db assets success
2025-10-8 12:39:06-debug: asset-db:refresh-all-database (167ms)
2025-10-8 12:39:06-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 12:39:14-debug: refresh db internal success
2025-10-8 12:39:14-debug: refresh db i18n success
2025-10-8 12:39:14-debug: refresh db assets success
2025-10-8 12:39:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 12:39:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 12:39:14-debug: asset-db:refresh-all-database (158ms)
2025-10-8 13:41:59-debug: refresh db internal success
2025-10-8 13:41:59-debug: refresh db i18n success
2025-10-8 13:41:59-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\event\actions\EnemyEventAction.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 13:41:59-debug: refresh db assets success
2025-10-8 13:41:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 13:41:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 13:41:59-debug: asset-db:refresh-all-database (252ms)
2025-10-8 13:42:02-debug: refresh db internal success
2025-10-8 13:42:02-debug: refresh db i18n success
2025-10-8 13:42:02-debug: refresh db assets success
2025-10-8 13:42:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 13:42:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 13:42:02-debug: asset-db:refresh-all-database (196ms)
2025-10-8 13:42:08-debug: refresh db internal success
2025-10-8 13:42:08-debug: refresh db i18n success
2025-10-8 13:42:08-debug: refresh db assets success
2025-10-8 13:42:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 13:42:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 13:42:08-debug: asset-db:refresh-all-database (282ms)
2025-10-8 13:42:08-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 13:42:08-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 13:42:13-debug: refresh db internal success
2025-10-8 13:42:13-debug: refresh db i18n success
2025-10-8 13:42:13-debug: refresh db assets success
2025-10-8 13:42:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 13:42:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 13:42:13-debug: asset-db:refresh-all-database (163ms)
2025-10-8 13:43:38-debug: refresh db internal success
2025-10-8 13:43:38-debug: refresh db i18n success
2025-10-8 13:43:38-debug: refresh db assets success
2025-10-8 13:43:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 13:43:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 13:43:38-debug: asset-db:refresh-all-database (175ms)
2025-10-8 13:43:38-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 13:43:38-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 14:26:03-debug: refresh db internal success
2025-10-8 14:26:03-debug: refresh db i18n success
2025-10-8 14:26:03-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\enemy\EnemyPlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 14:26:03-debug: refresh db assets success
2025-10-8 14:26:03-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 14:26:03-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 14:26:03-debug: asset-db:refresh-all-database (297ms)
2025-10-8 14:26:03-debug: asset-db:worker-effect-data-processing (2ms)
2025-10-8 14:26:03-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-10-8 14:26:12-debug: refresh db internal success
2025-10-8 14:26:12-debug: refresh db i18n success
2025-10-8 14:26:13-debug: refresh db assets success
2025-10-8 14:26:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 14:26:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 14:26:13-debug: asset-db:refresh-all-database (178ms)
2025-10-8 14:26:13-debug: asset-db:worker-effect-data-processing (2ms)
2025-10-8 14:26:13-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-10-8 14:26:20-debug: refresh db internal success
2025-10-8 14:26:20-debug: refresh db i18n success
2025-10-8 14:26:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 14:26:20-debug: refresh db assets success
2025-10-8 14:26:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 14:26:20-debug: asset-db:refresh-all-database (153ms)
2025-10-8 14:26:20-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 14:26:20-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-10-8 14:29:37-debug: refresh db internal success
2025-10-8 14:29:37-debug: refresh db i18n success
2025-10-8 14:29:37-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\enemy\EnemyPlaneBase.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 14:29:37-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\event\actions\EnemyEventAction.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 14:29:37-debug: refresh db assets success
2025-10-8 14:29:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 14:29:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 14:29:37-debug: asset-db:refresh-all-database (197ms)
2025-10-8 14:36:33-debug: refresh db internal success
2025-10-8 14:36:33-debug: refresh db i18n success
2025-10-8 14:36:33-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\Bullet.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 14:36:33-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\DefaultMove.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 14:36:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 14:36:33-debug: refresh db assets success
2025-10-8 14:36:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 14:36:33-debug: asset-db:refresh-all-database (197ms)
2025-10-8 14:36:33-debug: asset-db:worker-effect-data-processing (2ms)
2025-10-8 14:36:33-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-10-8 14:43:04-debug: refresh db internal success
2025-10-8 14:43:04-debug: refresh db i18n success
2025-10-8 14:43:04-debug: refresh db assets success
2025-10-8 14:43:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 14:43:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 14:43:04-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 14:43:04-debug: asset-db:refresh-all-database (234ms)
2025-10-8 14:43:04-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-10-8 14:43:06-debug: refresh db internal success
2025-10-8 14:43:06-debug: refresh db i18n success
2025-10-8 14:43:07-debug: refresh db assets success
2025-10-8 14:43:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 14:43:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 14:43:07-debug: asset-db:refresh-all-database (179ms)
2025-10-8 14:43:07-debug: asset-db:worker-effect-data-processing (2ms)
2025-10-8 14:43:07-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-10-8 14:44:49-debug: refresh db internal success
2025-10-8 14:44:49-debug: refresh db i18n success
2025-10-8 14:44:49-debug: refresh db assets success
2025-10-8 14:44:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 14:44:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 14:44:49-debug: asset-db:refresh-all-database (199ms)
2025-10-8 14:44:55-debug: refresh db internal success
2025-10-8 14:44:55-debug: refresh db i18n success
2025-10-8 14:44:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 14:44:55-debug: refresh db assets success
2025-10-8 14:44:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 14:44:55-debug: asset-db:refresh-all-database (187ms)
2025-10-8 14:44:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 14:44:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 14:58:09-debug: refresh db internal success
2025-10-8 14:58:09-debug: refresh db i18n success
2025-10-8 14:58:09-debug: refresh db assets success
2025-10-8 14:58:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 14:58:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 14:58:09-debug: asset-db:refresh-all-database (187ms)
2025-10-8 15:04:27-debug: refresh db internal success
2025-10-8 15:04:27-debug: refresh db i18n success
2025-10-8 15:04:27-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\april_fools_day_btn.png
background: #aaff85; color: #000;
color: #000;
2025-10-8 15:04:27-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\april_fools_day_btn.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-8 15:04:27-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right\april_fools_day_btn.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-8 15:04:27-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\texture\home_ui\right
background: #aaff85; color: #000;
color: #000;
2025-10-8 15:04:27-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\ObjectPool.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 15:04:27-debug: refresh db assets success
2025-10-8 15:04:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 15:04:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 15:04:27-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 15:04:27-debug: asset-db:refresh-all-database (215ms)
2025-10-8 15:04:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 15:04:29-debug: Query all assets info in project
2025-10-8 15:04:29-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-8 15:04:29-debug: Skip compress image, progress: 0%
2025-10-8 15:04:29-debug: Init all bundles start..., progress: 0%
2025-10-8 15:04:29-debug: 查询 Asset Bundle start, progress: 0%
2025-10-8 15:04:29-debug: // ---- build task 查询 Asset Bundle ----
2025-10-8 15:04:29-debug: Num of bundles: 18..., progress: 0%
2025-10-8 15:04:29-debug: Init bundle root assets start..., progress: 0%
2025-10-8 15:04:29-debug:   Number of all scripts: 306
2025-10-8 15:04:29-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-8 15:04:29-debug: Init bundle root assets success..., progress: 0%
2025-10-8 15:04:29-debug:   Number of other assets: 2653
2025-10-8 15:04:29-debug:   Number of all scenes: 11
2025-10-8 15:04:29-debug: // ---- build task 查询 Asset Bundle ---- (48ms)
2025-10-8 15:04:29-log: run build task 查询 Asset Bundle success in 48 ms√, progress: 5%
2025-10-8 15:04:29-debug: 查询 Asset Bundle start, progress: 5%
2025-10-8 15:04:29-debug: [Build Memory track]: 查询 Asset Bundle start:203.92MB, end 207.04MB, increase: 3.12MB
2025-10-8 15:04:29-debug: // ---- build task 查询 Asset Bundle ----
2025-10-8 15:04:29-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-10-8 15:04:29-debug: [Build Memory track]: 查询 Asset Bundle start:207.07MB, end 207.44MB, increase: 384.65KB
2025-10-8 15:04:29-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-8 15:04:29-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-10-8 15:04:29-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-8 15:04:29-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-10-8 15:04:29-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:207.48MB, end 207.51MB, increase: 27.57KB
2025-10-8 15:04:29-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-10-8 15:04:29-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-8 15:04:29-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-8 15:04:29-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-10-8 15:04:29-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-10-8 15:04:29-debug: [Build Memory track]: 填充脚本数据到 settings.json start:207.54MB, end 207.56MB, increase: 30.01KB
2025-10-8 15:04:29-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-8 15:04:29-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-8 15:04:29-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (5ms)
2025-10-8 15:04:29-log: run build task 整理部分构建选项内数据到 settings.json success in 5 ms√, progress: 15%
2025-10-8 15:04:29-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:207.59MB, end 207.92MB, increase: 329.48KB
2025-10-8 15:16:31-debug: refresh db internal success
2025-10-8 15:16:31-debug: refresh db i18n success
2025-10-8 15:16:31-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\ObjectPool.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 15:16:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 15:16:31-debug: refresh db assets success
2025-10-8 15:16:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 15:16:31-debug: asset-db:worker-effect-data-processing (3ms)
2025-10-8 15:16:31-debug: asset-db:refresh-all-database (200ms)
2025-10-8 15:16:31-debug: asset-db-hook-engine-extends-afterRefresh (3ms)
2025-10-8 15:16:49-debug: refresh db internal success
2025-10-8 15:16:49-debug: refresh db i18n success
2025-10-8 15:16:49-debug: refresh db assets success
2025-10-8 15:16:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 15:16:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 15:16:49-debug: asset-db:refresh-all-database (162ms)
2025-10-8 15:16:49-debug: asset-db:worker-effect-data-processing (2ms)
2025-10-8 15:16:49-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-10-8 15:17:06-debug: refresh db internal success
2025-10-8 15:17:06-debug: refresh db i18n success
2025-10-8 15:17:06-debug: refresh db assets success
2025-10-8 15:17:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 15:17:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 15:17:06-debug: asset-db:refresh-all-database (162ms)
2025-10-8 15:17:06-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 15:17:06-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 15:17:27-debug: Start record console... {file(E:\M2Game\Client\temp\builder\log\wechatgame2025-8-5 15-46.log)}
2025-10-8 15:17:27-debug: =================================== build Task (wechatgame) Start ================================
2025-10-8 15:17:27-debug: Start build task, options:
{"name":"m2-game-client","server":"https://m2gameclient-1349698650.cos.ap-guangzhou.myqcloud.com/DevDowolad/","engineModulesConfigKey":"defaultConfig","platform":"wechatgame","buildPath":"project://build","debug":false,"buildMode":"normal","mangleProperties":true,"md5Cache":true,"skipCompressTexture":false,"sourceMaps":false,"overwriteProjectSettings":{"macroConfig":{"cleanupImageCache":"on"},"includeModules":{"physics":"inherit-project-setting","physics-2d":"inherit-project-setting","gfx-webgl2":"off"}},"nativeCodeBundleMode":"wasm","polyfills":{"asyncFunctions":false},"experimentalEraseModules":true,"startSceneAssetBundle":false,"bundleConfigs":[],"inlineEnum":true,"useBuiltinServer":false,"md5CacheOptions":{"excludes":[],"includes":[],"replaceOnly":[],"handleTemplateMd5Link":true},"mainBundleIsRemote":false,"mainBundleCompressionType":"merge_dep","useSplashScreen":true,"bundleCommonChunk":false,"packAutoAtlas":true,"startScene":"a8c92a44-b866-44b1-b8a0-61d8cf8dcab3","outputName":"wechatgame","taskName":"wechatgame","scenes":[{"url":"db://assets/scenes/DevLogin.scene","uuid":"4e0f763c-2bca-4fe4-a6f5-7ede697e4d9d"},{"url":"db://assets/scenes/Game.scene","uuid":"6b52bce3-af16-4791-a85c-1786c6ed769a"},{"url":"db://assets/scenes/Main.scene","uuid":"0fe3d3b7-130d-477b-a1b8-02765e5c46c4"},{"url":"db://assets/scenes/ResUpdate.scene","uuid":"a8c92a44-b866-44b1-b8a0-61d8cf8dcab3"}],"wasmCompressionMode":false,"packages":{"wechatgame":{"orientation":"portrait","appid":"wxc333e1450fd06f8e","buildOpenDataContextTemplate":"","separateEngine":true,"highPerformanceMode":true,"__version__":"1.0.4"},"cocos-service":{"configID":"88e1f5","services":[],"__version__":"3.0.9"}},"__version__":"1.3.9","logDest":"project://temp/builder/log/wechatgame2025-8-5 15-46.log"}
2025-10-8 15:17:27-debug: Build with Cocos Creator 3.8.6
2025-10-8 15:17:27-debug: wechatgame:(onBeforeBuild) start..., progress: 0%
2025-10-8 15:17:27-debug: // ---- build task wechatgame：onBeforeBuild ----
2025-10-8 15:17:27-debug: // ---- build task wechatgame：onBeforeBuild ---- (4ms)
2025-10-8 15:17:27-debug: wechatgame:(onBeforeBuild) in 4 ms ✓, progress: 2%
2025-10-8 15:17:27-debug: // ---- build task cocos-service：onBeforeBuild ----
2025-10-8 15:17:27-debug: cocos-service:(onBeforeBuild) start..., progress: 2%
2025-10-8 15:17:27-debug: // ---- build task cocos-service：onBeforeBuild ---- (125ms)
2025-10-8 15:17:27-debug: cocos-service:(onBeforeBuild) in 125 ms ✓, progress: 4%
2025-10-8 15:17:27-debug: scene:(onBeforeBuild) start..., progress: 4%
2025-10-8 15:17:27-debug: // ---- build task scene：onBeforeBuild ----
2025-10-8 15:17:30-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\prefabs\plane\enemyplane\100005.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-8 15:17:30-debug: // ---- build task scene：onBeforeBuild ---- (3341ms)
2025-10-8 15:17:30-log: Asset DB is paused with build!
2025-10-8 15:17:30-debug: scene:(onBeforeBuild) in 3341 ms ✓, progress: 5%
2025-10-8 15:17:30-debug: Start lock asset db..., progress: 5%
2025-10-8 15:17:30-debug: Query all assets info in project
2025-10-8 15:17:30-debug: asset-db:reimport-assetf3918511-c282-4cae-87ca-d509f333186e (11ms)
2025-10-8 15:17:30-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-8 15:17:30-debug: wechatgame:(onAfterInit) start..., progress: 5%
2025-10-8 15:17:30-debug: // ---- build task wechatgame：onAfterInit ----
2025-10-8 15:17:30-debug: wechatgame:(onAfterInit) in 7 ms ✓, progress: 7%
2025-10-8 15:17:30-debug: // ---- build task wechatgame：onAfterInit ---- (7ms)
2025-10-8 15:17:30-debug: cocos-service:(onAfterInit) start..., progress: 7%
2025-10-8 15:17:30-debug: // ---- build task cocos-service：onAfterInit ----
2025-10-8 15:17:31-log: 资源数据库已锁定，资源操作(refresh)将会延迟响应，请稍侯
2025-10-8 15:17:31-debug: // ---- build task cocos-service：onAfterInit ---- (107ms)
2025-10-8 15:17:31-debug: wechatgame:(onBeforeBundleInit) start..., progress: 9%
2025-10-8 15:17:31-debug: cocos-service:(onAfterInit) in 107 ms ✓, progress: 9%
2025-10-8 15:17:31-debug: wechatgame:(onBeforeBundleInit) start..., progress: 0%
2025-10-8 15:17:31-debug: Skip compress image, progress: 0%
2025-10-8 15:17:31-debug: // ---- build task wechatgame：onBeforeBundleInit ----
2025-10-8 15:17:31-debug: // ---- build task wechatgame：onBeforeBundleInit ---- (8ms)
2025-10-8 15:17:31-debug: wechatgame:(onBeforeBundleInit) in 8 ms ✓, progress: 7%
2025-10-8 15:17:31-debug: wechatgame:(onBeforeBundleInit) in 8 ms ✓, progress: 9%
2025-10-8 15:17:31-debug: adsense-h5g-plugin:(onBeforeBundleInit) start..., progress: 9%
2025-10-8 15:17:31-debug: // ---- build task adsense-h5g-plugin：onBeforeBundleInit ----
2025-10-8 15:17:31-debug: adsense-h5g-plugin:(onBeforeBundleInit) start..., progress: 7%
2025-10-8 15:17:31-debug: [adsense-h5g-plugin] remove script success
2025-10-8 15:17:31-debug: // ---- build task adsense-h5g-plugin：onBeforeBundleInit ---- (11ms)
2025-10-8 15:17:31-debug: adsense-h5g-plugin:(onBeforeBundleInit) in 11 ms ✓, progress: 9%
2025-10-8 15:17:31-debug: adsense-h5g-plugin:(onBeforeBundleInit) in 11 ms ✓, progress: 13%
2025-10-8 15:17:31-debug: Init all bundles start..., progress: 9%
2025-10-8 15:17:31-debug: Init all bundles start..., progress: 13%
2025-10-8 15:17:31-debug: 查询 Asset Bundle start, progress: 9%
2025-10-8 15:17:31-debug: // ---- build task 查询 Asset Bundle ----
2025-10-8 15:17:31-debug: Num of bundles: 18..., progress: 9%
2025-10-8 15:17:31-debug: Num of bundles: 18..., progress: 13%
2025-10-8 15:17:31-debug: Init bundle root assets start..., progress: 9%
2025-10-8 15:17:31-debug: Init bundle root assets start..., progress: 13%
2025-10-8 15:17:31-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-8 15:17:31-debug:   Number of other assets: 2653
2025-10-8 15:17:31-debug: Init bundle root assets success..., progress: 9%
2025-10-8 15:17:31-debug: Init bundle root assets success..., progress: 13%
2025-10-8 15:17:31-debug:   Number of all scripts: 306
2025-10-8 15:17:31-debug: reload all scripts.
2025-10-8 15:17:31-log: creating executor ...
2025-10-8 15:17:31-debug:   Number of all scenes: 11
2025-10-8 15:17:31-debug: [[Executor]] reload before lock
2025-10-8 15:17:31-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\93\93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\93\93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/base" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/gfx-webgl" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/gfx-webgpu" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/gfx-empty" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/gfx-webgl2" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/3d" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/animation" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/2d" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/skeletal-animation" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/sorting" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/mask" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/ui-skew" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/rich-text" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/ui" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/graphics" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/affine-transform" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/particle-2d" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-framework" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/particle" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-cannon" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-ammo" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-builtin" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-physx" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-2d-framework" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-2d-builtin" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-2d-box2d-jsb" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-2d-box2d-wasm" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/intersection-2d" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-2d-box2d" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/profiler" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/primitive" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/geometry-renderer" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/audio" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/video" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/xr" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/light-probe" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/webview" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/terrain" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/tween" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/tiled-map" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/vendor-google" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/spine" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/dragon-bones" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/custom-pipeline" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/custom-pipeline-post-process" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/base" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/gfx-webgl2" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/gfx-empty" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/gfx-webgpu" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/3d" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/legacy-pipeline" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/animation" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/skeletal-animation" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/sorting" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/gfx-webgl" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/mask" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/rich-text" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/graphics" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/2d" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/affine-transform" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/ui-skew" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/particle" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/ui" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/particle-2d" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-framework" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-physx" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-cannon" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-builtin" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-2d-framework" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-ammo" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-2d-box2d" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-2d-box2d-wasm" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/intersection-2d" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/primitive" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-2d-builtin" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/physics-2d-box2d-jsb" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/profiler" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/geometry-renderer" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/video" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/audio" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/xr" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/light-probe" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/webview" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/terrain" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/tween" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/tiled-map" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/vendor-google" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/custom-pipeline" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/dragon-bones" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/custom-pipeline-post-process" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/legacy-pipeline" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/cc-fu/spine" loaded.
2025-10-8 15:17:31-debug: Invalidating 'pack:///chunks/93/93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js'
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/93/93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/93/93ba276ea7b26ffcdc433fab14afc1ed6f05647b.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\6d\6d8fd2b0177941b032ddc0733af48a561fb60657.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\6d\6d8fd2b0177941b032ddc0733af48a561fb60657.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\d2\d208b01558c13077e4a9c9f1302dfbf2b2122e40.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\d2\d208b01558c13077e4a9c9f1302dfbf2b2122e40.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\6a\6a5019a719a9014c047e67aa1cf34453ab8392ce.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\6a\6a5019a719a9014c047e67aa1cf34453ab8392ce.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b3\b37de22ab9dff6e9f979ab9e517f60b5b4ae4ba5.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b3\b37de22ab9dff6e9f979ab9e517f60b5b4ae4ba5.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\98\9846cefb9cb6e16313f2e5e80bbb689314385757.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\98\9846cefb9cb6e16313f2e5e80bbb689314385757.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\36\36278e5c964e5e2f737bca1654894a5a7b2a7063.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\36\36278e5c964e5e2f737bca1654894a5a7b2a7063.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\38\383c24386be9d9de15fc0c17a8951753b54d596a.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\38\383c24386be9d9de15fc0c17a8951753b54d596a.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/engine-export-to-editor/cc/editor/populate-internal-constants" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/38/383c24386be9d9de15fc0c17a8951753b54d596a.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "cce:/internal/x/engine-export-to-editor/cc/editor/populate-internal-constants" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/6a/6a5019a719a9014c047e67aa1cf34453ab8392ce.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/98/9846cefb9cb6e16313f2e5e80bbb689314385757.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register BuiltinPipelineSettings
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/36/36278e5c964e5e2f737bca1654894a5a7b2a7063.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register BuiltinDepthOfFieldPass
2025-10-8 15:17:31-debug: [[Executor]] Register BuiltinPipelinePassBuilder
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/b3/b37de22ab9dff6e9f979ab9e517f60b5b4ae4ba5.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/d2/d208b01558c13077e4a9c9f1302dfbf2b2122e40.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\49\49c387c7d23ec5c771c1bd713cdd20f77551061d.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\49\49c387c7d23ec5c771c1bd713cdd20f77551061d.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register DebugViewRuntimeControl
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/49/49c387c7d23ec5c771c1bd713cdd20f77551061d.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\97\975c4a108b072e4d69bc7af358441b930e65baeb.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\97\975c4a108b072e4d69bc7af358441b930e65baeb.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\1e\1e9adc2bb6f09ceb0f4968dc16b4388edc409826.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\1e\1e9adc2bb6f09ceb0f4968dc16b4388edc409826.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\4e\4ebe4ade7f18855c1e2eae8d95d0aa67c14dce84.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\4e\4ebe4ade7f18855c1e2eae8d95d0aa67c14dce84.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e2\e2f211f332981cffeb2db47a354ba9fea8e12df4.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e2\e2f211f332981cffeb2db47a354ba9fea8e12df4.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\f0\f0e2db9880d4139156139b8b97f6146c9ee9541a.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\f0\f0e2db9880d4139156139b8b97f6146c9ee9541a.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e8\e8f44c1cf732f47e91a38019069dc2e024ef1fd7.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e8\e8f44c1cf732f47e91a38019069dc2e024ef1fd7.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\76\76fa0be13354db51a2601305aede5e7accf2ef16.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\76\76fa0be13354db51a2601305aede5e7accf2ef16.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\d4\d42d10b911d94e8b48693e755ebfb89cc9f5727c.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\d4\d42d10b911d94e8b48693e755ebfb89cc9f5727c.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\db\dba41ee86f97ac7b4f88b8b925886a2c7603bedf.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\db\dba41ee86f97ac7b4f88b8b925886a2c7603bedf.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e4\e4db7efb87381f51c2602a8c6e266e53281dfde6.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e4\e4db7efb87381f51c2602a8c6e266e53281dfde6.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\cb\cb238d9203430c378d7532a262d665f2f844cb27.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\cb\cb238d9203430c378d7532a262d665f2f844cb27.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\2d\2d5ee455e4b5511cf419367a13a05e3df713f9df.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\2d\2d5ee455e4b5511cf419367a13a05e3df713f9df.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\49\4988e57f1774b534a27a11b045f07b41dd195db5.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\49\4988e57f1774b534a27a11b045f07b41dd195db5.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\f2\f2f47513cbff16895d89ce7181f4936f6d1f331d.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\f2\f2f47513cbff16895d89ce7181f4936f6d1f331d.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\86\8674b80c87f4b9f781d8ec2011f8c297fe366380.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\86\8674b80c87f4b9f781d8ec2011f8c297fe366380.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\0f\0f7d45d49c12c269ee29be9a4e2e2e4388b4e38c.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\0f\0f7d45d49c12c269ee29be9a4e2e2e4388b4e38c.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b9\b9a5a29dde8f3f66c919c6c0172feca03bd9f99e.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b9\b9a5a29dde8f3f66c919c6c0172feca03bd9f99e.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\2c\2c124672d3d5eeeeb3a4ea2a7b7491139ef5874f.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\2c\2c124672d3d5eeeeb3a4ea2a7b7491139ef5874f.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\39\397ffd4c727b18ccffaa1714c31353e0e7d6d592.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\39\397ffd4c727b18ccffaa1714c31353e0e7d6d592.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\fa\fac430a3ea3298469d664700720f404884b654ea.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\fa\fac430a3ea3298469d664700720f404884b654ea.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b0\b03e5058a13493d47f9f6ef8d6056ce4fd7339ad.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b0\b03e5058a13493d47f9f6ef8d6056ce4fd7339ad.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\9b\9b6fa3deaa4212c4218cbab9ec8cec811da83de4.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\9b\9b6fa3deaa4212c4218cbab9ec8cec811da83de4.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\a4\a44cd5f0015d5a050e3b20c5f1d8f2b6b4d9b3e8.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\a4\a44cd5f0015d5a050e3b20c5f1d8f2b6b4d9b3e8.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\58\58f0b7b1222b6ae594511f50c06b881acf6a2202.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\58\58f0b7b1222b6ae594511f50c06b881acf6a2202.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\2d\2d356fec0aa65258fa1f53cbd10fc356b80e71e6.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\2d\2d356fec0aa65258fa1f53cbd10fc356b80e71e6.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\c7\c72d28c7f685b05510553bbce697b446b9d1636d.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\c7\c72d28c7f685b05510553bbce697b446b9d1636d.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\d5\d583c9999dc40e2c42fdb6304d08cda1dd162743.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\d5\d583c9999dc40e2c42fdb6304d08cda1dd162743.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\66\66b41c840f31f93c922e35659624520b3f26a7db.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\66\66b41c840f31f93c922e35659624520b3f26a7db.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\a5\a5692f162b6a03431d245ba4ece01b6bcb8a6fb9.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\a5\a5692f162b6a03431d245ba4ece01b6bcb8a6fb9.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\dc\dc7d06ce11d5b3bb2d3345da4e860116f2e77ad5.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\dc\dc7d06ce11d5b3bb2d3345da4e860116f2e77ad5.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\30\306536c25bb09611deada8f0a30ea325fb55d808.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\30\306536c25bb09611deada8f0a30ea325fb55d808.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\de\de64c8791ac0fc4e0f45bf033948a24b4a432443.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\de\de64c8791ac0fc4e0f45bf033948a24b4a432443.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\dd\ddfebf349616332ee9ca8952a5ff5688555a4ded.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\dd\ddfebf349616332ee9ca8952a5ff5688555a4ded.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\eb\eb8dfd2022287bbe6a5a082dc8ea2735bc77f2bf.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\eb\eb8dfd2022287bbe6a5a082dc8ea2735bc77f2bf.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b2\b2dd9a90fa4ab75027a9b57b5b45aceae63c0626.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b2\b2dd9a90fa4ab75027a9b57b5b45aceae63c0626.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\2f\2f6a1707188647bfefb7b1a4bbb44319bcee3240.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\2f\2f6a1707188647bfefb7b1a4bbb44319bcee3240.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e6\e66e6eb0dbb0690a552320bc2883e300f92cf9d7.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e6\e66e6eb0dbb0690a552320bc2883e300f92cf9d7.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\37\3764cf8be269f5acce3c0a176d2b0406bc950c0e.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\37\3764cf8be269f5acce3c0a176d2b0406bc950c0e.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\bd\bd12411bb0f5846c790d24105a75dc48c75c65fd.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\bd\bd12411bb0f5846c790d24105a75dc48c75c65fd.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\8f\8f13295059422e284cd539f3b6087c6af8a64805.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\8f\8f13295059422e284cd539f3b6087c6af8a64805.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\79\79c412a61c530e8cd5f93d0339bbad0cd6a2771a.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\79\79c412a61c530e8cd5f93d0339bbad0cd6a2771a.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\d4\d494af854170793058dc919ff487c6720c088034.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\d4\d494af854170793058dc919ff487c6720c088034.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\51\51f9d3d4f7cc0674ab1d5446e4857a95fbeb796f.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\51\51f9d3d4f7cc0674ab1d5446e4857a95fbeb796f.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\63\631663451fa14c8835b694632d5a08efb2b43d6d.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\63\631663451fa14c8835b694632d5a08efb2b43d6d.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\13\13b480a194de841b1495e16b08f6ff4979803018.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\13\13b480a194de841b1495e16b08f6ff4979803018.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\21\21be59894c688ba332bf4357b62cd46bc0a6aaa4.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\21\21be59894c688ba332bf4357b62cd46bc0a6aaa4.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\10\1093ac4567dcb083d0ee94ef221924b98d5315af.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\10\1093ac4567dcb083d0ee94ef221924b98d5315af.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\7c\7cb4e3810efc464bbb1590c44132a4978b0fa282.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\7c\7cb4e3810efc464bbb1590c44132a4978b0fa282.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\8b\8bcffb02ab12628788aa5c565c8d9becccb322b0.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\8b\8bcffb02ab12628788aa5c565c8d9becccb322b0.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\13\1325d58d75a0e12b90c75b3dd8a665b8917fbeb2.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\13\1325d58d75a0e12b90c75b3dd8a665b8917fbeb2.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\63\63fa646c35d98b6ee11a2f06b203534155a8bc2a.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\63\63fa646c35d98b6ee11a2f06b203534155a8bc2a.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\89\891f6602a68e2cf301d3606a311c8247c7229420.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\89\891f6602a68e2cf301d3606a311c8247c7229420.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\92\92653a1ecdfc17300f24c15c6aaee6e4fd379329.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\92\92653a1ecdfc17300f24c15c6aaee6e4fd379329.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\5e\5e81cf07ecf39ceec91a161814d8daf069764468.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\5e\5e81cf07ecf39ceec91a161814d8daf069764468.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\6e\6eec49bf5ae9e4658372fe8aca5870a2418f6ce8.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\6e\6eec49bf5ae9e4658372fe8aca5870a2418f6ce8.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\de\de37354d1e208d297c710ab8a668818775417f14.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\de\de37354d1e208d297c710ab8a668818775417f14.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ef\efc23f3fcea7a0864e78d76a09459e2773c0df3d.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ef\efc23f3fcea7a0864e78d76a09459e2773c0df3d.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\0e\0ea580fa94cd01e392d4c5d897abe036902ffdfd.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\0e\0ea580fa94cd01e392d4c5d897abe036902ffdfd.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b6\b6d80e7809dd8a4077db06cf56669229bbef59fc.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b6\b6d80e7809dd8a4077db06cf56669229bbef59fc.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\a1\a1413ac70923620dcc4c80b3721f9032b10813d7.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\a1\a1413ac70923620dcc4c80b3721f9032b10813d7.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ad\ad5ab252594726c50fbafe899bfed1727ccfe261.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ad\ad5ab252594726c50fbafe899bfed1727ccfe261.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ca\ca7576346b3ce5da5dd0e026b6b6b161beee81b2.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ca\ca7576346b3ce5da5dd0e026b6b6b161beee81b2.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\fc\fc5386d40fe01a13e9c9f84a41a230ba2d8baa67.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\fc\fc5386d40fe01a13e9c9f84a41a230ba2d8baa67.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e6\e6fd3f922ee66a80d1b7cb20f98803f4634b49e9.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e6\e6fd3f922ee66a80d1b7cb20f98803f4634b49e9.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\42\4275204f6690f93254b532b7bacf33aaed853230.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\42\4275204f6690f93254b532b7bacf33aaed853230.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\30\30ed714289e1a7c4347955fc87830b6ec0dcc4f2.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\30\30ed714289e1a7c4347955fc87830b6ec0dcc4f2.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\05\056a09e49c703daed19b4b8a1c08397d68fe8e40.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\05\056a09e49c703daed19b4b8a1c08397d68fe8e40.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\07\07437f8f3e0b17c81d2d426d702b5eb5ff92fc45.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\07\07437f8f3e0b17c81d2d426d702b5eb5ff92fc45.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\a0\a0d063530de23ddade53e1a935bf64c81277c061.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\a0\a0d063530de23ddade53e1a935bf64c81277c061.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\d3\d3f17c80a7ba3ad703c0c8820e725fecd473d96a.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\d3\d3f17c80a7ba3ad703c0c8820e725fecd473d96a.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\41\41d00f9be52f7272750998107d15febd88063eca.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\41\41d00f9be52f7272750998107d15febd88063eca.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\88\88047bdf14759465c4b7b8e29d64ea59e5fde9cb.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\88\88047bdf14759465c4b7b8e29d64ea59e5fde9cb.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\0a\0a34a03cf2f2a435d4518dd30ea010370f807b9c.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\0a\0a34a03cf2f2a435d4518dd30ea010370f807b9c.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\95\9518a1f41add60ffba208eb94b8d97cd7cd90e9c.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\95\9518a1f41add60ffba208eb94b8d97cd7cd90e9c.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\c5\c5d8871e59eaec0a77652d199c7da288c1619860.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\c5\c5d8871e59eaec0a77652d199c7da288c1619860.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b0\b0f730a15b5208560f69d5aa3d2d073b038751e8.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b0\b0f730a15b5208560f69d5aa3d2d073b038751e8.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b9\b9b7979390f3f12e190b7b691455778626fd9555.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b9\b9b7979390f3f12e190b7b691455778626fd9555.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\9a\9a29176490591e4da47ed7fbbee58807b3d1b1e0.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\9a\9a29176490591e4da47ed7fbbee58807b3d1b1e0.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\5f\5f90ab7bccc1408695609f67e46d0312486e1568.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\5f\5f90ab7bccc1408695609f67e46d0312486e1568.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\00\00bfddf50ace68c5ceae4cbd0b92a0f145286442.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\00\00bfddf50ace68c5ceae4cbd0b92a0f145286442.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\59\594dc329712975a0a9b5849d2bfd9bf9a45a4566.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\59\594dc329712975a0a9b5849d2bfd9bf9a45a4566.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ef\ef17f0fe450d2bbcc54bc7560be5d0c8aed84332.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ef\ef17f0fe450d2bbcc54bc7560be5d0c8aed84332.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\32\32b64b1a64c81bd8ab875bb846ef1373d22a780f.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\32\32b64b1a64c81bd8ab875bb846ef1373d22a780f.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\dd\dd5f71dda0c50a9f9aa4c2fc2dda505053014415.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\dd\dd5f71dda0c50a9f9aa4c2fc2dda505053014415.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\52\527d6e8a664e07768c7197fd26372098745b5ef2.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\52\527d6e8a664e07768c7197fd26372098745b5ef2.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\0a\0ad4a38d3c2645dc2ca29f901c1d2f8ee6d21c7d.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\0a\0ad4a38d3c2645dc2ca29f901c1d2f8ee6d21c7d.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\74\741d01e4145bb9d5291f27ec213af9daa7056ce0.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\74\741d01e4145bb9d5291f27ec213af9daa7056ce0.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\5a\5a67953330630342960a6b6e0c80a90fb6b178ef.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\5a\5a67953330630342960a6b6e0c80a90fb6b178ef.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\f0\f0fbeb665ab5431b569e0618318c5263e32b6c0e.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\f0\f0fbeb665ab5431b569e0618318c5263e32b6c0e.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\94\94e0cf8e0961b14ff25e41f87b4033b79bbb897c.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\94\94e0cf8e0961b14ff25e41f87b4033b79bbb897c.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b5\b52c47e2eca12c56b8ed4db63ecafa24c0cf1395.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b5\b52c47e2eca12c56b8ed4db63ecafa24c0cf1395.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\7c\7c02d99877a6775d0211e741dbb9ae69f2566ba2.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\7c\7c02d99877a6775d0211e741dbb9ae69f2566ba2.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\01\010dba96fb560cb553830cbedcce056c3740edb9.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\01\010dba96fb560cb553830cbedcce056c3740edb9.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e6\e63f3934a70ac58ad785fb392372147bd4f4d7bb.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e6\e63f3934a70ac58ad785fb392372147bd4f4d7bb.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\17\17041adbf37b8eec20d070d615bc282147ab6748.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\17\17041adbf37b8eec20d070d615bc282147ab6748.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ff\ff790ef906fea958f16bb343d2760e0a7df9e3f3.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ff\ff790ef906fea958f16bb343d2760e0a7df9e3f3.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b7\b7bb0be137b1fc71512eefb26b244ded2451daae.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b7\b7bb0be137b1fc71512eefb26b244ded2451daae.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\7e\7ec808c08c794a582031f5f5e6f729f7bd88b823.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\7e\7ec808c08c794a582031f5f5e6f729f7bd88b823.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\24\24109fe4f603e5c1b0591c771b383507af10de31.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\24\24109fe4f603e5c1b0591c771b383507af10de31.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\f5\f54b4cf4f8d03920cd9c87dfb4881bddc6fe68ed.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\f5\f54b4cf4f8d03920cd9c87dfb4881bddc6fe68ed.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e6\e604cc86ecf59800d5424747d9b69e78f4859843.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e6\e604cc86ecf59800d5424747d9b69e78f4859843.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b1\b150558e244271aa7acf525dff0fc748287f2fd6.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b1\b150558e244271aa7acf525dff0fc748287f2fd6.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b5\b59ee027701a05f5c52250631842041fa7ab5be5.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b5\b59ee027701a05f5c52250631842041fa7ab5be5.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\54\54e73dcfac1385d6d150ab5979bff025ff5be60e.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\54\54e73dcfac1385d6d150ab5979bff025ff5be60e.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\82\82560c467b0287e70ede1a2438d26e7ab0a6b0f6.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\82\82560c467b0287e70ede1a2438d26e7ab0a6b0f6.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\bb\bb8ba5f93385d6706f932c8b49550248f4d3ebd4.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\bb\bb8ba5f93385d6706f932c8b49550248f4d3ebd4.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\a5\a544e3c81bd14e31764538e29b5832830cc46b39.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\a5\a544e3c81bd14e31764538e29b5832830cc46b39.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\2d\2de0d6ad1d9605699c671c57185c9dbc99e939cf.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\2d\2de0d6ad1d9605699c671c57185c9dbc99e939cf.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\3b\3ba0014a4c84568deb516381c5719ab374bec8c3.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\3b\3ba0014a4c84568deb516381c5719ab374bec8c3.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\60\60a830ad015e7d68e401a98a73f6c91d356f14f0.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\60\60a830ad015e7d68e401a98a73f6c91d356f14f0.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\44\44adf2d341aa0b184df67aa9a279fa625449a3cc.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\44\44adf2d341aa0b184df67aa9a279fa625449a3cc.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\90\90776a631c48351c0114b6d4898c274778b1ec57.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\90\90776a631c48351c0114b6d4898c274778b1ec57.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\6d\6d3e68672dcaef837f498fdb680d212abeff87e7.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\6d\6d3e68672dcaef837f498fdb680d212abeff87e7.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\56\5647e59e5b6f41fec68eb89e5e90231b442657e3.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\56\5647e59e5b6f41fec68eb89e5e90231b442657e3.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\bb\bb286bfef14de9ee094fbe4bcb2583153af35cd1.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\bb\bb286bfef14de9ee094fbe4bcb2583153af35cd1.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\0b\0b7ca58fb96bcfef72d08ca1a020684631012b18.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\0b\0b7ca58fb96bcfef72d08ca1a020684631012b18.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\84\84b7d24172003d134d2c2dd175bef9ea82c35b86.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\84\84b7d24172003d134d2c2dd175bef9ea82c35b86.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ec\ec4fbd272a2d52c91eac5a0c69649fa14c52ffe0.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ec\ec4fbd272a2d52c91eac5a0c69649fa14c52ffe0.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\9e\9edbbcb0576c1095003402ac38a37c3cb6ff3a2f.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\9e\9edbbcb0576c1095003402ac38a37c3cb6ff3a2f.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\cf\cf2994eedf9b5e95dc56ef52f98dcdd37cf4a779.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\cf\cf2994eedf9b5e95dc56ef52f98dcdd37cf4a779.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\6a\6a1e8f2712cde486f3cd00c2d3d5926bf07ec48c.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\6a\6a1e8f2712cde486f3cd00c2d3d5926bf07ec48c.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\3e\3e047d5942402680c7dea00bcdb86f7d25c5c3a0.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\3e\3e047d5942402680c7dea00bcdb86f7d25c5c3a0.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\d6\d6b1af120689abbd92685b36349d4e303df285f9.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\d6\d6b1af120689abbd92685b36349d4e303df285f9.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\73\737cd15b784ce2d162e793639097b7ff44406d3e.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\73\737cd15b784ce2d162e793639097b7ff44406d3e.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\56\566192620458269380d7998a987cf7e7154c35f9.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\56\566192620458269380d7998a987cf7e7154c35f9.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\8c\8c79d84b7cbc7f7b1c93fd97f9ffec8a3c82bd30.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\8c\8c79d84b7cbc7f7b1c93fd97f9ffec8a3c82bd30.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\6b\6b4f0eca01d713722c1a1b4ee80cd90a830140cb.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\6b\6b4f0eca01d713722c1a1b4ee80cd90a830140cb.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\d7\d7f2169f5673db219f871f7e98c93ef6ea4327e6.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\d7\d7f2169f5673db219f871f7e98c93ef6ea4327e6.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ab\ab3aa85054eb937cc560b987ef904fbde711c4cc.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ab\ab3aa85054eb937cc560b987ef904fbde711c4cc.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\1a\1aa443112438f67766de2fa38a92dd676c2977dc.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\1a\1aa443112438f67766de2fa38a92dd676c2977dc.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\4b\4b9346e67cad1d5b42ef751bd466d463e39f33fb.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\4b\4b9346e67cad1d5b42ef751bd466d463e39f33fb.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\79\79dbbe89fd729c78558a41e16ca4562ac2d5abe2.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\79\79dbbe89fd729c78558a41e16ca4562ac2d5abe2.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\68\680504dd487273468825c4986de6d6e3e34ec2c6.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\68\680504dd487273468825c4986de6d6e3e34ec2c6.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\69\69618d2b8638736ce0c2cc9f97d1611f2ea1d548.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\69\69618d2b8638736ce0c2cc9f97d1611f2ea1d548.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\23\236d26e2ab2c170ef1e282693a2b59ca23805bb7.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\23\236d26e2ab2c170ef1e282693a2b59ca23805bb7.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\31\311dbda17eeb2ab61374091ade5386605b2f93d9.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\31\311dbda17eeb2ab61374091ade5386605b2f93d9.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\88\8865aab96087177ec0f58238a1171d777907c673.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\88\8865aab96087177ec0f58238a1171d777907c673.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\27\2786ace0add3cc123339a5b767059eeefe3d73ec.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\27\2786ace0add3cc123339a5b767059eeefe3d73ec.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\c9\c9ecc9eee1546ae62739436b638b5a4d1fb26152.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\c9\c9ecc9eee1546ae62739436b638b5a4d1fb26152.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\8a\8a6017960e7de81db54546d0443fbdc437d5014e.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\8a\8a6017960e7de81db54546d0443fbdc437d5014e.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\78\78ff22544d5ec9dcba0f95705d15cec70e48565f.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\78\78ff22544d5ec9dcba0f95705d15cec70e48565f.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\40\40d18e5b11a0262c3e27dff0f98322171ec12ce7.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\40\40d18e5b11a0262c3e27dff0f98322171ec12ce7.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\91\91f7e54ea264387ec76eb8cad007b249186e7890.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\91\91f7e54ea264387ec76eb8cad007b249186e7890.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\0d\0d0ee137a6db3c2b607b477bcfd024ac1cc6a92d.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\0d\0d0ee137a6db3c2b607b477bcfd024ac1cc6a92d.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e2\e24c4b17039b357a48f92267de6e7f91c2c757eb.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e2\e24c4b17039b357a48f92267de6e7f91c2c757eb.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\8b\8bc8a2ca94b4bfeaed592faf138bdcc44f2b94ac.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\8b\8bc8a2ca94b4bfeaed592faf138bdcc44f2b94ac.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\19\19c5da1528f9eb904915155f0fb7ecb5349b3fd5.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\19\19c5da1528f9eb904915155f0fb7ecb5349b3fd5.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b6\b64ef3a409e403602bbbe6c159f5c5f661bae745.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b6\b64ef3a409e403602bbbe6c159f5c5f661bae745.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\d5\d5d213ceeee10461334bcde8d6baf9571830475b.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\d5\d5d213ceeee10461334bcde8d6baf9571830475b.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\fd\fd1468bb1991c6a157d9b8dbc46cddfd092c187b.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\fd\fd1468bb1991c6a157d9b8dbc46cddfd092c187b.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\6e\6e8372a14d821670c1f6f6f048c69a8d03d444e7.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\6e\6e8372a14d821670c1f6f6f048c69a8d03d444e7.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\d1\d1cd27b3964e1849b6c6cf7ed46aeca031000e73.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\d1\d1cd27b3964e1849b6c6cf7ed46aeca031000e73.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e5\e57a55f03225333ecca397277f6551408dba9b7b.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e5\e57a55f03225333ecca397277f6551408dba9b7b.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\6f\6fb8da61b6426ec4bc9bd2e9b356a34e8f3b57f2.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\6f\6fb8da61b6426ec4bc9bd2e9b356a34e8f3b57f2.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\de\def71f3ffa21c5a95637a2f4827c23319f040143.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\de\def71f3ffa21c5a95637a2f4827c23319f040143.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\7b\7bf7394fd428fbcc7f0d6d7a6be033c6c3b80747.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\7b\7bf7394fd428fbcc7f0d6d7a6be033c6c3b80747.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e5\e56df3307d3d4090926637980e0c40d172426668.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e5\e56df3307d3d4090926637980e0c40d172426668.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\eb\ebdf408893df8472d62b4a3e2ec79247dd7e0f7a.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\eb\ebdf408893df8472d62b4a3e2ec79247dd7e0f7a.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\68\68705d49b5f7e0afee059dc3d199d5215e1a8d3d.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\68\68705d49b5f7e0afee059dc3d199d5215e1a8d3d.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\88\88b719d2738554e24e45fae4a806f72ac0d85048.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\88\88b719d2738554e24e45fae4a806f72ac0d85048.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\21\212a75613812e337ef7e53ae87f2079ca2a98160.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\21\212a75613812e337ef7e53ae87f2079ca2a98160.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\9f\9fe086f29a8c2aebcf43e57e4e481b082e02ab2d.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\9f\9fe086f29a8c2aebcf43e57e4e481b082e02ab2d.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\bd\bd1efc31128ae550054ceba1b548b9f49a0f1aae.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\bd\bd1efc31128ae550054ceba1b548b9f49a0f1aae.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e9\e9dc75216d02175f8840f81538fe9d98f28f225e.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e9\e9dc75216d02175f8840f81538fe9d98f28f225e.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\75\75b43c14ac639813ff207172c0d864a53b18e996.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\75\75b43c14ac639813ff207172c0d864a53b18e996.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\7c\7cd07f8b0648a4f2398f916ea4ff88fdcbc09972.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\7c\7cd07f8b0648a4f2398f916ea4ff88fdcbc09972.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\db\db8edb686d0f8f814a6d1f3687b4afa681c171dc.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\db\db8edb686d0f8f814a6d1f3687b4afa681c171dc.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\df\df020fa691ecdcfa1ab17ffdb0c35179a767fb9a.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\df\df020fa691ecdcfa1ab17ffdb0c35179a767fb9a.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\1f\1f0595ad2ef0a195f498496861d888725b778d2c.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\1f\1f0595ad2ef0a195f498496861d888725b778d2c.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\78\7828890f8710922932e5ab7731a0d05653094571.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\78\7828890f8710922932e5ab7731a0d05653094571.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\2a\2a2b5da3a6b4535f66f493dd8ca796a353498f63.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\2a\2a2b5da3a6b4535f66f493dd8ca796a353498f63.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e1\e17453c00f098dd3944850b9ed79060025a9f65a.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e1\e17453c00f098dd3944850b9ed79060025a9f65a.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\36\362111050de5a65aeb8dad4ecfffb96b625ae0b6.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\36\362111050de5a65aeb8dad4ecfffb96b625ae0b6.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\52\5286fe9399e518fe7a689362bce32ea5afeb5980.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\52\5286fe9399e518fe7a689362bce32ea5afeb5980.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\02\02aba28027c123f0542cc812a0ab707996365f09.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\02\02aba28027c123f0542cc812a0ab707996365f09.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e0\e0665bf9af35c56d4f7a695a0dba0b3133a3e2b6.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e0\e0665bf9af35c56d4f7a695a0dba0b3133a3e2b6.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e3\e3a72238868d4696883159324a0771129dd061c3.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e3\e3a72238868d4696883159324a0771129dd061c3.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\7f\7f9a6a53d00d2e31c653b0fd93a771cd30183718.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\7f\7f9a6a53d00d2e31c653b0fd93a771cd30183718.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b3\b37b65c36911fa6fd0d47e8f281747979656b5a0.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b3\b37b65c36911fa6fd0d47e8f281747979656b5a0.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\91\912cde9ad7825428681b617cbcff9953419739b0.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\91\912cde9ad7825428681b617cbcff9953419739b0.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\0e\0ec7695a06e6b6c4e640628df2a81505b89c2a7e.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\0e\0ec7695a06e6b6c4e640628df2a81505b89c2a7e.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\57\572c2482b7bf60bd5824e5322dc45f9c58de9bbe.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\57\572c2482b7bf60bd5824e5322dc45f9c58de9bbe.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\fd\fdd5fdcf9020e09963104ef9a589015f60563c36.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\fd\fdd5fdcf9020e09963104ef9a589015f60563c36.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\87\877588c1388f99a40b5d00413a00be8fa44ae7bb.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\87\877588c1388f99a40b5d00413a00be8fa44ae7bb.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b3\b32576a8fcbb78252d8c65cf59b6e909493f7295.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b3\b32576a8fcbb78252d8c65cf59b6e909493f7295.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b0\b0f2f4b42f38990406443c07dd3760ab2e1c2381.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b0\b0f2f4b42f38990406443c07dd3760ab2e1c2381.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\8c\8c1cee604c44cb35efe2adf1c920eee0e896e107.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\8c\8c1cee604c44cb35efe2adf1c920eee0e896e107.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\3b\3b28f25d2c6aadf7c5faed0994f7f7810f323f2c.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\3b\3b28f25d2c6aadf7c5faed0994f7f7810f323f2c.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\8a\8a4c1aafd9384abb97d7e749583f27b1f3136b6e.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\8a\8a4c1aafd9384abb97d7e749583f27b1f3136b6e.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\09\097ebd72da55e0ef05ea1980f7afdf5f87854762.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\09\097ebd72da55e0ef05ea1980f7afdf5f87854762.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\95\95bf73f789595b749bd0d225b2a64107f6576b23.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\95\95bf73f789595b749bd0d225b2a64107f6576b23.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\04\04aad75c26270c74311e3fd782bf005c88be3cf6.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\04\04aad75c26270c74311e3fd782bf005c88be3cf6.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\2e\2e2dc482bc3c84f8e05401101816db5cb3b68d53.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\2e\2e2dc482bc3c84f8e05401101816db5cb3b68d53.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\48\48c9995c8f670e5cde94784af8d1c48b13416c64.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\48\48c9995c8f670e5cde94784af8d1c48b13416c64.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ff\ffa17cd49b85ae30500893647450e3303214f419.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ff\ffa17cd49b85ae30500893647450e3303214f419.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ae\ae75311657e067b9d569d89ccb3ba9342237187f.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ae\ae75311657e067b9d569d89ccb3ba9342237187f.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ef\eff24ca334ada5a05988240714712a9e486b7e3e.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ef\eff24ca334ada5a05988240714712a9e486b7e3e.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\34\34768833a2d2a9f8362638e4f3f8a0f752c57b2d.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\34\34768833a2d2a9f8362638e4f3f8a0f752c57b2d.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\39\394e470c498092a6e74cab8175b8092524b41277.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\39\394e470c498092a6e74cab8175b8092524b41277.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\fd\fdff4dfc58f944cf54aeaf618d77a2da4d9e4018.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\fd\fdff4dfc58f944cf54aeaf618d77a2da4d9e4018.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\77\779522b24ab77546d0987fdb8f8b28226879cc1c.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\77\779522b24ab77546d0987fdb8f8b28226879cc1c.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\86\86477da00b0b80f9d022a25bef151fbf5ea9d79a.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\86\86477da00b0b80f9d022a25bef151fbf5ea9d79a.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\46\46e5c437dbac6ebf2048d5d6ec4e894eaed0e203.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\46\46e5c437dbac6ebf2048d5d6ec4e894eaed0e203.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\74\747e6ec48f524c72e4f991262bed5706bef7da62.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\74\747e6ec48f524c72e4f991262bed5706bef7da62.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\1e\1e05016e94cb714da232f5b1732f134804aa3a4e.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\1e\1e05016e94cb714da232f5b1732f134804aa3a4e.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\2c\2c048b480e3cd0e9fb280aa9d81fa385f2ad2c7d.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\2c\2c048b480e3cd0e9fb280aa9d81fa385f2ad2c7d.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\5e\5eb873da77f55bc7177c2ed631ec361ad8431469.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\5e\5eb873da77f55bc7177c2ed631ec361ad8431469.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\fb\fbb958288fdc56230865e60cc96c86854d1a9b61.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\fb\fbb958288fdc56230865e60cc96c86854d1a9b61.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\d1\d1557274021160095bb1581cae3d1a3f34105fb5.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\d1\d1557274021160095bb1581cae3d1a3f34105fb5.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\96\96b695b65cdb8cdba445d97f4803d170fccc6bae.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\96\96b695b65cdb8cdba445d97f4803d170fccc6bae.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\cd\cd7206e67ebd3ad9bcbae6876db8e4c570e4243e.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\cd\cd7206e67ebd3ad9bcbae6876db8e4c570e4243e.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\a2\a28b25744aa1278308ac67be656581373fcb2ea1.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\a2\a28b25744aa1278308ac67be656581373fcb2ea1.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\49\49dd66dd922b9361df98e877ee82fb6aa2b92643.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\49\49dd66dd922b9361df98e877ee82fb6aa2b92643.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\6d\6d09758b923ce5be419dfb7e8a5385454d97bc38.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\6d\6d09758b923ce5be419dfb7e8a5385454d97bc38.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\1d\1d5302ea2701eece96bd8954f988ddaca69323a8.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\1d\1d5302ea2701eece96bd8954f988ddaca69323a8.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\97\97aa86a28235941b87345596a0a90ad8b54a8065.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\97\97aa86a28235941b87345596a0a90ad8b54a8065.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\24\2466e9d33504abb9551f37493d2fee4a4a511adc.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\24\2466e9d33504abb9551f37493d2fee4a4a511adc.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\9c\9cf48919471ae361bf35621d6a65df21a6cb7840.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\9c\9cf48919471ae361bf35621d6a65df21a6cb7840.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\92\928ab71a9883ce6ff25bdd946042179d19325cc0.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\92\928ab71a9883ce6ff25bdd946042179d19325cc0.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\9e\9e04bad75f933ba0d84c33840dcdd64dc4d15291.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\9e\9e04bad75f933ba0d84c33840dcdd64dc4d15291.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\f8\f8962a845727f76655d221c94ef7ca6626e23ceb.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\f8\f8962a845727f76655d221c94ef7ca6626e23ceb.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e3\e3d0585cbe8b83afafa8cc7712fab5b65b43b313.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e3\e3d0585cbe8b83afafa8cc7712fab5b65b43b313.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ce\cee298425f64e84764641d1edc9de77392e6a191.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ce\cee298425f64e84764641d1edc9de77392e6a191.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\f0\f094a9b11f9e717b0d194869cd74602637adffee.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\f0\f094a9b11f9e717b0d194869cd74602637adffee.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\21\21e3b8f0dcc8af32f9d11fb67233e89f73a804bb.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\21\21e3b8f0dcc8af32f9d11fb67233e89f73a804bb.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e8\e8f8aba6eb1d0e0856af81c66167476beb5f94f5.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e8\e8f8aba6eb1d0e0856af81c66167476beb5f94f5.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\3b\3bc827fdf6896d4bdc639f626c3c302b13615ec4.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\3b\3bc827fdf6896d4bdc639f626c3c302b13615ec4.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\23\236de097c8e810231f1415ff80c68a7759d27015.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\23\236de097c8e810231f1415ff80c68a7759d27015.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b5\b556a56fdd130ca3217c68179b550eb6c9276729.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b5\b556a56fdd130ca3217c68179b550eb6c9276729.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\d2\d23d8cdf991ed2b441fb71f05e5d30057987e7e2.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\d2\d23d8cdf991ed2b441fb71f05e5d30057987e7e2.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\86\86e4f60af33e0ca65c746896f26e3fb18ea91382.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\86\86e4f60af33e0ca65c746896f26e3fb18ea91382.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\fc\fc90e596ba0e4d99ec535d35fbb42ea3cf0cbb79.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\fc\fc90e596ba0e4d99ec535d35fbb42ea3cf0cbb79.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\eb\eb5ad162d34a0b21c6d53b3b2115a610beb28f0b.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\eb\eb5ad162d34a0b21c6d53b3b2115a610beb28f0b.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\22\2225cf311da776623895995675dff5747d5ab564.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\22\2225cf311da776623895995675dff5747d5ab564.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\73\73fd01381d7f132fe67fa75617a1b84e2e9b533c.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\73\73fd01381d7f132fe67fa75617a1b84e2e9b533c.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\43\438e08e466625518136199d3c272987f159d64e3.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\43\438e08e466625518136199d3c272987f159d64e3.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\d1\d12b47c1267b35f51eae0114df6092ba1e8b0202.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\d1\d12b47c1267b35f51eae0114df6092ba1e8b0202.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\67\679b080ca8d157eb44297c30b365fd4cdd158bd6.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\67\679b080ca8d157eb44297c30b365fd4cdd158bd6.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\9a\9afbbdab570a4771717e85d7d7cc22b0552ce61a.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\9a\9afbbdab570a4771717e85d7d7cc22b0552ce61a.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\de\debd2fcb9f50d04478b5628264fcc4137f83025c.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\de\debd2fcb9f50d04478b5628264fcc4137f83025c.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\2d\2dc154f5d73b7c3df490a78b59dce8854bfac296.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\2d\2dc154f5d73b7c3df490a78b59dce8854bfac296.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\00\000ccbb51cdcaedd8f231360cdd126860f871ab2.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\00\000ccbb51cdcaedd8f231360cdd126860f871ab2.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\58\5877bd7047e42f39647f4b2c7fbe318174fb9e67.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\58\5877bd7047e42f39647f4b2c7fbe318174fb9e67.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\08\081e7960460cd6435e3945ce4934845bc70df678.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\08\081e7960460cd6435e3945ce4934845bc70df678.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\de\de1fe9b319efd692ebb13cb9bfed16ab791f1684.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\de\de1fe9b319efd692ebb13cb9bfed16ab791f1684.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\a9\a9939944624d6fd870b64008f9da297fd860c67b.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\a9\a9939944624d6fd870b64008f9da297fd860c67b.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\02\029bd0977250dfbb1a941d6af005ac13dd7274f7.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\02\029bd0977250dfbb1a941d6af005ac13dd7274f7.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\21\21f251e756c75b240f2aa36891fef7a0f10e03b2.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\21\21f251e756c75b240f2aa36891fef7a0f10e03b2.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\48\48439f7adeda22ddda0cf4ad162ff60e069efbda.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\48\48439f7adeda22ddda0cf4ad162ff60e069efbda.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\39\39afcfdc15b8d19392022cb4ed48f3cf5951a5c0.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\39\39afcfdc15b8d19392022cb4ed48f3cf5951a5c0.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\07\071fead6138cb52bd023897424460413960b7c1b.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\07\071fead6138cb52bd023897424460413960b7c1b.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\85\85f912b58c5ed6f441cd1c367fad16fd1938d224.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\85\85f912b58c5ed6f441cd1c367fad16fd1938d224.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\fd\fd7f5bbc13425698edb92d84e45991d4e1cf1e4a.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\fd\fd7f5bbc13425698edb92d84e45991d4e1cf1e4a.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\f1\f1f359dac71bf3c98cce8956dcbd3baf302f9ac7.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\f1\f1f359dac71bf3c98cce8956dcbd3baf302f9ac7.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b3\b32d44179fea9a606d6e21a12da14979ca6d2021.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b3\b32d44179fea9a606d6e21a12da14979ca6d2021.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\68\684399f0b25e55dc06c304c09091fa3743105838.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\68\684399f0b25e55dc06c304c09091fa3743105838.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\3c\3c1262a4298b1a22fff55106c48c3a40adc59f8d.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\3c\3c1262a4298b1a22fff55106c48c3a40adc59f8d.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\f4\f4a72348f8c94b46a745ef11b6de0fef0d8aee0b.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\f4\f4a72348f8c94b46a745ef11b6de0fef0d8aee0b.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\4d\4de4022eb73762a8ce09c543654f3024f09f3047.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\4d\4de4022eb73762a8ce09c543654f3024f09f3047.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\f1\f17663d8be278fe1c9263b4693ef96fc9a7717fd.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\f1\f17663d8be278fe1c9263b4693ef96fc9a7717fd.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b2\b2191b3e7b3f8c7b5ea5e7005742d5ea014750ee.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b2\b2191b3e7b3f8c7b5ea5e7005742d5ea014750ee.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b0\b014b224d24b57c6ae45a3a0ba71c02f32cf28a4.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b0\b014b224d24b57c6ae45a3a0ba71c02f32cf28a4.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\bb\bb9b33b2273a99d958fbdd91f2e7ce7081eb88a7.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\bb\bb9b33b2273a99d958fbdd91f2e7ce7081eb88a7.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\10\10779258f979b2f5ebfd9bc4a54b273b65d669e7.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\10\10779258f979b2f5ebfd9bc4a54b273b65d669e7.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\26\2658d2b36a86a0f612b98d6717158b5bf8447779.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\26\2658d2b36a86a0f612b98d6717158b5bf8447779.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\09\09320549d423ae618c1da7bf2f3a72be3457789e.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\09\09320549d423ae618c1da7bf2f3a72be3457789e.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b2\b2054794b5e86c14a8530bfcdfb7f291335b0167.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b2\b2054794b5e86c14a8530bfcdfb7f291335b0167.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/eb/eb8dfd2022287bbe6a5a082dc8ea2735bc77f2bf.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register RootPersist
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/f0/f0e2db9880d4139156139b8b97f6146c9ee9541a.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register IMgr
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/ad/ad5ab252594726c50fbafe899bfed1727ccfe261.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/ca/ca7576346b3ce5da5dd0e026b6b6b161beee81b2.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/d5/d583c9999dc40e2c42fdb6304d08cda1dd162743.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/2d/2d356fec0aa65258fa1f53cbd10fc356b80e71e6.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/fc/fc5386d40fe01a13e9c9f84a41a230ba2d8baa67.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register LubanMgr
2025-10-8 15:17:31-debug: [[Executor]] Register GlobalDataManager
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/c7/c72d28c7f685b05510553bbce697b446b9d1636d.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/1e/1e9adc2bb6f09ceb0f4968dc16b4388edc409826.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/e4/e4db7efb87381f51c2602a8c6e266e53281dfde6.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/05/056a09e49c703daed19b4b8a1c08397d68fe8e40.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/2d/2dc154f5d73b7c3df490a78b59dce8854bfac296.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/a0/a0d063530de23ddade53e1a935bf64c81277c061.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/58/5877bd7047e42f39647f4b2c7fbe318174fb9e67.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/a9/a9939944624d6fd870b64008f9da297fd860c67b.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/08/081e7960460cd6435e3945ce4934845bc70df678.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/02/029bd0977250dfbb1a941d6af005ac13dd7274f7.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/de/de1fe9b319efd692ebb13cb9bfed16ab791f1684.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/3b/3bc827fdf6896d4bdc639f626c3c302b13615ec4.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/b3/b32d44179fea9a606d6e21a12da14979ca6d2021.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/f8/f8962a845727f76655d221c94ef7ca6626e23ceb.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/e3/e3d0585cbe8b83afafa8cc7712fab5b65b43b313.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/9e/9e04bad75f933ba0d84c33840dcdd64dc4d15291.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/ce/cee298425f64e84764641d1edc9de77392e6a191.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/21/21f251e756c75b240f2aa36891fef7a0f10e03b2.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/00/000ccbb51cdcaedd8f231360cdd126860f871ab2.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/21/21e3b8f0dcc8af32f9d11fb67233e89f73a804bb.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/e8/e8f8aba6eb1d0e0856af81c66167476beb5f94f5.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/19/19c5da1528f9eb904915155f0fb7ecb5349b3fd5.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/60/60a830ad015e7d68e401a98a73f6c91d356f14f0.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/bd/bd1efc31128ae550054ceba1b548b9f49a0f1aae.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/dd/ddfebf349616332ee9ca8952a5ff5688555a4ded.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/0a/0ad4a38d3c2645dc2ca29f901c1d2f8ee6d21c7d.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/8f/8f13295059422e284cd539f3b6087c6af8a64805.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/d4/d42d10b911d94e8b48693e755ebfb89cc9f5727c.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/e8/e8f44c1cf732f47e91a38019069dc2e024ef1fd7.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/74/741d01e4145bb9d5291f27ec213af9daa7056ce0.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/40/40d18e5b11a0262c3e27dff0f98322171ec12ce7.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register ButtonPlus
2025-10-8 15:17:31-debug: [[Executor]] Register PopupUI
2025-10-8 15:17:31-debug: [[Executor]] Register MarqueeUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/30/306536c25bb09611deada8f0a30ea325fb55d808.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/e2/e24c4b17039b357a48f92267de6e7f91c2c757eb.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/8b/8bc8a2ca94b4bfeaed592faf138bdcc44f2b94ac.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register StateSprite
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/56/5647e59e5b6f41fec68eb89e5e90231b442657e3.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/d3/d3f17c80a7ba3ad703c0c8820e725fecd473d96a.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register ItemQuaIcon
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/9f/9fe086f29a8c2aebcf43e57e4e481b082e02ab2d.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register LevelUPUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/91/91f7e54ea264387ec76eb8cad007b249186e7890.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/f0/f0fbeb665ab5431b569e0618318c5263e32b6c0e.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register ToastUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/88/8865aab96087177ec0f58238a1171d777907c673.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/e6/e604cc86ecf59800d5424747d9b69e78f4859843.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/b5/b52c47e2eca12c56b8ed4db63ecafa24c0cf1395.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/0d/0d0ee137a6db3c2b607b477bcfd024ac1cc6a92d.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/5a/5a67953330630342960a6b6e0c80a90fb6b178ef.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/7c/7c02d99877a6775d0211e741dbb9ae69f2566ba2.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/4b/4b9346e67cad1d5b42ef751bd466d463e39f33fb.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/17/17041adbf37b8eec20d070d615bc282147ab6748.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/b7/b7bb0be137b1fc71512eefb26b244ded2451daae.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/e6/e63f3934a70ac58ad785fb392372147bd4f4d7bb.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/01/010dba96fb560cb553830cbedcce056c3740edb9.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/f5/f54b4cf4f8d03920cd9c87dfb4881bddc6fe68ed.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/7e/7ec808c08c794a582031f5f5e6f729f7bd88b823.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/13/13b480a194de841b1495e16b08f6ff4979803018.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register NetMgr
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/94/94e0cf8e0961b14ff25e41f87b4033b79bbb897c.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/a5/a5692f162b6a03431d245ba4ece01b6bcb8a6fb9.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/44/44adf2d341aa0b184df67aa9a279fa625449a3cc.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register StateMachine
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/41/41d00f9be52f7272750998107d15febd88063eca.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/90/90776a631c48351c0114b6d4898c274778b1ec57.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/84/84b7d24172003d134d2c2dd175bef9ea82c35b86.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register FCollider
2025-10-8 15:17:31-debug: [[Executor]] Register FBoxCollider
2025-10-8 15:17:31-debug: [[Executor]] Register FCircleCollider
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/ec/ec4fbd272a2d52c91eac5a0c69649fa14c52ffe0.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/0b/0b7ca58fb96bcfef72d08ca1a020684631012b18.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register Plane
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/e6/e6fd3f922ee66a80d1b7cb20f98803f4634b49e9.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/66/66b41c840f31f93c922e35659624520b3f26a7db.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/2f/2f6a1707188647bfefb7b1a4bbb44319bcee3240.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/07/07437f8f3e0b17c81d2d426d702b5eb5ff92fc45.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/f0/f094a9b11f9e717b0d194869cd74602637adffee.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/e9/e9dc75216d02175f8840f81538fe9d98f28f225e.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/75/75b43c14ac639813ff207172c0d864a53b18e996.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register DevLoginData
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/db/db8edb686d0f8f814a6d1f3687b4afa681c171dc.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/df/df020fa691ecdcfa1ab17ffdb0c35179a767fb9a.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/78/7828890f8710922932e5ab7731a0d05653094571.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/7c/7cd07f8b0648a4f2398f916ea4ff88fdcbc09972.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/36/362111050de5a65aeb8dad4ecfffb96b625ae0b6.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/2a/2a2b5da3a6b4535f66f493dd8ca796a353498f63.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/e1/e17453c00f098dd3944850b9ed79060025a9f65a.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/1f/1f0595ad2ef0a195f498496861d888725b778d2c.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/7f/7f9a6a53d00d2e31c653b0fd93a771cd30183718.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/52/5286fe9399e518fe7a689362bce32ea5afeb5980.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/91/912cde9ad7825428681b617cbcff9953419739b0.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/02/02aba28027c123f0542cc812a0ab707996365f09.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/e3/e3a72238868d4696883159324a0771129dd061c3.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/8c/8c1cee604c44cb35efe2adf1c920eee0e896e107.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/b3/b37b65c36911fa6fd0d47e8f281747979656b5a0.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/e0/e0665bf9af35c56d4f7a695a0dba0b3133a3e2b6.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/0e/0ec7695a06e6b6c4e640628df2a81505b89c2a7e.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/3b/3b28f25d2c6aadf7c5faed0994f7f7810f323f2c.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/fd/fdd5fdcf9020e09963104ef9a589015f60563c36.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/b3/b32576a8fcbb78252d8c65cf59b6e909493f7295.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/87/877588c1388f99a40b5d00413a00be8fa44ae7bb.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/57/572c2482b7bf60bd5824e5322dc45f9c58de9bbe.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/8a/8a4c1aafd9384abb97d7e749583f27b1f3136b6e.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/09/097ebd72da55e0ef05ea1980f7afdf5f87854762.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/b0/b0f2f4b42f38990406443c07dd3760ab2e1c2381.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/2e/2e2dc482bc3c84f8e05401101816db5cb3b68d53.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/04/04aad75c26270c74311e3fd782bf005c88be3cf6.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/95/95bf73f789595b749bd0d225b2a64107f6576b23.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/48/48c9995c8f670e5cde94784af8d1c48b13416c64.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/ff/ffa17cd49b85ae30500893647450e3303214f419.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/ae/ae75311657e067b9d569d89ccb3ba9342237187f.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/ef/eff24ca334ada5a05988240714712a9e486b7e3e.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/34/34768833a2d2a9f8362638e4f3f8a0f752c57b2d.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/b6/b64ef3a409e403602bbbe6c159f5c5f661bae745.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/6d/6d3e68672dcaef837f498fdb680d212abeff87e7.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/dc/dc7d06ce11d5b3bb2d3345da4e860116f2e77ad5.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register MyApp
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/42/4275204f6690f93254b532b7bacf33aaed853230.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/4e/4ebe4ade7f18855c1e2eae8d95d0aa67c14dce84.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/e2/e2f211f332981cffeb2db47a354ba9fea8e12df4.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/79/79c412a61c530e8cd5f93d0339bbad0cd6a2771a.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/76/76fa0be13354db51a2601305aede5e7accf2ef16.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/bb/bb286bfef14de9ee094fbe4bcb2583153af35cd1.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/51/51f9d3d4f7cc0674ab1d5446e4857a95fbeb796f.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/3e/3e047d5942402680c7dea00bcdb86f7d25c5c3a0.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/b2/b2dd9a90fa4ab75027a9b57b5b45aceae63c0626.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/cf/cf2994eedf9b5e95dc56ef52f98dcdd37cf4a779.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/d6/d6b1af120689abbd92685b36349d4e303df285f9.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register GameReviveUI
2025-10-8 15:17:31-debug: [[Executor]] Register DragButton
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/fd/fd1468bb1991c6a157d9b8dbc46cddfd092c187b.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/88/88047bdf14759465c4b7b8e29d64ea59e5fde9cb.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register MBoomUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/6a/6a1e8f2712cde486f3cd00c2d3d5926bf07ec48c.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/cb/cb238d9203430c378d7532a262d665f2f844cb27.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register LoadingUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/8c/8c79d84b7cbc7f7b1c93fd97f9ffec8a3c82bd30.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register BottomUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/bd/bd12411bb0f5846c790d24105a75dc48c75c65fd.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register TopUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/49/4988e57f1774b534a27a11b045f07b41dd195db5.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/b9/b9a5a29dde8f3f66c919c6c0172feca03bd9f99e.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/46/46e5c437dbac6ebf2048d5d6ec4e894eaed0e203.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/74/747e6ec48f524c72e4f991262bed5706bef7da62.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/d1/d1557274021160095bb1581cae3d1a3f34105fb5.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/2c/2c048b480e3cd0e9fb280aa9d81fa385f2ad2c7d.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/5e/5eb873da77f55bc7177c2ed631ec361ad8431469.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/fb/fbb958288fdc56230865e60cc96c86854d1a9b61.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/b5/b556a56fdd130ca3217c68179b550eb6c9276729.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/d2/d23d8cdf991ed2b441fb71f05e5d30057987e7e2.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register SerializableRPNProgram
2025-10-8 15:17:31-debug: [[Executor]] Register ExpressionValue
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/f1/f1f359dac71bf3c98cce8956dcbd3baf302f9ac7.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register EventConditionData
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/23/236de097c8e810231f1415ff80c68a7759d27015.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register EventActionData
2025-10-8 15:17:31-debug: [[Executor]] Register EventGroupData
2025-10-8 15:17:31-debug: [[Executor]] Register BulletData
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/3c/3c1262a4298b1a22fff55106c48c3a40adc59f8d.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/b0/b014b224d24b57c6ae45a3a0ba71c02f32cf28a4.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/07/071fead6138cb52bd023897424460413960b7c1b.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register DefaultMove
2025-10-8 15:17:31-debug: [[Executor]] Register EmitterData
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/d1/d1cd27b3964e1849b6c6cf7ed46aeca031000e73.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/10/10779258f979b2f5ebfd9bc4a54b273b65d669e7.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/68/684399f0b25e55dc06c304c09091fa3743105838.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/39/39afcfdc15b8d19392022cb4ed48f3cf5951a5c0.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/85/85f912b58c5ed6f441cd1c367fad16fd1938d224.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register Entity
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/f4/f4a72348f8c94b46a745ef11b6de0fef0d8aee0b.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/fd/fdff4dfc58f944cf54aeaf618d77a2da4d9e4018.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/e5/e57a55f03225333ecca397277f6551408dba9b7b.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register Bullet
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/86/86e4f60af33e0ca65c746896f26e3fb18ea91382.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register Emitter
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/49/49dd66dd922b9361df98e877ee82fb6aa2b92643.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/6f/6fb8da61b6426ec4bc9bd2e9b356a34e8f3b57f2.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/48/48439f7adeda22ddda0cf4ad162ff60e069efbda.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/6d/6d09758b923ce5be419dfb7e8a5385454d97bc38.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/d7/d7f2169f5673db219f871f7e98c93ef6ea4327e6.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/6b/6b4f0eca01d713722c1a1b4ee80cd90a830140cb.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/1a/1aa443112438f67766de2fa38a92dd676c2977dc.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register BattleLayer
2025-10-8 15:17:31-debug: [[Executor]] Register GamePauseUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/97/97aa86a28235941b87345596a0a90ad8b54a8065.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/cd/cd7206e67ebd3ad9bcbae6876db8e4c570e4243e.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register GameFightUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/7b/7bf7394fd428fbcc7f0d6d7a6be033c6c3b80747.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register GameDebugInfo
2025-10-8 15:17:31-debug: [[Executor]] Register WheelSpinnerUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/68/68705d49b5f7e0afee059dc3d199d5215e1a8d3d.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/de/def71f3ffa21c5a95637a2f4827c23319f040143.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/73/73fd01381d7f132fe67fa75617a1b84e2e9b533c.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/67/679b080ca8d157eb44297c30b365fd4cdd158bd6.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/22/2225cf311da776623895995675dff5747d5ab564.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/d1/d12b47c1267b35f51eae0114df6092ba1e8b0202.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/1d/1d5302ea2701eece96bd8954f988ddaca69323a8.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/43/438e08e466625518136199d3c272987f159d64e3.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/21/212a75613812e337ef7e53ae87f2079ca2a98160.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/26/2658d2b36a86a0f612b98d6717158b5bf8447779.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/eb/eb5ad162d34a0b21c6d53b3b2115a610beb28f0b.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register WaveConditionData
2025-10-8 15:17:31-debug: [[Executor]] Register SpawnGroup
2025-10-8 15:17:31-debug: [[Executor]] Register WaveActionData
2025-10-8 15:17:31-debug: [[Executor]] Register FormationPoint
2025-10-8 15:17:31-debug: [[Executor]] Register FormationGroup
2025-10-8 15:17:31-debug: [[Executor]] Register WaveData
2025-10-8 15:17:31-debug: [[Executor]] Register WaveEventGroupData
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/f1/f17663d8be278fe1c9263b4693ef96fc9a7717fd.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/bb/bb9b33b2273a99d958fbdd91f2e7ce7081eb88a7.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register PathPoint
2025-10-8 15:17:31-debug: [[Executor]] Register PathData
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/09/09320549d423ae618c1da7bf2f3a72be3457789e.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/4d/4de4022eb73762a8ce09c543654f3024f09f3047.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/b2/b2054794b5e86c14a8530bfcdfb7f291335b0167.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/b2/b2191b3e7b3f8c7b5ea5e7005742d5ea014750ee.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register Wave
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/fc/fc90e596ba0e4d99ec535d35fbb42ea3cf0cbb79.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/fd/fd7f5bbc13425698edb92d84e45991d4e1cf1e4a.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/a2/a28b25744aa1278308ac67be656581373fcb2ea1.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/de/debd2fcb9f50d04478b5628264fcc4137f83025c.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/27/2786ace0add3cc123339a5b767059eeefe3d73ec.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/e5/e56df3307d3d4090926637980e0c40d172426668.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register LevelNodeCheckOutScreen
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/9a/9afbbdab570a4771717e85d7d7cc22b0552ce61a.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/56/566192620458269380d7998a987cf7e7154c35f9.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register LevelUtils
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/24/2466e9d33504abb9551f37493d2fee4a4a511adc.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register LevelBackgroundLayerUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/92/928ab71a9883ce6ff25bdd946042179d19325cc0.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register LevelLayer
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/9c/9cf48919471ae361bf35621d6a65df21a6cb7840.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register LevelLayerUI
2025-10-8 15:17:31-debug: [[Executor]] Register LevelBaseUI
2025-10-8 15:17:31-debug: [[Executor]] Register GameMapRun
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/88/88b719d2738554e24e45fae4a806f72ac0d85048.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/31/311dbda17eeb2ab61374091ade5386605b2f93d9.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register RogueUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/79/79dbbe89fd729c78558a41e16ca4562ac2d5abe2.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/eb/ebdf408893df8472d62b4a3e2ec79247dd7e0f7a.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register GameMain
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/ab/ab3aa85054eb937cc560b987ef904fbde711c4cc.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/c5/c5d8871e59eaec0a77652d199c7da288c1619860.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/39/394e470c498092a6e74cab8175b8092524b41277.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/73/737cd15b784ce2d162e793639097b7ff44406d3e.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/d5/d5d213ceeee10461334bcde8d6baf9571830475b.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/86/86477da00b0b80f9d022a25bef151fbf5ea9d79a.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register EnemyActionData
2025-10-8 15:17:31-debug: [[Executor]] Register EnemyConditionData
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/1e/1e05016e94cb714da232f5b1732f134804aa3a4e.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/96/96b695b65cdb8cdba445d97f4803d170fccc6bae.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register EnemyEventGroupData
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/6e/6e8372a14d821670c1f6f6f048c69a8d03d444e7.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register EnemyPrefab
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/9e/9edbbcb0576c1095003402ac38a37c3cb6ff3a2f.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/b0/b0f730a15b5208560f69d5aa3d2d073b038751e8.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/77/779522b24ab77546d0987fdb8f8b28226879cc1c.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/9a/9a29176490591e4da47ed7fbbee58807b3d1b1e0.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/95/9518a1f41add60ffba208eb94b8d97cd7cd90e9c.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/b9/b9b7979390f3f12e190b7b691455778626fd9555.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register UIAnimMethods
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/5f/5f90ab7bccc1408695609f67e46d0312486e1568.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/69/69618d2b8638736ce0c2cc9f97d1611f2ea1d548.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register EnemyEffectLayer
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/23/236d26e2ab2c170ef1e282693a2b59ca23805bb7.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/00/00bfddf50ace68c5ceae4cbd0b92a0f145286442.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/68/680504dd487273468825c4986de6d6e3e34ec2c6.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/59/594dc329712975a0a9b5849d2bfd9bf9a45a4566.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register RogueItem
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/52/527d6e8a664e07768c7197fd26372098745b5ef2.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/8a/8a6017960e7de81db54546d0443fbdc437d5014e.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register GameMapManager
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/dd/dd5f71dda0c50a9f9aa4c2fc2dda505053014415.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/32/32b64b1a64c81bd8ab875bb846ef1373d22a780f.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/d4/d494af854170793058dc919ff487c6720c088034.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/ef/ef17f0fe450d2bbcc54bc7560be5d0c8aed84332.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register ListItem
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/24/24109fe4f603e5c1b0591c771b383507af10de31.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/8b/8bcffb02ab12628788aa5c565c8d9becccb322b0.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register MailCellUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/10/1093ac4567dcb083d0ee94ef221924b98d5315af.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register MailUI
2025-10-8 15:17:31-debug: [[Executor]] Register List
2025-10-8 15:17:31-debug: [[Executor]] Register BuidingUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/ef/efc23f3fcea7a0864e78d76a09459e2773c0df3d.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/b6/b6d80e7809dd8a4077db06cf56669229bbef59fc.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/0f/0f7d45d49c12c269ee29be9a4e2e2e4388b4e38c.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register BuildingInfoUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/3b/3ba0014a4c84568deb516381c5719ab374bec8c3.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/82/82560c467b0287e70ede1a2438d26e7ab0a6b0f6.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register StoryRewardUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/a1/a1413ac70923620dcc4c80b3721f9032b10813d7.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register TabPanel
2025-10-8 15:17:31-debug: [[Executor]] Register StoryCellUI
2025-10-8 15:17:31-debug: [[Executor]] Register ProgressPanel
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/fa/fac430a3ea3298469d664700720f404884b654ea.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/78/78ff22544d5ec9dcba0f95705d15cec70e48565f.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register StoryUI
2025-10-8 15:17:31-debug: [[Executor]] Register TaskItem
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/c9/c9ecc9eee1546ae62739436b638b5a4d1fb26152.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register TaskUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/ff/ff790ef906fea958f16bb343d2760e0a7df9e3f3.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/21/21be59894c688ba332bf4357b62cd46bc0a6aaa4.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/86/8674b80c87f4b9f781d8ec2011f8c297fe366380.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register HomeUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/37/3764cf8be269f5acce3c0a176d2b0406bc950c0e.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/de/de64c8791ac0fc4e0f45bf033948a24b4a432443.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register TaskTipUI
2025-10-8 15:17:31-debug: [[Executor]] Register Tabs
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/db/dba41ee86f97ac7b4f88b8b925886a2c7603bedf.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/30/30ed714289e1a7c4347955fc87830b6ec0dcc4f2.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register FriendUI
2025-10-8 15:17:31-debug: [[Executor]] Register uiSelectItem
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/e6/e66e6eb0dbb0690a552320bc2883e300f92cf9d7.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register DevLoginUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/2d/2d5ee455e4b5511cf419367a13a05e3df713f9df.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register MatchCellUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/0a/0a34a03cf2f2a435d4518dd30ea010370f807b9c.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register uiSelect
2025-10-8 15:17:31-debug: [[Executor]] Register MatchRankUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/63/631663451fa14c8835b694632d5a08efb2b43d6d.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/f2/f2f47513cbff16895d89ce7181f4936f6d1f331d.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register MatchUI
2025-10-8 15:17:31-debug: [[Executor]] Register PKBuyUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/89/891f6602a68e2cf301d3606a311c8247c7229420.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register AvatarIcon
2025-10-8 15:17:31-debug: [[Executor]] Register PKHistoryUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/54/54e73dcfac1385d6d150ab5979bff025ff5be60e.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register PKMatchUI
2025-10-8 15:17:31-debug: [[Executor]] Register PKRewardIcon
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/92/92653a1ecdfc17300f24c15c6aaee6e4fd379329.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/13/1325d58d75a0e12b90c75b3dd8a665b8917fbeb2.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/7c/7cb4e3810efc464bbb1590c44132a4978b0fa282.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/63/63fa646c35d98b6ee11a2f06b203534155a8bc2a.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register PKShopUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/2c/2c124672d3d5eeeeb3a4ea2a7b7491139ef5874f.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register PKUI
2025-10-8 15:17:31-debug: [[Executor]] Register PlaneEquipInfoUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/b1/b150558e244271aa7acf525dff0fc748287f2fd6.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/5e/5e81cf07ecf39ceec91a161814d8daf069764468.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register BagItem
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/b5/b59ee027701a05f5c52250631842041fa7ab5be5.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/a5/a544e3c81bd14e31764538e29b5832830cc46b39.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register DropDown
2025-10-8 15:17:31-debug: [[Executor]] Register BagGrid
2025-10-8 15:17:31-debug: [[Executor]] Register SortTypeDropdown
2025-10-8 15:17:31-debug: [[Executor]] Register TopBlockInputUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/bb/bb8ba5f93385d6706f932c8b49550248f4d3ebd4.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/2d/2de0d6ad1d9605699c671c57185c9dbc99e939cf.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register PlaneCombineResultUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/de/de37354d1e208d297c710ab8a668818775417f14.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register EquipDisplay
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/0e/0ea580fa94cd01e392d4c5d897abe036902ffdfd.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register PlaneUI
2025-10-8 15:17:31-debug: [[Executor]] Register CombineDisplay
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/6e/6eec49bf5ae9e4658372fe8aca5870a2418f6ce8.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/39/397ffd4c727b18ccffaa1714c31353e0e7d6d592.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register ShopUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/b0/b03e5058a13493d47f9f6ef8d6056ce4fd7339ad.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/9b/9b6fa3deaa4212c4218cbab9ec8cec811da83de4.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register SkyIslandUI
2025-10-8 15:17:31-debug: [[Executor]] Register TalentUI
2025-10-8 15:17:31-debug: [[Executor]] Register CommonEntry
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/a4/a44cd5f0015d5a050e3b20c5f1d8f2b6b4d9b3e8.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/58/58f0b7b1222b6ae594511f50c06b881acf6a2202.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/97/975c4a108b072e4d69bc7af358441b930e65baeb.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\6b\6b84b1c3ebbb7457f2e06b8aabaeb3ba6bd8d045.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\6b\6b84b1c3ebbb7457f2e06b8aabaeb3ba6bd8d045.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/6b/6b84b1c3ebbb7457f2e06b8aabaeb3ba6bd8d045.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\47\47662f803abe7d0935933ec29cde005b64b40c50.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\47\47662f803abe7d0935933ec29cde005b64b40c50.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register FPolygonCollider
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/47/47662f803abe7d0935933ec29cde005b64b40c50.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\79\7983294ee03fd2619ed97c3289fe6adabb609214.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\79\7983294ee03fd2619ed97c3289fe6adabb609214.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/79/7983294ee03fd2619ed97c3289fe6adabb609214.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\56\56d896c5e7a6cf83b2c121ecd3e35fcee34153ee.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\56\56d896c5e7a6cf83b2c121ecd3e35fcee34153ee.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register BulletEventData
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/56/56d896c5e7a6cf83b2c121ecd3e35fcee34153ee.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\96\96c32fab17874662fe571d9d1f0c7869747cc6b0.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\96\96c32fab17874662fe571d9d1f0c7869747cc6b0.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register SerializableRandomRange
2025-10-8 15:17:31-debug: [[Executor]] Register EmittierTerrain
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/96/96c32fab17874662fe571d9d1f0c7869747cc6b0.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\97\97ad45c924900ac4799a7df7974bf339c0924b9e.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\97\97ad45c924900ac4799a7df7974bf339c0924b9e.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register TerrainElem
2025-10-8 15:17:31-debug: [[Executor]] Register RandTerrain
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/97/97ad45c924900ac4799a7df7974bf339c0924b9e.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\d3\d3f8f3c4f7e2805a3a0cfb43aa494cc7d1109e27.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\d3\d3f8f3c4f7e2805a3a0cfb43aa494cc7d1109e27.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/d3/d3f8f3c4f7e2805a3a0cfb43aa494cc7d1109e27.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\3a\3ab04d6148f2f9afc6a8c56deda606c3ccffe6af.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\3a\3ab04d6148f2f9afc6a8c56deda606c3ccffe6af.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register LevelItem
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/3a/3ab04d6148f2f9afc6a8c56deda606c3ccffe6af.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\85\8551feef9d02c34059372ec033fea94a6d84c434.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\85\8551feef9d02c34059372ec033fea94a6d84c434.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register LevelItemEvent
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/85/8551feef9d02c34059372ec033fea94a6d84c434.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\32\32805abd448bcecca1db0b5777829fa55ddb1871.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\32\32805abd448bcecca1db0b5777829fa55ddb1871.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/32/32805abd448bcecca1db0b5777829fa55ddb1871.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\59\593c4ca226a05759dc333b45e98a646f861cf82d.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\59\593c4ca226a05759dc333b45e98a646f861cf82d.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register CameraMove
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/59/593c4ca226a05759dc333b45e98a646f861cf82d.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\4d\4d2a6a114b467cc548a7a22675ddcebf2b95cb0d.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\4d\4d2a6a114b467cc548a7a22675ddcebf2b95cb0d.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register FollowCamera
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/4d/4d2a6a114b467cc548a7a22675ddcebf2b95cb0d.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\81\81ee4b32ceedb006c4da5e8d29111cfc525eb6a2.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\81\81ee4b32ceedb006c4da5e8d29111cfc525eb6a2.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register LevelDebug
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/81/81ee4b32ceedb006c4da5e8d29111cfc525eb6a2.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\79\794ce2bb3f0615a636a10a8c3f025e15162051e7.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\79\794ce2bb3f0615a636a10a8c3f025e15162051e7.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register PathMove
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/79/794ce2bb3f0615a636a10a8c3f025e15162051e7.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\c3\c3eeab076ad5557fc4ade55f2b748c8de3b62136.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\c3\c3eeab076ad5557fc4ade55f2b748c8de3b62136.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/c3/c3eeab076ad5557fc4ade55f2b748c8de3b62136.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register ColliderTest
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\fc\fc47ced621650865bf3a7eb29dcbd9f3e24da14b.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\fc\fc47ced621650865bf3a7eb29dcbd9f3e24da14b.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register Controller
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/fc/fc47ced621650865bf3a7eb29dcbd9f3e24da14b.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\56\56c90a1c46b1960dcbeb749e5ba8afa0a469952f.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\56\56c90a1c46b1960dcbeb749e5ba8afa0a469952f.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/56/56c90a1c46b1960dcbeb749e5ba8afa0a469952f.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register EffectLayer
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ea\ea2f35f8a5bc57ff040be1761e2408ee788d1fdc.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ea\ea2f35f8a5bc57ff040be1761e2408ee788d1fdc.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\35\35a1285364543f5cebf4e28666d4a0617cf2233f.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\35\35a1285364543f5cebf4e28666d4a0617cf2233f.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register LevelElemUI
2025-10-8 15:17:31-debug: [[Executor]] Register LevelCondition
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/ea/ea2f35f8a5bc57ff040be1761e2408ee788d1fdc.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/35/35a1285364543f5cebf4e28666d4a0617cf2233f.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\5f\5f5a2410841264e1cff883427289752f0190f34d.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\5f\5f5a2410841264e1cff883427289752f0190f34d.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\34\3435344f493148055fba65ecf53e091d359bd333.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\34\3435344f493148055fba65ecf53e091d359bd333.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/34/3435344f493148055fba65ecf53e091d359bd333.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register LevelWaveGroup
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/5f/5f5a2410841264e1cff883427289752f0190f34d.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register LevelEventTrigger
2025-10-8 15:17:31-debug: [[Executor]] Register LevelEventUI
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\28\287bb4612ce4894f93144c72fbefe65f033a30be.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\28\287bb4612ce4894f93144c72fbefe65f033a30be.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\67\67d41c1a9f247a91090bdffef1632854287926f0.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\67\67d41c1a9f247a91090bdffef1632854287926f0.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\be\bea3a0adb1dca9c836c64fa6b9f8ed8486364dbd.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\be\bea3a0adb1dca9c836c64fa6b9f8ed8486364dbd.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\d0\d0adc5e49b28aff496194458d141883ee3ec10f0.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\d0\d0adc5e49b28aff496194458d141883ee3ec10f0.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\00\00d818132fd9d2b2ab5184e18d4aafe4d68f2105.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\00\00d818132fd9d2b2ab5184e18d4aafe4d68f2105.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\df\dfce8e34e31d3004f1d8615ca0a443cca4efbfba.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\df\dfce8e34e31d3004f1d8615ca0a443cca4efbfba.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\c0\c0f311f5f84efc46088b1336a7cbfa0b13e01b0b.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\c0\c0f311f5f84efc46088b1336a7cbfa0b13e01b0b.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\24\24299248ecdde84f46e07f6c1cc591c826a7a259.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\24\24299248ecdde84f46e07f6c1cc591c826a7a259.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\fc\fc55ca70e6bd5d0175e4ac91ed45d9f28cfdc662.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\fc\fc55ca70e6bd5d0175e4ac91ed45d9f28cfdc662.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/c0/c0f311f5f84efc46088b1336a7cbfa0b13e01b0b.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/fc/fc55ca70e6bd5d0175e4ac91ed45d9f28cfdc662.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/d0/d0adc5e49b28aff496194458d141883ee3ec10f0.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/00/00d818132fd9d2b2ab5184e18d4aafe4d68f2105.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/be/bea3a0adb1dca9c836c64fa6b9f8ed8486364dbd.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register Weapon
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/67/67d41c1a9f247a91090bdffef1632854287926f0.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/df/dfce8e34e31d3004f1d8615ca0a443cca4efbfba.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register PlaneBase
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/24/24299248ecdde84f46e07f6c1cc591c826a7a259.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/28/287bb4612ce4894f93144c72fbefe65f033a30be.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\4b\4b380754ec2052c80f1e8d238cb60d7d60631247.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\4b\4b380754ec2052c80f1e8d238cb60d7d60631247.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/4b/4b380754ec2052c80f1e8d238cb60d7d60631247.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register PlaneBaseDebug
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\92\92be7c6d1d73422263a08ecc828e5842c8e4c823.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\92\92be7c6d1d73422263a08ecc828e5842c8e4c823.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e4\e4de7fa78e42e815e4d91deff8407c1ac3aadf2c.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e4\e4de7fa78e42e815e4d91deff8407c1ac3aadf2c.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/e4/e4de7fa78e42e815e4d91deff8407c1ac3aadf2c.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register BossPlane
2025-10-8 15:17:31-debug: [[Executor]] Register EnemyPlaneBase
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/92/92be7c6d1d73422263a08ecc828e5842c8e4c823.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\40\407eb4434100b661db0fb553a36f0ef5b4c895d2.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\40\407eb4434100b661db0fb553a36f0ef5b4c895d2.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\06\06ee078503a7f15a7d3ca16ab0269aad04ee7ba6.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\06\06ee078503a7f15a7d3ca16ab0269aad04ee7ba6.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\01\01fe3945bdb1a834dea36cdb16044eb8cfa4d0c2.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\01\01fe3945bdb1a834dea36cdb16044eb8cfa4d0c2.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register EnemyPlaneBaseDebug
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/01/01fe3945bdb1a834dea36cdb16044eb8cfa4d0c2.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register EnemyPlaneDebug
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/06/06ee078503a7f15a7d3ca16ab0269aad04ee7ba6.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register EnemyPlane
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/40/407eb4434100b661db0fb553a36f0ef5b4c895d2.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\29\292e7980528cd20afe6c7cb284e621f693f957da.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\29\292e7980528cd20afe6c7cb284e621f693f957da.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\73\733e9acdc3e6fbb07c7098d218d7eb4e13007a43.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\73\733e9acdc3e6fbb07c7098d218d7eb4e13007a43.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\5d\5d454448b8b07f76d492b69a7447a1a3918910db.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\5d\5d454448b8b07f76d492b69a7447a1a3918910db.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/73/733e9acdc3e6fbb07c7098d218d7eb4e13007a43.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/5d/5d454448b8b07f76d492b69a7447a1a3918910db.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register MainPlaneDebug
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/29/292e7980528cd20afe6c7cb284e621f693f957da.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register MainPlane
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\91\9154247d8a826eeb53d51174954aebdc47c3778d.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\91\9154247d8a826eeb53d51174954aebdc47c3778d.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/91/9154247d8a826eeb53d51174954aebdc47c3778d.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\12\12508ba2447d4d7ad93aa27a5da6c445f058d707.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\12\12508ba2447d4d7ad93aa27a5da6c445f058d707.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register AnnouncementUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/12/12508ba2447d4d7ad93aa27a5da6c445f058d707.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/62/625e6cba8f4c4ba267f1facaca621bd736ffce94.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\62\625e6cba8f4c4ba267f1facaca621bd736ffce94.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\62\625e6cba8f4c4ba267f1facaca621bd736ffce94.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register RewardUI
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\f0\f0baa37d961b2dbdc2e621e3a7044080cf46c642.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\f0\f0baa37d961b2dbdc2e621e3a7044080cf46c642.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\eb\eb109ee0682dc6c714d5f4a65b8603af9dfe8da1.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\eb\eb109ee0682dc6c714d5f4a65b8603af9dfe8da1.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\69\69d37b1710fe8ed0da86d97cd25abd62dbfd06fb.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\69\69d37b1710fe8ed0da86d97cd25abd62dbfd06fb.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\9f\9f8b08d56964dd757a4d2d4fcae1338f832d30f3.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\9f\9f8b08d56964dd757a4d2d4fcae1338f832d30f3.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register StatisticsHurtCell
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/69/69d37b1710fe8ed0da86d97cd25abd62dbfd06fb.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register StatisticsScoreCell
2025-10-8 15:17:31-debug: [[Executor]] Register StatisticsUI
2025-10-8 15:17:31-debug: [[Executor]] Register SettlementResultUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/9f/9f8b08d56964dd757a4d2d4fcae1338f832d30f3.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/eb/eb109ee0682dc6c714d5f4a65b8603af9dfe8da1.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/f0/f0baa37d961b2dbdc2e621e3a7044080cf46c642.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\37\373c1fa90721a7f4dd6a130bcc1502d168754c9f.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\37\373c1fa90721a7f4dd6a130bcc1502d168754c9f.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register SettlementUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/37/373c1fa90721a7f4dd6a130bcc1502d168754c9f.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e7\e74dd04b6057e3dabd95c0415336013d19917029.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e7\e74dd04b6057e3dabd95c0415336013d19917029.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register TextUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/e7/e74dd04b6057e3dabd95c0415336013d19917029.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\83\835ceae532c9fdafe97606b9cd8a94d3917c11d1.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\83\835ceae532c9fdafe97606b9cd8a94d3917c11d1.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\09\097034929a01aea846865e33d875904b1c124cab.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\09\097034929a01aea846865e33d875904b1c124cab.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register FriendCellUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/09/097034929a01aea846865e33d875904b1c124cab.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register FriendAddUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/83/835ceae532c9fdafe97606b9cd8a94d3917c11d1.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ce\ce1464687b5139feba2e4cf70817c53232fefa7c.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ce\ce1464687b5139feba2e4cf70817c53232fefa7c.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register FriendListUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/ce/ce1464687b5139feba2e4cf70817c53232fefa7c.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\94\9479aade575e4c8e5d673d0a146d08b87714be6f.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\94\9479aade575e4c8e5d673d0a146d08b87714be6f.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register FriendStrangerUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/94/9479aade575e4c8e5d673d0a146d08b87714be6f.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\1c\1c3d501f60330adfa23c0c5f514de2fe0e7f0423.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\1c\1c3d501f60330adfa23c0c5f514de2fe0e7f0423.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register RatioScaler
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/1c/1c3d501f60330adfa23c0c5f514de2fe0e7f0423.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\c7\c7b81ab1bbc693cd2d603dfa4a040378fb6f38b4.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\c7\c7b81ab1bbc693cd2d603dfa4a040378fb6f38b4.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register PlaneRes
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/c7/c7b81ab1bbc693cd2d603dfa4a040378fb6f38b4.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\a9\a924e09209b4a52f568b464e7ed3bf00a4861893.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\a9\a924e09209b4a52f568b464e7ed3bf00a4861893.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register PlaneShowUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/a9/a924e09209b4a52f568b464e7ed3bf00a4861893.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\0a\0a229576f85b8e9f292ce0e200faa9f76f182eac.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\0a\0a229576f85b8e9f292ce0e200faa9f76f182eac.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register DialogueUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/0a/0a229576f85b8e9f292ce0e200faa9f76f182eac.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b2\b2472bb5acd2ec46d38348b9c12e3f8b451e0110.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b2\b2472bb5acd2ec46d38348b9c12e3f8b451e0110.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register MainUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/b2/b2472bb5acd2ec46d38348b9c12e3f8b451e0110.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\83\831b1ef9717e988b50c963bd8f8ab680a93b6a36.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\83\831b1ef9717e988b50c963bd8f8ab680a93b6a36.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register PKHistoryCellUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/83/831b1ef9717e988b50c963bd8f8ab680a93b6a36.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register PKReconnectUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/ce/ce85681e3bd5f0e8807525f333dd58c56ff03607.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ce\ce85681e3bd5f0e8807525f333dd58c56ff03607.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ce\ce85681e3bd5f0e8807525f333dd58c56ff03607.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\55\554ba8609e0908251f8a022c46e5bb36392e9cbd.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\55\554ba8609e0908251f8a022c46e5bb36392e9cbd.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/55/554ba8609e0908251f8a022c46e5bb36392e9cbd.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register PKResultUI
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\6c\6c8b50718474c92f3a83440464b5da79e1fe7d43.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\6c\6c8b50718474c92f3a83440464b5da79e1fe7d43.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register PKShopItem
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/6c/6c8b50718474c92f3a83440464b5da79e1fe7d43.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\79\7913bd5daad5494b598f0c22dcb46ed226880077.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\79\7913bd5daad5494b598f0c22dcb46ed226880077.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\22\22d78004d8af00265d4d16ed942d1b73ebe14593.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\22\22d78004d8af00265d4d16ed942d1b73ebe14593.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\2c\2c47d26b9af8b771c14f34b0a4b7e49850473556.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\2c\2c47d26b9af8b771c14f34b0a4b7e49850473556.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/2c/2c47d26b9af8b771c14f34b0a4b7e49850473556.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register GmUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/22/22d78004d8af00265d4d16ed942d1b73ebe14593.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register GmButtonUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/79/7913bd5daad5494b598f0c22dcb46ed226880077.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register GMEntry
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\7e\7e5dd0d66318638adccc011f34e53f6fa481cad6.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\7e\7e5dd0d66318638adccc011f34e53f6fa481cad6.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register EmitterEditor
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/7e/7e5dd0d66318638adccc011f34e53f6fa481cad6.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\f2\f23230ccff8b04b1bc654c116c9dae559244ed5c.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\f2\f23230ccff8b04b1bc654c116c9dae559244ed5c.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\d6\d64c99caf3287de1f11fef52523ad5d812527048.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\d6\d64c99caf3287de1f11fef52523ad5d812527048.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/f2/f23230ccff8b04b1bc654c116c9dae559244ed5c.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\9e\9e98da4040ca902f6a69ba2e208479da92c21072.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\9e\9e98da4040ca902f6a69ba2e208479da92c21072.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/9e/9e98da4040ca902f6a69ba2e208479da92c21072.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/d6/d64c99caf3287de1f11fef52523ad5d812527048.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e3\e3e9e6cc8498998ec9a359a29bbd67e5325f18a6.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e3\e3e9e6cc8498998ec9a359a29bbd67e5325f18a6.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register GizmoManager
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/e3/e3e9e6cc8498998ec9a359a29bbd67e5325f18a6.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\7f\7faf2e43a321c3c1a125f57fa1812daa6a9e334f.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\7f\7faf2e43a321c3c1a125f57fa1812daa6a9e334f.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\c4\c41a9c31b8275775b257188d7587bba68c0ae7b1.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\c4\c41a9c31b8275775b257188d7587bba68c0ae7b1.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\7c\7c8164ef85ecc97742f9c4c1a1cb99eb36d4908a.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\7c\7c8164ef85ecc97742f9c4c1a1cb99eb36d4908a.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\49\49b6312c83bff34f9f47dc328a83633ffabd575f.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\49\49b6312c83bff34f9f47dc328a83633ffabd575f.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\41\41cd73be68eabcc1cac24b8cc6301d64aca50e3d.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\41\41cd73be68eabcc1cac24b8cc6301d64aca50e3d.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\9a\9a9278044bb320de63d83dee6bd2c69924eb1e4f.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\9a\9a9278044bb320de63d83dee6bd2c69924eb1e4f.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\6a\6a0c1df255fa2bfb0e32d0364e23dcfc018d0664.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\6a\6a0c1df255fa2bfb0e32d0364e23dcfc018d0664.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b0\b09cd97fe2cb43a7b5c53aaa1e3a7c89d31e63fc.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\b0\b09cd97fe2cb43a7b5c53aaa1e3a7c89d31e63fc.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\af\af5a9c10309a49d7a1199fd0ac923a693f443e59.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\af\af5a9c10309a49d7a1199fd0ac923a693f443e59.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\01\019ce000f7f1fcb1c79b33b69727b97422d130e4.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\01\019ce000f7f1fcb1c79b33b69727b97422d130e4.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ba\ba58fb7d26b25787d6719b24919717aff61d4a43.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ba\ba58fb7d26b25787d6719b24919717aff61d4a43.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register LayerEditorRandomRange
2025-10-8 15:17:31-debug: [[Executor]] Register LevelRandTerrainUI
2025-10-8 15:17:31-debug: [[Executor]] Register LevelScrollLayerUI
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\02\0229a09a318268a3f9f18732d54e2001c3b06889.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\02\0229a09a318268a3f9f18732d54e2001c3b06889.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register LevelLayer
2025-10-8 15:17:31-debug: [[Executor]] Register LevelRandTerrainsLayerUI
2025-10-8 15:17:31-debug: [[Executor]] Register LevelRandTerrainsLayersUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/7c/7c8164ef85ecc97742f9c4c1a1cb99eb36d4908a.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register LevelBackgroundLayer
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/9a/9a9278044bb320de63d83dee6bd2c69924eb1e4f.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register LevelEditorLayerUI
2025-10-8 15:17:31-debug: [[Executor]] Register LevelEditorPrefabParse
2025-10-8 15:17:31-debug: [[Executor]] Register LevelEditorElemUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/c4/c41a9c31b8275775b257188d7587bba68c0ae7b1.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register PathPointEditor
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/af/af5a9c10309a49d7a1199fd0ac923a693f443e59.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register PathEditor
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/02/0229a09a318268a3f9f18732d54e2001c3b06889.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register WavePreview
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/41/41cd73be68eabcc1cac24b8cc6301d64aca50e3d.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/b0/b09cd97fe2cb43a7b5c53aaa1e3a7c89d31e63fc.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register LevelEditorWaveGroup
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/ba/ba58fb7d26b25787d6719b24919717aff61d4a43.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register LevelEditorWave
2025-10-8 15:17:31-debug: [[Executor]] Register LevelEditorEventTrigger
2025-10-8 15:17:31-debug: [[Executor]] Register LevelEditorCondition
2025-10-8 15:17:31-debug: [[Executor]] Register LevelEditorEventUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/01/019ce000f7f1fcb1c79b33b69727b97422d130e4.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/49/49b6312c83bff34f9f47dc328a83633ffabd575f.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register LevelEditorBaseUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/6a/6a0c1df255fa2bfb0e32d0364e23dcfc018d0664.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register LevelEditorEventShadowUI
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/7f/7faf2e43a321c3c1a125f57fa1812daa6a9e334f.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ae\ae1b06f8151371006232ccd5a90197f46fa687cb.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ae\ae1b06f8151371006232ccd5a90197f46fa687cb.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/ae/ae1b06f8151371006232ccd5a90197f46fa687cb.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register LevelEditorUI
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\68\68345f04f399412466fe8f33f500c3d0a76e4ed4.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\68\68345f04f399412466fe8f33f500c3d0a76e4ed4.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\20\2008ef90ba12a1fbf9144f9c206edfd8363bfa6e.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\20\2008ef90ba12a1fbf9144f9c206edfd8363bfa6e.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register FormationPointEditor
2025-10-8 15:17:31-debug: [[Executor]] Register FormationEditor
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/20/2008ef90ba12a1fbf9144f9c206edfd8363bfa6e.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/68/68345f04f399412466fe8f33f500c3d0a76e4ed4.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\77\7703db237abf07e429c42aaafdd49b91e206e1da.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\77\7703db237abf07e429c42aaafdd49b91e206e1da.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/77/7703db237abf07e429c42aaafdd49b91e206e1da.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register PlaneView
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\00\003496f4e0dca3b1df8cfaa9e3d21cb19cbf60cd.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\00\003496f4e0dca3b1df8cfaa9e3d21cb19cbf60cd.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/00/003496f4e0dca3b1df8cfaa9e3d21cb19cbf60cd.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e9\e9b2951d675b23d7223888db04a52de1c81e74d3.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\e9\e9b2951d675b23d7223888db04a52de1c81e74d3.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/e9/e9b2951d675b23d7223888db04a52de1c81e74d3.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\1d\1ddba001cbfd7d9fb907af9b0311529529fea1aa.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\1d\1ddba001cbfd7d9fb907af9b0311529529fea1aa.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\4c\4c66674e3bc8bb075d68c882061879b9bbfde183.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\4c\4c66674e3bc8bb075d68c882061879b9bbfde183.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\73\735cb9a320bed89f03010d466fdb5f5757475af8.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\73\735cb9a320bed89f03010d466fdb5f5757475af8.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/4c/4c66674e3bc8bb075d68c882061879b9bbfde183.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/73/735cb9a320bed89f03010d466fdb5f5757475af8.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/1d/1ddba001cbfd7d9fb907af9b0311529529fea1aa.js" loaded.
2025-10-8 15:17:31-log: start init_cs_proto.js
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\40\406fe018c5000ba08084b27d94fbd4bef2f34f3c.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\40\406fe018c5000ba08084b27d94fbd4bef2f34f3c.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register ResUpdate
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/40/406fe018c5000ba08084b27d94fbd4bef2f34f3c.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\43\43db00c3c4fd1f269121d5fdd57302f4949c5953.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\43\43db00c3c4fd1f269121d5fdd57302f4949c5953.js is not in module cache!
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ad\ad27b4b6f8661dcd5de180e61f0b9450958f80fa.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\ad\ad27b4b6f8661dcd5de180e61f0b9450958f80fa.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/43/43db00c3c4fd1f269121d5fdd57302f4949c5953.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Register LocalizedLabel
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/ad/ad27b4b6f8661dcd5de180e61f0b9450958f80fa.js" loaded.
2025-10-8 15:17:31-debug: E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\64\64671a7b62aeef77cd5d4dd690d95e66e1ee1b78.js resolved to E:\M2Game\Client\temp\programming\packer-driver\targets\editor\chunks\64\64671a7b62aeef77cd5d4dd690d95e66e1ee1b78.js is not in module cache!
2025-10-8 15:17:31-debug: [[Executor]] Register LocalizedSpriteItem
2025-10-8 15:17:31-debug: [[Executor]] Register LocalizedSprite
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/64/64671a7b62aeef77cd5d4dd690d95e66e1ee1b78.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] Module "pack:///chunks/6d/6d8fd2b0177941b032ddc0733af48a561fb60657.js" loaded.
2025-10-8 15:17:31-debug: [[Executor]] after unlock
2025-10-8 15:17:31-debug: Incremental keys: regeneratorRuntime,DataMgr,_languageData,languages
2025-10-8 15:17:31-debug: Init bundle share assets start..., progress: 13%
2025-10-8 15:17:31-debug: Init bundle share assets start..., progress: 9%
2025-10-8 15:17:31-warn: The SpriteFrame used by component "cc.Sprite" in prefab "yy1_1_5" is missing. Detailed information:
Node path: "yy1_1_5/Sprite"
Asset url: "db://assets/resources/game/level/background/yy1_1_5"
Missing uuid: "ddfcd00f-37e5-4c11-8129-247627e7392a@f9941"


2025-10-8 15:17:31-warn: The sp.SkeletonData used by component "sp.Skeleton" in prefab "Boom" is missing. Detailed information:
Node path: "Boom"
Asset url: "db://assets/resources/game/prefabs/effect/Boom"
Missing uuid: "4c6c1a86-f966-4a22-b8b6-afd6a6a25028"


2025-10-8 15:17:31-warn: The SpriteFrame used by component "cc.Sprite" in prefab "test_1" is missing. Detailed information:
Node path: "test_1"
Asset url: "db://assets/resources/game/prefabs/bullet/test_1"
Missing uuid: "a6099a5a-ad14-4dae-a2e9-269bb8c5c187@f9941"


2025-10-8 15:17:31-warn: The Prefab used by component "Emitter" in prefab "Bullet_211501001" is missing. Detailed information:
Node path: "Bullet_211501001"
Asset url: "db://assets/resources/game/prefabs/emitter/Bullet_211501001"
Missing uuid: "75ee2b3e-4f4c-4f94-8a76-56741b2de07d"


2025-10-8 15:17:31-warn: The SpriteFrame used by component "cc.ParticleSystem2D" in prefab "Hurt" is missing. Detailed information:
Node path: "Hurt"
Asset url: "db://assets/resources/game/prefabs/effect/Hurt"
Missing uuid: "c42707d5-f97f-46ab-831d-aaefb65bc046@fe5fd"


2025-10-8 15:17:31-warn: The Prefab used by component "Emitter" in prefab "Bullet_212501001" is missing. Detailed information:
Node path: "Bullet_212501001"
Asset url: "db://assets/resources/game/prefabs/emitter/Bullet_212501001"
Missing uuid: "75ee2b3e-4f4c-4f94-8a76-56741b2de07d"


2025-10-8 15:17:31-warn: The Prefab used by component "Emitter" in prefab "Bullet_213501001" is missing. Detailed information:
Node path: "Bullet_213501001"
Asset url: "db://assets/resources/game/prefabs/emitter/Bullet_213501001"
Missing uuid: "75ee2b3e-4f4c-4f94-8a76-56741b2de07d"


2025-10-8 15:17:31-warn: The Prefab used by component "Emitter" in prefab "Bullet_211501002" is missing. Detailed information:
Node path: "Bullet_211501002"
Asset url: "db://assets/resources/game/prefabs/emitter/Bullet_211501002"
Missing uuid: "75ee2b3e-4f4c-4f94-8a76-56741b2de07d"


2025-10-8 15:17:31-warn: The Prefab used by component "Emitter" in prefab "Bullet_214501001" is missing. Detailed information:
Node path: "Bullet_214501001"
Asset url: "db://assets/resources/game/prefabs/emitter/Bullet_214501001"
Missing uuid: "75ee2b3e-4f4c-4f94-8a76-56741b2de07d"


2025-10-8 15:17:31-warn: The Prefab used by component "Emitter" in prefab "Bullet_214501002" is missing. Detailed information:
Node path: "Bullet_214501002"
Asset url: "db://assets/resources/game/prefabs/emitter/Bullet_214501002"
Missing uuid: "75ee2b3e-4f4c-4f94-8a76-56741b2de07d"


2025-10-8 15:17:31-warn: The Prefab used by component "Emitter" in prefab "Bullet_213501002" is missing. Detailed information:
Node path: "Bullet_213501002"
Asset url: "db://assets/resources/game/prefabs/emitter/Bullet_213501002"
Missing uuid: "75ee2b3e-4f4c-4f94-8a76-56741b2de07d"


2025-10-8 15:17:31-warn: The Prefab used by component "Emitter" in prefab "Bullet_215501002" is missing. Detailed information:
Node path: "Bullet_215501002"
Asset url: "db://assets/resources/game/prefabs/emitter/Bullet_215501002"
Missing uuid: "75ee2b3e-4f4c-4f94-8a76-56741b2de07d"


2025-10-8 15:17:31-warn: The Prefab used by component "Emitter" in prefab "Bullet_215501001" is missing. Detailed information:
Node path: "Bullet_215501001"
Asset url: "db://assets/resources/game/prefabs/emitter/Bullet_215501001"
Missing uuid: "75ee2b3e-4f4c-4f94-8a76-56741b2de07d"


2025-10-8 15:17:31-warn: The Prefab used by node "New Node" in prefab "RandNod_mount_08" is missing. Detailed information:
Node path: "RandNod_mount_08/New Node"
Asset url: "db://assets/resources/game/level/background/Prefab/HighSky/RandNod/RandNod_mount_08"
Missing uuid: "3d2365f8-50af-4528-a96a-961db2fa169b"


2025-10-8 15:17:31-warn: The Prefab used by component "Emitter" in prefab "Bullet_215501003" is missing. Detailed information:
Node path: "Bullet_215501003"
Asset url: "db://assets/resources/game/prefabs/emitter/Bullet_215501003"
Missing uuid: "75ee2b3e-4f4c-4f94-8a76-56741b2de07d"


2025-10-8 15:17:31-debug: Init bundle share assets success..., progress: 9%
2025-10-8 15:17:31-debug: Init bundle share assets success..., progress: 13%
2025-10-8 15:17:31-debug: handle json group in bundle internal
2025-10-8 15:17:31-debug: handle json group in bundle resources
2025-10-8 15:17:31-debug: handle json group in bundle main
2025-10-8 15:17:31-debug: handle json group in bundle common
2025-10-8 15:17:31-debug: handle json group in bundle editor
2025-10-8 15:17:31-debug: handle json group in bundle internal success
2025-10-8 15:17:31-debug: handle json group in bundle home
2025-10-8 15:17:31-debug: handle json group in bundle home_mail
2025-10-8 15:17:31-debug: handle json group in bundle home_friend
2025-10-8 15:17:31-debug: handle json group in bundle gm
2025-10-8 15:17:31-debug: handle json group in bundle home_match
2025-10-8 15:17:31-debug: handle json group in bundle home_shop
2025-10-8 15:17:31-debug: handle json group in bundle home_plane
2025-10-8 15:17:31-debug: handle json group in bundle home_skyisland
2025-10-8 15:17:31-debug: handle json group in bundle home_talent
2025-10-8 15:17:31-debug: handle json group in bundle home_pk
2025-10-8 15:17:31-debug: handle json group in bundle home_story
2025-10-8 15:17:31-debug: init image compress task 0 in bundle internal
2025-10-8 15:17:31-debug: handle json group in bundle home_task
2025-10-8 15:17:31-debug: handle json group in bundle home_shop success
2025-10-8 15:17:31-debug: handle json group in bundle luban
2025-10-8 15:17:31-debug: init image compress task 0 in bundle home_shop
2025-10-8 15:17:31-debug: handle json group in bundle home_talent success
2025-10-8 15:17:31-debug: init image compress task 0 in bundle home_skyisland
2025-10-8 15:17:31-debug: handle json group in bundle home_skyisland success
2025-10-8 15:17:31-debug: handle json group in bundle home_mail success
2025-10-8 15:17:31-debug: init image compress task 0 in bundle home_mail
2025-10-8 15:17:31-debug: init image compress task 0 in bundle home_talent
2025-10-8 15:17:31-debug: init image compress task 0 in bundle gm
2025-10-8 15:17:31-debug: handle json group in bundle gm success
2025-10-8 15:17:31-debug: init image compress task 0 in bundle home_plane
2025-10-8 15:17:31-debug: handle json group in bundle home_plane success
2025-10-8 15:17:31-debug: init image compress task 0 in bundle home_match
2025-10-8 15:17:31-debug: handle json group in bundle home_match success
2025-10-8 15:17:31-debug: handle json group in bundle home_friend success
2025-10-8 15:17:31-debug: handle json group in bundle luban success
2025-10-8 15:17:31-debug: init image compress task 0 in bundle home_friend
2025-10-8 15:17:31-debug: handle json group in bundle editor success
2025-10-8 15:17:31-debug: handle json group in bundle home_task success
2025-10-8 15:17:31-debug: init image compress task 0 in bundle home_task
2025-10-8 15:17:31-debug: init image compress task 0 in bundle editor
2025-10-8 15:17:31-debug: handle json group in bundle home_story success
2025-10-8 15:17:31-debug: init image compress task 0 in bundle luban
2025-10-8 15:17:31-debug: init image compress task 0 in bundle home_story
2025-10-8 15:17:31-debug: init image compress task 0 in bundle common
2025-10-8 15:17:31-debug: handle json group in bundle main success
2025-10-8 15:17:31-debug: handle json group in bundle common success
2025-10-8 15:17:31-debug: handle json group in bundle home_pk success
2025-10-8 15:17:31-debug: init image compress task 0 in bundle main
2025-10-8 15:17:31-debug: init image compress task 0 in bundle home_pk
2025-10-8 15:17:31-debug: handle json group in bundle resources success
2025-10-8 15:17:31-debug: init image compress task 0 in bundle home
2025-10-8 15:17:31-debug: handle json group in bundle home success
2025-10-8 15:17:31-debug: init image compress task 0 in bundle resources
2025-10-8 15:17:31-debug: // ---- build task 查询 Asset Bundle ---- (601ms)
2025-10-8 15:17:31-log: run build task 查询 Asset Bundle success in 601 ms√, progress: 14%
2025-10-8 15:17:31-debug: [Build Memory track]: 查询 Asset Bundle start:215.95MB, end 236.47MB, increase: 20.52MB
2025-10-8 15:17:31-debug: // ---- build task 查询 Asset Bundle ----
2025-10-8 15:17:31-debug: 查询 Asset Bundle start, progress: 14%
2025-10-8 15:17:31-debug: // ---- build task 查询 Asset Bundle ---- (10ms)
2025-10-8 15:17:31-log: run build task 查询 Asset Bundle success in 10 ms√, progress: 19%
2025-10-8 15:17:31-debug: 打包脚本 start, progress: 19%
2025-10-8 15:17:31-debug: // ---- build task 打包脚本 ----
2025-10-8 15:17:31-log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-10-8 15:17:31-debug: [Build Memory track]: 查询 Asset Bundle start:236.50MB, end 236.93MB, increase: 438.73KB
2025-10-8 15:17:32-log: [build-script]enter sub process 949508, C:\ProgramData\cocos\editors\Creator\3.8.6\CocosCreator.exe,C:\ProgramData\cocos\editors\Creator\3.8.6\resources\app.asar\builtin\builder\static\sub-process-index


2025-10-8 15:17:33-debug: excute-script over with build-script 2274ms
2025-10-8 15:17:33-debug: Generate systemJs..., progress: 19%
2025-10-8 15:17:33-log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-10-8 15:17:36-debug: excute-script over with build-script 2049ms
2025-10-8 15:17:36-debug: 构建项目脚本 start..., progress: 19%
2025-10-8 15:17:36-debug: Build script in bundle start, progress: 19%
2025-10-8 15:17:36-debug: Build script in bundle start, progress: 13%
2025-10-8 15:17:36-log: Run build task(build-script) in child, see: chrome://inspect/#devices
2025-10-8 15:17:36-log: [build-script]Create cjs interop url for 'file:///E:/M2Game/Client/assets/scripts/AAA/init_cs_proto.js'


2025-10-8 15:17:36-log: [build-script]Create cjs interop url for 'file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js'


2025-10-8 15:17:40-log: [build-script]Create cjs interop url for 'file:///E:/M2Game/Client/assets/scripts/AAA/init_cs_proto.js'


2025-10-8 15:17:40-log: [build-script]Create cjs interop url for 'file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js'


2025-10-8 15:17:40-log: [build-script]Create cjs interop url for 'file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js'


2025-10-8 15:17:40-log: [build-script]Create cjs interop url for 'file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js'


2025-10-8 15:17:40-log: [build-script]Create cjs interop url for 'file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js'


2025-10-8 15:17:40-log: [build-script]Create cjs interop url for 'file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js'


2025-10-8 15:17:40-log: [build-script]Create cjs interop url for 'file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js'


2025-10-8 15:17:40-log: [build-script]Create cjs interop url for 'file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js'


2025-10-8 15:17:40-log: [build-script]Create cjs interop url for 'file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js'


2025-10-8 15:17:40-log: [build-script]Create cjs interop url for 'file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js'


2025-10-8 15:17:40-log: [build-script]Create cjs interop url for 'file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js'


2025-10-8 15:17:40-log: [build-script]Create cjs interop url for 'file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js'


2025-10-8 15:17:40-log: [build-script]Create cjs interop url for 'file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js'


2025-10-8 15:17:40-log: [build-script]Create cjs interop url for 'file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js'


2025-10-8 15:17:40-log: [build-script]Create cjs interop url for 'file:///E:/M2Game/Client/node_modules/crypto-js/index.js'


2025-10-8 15:17:40-log: [build-script]Create cjs interop url for 'file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js'


2025-10-8 15:17:40-log: [build-script]Create cjs interop url for 'file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js'


2025-10-8 15:17:40-log: [build-script]Create cjs interop url for 'file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js'


2025-10-8 15:17:40-log: [build-script]Create cjs interop url for 'file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js'


2025-10-8 15:17:40-log: [build-script]Create cjs interop url for 'file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js'


2025-10-8 15:17:40-log: [build-script]Create cjs interop url for 'file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js'


2025-10-8 15:17:40-log: [build-script]Create cjs interop url for 'file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js'


2025-10-8 15:17:40-log: [build-script]Create cjs interop url for 'file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js'


2025-10-8 15:17:40-log: [build-script]Create cjs interop url for 'file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js'


2025-10-8 15:17:40-log: [build-script]Create cjs interop url for 'file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js'


2025-10-8 15:17:40-log: [build-script]Create cjs interop url for 'file:///E:/M2Game/Client/assets/bundles/common/script/autogen/pb/cs_proto.js'


2025-10-8 15:17:41-debug: [build-script][BABEL] Note: The code generator has deoptimised the styling of C:\ProgramData\cocos\editors\Creator\3.8.6\file:\E:\M2Game\Client\assets\bundles\common\script\autogen\pb\cs_proto.js as it exceeds the max of 500KB.


2025-10-8 15:17:42-warn: [build-script][[Build.Script.Rollup]] Circular dependency: file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/audio/audioManager.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/data/DataManager.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/data/bag/Bag.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/data/DataManager.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/data/base/Role.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/messagebox/MessageBox.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/LevelUPUI.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts


2025-10-8 15:17:42-warn: [build-script][[Build.Script.Rollup]] Circular dependency: file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/data/DataManager.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/data/base/Role.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/messagebox/MessageBox.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/LevelUPUI.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/base/ItemQuaIcon.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/data/DataManager.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/data/base/Role.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/data/DataManager.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/Equip.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/EquipCombine.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/data/DataManager.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/Equip.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/EquipSlots.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/data/DataManager.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/data/friend/Friend.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/data/DataManager.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/data/game_logic/GameLogic.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///E:/M2Game/Client/assets/bundles/common/script/data/DataManager.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/data/game_logic/GameLogic.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/data/DataManager.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/data/DataManager.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/data/game_mode/GameMode.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/data/DataManager.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/data/gm/GM.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/data/DataManager.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/data/mail/Mail.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/data/DataManager.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/data/pk/PK.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/network/NetMgr.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/data/DataManager.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/data/task/Task.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/plane/PlaneManager.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/ui/Plane.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/plane/PlaneManager.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/app/MyApp.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>///E:/M2Game/Client/assets/bundles/common/script/game/GameInsStart.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/game/manager/BattleManager.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>
[warning][[Build.Script.Rollup]] Circular dependency: file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/BulletSystem.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/EventGroup.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/actions/EmitterEventActions.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/Emitter.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/BulletData.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/game/move/IMovable.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/BulletSystem.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/BulletSystem.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/EventGroup.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/actions/EmitterEventActions.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/Emitter.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/Bullet.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/BulletSystem.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/EventGroup.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/actions/EmitterEventActions.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/Emitter.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/Bullet.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/EventGroup.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/BulletSystem.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/EventGroup.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/actions/EmitterEventActions.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/Emitter.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/BulletSystem.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/EventGroup.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/actions/EmitterEventActions.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/Emitter.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/EventGroup.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/BulletSystem.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/EventGroup.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/game/bullet/BulletSystem.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/GameMapRun.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelBaseUI.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelLayerUI.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelEventRun.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/GameMapRun.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendUI.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>///E:/M2Game/Client/assets/bundles/common/script/ui/friend/FriendUI.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/mainPlane/MainPlane.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/mainPlane/MainPlaneDebug.ts -> file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/mainPlane/MainPlane.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///E:/M2Game/Client/assets/editor/level/LevelEditorBaseUI.ts -> file:///E:/M2Game/Client/assets/editor/level/LevelEditorEventShadowUI.ts -> file:///E:/M2Game/Client/assets/editor/level/LevelEditorElemUI.ts -> file:///E:/M2Game/Client/assets/editor/level/LevelEditorBaseUI.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///E:/M2Game/Client/assets/editor/level/wave/PathEditor.ts -> file:///E:/M2Game/Client/assets/editor/level/wave/PathPointEditor.ts -> file:///E:/M2Game/Client/assets/editor/level/wave/PathEditor.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///E:/M2Game/Client/assets/editor/level/LevelEditorEventUI.ts -> file:///E:/M2Game/Client/assets/editor/level/preview/WavePreview.ts -> file:///E:/M2Game/Client/assets/editor/level/LevelEditorEventUI.ts
[warning][[Build.Script.Rollup]] Circular dependency: file:///E:/M2Game/Client/assets/bundles/gm/script/ui/GmButtonUI.ts -> file:///E:/M2Game/Client/assets/bundles/gm/script/ui/GmUI.ts -> file:///E:/M2Game/Client/assets/bundles/gm/script/ui/GmButtonUI.ts


2025-10-8 15:17:42-warn: [build-script][[Build.Script.Rollup]] Use of eval is strongly discouraged, as it poses security risks and may cause issues with minification


2025-10-8 15:17:47-debug: excute-script over with build-script 10964ms
2025-10-8 15:17:47-debug: Copy externalScripts success!
2025-10-8 15:17:47-debug: Build script in bundle success, progress: 19%
2025-10-8 15:17:47-debug: Build script in bundle success, progress: 13%
2025-10-8 15:17:47-debug: 构建项目脚本 in (11026 ms) √, progress: 19%
2025-10-8 15:17:47-debug: Copy plugin script ..., progress: 19%
2025-10-8 15:17:47-debug: Generate import-map..., progress: 19%
2025-10-8 15:17:47-log: run build task 打包脚本 success in 15 s√, progress: 24%
2025-10-8 15:17:47-debug: // ---- build task 打包脚本 ---- (15416ms)
2025-10-8 15:17:47-debug: Build Assets start, progress: 24%
2025-10-8 15:17:47-debug: // ---- build task Build Assets ----
2025-10-8 15:17:47-debug: [Build Memory track]: 打包脚本 start:237.13MB, end 239.04MB, increase: 1.90MB
2025-10-8 15:17:47-debug: Build bundles..., progress: 24%
2025-10-8 15:17:47-debug: Pack Images start, progress: 13%
2025-10-8 15:17:47-debug: Pack Images start, progress: 24%
2025-10-8 15:17:47-debug: Start trim sprite image ...
2025-10-8 15:17:47-debug: Start trim sprite image ...
2025-10-8 15:17:47-debug: Start trim sprite image ...
2025-10-8 15:17:47-debug: Start trim sprite image ...
2025-10-8 15:17:47-debug: Start trim sprite image ...
2025-10-8 15:17:47-debug: Start trim sprite image ...
2025-10-8 15:17:47-debug: Start trim sprite image ...
2025-10-8 15:17:47-debug: Start trim sprite image ...
2025-10-8 15:17:47-debug: determine atlas size...
2025-10-8 15:17:47-debug: Start generate atlas image...
2025-10-8 15:17:47-debug: determine atlas size...
2025-10-8 15:17:47-debug: Start generate atlas image...
2025-10-8 15:17:47-debug: determine atlas size...
2025-10-8 15:17:47-debug: Start generate atlas image...
2025-10-8 15:17:47-debug: determine atlas size...
2025-10-8 15:17:47-debug: Start generate atlas image...
2025-10-8 15:17:47-debug: determine atlas size...
2025-10-8 15:17:47-debug: Start generate atlas image...
2025-10-8 15:17:47-debug: determine atlas size...
2025-10-8 15:17:47-debug: Start generate atlas image...
2025-10-8 15:17:47-debug: determine atlas size...
2025-10-8 15:17:47-debug: Start generate atlas image...
2025-10-8 15:17:47-debug: determine atlas size...
2025-10-8 15:17:47-debug: Start generate atlas image...
2025-10-8 15:17:47-debug: Asset {asset(db://assets/bundles/home/<USER>/settlement_ui/settlement-auto-atlas.pac)} is Bundle
2025-10-8 15:17:47-debug: Asset {asset(db://assets/bundles/home/<USER>/statistics_ui/statistics-auto-atlas.pac)} is Bundle
2025-10-8 15:17:47-debug: Asset {asset(db://assets/bundles/home/<USER>/home_ui/home-panel-atlas.pac)} is Bundle
2025-10-8 15:17:47-warn: 图集（UUID：1b5574076）同时被 db://assets/bundles/home 和 db://assets/bundles/home_match,db://assets/bundles/home_pk,db://assets/bundles/home_story 引用。这将导致图集被拷贝多份并包含在打包文件中，增加包体的大小。
考虑以下优化选项来优化您的 bundle 配置并减小包体大小： 
    1. 调整 bundle 的优先级，确保 Atlas 图集仅被包含在优先的 bundle 中。
    2. 仔细检查图集在各个 bundle 中的使用情况，并在可能的情况下将其合并到单个 bundle 中。

2025-10-8 15:17:47-warn: 图集（UUID：1116636f8）同时被 db://assets/bundles/home 和 db://assets/bundles/home_story,db://assets/bundles/home_task 引用。这将导致图集被拷贝多份并包含在打包文件中，增加包体的大小。
考虑以下优化选项来优化您的 bundle 配置并减小包体大小： 
    1. 调整 bundle 的优先级，确保 Atlas 图集仅被包含在优先的 bundle 中。
    2. 仔细检查图集在各个 bundle 中的使用情况，并在可能的情况下将其合并到单个 bundle 中。

2025-10-8 15:17:47-debug: builder:pack-auto-atlas-image (667ms)
2025-10-8 15:17:47-debug: Compress image start..., progress: 24%
2025-10-8 15:17:47-debug: Pack Images success, progress: 24%
2025-10-8 15:17:47-debug: Pack Images success, progress: 13%
2025-10-8 15:17:47-debug: Compress image start..., progress: 13%
2025-10-8 15:17:47-debug: sort compress task {}
2025-10-8 15:17:47-debug: Num of all image compress task 0, really: 0, configTasks: 0
2025-10-8 15:17:47-debug: Compress image success..., progress: 13%
2025-10-8 15:17:47-debug: Compress image success..., progress: 24%
2025-10-8 15:17:47-debug: No image need to compress
2025-10-8 15:17:47-debug: Output asset in bundles start, progress: 24%
2025-10-8 15:17:47-debug: Output asset in bundles start, progress: 13%
2025-10-8 15:17:47-debug: handle json group
2025-10-8 15:17:47-debug: Handle all json groups in bundle internal
2025-10-8 15:17:47-debug: Handle all json groups in bundle resources
2025-10-8 15:17:47-debug: handle json group
2025-10-8 15:17:47-debug: Handle all json groups in bundle main
2025-10-8 15:17:47-debug: handle json group
2025-10-8 15:17:47-debug: Handle all json groups in bundle common
2025-10-8 15:17:47-debug: handle json group
2025-10-8 15:17:47-debug: handle json group
2025-10-8 15:17:47-debug: Handle all json groups in bundle editor
2025-10-8 15:17:47-debug: Handle all json groups in bundle gm
2025-10-8 15:17:47-debug: handle json group
2025-10-8 15:17:47-debug: handle json group
2025-10-8 15:17:47-debug: Handle all json groups in bundle home
2025-10-8 15:17:47-debug: Handle all json groups in bundle home_friend
2025-10-8 15:17:47-debug: handle json group
2025-10-8 15:17:47-debug: Handle all json groups in bundle home_mail
2025-10-8 15:17:47-debug: Handle all json groups in bundle home_match
2025-10-8 15:17:47-debug: handle json group
2025-10-8 15:17:47-debug: handle json group
2025-10-8 15:17:47-debug: Handle all json groups in bundle home_pk
2025-10-8 15:17:47-debug: handle json group
2025-10-8 15:17:47-debug: Handle all json groups in bundle home_plane
2025-10-8 15:17:47-debug: handle json group
2025-10-8 15:17:47-debug: Handle all json groups in bundle home_shop
2025-10-8 15:17:47-debug: handle json group
2025-10-8 15:17:47-debug: handle single json
2025-10-8 15:17:47-debug: handle json group
2025-10-8 15:17:47-debug: Handle all json groups in bundle home_skyisland
2025-10-8 15:17:47-debug: handle single json
2025-10-8 15:17:47-debug: Handle all json groups in bundle home_story
2025-10-8 15:17:47-debug: handle json group
2025-10-8 15:17:47-debug: Handle all json groups in bundle home_talent
2025-10-8 15:17:47-debug: handle json group
2025-10-8 15:17:47-debug: handle single json
2025-10-8 15:17:47-debug: Handle all json groups in bundle home_task
2025-10-8 15:17:47-debug: handle json group
2025-10-8 15:17:47-debug: Handle all json groups in bundle luban
2025-10-8 15:17:47-debug: handle json group
2025-10-8 15:17:47-debug: handle single json
2025-10-8 15:17:47-debug: Json group(079baee3b) compile success，json number: 6
2025-10-8 15:17:47-debug: Json group(094780fa3) compile success，json number: 6
2025-10-8 15:17:47-debug: handle single json
2025-10-8 15:17:47-debug: Json group(02c0b50a6) compile success，json number: 6
2025-10-8 15:17:47-debug: handle single json
2025-10-8 15:17:47-debug: Json group(0a7eaa322) compile success，json number: 6
2025-10-8 15:17:47-debug: Json group(06b6661cf) compile success，json number: 6
2025-10-8 15:17:47-debug: Json group(0694f943a) compile success，json number: 6
2025-10-8 15:17:47-debug: Json group(05fdb1ea7) compile success，json number: 6
2025-10-8 15:17:47-debug: Json group(08e9ac0ea) compile success，json number: 6
2025-10-8 15:17:47-debug: handle single json
2025-10-8 15:17:47-debug: Json group(0db77052d) compile success，json number: 6
2025-10-8 15:17:47-debug: Json group(0c0b7400e) compile success，json number: 6
2025-10-8 15:17:47-debug: handle single json
2025-10-8 15:17:47-debug: Json group(0d2ab0883) compile success，json number: 6
2025-10-8 15:17:47-debug: Json group(0e32404cf) compile success，json number: 6
2025-10-8 15:17:47-debug: handle single json
2025-10-8 15:17:47-debug: Json group(0f530035a) compile success，json number: 6
2025-10-8 15:17:47-debug: handle single json
2025-10-8 15:17:47-debug: Json group(081300434) compile success，json number: 6
2025-10-8 15:17:47-debug: Json group(041ae6e93) compile success，json number: 6
2025-10-8 15:17:47-debug: Json group(046987440) compile success，json number: 6
2025-10-8 15:17:47-debug: handle single json
2025-10-8 15:17:47-debug: Json group(0ae53dbc3) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(08e9ac0ea) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(05d015c8b) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0c321c462) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(046987440) compile success，json number: 6
2025-10-8 15:17:48-debug: handle single json
2025-10-8 15:17:48-debug: Json group(0ba081319) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(017a4e952) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0973af27d) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0fadba2d5) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0b64d5e39) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0e67deda5) compile success，json number: 6
2025-10-8 15:17:48-debug: handle single json
2025-10-8 15:17:48-debug: Json group(07e6f3291) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0139d9e4a) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(01466fad9) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0784c2bb0) compile success，json number: 6
2025-10-8 15:17:48-debug: handle single json
2025-10-8 15:17:48-debug: Json group(078e9ef34) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(06585a170) compile success，json number: 6
2025-10-8 15:17:48-debug: handle single json
2025-10-8 15:17:48-debug: Json group(08e9ac0ea) compile success，json number: 6
2025-10-8 15:17:48-debug: handle single json
2025-10-8 15:17:48-debug: Json group(05032501c) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(066af4b70) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(01eade86e) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(036dfc556) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(081a1693d) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0d9de0e33) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(044e1397a) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0bb3ebb71) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0b171a3a2) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(02a8f9232) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0d3c8e100) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(08e9ac0ea) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0c45e3f98) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(06d07bd11) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(046987440) compile success，json number: 6
2025-10-8 15:17:48-debug: handle single json
2025-10-8 15:17:48-debug: Json group(091f23692) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(04ae6fd4b) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0515cf8b8) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0a9df0c9e) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0383475f9) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(02004b379) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0d26fdb37) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0b2d9a7ca) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(03de57a0d) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(07d20f1f3) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(08451f7e4) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(05126142e) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(096dfd230) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0616565a7) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0761669ec) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0226fb600) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0749f7464) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0113cd950) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(05335d1cf) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0156c5da0) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(06cb6e232) compile success，json number: 6
2025-10-8 15:17:48-warn: The Prefab used by node "New Node" in prefab "RandNod_mount_08" is missing. Detailed information:
Node path: "RandNod_mount_08/New Node"
Asset url: "db://assets/resources/game/level/background/Prefab/HighSky/RandNod/RandNod_mount_08"
Missing uuid: "3d2365f8-50af-4528-a96a-961db2fa169b"


2025-10-8 15:17:48-debug: Json group(038ff5102) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(01993693d) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0a886d2d8) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0a3495154) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0d709ea09) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(06ab5432f) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0432d4ec0) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0b645ba04) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(07af0fb6f) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(02c20e680) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0d10d573f) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0827ed2fd) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(08246c1cd) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0cfcb934e) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0599dd400) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0fbcbf670) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(05f26342a) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0a820032a) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0c3c23537) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0ec13b7b1) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(078b89556) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0702ffda2) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(059bce80c) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0557071ea) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0f4370f94) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(047ad3740) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0a8b41880) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0ee15e949) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0c45dd745) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(089ec88f0) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(06593d6d5) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0c85ed9c4) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(085bead68) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(09433a367) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0d06f6679) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0ec0be733) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(05ad39782) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0dbe0c754) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(06e860cb0) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0f570ffa1) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(066b85e97) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0a2dd3605) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(04fd32595) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0379828a5) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(09f3ce4b1) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(08dd3ad68) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(041d55af0) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0c07a8c0a) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(047099cdb) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(060469a79) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0a9dbdeae) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0dbf32ad9) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(012cdca69) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0ff618c3f) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(02289a28e) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(05a220f31) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(01b15bb35) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(03dd70ebf) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(013c109df) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(05e8fa1a7) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0c383de87) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(03639ccbf) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(07701a33c) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0daa58b00) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0dba05c80) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0e01dac56) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0627fe977) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0381d43aa) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0f9554460) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(096a01655) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0a9d9a1a2) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0e6619eee) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(037e0a375) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(04048c360) compile success，json number: 6
2025-10-8 15:17:48-debug: Json group(0f5440ade) compile success，json number: 6
2025-10-8 15:17:49-debug: Json group(0598a3140) compile success，json number: 6
2025-10-8 15:17:49-debug: Json group(03d4d71a1) compile success，json number: 6
2025-10-8 15:17:49-debug: Json group(082150b43) compile success，json number: 6
2025-10-8 15:17:49-debug: Json group(0390ee430) compile success，json number: 6
2025-10-8 15:17:49-debug: Json group(0dd9335d5) compile success，json number: 6
2025-10-8 15:17:49-debug: Json group(0703cd26b) compile success，json number: 6
2025-10-8 15:17:49-debug: Json group(0371aef90) compile success，json number: 6
2025-10-8 15:17:49-debug: Json group(0382218dc) compile success，json number: 6
2025-10-8 15:17:49-debug: Json group(080e1021a) compile success，json number: 6
2025-10-8 15:17:49-debug: Json group(0fba85dc0) compile success，json number: 6
2025-10-8 15:17:49-debug: Json group(0785d3922) compile success，json number: 6
2025-10-8 15:17:49-debug: handle single json
2025-10-8 15:17:49-warn: The SpriteFrame used by component "cc.Sprite" in prefab "test_1" is missing. Detailed information:
Node path: "test_1"
Asset url: "db://assets/resources/game/prefabs/bullet/test_1"
Missing uuid: "a6099a5a-ad14-4dae-a2e9-269bb8c5c187@f9941"


2025-10-8 15:17:49-warn: The Prefab used by component "Emitter" in prefab "Bullet_213501001" is missing. Detailed information:
Node path: "Bullet_213501001"
Asset url: "db://assets/resources/game/prefabs/emitter/Bullet_213501001"
Missing uuid: "75ee2b3e-4f4c-4f94-8a76-56741b2de07d"


2025-10-8 15:17:49-warn: The Prefab used by component "Emitter" in prefab "Bullet_211501002" is missing. Detailed information:
Node path: "Bullet_211501002"
Asset url: "db://assets/resources/game/prefabs/emitter/Bullet_211501002"
Missing uuid: "75ee2b3e-4f4c-4f94-8a76-56741b2de07d"


2025-10-8 15:17:49-warn: The Prefab used by component "Emitter" in prefab "Bullet_214501001" is missing. Detailed information:
Node path: "Bullet_214501001"
Asset url: "db://assets/resources/game/prefabs/emitter/Bullet_214501001"
Missing uuid: "75ee2b3e-4f4c-4f94-8a76-56741b2de07d"


2025-10-8 15:17:49-warn: The SpriteFrame used by component "cc.ParticleSystem2D" in prefab "Hurt" is missing. Detailed information:
Node path: "Hurt"
Asset url: "db://assets/resources/game/prefabs/effect/Hurt"
Missing uuid: "c42707d5-f97f-46ab-831d-aaefb65bc046@fe5fd"


2025-10-8 15:17:49-warn: The Prefab used by component "Emitter" in prefab "Bullet_214501002" is missing. Detailed information:
Node path: "Bullet_214501002"
Asset url: "db://assets/resources/game/prefabs/emitter/Bullet_214501002"
Missing uuid: "75ee2b3e-4f4c-4f94-8a76-56741b2de07d"


2025-10-8 15:17:49-warn: The Prefab used by component "Emitter" in prefab "Bullet_215501002" is missing. Detailed information:
Node path: "Bullet_215501002"
Asset url: "db://assets/resources/game/prefabs/emitter/Bullet_215501002"
Missing uuid: "75ee2b3e-4f4c-4f94-8a76-56741b2de07d"


2025-10-8 15:17:49-warn: The Prefab used by component "Emitter" in prefab "Bullet_215501003" is missing. Detailed information:
Node path: "Bullet_215501003"
Asset url: "db://assets/resources/game/prefabs/emitter/Bullet_215501003"
Missing uuid: "75ee2b3e-4f4c-4f94-8a76-56741b2de07d"


2025-10-8 15:17:49-warn: The SpriteFrame used by component "cc.Sprite" in prefab "yy1_1_5" is missing. Detailed information:
Node path: "yy1_1_5/Sprite"
Asset url: "db://assets/resources/game/level/background/yy1_1_5"
Missing uuid: "ddfcd00f-37e5-4c11-8129-247627e7392a@f9941"


2025-10-8 15:17:49-warn: The Prefab used by component "Emitter" in prefab "Bullet_212501001" is missing. Detailed information:
Node path: "Bullet_212501001"
Asset url: "db://assets/resources/game/prefabs/emitter/Bullet_212501001"
Missing uuid: "75ee2b3e-4f4c-4f94-8a76-56741b2de07d"


2025-10-8 15:17:49-warn: The Prefab used by component "Emitter" in prefab "Bullet_211501001" is missing. Detailed information:
Node path: "Bullet_211501001"
Asset url: "db://assets/resources/game/prefabs/emitter/Bullet_211501001"
Missing uuid: "75ee2b3e-4f4c-4f94-8a76-56741b2de07d"


2025-10-8 15:17:49-warn: The Prefab used by component "Emitter" in prefab "Bullet_213501002" is missing. Detailed information:
Node path: "Bullet_213501002"
Asset url: "db://assets/resources/game/prefabs/emitter/Bullet_213501002"
Missing uuid: "75ee2b3e-4f4c-4f94-8a76-56741b2de07d"


2025-10-8 15:17:49-warn: The sp.SkeletonData used by component "sp.Skeleton" in prefab "Boom" is missing. Detailed information:
Node path: "Boom"
Asset url: "db://assets/resources/game/prefabs/effect/Boom"
Missing uuid: "4c6c1a86-f966-4a22-b8b6-afd6a6a25028"


2025-10-8 15:17:49-warn: The Prefab used by component "Emitter" in prefab "Bullet_215501001" is missing. Detailed information:
Node path: "Bullet_215501001"
Asset url: "db://assets/resources/game/prefabs/emitter/Bullet_215501001"
Missing uuid: "75ee2b3e-4f4c-4f94-8a76-56741b2de07d"


2025-10-8 15:17:49-debug: Output asset in bundles success, progress: 24%
2025-10-8 15:17:49-debug: Output asset in bundles success, progress: 13%
2025-10-8 15:17:49-debug: Output asset in bundles start, progress: 24%
2025-10-8 15:17:49-debug: Output asset in bundles start, progress: 13%
2025-10-8 15:17:49-debug: add md5 to bundle internal...
2025-10-8 15:17:49-debug: add md5 to bundle resources...
2025-10-8 15:17:49-debug: add md5 to bundle main...
2025-10-8 15:17:49-debug: add md5 to bundle common...
2025-10-8 15:17:49-debug: add md5 to bundle editor...
2025-10-8 15:17:49-debug: add md5 to bundle home...
2025-10-8 15:17:49-debug: add md5 to bundle gm...
2025-10-8 15:17:49-debug: add md5 to bundle home_friend...
2025-10-8 15:17:49-debug: add md5 to bundle home_mail...
2025-10-8 15:17:49-debug: add md5 to bundle home_match...
2025-10-8 15:17:49-debug: add md5 to bundle home_pk...
2025-10-8 15:17:49-debug: add md5 to bundle home_plane...
2025-10-8 15:17:49-debug: add md5 to bundle home_shop...
2025-10-8 15:17:49-debug: add md5 to bundle home_skyisland...
2025-10-8 15:17:49-debug: add md5 to bundle home_story...
2025-10-8 15:17:49-debug: add md5 to bundle home_talent...
2025-10-8 15:17:49-debug: add md5 to bundle home_task...
2025-10-8 15:17:49-debug: add md5 to bundle luban...
2025-10-8 15:17:49-debug: add md5 to bundle internal success
2025-10-8 15:17:49-debug: compress config of bundle internal...
2025-10-8 15:17:49-debug: output config of bundle internal success
2025-10-8 15:17:49-debug: compress config of bundle internal success
2025-10-8 15:17:49-debug: output config of bundle internal
2025-10-8 15:17:49-debug: add md5 to bundle home_mail success
2025-10-8 15:17:49-debug: compress config of bundle home_mail...
2025-10-8 15:17:49-debug: output config of bundle home_mail success
2025-10-8 15:17:49-debug: compress config of bundle home_mail success
2025-10-8 15:17:49-debug: output config of bundle home_mail
2025-10-8 15:17:49-debug: add md5 to bundle home_shop success
2025-10-8 15:17:49-debug: compress config of bundle home_shop...
2025-10-8 15:17:49-debug: output config of bundle home_shop
2025-10-8 15:17:49-debug: output config of bundle home_shop success
2025-10-8 15:17:49-debug: compress config of bundle home_shop success
2025-10-8 15:17:49-debug: add md5 to bundle home_talent success
2025-10-8 15:17:49-debug: compress config of bundle home_talent...
2025-10-8 15:17:49-debug: output config of bundle home_talent
2025-10-8 15:17:49-debug: output config of bundle home_talent success
2025-10-8 15:17:49-debug: compress config of bundle home_skyisland...
2025-10-8 15:17:49-debug: compress config of bundle home_talent success
2025-10-8 15:17:49-debug: compress config of bundle home_skyisland success
2025-10-8 15:17:49-debug: output config of bundle home_skyisland success
2025-10-8 15:17:49-debug: output config of bundle home_skyisland
2025-10-8 15:17:49-debug: add md5 to bundle home_skyisland success
2025-10-8 15:17:49-debug: add md5 to bundle home_match success
2025-10-8 15:17:49-debug: compress config of bundle home_match...
2025-10-8 15:17:49-debug: compress config of bundle home_match success
2025-10-8 15:17:49-debug: output config of bundle home_match
2025-10-8 15:17:49-debug: output config of bundle home_match success
2025-10-8 15:17:49-debug: add md5 to bundle gm success
2025-10-8 15:17:49-debug: compress config of bundle gm...
2025-10-8 15:17:49-debug: compress config of bundle gm success
2025-10-8 15:17:49-debug: output config of bundle gm
2025-10-8 15:17:49-debug: output config of bundle gm success
2025-10-8 15:17:49-debug: add md5 to bundle home_plane success
2025-10-8 15:17:49-debug: compress config of bundle home_plane...
2025-10-8 15:17:49-debug: compress config of bundle home_plane success
2025-10-8 15:17:49-debug: output config of bundle home_plane
2025-10-8 15:17:49-debug: output config of bundle home_plane success
2025-10-8 15:17:49-debug: add md5 to bundle home_friend success
2025-10-8 15:17:49-debug: compress config of bundle home_friend...
2025-10-8 15:17:49-debug: compress config of bundle home_friend success
2025-10-8 15:17:49-debug: output config of bundle home_friend
2025-10-8 15:17:49-debug: output config of bundle home_friend success
2025-10-8 15:17:49-debug: add md5 to bundle home_story success
2025-10-8 15:17:49-debug: compress config of bundle home_story...
2025-10-8 15:17:49-debug: compress config of bundle home_story success
2025-10-8 15:17:49-debug: output config of bundle home_story
2025-10-8 15:17:49-debug: output config of bundle home_story success
2025-10-8 15:17:49-debug: add md5 to bundle home_task success
2025-10-8 15:17:49-debug: compress config of bundle home_task...
2025-10-8 15:17:49-debug: compress config of bundle home_task success
2025-10-8 15:17:49-debug: output config of bundle home_task
2025-10-8 15:17:49-debug: output config of bundle home_task success
2025-10-8 15:17:49-debug: add md5 to bundle editor success
2025-10-8 15:17:49-debug: compress config of bundle editor...
2025-10-8 15:17:49-debug: compress config of bundle editor success
2025-10-8 15:17:49-debug: output config of bundle editor
2025-10-8 15:17:49-debug: output config of bundle editor success
2025-10-8 15:17:49-debug: compress config of bundle luban success
2025-10-8 15:17:49-debug: output config of bundle luban
2025-10-8 15:17:49-debug: add md5 to bundle luban success
2025-10-8 15:17:49-debug: compress config of bundle luban...
2025-10-8 15:17:49-debug: output config of bundle luban success
2025-10-8 15:17:49-debug: add md5 to bundle home_pk success
2025-10-8 15:17:49-debug: compress config of bundle home_pk...
2025-10-8 15:17:49-debug: compress config of bundle home_pk success
2025-10-8 15:17:49-debug: output config of bundle home_pk
2025-10-8 15:17:49-debug: output config of bundle home_pk success
2025-10-8 15:17:49-debug: compress config of bundle main...
2025-10-8 15:17:49-debug: output config of bundle main
2025-10-8 15:17:49-debug: compress config of bundle main success
2025-10-8 15:17:49-debug: add md5 to bundle main success
2025-10-8 15:17:49-debug: output config of bundle main success
2025-10-8 15:17:49-debug: add md5 to bundle common success
2025-10-8 15:17:49-debug: compress config of bundle common...
2025-10-8 15:17:49-debug: compress config of bundle common success
2025-10-8 15:17:49-debug: output config of bundle common
2025-10-8 15:17:49-debug: output config of bundle common success
2025-10-8 15:17:49-debug: add md5 to bundle home success
2025-10-8 15:17:49-debug: compress config of bundle home...
2025-10-8 15:17:49-debug: output config of bundle home
2025-10-8 15:17:49-debug: compress config of bundle home success
2025-10-8 15:17:49-debug: output config of bundle home success
2025-10-8 15:17:50-debug: compress config of bundle resources...
2025-10-8 15:17:50-debug: output config of bundle resources
2025-10-8 15:17:50-debug: compress config of bundle resources success
2025-10-8 15:17:50-debug: Output asset in bundles success, progress: 24%
2025-10-8 15:17:50-debug: output config of bundle resources success
2025-10-8 15:17:50-debug: Output asset in bundles success, progress: 13%
2025-10-8 15:17:50-debug: add md5 to bundle resources success
2025-10-8 15:17:50-debug: // ---- build task Build Assets ---- (3094ms)
2025-10-8 15:17:50-debug: wechatgame:(onAfterBundleBuildTask) start..., progress: 29%
2025-10-8 15:17:50-debug: [Build Memory track]: Build Assets start:239.06MB, end 228.21MB, increase: -11118.11KB
2025-10-8 15:17:50-log: run build task Build Assets success in 3 s√, progress: 29%
2025-10-8 15:17:50-debug: wechatgame:(onAfterBundleBuildTask) start..., progress: 13%
2025-10-8 15:17:50-debug: // ---- build task wechatgame：onAfterBundleBuildTask ----
2025-10-8 15:17:50-debug: // ---- build task wechatgame：onAfterBundleBuildTask ---- (10ms)
2025-10-8 15:17:50-debug: wechatgame:(onAfterBundleBuildTask) in 10 ms ✓, progress: 29%
2025-10-8 15:17:50-debug: wechatgame:(onAfterBundleBuildTask) in 10 ms ✓, progress: 20%
2025-10-8 15:17:50-debug: wechatgame:(onAfterBuildAssets) start..., progress: 29%
2025-10-8 15:17:50-debug: // ---- build task wechatgame：onAfterBuildAssets ----
2025-10-8 15:17:50-debug: // ---- build task wechatgame：onAfterBuildAssets ---- (10ms)
2025-10-8 15:17:50-debug: wechatgame:(onAfterBuildAssets) in 10 ms ✓, progress: 31%
2025-10-8 15:17:50-debug: 整理部分构建选项内数据到 settings.json start, progress: 31%
2025-10-8 15:17:50-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-8 15:17:50-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (10ms)
2025-10-8 15:17:50-log: run build task 整理部分构建选项内数据到 settings.json success in 10 ms√, progress: 33%
2025-10-8 15:17:50-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:228.46MB, end 228.52MB, increase: 63.32KB
2025-10-8 15:17:50-debug: 填充脚本数据到 settings.json start, progress: 33%
2025-10-8 15:17:50-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-8 15:17:50-log: run build task 填充脚本数据到 settings.json success in 11 ms√, progress: 34%
2025-10-8 15:17:50-debug: [Build Memory track]: 填充脚本数据到 settings.json start:228.55MB, end 228.62MB, increase: 73.42KB
2025-10-8 15:17:50-debug: 整理部分构建选项内数据到 settings.json start, progress: 34%
2025-10-8 15:17:50-debug: // ---- build task 填充脚本数据到 settings.json ---- (11ms)
2025-10-8 15:17:50-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-8 15:17:50-log: run build task 整理部分构建选项内数据到 settings.json success in 330 ms√, progress: 36%
2025-10-8 15:17:50-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (330ms)
2025-10-8 15:17:50-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:228.65MB, end 229.03MB, increase: 392.77KB
2025-10-8 15:17:50-debug: wechatgame:(onBeforeCompressSettings) start..., progress: 36%
2025-10-8 15:17:50-debug: // ---- build task wechatgame：onBeforeCompressSettings ----
2025-10-8 15:17:50-debug: // ---- build task wechatgame：onBeforeCompressSettings ---- (9ms)
2025-10-8 15:17:50-debug: wechatgame:(onBeforeCompressSettings) in 9 ms ✓, progress: 38%
2025-10-8 15:17:50-debug: cocos-service:(onBeforeCompressSettings) start..., progress: 38%
2025-10-8 15:17:50-debug: // ---- build task cocos-service：onBeforeCompressSettings ----
2025-10-8 15:17:50-debug: cocos-service:(onBeforeCompressSettings) in 195 ms ✓, progress: 40%
2025-10-8 15:17:50-debug: // ---- build task 整理静态模板文件 ----
2025-10-8 15:17:50-debug: // ---- build task cocos-service：onBeforeCompressSettings ---- (195ms)
2025-10-8 15:17:50-debug: 整理静态模板文件 start, progress: 40%
2025-10-8 15:17:50-debug: // ---- build task 整理静态模板文件 ---- (34ms)
2025-10-8 15:17:50-log: run build task 整理静态模板文件 success in 34 ms√, progress: 45%
2025-10-8 15:17:50-debug: [Build Memory track]: 整理静态模板文件 start:229.28MB, end 235.36MB, increase: 6.08MB
2025-10-8 15:17:50-debug: cocos-service:(onAfterCompressSettings) start..., progress: 45%
2025-10-8 15:17:50-debug: // ---- build task cocos-service：onAfterCompressSettings ----
2025-10-8 15:17:50-debug: // ---- build task cocos-service：onAfterCompressSettings ---- (94ms)
2025-10-8 15:17:50-debug: // ---- build task wechatgame：onBeforeCopyBuildTemplate ----
2025-10-8 15:17:50-debug: wechatgame:(onBeforeCopyBuildTemplate) start..., progress: 46%
2025-10-8 15:17:50-debug: cocos-service:(onAfterCompressSettings) in 94 ms ✓, progress: 46%
2025-10-8 15:17:50-debug: // ---- build task wechatgame：onBeforeCopyBuildTemplate ---- (10ms)
2025-10-8 15:17:50-debug: wechatgame:(onBeforeCopyBuildTemplate) in 10 ms ✓, progress: 48%
2025-10-8 15:17:50-debug: wechatgame:(onAfterCopyBuildTemplate) start..., progress: 48%
2025-10-8 15:17:50-debug: // ---- build task wechatgame：onAfterCopyBuildTemplate ----
2025-10-8 15:17:50-debug: wechatgame:(onAfterCopyBuildTemplate) in 8 ms ✓, progress: 50%
2025-10-8 15:17:50-debug: // ---- build task wechatgame：onAfterCopyBuildTemplate ---- (8ms)
2025-10-8 15:17:50-debug: 给所有的资源加上 MD5 后缀 start, progress: 50%
2025-10-8 15:17:50-debug: // ---- build task 给所有的资源加上 MD5 后缀 ----
2025-10-8 15:17:50-debug: add suffix to assets(8) success!
2025-10-8 15:17:50-debug: // ---- build task 给所有的资源加上 MD5 后缀 ---- (31ms)
2025-10-8 15:17:50-log: run build task 给所有的资源加上 MD5 后缀 success in 31 ms√, progress: 60%
2025-10-8 15:17:50-debug: [Build Memory track]: 给所有的资源加上 MD5 后缀 start:236.06MB, end 231.42MB, increase: -4749.14KB
2025-10-8 15:17:50-debug: // ---- build task wechatgame：onAfterBuild ----
2025-10-8 15:17:50-debug: wechatgame:(onAfterBuild) start..., progress: 60%
2025-10-8 15:17:51-debug: // ---- build task wechatgame：onAfterBuild ---- (285ms)
2025-10-8 15:17:51-debug: wechatgame:(onAfterBuild) in 285 ms ✓, progress: 62%
2025-10-8 15:17:51-debug: cocos-service:(onAfterBuild) start..., progress: 62%
2025-10-8 15:17:51-debug: // ---- build task cocos-service：onAfterBuild ----
2025-10-8 15:17:51-debug: cocos-service:(onAfterBuild) in 89 ms ✓, progress: 64%
2025-10-8 15:17:51-debug: // ---- build task cocos-service：onAfterBuild ---- (89ms)
2025-10-8 15:17:51-debug: adsense-h5g-plugin:(onAfterBuild) start..., progress: 64%
2025-10-8 15:17:51-debug: // ---- build task adsense-h5g-plugin：onAfterBuild ----
2025-10-8 15:17:51-debug: // ---- build task adsense-h5g-plugin：onAfterBuild ---- (8ms)
2025-10-8 15:17:51-debug: adsense-h5g-plugin:(onAfterBuild) in 8 ms ✓, progress: 65%
2025-10-8 15:17:51-log: Asset DB is resume!
2025-10-8 15:17:51-debug: builder:build-project-total (23927ms)
2025-10-8 15:17:51-warn: The Prefab used by node "New Node" in prefab "RandNod_mount_08" is missing. Detailed information:
Node path: "RandNod_mount_08/New Node"
Asset url: "db://assets/resources/game/level/background/Prefab/HighSky/RandNod/RandNod_mount_08"
Missing uuid: "3d2365f8-50af-4528-a96a-961db2fa169b"


2025-10-8 15:17:51-warn: The SpriteFrame used by component "cc.Sprite" in prefab "test_1" is missing. Detailed information:
Node path: "test_1"
Asset url: "db://assets/resources/game/prefabs/bullet/test_1"
Missing uuid: "a6099a5a-ad14-4dae-a2e9-269bb8c5c187@f9941"


2025-10-8 15:17:51-warn: The Prefab used by component "Emitter" in prefab "Bullet_213501001" is missing. Detailed information:
Node path: "Bullet_213501001"
Asset url: "db://assets/resources/game/prefabs/emitter/Bullet_213501001"
Missing uuid: "75ee2b3e-4f4c-4f94-8a76-56741b2de07d"


2025-10-8 15:17:51-warn: The Prefab used by component "Emitter" in prefab "Bullet_211501002" is missing. Detailed information:
Node path: "Bullet_211501002"
Asset url: "db://assets/resources/game/prefabs/emitter/Bullet_211501002"
Missing uuid: "75ee2b3e-4f4c-4f94-8a76-56741b2de07d"


2025-10-8 15:17:51-warn: The Prefab used by component "Emitter" in prefab "Bullet_214501001" is missing. Detailed information:
Node path: "Bullet_214501001"
Asset url: "db://assets/resources/game/prefabs/emitter/Bullet_214501001"
Missing uuid: "75ee2b3e-4f4c-4f94-8a76-56741b2de07d"


2025-10-8 15:17:51-warn: The Prefab used by component "Emitter" in prefab "Bullet_214501002" is missing. Detailed information:
Node path: "Bullet_214501002"
Asset url: "db://assets/resources/game/prefabs/emitter/Bullet_214501002"
Missing uuid: "75ee2b3e-4f4c-4f94-8a76-56741b2de07d"


2025-10-8 15:17:51-warn: The SpriteFrame used by component "cc.ParticleSystem2D" in prefab "Hurt" is missing. Detailed information:
Node path: "Hurt"
Asset url: "db://assets/resources/game/prefabs/effect/Hurt"
Missing uuid: "c42707d5-f97f-46ab-831d-aaefb65bc046@fe5fd"


2025-10-8 15:17:51-warn: The SpriteFrame used by component "cc.Sprite" in prefab "yy1_1_5" is missing. Detailed information:
Node path: "yy1_1_5/Sprite"
Asset url: "db://assets/resources/game/level/background/yy1_1_5"
Missing uuid: "ddfcd00f-37e5-4c11-8129-247627e7392a@f9941"


2025-10-8 15:17:51-warn: The Prefab used by component "Emitter" in prefab "Bullet_215501002" is missing. Detailed information:
Node path: "Bullet_215501002"
Asset url: "db://assets/resources/game/prefabs/emitter/Bullet_215501002"
Missing uuid: "75ee2b3e-4f4c-4f94-8a76-56741b2de07d"


2025-10-8 15:17:51-warn: The Prefab used by component "Emitter" in prefab "Bullet_212501001" is missing. Detailed information:
Node path: "Bullet_212501001"
Asset url: "db://assets/resources/game/prefabs/emitter/Bullet_212501001"
Missing uuid: "75ee2b3e-4f4c-4f94-8a76-56741b2de07d"


2025-10-8 15:17:51-warn: The Prefab used by component "Emitter" in prefab "Bullet_215501003" is missing. Detailed information:
Node path: "Bullet_215501003"
Asset url: "db://assets/resources/game/prefabs/emitter/Bullet_215501003"
Missing uuid: "75ee2b3e-4f4c-4f94-8a76-56741b2de07d"


2025-10-8 15:17:51-warn: The Prefab used by component "Emitter" in prefab "Bullet_213501002" is missing. Detailed information:
Node path: "Bullet_213501002"
Asset url: "db://assets/resources/game/prefabs/emitter/Bullet_213501002"
Missing uuid: "75ee2b3e-4f4c-4f94-8a76-56741b2de07d"


2025-10-8 15:17:51-warn: The sp.SkeletonData used by component "sp.Skeleton" in prefab "Boom" is missing. Detailed information:
Node path: "Boom"
Asset url: "db://assets/resources/game/prefabs/effect/Boom"
Missing uuid: "4c6c1a86-f966-4a22-b8b6-afd6a6a25028"


2025-10-8 15:17:51-warn: The Prefab used by component "Emitter" in prefab "Bullet_211501001" is missing. Detailed information:
Node path: "Bullet_211501001"
Asset url: "db://assets/resources/game/prefabs/emitter/Bullet_211501001"
Missing uuid: "75ee2b3e-4f4c-4f94-8a76-56741b2de07d"


2025-10-8 15:17:51-warn: The Prefab used by component "Emitter" in prefab "Bullet_215501001" is missing. Detailed information:
Node path: "Bullet_215501001"
Asset url: "db://assets/resources/game/prefabs/emitter/Bullet_215501001"
Missing uuid: "75ee2b3e-4f4c-4f94-8a76-56741b2de07d"


2025-10-8 15:17:51-debug: [Build Memory track]: builder:build-project-total start:215.71MB, end 246.63MB, increase: 30.92MB
2025-10-8 15:17:51-debug: ================================ build Task (wechatgame) Finished in (24 s)ms ================================
2025-10-8 15:17:51-debug: build success in 23927!
2025-10-8 15:17:51-debug: Stop record console. {file(E:\M2Game\Client\temp\builder\log\wechatgame2025-8-5 15-46.log)}
2025-10-8 15:17:51-debug: refresh db internal success
2025-10-8 15:17:51-debug: refresh db i18n success
2025-10-8 15:17:51-debug: refresh db assets success
2025-10-8 15:17:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 15:17:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 15:17:51-debug: asset-db:refresh-all-database (441ms)
2025-10-8 15:17:51-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 15:17:51-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 15:18:32-debug: refresh db internal success
2025-10-8 15:18:32-debug: refresh db i18n success
2025-10-8 15:18:32-debug: refresh db assets success
2025-10-8 15:18:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 15:18:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 15:18:32-debug: asset-db:refresh-all-database (239ms)
2025-10-8 15:18:32-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 15:18:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 15:19:04-debug: refresh db internal success
2025-10-8 15:19:04-debug: refresh db i18n success
2025-10-8 15:19:04-debug: refresh db assets success
2025-10-8 15:19:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 15:19:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 15:19:04-debug: asset-db:refresh-all-database (169ms)
2025-10-8 15:19:04-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 15:19:04-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-10-8 15:30:25-debug: refresh db internal success
2025-10-8 15:30:25-debug: refresh db i18n success
2025-10-8 15:30:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 15:30:25-debug: refresh db assets success
2025-10-8 15:30:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 15:30:25-debug: asset-db:refresh-all-database (203ms)
2025-10-8 15:30:25-debug: asset-db:worker-effect-data-processing (2ms)
2025-10-8 15:30:25-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-10-8 15:43:23-debug: refresh db internal success
2025-10-8 15:43:23-debug: refresh db i18n success
2025-10-8 15:43:23-debug: refresh db assets success
2025-10-8 15:43:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 15:43:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 15:43:23-debug: asset-db:refresh-all-database (263ms)
2025-10-8 15:43:23-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 15:43:38-debug: refresh db internal success
2025-10-8 15:43:38-debug: refresh db i18n success
2025-10-8 15:43:38-debug: refresh db assets success
2025-10-8 15:43:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 15:43:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 15:43:38-debug: asset-db:refresh-all-database (178ms)
2025-10-8 15:44:13-debug: refresh db internal success
2025-10-8 15:44:13-debug: refresh db i18n success
2025-10-8 15:44:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 15:44:13-debug: refresh db assets success
2025-10-8 15:44:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 15:44:13-debug: asset-db:refresh-all-database (155ms)
2025-10-8 15:44:18-debug: refresh db internal success
2025-10-8 15:44:18-debug: refresh db i18n success
2025-10-8 15:44:18-debug: refresh db assets success
2025-10-8 15:44:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 15:44:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 15:44:18-debug: asset-db:refresh-all-database (152ms)
2025-10-8 15:44:44-debug: %cImport%c: E:\M2Game\Client\assets\scenes\Game.scene
background: #aaff85; color: #000;
color: #000;
2025-10-8 15:44:44-debug: asset-db:reimport-asset6b52bce3-af16-4791-a85c-1786c6ed769a (6ms)
2025-10-8 15:44:48-debug: Query all assets info in project
2025-10-8 15:44:48-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-8 15:44:48-debug: Skip compress image, progress: 0%
2025-10-8 15:44:48-debug: Init all bundles start..., progress: 0%
2025-10-8 15:44:48-debug: Num of bundles: 18..., progress: 0%
2025-10-8 15:44:48-debug: 查询 Asset Bundle start, progress: 0%
2025-10-8 15:44:48-debug: // ---- build task 查询 Asset Bundle ----
2025-10-8 15:44:48-debug: Init bundle root assets start..., progress: 0%
2025-10-8 15:44:48-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-8 15:44:48-debug:   Number of all scripts: 306
2025-10-8 15:44:48-debug:   Number of other assets: 2653
2025-10-8 15:44:48-debug:   Number of all scenes: 11
2025-10-8 15:44:48-debug: Init bundle root assets success..., progress: 0%
2025-10-8 15:44:48-debug: [Build Memory track]: 查询 Asset Bundle start:243.35MB, end 239.14MB, increase: -4301.20KB
2025-10-8 15:44:48-debug: // ---- build task 查询 Asset Bundle ---- (33ms)
2025-10-8 15:44:48-log: run build task 查询 Asset Bundle success in 33 ms√, progress: 5%
2025-10-8 15:44:48-debug: 查询 Asset Bundle start, progress: 5%
2025-10-8 15:44:48-debug: // ---- build task 查询 Asset Bundle ----
2025-10-8 15:44:48-debug: // ---- build task 查询 Asset Bundle ---- (5ms)
2025-10-8 15:44:48-debug: [Build Memory track]: 查询 Asset Bundle start:239.17MB, end 238.69MB, increase: -492.31KB
2025-10-8 15:44:48-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-8 15:44:48-log: run build task 查询 Asset Bundle success in 5 ms√, progress: 10%
2025-10-8 15:44:48-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-8 15:44:48-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-10-8 15:44:48-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:238.72MB, end 238.73MB, increase: 17.24KB
2025-10-8 15:44:48-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-8 15:44:48-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-8 15:44:48-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-8 15:44:48-debug: [Build Memory track]: 填充脚本数据到 settings.json start:238.76MB, end 238.77MB, increase: 15.49KB
2025-10-8 15:44:48-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-10-8 15:44:48-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-8 15:44:48-log: run build task 整理部分构建选项内数据到 settings.json success in 4 ms√, progress: 15%
2025-10-8 15:44:48-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:238.80MB, end 239.13MB, increase: 333.46KB
2025-10-8 15:44:48-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-10-8 15:47:31-debug: refresh db internal success
2025-10-8 15:47:31-debug: refresh db i18n success
2025-10-8 15:47:31-debug: refresh db assets success
2025-10-8 15:47:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 15:47:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 15:47:31-debug: asset-db:refresh-all-database (196ms)
2025-10-8 15:47:31-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 15:47:31-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 15:47:37-debug: refresh db internal success
2025-10-8 15:47:37-debug: refresh db i18n success
2025-10-8 15:47:37-debug: refresh db assets success
2025-10-8 15:47:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 15:47:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 15:47:37-debug: asset-db:refresh-all-database (161ms)
2025-10-8 15:47:37-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 15:47:37-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 16:04:21-debug: refresh db internal success
2025-10-8 16:04:21-debug: refresh db i18n success
2025-10-8 16:04:21-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\LevelLayer.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 16:04:21-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map
background: #aaff85; color: #000;
color: #000;
2025-10-8 16:04:21-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\LevelBackgroundLayerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 16:04:21-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\LevelEventRun.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 16:04:21-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\LevelBaseUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 16:04:21-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\LevelLayerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 16:04:21-debug: refresh db assets success
2025-10-8 16:04:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 16:04:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 16:04:21-debug: asset-db:refresh-all-database (206ms)
2025-10-8 16:07:25-debug: refresh db internal success
2025-10-8 16:07:25-debug: refresh db i18n success
2025-10-8 16:07:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane
background: #aaff85; color: #000;
color: #000;
2025-10-8 16:07:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\LevelBaseUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 16:07:25-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\PlaneBaseDebug.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 16:07:25-debug: refresh db assets success
2025-10-8 16:07:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 16:07:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 16:07:25-debug: asset-db:refresh-all-database (194ms)
2025-10-8 16:09:15-debug: refresh db internal success
2025-10-8 16:09:15-debug: refresh db i18n success
2025-10-8 16:09:15-debug: refresh db assets success
2025-10-8 16:09:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 16:09:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 16:09:15-debug: asset-db:refresh-all-database (165ms)
2025-10-8 16:09:15-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 16:09:15-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 16:13:46-debug: refresh db internal success
2025-10-8 16:13:46-debug: refresh db i18n success
2025-10-8 16:13:46-debug: Query all assets info in project
2025-10-8 16:13:46-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-8 16:13:46-debug: Skip compress image, progress: 0%
2025-10-8 16:13:46-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\LevelBaseUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 16:13:46-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\LevelBackgroundLayerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 16:13:46-debug: Num of bundles: 18..., progress: 0%
2025-10-8 16:13:46-debug: Init all bundles start..., progress: 0%
2025-10-8 16:13:46-debug: 查询 Asset Bundle start, progress: 0%
2025-10-8 16:13:46-debug: // ---- build task 查询 Asset Bundle ----
2025-10-8 16:13:46-debug: Init bundle root assets start..., progress: 0%
2025-10-8 16:13:46-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-8 16:13:46-debug:   Number of all scripts: 307
2025-10-8 16:13:46-debug: Init bundle root assets success..., progress: 0%
2025-10-8 16:13:46-debug:   Number of other assets: 2653
2025-10-8 16:13:46-debug:   Number of all scenes: 11
2025-10-8 16:13:46-debug: // ---- build task 查询 Asset Bundle ---- (30ms)
2025-10-8 16:13:46-debug: [Build Memory track]: 查询 Asset Bundle start:244.95MB, end 244.84MB, increase: -118.80KB
2025-10-8 16:13:46-log: run build task 查询 Asset Bundle success in 30 ms√, progress: 5%
2025-10-8 16:13:46-debug: 查询 Asset Bundle start, progress: 5%
2025-10-8 16:13:46-debug: // ---- build task 查询 Asset Bundle ----
2025-10-8 16:13:46-debug: [Build Memory track]: 查询 Asset Bundle start:244.86MB, end 245.78MB, increase: 936.49KB
2025-10-8 16:13:46-debug: // ---- build task 查询 Asset Bundle ---- (4ms)
2025-10-8 16:13:46-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-8 16:13:46-log: run build task 查询 Asset Bundle success in 4 ms√, progress: 10%
2025-10-8 16:13:46-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-8 16:13:46-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-10-8 16:13:46-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:245.80MB, end 245.90MB, increase: 103.29KB
2025-10-8 16:13:46-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-10-8 16:13:46-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-8 16:13:46-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-8 16:13:46-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-10-8 16:13:46-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-10-8 16:13:46-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-8 16:13:46-debug: [Build Memory track]: 填充脚本数据到 settings.json start:245.93MB, end 246.05MB, increase: 117.91KB
2025-10-8 16:13:46-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-8 16:13:46-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-10-8 16:13:46-log: run build task 整理部分构建选项内数据到 settings.json success in 4 ms√, progress: 15%
2025-10-8 16:13:46-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:246.07MB, end 243.90MB, increase: -2223.38KB
2025-10-8 16:13:47-debug: refresh db assets success
2025-10-8 16:13:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 16:13:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 16:13:47-debug: asset-db:refresh-all-database (242ms)
2025-10-8 16:13:47-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 16:13:47-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 16:13:48-debug: Query all assets info in project
2025-10-8 16:13:48-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-8 16:13:48-debug: Skip compress image, progress: 0%
2025-10-8 16:13:48-debug: Init all bundles start..., progress: 0%
2025-10-8 16:13:48-debug: Num of bundles: 18..., progress: 0%
2025-10-8 16:13:48-debug: // ---- build task 查询 Asset Bundle ----
2025-10-8 16:13:48-debug: 查询 Asset Bundle start, progress: 0%
2025-10-8 16:13:48-debug: Init bundle root assets start..., progress: 0%
2025-10-8 16:13:48-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-8 16:13:48-debug:   Number of other assets: 2653
2025-10-8 16:13:48-debug:   Number of all scenes: 11
2025-10-8 16:13:48-debug: Init bundle root assets success..., progress: 0%
2025-10-8 16:13:48-debug:   Number of all scripts: 307
2025-10-8 16:13:48-debug: // ---- build task 查询 Asset Bundle ---- (25ms)
2025-10-8 16:13:48-log: run build task 查询 Asset Bundle success in 25 ms√, progress: 5%
2025-10-8 16:13:48-debug: [Build Memory track]: 查询 Asset Bundle start:244.43MB, end 246.62MB, increase: 2.19MB
2025-10-8 16:13:48-debug: 查询 Asset Bundle start, progress: 5%
2025-10-8 16:13:48-debug: // ---- build task 查询 Asset Bundle ----
2025-10-8 16:13:48-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-10-8 16:13:48-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-10-8 16:13:48-debug: [Build Memory track]: 查询 Asset Bundle start:246.65MB, end 247.01MB, increase: 374.65KB
2025-10-8 16:13:48-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-8 16:13:48-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-8 16:13:48-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-8 16:13:48-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:247.04MB, end 247.05MB, increase: 17.12KB
2025-10-8 16:13:48-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-10-8 16:13:48-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-8 16:13:48-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-10-8 16:13:48-debug: [Build Memory track]: 填充脚本数据到 settings.json start:247.08MB, end 247.10MB, increase: 15.58KB
2025-10-8 16:13:48-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-8 16:13:48-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-8 16:13:48-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-10-8 16:13:48-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-10-8 16:13:48-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:247.12MB, end 247.43MB, increase: 319.72KB
2025-10-8 16:48:19-debug: refresh db internal success
2025-10-8 16:48:19-debug: refresh db i18n success
2025-10-8 16:48:19-debug: refresh db assets success
2025-10-8 16:48:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 16:48:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 16:48:19-debug: asset-db:refresh-all-database (251ms)
2025-10-8 16:48:19-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 16:48:19-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 17:27:11-debug: refresh db internal success
2025-10-8 17:27:11-debug: refresh db i18n success
2025-10-8 17:27:11-debug: refresh db assets success
2025-10-8 17:27:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 17:27:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 17:27:11-debug: asset-db:refresh-all-database (204ms)
2025-10-8 17:27:11-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 17:27:11-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-10-8 17:28:40-debug: refresh db internal success
2025-10-8 17:28:40-debug: refresh db i18n success
2025-10-8 17:28:40-debug: refresh db assets success
2025-10-8 17:28:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 17:28:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 17:28:40-debug: asset-db:refresh-all-database (206ms)
2025-10-8 17:32:50-debug: refresh db internal success
2025-10-8 17:32:50-debug: refresh db i18n success
2025-10-8 17:32:50-debug: refresh db assets success
2025-10-8 17:32:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 17:32:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 17:32:50-debug: asset-db:refresh-all-database (170ms)
2025-10-8 17:32:50-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 17:32:50-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-10-8 17:32:55-debug: refresh db internal success
2025-10-8 17:32:55-debug: refresh db i18n success
2025-10-8 17:32:55-debug: refresh db assets success
2025-10-8 17:32:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 17:32:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 17:32:55-debug: asset-db:refresh-all-database (201ms)
2025-10-8 17:32:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 17:32:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 17:33:05-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-10-8 17:33:05-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (5ms)
2025-10-8 17:33:58-debug: refresh db internal success
2025-10-8 17:33:58-debug: refresh db i18n success
2025-10-8 17:33:58-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 17:33:58-debug: refresh db assets success
2025-10-8 17:33:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 17:33:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 17:33:58-debug: asset-db:refresh-all-database (232ms)
2025-10-8 17:33:58-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 17:33:58-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 17:34:10-debug: refresh db internal success
2025-10-8 17:34:10-debug: refresh db i18n success
2025-10-8 17:34:10-debug: refresh db assets success
2025-10-8 17:34:10-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 17:34:10-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 17:34:10-debug: asset-db:refresh-all-database (170ms)
2025-10-8 17:34:10-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 17:34:10-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 17:36:47-debug: refresh db internal success
2025-10-8 17:36:47-debug: refresh db i18n success
2025-10-8 17:36:47-debug: refresh db assets success
2025-10-8 17:36:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 17:36:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 17:36:47-debug: asset-db:refresh-all-database (197ms)
2025-10-8 17:48:16-debug: refresh db internal success
2025-10-8 17:48:16-debug: refresh db i18n success
2025-10-8 17:48:16-debug: refresh db assets success
2025-10-8 17:48:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 17:48:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 17:48:16-debug: asset-db:refresh-all-database (232ms)
2025-10-8 17:48:16-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 17:48:16-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 17:48:17-debug: Query all assets info in project
2025-10-8 17:48:17-debug: init custom config: keepNodeUuid: false, useCache: true
2025-10-8 17:48:17-debug: Skip compress image, progress: 0%
2025-10-8 17:48:17-debug: Num of bundles: 18..., progress: 0%
2025-10-8 17:48:17-debug: Init all bundles start..., progress: 0%
2025-10-8 17:48:17-debug: // ---- build task 查询 Asset Bundle ----
2025-10-8 17:48:17-debug: 查询 Asset Bundle start, progress: 0%
2025-10-8 17:48:17-debug: Init bundle root assets start..., progress: 0%
2025-10-8 17:48:17-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,871c3b6c-7379-419d-bda3-794b239ab90d,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-10-8 17:48:17-debug:   Number of all scenes: 11
2025-10-8 17:48:17-debug:   Number of other assets: 2653
2025-10-8 17:48:17-debug: Init bundle root assets success..., progress: 0%
2025-10-8 17:48:17-debug:   Number of all scripts: 307
2025-10-8 17:48:17-debug: // ---- build task 查询 Asset Bundle ---- (38ms)
2025-10-8 17:48:17-log: run build task 查询 Asset Bundle success in 38 ms√, progress: 5%
2025-10-8 17:48:17-debug: // ---- build task 查询 Asset Bundle ----
2025-10-8 17:48:17-debug: [Build Memory track]: 查询 Asset Bundle start:249.72MB, end 249.36MB, increase: -363.54KB
2025-10-8 17:48:17-debug: 查询 Asset Bundle start, progress: 5%
2025-10-8 17:48:17-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-10-8 17:48:17-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-10-8 17:48:17-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-10-8 17:48:17-debug: [Build Memory track]: 查询 Asset Bundle start:249.39MB, end 249.75MB, increase: 376.21KB
2025-10-8 17:48:17-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-8 17:48:17-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-10-8 17:48:17-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-10-8 17:48:17-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-10-8 17:48:17-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:249.79MB, end 249.81MB, increase: 24.73KB
2025-10-8 17:48:17-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-10-8 17:48:17-debug: // ---- build task 填充脚本数据到 settings.json ---- (2ms)
2025-10-8 17:48:17-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-10-8 17:48:17-log: run build task 填充脚本数据到 settings.json success in 2 ms√, progress: 13%
2025-10-8 17:48:17-debug: [Build Memory track]: 填充脚本数据到 settings.json start:249.84MB, end 249.86MB, increase: 25.52KB
2025-10-8 17:48:17-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-10-8 17:48:17-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (4ms)
2025-10-8 17:48:17-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:249.89MB, end 250.21MB, increase: 325.06KB
2025-10-8 17:48:17-log: run build task 整理部分构建选项内数据到 settings.json success in 4 ms√, progress: 15%
2025-10-8 17:49:24-debug: refresh db internal success
2025-10-8 17:49:24-debug: refresh db i18n success
2025-10-8 17:49:24-debug: refresh db assets success
2025-10-8 17:49:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 17:49:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 17:49:24-debug: asset-db:refresh-all-database (177ms)
2025-10-8 18:36:21-debug: refresh db internal success
2025-10-8 18:36:21-debug: refresh db i18n success
2025-10-8 18:36:21-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\Bullet.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 18:36:21-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 18:36:21-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\DefaultMove.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 18:36:21-debug: refresh db assets success
2025-10-8 18:36:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 18:36:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 18:36:21-debug: asset-db:refresh-all-database (249ms)
2025-10-8 18:36:21-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-10-8 18:36:21-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 18:37:50-debug: refresh db internal success
2025-10-8 18:37:50-debug: refresh db i18n success
2025-10-8 18:37:50-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\IMovable.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 18:37:50-debug: refresh db assets success
2025-10-8 18:37:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 18:37:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 18:37:50-debug: asset-db:refresh-all-database (213ms)
2025-10-8 18:37:50-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 18:37:50-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 18:37:53-debug: refresh db internal success
2025-10-8 18:37:53-debug: refresh db i18n success
2025-10-8 18:37:53-debug: refresh db assets success
2025-10-8 18:37:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 18:37:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 18:37:53-debug: asset-db:refresh-all-database (199ms)
2025-10-8 18:37:53-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 18:38:21-debug: refresh db internal success
2025-10-8 18:38:21-debug: refresh db i18n success
2025-10-8 18:38:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 18:38:21-debug: refresh db assets success
2025-10-8 18:38:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 18:38:21-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 18:38:21-debug: asset-db:refresh-all-database (173ms)
2025-10-8 18:38:21-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 18:38:57-debug: refresh db internal success
2025-10-8 18:38:57-debug: refresh db i18n success
2025-10-8 18:38:57-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\DefaultMove.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 18:38:57-debug: refresh db assets success
2025-10-8 18:38:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 18:38:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 18:38:57-debug: asset-db:refresh-all-database (170ms)
2025-10-8 18:38:57-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 18:38:57-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 18:39:54-debug: refresh db internal success
2025-10-8 18:39:54-debug: refresh db i18n success
2025-10-8 18:39:54-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\DefaultMove.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 18:39:54-debug: refresh db assets success
2025-10-8 18:39:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 18:39:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 18:39:54-debug: asset-db:refresh-all-database (213ms)
2025-10-8 18:40:27-debug: refresh db internal success
2025-10-8 18:40:27-debug: refresh db i18n success
2025-10-8 18:40:27-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\DefaultMove.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 18:40:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 18:40:27-debug: refresh db assets success
2025-10-8 18:40:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 18:40:27-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 18:40:27-debug: asset-db:refresh-all-database (202ms)
2025-10-8 18:40:27-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 18:41:14-debug: refresh db internal success
2025-10-8 18:41:14-debug: refresh db i18n success
2025-10-8 18:41:15-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 18:41:15-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\DefaultMove.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 18:41:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 18:41:15-debug: refresh db assets success
2025-10-8 18:41:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 18:41:15-debug: asset-db:refresh-all-database (210ms)
2025-10-8 18:41:15-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 18:41:15-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 18:41:18-debug: refresh db internal success
2025-10-8 18:41:18-debug: refresh db i18n success
2025-10-8 18:41:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 18:41:18-debug: refresh db assets success
2025-10-8 18:41:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 18:41:18-debug: asset-db:refresh-all-database (197ms)
2025-10-8 18:41:18-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 18:41:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 18:41:52-debug: refresh db internal success
2025-10-8 18:41:52-debug: refresh db i18n success
2025-10-8 18:41:52-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 18:41:52-debug: refresh db assets success
2025-10-8 18:41:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 18:41:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 18:41:52-debug: asset-db:refresh-all-database (289ms)
2025-10-8 18:41:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 18:41:53-debug: refresh db internal success
2025-10-8 18:41:53-debug: refresh db i18n success
2025-10-8 18:41:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 18:41:54-debug: refresh db assets success
2025-10-8 18:41:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 18:41:54-debug: asset-db:refresh-all-database (202ms)
2025-10-8 18:41:54-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 18:42:24-debug: refresh db internal success
2025-10-8 18:42:24-debug: refresh db i18n success
2025-10-8 18:42:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 18:42:24-debug: refresh db assets success
2025-10-8 18:42:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 18:42:24-debug: asset-db:refresh-all-database (216ms)
2025-10-8 18:42:24-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 18:42:24-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 18:42:45-debug: refresh db internal success
2025-10-8 18:42:45-debug: refresh db i18n success
2025-10-8 18:42:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 18:42:45-debug: refresh db assets success
2025-10-8 18:42:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 18:42:45-debug: asset-db:refresh-all-database (198ms)
2025-10-8 18:43:31-debug: refresh db internal success
2025-10-8 18:43:31-debug: refresh db i18n success
2025-10-8 18:43:31-debug: refresh db assets success
2025-10-8 18:43:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 18:43:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 18:43:31-debug: asset-db:refresh-all-database (225ms)
2025-10-8 18:43:31-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 18:43:31-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 18:44:11-debug: refresh db internal success
2025-10-8 18:44:11-debug: refresh db i18n success
2025-10-8 18:44:11-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 18:44:11-debug: refresh db assets success
2025-10-8 18:44:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 18:44:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 18:44:11-debug: asset-db:refresh-all-database (234ms)
2025-10-8 18:46:13-debug: refresh db internal success
2025-10-8 18:46:13-debug: refresh db i18n success
2025-10-8 18:46:13-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 18:46:13-debug: refresh db assets success
2025-10-8 18:46:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 18:46:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 18:46:13-debug: asset-db:refresh-all-database (214ms)
2025-10-8 18:46:13-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 18:46:13-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 18:46:51-debug: refresh db internal success
2025-10-8 18:46:51-debug: refresh db i18n success
2025-10-8 18:46:51-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 18:46:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 18:46:51-debug: refresh db assets success
2025-10-8 18:46:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 18:46:51-debug: asset-db:refresh-all-database (242ms)
2025-10-8 18:48:06-debug: refresh db internal success
2025-10-8 18:48:06-debug: refresh db i18n success
2025-10-8 18:48:06-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 18:48:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 18:48:06-debug: refresh db assets success
2025-10-8 18:48:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 18:48:06-debug: asset-db:refresh-all-database (212ms)
2025-10-8 18:48:06-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 18:48:26-debug: refresh db internal success
2025-10-8 18:48:26-debug: refresh db i18n success
2025-10-8 18:48:26-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 18:48:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 18:48:26-debug: refresh db assets success
2025-10-8 18:48:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 18:48:26-debug: asset-db:refresh-all-database (223ms)
2025-10-8 18:48:26-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 18:48:52-debug: refresh db internal success
2025-10-8 18:48:52-debug: refresh db i18n success
2025-10-8 18:48:52-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 18:48:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 18:48:52-debug: refresh db assets success
2025-10-8 18:48:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 18:48:52-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 18:48:52-debug: asset-db:refresh-all-database (219ms)
2025-10-8 18:48:52-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 18:49:41-debug: refresh db internal success
2025-10-8 18:49:41-debug: refresh db i18n success
2025-10-8 18:49:41-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\Bullet.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 18:49:41-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 18:49:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 18:49:41-debug: refresh db assets success
2025-10-8 18:49:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 18:49:41-debug: asset-db:refresh-all-database (209ms)
2025-10-8 18:49:41-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 18:49:41-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 18:49:58-debug: refresh db internal success
2025-10-8 18:49:58-debug: refresh db i18n success
2025-10-8 18:49:58-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\Bullet.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 18:49:58-debug: refresh db assets success
2025-10-8 18:49:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 18:49:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 18:49:58-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 18:49:58-debug: asset-db:refresh-all-database (187ms)
2025-10-8 18:49:58-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 18:50:18-debug: refresh db internal success
2025-10-8 18:50:18-debug: refresh db i18n success
2025-10-8 18:50:18-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 18:50:18-debug: refresh db assets success
2025-10-8 18:50:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 18:50:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 18:50:18-debug: asset-db:refresh-all-database (209ms)
2025-10-8 18:50:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 18:51:17-debug: refresh db internal success
2025-10-8 18:51:17-debug: refresh db i18n success
2025-10-8 18:51:17-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 18:51:17-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\DefaultMove.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 18:51:17-debug: refresh db assets success
2025-10-8 18:51:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 18:51:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 18:51:17-debug: asset-db:refresh-all-database (229ms)
2025-10-8 18:51:17-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 18:51:17-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-8 18:51:25-debug: refresh db internal success
2025-10-8 18:51:25-debug: refresh db i18n success
2025-10-8 18:51:25-debug: refresh db assets success
2025-10-8 18:51:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 18:51:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 18:51:25-debug: asset-db:worker-effect-data-processing (2ms)
2025-10-8 18:51:25-debug: asset-db:refresh-all-database (168ms)
2025-10-8 18:51:25-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-10-8 18:52:20-debug: refresh db internal success
2025-10-8 18:52:20-debug: refresh db i18n success
2025-10-8 18:52:20-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\DefaultMove.ts
background: #aaff85; color: #000;
color: #000;
2025-10-8 18:52:20-debug: refresh db assets success
2025-10-8 18:52:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-8 18:52:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-8 18:52:20-debug: asset-db:refresh-all-database (215ms)
2025-10-8 18:52:20-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-8 18:52:20-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 09:19:27-debug: refresh db internal success
2025-10-9 09:19:27-debug: refresh db i18n success
2025-10-9 09:19:27-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\texture\item\Purple_bg.png@6c48a
background: #ffb8b8; color: #000;
color: #000;
2025-10-9 09:19:27-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\icon
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:27-debug: %cImport%c: E:\M2Game\Client\assets\resources\audio\sound
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:27-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\match\MatchResultUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:27-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\pk\FriendFusionCellUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:27-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\pk\FriendFusionUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:27-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\pk\FriendRankCellUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:27-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\texture\item\Purple_bg.png@f9941
background: #ffb8b8; color: #000;
color: #000;
2025-10-9 09:19:27-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\pk\FriendRankUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:27-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\texture\item\Purple_bg.png
background: #ffb8b8; color: #000;
color: #000;
2025-10-9 09:19:27-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\item\auto-atlas.pac
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:27-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_match\prefab\ui\MatchResultUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:27-debug: %cImport%c: E:\M2Game\Client\assets\resources\audio\sound\click.mp3
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:27-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_pk\prefab\ui\FriendFusionCellUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:27-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\item\purple_bg.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:27-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_pk\prefab\ui\FriendFusionUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:27-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_pk\prefab\ui\FriendRankCellUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:27-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_pk\prefab\ui\FriendRankUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\resources
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\item\purple_bg.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\resources\audio
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\texture\map\Bg_white_cloud.png
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\texture\map\Bg_white_cloud.png@6c48a
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\texture\map\Bg_white_cloud.png@f9941
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\scripts\core\base
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\event
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\texture\item
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_match\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_pk\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\FormationEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\wave\PathEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\texture\map
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\scripts\core\base\ResManager.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\base
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\pk
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\event\DataEvent.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\dyncTerrain
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\utils
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\mail
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\match
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\pk
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\story
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\base
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\base\Role.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\pk\PK.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\data\PathData.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\dyncTerrain\EmittierTerrain.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\move\DefaultMove.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\utils\UITools.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\SettlementResultUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\SettlementUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\mail\MailCellUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\match\MatchRankUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\pk\PKHistoryCellUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\pk\PKBuyUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\story\BuidingUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\base\ItemQuaIcon.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\base\AvatarIcon.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\LevelEmittier
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\LevelBaseUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\LevelBackgroundLayerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\LevelLayer.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\LevelLayerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\map\LevelNodeCheckOutScreen.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\ui\plane\PlaneBaseDebug.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\components\button
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\LevelEmittier\TraainEmittier_HighSky_Plane.prefab
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\components\button\ButtonPlus.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 09:19:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 09:19:28-debug: refresh db assets success
2025-10-9 09:19:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 09:19:28-debug: asset-db:refresh-all-database (509ms)
2025-10-9 09:19:28-debug: asset-db:worker-effect-data-processing (3ms)
2025-10-9 09:19:28-debug: asset-db-hook-engine-extends-afterRefresh (3ms)

import { _decorator, Component, Enum, misc, Node, UITransform, Vec2, Vec3 } from 'cc';
import { IMovable, MoveBase, eOrientationType } from './IMovable';
import PlaneBase from '../ui/plane/PlaneBase';
const { degreesToRadians, radiansToDegrees } = misc;
const { ccclass, property, executeInEditMode } = _decorator;

export type TargetingDelegate = () => PlaneBase | null;

@ccclass('DefaultMove')
@executeInEditMode
export class DefaultMove extends MoveBase {
    public get isTrackingTarget() { return this._trackingTimeLimit > 0; }
    // 追踪配置
    protected _trackingTimeLimit: number = 0;       // 追踪持续时间（毫秒，0表示无限制）
    protected _trackingAngleLimit: number = 180;    // 最大转向角度（度，180表示可以完全掉头）
    protected _targetingDelegate: TargetingDelegate | null = null;  // 选择目标委托
    protected _target: PlaneBase | null = null;            // 追踪的目标节点
    public get target() { return this._target; }

    public tick(dt: number): void {
        if (!this._isMovable) return;

        // 处理追踪逻辑
        if (this.isTrackingTarget) {
            this.updateTracking(dt);
        }

        const angleRadians = degreesToRadians(this.speedAngle);
        // Convert speed and angle to velocity vector
        let velocityX = this.speed * Math.cos(angleRadians);
        let velocityY = this.speed * Math.sin(angleRadians);

        // Convert acceleration and angle to acceleration vector
        if (this.acceleration !== 0) {
            const accelerationRadians = degreesToRadians(this.accelerationAngle);
            const accelerationX = this.acceleration * Math.cos(accelerationRadians);
            const accelerationY = this.acceleration * Math.sin(accelerationRadians);
            // Update velocity vector: v = v + a * dt
            velocityX += accelerationX * dt;
            velocityY += accelerationY * dt;
        }

        // Convert back to speed and angle
        this.speed = Math.sqrt(velocityX * velocityX + velocityY * velocityY);
        this.speedAngle = radiansToDegrees(Math.atan2(velocityY, velocityX));

        // Update position: p = p + v * dt
        if (velocityX !== 0 || velocityY !== 0) {
            // Update base position (main movement path)
            this._basePosition.x += velocityX * dt;
            this._basePosition.y += velocityY * dt;

            // Start with base position
            this._position.set(this._basePosition);
            this.updateTilting(degreesToRadians(this.speedAngle), dt, this._position);

            this.node.setPosition(this._position);
            this.checkVisibility();

            this.updateOrientation(dt);
        }
    }

    protected getDesiredOrientation(dt: number): number {
        switch (this.orientationType) {
            case eOrientationType.Path:
                return this.speedAngle;
            case eOrientationType.Target:
                if (this.target) {
                    const targetPos = this.target.node.position;
                    const currentPos = this._basePosition;
                    return radiansToDegrees(Math.atan2(targetPos.y - currentPos.y, targetPos.x - currentPos.x));
                }
                break;
            case eOrientationType.Fixed:
                return this.orientationParam;
            case eOrientationType.Rotate:
                return this.orientation + this.orientationParam * dt;
        }
        return this.orientation;
    }

    protected updateOrientation(dt: number): void {
        this.orientation = this.getDesiredOrientation(dt);
        this.node.setRotationFromEuler(0, 0, this.orientation + this.forwardOrientation);
    }

    /**
     * 更新追踪逻辑
     * @param dt 时间增量（秒）
     * @returns 追踪结果，包含新的速度向量，如果停止追踪则返回null
     */
    protected updateTracking(dt: number): void {
        if (!this._target || this._target.isDead) 
        {
            this._target = this._targetingDelegate ? this._targetingDelegate() : null;
            // still null
            if (!this._target) return;
        }

        const targetPos = this._target.node.position;
        const currentPos = this._position;

        // Calculate direction to target
        const directionX = targetPos.x - currentPos.x;
        const directionY = targetPos.y - currentPos.y;
        const distance = Math.sqrt(directionX * directionX + directionY * directionY);

        // 检查是否应该停止追踪
        if (this._trackingTimeLimit > 0) {
            this._trackingTimeLimit -= dt * 1000; // dt是秒，转换为毫秒
            if (this._trackingTimeLimit <= 0) {
                return;
            }
        }

        if (distance > 0) {
            // Calculate desired angle to target
            const desiredAngle = radiansToDegrees(Math.atan2(directionY, directionX));

            // Smoothly adjust speedAngle toward target
            const angleDiff = desiredAngle - this.speedAngle;
            // Normalize angle difference to [-180, 180] range
            const normalizedAngleDiff = ((angleDiff + 180) % 360) - 180;

            // 检查转向角度限制
            if (Math.abs(normalizedAngleDiff) > this._trackingAngleLimit) {
                return;
            }

            // Apply tracking adjustment
            const trackingStrength = 1.0; // Can be made configurable
            const maxTurnRate = this.turnSpeed; // degrees per second
            const turnAmount = Math.min(Math.abs(normalizedAngleDiff), maxTurnRate * dt) * Math.sign(normalizedAngleDiff);

            this.speedAngle += turnAmount * trackingStrength;
        }
    }

    /**
     * Set the target
     */
    // public setTarget(target: Entity | null): DefaultMove {
    //     this._target = target ? target.node : null;
    //     return this;
    // }
    public setTargetingDelegate(func: TargetingDelegate | null): DefaultMove {
        this._targetingDelegate = func;
        return this;
    }

    /**
     * Set whether to track the target
     */
    public stopTracking(): void {
        this._trackingTimeLimit = 0;
    }

    /**
     * 设置追踪持续时间
     * @param duration 持续时间（毫秒）
     */
    public setTrackingDuration(duration: number): DefaultMove {
        this._trackingTimeLimit = duration;
        return this;
    }

    /**
     * 设置最大转向角度
     * @param angle 最大角度（度）
     */
    public setTrackingMaxTurnAngle(angle: number): DefaultMove {
        this._trackingAngleLimit = angle;
        return this;
    }

    public setMovable(movable: boolean): DefaultMove {
        this._isMovable = movable;

        if (this._isMovable) {
            // Initialize base position to current node position
            this.node.getPosition(this._basePosition);
            // 重置可见性状态
            // this._wasVisible = this._isVisible = false;
        }

        return this;
    }
}
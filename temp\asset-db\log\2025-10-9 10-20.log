2025-10-9 10:20:11-log: Cannot access game frame or container.
2025-10-9 10:20:11-debug: asset-db:require-engine-code (475ms)
2025-10-9 10:20:11-log: meshopt wasm decoder initialized
2025-10-9 10:20:11-log: [bullet]:bullet wasm lib loaded.
2025-10-9 10:20:11-log: [box2d]:box2d wasm lib loaded.
2025-10-9 10:20:11-log: Cocos Creator v3.8.6
2025-10-9 10:20:11-log: Using legacy pipeline
2025-10-9 10:20:11-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:30.80MB, end 79.96MB, increase: 49.15MB
2025-10-9 10:20:11-debug: [Assets Memory track]: asset-db-plugin-register: programming start:81.08MB, end 83.83MB, increase: 2.74MB
2025-10-9 10:20:11-log: Forward render pipeline initialized.
2025-10-9 10:20:12-debug: [Assets Memory track]: asset-db-plugin-register: project start:80.94MB, end 228.35MB, increase: 147.42MB
2025-10-9 10:20:12-debug: [Assets Memory track]: asset-db-plugin-register: engine-extends start:225.03MB, end 228.15MB, increase: 3.12MB
2025-10-9 10:20:12-debug: [Assets Memory track]: asset-db-plugin-register: builder start:83.88MB, end 224.78MB, increase: 140.90MB
2025-10-9 10:20:12-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:79.98MB, end 228.38MB, increase: 148.40MB
2025-10-9 10:20:12-debug: run package(honor-mini-game) handler(enable) start
2025-10-9 10:20:12-debug: run package(huawei-agc) handler(enable) start
2025-10-9 10:20:12-debug: run package(honor-mini-game) handler(enable) success!
2025-10-9 10:20:12-debug: run package(huawei-quick-game) handler(enable) start
2025-10-9 10:20:12-debug: run package(huawei-quick-game) handler(enable) success!
2025-10-9 10:20:12-debug: run package(ios) handler(enable) start
2025-10-9 10:20:12-debug: run package(huawei-agc) handler(enable) success!
2025-10-9 10:20:12-debug: run package(ios) handler(enable) success!
2025-10-9 10:20:12-debug: run package(linux) handler(enable) success!
2025-10-9 10:20:12-debug: run package(linux) handler(enable) start
2025-10-9 10:20:12-debug: run package(mac) handler(enable) start
2025-10-9 10:20:12-debug: run package(mac) handler(enable) success!
2025-10-9 10:20:12-debug: run package(migu-mini-game) handler(enable) start
2025-10-9 10:20:12-debug: run package(migu-mini-game) handler(enable) success!
2025-10-9 10:20:12-debug: run package(native) handler(enable) success!
2025-10-9 10:20:12-debug: run package(native) handler(enable) start
2025-10-9 10:20:12-debug: run package(oppo-mini-game) handler(enable) start
2025-10-9 10:20:12-debug: run package(ohos) handler(enable) start
2025-10-9 10:20:12-debug: run package(ohos) handler(enable) success!
2025-10-9 10:20:12-debug: run package(runtime-dev-tools) handler(enable) success!
2025-10-9 10:20:12-debug: run package(oppo-mini-game) handler(enable) success!
2025-10-9 10:20:12-debug: run package(taobao-mini-game) handler(enable) start
2025-10-9 10:20:12-debug: run package(taobao-mini-game) handler(enable) success!
2025-10-9 10:20:12-debug: run package(vivo-mini-game) handler(enable) start
2025-10-9 10:20:12-debug: run package(runtime-dev-tools) handler(enable) start
2025-10-9 10:20:12-debug: run package(vivo-mini-game) handler(enable) success!
2025-10-9 10:20:12-debug: run package(web-mobile) handler(enable) start
2025-10-9 10:20:12-debug: run package(web-mobile) handler(enable) success!
2025-10-9 10:20:12-debug: run package(web-desktop) handler(enable) success!
2025-10-9 10:20:12-debug: run package(wechatgame) handler(enable) start
2025-10-9 10:20:12-debug: run package(web-desktop) handler(enable) start
2025-10-9 10:20:12-debug: run package(wechatgame) handler(enable) success!
2025-10-9 10:20:12-debug: run package(windows) handler(enable) start
2025-10-9 10:20:12-debug: run package(wechatprogram) handler(enable) start
2025-10-9 10:20:12-debug: run package(wechatprogram) handler(enable) success!
2025-10-9 10:20:12-debug: run package(xiaomi-quick-game) handler(enable) start
2025-10-9 10:20:12-debug: run package(cocos-service) handler(enable) start
2025-10-9 10:20:12-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-10-9 10:20:12-debug: run package(windows) handler(enable) success!
2025-10-9 10:20:12-debug: run package(cocos-service) handler(enable) success!
2025-10-9 10:20:12-debug: run package(im-plugin) handler(enable) success!
2025-10-9 10:20:12-debug: run package(im-plugin) handler(enable) start
2025-10-9 10:20:12-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-10-9 10:20:12-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-10-9 10:20:12-debug: start refresh asset from db://assets/editor/enum-gen/EmitterEnum.ts...
2025-10-9 10:20:12-debug: refresh asset db://assets/editor/enum-gen success
2025-10-9 10:20:12-debug: run package(emitter-editor) handler(enable) start
2025-10-9 10:20:12-debug: start refresh asset from db://assets/editor/enum-gen/EnemyEnum.ts...
2025-10-9 10:20:12-debug: run package(emitter-editor) handler(enable) success!
2025-10-9 10:20:12-debug: refresh asset db://assets/editor/enum-gen success
2025-10-9 10:20:12-debug: run package(i18n) handler(enable) start
2025-10-9 10:20:12-debug: run package(level-editor) handler(enable) success!
2025-10-9 10:20:12-debug: run package(level-editor) handler(enable) start
2025-10-9 10:20:12-debug: run package(placeholder) handler(enable) start
2025-10-9 10:20:12-debug: run package(i18n) handler(enable) success!
2025-10-9 10:20:12-debug: run package(placeholder) handler(enable) success!
2025-10-9 10:20:12-debug: asset-db:worker-init: initPlugin (1175ms)
2025-10-9 10:20:12-debug: [Assets Memory track]: asset-db:worker-init start:30.79MB, end 225.15MB, increase: 194.35MB
2025-10-9 10:20:12-debug: Run asset db hook programming:beforePreStart ...
2025-10-9 10:20:12-debug: Run asset db hook programming:beforePreStart success!
2025-10-9 10:20:12-debug: Run asset db hook engine-extends:beforePreStart ...
2025-10-9 10:20:12-debug: Run asset db hook engine-extends:beforePreStart success!
2025-10-9 10:20:12-debug: asset-db-hook-programming-beforePreStart (74ms)
2025-10-9 10:20:12-debug: asset-db:worker-init (1794ms)
2025-10-9 10:20:12-debug: asset-db-hook-engine-extends-beforePreStart (73ms)
2025-10-9 10:20:12-debug: Preimport db internal success
2025-10-9 10:20:12-debug: %cImport%c: E:\M2Game\Client\assets\editor\enum-gen\EmitterEnum.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:20:12-debug: %cImport%c: E:\M2Game\Client\assets\editor\enum-gen\EnemyEnum.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:20:12-debug: Preimport db assets success
2025-10-9 10:20:12-debug: Preimport db i18n success
2025-10-9 10:20:12-debug: Run asset db hook programming:afterPreStart ...
2025-10-9 10:20:12-debug: starting packer-driver...
2025-10-9 10:20:20-debug: initialize scripting environment...
2025-10-9 10:20:20-debug: [[Executor]] prepare before lock
2025-10-9 10:20:20-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-10-9 10:20:20-debug: Run asset db hook programming:afterPreStart success!
2025-10-9 10:20:20-debug: [[Executor]] prepare after unlock
2025-10-9 10:20:20-debug: Run asset db hook engine-extends:afterPreStart ...
2025-10-9 10:20:20-debug: Run asset db hook engine-extends:afterPreStart success!
2025-10-9 10:20:20-debug: [Assets Memory track]: asset-db:worker-init: preStart start:225.16MB, end 233.66MB, increase: 8.50MB
2025-10-9 10:20:20-debug: Start up the 'internal' database...
2025-10-9 10:20:20-debug: asset-db-hook-programming-afterPreStart (7337ms)
2025-10-9 10:20:20-debug: asset-db:worker-effect-data-processing (201ms)
2025-10-9 10:20:20-debug: asset-db-hook-engine-extends-afterPreStart (202ms)
2025-10-9 10:20:20-debug: Start up the 'assets' database...
2025-10-9 10:20:20-debug: asset-db:worker-startup-database[internal] (7710ms)
2025-10-9 10:20:20-debug: Start up the 'i18n' database...
2025-10-9 10:20:20-debug: asset-db:worker-startup-database[assets] (7687ms)
2025-10-9 10:20:20-debug: lazy register asset handler *
2025-10-9 10:20:20-debug: lazy register asset handler directory
2025-10-9 10:20:20-debug: lazy register asset handler spine-data
2025-10-9 10:20:20-debug: lazy register asset handler dragonbones
2025-10-9 10:20:20-debug: lazy register asset handler dragonbones-atlas
2025-10-9 10:20:20-debug: lazy register asset handler text
2025-10-9 10:20:20-debug: lazy register asset handler json
2025-10-9 10:20:20-debug: lazy register asset handler terrain
2025-10-9 10:20:20-debug: lazy register asset handler javascript
2025-10-9 10:20:20-debug: lazy register asset handler typescript
2025-10-9 10:20:20-debug: lazy register asset handler scene
2025-10-9 10:20:20-debug: lazy register asset handler prefab
2025-10-9 10:20:20-debug: lazy register asset handler sprite-frame
2025-10-9 10:20:20-debug: lazy register asset handler image
2025-10-9 10:20:20-debug: lazy register asset handler tiled-map
2025-10-9 10:20:20-debug: lazy register asset handler alpha-image
2025-10-9 10:20:20-debug: lazy register asset handler buffer
2025-10-9 10:20:20-debug: lazy register asset handler sign-image
2025-10-9 10:20:20-debug: lazy register asset handler erp-texture-cube
2025-10-9 10:20:20-debug: lazy register asset handler texture-cube
2025-10-9 10:20:20-debug: lazy register asset handler texture
2025-10-9 10:20:20-debug: lazy register asset handler render-texture
2025-10-9 10:20:20-debug: lazy register asset handler rt-sprite-frame
2025-10-9 10:20:20-debug: lazy register asset handler gltf
2025-10-9 10:20:20-debug: lazy register asset handler texture-cube-face
2025-10-9 10:20:20-debug: lazy register asset handler gltf-mesh
2025-10-9 10:20:20-debug: lazy register asset handler gltf-animation
2025-10-9 10:20:20-debug: lazy register asset handler gltf-scene
2025-10-9 10:20:20-debug: lazy register asset handler gltf-embeded-image
2025-10-9 10:20:20-debug: lazy register asset handler gltf-material
2025-10-9 10:20:20-debug: lazy register asset handler gltf-skeleton
2025-10-9 10:20:20-debug: lazy register asset handler physics-material
2025-10-9 10:20:20-debug: lazy register asset handler fbx
2025-10-9 10:20:20-debug: lazy register asset handler material
2025-10-9 10:20:20-debug: lazy register asset handler effect-header
2025-10-9 10:20:20-debug: lazy register asset handler effect
2025-10-9 10:20:20-debug: lazy register asset handler animation-clip
2025-10-9 10:20:20-debug: lazy register asset handler audio-clip
2025-10-9 10:20:20-debug: lazy register asset handler animation-graph-variant
2025-10-9 10:20:20-debug: lazy register asset handler animation-graph
2025-10-9 10:20:20-debug: lazy register asset handler animation-mask
2025-10-9 10:20:20-debug: lazy register asset handler ttf-font
2025-10-9 10:20:20-debug: lazy register asset handler bitmap-font
2025-10-9 10:20:20-debug: lazy register asset handler sprite-atlas
2025-10-9 10:20:20-debug: lazy register asset handler particle
2025-10-9 10:20:20-debug: lazy register asset handler auto-atlas
2025-10-9 10:20:20-debug: lazy register asset handler render-pipeline
2025-10-9 10:20:20-debug: lazy register asset handler label-atlas
2025-10-9 10:20:20-debug: lazy register asset handler render-stage
2025-10-9 10:20:20-debug: lazy register asset handler render-flow
2025-10-9 10:20:20-debug: lazy register asset handler instantiation-skeleton
2025-10-9 10:20:20-debug: lazy register asset handler instantiation-animation
2025-10-9 10:20:20-debug: lazy register asset handler instantiation-mesh
2025-10-9 10:20:20-debug: lazy register asset handler video-clip
2025-10-9 10:20:20-debug: lazy register asset handler instantiation-material
2025-10-9 10:20:20-debug: asset-db:worker-startup-database[i18n] (7614ms)
2025-10-9 10:20:20-debug: asset-db:start-database (7824ms)
2025-10-9 10:20:20-debug: asset-db:ready (11255ms)
2025-10-9 10:20:20-debug: fix the bug of updateDefaultUserData
2025-10-9 10:20:20-debug: init worker message success
2025-10-9 10:20:20-debug: [Build Memory track]: builder:worker-init start:193.49MB, end 206.76MB, increase: 13.27MB
2025-10-9 10:20:20-debug: programming:execute-script (4ms)
2025-10-9 10:20:20-debug: builder:worker-init (258ms)
2025-10-9 10:21:05-debug: refresh db internal success
2025-10-9 10:21:05-debug: refresh db assets success
2025-10-9 10:21:05-debug: refresh db i18n success
2025-10-9 10:21:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 10:21:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 10:21:05-debug: asset-db:refresh-all-database (233ms)
2025-10-9 10:21:05-debug: asset-db:worker-effect-data-processing (2ms)
2025-10-9 10:21:05-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-10-9 10:21:07-debug: start refresh asset from db://assets/resources/game/level/background/Prefab/Config...
2025-10-9 10:21:07-debug: start refresh asset from db://assets/resources/game/level/background/Prefab/Config...
2025-10-9 10:21:07-debug: start refresh asset from db://assets/resources/game/level/background/Prefab/Config...
2025-10-9 10:21:07-debug: start refresh asset from db://assets/resources/game/level/background/Prefab/Config...
2025-10-9 10:21:07-debug: start refresh asset from db://assets/resources/game/level/background/Prefab/Config...
2025-10-9 10:21:07-debug: start refresh asset from db://assets/resources/game/level/background/Prefab/Config...
2025-10-9 10:21:07-debug: start refresh asset from db://assets/resources/game/level/background/Prefab/Config...
2025-10-9 10:21:07-debug: start refresh asset from db://assets/resources/game/level/background/Prefab/Config...
2025-10-9 10:21:07-debug: start refresh asset from db://assets/resources/game/level/background/Prefab/Config...
2025-10-9 10:21:07-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_StartCloud02.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:21:07-debug: refresh asset db://assets/resources/game/level/background/Prefab success
2025-10-9 10:21:07-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_Island_01.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:21:07-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_Island_02.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:21:07-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_Island_16.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:21:07-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_Island_15.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:21:07-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_Island_14.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:21:07-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_StartCloud01.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:21:07-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandomNod_LayerClouds_01_short.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:21:07-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandomNod_LayerClouds_02_Long.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:21:07-debug: refresh asset db://assets/resources/game/level/background/Prefab success
2025-10-9 10:21:07-debug: refresh asset db://assets/resources/game/level/background/Prefab success
2025-10-9 10:21:07-debug: refresh asset db://assets/resources/game/level/background/Prefab success
2025-10-9 10:21:07-debug: refresh asset db://assets/resources/game/level/background/Prefab success
2025-10-9 10:21:07-debug: refresh asset db://assets/resources/game/level/background/Prefab success
2025-10-9 10:21:07-debug: refresh asset db://assets/resources/game/level/background/Prefab success
2025-10-9 10:21:07-debug: refresh asset db://assets/resources/game/level/background/Prefab success
2025-10-9 10:21:07-debug: refresh asset db://assets/resources/game/level/background/Prefab success
2025-10-9 10:21:07-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandomNod_LayerClouds_01_short.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:21:07-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_StartCloud02.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:21:07-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_StartCloud01.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:21:07-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_Island_01.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:21:07-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandomNod_LayerClouds_02_Long.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:21:07-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_Island_02.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:21:07-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_Island_16.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:21:07-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_Island_15.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:21:07-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_Island_14.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:21:07-debug: asset-db:reimport-asset8e9a0448-202a-4b18-aeb4-2c6758698cde (83ms)
2025-10-9 10:21:07-debug: asset-db:reimport-asset5165156c-8464-4aee-97ef-3cc8a5619632 (84ms)
2025-10-9 10:21:07-debug: asset-db:reimport-asset43037389-e38f-4941-a90e-98d7f0819b94 (84ms)
2025-10-9 10:21:07-debug: asset-db:reimport-asset271467a7-62c8-465d-a373-bf2f4d45e3bc (84ms)
2025-10-9 10:21:07-debug: asset-db:reimport-asset4eb60cb1-351f-4b70-aeb4-104a20af3493 (84ms)
2025-10-9 10:21:07-debug: asset-db:reimport-assetd6f91465-f5e3-4846-b3d1-0c56dfb033bc (84ms)
2025-10-9 10:21:07-debug: asset-db:reimport-assetd8162466-ff6e-4785-bdfe-4ebd4372517e (83ms)
2025-10-9 10:21:07-debug: asset-db:reimport-asset4cb5bbf6-5eba-49a3-891e-63577031516f (84ms)
2025-10-9 10:21:07-debug: asset-db:reimport-asset0aca13ea-da5c-4055-ae7d-b91e72dc8fa8 (83ms)
2025-10-9 10:49:52-debug: refresh db internal success
2025-10-9 10:49:53-debug: refresh db assets success
2025-10-9 10:49:53-debug: refresh db i18n success
2025-10-9 10:49:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 10:49:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 10:49:53-debug: asset-db:refresh-all-database (360ms)
2025-10-9 10:50:00-debug: refresh db internal success
2025-10-9 10:50:00-debug: refresh db assets success
2025-10-9 10:50:00-debug: refresh db i18n success
2025-10-9 10:50:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 10:50:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 10:50:00-debug: asset-db:refresh-all-database (175ms)
2025-10-9 10:55:45-debug: refresh db internal success
2025-10-9 10:55:45-debug: refresh db assets success
2025-10-9 10:55:45-debug: refresh db i18n success
2025-10-9 10:55:45-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 10:55:45-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 10:55:45-debug: asset-db:refresh-all-database (225ms)
2025-10-9 10:55:45-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 10:55:45-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 10:55:50-debug: start refresh asset from db://assets/resources/game/level/background/Prefab/Config...
2025-10-9 10:55:50-debug: start refresh asset from db://assets/resources/game/level/background/Prefab/Config...
2025-10-9 10:55:50-debug: start refresh asset from db://assets/resources/game/level/background/Prefab/Config...
2025-10-9 10:55:50-debug: start refresh asset from db://assets/resources/game/level/background/Prefab/Config...
2025-10-9 10:55:50-debug: start refresh asset from db://assets/resources/game/level/background/Prefab/Config...
2025-10-9 10:55:50-debug: start refresh asset from db://assets/resources/game/level/background/Prefab/Config...
2025-10-9 10:55:50-debug: start refresh asset from db://assets/resources/game/level/background/Prefab/Config...
2025-10-9 10:55:50-debug: start refresh asset from db://assets/resources/game/level/background/Prefab/Config...
2025-10-9 10:55:50-debug: start refresh asset from db://assets/resources/game/level/background/Prefab/Config...
2025-10-9 10:55:50-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_StartCloud02.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:55:50-debug: refresh asset db://assets/resources/game/level/background/Prefab success
2025-10-9 10:55:50-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_Island_02.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:55:50-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_Island_15.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:55:50-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_Island_01.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:55:50-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_Island_14.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:55:50-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_Island_16.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:55:50-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_StartCloud01.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:55:50-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandomNod_LayerClouds_01_short.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:55:50-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandomNod_LayerClouds_02_Long.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:55:50-debug: refresh asset db://assets/resources/game/level/background/Prefab success
2025-10-9 10:55:50-debug: refresh asset db://assets/resources/game/level/background/Prefab success
2025-10-9 10:55:50-debug: refresh asset db://assets/resources/game/level/background/Prefab success
2025-10-9 10:55:50-debug: refresh asset db://assets/resources/game/level/background/Prefab success
2025-10-9 10:55:50-debug: refresh asset db://assets/resources/game/level/background/Prefab success
2025-10-9 10:55:50-debug: refresh asset db://assets/resources/game/level/background/Prefab success
2025-10-9 10:55:50-debug: refresh asset db://assets/resources/game/level/background/Prefab success
2025-10-9 10:55:50-debug: refresh asset db://assets/resources/game/level/background/Prefab success
2025-10-9 10:55:50-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_StartCloud02.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:55:50-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_StartCloud01.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:55:50-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_Island_01.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:55:50-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandomNod_LayerClouds_01_short.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:55:50-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandomNod_LayerClouds_02_Long.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:55:50-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_Island_14.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:55:50-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_Island_16.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:55:50-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_Island_02.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:55:50-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\background\Prefab\Config\RandNod_Island_15.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:55:50-debug: asset-db:reimport-asset5165156c-8464-4aee-97ef-3cc8a5619632 (55ms)
2025-10-9 10:55:50-debug: asset-db:reimport-asset271467a7-62c8-465d-a373-bf2f4d45e3bc (55ms)
2025-10-9 10:55:50-debug: asset-db:reimport-asset8e9a0448-202a-4b18-aeb4-2c6758698cde (55ms)
2025-10-9 10:55:50-debug: asset-db:reimport-asset4eb60cb1-351f-4b70-aeb4-104a20af3493 (55ms)
2025-10-9 10:55:50-debug: asset-db:reimport-asset43037389-e38f-4941-a90e-98d7f0819b94 (54ms)
2025-10-9 10:55:50-debug: asset-db:reimport-assetd8162466-ff6e-4785-bdfe-4ebd4372517e (54ms)
2025-10-9 10:55:50-debug: asset-db:reimport-asset0aca13ea-da5c-4055-ae7d-b91e72dc8fa8 (54ms)
2025-10-9 10:55:50-debug: asset-db:reimport-asset4cb5bbf6-5eba-49a3-891e-63577031516f (54ms)
2025-10-9 10:55:50-debug: asset-db:reimport-assetd6f91465-f5e3-4846-b3d1-0c56dfb033bc (54ms)
2025-10-9 10:55:59-debug: %cImport%c: E:\M2Game\Client\assets\resources\game\level\131000001.json
background: #aaff85; color: #000;
color: #000;
2025-10-9 10:55:59-debug: asset-db:reimport-asset8e352073-9af1-4033-b6b6-f5db6fdccbc6 (5ms)
2025-10-9 10:59:43-debug: refresh db internal success
2025-10-9 10:59:43-debug: refresh db assets success
2025-10-9 10:59:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 10:59:43-debug: refresh db i18n success
2025-10-9 10:59:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 10:59:43-debug: asset-db:refresh-all-database (206ms)
2025-10-9 10:59:54-debug: refresh db internal success
2025-10-9 10:59:54-debug: refresh db assets success
2025-10-9 10:59:54-debug: refresh db i18n success
2025-10-9 10:59:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 10:59:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 10:59:54-debug: asset-db:refresh-all-database (164ms)
2025-10-9 10:59:59-debug: refresh db internal success
2025-10-9 10:59:59-debug: refresh db assets success
2025-10-9 10:59:59-debug: refresh db i18n success
2025-10-9 10:59:59-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 10:59:59-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 10:59:59-debug: asset-db:refresh-all-database (168ms)
2025-10-9 10:59:59-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 11:00:06-debug: refresh db internal success
2025-10-9 11:00:06-debug: refresh db assets success
2025-10-9 11:00:06-debug: refresh db i18n success
2025-10-9 11:00:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 11:00:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 11:00:06-debug: asset-db:refresh-all-database (178ms)
2025-10-9 11:00:06-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 11:20:41-debug: refresh db internal success
2025-10-9 11:20:41-debug: %cImport%c: E:\M2Game\Client\assets\editor\plane\PlaneEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 11:20:41-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\game\manager\EnemyManager.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 11:20:41-debug: refresh db assets success
2025-10-9 11:20:41-debug: refresh db i18n success
2025-10-9 11:20:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 11:20:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 11:20:41-debug: asset-db:refresh-all-database (189ms)
2025-10-9 11:20:50-debug: refresh db internal success
2025-10-9 11:20:51-debug: refresh db assets success
2025-10-9 11:20:51-debug: refresh db i18n success
2025-10-9 11:20:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 11:20:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 11:20:51-debug: asset-db:refresh-all-database (196ms)
2025-10-9 11:25:51-debug: refresh db internal success
2025-10-9 11:25:51-debug: %cImport%c: E:\M2Game\Client\assets\editor\plane\PlaneEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 11:25:51-debug: refresh db assets success
2025-10-9 11:25:51-debug: refresh db i18n success
2025-10-9 11:25:51-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 11:25:51-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 11:25:51-debug: asset-db:refresh-all-database (175ms)
2025-10-9 11:25:51-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 11:25:51-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 11:25:57-debug: refresh db internal success
2025-10-9 11:25:57-debug: refresh db assets success
2025-10-9 11:25:57-debug: refresh db i18n success
2025-10-9 11:25:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 11:25:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 11:25:57-debug: asset-db:refresh-all-database (185ms)
2025-10-9 11:27:01-debug: refresh db internal success
2025-10-9 11:27:01-debug: %cImport%c: E:\M2Game\Client\assets\editor\plane\PlaneEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 11:27:01-debug: refresh db assets success
2025-10-9 11:27:01-debug: refresh db i18n success
2025-10-9 11:27:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 11:27:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 11:27:01-debug: asset-db:refresh-all-database (226ms)
2025-10-9 11:27:01-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 11:27:01-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-10-9 11:33:14-debug: refresh db internal success
2025-10-9 11:33:14-debug: %cImport%c: E:\M2Game\Client\assets\editor\plane\PlaneEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-10-9 11:33:14-debug: refresh db assets success
2025-10-9 11:33:14-debug: refresh db i18n success
2025-10-9 11:33:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-10-9 11:33:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-10-9 11:33:14-debug: asset-db:refresh-all-database (186ms)
2025-10-9 11:33:14-debug: asset-db:worker-effect-data-processing (1ms)
2025-10-9 11:33:14-debug: asset-db-hook-engine-extends-afterRefresh (1ms)

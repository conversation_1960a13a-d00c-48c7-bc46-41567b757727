System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Label, Node, ProgressBar, tween, BaseUI, UILayer, UIMgr, BundleName, ButtonPlus, StatisticsUI, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _dec10, _dec11, _dec12, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _descriptor9, _descriptor10, _descriptor11, _crd, ccclass, property, SettlementResultUI;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIOpt(extras) {
    _reporterNs.report("UIOpt", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "../../const/BundleConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "./components/button/ButtonPlus", _context.meta, extras);
  }

  function _reportPossibleCrUseOfStatisticsUI(extras) {
    _reporterNs.report("StatisticsUI", "./StatisticsUI", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Label = _cc.Label;
      Node = _cc.Node;
      ProgressBar = _cc.ProgressBar;
      tween = _cc.tween;
    }, function (_unresolved_2) {
      BaseUI = _unresolved_2.BaseUI;
      UILayer = _unresolved_2.UILayer;
      UIMgr = _unresolved_2.UIMgr;
    }, function (_unresolved_3) {
      BundleName = _unresolved_3.BundleName;
    }, function (_unresolved_4) {
      ButtonPlus = _unresolved_4.ButtonPlus;
    }, function (_unresolved_5) {
      StatisticsUI = _unresolved_5.StatisticsUI;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "bbeb8I5qipNA7IJWaqo9Yj9", "SettlementResultUI", undefined);

      __checkObsolete__(['_decorator', 'Label', 'Node', 'ProgressBar', 'resources', 'Sprite', 'SpriteFrame', 'tween']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("SettlementResultUI", SettlementResultUI = (_dec = ccclass('SettlementResultUI'), _dec2 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec3 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec4 = property(Label), _dec5 = property(Label), _dec6 = property(Label), _dec7 = property(Node), _dec8 = property(Node), _dec9 = property(Node), _dec10 = property(Label), _dec11 = property(ProgressBar), _dec12 = property(Label), _dec(_class = (_class2 = class SettlementResultUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "btnOK", _descriptor, this);

          _initializerDefineProperty(this, "btnNext", _descriptor2, this);

          _initializerDefineProperty(this, "lblScore", _descriptor3, this);

          _initializerDefineProperty(this, "scoreAdd", _descriptor4, this);

          _initializerDefineProperty(this, "scoreHigh", _descriptor5, this);

          _initializerDefineProperty(this, "nodeStar1", _descriptor6, this);

          _initializerDefineProperty(this, "nodeStar2", _descriptor7, this);

          _initializerDefineProperty(this, "nodeStar3", _descriptor8, this);

          _initializerDefineProperty(this, "lblLevel", _descriptor9, this);

          _initializerDefineProperty(this, "expProBar", _descriptor10, this);

          _initializerDefineProperty(this, "lblExp", _descriptor11, this);

          this._scoreTween = void 0;
          // 分数动画的 tween 引用
          this._expTween = void 0;
        }

        // 经验条动画的 tween 引用
        static getUrl() {
          return "prefab/ui/SettlementResultUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Top;
        }

        static getBundleName() {
          return (_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).Home;
        }

        static getUIOption() {
          return {
            isClickBgCloseUI: false
          };
        }

        onLoad() {}

        onOKClick() {
          var _this = this;

          return _asyncToGenerator(function* () {
            // 停止所有动画
            if (_this._scoreTween) _this._scoreTween.stop();
            if (_this._expTween) _this._expTween.stop();
            (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).closeUI(SettlementResultUI);
          })();
        }

        onNextClick() {
          var _this2 = this;

          return _asyncToGenerator(function* () {
            // 停止所有动画
            if (_this2._scoreTween) _this2._scoreTween.stop();
            if (_this2._expTween) _this2._expTween.stop();
            yield (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).openUI(_crd && StatisticsUI === void 0 ? (_reportPossibleCrUseOfStatisticsUI({
              error: Error()
            }), StatisticsUI) : StatisticsUI);
            (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).closeUI(SettlementResultUI);
          })();
        }

        onShow() {
          var _this3 = this;

          return _asyncToGenerator(function* () {
            _this3.setNodeStar(_this3.nodeStar1, 1);

            _this3.setNodeStar(_this3.nodeStar2, 2);

            _this3.setNodeStar(_this3.nodeStar3, 3);

            _this3.btnOK.addClick(_this3.onOKClick, _this3);

            _this3.btnNext.addClick(_this3.onNextClick, _this3);

            _this3.scoreAdd.string = "分数加成 " + 123 + "%";
            _this3.lblScore.string = "0";
            _this3.scoreHigh.string = "历史最高分 " + 10000;
            var gap = 0.5; // 分数动画

            var score = 1000;
            _this3._scoreTween = tween({
              value: 0
            }).to(gap, {
              value: score
            }, {
              onUpdate: target => {
                if (!_this3.lblScore || target === undefined) return;
                _this3.lblScore.string = Math.round(target.value).toString();
              }
            }).start();
            var exp = 0.8;
            var finalExp = 10000; // 最终值
            // 经验条动画

            _this3._expTween = tween(_this3.expProBar).to(gap, {
              progress: exp
            }, {
              onUpdate: () => {
                if (!_this3.expProBar) return;
                _this3.lblExp.string = Math.round(finalExp * _this3.expProBar.progress) + "/" + finalExp;
              }
            }).start();
            _this3.lblLevel.string = "lv" + 30;
          })();
        }

        onHide() {
          return _asyncToGenerator(function* () {})();
        }

        onClose() {
          var _this4 = this;

          return _asyncToGenerator(function* () {
            // 停止所有动画
            if (_this4._scoreTween) _this4._scoreTween.stop();
            if (_this4._expTween) _this4._expTween.stop();
          })();
        }

        setNodeStar(node, val) {
          node.getComponentInChildren(Label).string = val.toString();
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "btnOK", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "btnNext", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "lblScore", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "scoreAdd", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "scoreHigh", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "nodeStar1", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "nodeStar2", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor8 = _applyDecoratedDescriptor(_class2.prototype, "nodeStar3", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor9 = _applyDecoratedDescriptor(_class2.prototype, "lblLevel", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor10 = _applyDecoratedDescriptor(_class2.prototype, "expProBar", [_dec11], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor11 = _applyDecoratedDescriptor(_class2.prototype, "lblExp", [_dec12], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=f0baa37d961b2dbdc2e621e3a7044080cf46c642.js.map
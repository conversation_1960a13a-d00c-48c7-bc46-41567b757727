System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, MyApp, csproto, DataEvent, EventMgr, MoneyType, PK, _crd;

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/bundles/common/script/app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "db://assets/bundles/common/script/autogen/pb/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfcomm(extras) {
    _reporterNs.report("comm", "db://assets/bundles/common/script/autogen/pb/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataEvent(extras) {
    _reporterNs.report("DataEvent", "../../event/DataEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "../../event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIData(extras) {
    _reporterNs.report("IData", "../DataManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMoneyType(extras) {
    _reporterNs.report("MoneyType", "../../autogen/luban/schema", _context.meta, extras);
  }

  _export("PK", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      MyApp = _unresolved_2.MyApp;
    }, function (_unresolved_3) {
      csproto = _unresolved_3.default;
    }, function (_unresolved_4) {
      DataEvent = _unresolved_4.DataEvent;
    }, function (_unresolved_5) {
      EventMgr = _unresolved_5.EventMgr;
    }, function (_unresolved_6) {
      MoneyType = _unresolved_6.MoneyType;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "b9b7eoRv5pBo42CtnRSIeer", "PK", undefined);

      _export("PK", PK = class PK {
        constructor() {
          this.selectFusionIdx = -1;
          this.self_info = void 0;
          this.other_info = void 0;
          this.get_list = void 0;
          this.award_info = void 0;
        }

        init() {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GAME_PVP_MATCH, this.onGamePvpMatch, this);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GAME_PVP_START, this.onGamePvpStart, this);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GAME_PVP_CANCEL, this.onGamePvpCancel, this);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GAME_PVP_END, this.onGamePvpEnd, this);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_TASK_GET_REWARD, this.onGamePvpGetAward, this);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GAME_PVP_GET_INFO, this.onGamePvpGetInfo, this);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GAME_PVP_GET_LIST, this.onGamePvpGetList, this);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_STORE_BUY, this.onStoreBuy, this);
        } //#region 收协议


        onGamePvpMatch(msg) {
          if (!msg.body || !msg.body.game_pvp_match) {
            return;
          }

          if (msg.ret_code != (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.RET_CODE.RET_CODE_NO_ERROR) {} else {
            (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
              error: Error()
            }), EventMgr) : EventMgr).emit((_crd && DataEvent === void 0 ? (_reportPossibleCrUseOfDataEvent({
              error: Error()
            }), DataEvent) : DataEvent).GamePvpMatchSuc);
          }
        }

        onGamePvpStart(msg) {
          var _msg$body$game_pvp_st, _msg$body$game_pvp_st2;

          if (!msg.body || !msg.body.game_pvp_start) {
            return;
          }

          this.self_info = (_msg$body$game_pvp_st = msg.body.game_pvp_start.info) == null ? void 0 : _msg$body$game_pvp_st.self_info;
          this.other_info = (_msg$body$game_pvp_st2 = msg.body.game_pvp_start.info) == null ? void 0 : _msg$body$game_pvp_st2.other_info;
        }

        onGamePvpCancel(msg) {
          if (!msg.body || !msg.body.game_pvp_cancel) {
            return;
          }

          let reason = msg.body.game_pvp_cancel.reason;

          switch (reason) {
            case (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
              error: Error()
            }), csproto) : csproto).cs.MATCH_CANCEL_REASON.MATCH_CANCEL_REASON_USER_CANCEL:
              break;

            case (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
              error: Error()
            }), csproto) : csproto).cs.MATCH_CANCEL_REASON.MATCH_CANCEL_REASON_MATCH_TIMEOUT:
              break;

            case (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
              error: Error()
            }), csproto) : csproto).cs.MATCH_CANCEL_REASON.MATCH_CANCEL_REASON_MATCH_RETRY:
              break;

            case (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
              error: Error()
            }), csproto) : csproto).cs.MATCH_CANCEL_REASON.MATCH_CANCEL_REASON_MATCH_STOP:
              break;
          }
        }

        onGamePvpEnd(msg) {
          if (!msg.body || !msg.body.game_pvp_end) {
            return;
          }

          let result = msg.body.game_pvp_end.pvp_result;

          if (result) {
            var _msg$body$game_pvp_en;

            let res = (_msg$body$game_pvp_en = msg.body.game_pvp_end.pvp_result) == null ? void 0 : _msg$body$game_pvp_en.result_code;

            switch (res) {
              case (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
                error: Error()
              }), csproto) : csproto).comm.GAME_PVP_RESULT.GAME_PVP_RESULT_WIN:
                break;

              case (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
                error: Error()
              }), csproto) : csproto).comm.GAME_PVP_RESULT.GAME_PVP_RESULT_LOSE:
                break;

              case (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
                error: Error()
              }), csproto) : csproto).comm.GAME_PVP_RESULT.GAME_PVP_RESULT_DRAW:
                break;

              case (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
                error: Error()
              }), csproto) : csproto).comm.GAME_PVP_RESULT.GAME_PVP_RESULT_CANCEL:
                break;
            }
          }
        }

        onGamePvpGetAward(msg) {
          if (!msg.body || !msg.body.game_pvp_get_reward) {
            return;
          }

          if (msg.ret_code != (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.RET_CODE.RET_CODE_NO_ERROR) {}

          this.award_info = msg.body.game_pvp_get_reward.reward_info;
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).emit((_crd && DataEvent === void 0 ? (_reportPossibleCrUseOfDataEvent({
            error: Error()
          }), DataEvent) : DataEvent).GamePvpGetAward);
        }

        onGamePvpGetInfo(msg) {
          if (!msg.body || !msg.body.game_pvp_get_info) {
            return;
          }

          const info = msg.body.game_pvp_get_info.info;
          const status = msg.body.game_pvp_get_info.status;

          if (status == (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.GAME_PVP_STATUS.GAME_PVP_STATUS_IN_MATCH) {}

          if (msg.ret_code != (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.RET_CODE.RET_CODE_NO_ERROR) {}
        } //登录成功后，拉取PVP列表


        onGamePvpGetList(msg) {
          if (!msg.body || !msg.body.game_pvp_get_list) {
            return;
          }

          if (msg.ret_code != (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.RET_CODE.RET_CODE_NO_ERROR) {
            return;
          }

          this.get_list = msg.body.game_pvp_get_list.list;
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).emit((_crd && DataEvent === void 0 ? (_reportPossibleCrUseOfDataEvent({
            error: Error()
          }), DataEvent) : DataEvent).GamePvpGetList);
        }

        onStoreBuy(msg) {
          if (!msg.body || !msg.body.store_buy) {
            return;
          }

          if (msg.ret_code != (_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).comm.RET_CODE.RET_CODE_NO_ERROR) {
            return;
          }

          const gain_items = msg.body.store_buy.gain_items;
        } //#endregion
        //#region 发协议


        cmdStoreBuy(goods_id, goods_num, money_type, money_id, money_num, buy_type) {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_STORE_BUY, {
            store_buy: {
              goods_id: goods_id,
              goods_num: goods_num,
              money_type: (_crd && MoneyType === void 0 ? (_reportPossibleCrUseOfMoneyType({
                error: Error()
              }), MoneyType) : MoneyType).ITEM,
              money_id: money_id,
              money_num: money_num,
              buy_type: buy_type // 购买的方式, 附加的方式. 比如看广告有折扣等情况. 

            }
          });
        } //#endregion


        update() {}

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=010dba96fb560cb553830cbedcce056c3740edb9.js.map
System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Graphics, Color, Component, Vec3, instantiate, assetManager, CCObject, Wave, LubanMgr, EnemyPlane, eMoveEvent, PathData, PathEditor, LevelEditorEventUI, _dec, _dec2, _dec3, _dec4, _class, _class2, _crd, ccclass, property, executeInEditMode, menu, requireComponent, WavePreview;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfWave(extras) {
    _reporterNs.report("Wave", "db://assets/bundles/common/script/game/wave/Wave", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLubanMgr(extras) {
    _reporterNs.report("LubanMgr", "db://assets/bundles/common/script/luban/LubanMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyPlane(extras) {
    _reporterNs.report("EnemyPlane", "db://assets/bundles/common/script/game/ui/plane/enemy/EnemyPlane", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeMoveEvent(extras) {
    _reporterNs.report("eMoveEvent", "db://assets/bundles/common/script/game/move/IMovable", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathData(extras) {
    _reporterNs.report("PathData", "db://assets/bundles/common/script/game/data/PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathEditor(extras) {
    _reporterNs.report("PathEditor", "db://assets/editor/level/wave/PathEditor", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelEditorEventUI(extras) {
    _reporterNs.report("LevelEditorEventUI", "../LevelEditorEventUI", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Graphics = _cc.Graphics;
      Color = _cc.Color;
      Component = _cc.Component;
      Vec3 = _cc.Vec3;
      instantiate = _cc.instantiate;
      assetManager = _cc.assetManager;
      CCObject = _cc.CCObject;
    }, function (_unresolved_2) {
      Wave = _unresolved_2.Wave;
    }, function (_unresolved_3) {
      LubanMgr = _unresolved_3.LubanMgr;
    }, function (_unresolved_4) {
      EnemyPlane = _unresolved_4.default;
    }, function (_unresolved_5) {
      eMoveEvent = _unresolved_5.eMoveEvent;
    }, function (_unresolved_6) {
      PathData = _unresolved_6.PathData;
    }, function (_unresolved_7) {
      PathEditor = _unresolved_7.PathEditor;
    }, function (_unresolved_8) {
      LevelEditorEventUI = _unresolved_8.LevelEditorEventUI;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "e6727UuHORMv49xH6m7Wz2U", "WavePreview", undefined);

      __checkObsolete__(['_decorator', 'Node', 'Prefab', 'Graphics', 'Color', 'Component', 'Vec2', 'Vec3', 'instantiate', 'assetManager', 'CCObject']);

      ({
        ccclass,
        property,
        executeInEditMode,
        menu,
        requireComponent
      } = _decorator);

      /// 用来创建和管理波次的所有飞机对象
      _export("WavePreview", WavePreview = (_dec = ccclass('WavePreview'), _dec2 = menu("怪物/编辑器/波次预览"), _dec3 = requireComponent(Graphics), _dec4 = executeInEditMode(), _dec(_class = _dec2(_class = _dec3(_class = _dec4(_class = (_class2 = class WavePreview extends Component {
        constructor() {
          super(...arguments);
          this._luban = null;
          this._cachedParentSelection = false;
          this._cachedWorldPos = new Vec3();
          this._wave = null;
          this._graphics = null;
          this._parentEvent = null;
          this._lineColor = Color.RED;
          // 这里的wave时编辑器play时，用来动态创建小怪的wave。
          this.isPreviewing = false;
        }

        get luban() {
          if (this._luban == null) {
            this._luban = new (_crd && LubanMgr === void 0 ? (_reportPossibleCrUseOfLubanMgr({
              error: Error()
            }), LubanMgr) : LubanMgr)();
          }

          return this._luban;
        }

        onLoad() {
          var _parent, _this$_parentEvent;

          this._wave = this.node.parent.getComponent(_crd && Wave === void 0 ? (_reportPossibleCrUseOfWave({
            error: Error()
          }), Wave) : Wave);
          this._graphics = this.getComponent(Graphics) || this.addComponent(Graphics);
          this._parentEvent = (_parent = this.node.parent.parent) == null ? void 0 : _parent.getComponent(_crd && LevelEditorEventUI === void 0 ? (_reportPossibleCrUseOfLevelEditorEventUI({
            error: Error()
          }), LevelEditorEventUI) : LevelEditorEventUI);
          this._cachedParentSelection = ((_this$_parentEvent = this._parentEvent) == null ? void 0 : _this$_parentEvent.isSelected()) || false;
        }

        reset() {
          this.node.removeAllChildren();
        }

        update(dt) {
          var _this$_parentEvent2;

          var worldPos = this.node.parent.worldPosition;
          var shouldDraw = false;

          if (!this._cachedWorldPos.equals(worldPos)) {
            this._cachedWorldPos.set(worldPos);

            shouldDraw = true;
          }

          var parentSelected = ((_this$_parentEvent2 = this._parentEvent) == null ? void 0 : _this$_parentEvent2.isSelected()) || false;

          if (this._cachedParentSelection != parentSelected) {
            this._cachedParentSelection = parentSelected;
            shouldDraw = true;
          }

          if (shouldDraw) {
            this.tickPreviewDraw();
          }

          this.tickPreview(dt);
        }

        setLineColor(color) {
          this._lineColor = color;
        }

        tickPreviewDraw() {
          var _this$_wave;

          var pathAsset = (_this$_wave = this._wave) == null ? void 0 : _this$_wave.waveData.pathAsset;

          if (pathAsset) {
            var _this$_parentEvent3;

            // draw path
            var path = (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
              error: Error()
            }), PathData) : PathData).fromJSON(pathAsset.json);
            var subdivided = path.getSubdividedPoints();
            var isParentSelected = (_this$_parentEvent3 = this._parentEvent) == null ? void 0 : _this$_parentEvent3.isSelected();
            var isWaveActive = this.isPreviewing && !this._wave.isSpawnCompleted && this._wave.waveElapsedTime > 0;
            var color = isWaveActive ? Color.GREEN : isParentSelected ? Color.YELLOW : this._lineColor;

            if (subdivided.length > 1) {
              (_crd && PathEditor === void 0 ? (_reportPossibleCrUseOfPathEditor({
                error: Error()
              }), PathEditor) : PathEditor).drawUniformPath(this._graphics, subdivided, color, path.closed, 10);
              var endPoint = subdivided[subdivided.length - 1];
              var prevPoint = subdivided[subdivided.length - 2];

              if (subdivided.length >= 5) {
                prevPoint = subdivided[subdivided.length - 5];
              }

              var direction = Math.atan2(endPoint.y - prevPoint.y, endPoint.x - prevPoint.x);
              (_crd && PathEditor === void 0 ? (_reportPossibleCrUseOfPathEditor({
                error: Error()
              }), PathEditor) : PathEditor).drawPathDirectionArrow(this._graphics, endPoint.position, direction, path.closed);
            } // draw path points


            this.drawPathPoints(path);
          }
        }

        drawPathPoints(path) {
          if (!path.points || path.points.length === 0) return; // 使用同一个 Graphics 绘制所有路径点

          for (var i = 0; i < path.points.length; i++) {
            var point = path.points[i];
            (_crd && PathEditor === void 0 ? (_reportPossibleCrUseOfPathEditor({
              error: Error()
            }), PathEditor) : PathEditor).drawPathPointAtPosition(this._graphics, point, point.x, point.y, false, // 不选中状态
            15, // 点大小稍小一些
            i, path.points.length, path.startIdx, path.endIdx, false, false);
          }
        }

        static tickActivePlane(dt) {
          WavePreview.activePlane.forEach(plane => {
            plane.moveCom.tick(dt);
          });
        }

        triggerPreview(posX, posY) {
          var _this$_wave2,
              _this = this;

          console.log('WavePreview - triggerPreview: ', posX, posY, (_this$_wave2 = this._wave) == null ? void 0 : _this$_wave2.waveName);

          if (!this._wave) {
            return;
          }

          this._wave.setCreatePlaneDelegate( /*#__PURE__*/_asyncToGenerator(function* (planeId, pos, angle) {
            _this.createDummyPlane(_this._wave, planeId, pos, angle);
          }));

          this._wave.trigger(posX, posY);

          this.isPreviewing = true;
        }

        tickPreview(dt) {
          if (!this.isPreviewing) {
            return;
          }

          var dtInMiliseconds = dt * 1000;

          if (this._wave) {
            this._wave.tick(dtInMiliseconds);
          }
        }

        clearPreview() {
          // console.log('WavePreview - clearPreview: ', this.activePlane.length);
          this.isPreviewing = false; // just in case

          this.node.removeAllChildren();
        }

        static clearPool() {
          WavePreview.activePlane.forEach(plane => {
            plane.node.destroy();
          });
          WavePreview.planePool.forEach(plane => {
            plane.node.destroy();
          });
          WavePreview.planePool = [];
        }

        createDummyPlane(wave, planeId, pos, angle) {
          var _this2 = this;

          // 对应"assets/editor/level/prefab/dummy_plane";
          // console.log('WavePreview - createDummyPlane: ', planeId, pos, angle);
          var plane = null;

          if (WavePreview.planePool.length > 0) {
            // 从对象池里拿一个dummy plane
            plane = WavePreview.planePool.pop();
            plane.node.active = true;
            plane.initMove(pos.x, pos.y, angle);

            if (wave.path) {
              plane.initPath(pos.x, pos.y, wave.path);
            } // push when not in activePlane


            if (!WavePreview.activePlane.includes(plane)) {
              WavePreview.activePlane.push(plane);
            }
          } else {
            var dummy_plane_uuid = "698c56c6-6603-4e69-abaf-421b721ef307";
            assetManager.loadAny({
              uuid: dummy_plane_uuid
            }, /*#__PURE__*/_asyncToGenerator(function* (err, prefab) {
              if (err) {
                console.error("WavePreview createDummyPlane load prefab err", err);
                return;
              }

              try {
                // if (this.luban?.table == null) {
                //     await this.luban?.initInEditor();
                // }
                var canvas = _this2.node.scene.getChildByName("Canvas");

                if (!canvas) {
                  console.error("WavePreview createDummyPlane no canvas");
                  return;
                }

                var planeNode = instantiate(prefab);
                planeNode.hideFlags = CCObject.Flags.AllHideMasks;

                var _plane = planeNode.getComponent(_crd && EnemyPlane === void 0 ? (_reportPossibleCrUseOfEnemyPlane({
                  error: Error()
                }), EnemyPlane) : EnemyPlane);

                if (_plane) {
                  canvas.addChild(planeNode); // this.node.parent!.addChild(planeNode);
                  // const enemyData = new EnemyData(planeId, this.luban?.table.TbResEnemy.get(planeId));
                  // const prefab = await LevelEditorUtils.loadByPath<Prefab>(enemyData.recoursePrefab);
                  // plane.initPlane(enemyData, prefab!);

                  _plane.initMove(pos.x, pos.y, angle);

                  _plane.moveCom.removeAllListeners();

                  _plane.moveCom.on((_crd && eMoveEvent === void 0 ? (_reportPossibleCrUseOfeMoveEvent({
                    error: Error()
                  }), eMoveEvent) : eMoveEvent).onBecomeInvisible, () => {
                    _plane.node.active = false;

                    _plane.moveCom.reset();

                    WavePreview.activePlane = WavePreview.activePlane.filter(p => p !== _plane);
                    WavePreview.planePool.push(_plane);
                  });

                  if (wave.path) {
                    _plane.initPath(pos.x, pos.y, wave.path);
                  }

                  if (!WavePreview.activePlane.includes(_plane)) {
                    WavePreview.activePlane.push(_plane);
                  }
                } else {
                  planeNode.destroy();
                }
              } catch (error) {
                console.error("WavePreview createDummyPlane err", error);
              }
            }));
          }
        }

      }, _class2.activePlane = [], _class2.planePool = [], _class2)) || _class) || _class) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=41cd73be68eabcc1cac24b8cc6301d64aca50e3d.js.map
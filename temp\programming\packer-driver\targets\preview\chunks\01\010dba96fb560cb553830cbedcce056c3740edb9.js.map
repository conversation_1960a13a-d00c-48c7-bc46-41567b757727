{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/data/pk/PK.ts"], "names": ["PK", "MyApp", "csproto", "DataEvent", "EventMgr", "MoneyType", "selectFusionIdx", "self_info", "other_info", "get_list", "award_info", "init", "netMgr", "registerHandler", "cs", "CS_CMD", "CS_CMD_GAME_PVP_MATCH", "onGamePvpMatch", "CS_CMD_GAME_PVP_START", "onGamePvpStart", "CS_CMD_GAME_PVP_CANCEL", "onGamePvpCancel", "CS_CMD_GAME_PVP_END", "onGamePvpEnd", "CS_CMD_TASK_GET_REWARD", "onGamePvpGetAward", "CS_CMD_GAME_PVP_GET_INFO", "onGamePvpGetInfo", "CS_CMD_GAME_PVP_GET_LIST", "onGamePvpGetList", "CS_CMD_STORE_BUY", "onStoreBuy", "msg", "body", "game_pvp_match", "ret_code", "comm", "RET_CODE", "RET_CODE_NO_ERROR", "emit", "GamePvpMatchSuc", "game_pvp_start", "info", "game_pvp_cancel", "reason", "MATCH_CANCEL_REASON", "MATCH_CANCEL_REASON_USER_CANCEL", "MATCH_CANCEL_REASON_MATCH_TIMEOUT", "MATCH_CANCEL_REASON_MATCH_RETRY", "MATCH_CANCEL_REASON_MATCH_STOP", "game_pvp_end", "result", "pvp_result", "res", "result_code", "GAME_PVP_RESULT", "GAME_PVP_RESULT_WIN", "GAME_PVP_RESULT_LOSE", "GAME_PVP_RESULT_DRAW", "GAME_PVP_RESULT_CANCEL", "game_pvp_get_reward", "reward_info", "GamePvpGetAward", "game_pvp_get_info", "status", "GAME_PVP_STATUS", "GAME_PVP_STATUS_IN_MATCH", "game_pvp_get_list", "list", "GamePvpGetList", "store_buy", "gain_items", "cmdStoreBuy", "goods_id", "goods_num", "money_type", "money_id", "money_num", "buy_type", "sendMessage", "ITEM", "update"], "mappings": ";;;8EAMaA,E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AANJC,MAAAA,K,iBAAAA,K;;AACFC,MAAAA,O;;AACEC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,Q,iBAAAA,Q;;AAEAC,MAAAA,S,iBAAAA,S;;;;;;;oBACIL,E,GAAN,MAAMA,EAAN,CAA0B;AAAA;AAAA,eAE7BM,eAF6B,GAEH,CAAC,CAFE;AAAA,eAI7BC,SAJ6B;AAAA,eAK7BC,UAL6B;AAAA,eAM7BC,QAN6B;AAAA,eAO7BC,UAP6B;AAAA;;AAQtBC,QAAAA,IAAI,GAAS;AAChB;AAAA;AAAA,8BAAMC,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBC,qBAA/C,EAAsE,KAAKC,cAA3E,EAA2F,IAA3F;AACA;AAAA;AAAA,8BAAML,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBG,qBAA/C,EAAsE,KAAKC,cAA3E,EAA2F,IAA3F;AACA;AAAA;AAAA,8BAAMP,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBK,sBAA/C,EAAuE,KAAKC,eAA5E,EAA6F,IAA7F;AACA;AAAA;AAAA,8BAAMT,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBO,mBAA/C,EAAoE,KAAKC,YAAzE,EAAuF,IAAvF;AACA;AAAA;AAAA,8BAAMX,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBS,sBAA/C,EAAuE,KAAKC,iBAA5E,EAA+F,IAA/F;AACA;AAAA;AAAA,8BAAMb,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBW,wBAA/C,EAAyE,KAAKC,gBAA9E,EAAgG,IAAhG;AACA;AAAA;AAAA,8BAAMf,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBa,wBAA/C,EAAyE,KAAKC,gBAA9E,EAAgG,IAAhG;AAEA;AAAA;AAAA,8BAAMjB,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBe,gBAA/C,EAAiE,KAAKC,UAAtE,EAAkF,IAAlF;AACH,SAlB4B,CAmB7B;;;AACAd,QAAAA,cAAc,CAACe,GAAD,EAAgC;AAC1C,cAAI,CAACA,GAAG,CAACC,IAAL,IAAa,CAACD,GAAG,CAACC,IAAJ,CAASC,cAA3B,EAA2C;AACvC;AACH;;AACD,cAAIF,GAAG,CAACG,QAAJ,IAAgB;AAAA;AAAA,kCAAQC,IAAR,CAAaC,QAAb,CAAsBC,iBAA1C,EAA6D,CAC5D,CADD,MACO;AACH;AAAA;AAAA,sCAASC,IAAT,CAAc;AAAA;AAAA,wCAAUC,eAAxB;AACH;AACJ;;AAEDrB,QAAAA,cAAc,CAACa,GAAD,EAAgC;AAAA;;AAC1C,cAAI,CAACA,GAAG,CAACC,IAAL,IAAa,CAACD,GAAG,CAACC,IAAJ,CAASQ,cAA3B,EAA2C;AACvC;AACH;;AACD,eAAKlC,SAAL,4BAAiByB,GAAG,CAACC,IAAJ,CAASQ,cAAT,CAAwBC,IAAzC,qBAAiB,sBAA8BnC,SAA/C;AACA,eAAKC,UAAL,6BAAkBwB,GAAG,CAACC,IAAJ,CAASQ,cAAT,CAAwBC,IAA1C,qBAAkB,uBAA8BlC,UAAhD;AACH;;AAEDa,QAAAA,eAAe,CAACW,GAAD,EAAgC;AAC3C,cAAI,CAACA,GAAG,CAACC,IAAL,IAAa,CAACD,GAAG,CAACC,IAAJ,CAASU,eAA3B,EAA4C;AACxC;AACH;;AACD,cAAIC,MAAM,GAAGZ,GAAG,CAACC,IAAJ,CAASU,eAAT,CAAyBC,MAAtC;;AACA,kBAAQA,MAAR;AACI,iBAAK;AAAA;AAAA,oCAAQ9B,EAAR,CAAW+B,mBAAX,CAA+BC,+BAApC;AACI;;AACJ,iBAAK;AAAA;AAAA,oCAAQhC,EAAR,CAAW+B,mBAAX,CAA+BE,iCAApC;AACI;;AACJ,iBAAK;AAAA;AAAA,oCAAQjC,EAAR,CAAW+B,mBAAX,CAA+BG,+BAApC;AACI;;AACJ,iBAAK;AAAA;AAAA,oCAAQlC,EAAR,CAAW+B,mBAAX,CAA+BI,8BAApC;AACI;AARR;AAUH;;AAED1B,QAAAA,YAAY,CAACS,GAAD,EAAgC;AACxC,cAAI,CAACA,GAAG,CAACC,IAAL,IAAa,CAACD,GAAG,CAACC,IAAJ,CAASiB,YAA3B,EAAyC;AACrC;AACH;;AACD,cAAIC,MAAM,GAAGnB,GAAG,CAACC,IAAJ,CAASiB,YAAT,CAAsBE,UAAnC;;AACA,cAAID,MAAJ,EAAY;AAAA;;AACR,gBAAIE,GAAG,4BAAGrB,GAAG,CAACC,IAAJ,CAASiB,YAAT,CAAsBE,UAAzB,qBAAG,sBAAkCE,WAA5C;;AACA,oBAAQD,GAAR;AACI,mBAAK;AAAA;AAAA,sCAAQjB,IAAR,CAAamB,eAAb,CAA6BC,mBAAlC;AACI;;AACJ,mBAAK;AAAA;AAAA,sCAAQpB,IAAR,CAAamB,eAAb,CAA6BE,oBAAlC;AACI;;AACJ,mBAAK;AAAA;AAAA,sCAAQrB,IAAR,CAAamB,eAAb,CAA6BG,oBAAlC;AACI;;AACJ,mBAAK;AAAA;AAAA,sCAAQtB,IAAR,CAAamB,eAAb,CAA6BI,sBAAlC;AACI;AARR;AAUH;AACJ;;AAEDlC,QAAAA,iBAAiB,CAACO,GAAD,EAAgC;AAC7C,cAAI,CAACA,GAAG,CAACC,IAAL,IAAa,CAACD,GAAG,CAACC,IAAJ,CAAS2B,mBAA3B,EAAgD;AAC5C;AACH;;AACD,cAAI5B,GAAG,CAACG,QAAJ,IAAgB;AAAA;AAAA,kCAAQC,IAAR,CAAaC,QAAb,CAAsBC,iBAA1C,EAA6D,CAC5D;;AACD,eAAK5B,UAAL,GAAkBsB,GAAG,CAACC,IAAJ,CAAS2B,mBAAT,CAA6BC,WAA/C;AACA;AAAA;AAAA,oCAAStB,IAAT,CAAc;AAAA;AAAA,sCAAUuB,eAAxB;AACH;;AAEDnC,QAAAA,gBAAgB,CAACK,GAAD,EAAgC;AAC5C,cAAI,CAACA,GAAG,CAACC,IAAL,IAAa,CAACD,GAAG,CAACC,IAAJ,CAAS8B,iBAA3B,EAA8C;AAC1C;AACH;;AACD,cAAMrB,IAAI,GAAGV,GAAG,CAACC,IAAJ,CAAS8B,iBAAT,CAA2BrB,IAAxC;AACA,cAAMsB,MAAM,GAAGhC,GAAG,CAACC,IAAJ,CAAS8B,iBAAT,CAA2BC,MAA1C;;AACA,cAAIA,MAAM,IAAI;AAAA;AAAA,kCAAQ5B,IAAR,CAAa6B,eAAb,CAA6BC,wBAA3C,EAAqE,CAEpE;;AACD,cAAIlC,GAAG,CAACG,QAAJ,IAAgB;AAAA;AAAA,kCAAQC,IAAR,CAAaC,QAAb,CAAsBC,iBAA1C,EAA6D,CAC5D;AACJ,SAhG4B,CAkG7B;;;AACAT,QAAAA,gBAAgB,CAACG,GAAD,EAAgC;AAC5C,cAAI,CAACA,GAAG,CAACC,IAAL,IAAa,CAACD,GAAG,CAACC,IAAJ,CAASkC,iBAA3B,EAA8C;AAC1C;AACH;;AAED,cAAInC,GAAG,CAACG,QAAJ,IAAgB;AAAA;AAAA,kCAAQC,IAAR,CAAaC,QAAb,CAAsBC,iBAA1C,EAA6D;AACzD;AACH;;AACD,eAAK7B,QAAL,GAAgBuB,GAAG,CAACC,IAAJ,CAASkC,iBAAT,CAA2BC,IAA3C;AACA;AAAA;AAAA,oCAAS7B,IAAT,CAAc;AAAA;AAAA,sCAAU8B,cAAxB;AACH;;AACDtC,QAAAA,UAAU,CAACC,GAAD,EAAgC;AACtC,cAAI,CAACA,GAAG,CAACC,IAAL,IAAa,CAACD,GAAG,CAACC,IAAJ,CAASqC,SAA3B,EAAsC;AAClC;AACH;;AACD,cAAItC,GAAG,CAACG,QAAJ,IAAgB;AAAA;AAAA,kCAAQC,IAAR,CAAaC,QAAb,CAAsBC,iBAA1C,EAA6D;AACzD;AACH;;AACD,cAAMiC,UAAU,GAAGvC,GAAG,CAACC,IAAJ,CAASqC,SAAT,CAAmBC,UAAtC;AACH,SAtH4B,CAuH7B;AAEA;;;AACAC,QAAAA,WAAW,CAACC,QAAD,EAAmBC,SAAnB,EAAsCC,UAAtC,EAA0DC,QAA1D,EAA4EC,SAA5E,EAA+FC,QAA/F,EAAiH;AACxH;AAAA;AAAA,8BAAMlE,MAAN,CAAamE,WAAb,CAAyB;AAAA;AAAA,kCAAQjE,EAAR,CAAWC,MAAX,CAAkBe,gBAA3C,EAA6D;AACzDwC,YAAAA,SAAS,EAAE;AACPG,cAAAA,QAAQ,EAAEA,QADH;AAEPC,cAAAA,SAAS,EAAEA,SAFJ;AAGPC,cAAAA,UAAU,EAAE;AAAA;AAAA,0CAAUK,IAHf;AAIPJ,cAAAA,QAAQ,EAAEA,QAJH;AAKPC,cAAAA,SAAS,EAAEA,SALJ;AAMPC,cAAAA,QAAQ,EAAEA,QANH,CAMW;;AANX;AAD8C,WAA7D;AAUH,SArI4B,CAuI7B;;;AACOG,QAAAA,MAAM,GAAS,CACrB;;AAzI4B,O", "sourcesContent": ["import { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\nimport csproto, { comm } from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';\nimport { DataEvent } from '../../event/DataEvent';\nimport { EventMgr } from '../../event/EventManager';\nimport { IData } from \"../DataManager\";\nimport { MoneyType } from '../../autogen/luban/schema';\nexport class PK implements IData {\n\n    selectFusionIdx: number = -1;\n\n    self_info?: (csproto.comm.IGamePvpInfoSide | null);\n    other_info?: (csproto.comm.IGamePvpInfoSide | null);\n    get_list?: (csproto.comm.IGamePvpHistory[] | null);\n    award_info?: (csproto.comm.IGameRewardInfo | null);\n    public init(): void {\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GAME_PVP_MATCH, this.onGamePvpMatch, this);\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GAME_PVP_START, this.onGamePvpStart, this);\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GAME_PVP_CANCEL, this.onGamePvpCancel, this);\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GAME_PVP_END, this.onGamePvpEnd, this);\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_TASK_GET_REWARD, this.onGamePvpGetAward, this);\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GAME_PVP_GET_INFO, this.onGamePvpGetInfo, this);\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GAME_PVP_GET_LIST, this.onGamePvpGetList, this);\n\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_STORE_BUY, this.onStoreBuy, this);\n    }\n    //#region 收协议\n    onGamePvpMatch(msg: csproto.cs.IS2CMsg): void {\n        if (!msg.body || !msg.body.game_pvp_match) {\n            return;\n        }\n        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {\n        } else {\n            EventMgr.emit(DataEvent.GamePvpMatchSuc);\n        }\n    }\n\n    onGamePvpStart(msg: csproto.cs.IS2CMsg): void {\n        if (!msg.body || !msg.body.game_pvp_start) {\n            return;\n        }\n        this.self_info = msg.body.game_pvp_start.info?.self_info;\n        this.other_info = msg.body.game_pvp_start.info?.other_info;\n    }\n\n    onGamePvpCancel(msg: csproto.cs.IS2CMsg): void {\n        if (!msg.body || !msg.body.game_pvp_cancel) {\n            return;\n        }\n        let reason = msg.body.game_pvp_cancel.reason;\n        switch (reason) {\n            case csproto.cs.MATCH_CANCEL_REASON.MATCH_CANCEL_REASON_USER_CANCEL:\n                break;\n            case csproto.cs.MATCH_CANCEL_REASON.MATCH_CANCEL_REASON_MATCH_TIMEOUT:\n                break;\n            case csproto.cs.MATCH_CANCEL_REASON.MATCH_CANCEL_REASON_MATCH_RETRY:\n                break;\n            case csproto.cs.MATCH_CANCEL_REASON.MATCH_CANCEL_REASON_MATCH_STOP:\n                break;\n        }\n    }\n\n    onGamePvpEnd(msg: csproto.cs.IS2CMsg): void {\n        if (!msg.body || !msg.body.game_pvp_end) {\n            return;\n        }\n        let result = msg.body.game_pvp_end.pvp_result;\n        if (result) {\n            let res = msg.body.game_pvp_end.pvp_result?.result_code;\n            switch (res) {\n                case csproto.comm.GAME_PVP_RESULT.GAME_PVP_RESULT_WIN:\n                    break;\n                case csproto.comm.GAME_PVP_RESULT.GAME_PVP_RESULT_LOSE:\n                    break;\n                case csproto.comm.GAME_PVP_RESULT.GAME_PVP_RESULT_DRAW:\n                    break;\n                case csproto.comm.GAME_PVP_RESULT.GAME_PVP_RESULT_CANCEL:\n                    break;\n            }\n        }\n    }\n\n    onGamePvpGetAward(msg: csproto.cs.IS2CMsg): void {\n        if (!msg.body || !msg.body.game_pvp_get_reward) {\n            return;\n        }\n        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {\n        }\n        this.award_info = msg.body.game_pvp_get_reward.reward_info;\n        EventMgr.emit(DataEvent.GamePvpGetAward)\n    }\n\n    onGamePvpGetInfo(msg: csproto.cs.IS2CMsg): void {\n        if (!msg.body || !msg.body.game_pvp_get_info) {\n            return;\n        }\n        const info = msg.body.game_pvp_get_info.info!\n        const status = msg.body.game_pvp_get_info.status!\n        if (status == csproto.comm.GAME_PVP_STATUS.GAME_PVP_STATUS_IN_MATCH) {\n\n        }\n        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {\n        }\n    }\n\n    //登录成功后，拉取PVP列表\n    onGamePvpGetList(msg: csproto.cs.IS2CMsg): void {\n        if (!msg.body || !msg.body.game_pvp_get_list) {\n            return;\n        }\n\n        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {\n            return;\n        }\n        this.get_list = msg.body.game_pvp_get_list.list!\n        EventMgr.emit(DataEvent.GamePvpGetList)\n    }\n    onStoreBuy(msg: csproto.cs.IS2CMsg): void {\n        if (!msg.body || !msg.body.store_buy) {\n            return;\n        }\n        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {\n            return;\n        }\n        const gain_items = msg.body.store_buy.gain_items!\n    }\n    //#endregion\n\n    //#region 发协议\n    cmdStoreBuy(goods_id: number, goods_num: number, money_type: number, money_id: number, money_num: number, buy_type: number) {\n        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_STORE_BUY, {\n            store_buy: {\n                goods_id: goods_id,\n                goods_num: goods_num,\n                money_type: MoneyType.ITEM,\n                money_id: money_id,\n                money_num: money_num,\n                buy_type: buy_type// 购买的方式, 附加的方式. 比如看广告有折扣等情况. \n            }\n        })\n    }\n\n    //#endregion\n    public update(): void {\n    }\n}\n"]}
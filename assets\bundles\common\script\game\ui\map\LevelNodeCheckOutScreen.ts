import { _decorator, Component, Node, UITransform, Vec3 } from 'cc';
import { GameConst } from 'db://assets/scripts/core/base/GameConst';
import { logDebug, logError } from 'db://assets/scripts/utils/Logger';
import { GameIns } from 'db://assets/bundles/common/script/game/GameIns';
const { ccclass, property } = _decorator;

@ccclass('LevelNodeCheckOutScreen')
export class LevelNodeCheckOutScreen extends Component {
    private _remove_thresHoldTop: number = 0;
    private _remove_thresHoldBottom: number = 0;
    private _height: number = 0;
    private _worldPos: Vec3 = new Vec3();
    private _hasEnteredScreen: boolean = false;
    private _frameCounter: number = 0;
    private _isRecycling: boolean = false;

    private _poolName: string = '';

    public init(poolName: string): void {
        this._poolName = poolName;
        this._hasEnteredScreen = false;
        this._isRecycling = false;

        const uiTransform = this.node.getComponent(UITransform);
        if (uiTransform) {
            this._height = uiTransform.height;
        } else {
            logError('LevelNodeCheckOutScreen',`节点${this.node.name}缺少UITransform组件`);
        }

        this._remove_thresHoldBottom = GameConst.BATTLE_VIEW_BOTTOM;
        this._remove_thresHoldTop = GameConst.BATTLE_VIEW_TOP;
    }

    update(deltaTime: number) {
        if (this.node.isValid === false || this.node.active === false) return;
        if (this._height === 0) return;

        this._frameCounter++;
        
        // 每30帧检测一次
        if (this._frameCounter >= 30) {
            this._frameCounter = 0;
            this._checkScreenBoundary();
        }
        /*this.node.getWorldPosition(this._worldPos);
        
        const topPosition = this._worldPos.y + this._height / 2;

        if (topPosition < this._remove_thresHold) {
            this._recycleNode();
        }*/
    }

    private _checkScreenBoundary(): void {
        if (!this.node.isValid || this.node.active === false) return;
        if (this._height <= 0) return;
        if (this._isRecycling) return;
        
        this.node.getWorldPosition(this._worldPos);
    
        const topPosition = this._worldPos.y + this._height / 2;
        const bottomPosition = this._worldPos.y - this._height / 2;
        
        // 检查是否进入过屏幕
        if (!this._hasEnteredScreen) {
            // 如果节点任何部分在屏幕内，则标记为已进入
            if (bottomPosition < this._remove_thresHoldTop && topPosition > this._remove_thresHoldBottom) {
                this._hasEnteredScreen = true;
            }
            return;
        }
        
        // 检查是否移出屏幕（顶部或底部）
        const isOutOfScreenTop = topPosition < this._remove_thresHoldBottom;
        const isOutOfScreenBottom = bottomPosition > this._remove_thresHoldTop;
        
        if (isOutOfScreenTop || isOutOfScreenBottom) {
            this._recycleNode();
        }
    }

    private _recycleNode(): void {
        if (this._isRecycling) return;
        this._isRecycling = true;

        if (this._poolName.length > 0) {
            logDebug('LevelNodeCheckOutScreen',` 节点${this.node.name}已回收到对象池${this._poolName}`);
            GameIns.gameMapManager.mapObjectPoolManager.put(this._poolName, this.node);
        } else {
            // 没有指定对象池时直接销毁
            logDebug('LevelNodeCheckOutScreen', `节点${this.node.name}已销毁`);
            this.node.destroy();
        }
    }
}



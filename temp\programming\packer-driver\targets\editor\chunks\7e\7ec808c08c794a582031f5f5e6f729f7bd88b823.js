System.register(["__unresolved_0", "cc"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, PlaneCacheInfoElem, PlaneCacheInfo, _crd;

  function _reportPossibleCrUseOfIData(extras) {
    _reporterNs.report("IData", "db://assets/bundles/common/script/data/DataManager", _context.meta, extras);
  }

  _export({
    PlaneCacheInfoElem: void 0,
    PlaneCacheInfo: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "09c979s2npD7Jr9aMgf/Uw+", "PlaneCacheInfo", undefined);

      _export("PlaneCacheInfoElem", PlaneCacheInfoElem = class PlaneCacheInfoElem {
        constructor() {
          this.planeId = 0;
        }

      });

      _export("PlaneCacheInfo", PlaneCacheInfo = class PlaneCacheInfo {
        constructor() {
          this._isInit = false;
          this.curPlaneId = 0;
          this._planeDataDic = {};
        }

        //飞机数据
        init() {
          this._isInit = true;
          this.onNetAllPlaneInfo();
        }

        update() {}

        onNetAllPlaneInfo() {
          this.curPlaneId = 10100001; //10003101;

          this._planeDataDic = {};
          this._planeDataDic[this.curPlaneId] = {
            planeId: this.curPlaneId
          };
        }

        getPlaneInfoById(id) {
          if (!this._isInit) {
            this.init();
          }

          return this._planeDataDic[id];
        }

        getCurPlaneInfo() {
          if (!this._isInit) {
            this.init();
          }

          return this._planeDataDic[this.curPlaneId];
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=7ec808c08c794a582031f5f5e6f729f7bd88b823.js.map
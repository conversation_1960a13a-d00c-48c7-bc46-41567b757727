System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _crd, BundleName;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "d8e92Fh3UlDwaxRqIMPKI1T", "BundleConst", undefined);

      _export("BundleName", BundleName = {
        Common: "common",
        Gm: "gm",
        Home: "home",
        Luban: "luban",
        HomePlane: "home_plane",
        HomeTalent: "home_talent",
        HomeShop: "home_shop",
        HomeSkyIsland: "home_skyisland",
        HomeFriend: "home_friend",
        HomeMail: "home_mail",
        HomePK: "home_pk",
        homeMatch: "home_match",
        HomeStory: "home_story",
        HomeTask: "home_task",
        GameFight: "game_fight"
      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=1e9adc2bb6f09ceb0f4968dc16b4388edc409826.js.map
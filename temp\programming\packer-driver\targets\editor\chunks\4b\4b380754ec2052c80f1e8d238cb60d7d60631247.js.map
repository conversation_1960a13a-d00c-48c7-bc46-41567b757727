{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/PlaneBaseDebug.ts"], "names": ["_decorator", "CCBoolean", "CCFloat", "CCInteger", "Component", "PlaneBase", "AttributeConst", "ccclass", "property", "disallowMultiple", "PlaneBaseDebug", "type", "_planeBase", "start", "node", "getComponent", "maxHP", "maxHp", "hpRecovery", "attribute", "getHPRecovery", "curHP", "curHp", "isDead", "attack", "getAttack", "bulletAttack", "getFinialAttributeByOutInKey", "BulletAttackOutAdd", "BulletAttackOutPer", "BulletAttackInAdd", "BulletAttackInPer", "bufferList", "buff<PERSON><PERSON>p", "list", "buffs", "for<PERSON>ach", "buff", "push", "res", "id", "applyBuff", "value", "A<PERSON><PERSON><PERSON><PERSON>", "applyBuffID"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;;AAC7CC,MAAAA,S;;AACEC,MAAAA,c,iBAAAA,c;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA0CT,U;;yBAI3BU,c,WAFpBH,OAAO,CAAC,gBAAD,C,UACPE,gBAAgB,CAAC,IAAD,C,UAMZD,QAAQ,CAACN,OAAD,C,UAIRM,QAAQ,CAACN,OAAD,C,UAIRM,QAAQ,CAACN,OAAD,C,UAIRM,QAAQ,CAACP,SAAD,C,UAIRO,QAAQ,CAACN,OAAD,C,UAIRM,QAAQ,CAACL,SAAD,C,UAQRK,QAAQ,CAAC;AACNG,QAAAA,IAAI,EAAE,CAACR,SAAD;AADA,OAAD,C,WAcRK,QAAQ,CAACL,SAAD,C,WAERK,QAAQ,CAACP,SAAD,C,0CAnDb,MAEqBS,cAFrB,SAE4CN,SAF5C,CAEqD;AAAA;AAAA;AAAA,eACvCQ,UADuC,GACR,IADQ;;AAAA;AAAA;;AAEjDC,QAAAA,KAAK,GAAG;AACJ,eAAKD,UAAL,GAAkB,KAAKE,IAAL,CAAUC,YAAV;AAAA;AAAA,qCAAlB;AACH;;AAEQ,YAALC,KAAK,GAAS;AAAA;;AACd,iBAAO,0BAAKJ,UAAL,sCAAiBK,KAAjB,KAA0B,CAAjC;AACH;;AAEa,YAAVC,UAAU,GAAU;AAAA;;AACpB,iBAAO,2BAAKN,UAAL,uCAAiBO,SAAjB,CAA2BC,aAA3B,OAA4C,CAAnD;AACH;;AAEQ,YAALC,KAAK,GAAU;AAAA;;AACf,iBAAO,2BAAKT,UAAL,uCAAiBU,KAAjB,KAA0B,CAAjC;AACH;;AAES,YAANC,MAAM,GAAW;AAAA;;AACjB,iBAAO,2BAAKX,UAAL,uCAAiBW,MAAjB,KAA2B,KAAlC;AACH;;AAES,YAANC,MAAM,GAAG;AAAA;;AACT,sCAAO,KAAKZ,UAAZ,qBAAO,kBAAiBa,SAAjB,EAAP;AACH;;AAEe,YAAZC,YAAY,GAAG;AAAA;;AACf,sCAAO,KAAKd,UAAZ,qBAAO,kBAAiBO,SAAjB,CAA2BQ,4BAA3B,CAAwD;AAAA;AAAA,gDAAeC,kBAAvE,EACH;AAAA;AAAA,gDAAeC,kBADZ,EAEH;AAAA;AAAA,gDAAeC,iBAFZ,EAGH;AAAA;AAAA,gDAAeC,iBAHZ,CAAP;AAIH;;AAKa,YAAVC,UAAU,GAAY;AACtB,cAAI,CAAC,KAAKpB,UAAN,IAAoB,CAAC,KAAKA,UAAL,CAAgBqB,QAAzC,EAAmD;AAC/C,mBAAO,EAAP;AACH;;AACD,cAAIC,IAAa,GAAG,EAApB;;AACA,eAAKtB,UAAL,CAAgBqB,QAAhB,CAAyBE,KAAzB,CAA+BC,OAA/B,CAAwCC,IAAD,IAAU;AAC7CH,YAAAA,IAAI,CAACI,IAAL,CAAUD,IAAI,CAACE,GAAL,CAASC,EAAnB;AACH,WAFD;;AAGA,iBAAON,IAAP;AACH;;AAKY,YAATO,SAAS,GAAG;AACZ,iBAAO,KAAP;AACH;;AACY,YAATA,SAAS,CAACC,KAAD,EAAgB;AAAA;;AACzB,cAAI,CAACA,KAAL,EAAY;AACR;AACH;;AACD,oCAAK9B,UAAL,+BAAiBqB,QAAjB,CAA0BU,SAA1B,CAAoC,KAApC,EAA2C,KAAKC,WAAhD;AACH;;AA1DgD,O;;;;;iBAgD5B,C", "sourcesContent": ["import { _decorator, CCBoolean, CCFloat, CCInteger, Component } from \"cc\";\r\nimport PlaneBase from \"./PlaneBase\";\r\nimport { AttributeConst } from \"../../../const/AttributeConst\";\r\nconst { ccclass, property, disallowMultiple } = _decorator;\r\n\r\n@ccclass(\"PlaneBaseDebug\")\r\n@disallowMultiple(true)\r\nexport default class PlaneBaseDebug extends Component{\r\n    protected _planeBase: PlaneBase | null = null;\r\n    start() {\r\n        this._planeBase = this.node.getComponent(PlaneBase);\r\n    }\r\n    @property(CCFloat)\r\n    get maxHP():number{\r\n        return this._planeBase?.maxHp || 0;\r\n    }\r\n    @property(CCFloat)\r\n    get hpRecovery():number {\r\n        return this._planeBase?.attribute.getHPRecovery()||0;\r\n    }\r\n    @property(CCFloat)\r\n    get curHP():number {\r\n        return this._planeBase?.curHp || 0;\r\n    }\r\n    @property(CCBoolean)\r\n    get isDead():boolean {\r\n        return this._planeBase?.isDead || false;\r\n    }\r\n    @property(CCFloat)\r\n    get attack() {\r\n        return this._planeBase?.getAttack();\r\n    }\r\n    @property(CCInteger)\r\n    get bulletAttack() {\r\n        return this._planeBase?.attribute.getFinialAttributeByOutInKey(AttributeConst.BulletAttackOutAdd, \r\n            AttributeConst.BulletAttackOutPer, \r\n            AttributeConst.BulletAttackInAdd, \r\n            AttributeConst.BulletAttackInPer);\r\n    }\r\n\r\n    @property({\r\n        type: [CCInteger]\r\n    })\r\n    get bufferList():number[] {\r\n        if (!this._planeBase || !this._planeBase.buffComp) {\r\n            return [];\r\n        }\r\n        let list:number[] = [];\r\n        this._planeBase.buffComp.buffs.forEach((buff) => {\r\n            list.push(buff.res.id);\r\n        })\r\n        return list;\r\n    }\r\n\r\n    @property(CCInteger)\r\n    applyBuffID:number = 0;\r\n    @property(CCBoolean)\r\n    get applyBuff() {\r\n        return false;\r\n    }\r\n    set applyBuff(value:boolean) {\r\n        if (!value) {\r\n            return\r\n        }\r\n        this._planeBase?.buffComp.ApplyBuff(false, this.applyBuffID);\r\n    }\r\n}"]}
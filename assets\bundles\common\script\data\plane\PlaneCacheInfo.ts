import { IData } from "db://assets/bundles/common/script/data/DataManager";

export class PlaneCacheInfoElem {
    planeId: number = 0;
}

export class PlaneCacheInfo implements IData {
    _isInit = false;
    curPlaneId: number = 0;
    _planeDataDic:{[key:number]:PlaneCacheInfoElem} = {}; //飞机数据


    public init(): void {
        this._isInit = true;
        this.onNetAllPlaneInfo()
    }

    update(): void {
    }

    onNetAllPlaneInfo(){
        this.curPlaneId = 10100001;//10003101;
        this._planeDataDic = {}
        this._planeDataDic[this.curPlaneId] = {
            planeId:this.curPlaneId,
        }
    }

    getPlaneInfoById(id:number){
        if (!this._isInit){
            this.init();
        }
        return this._planeDataDic[id]
    }
    getCurPlaneInfo() {
        if (!this._isInit){
            this.init();
        }
        return this._planeDataDic[this.curPlaneId]
    }
}

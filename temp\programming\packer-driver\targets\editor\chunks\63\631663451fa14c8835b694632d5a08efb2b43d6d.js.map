{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/match/MatchRankUI.ts"], "names": ["_decorator", "Label", "BundleName", "ButtonPlus", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "UITools", "List", "MatchCellUI", "ccclass", "property", "MatchRankUI", "leftTimes", "data", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getBundleName", "homeMatch", "getUIOption", "isClickBgCloseUI", "onLoad", "btnGo", "addClick", "onGoClick", "Math", "floor", "Date", "now", "i", "push", "rank", "name", "score", "nameArr", "string", "scoreArr", "toString", "list", "numItems", "length", "closeUI", "onShow", "onHide", "onClose", "update", "dt", "left", "lblTime", "formatTime", "onDestroy", "onList<PERSON>ender", "listItem", "row", "dataIndex", "cell", "getComponent", "setInfo"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;;AACZC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,I;;AACEC,MAAAA,W,iBAAAA,W;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;6BAGjBY,W,WADZF,OAAO,CAAC,aAAD,C,UAEHC,QAAQ;AAAA;AAAA,mC,UAGRA,QAAQ,CAACV,KAAD,C,UAKRU,QAAQ;AAAA;AAAA,uB,UAGRA,QAAQ,CAACV,KAAD,C,UAGRU,QAAQ,CAACV,KAAD,C,2BAhBb,MACaW,WADb;AAAA;AAAA,4BACwC;AAAA;AAAA;;AAAA;;AAAA;;AAAA,eAOpCC,SAPoC,GAOhB,CAPgB;;AAAA;;AAAA;;AAAA;;AAAA,eAkBpCC,IAlBoC,GAkBoB,EAlBpB;AAAA;;AAoBhB,eAANC,MAAM,GAAW;AAAE,iBAAO,uBAAP;AAAiC;;AAC5C,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAwB;;AACjC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,SAAlB;AAA8B;;AAC7C,eAAXC,WAAW,GAAU;AAAE,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AAAmC;;AAC9DC,QAAAA,MAAM,GAAS;AACrB,eAAKC,KAAL,CAAYC,QAAZ,CAAqB,KAAKC,SAA1B,EAAqC,IAArC;AACA,eAAKZ,SAAL,GAAiBa,IAAI,CAACC,KAAL,CAAWC,IAAI,CAACC,GAAL,KAAa,IAAxB,IAAgC,OAAO,EAAvC,GAA4C,KAAK,EAAjD,GAAsD,EAAvE;;AAEA,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,EAApB,EAAwBA,CAAC,EAAzB,EAA6B;AACzB,iBAAKhB,IAAL,CAAUiB,IAAV,CAAe;AAAEC,cAAAA,IAAI,EAAEF,CAAR;AAAWG,cAAAA,IAAI,EAAE,KAAjB;AAAwBC,cAAAA,KAAK,EAAE,QAAQJ;AAAvC,aAAf;AACH;;AAED,eAAK,IAAIA,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,CAApB,EAAuBA,CAAC,EAAxB,EAA4B;AACxB,iBAAKK,OAAL,CAAaL,CAAb,EAAgBM,MAAhB,GAAyB,KAAKtB,IAAL,CAAUgB,CAAV,EAAaG,IAAtC;AACA,iBAAKI,QAAL,CAAcP,CAAd,EAAiBM,MAAjB,GAA0B,KAAKtB,IAAL,CAAUgB,CAAV,EAAaI,KAAb,CAAmBI,QAAnB,EAA1B;AACH;;AACD,eAAKC,IAAL,CAAWC,QAAX,GAAsB,KAAK1B,IAAL,CAAU2B,MAAV,GAAmB,CAAzC;AACH;;AAEc,cAAThB,SAAS,GAAG;AACd;AAAA;AAAA,8BAAMiB,OAAN,CAAc9B,WAAd;AACH;;AACW,cAAN+B,MAAM,GAAkB,CAE7B;;AACW,cAANC,MAAM,GAAkB,CAC7B;;AACY,cAAPC,OAAO,GAAkB,CAC9B;;AACSC,QAAAA,MAAM,CAACC,EAAD,EAAmB;AAC/B,cAAIC,IAAI,GAAG,KAAKnC,SAAL,GAAiBa,IAAI,CAACC,KAAL,CAAWC,IAAI,CAACC,GAAL,KAAa,IAAxB,CAA5B;;AACA,cAAImB,IAAI,GAAG,CAAX,EAAc;AACV;AACH;;AACD,eAAKC,OAAL,CAAcb,MAAd,GAAuB,UAAU;AAAA;AAAA,kCAAQc,UAAR,CAAmBF,IAAnB,EAAyB,IAAzB,CAAjC;AACH;;AACSG,QAAAA,SAAS,GAAS,CAC3B;;AAEDC,QAAAA,YAAY,CAACC,QAAD,EAAiBC,GAAjB,EAA8B;AACtC,cAAIC,SAAS,GAAGD,GAAG,GAAG,CAAtB;;AACA,cAAIC,SAAS,GAAG,KAAKzC,IAAL,CAAU2B,MAA1B,EAAkC;AAC9B,kBAAMe,IAAI,GAAGH,QAAQ,CAACI,YAAT;AAAA;AAAA,2CAAb;;AACA,gBAAID,IAAI,KAAK,IAAb,EAAmB;AACfA,cAAAA,IAAI,CAACE,OAAL,CAAa,KAAK5C,IAAL,CAAUyC,SAAV,EAAqBvB,IAAlC,EAAwC,KAAKlB,IAAL,CAAUyC,SAAV,EAAqBtB,IAA7D,EAAmE,KAAKnB,IAAL,CAAUyC,SAAV,EAAqBrB,KAAxF;AACH;AACJ;AACJ;;AAnEmC,O;;;;;iBAET,I;;;;;;;iBAGH,I;;;;;;;iBAKJ,I;;;;;;;iBAGD,E;;;;;;;iBAGC,E", "sourcesContent": ["import { _decorator, Label, Node } from 'cc';\r\nimport { BundleName } from 'db://assets/bundles/common/script/const/BundleConst';\r\nimport { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';\r\nimport { BaseUI, UILayer, UIMgr, UIOpt } from 'db://assets/scripts/core/base/UIMgr';\r\nimport { UITools } from '../../game/utils/UITools';\r\nimport List from '../common/components/list/List';\r\nimport { MatchCellUI } from './MatchCellUI';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('MatchRankUI')\r\nexport class MatchRankUI extends BaseUI {\r\n    @property(ButtonPlus)\r\n    btnGo: ButtonPlus | null = null;\r\n\r\n    @property(Label)\r\n    lblTime: Label | null = null;\r\n\r\n    leftTimes: number = 0;\r\n\r\n    @property(List)\r\n    list: List | null = null;\r\n\r\n    @property(Label)\r\n    nameArr: Label[] = [];\r\n\r\n    @property(Label)\r\n    scoreArr: Label[] = [];\r\n\r\n    data: { rank: number, name: string, score: number }[] = []\r\n\r\n    public static getUrl(): string { return \"prefab/ui/MatchRankUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Default }\r\n    public static getBundleName(): string { return BundleName.homeMatch; }\r\n    public static getUIOption(): UIOpt { return { isClickBgCloseUI: true } }\r\n    protected onLoad(): void {\r\n        this.btnGo!.addClick(this.onGoClick, this);\r\n        this.leftTimes = Math.floor(Date.now() / 1000) + 3600 * 20 + 50 * 60 + 10;\r\n\r\n        for (let i = 0; i < 10; i++) {\r\n            this.data.push({ rank: i, name: \"小师妹\", score: 10000 + i });\r\n        }\r\n\r\n        for (let i = 0; i < 3; i++) {\r\n            this.nameArr[i].string = this.data[i].name;\r\n            this.scoreArr[i].string = this.data[i].score.toString();\r\n        }\r\n        this.list!.numItems = this.data.length - 3;\r\n    }\r\n\r\n    async onGoClick() {\r\n        UIMgr.closeUI(MatchRankUI);\r\n    }\r\n    async onShow(): Promise<void> {\r\n\r\n    }\r\n    async onHide(): Promise<void> {\r\n    }\r\n    async onClose(): Promise<void> {\r\n    }\r\n    protected update(dt: number): void {\r\n        let left = this.leftTimes - Math.floor(Date.now() / 1000);\r\n        if (left < 0) {\r\n            return;\r\n        }\r\n        this.lblTime!.string = \"剩余时间：\" + UITools.formatTime(left, true);\r\n    }\r\n    protected onDestroy(): void {\r\n    }\r\n\r\n    onListRender(listItem: Node, row: number) {\r\n        let dataIndex = row + 3;\r\n        if (dataIndex < this.data.length) {\r\n            const cell = listItem.getComponent(MatchCellUI);\r\n            if (cell !== null) {\r\n                cell.setInfo(this.data[dataIndex].rank, this.data[dataIndex].name, this.data[dataIndex].score);\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n"]}
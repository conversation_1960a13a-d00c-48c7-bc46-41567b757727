{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/PKHistoryCellUI.ts"], "names": ["_decorator", "<PERSON><PERSON>", "Component", "Label", "Node", "MyApp", "csproto", "DataEvent", "EventMgr", "UITools", "AvatarIcon", "ccclass", "property", "PKHistoryCellUI", "guid", "onButtonClick", "netMgr", "sendMessage", "cs", "CS_CMD", "CS_CMD_GAME_PVP_GET_REWARD", "game_pvp_get_reward", "start", "on", "GamePvpGetAward", "getAward", "modifyNumber", "score1P", "score2P", "timestamp", "LabelTime", "string", "formatDate", "iconLeft", "children", "for<PERSON>ach", "node", "active", "iconRight", "update", "deltaTime", "onDestroy"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;;AACtCC,MAAAA,K,iBAAAA,K;;AACFC,MAAAA,O;;AACEC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBZ,U;;iCAGjBa,e,WADZF,OAAO,CAAC,iBAAD,C,UAGHC,QAAQ,CAACX,MAAD,C,UAGRW,QAAQ,CAACT,KAAD,C,UAGRS,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ,CAACT,KAAD,C,UAERS,QAAQ,CAACT,KAAD,C,UAGRS,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ,CAACT,KAAD,C,UAERS,QAAQ,CAACT,KAAD,C,WAGRS,QAAQ,CAACT,KAAD,C,WAERS,QAAQ,CAACT,KAAD,C,WAGRS,QAAQ,CAACR,IAAD,C,WAERQ,QAAQ,CAACR,IAAD,C,2BA9Bb,MACaS,eADb,SACqCX,SADrC,CAC+C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAgCpCY,IAhCoC;AAAA;;AAkC3CC,QAAAA,aAAa,GAAG;AACZ,cAAI,CAAC,KAAKD,IAAV,EAAgB;AACZ;AACH;;AACD;AAAA;AAAA,8BAAME,MAAN,CAAaC,WAAb,CAAyB;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBC,0BAA3C,EAAuE;AAAEC,YAAAA,mBAAmB,EAAE;AAAEP,cAAAA,IAAI,EAAE,KAAKA;AAAb;AAAvB,WAAvE;AACH;;AAEDQ,QAAAA,KAAK,GAAG;AAAA;;AACJ;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,sCAAUC,eAAtB,EAAuC,KAAKC,QAA5C,EAAsD,IAAtD;AACA;AAAA;AAAA,kCAAQC,YAAR,CAAqB,KAAKC,OAA1B,EAAoC,SAApC;AACA;AAAA;AAAA,kCAAQD,YAAR,CAAqB,KAAKE,OAA1B,EAAoC,SAApC;AACA,cAAMC,SAAS,GAAG,UAAlB,CAJI,CAI0B;;AAC9B,eAAKC,SAAL,CAAgBC,MAAhB,GAAyB;AAAA;AAAA,kCAAQC,UAAR,CAAmBH,SAAnB,CAAzB;AAEA,iCAAKI,QAAL,4BAAeC,QAAf,CAAwBC,OAAxB,CAAiCC,IAAD,IAAU;AACtCA,YAAAA,IAAI,CAACC,MAAL,GAAc,KAAd;AACH,WAFD;AAGA,kCAAKC,SAAL,6BAAgBJ,QAAhB,CAAyBC,OAAzB,CAAkCC,IAAD,IAAU;AACvCA,YAAAA,IAAI,CAACC,MAAL,GAAc,KAAd;AACH,WAFD;AAGH;;AACOZ,QAAAA,QAAQ,GAAG,CAClB;;AAIDc,QAAAA,MAAM,CAACC,SAAD,EAAoB,CAEzB;;AAESC,QAAAA,SAAS,GAAS,CAE3B;;AAlE0C,O;;;;;iBAGjB,I;;;;;;;iBAGA,I;;;;;;;iBAGI,I;;;;;;;iBAEP,I;;;;;;;iBAEC,I;;;;;;;iBAGM,I;;;;;;;iBAEP,I;;;;;;;iBAEC,I;;;;;;;iBAGE,I;;;;;;;iBAEC,I;;;;;;;iBAGH,I;;;;;;;iBAEC,I", "sourcesContent": ["import { _decorator, Button, Component, Label, Node } from 'cc';\r\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\r\nimport csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';\r\nimport { DataEvent } from '../../event/DataEvent';\r\nimport { EventMgr } from '../../event/EventManager';\r\nimport { UITools } from '../../game/utils/UITools';\r\nimport { AvatarIcon } from '../common/components/base/AvatarIcon';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('PKHistoryCellUI')\r\nexport class PKHistoryCellUI extends Component {\r\n\r\n    @property(Button)\r\n    btnClick: Button | null = null;\r\n\r\n    @property(Label)\r\n    LabelType: Label | null = null;\r\n\r\n    @property(AvatarIcon)\r\n    avatar1P: AvatarIcon | null = null;\r\n    @property(Label)\r\n    name1P: Label | null = null;\r\n    @property(Label)\r\n    score1P: Label | null = null;\r\n\r\n    @property(AvatarIcon)\r\n    avatar2P: AvatarIcon | null = null;\r\n    @property(Label)\r\n    name2P: Label | null = null;\r\n    @property(Label)\r\n    score2P: Label | null = null;\r\n\r\n    @property(Label)\r\n    LabelTime: Label | null = null;\r\n    @property(Label)\r\n    labelMedal: Label | null = null;\r\n\r\n    @property(Node)\r\n    iconLeft: Node | null = null;\r\n    @property(Node)\r\n    iconRight: Node | null = null;\r\n\r\n    public guid: Long | undefined;\r\n\r\n    onButtonClick() {\r\n        if (!this.guid) {\r\n            return;\r\n        }\r\n        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GAME_PVP_GET_REWARD, { game_pvp_get_reward: { guid: this.guid } });\r\n    }\r\n\r\n    start() {\r\n        EventMgr.on(DataEvent.GamePvpGetAward, this.getAward, this)\r\n        UITools.modifyNumber(this.score1P!, 123456789);\r\n        UITools.modifyNumber(this.score2P!, 123456789);\r\n        const timestamp = 1744731000; // 2025/04/15 20:30\r\n        this.LabelTime!.string = UITools.formatDate(timestamp);\r\n\r\n        this.iconLeft?.children.forEach((node) => {\r\n            node.active = false;\r\n        })\r\n        this.iconRight?.children.forEach((node) => {\r\n            node.active = false;\r\n        })\r\n    }\r\n    private getAward() {\r\n    }\r\n\r\n\r\n\r\n    update(deltaTime: number) {\r\n\r\n    }\r\n\r\n    protected onDestroy(): void {\r\n\r\n    }\r\n\r\n}\r\n\r\n\r\n"]}
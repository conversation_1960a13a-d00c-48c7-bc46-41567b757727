System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, color, find, instantiate, Label, Node, BaseUI, UILayer, UIMgr, BundleName, ButtonPlus, WordType, GameIns, MyApp, getI18StrByKey, StringUtils, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _crd, TYPE_COLOR, ccclass, property, RogueUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "db://assets/scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "../../../const/BundleConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "../../common/components/button/ButtonPlus", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResWorldGroup(extras) {
    _reporterNs.report("ResWorldGroup", "../../../autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWordType(extras) {
    _reporterNs.report("WordType", "../../../autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../../game/GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../../app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfgetI18StrByKey(extras) {
    _reporterNs.report("getI18StrByKey", "../../../../../../../extensions/i18n/assets/LanguageData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfStringUtils(extras) {
    _reporterNs.report("StringUtils", "../../../../../../scripts/utils/StringUtils", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      color = _cc.color;
      find = _cc.find;
      instantiate = _cc.instantiate;
      Label = _cc.Label;
      Node = _cc.Node;
    }, function (_unresolved_2) {
      BaseUI = _unresolved_2.BaseUI;
      UILayer = _unresolved_2.UILayer;
      UIMgr = _unresolved_2.UIMgr;
    }, function (_unresolved_3) {
      BundleName = _unresolved_3.BundleName;
    }, function (_unresolved_4) {
      ButtonPlus = _unresolved_4.ButtonPlus;
    }, function (_unresolved_5) {
      WordType = _unresolved_5.WordType;
    }, function (_unresolved_6) {
      GameIns = _unresolved_6.GameIns;
    }, function (_unresolved_7) {
      MyApp = _unresolved_7.MyApp;
    }, function (_unresolved_8) {
      getI18StrByKey = _unresolved_8.getI18StrByKey;
    }, function (_unresolved_9) {
      StringUtils = _unresolved_9.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "e094c3NOSVC84pbIiaj27xx", "RogueUI", undefined);

      __checkObsolete__(['_decorator', 'color', 'find', 'instantiate', 'Label', 'Node']);

      TYPE_COLOR = {
        [(_crd && WordType === void 0 ? (_reportPossibleCrUseOfWordType({
          error: Error()
        }), WordType) : WordType).None]: "#FFFB80",
        [(_crd && WordType === void 0 ? (_reportPossibleCrUseOfWordType({
          error: Error()
        }), WordType) : WordType).Prop]: "#FFFB80",
        [(_crd && WordType === void 0 ? (_reportPossibleCrUseOfWordType({
          error: Error()
        }), WordType) : WordType).Missile]: "#80ff86",
        [(_crd && WordType === void 0 ? (_reportPossibleCrUseOfWordType({
          error: Error()
        }), WordType) : WordType).Laser]: "#59ffdb"
      };
      ({
        ccclass,
        property
      } = _decorator);

      _export("RogueUI", RogueUI = (_dec = ccclass("RogueUI"), _dec2 = property(Node), _dec3 = property([Node]), _dec4 = property(Label), _dec5 = property(Node), _dec6 = property(Node), _dec7 = property(Node), _dec(_class = (_class2 = class RogueUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "nodeFresh", _descriptor, this);

          _initializerDefineProperty(this, "rogueSelectNodes", _descriptor2, this);

          _initializerDefineProperty(this, "freshTimes", _descriptor3, this);

          _initializerDefineProperty(this, "NodeSelect", _descriptor4, this);

          _initializerDefineProperty(this, "itemProgress", _descriptor5, this);

          _initializerDefineProperty(this, "NodeProgress", _descriptor6, this);

          this._callFunc = null;
          this._groupId = 0;
          this._maxSelectCount = 0;
          this._selectedIds = [];
          this._curRefreshTimes = 0;
          this._maxRefreshTimes = 0;
        }

        static getUrl() {
          return "prefab/RogueUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Default;
        }

        static getBundleName() {
          return (_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).GameFight;
        }

        onLoad() {
          this.nodeFresh.getComponentInChildren(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
            error: Error()
          }), ButtonPlus) : ButtonPlus).addClick(this.onFreshClick, this);
          this.itemProgress.active = false;
        }

        async onShow(groupId, callFunc = null, maxSelectCount = 1) {
          this._groupId = groupId;
          this._maxSelectCount = maxSelectCount;
          this._callFunc = callFunc;
          this._selectedIds = [];
          this._maxRefreshTimes = 2;
          this.refreshRogueUI();
          this.refreshSelectUI();
          this.refreshProgressUI();
          this.refreshTimesUI();
        }

        async onHide(...args) {
          this.unscheduleAllCallbacks();
        }

        async onClose(...args) {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).rogueManager.recycleRogueItems(this.NodeSelect);
        }

        async closeUI() {
          var _this$_callFunc;

          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).closeUI(RogueUI);
          (_this$_callFunc = this._callFunc) == null || _this$_callFunc.call(this, (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).rogueManager.coverToBuffIds(this._selectedIds));
        }

        refreshRogueUI() {
          let list = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).rogueManager.randomWords(this._groupId);
          let len = list.length;

          for (let i = 0; i < len; i++) {
            let wordGroup = list[i];
            let item = this.rogueSelectNodes[i];
            item.active = wordGroup != null;

            if (!item.active) {
              continue;
            }

            let wordConfig = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).lubanTables.TbResWord.get(wordGroup.wordId);
            let NodeItem = find("NodeItem", item);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).rogueManager.recycleRogueItems(NodeItem);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).rogueManager.setRogueItem(NodeItem, wordGroup.wordId);
            let rogueDesc = find("rogueDesc", item);
            let rogueType = find("rogueType", item);
            rogueDesc.getComponent(Label).string = wordConfig.desc;
            rogueDesc.getComponent(Label).color = color(TYPE_COLOR[wordGroup.type] || "");
            item.getComponent(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
              error: Error()
            }), ButtonPlus) : ButtonPlus).addClick(() => {
              this.selectRogue(wordGroup);
            }, this);
          }
        }

        onFreshClick() {
          if (this._selectedIds.length >= this._maxSelectCount) {
            return;
          }

          if (this._curRefreshTimes >= this._maxRefreshTimes) {
            return;
          }

          this._curRefreshTimes++;
          this.refreshRogueUI();
          this.refreshTimesUI();
        }

        selectRogue(wordGroup) {
          if (this._selectedIds.length >= this._maxSelectCount) {
            return;
          }

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).rogueManager.selectRogue([wordGroup]);

          this._selectedIds.push(wordGroup.ID);

          this.refreshSelectUI();
          this.refreshProgressUI();
          this.refreshTimesUI();

          if (this._selectedIds.length >= this._maxSelectCount) {
            this.scheduleOnce(() => {
              this.closeUI();
            }, 0.5);
          } else {
            this.refreshRogueUI();
          }
        }

        refreshSelectUI() {
          let data = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).rogueManager.getSeLectedHistory();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).rogueManager.recycleRogueItems(this.NodeSelect);
          data.forEach((value, key) => {
            let config = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).lubanTables.TbResWordGroup.get(key);
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).rogueManager.setRogueItem(this.NodeSelect, config.wordId, value);
          });
        }

        refreshProgressUI() {
          this.NodeProgress.children.forEach(item => {
            item.active = false;
          });
          let curSelectCount = this._selectedIds.length;

          for (let i = 1; i <= this._maxSelectCount; i++) {
            let item = find(`item${i}`, this.NodeProgress);

            if (!item) {
              item = instantiate(this.itemProgress);
              item.name = `item${i}`;
              item.setPosition(item.position.x, 0);
              this.NodeProgress.addChild(item);
            }

            item.active = true;
            let progressBg = find("progressBg", item);
            let progressBg2 = find("progressBg2", item);
            let complete = find("complete", item);
            let LabelIndex = find("LabelIndex", item);
            progressBg.active = i < this._maxSelectCount;
            progressBg2.active = i < this._maxSelectCount && i <= curSelectCount;
            complete.active = i <= curSelectCount;
            LabelIndex.getComponent(Label).string = String(i);
          }
        }

        refreshTimesUI() {
          this.freshTimes.string = (_crd && StringUtils === void 0 ? (_reportPossibleCrUseOfStringUtils({
            error: Error()
          }), StringUtils) : StringUtils).getReplaceStr((_crd && getI18StrByKey === void 0 ? (_reportPossibleCrUseOfgetI18StrByKey({
            error: Error()
          }), getI18StrByKey) : getI18StrByKey)("ROUGE_LEFT_TIMES"), [this._maxRefreshTimes - this._curRefreshTimes, this._maxRefreshTimes]);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "nodeFresh", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "rogueSelectNodes", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "freshTimes", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "NodeSelect", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "itemProgress", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "NodeProgress", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=ab3aa85054eb937cc560b987ef904fbde711c4cc.js.map
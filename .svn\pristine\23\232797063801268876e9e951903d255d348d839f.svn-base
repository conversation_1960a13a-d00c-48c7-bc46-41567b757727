import { _decorator, EventTouch, Label, Node, Sprite, Widget } from 'cc';
import { MyApp } from 'db://assets/bundles/common/script/app/MyApp';
import { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';
import { BaseUI, UILayer, UIMgr } from "db://assets/scripts/core/base/UIMgr";
import { logError } from 'db://assets/scripts/utils/Logger';
import { BundleName } from '../../const/BundleConst';
import { HomeUIBaseSystem } from '../../const/HomeUIConst';
import { EventMgr } from '../../event/EventManager';
import { GameEnum } from '../../game/const/GameEnum';
import { startGameByMode } from '../../game/GameInsStart';
import { FriendUI } from '../friend/FriendUI';
import { MailUI } from '../mail/MailUI';
import { Story<PERSON> } from '../story/StoryUI';
import { TaskTipUI } from '../task/TaskTipUI';
import { DataMgr } from '../../data/DataManager';

const { ccclass, property } = _decorator;
@ccclass('HomeUI')
export class HomeUI extends BaseUI {
    public static getUrl(): string { return "prefab/ui/HomeUI"; }
    public static getLayer(): UILayer { return UILayer.Background }
    public static getBundleName(): string { return BundleName.Home }
    @property(Node)
    nodeBaseSystem: Node | null = null;

    @property(ButtonPlus)
    btnBattle: ButtonPlus | null = null;
    @property(ButtonPlus)
    btnStory: ButtonPlus | null = null;

    @property(Node)
    nodePlaneInfo: Node | null = null;
    @property(Node)
    planeStars: Node | null = null;
    @property(Label)
    planeName: Label | null = null;
    @property(Sprite)
    planeImage: Sprite | null = null;

    @property(Node)
    nodePlane: Node | null = null;

    @property(Node)
    panel: Node | null = null;

    private _isInit: boolean = false;

    protected onLoad(): void {
        MyApp.globalMgr.setUIResolution();
        let notchHeight = MyApp.platformSDK.getStatusBarHeight();
        this.panel!.getComponent(Widget)!.isAlignTop = true;
        this.panel!.getComponent(Widget)!.top = notchHeight;
        this.btnBattle!.addClick(this.onBattleClick, this);
        this.btnStory!.addClick(this.onStoryClick, this);
        this.nodeBaseSystem!.getComponentsInChildren(ButtonPlus).forEach((btn) => {
            btn.addClick(this.onBaseSystemClick, this);
        })
        this.setPlaneInfo();
    }

    setPlaneInfo() {
        let planeData = DataMgr.planeInfo.getCurPlaneInfo();
        if (planeData === undefined) {
            return;
        }
        const config = MyApp.lubanTables.TbPlane.get(planeData.planeId);
        if (config === undefined) {
            return;
        }
        let star = config!.starLevel;
        this.planeStars!.children.forEach((element, index) => {
            element!.children[0].active = index < star;
        });
        this.planeName!.string = config!.name;
    }

    async init() {
        if (this._isInit) return
        //异步加载初始化界面内的ui
        MyApp.resMgr.loadBundle(BundleName.HomeTask).then(async () => {
            UIMgr.openUI(TaskTipUI)
        })
        this._isInit = true;
    }

    async onShow(...args: any[]): Promise<void> {
        await this.init();
    }
    async onHide(...args: any[]): Promise<void> {
    }
    async onClose(...args: any[]): Promise<void> {
        EventMgr.targetOff(this)
    }
    protected onDestroy(): void {
        this.unscheduleAllCallbacks();
    }
    protected update(dt: number): void {
    }

    async onBattleClick() {
        MyApp.globalMgr.chapterID = 0;
        this.onBattle();
    }

    async onBattle() {
        startGameByMode(GameEnum.GameModeId.ENDLESS);
    }

    async onStoryClick() {
        await UIMgr.openUI(StoryUI);
    }

    private onBaseSystemClick(evt: EventTouch) {
        const nodeName = evt.target.name
        switch (nodeName) {
            case HomeUIBaseSystem.Social:
                break;
            case HomeUIBaseSystem.Announcement:
                break;
            case HomeUIBaseSystem.Friend:
                UIMgr.openUI(FriendUI)
                break;
            case HomeUIBaseSystem.Mail:
                UIMgr.openUI(MailUI)
                break;
            case HomeUIBaseSystem.Setting:
                break;
            default:
                logError("HomeUI", `onBaseSystemClick nodeName not found click handler${nodeName}`)
                break;
        }
    }
}


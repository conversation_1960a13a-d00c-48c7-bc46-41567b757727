import { error } from "cc";

import { ResEnemy } from "../../autogen/luban/schema";

import { MyApp } from "db://assets/bundles/common/script/app/MyApp";

import { AttributeConst } from "db://assets/bundles/common/script/const/AttributeConst";
import { PlaneBaseData } from "../../data/plane/PlaneData";

const PATH_SPINE = "game/prefabs/plane/enemyplane/"
export class EnemyData extends PlaneBaseData {

    config: ResEnemy | null = null;//飞机静态配置

    constructor(planeId:number) {
        super(planeId);
        this.config = MyApp.lubanTables.TbResEnemy.get(this._planeId)!;
        this.updateData();
    }

    get recoursePrefab() {
        if (!this.config) {
            return "";
        }
        return PATH_SPINE + this.config.prefab
    }

    updateData() {
        if (!this.config) {
            error(`enemyPlane ${this._planeId}: config is null, cannot update attributes.`);
            return;
        }
        this.addBaseAttribute(AttributeConst.MaxHPOutAdd, this.config.baseHp);
        this.addBaseAttribute(AttributeConst.AttackOutAdd, this.config.baseAtk);
    }
}
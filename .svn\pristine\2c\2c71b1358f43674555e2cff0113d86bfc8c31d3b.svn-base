"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.methods = void 0;
exports.load = load;
exports.unload = unload;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
function genEnum(jsonFileName, enumName) {
    try {
        // @ts-ignore
        const projectPath = Editor.Project.path;
        const jsonPath = path.join(projectPath, 'assets', 'bundles', 'luban', jsonFileName + '.json');
        const outputPath = path.join(projectPath, 'assets', 'editor', 'enum-gen', enumName + '.ts');
        // 检查输入文件是否存在
        if (!fs.existsSync(jsonPath)) {
            console.warn('json file not found:', jsonPath);
            return;
        }
        // 读取JSON文件
        const jsonContent = fs.readFileSync(jsonPath, 'utf8');
        const bulletData = JSON.parse(jsonContent);
        if (!Array.isArray(bulletData)) {
            console.warn('json is not an array');
            return;
        }
        // 生成枚举内容
        let enumContent = `export enum ${enumName} {\n`;
        bulletData.forEach((item) => {
            if (item.id) {
                // remove '-'
                item.name = item.name.replaceAll('-', '');
                // fix name start with digit(s)
                item.name = item.name.replace(/^(\d)/, 'D$1');
                if (item.name && item.name.trim() !== '') {
                    enumContent += `    ${item.name} = ${item.id},\n`;
                }
                else {
                    enumContent += `    ${item.id} = ${item.id},\n`;
                }
            }
        });
        enumContent += '}\n';
        // 确保输出目录存在
        const outputDir = path.dirname(outputPath);
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }
        // 写入文件
        fs.writeFileSync(outputPath, enumContent, 'utf8');
        console.log(`${enumName}.ts generated successfully at:`, outputPath);
        // 刷新资源数据库
        // @ts-ignore
        Editor.Message.request('asset-db', 'refresh-asset', `db://assets/editor/enum-gen/${enumName}.ts`);
    }
    catch (error) {
        console.error(`Error creating ${enumName}:`, error);
    }
}
/**
 * @en Registration method for the main process of Extension
 * @zh 为扩展的主进程的注册方法
 */
exports.methods = {
    /**
     * @en A method that can be triggered by message
     * @zh 通过 message 触发的方法
     */
    movePlayerUp() {
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'emitter-editor',
            method: 'movePlayerUp',
            args: []
        });
    },
    movePlayerDown() {
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'emitter-editor',
            method: 'movePlayerDown',
            args: []
        });
    },
    movePlayerLeft() {
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'emitter-editor',
            method: 'movePlayerLeft',
            args: []
        });
    },
    movePlayerRight() {
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'emitter-editor',
            method: 'movePlayerRight',
            args: []
        });
    },
    /**
     * 监听asset-db:asset-changed消息
     */
    onAssetChanged(_uuid, info) {
        if (info && info.path) {
            if (info.path.includes('tbresemitter.json')) {
                console.log('tbresemitter.json file changed, regenerating EmitterEnum...');
                exports.methods.createEmitterEnum();
            }
            else if (info.path.includes('tbresenemy.json')) {
                console.log('tbresenemy.json file changed, regenerating EnemyEnum...');
                exports.methods.createEnemyEnum();
            }
        }
    },
    /**
     * 创建EmitterEnum枚举文件
     */
    createEmitterEnum() {
        genEnum('tbresemitter', 'EmitterEnum');
    },
    createEnemyEnum() {
        genEnum('tbresenemy', 'EnemyEnum');
    }
};
/**
 * @en Method Triggered on Extension Startup
 * @zh 扩展启动时触发的方法
 */
function load() {
    console.log('emitter-editor extension loaded');
    console.log('Available methods:', Object.keys(exports.methods));
    // 初始化时生成一次EmitterEnum
    exports.methods.createEmitterEnum();
    exports.methods.createEnemyEnum();
}
/**
 * @en Method triggered when uninstalling the extension
 * @zh 卸载扩展时触发的方法
 */
function unload() { }
//# sourceMappingURL=data:application/json;base64,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
import { _decorator, EventTouch, Layout, math, UITransform } from 'cc';
import { BundleName } from 'db://assets/bundles/common/script/const/BundleConst';
import { EventMgr } from 'db://assets/bundles/common/script/event/EventManager';
import { HomeUIEvent } from 'db://assets/bundles/common/script/event/HomeUIEvent';
import { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';
import { BottomTab } from 'db://assets/bundles/common/script/ui/home/<USER>';
import { BaseUI, UILayer, UIMgr } from 'db://assets/scripts/core/base/UIMgr';
import { logDebug } from 'db://assets/scripts/utils/Logger';
import { BottomUIEvent } from '../../event/BottomUIEvent';

const { ccclass, property } = _decorator;

@ccclass('BottomUI')
export class BottomUI extends BaseUI {
    public static getUrl(): string { return "prefab/ui/BottomUI"; }
    public static getLayer(): UILayer { return UILayer.Default }
    public static getBundleName(): string { return BundleName.Home }
    @property(Layout)
    bottomLayer: Layout | null = null;

    private _moduleBtns: ButtonPlus[] = [];
    private _curClickTab: BottomTab = BottomTab.Home;
    private _originSize: math.Size | null = null;
    private _tabUIs: Map<number, BaseUI> = new Map();

    protected onLoad(): void {
        const moduleBtns = this.bottomLayer!.node.getComponentsInChildren(ButtonPlus);
        for (let i = 0; i < moduleBtns.length; i++) {
            const element = moduleBtns[i];
            element.addClick(this.onClick, this)
            this._moduleBtns.push(element);
        }
        this._originSize = this._moduleBtns[0]!.node.getComponent(UITransform)!.contentSize
        EventMgr.on(HomeUIEvent.BottomTabRegister, this.onTabUIRegister, this)
        this.updateLayout(true)
        logDebug("BottomUI onLoad", "aaaaa")
    }

    updateLayout(immediate = false) {
        /*暂时不做表现，等策划定
        this._moduleBtns.forEach((btn, i) => {
            const isSelected = i === this._curClickTab;
            const targetScale = isSelected ? 1.2 : 0.8;
            if (immediate) {
                btn.node.setScale(v3(targetScale, targetScale, 1));
            } else {
                tween(btn.node)
                    .to(.3,
                        { scale: v3(targetScale, targetScale, 1) },
                        { easing: 'sineOut' }
                    )
                    .start();
            }
        });

        // 延迟更新Layout以确保动画完成后重新计算位置
        this.scheduleOnce(() => {
            this.bottomLayer?.updateLayout();
        }, immediate ? 0 : .3);
        */
    }

    updateClickState() {
        const curTab = this._tabUIs.get(this._curClickTab);
        if (!curTab) {
            return
        }
        Object.values(BottomTab).forEach(v => {
            if (v == this._curClickTab) return;
            const baseUI = this._tabUIs.get(Number(v))
            if (!baseUI) { return }
            UIMgr.hideUI(baseUI.uiClass!)
        })
        this.updateLayout(true)
    }

    async onClick(event: EventTouch) {
        const index = this._moduleBtns.findIndex(v => {
            return v.node === event.target;
        })
        const oldTab = this._curClickTab;
        if (index == this._curClickTab) return;
        this._curClickTab = index
        const curTab = this._tabUIs.get(this._curClickTab);
        if (!curTab) { return }
        await UIMgr.openUI(curTab.uiClass!)
        EventMgr.emit(BottomUIEvent.SwitchPanel, oldTab, this._curClickTab)
        this.updateClickState();
    }

    onTabUIRegister(tab: BottomTab, baseUI: BaseUI) {
        this._tabUIs.set(tab, baseUI)
    }

    async onShow(...args: any[]): Promise<void> {
    }
    async onHide(...args: any[]): Promise<void> {
        this._tabUIs.forEach(v => {
            UIMgr.hideUI(v.uiClass!)
        })
    }

    async onClose(...args: any[]): Promise<void> {
        this._tabUIs.forEach(v => {
            UIMgr.closeUI(v.uiClass!)
        })
    }
    protected update(dt: number): void {
    }

}


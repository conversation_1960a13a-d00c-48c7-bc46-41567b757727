System.register(["__unresolved_0", "cc", "cc/env", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, assetManager, CCInteger, Component, instantiate, Prefab, v2, Vec2, EDITOR, Tools, MyApp, GameIns, logDebug, logError, logWarn, LevelUtils, _dec, _dec2, _dec3, _dec4, _class, _class2, _descriptor, _descriptor2, _descriptor3, _dec5, _dec6, _dec7, _dec8, _class4, _class5, _descriptor4, _crd, ccclass, property, executeInEditMode, menu, TerrainElem, RandTerrain;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "db://assets/bundles/common/script/game/utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/bundles/common/script/app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "db://assets/bundles/common/script/game/GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogDebug(extras) {
    _reporterNs.report("logDebug", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogError(extras) {
    _reporterNs.report("logError", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogWarn(extras) {
    _reporterNs.report("logWarn", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelUtils(extras) {
    _reporterNs.report("LevelUtils", "db://assets/bundles/common/script/game/ui/map/LevelUtils", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      assetManager = _cc.assetManager;
      CCInteger = _cc.CCInteger;
      Component = _cc.Component;
      instantiate = _cc.instantiate;
      Prefab = _cc.Prefab;
      v2 = _cc.v2;
      Vec2 = _cc.Vec2;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }, function (_unresolved_2) {
      Tools = _unresolved_2.Tools;
    }, function (_unresolved_3) {
      MyApp = _unresolved_3.MyApp;
    }, function (_unresolved_4) {
      GameIns = _unresolved_4.GameIns;
    }, function (_unresolved_5) {
      logDebug = _unresolved_5.logDebug;
      logError = _unresolved_5.logError;
      logWarn = _unresolved_5.logWarn;
    }, function (_unresolved_6) {
      LevelUtils = _unresolved_6.LevelUtils;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "c06femD0WhPk63lQ/D1YJtE", "RandTerrain", undefined);

      __checkObsolete__(['_decorator', 'assetManager', 'CCInteger', 'Component', 'instantiate', 'Prefab', 'v2', 'Vec2']);

      ({
        ccclass,
        property,
        executeInEditMode,
        menu
      } = _decorator);

      _export("TerrainElem", TerrainElem = (_dec = ccclass('TerrainElem'), _dec2 = property(CCInteger), _dec3 = property(Prefab), _dec4 = property({
        displayName: "坐标偏移"
      }), _dec(_class = (_class2 = class TerrainElem {
        constructor() {
          _initializerDefineProperty(this, "weight", _descriptor, this);

          _initializerDefineProperty(this, "elem", _descriptor2, this);

          _initializerDefineProperty(this, "offSet", _descriptor3, this);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "weight", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "elem", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "offSet", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return new Vec2(0, 0);
        }
      })), _class2)) || _class));

      _export("RandTerrain", RandTerrain = (_dec5 = ccclass('RandTerrain'), _dec6 = executeInEditMode(), _dec7 = menu('地形系统/随机地形组'), _dec8 = property({
        type: [TerrainElem]
      }), _dec5(_class4 = _dec6(_class4 = _dec7(_class4 = (_class5 = class RandTerrain extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "terrain", _descriptor4, this);
        }

        onLoad() {
          this._loadElems();
        } // 纯表现层业务，请勿将逻辑代码写到这里


        update() {
          if (EDITOR) {
            if (this.terrain === null || this.terrain.length == 0) {
              return;
            }

            const isCountMatch = this.node.children.length === this.terrain.length;
            let isUUIDMatch = true;

            for (let i = 0; i < Math.min(this.node.children.length, this.terrain.length); i++) {
              var _node$_prefab;

              const terrainElem = this.terrain[i]; // 增加空值检查

              if (!terrainElem || !terrainElem.elem) {
                (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
                  error: Error()
                }), logWarn) : logWarn)('RandTerrain', ` TerrainElem at index ${i} is invalid`);
                continue; // 跳过无效元素
              }

              const node = this.node.children[i]; // @ts-ignore

              const nodeUUID = (_node$_prefab = node._prefab) == null || (_node$_prefab = _node$_prefab.asset) == null ? void 0 : _node$_prefab._uuid; // 使用可选链

              const elemUUID = terrainElem.elem.uuid;

              if (nodeUUID !== elemUUID) {
                isUUIDMatch = false;
                break;
              }
            }

            if (!isCountMatch || !isUUIDMatch) {
              this._loadElems();
            } else {
              this.node.children.forEach((child, index) => {
                this.terrain[index].offSet = v2(child.position.x, child.position.y);
              });
            }
          }
        } // 只在编辑器调用


        play(bPlay) {
          if (EDITOR) {
            if (bPlay) {
              let weights = [];

              for (let i = 0; i < this.terrain.length; i++) {
                weights.push(this.terrain[i].weight);
              }

              const selectedIndex = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).getRandomIndexByWeights(weights, Math.random()); // 设置所有节点的active状态

              for (let i = 0; i < this.node.children.length; i++) {
                this.node.children[i].active = i === selectedIndex;
              }
            } else {
              for (let i = 0; i < this.node.children.length; i++) {
                this.node.children[i].active = true;
              }
            }
          }
        }

        onDestroy() {
          this.node.removeAllChildren();
        }

        _loadElems() {
          this.terrain.forEach(elem => {
            if (!elem || elem.elem == null) {
              return;
            }

            this.node.removeAllChildren();

            if (EDITOR) {
              var _elem$elem;

              assetManager.loadAny({
                uuid: (_elem$elem = elem.elem) == null ? void 0 : _elem$elem.uuid
              }, (err, prefab) => {
                if (err) {
                  (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
                    error: Error()
                  }), logError) : logError)('RandTerrain', ` load TerrainElem prefab err ${err}`);
                  return;
                }

                var node = instantiate(prefab);
                node.setPosition(elem.offSet.x, elem.offSet.y, 0);
                this.node.addChild(node);
              });
            } else {
              let weights = [];

              for (let i = 0; i < this.terrain.length; i++) {
                weights.push(this.terrain[i].weight);
              }

              const selectedIndex = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).getRandomIndexByWeights(weights, (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.random());
              const elem = this.terrain[selectedIndex];
              const path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                error: Error()
              }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                error: Error()
              }), MyApp) : MyApp).resMgr.defaultBundleName, elem.elem.uuid);
              (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                error: Error()
              }), MyApp) : MyApp).resMgr.load(path, (err, prefab) => {
                if (err) {
                  return;
                }

                var node = instantiate(prefab);
                node.setPosition(elem.offSet.x, elem.offSet.y, 0);
                this.node.addChild(node);
                (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                  error: Error()
                }), logDebug) : logDebug)('RandTerrain', ` load TerrainElem prefab ${(_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
                  error: Error()
                }), LevelUtils) : LevelUtils).extractPathPart(path, '')}`);
              });
            }
          });
        }

      }, (_descriptor4 = _applyDecoratedDescriptor(_class5.prototype, "terrain", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      })), _class5)) || _class4) || _class4) || _class4));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=97ad45c924900ac4799a7df7974bf339c0924b9e.js.map
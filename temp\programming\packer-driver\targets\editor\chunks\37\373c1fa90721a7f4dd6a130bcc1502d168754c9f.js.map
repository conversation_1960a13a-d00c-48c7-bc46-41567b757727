{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/SettlementUI.ts"], "names": ["_decorator", "Label", "Node", "ProgressBar", "tween", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "MyApp", "BundleName", "DataMgr", "UITools", "ButtonPlus", "StatisticsUI", "ccclass", "property", "SettlementUI", "game_stats", "_scoreTween", "_expTween", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Top", "getBundleName", "Home", "getUIOption", "isClickBgCloseUI", "onLoad", "btnNext", "addClick", "onNextClick", "buttonDouble", "onDoubletClick", "buttonStatistics", "onStatisticsClick", "scoreAdd", "string", "lblScore", "scoreHigh", "passScore", "lblTimeCost", "formatTime", "modifyNumber", "leftDoubleTimes", "gap", "score", "value", "to", "onUpdate", "target", "undefined", "Math", "round", "toString", "start", "curLevel", "role", "curExp", "maxExp", "exp", "expProBar", "progress", "lblExp", "lblLevel", "setNodeItem", "nodeItem1", "nodeItem2", "nodeItem3", "stop", "openUI", "closeUI", "node", "itemID", "limit", "res", "lubanTables", "TbResItem", "get", "nodeQuaIcon", "getChildByName", "icon", "quaSprite", "quality", "modifyStateSprite", "labelNum", "getComponent", "labelLimit", "labelNumY", "y", "labelLimitY", "middleY", "trim", "onShow", "result", "max_levels", "total_score", "nodeBestWeek", "active", "is_week_bset", "history_bese_score", "total_time_cost", "reward_items", "for<PERSON>ach", "item", "item_id", "expiration", "onHide", "onClose"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;;AAEtCC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBf,U;;8BAIjBgB,Y,WADZF,OAAO,CAAC,cAAD,C,UAGHC,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,UAGRA,QAAQ,CAACd,KAAD,C,UAERc,QAAQ,CAACd,KAAD,C,UAERc,QAAQ,CAACd,KAAD,C,UAGRc,QAAQ,CAACd,KAAD,C,UAERc,QAAQ,CAACZ,WAAD,C,WAERY,QAAQ,CAACd,KAAD,C,WAGRc,QAAQ,CAACb,IAAD,C,WAERa,QAAQ,CAACb,IAAD,C,WAERa,QAAQ,CAACb,IAAD,C,WAGRa,QAAQ,CAACb,IAAD,C,WAGRa,QAAQ,CAACd,KAAD,C,WAGRc,QAAQ,CAACd,KAAD,C,WAGRc,QAAQ,CAACd,KAAD,C,WAGRc,QAAQ,CAACb,IAAD,C,2BA3Cb,MACac,YADb;AAAA;AAAA,4BACyC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eA6CrCC,UA7CqC,GA6CM,EA7CN;AAAA,eA+C7BC,WA/C6B;AA+CX;AA/CW,eAgD7BC,SAhD6B;AAAA;;AAgDX;AAEN,eAANC,MAAM,GAAW;AAAE,iBAAO,wBAAP;AAAkC;;AAC7C,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,GAAf;AAAoB;;AAC7B,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,IAAlB;AAAwB;;AACvC,eAAXC,WAAW,GAAU;AAC/B,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AACH;;AAESC,QAAAA,MAAM,GAAS;AACrB,eAAKC,OAAL,CAAcC,QAAd,CAAuB,KAAKC,WAA5B,EAAyC,IAAzC;AACA,eAAKC,YAAL,CAAmBF,QAAnB,CAA4B,KAAKG,cAAjC,EAAiD,IAAjD;AACA,eAAKC,gBAAL,CAAuBJ,QAAvB,CAAgC,KAAKK,iBAArC,EAAwD,IAAxD;AAEA,eAAKC,QAAL,CAAeC,MAAf,GAAwB,UAAU,GAAV,GAAgB,GAAxC;AACA,eAAKC,QAAL,CAAeD,MAAf,GAAwB,GAAxB;AACA,eAAKE,SAAL,CAAgBF,MAAhB,GAAyB,WAAW,KAApC;AACA,eAAKG,SAAL,CAAgBH,MAAhB,GAAyB,IAAzB;AACA,eAAKI,WAAL,CAAkBJ,MAAlB,GAA2B,QAAQ;AAAA;AAAA,kCAAQK,UAAR,CAAmB,GAAnB,CAAnC;AACA;AAAA;AAAA,kCAAQC,YAAR,CAAqB,KAAKC,eAA1B,EAA4C,EAA5C,EAVqB,CAYrB;;AAEA,gBAAMC,GAAG,GAAG,GAAZ,CAdqB,CAerB;;AACA,gBAAMC,KAAK,GAAG,IAAd;AACA,eAAK3B,WAAL,GAAmBd,KAAK,CAAC;AAAE0C,YAAAA,KAAK,EAAE;AAAT,WAAD,CAAL,CACdC,EADc,CACXH,GADW,EACN;AAAEE,YAAAA,KAAK,EAAED;AAAT,WADM,EACY;AACvBG,YAAAA,QAAQ,EAAGC,MAAD,IAAY;AAClB,kBAAI,CAAC,KAAKZ,QAAN,IAAkBY,MAAM,KAAKC,SAAjC,EAA4C;AAC5C,mBAAKb,QAAL,CAAcD,MAAd,GAAuBe,IAAI,CAACC,KAAL,CAAWH,MAAM,CAACH,KAAlB,EAAyBO,QAAzB,EAAvB;AACH;AAJsB,WADZ,EAOdC,KAPc,EAAnB,CAjBqB,CA0BrB;;AACA,cAAIC,QAAQ,GAAG;AAAA;AAAA,kCAAQC,IAAR,CAAaD,QAA5B;AACA,cAAIE,MAAM,GAAG;AAAA;AAAA,kCAAQD,IAAR,CAAaC,MAA1B;AACA,cAAIC,MAAM,GAAG;AAAA;AAAA,kCAAQF,IAAR,CAAaE,MAA1B;AACA,cAAIC,GAAG,GAAGF,MAAM,GAAGC,MAAnB;;AACA,cAAIC,GAAG,GAAG,CAAV,EAAa;AACTA,YAAAA,GAAG,GAAG,CAAN;AACH;;AAED,eAAKC,SAAL,CAAgBC,QAAhB,GAA2B,CAA3B;;AACA,cAAIH,MAAM,GAAG,CAAb,EAAgB;AACZ;AACA,iBAAKvC,SAAL,GAAiBf,KAAK,CAAC,KAAKwD,SAAN,CAAL,CACZb,EADY,CACTH,GADS,EACJ;AAAEiB,cAAAA,QAAQ,EAAEF;AAAZ,aADI,EACe;AACxBX,cAAAA,QAAQ,EAAE,MAAM;AACZ,oBAAI,CAAC,KAAKY,SAAV,EAAqB;AACrB,qBAAKE,MAAL,CAAa1B,MAAb,GAAuB,GAAEe,IAAI,CAACC,KAAL,CAAWM,MAAM,GAAG,KAAKE,SAAL,CAAeC,QAAnC,CAA6C,IAAGH,MAAO,EAAhF;AACH;AAJuB,aADf,EAOZJ,KAPY,EAAjB;AAQH;;AACD,eAAKS,QAAL,CAAe3B,MAAf,GAAwBmB,QAAQ,CAACF,QAAT,EAAxB;AAEA,eAAKW,WAAL,CAAiB,KAAKC,SAAtB,EAAkC,QAAlC,EAA4C,GAA5C;AACA,eAAKD,WAAL,CAAiB,KAAKE,SAAtB,EAAkC,QAAlC,EAA4C,CAA5C;AACA,eAAKF,WAAL,CAAiB,KAAKG,SAAtB,EAAkC,QAAlC,EAA4C,GAA5C;AACH;;AAEgB,cAAXrC,WAAW,GAAG;AAChB;AACA,cAAI,KAAKZ,WAAT,EAAsB,KAAKA,WAAL,CAAiBkD,IAAjB;AACtB,cAAI,KAAKjD,SAAT,EAAoB,KAAKA,SAAL,CAAeiD,IAAf;AACpB,gBAAM;AAAA;AAAA,8BAAMC,MAAN;AAAA;AAAA,2CAAN;AACA;AAAA;AAAA,8BAAMC,OAAN,CAActD,YAAd;AACH;;AAEmB,cAAdgB,cAAc,GAAG;AACnB;AACA,cAAI,KAAKd,WAAT,EAAsB,KAAKA,WAAL,CAAiBkD,IAAjB;AACtB,cAAI,KAAKjD,SAAT,EAAoB,KAAKA,SAAL,CAAeiD,IAAf;AACpB,gBAAM;AAAA;AAAA,8BAAMC,MAAN;AAAA;AAAA,2CAAN;AACA;AAAA;AAAA,8BAAMC,OAAN,CAActD,YAAd;AACH;;AAEsB,cAAjBkB,iBAAiB,GAAG;AACtB;AACA,cAAI,KAAKhB,WAAT,EAAsB,KAAKA,WAAL,CAAiBkD,IAAjB;AACtB,cAAI,KAAKjD,SAAT,EAAoB,KAAKA,SAAL,CAAeiD,IAAf;AACpB,gBAAM;AAAA;AAAA,8BAAMC,MAAN;AAAA;AAAA,2CAAN;AACA;AAAA;AAAA,8BAAMC,OAAN,CAActD,YAAd;AACH;;AAEDgD,QAAAA,WAAW,CAACO,IAAD,EAAaC,MAAb,EAA6BC,KAA7B,EAA4C;AACnD,cAAIC,GAAG,GAAG;AAAA;AAAA,8BAAMC,WAAN,CAAkBC,SAAlB,CAA4BC,GAA5B,CAAgCL,MAAhC,CAAV;;AACA,cAAIE,GAAG,KAAK,IAAZ,EAAkB;AACd;AACH;;AAED,cAAII,WAAW,GAAGP,IAAI,CAAEQ,cAAN,CAAqB,aAArB,CAAlB;AACA,cAAIC,IAAI,GAAGF,WAAW,CAAEC,cAAb,CAA4B,MAA5B,CAAX;AACA,cAAIE,SAAS,GAAGH,WAAW,CAAEC,cAAb,CAA4B,mBAA5B,CAAhB;AACA,cAAIG,OAAO,GAAGR,GAAG,CAAEQ,OAAL,GAAe,CAA7B;AACA;AAAA;AAAA,kCAAQC,iBAAR,CAA0BF,SAA1B,EAAsCC,OAAtC;AACA,cAAIE,QAAQ,GAAGb,IAAI,CAAEQ,cAAN,CAAqB,UAArB,CAAf;AACA;AAAA;AAAA,kCAAQrC,YAAR,CAAqB0C,QAAQ,CAAEC,YAAV,CAAuBpF,KAAvB,CAArB,EAAqD,GAArD;AACA,cAAIqF,UAAU,GAAGf,IAAI,CAAEQ,cAAN,CAAqB,YAArB,CAAjB;;AACA,cAAIN,KAAK,KAAK,CAAd,EAAiB;AACba,YAAAA,UAAU,CAAED,YAAZ,CAAyBpF,KAAzB,EAAiCmC,MAAjC,GAA0C,EAA1C;AACH,WAFD,MAEO;AACH;AAAA;AAAA,oCAAQM,YAAR,CAAqB4C,UAAU,CAAED,YAAZ,CAAyBpF,KAAzB,CAArB,EAAuDwE,KAAvD,EAA8D,GAA9D;AACH;;AACD,gBAAMc,SAAS,GAAGH,QAAQ,CAAEI,CAA5B;AACA,gBAAMC,WAAW,GAAGH,UAAU,CAAEE,CAAhC;AACA,gBAAME,OAAO,GAAG,CAACH,SAAS,GAAGE,WAAb,IAA4B,CAA5C;;AACA,cAAIH,UAAU,CAAED,YAAZ,CAAyBpF,KAAzB,EAAiCmC,MAAjC,CAAwCuD,IAAxC,OAAmD,EAAvD,EAA2D;AACvDP,YAAAA,QAAQ,CAAEI,CAAV,GAAcE,OAAd;AACH;AACJ;;AACW,cAANE,MAAM,CAACC,MAAD,EAAkD;AAAA;;AAC1D,cAAIA,MAAM,IAAI,IAAd,EAAoB;AACpB,eAAKtD,SAAL,CAAgBH,MAAhB,kDAAyByD,MAAM,CAACC,UAAhC,qBAAyB,mBAAmBzC,QAAnB,EAAzB,oCAA0D,GAA1D;AACA,eAAKhB,QAAL,CAAeD,MAAf,GAAwByD,MAAM,CAACE,WAAP,CAAoB1C,QAApB,EAAxB;AACA,eAAK2C,YAAL,CAAmBC,MAAnB,GAA6BJ,MAAM,CAACK,YAAP,IAAuB,CAApD;AACA;AAAA;AAAA,kCAAQxD,YAAR,CAAqB,KAAKJ,SAA1B,EAAsCuD,MAAM,CAACM,kBAA7C;AACA,eAAK3D,WAAL,CAAkBJ,MAAlB,GAA2B,QAAQ;AAAA;AAAA,kCAAQK,UAAR,CAAmBoD,MAAM,CAACO,eAA1B,CAAnC;AACA,kCAAAP,MAAM,CAACQ,YAAP,kCAAqBC,OAArB,CAA6BC,IAAI,IAAI;AACjC,iBAAKvC,WAAL,CAAiB,KAAKC,SAAtB,EAAkCsC,IAAI,CAACC,OAAvC,EAAiDD,IAAI,CAACE,UAAtD;AACH,WAFD;AAGA,eAAKxF,UAAL,GAAkB4E,MAAM,CAAC5E,UAAzB;AACH;;AAEW,cAANyF,MAAM,GAAkB,CAC7B;;AACY,cAAPC,OAAO,GAAkB;AAC3B;AACA,cAAI,KAAKzF,WAAT,EAAsB,KAAKA,WAAL,CAAiBkD,IAAjB;AACtB,cAAI,KAAKjD,SAAT,EAAoB,KAAKA,SAAL,CAAeiD,IAAf;AACvB;;AApLoC,O;;;;;iBAGR,I;;;;;;;iBAEK,I;;;;;;;iBAEI,I;;;;;;;iBAGb,I;;;;;;;iBAEA,I;;;;;;;iBAEC,I;;;;;;;iBAGD,I;;;;;;;iBAEO,I;;;;;;;iBAET,I;;;;;;;iBAGE,I;;;;;;;iBAEA,I;;;;;;;iBAEA,I;;;;;;;iBAGG,I;;;;;;;iBAGF,I;;;;;;;iBAGE,I;;;;;;;iBAGI,I;;;;;;;iBAGN,I", "sourcesContent": ["import { _decorator, Label, Node, ProgressBar, tween } from 'cc';\r\nimport csproto from 'db://assets/bundles/common/script/autogen/pb/cs_proto.js';\r\nimport { BaseUI, UILayer, UIMgr, UIOpt } from \"db://assets/scripts/core/base/UIMgr\";\r\nimport { MyApp } from '../../app/MyApp';\r\nimport { BundleName } from '../../const/BundleConst';\r\nimport { DataMgr } from '../../data/DataManager';\r\nimport { UITools } from '../../game/utils/UITools';\r\nimport { ButtonPlus } from './components/button/ButtonPlus';\r\nimport { StatisticsUI } from './StatisticsUI';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n\r\n@ccclass('SettlementUI')\r\nexport class SettlementUI extends BaseUI {\r\n\r\n    @property(ButtonPlus)\r\n    btnNext: ButtonPlus | null = null;\r\n    @property(ButtonPlus)\r\n    buttonDouble: ButtonPlus | null = null;\r\n    @property(ButtonPlus)\r\n    buttonStatistics: ButtonPlus | null = null;\r\n\r\n    @property(Label)\r\n    lblScore: Label | null = null;\r\n    @property(Label)\r\n    scoreAdd: Label | null = null;\r\n    @property(Label)\r\n    scoreHigh: Label | null = null;\r\n\r\n    @property(Label)\r\n    lblLevel: Label | null = null;\r\n    @property(ProgressBar)\r\n    expProBar: ProgressBar | null = null;\r\n    @property(Label)\r\n    lblExp: Label | null = null;\r\n\r\n    @property(Node)\r\n    nodeItem1: Node | null = null;\r\n    @property(Node)\r\n    nodeItem2: Node | null = null;\r\n    @property(Node)\r\n    nodeItem3: Node | null = null;\r\n\r\n    @property(Node)\r\n    nodeBestWeek: Node | null = null;\r\n\r\n    @property(Label)\r\n    passScore: Label | null = null;\r\n\r\n    @property(Label)\r\n    lblTimeCost: Label | null = null;\r\n\r\n    @property(Label)\r\n    leftDoubleTimes: Label | null = null;\r\n\r\n    @property(Node)\r\n    doubleNode: Node | null = null;\r\n\r\n    game_stats: csproto.comm.IGameStatItem[] = [];\r\n\r\n    private _scoreTween: any; // 分数动画的 tween 引用\r\n    private _expTween: any;   // 经验条动画的 tween 引用\r\n\r\n    public static getUrl(): string { return \"prefab/ui/SettlementUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Top }\r\n    public static getBundleName(): string { return BundleName.Home }\r\n    public static getUIOption(): UIOpt {\r\n        return { isClickBgCloseUI: false }\r\n    }\r\n\r\n    protected onLoad(): void {\r\n        this.btnNext!.addClick(this.onNextClick, this);\r\n        this.buttonDouble!.addClick(this.onDoubletClick, this);\r\n        this.buttonStatistics!.addClick(this.onStatisticsClick, this);\r\n\r\n        this.scoreAdd!.string = \"分数加成 \" + 123 + \"%\";\r\n        this.lblScore!.string = \"0\";\r\n        this.scoreHigh!.string = \"历史最高分 \" + 10000;\r\n        this.passScore!.string = \"50\";\r\n        this.lblTimeCost!.string = \"用时 \" + UITools.formatTime(123);\r\n        UITools.modifyNumber(this.leftDoubleTimes!, 10);\r\n\r\n        //this.doubleNode!.active = false;\r\n\r\n        const gap = 0.5;\r\n        // 分数动画\r\n        const score = 1000;\r\n        this._scoreTween = tween({ value: 0 })\r\n            .to(gap, { value: score }, {\r\n                onUpdate: (target) => {\r\n                    if (!this.lblScore || target === undefined) return;\r\n                    this.lblScore.string = Math.round(target.value).toString();\r\n                }\r\n            })\r\n            .start();\r\n\r\n        //  GM命令 //setattr xp 1000    \r\n        let curLevel = DataMgr.role.curLevel;\r\n        let curExp = DataMgr.role.curExp;\r\n        let maxExp = DataMgr.role.maxExp;\r\n        let exp = curExp / maxExp;\r\n        if (exp > 1) {\r\n            exp = 1;\r\n        }\r\n\r\n        this.expProBar!.progress = 0;\r\n        if (maxExp > 0) {\r\n            // 经验条动画\r\n            this._expTween = tween(this.expProBar!)\r\n                .to(gap, { progress: exp }, {\r\n                    onUpdate: () => {\r\n                        if (!this.expProBar) return;\r\n                        this.lblExp!.string = `${Math.round(maxExp * this.expProBar.progress)}/${maxExp}`;\r\n                    }\r\n                })\r\n                .start();\r\n        }\r\n        this.lblLevel!.string = curLevel.toString();\r\n\r\n        this.setNodeItem(this.nodeItem1!, 80000101, 100);\r\n        this.setNodeItem(this.nodeItem2!, 80100101, 0);\r\n        this.setNodeItem(this.nodeItem3!, 89999998, 200);\r\n    }\r\n\r\n    async onNextClick() {\r\n        // 停止所有动画\r\n        if (this._scoreTween) this._scoreTween.stop();\r\n        if (this._expTween) this._expTween.stop();\r\n        await UIMgr.openUI(StatisticsUI);\r\n        UIMgr.closeUI(SettlementUI);\r\n    }\r\n\r\n    async onDoubletClick() {\r\n        // 停止所有动画\r\n        if (this._scoreTween) this._scoreTween.stop();\r\n        if (this._expTween) this._expTween.stop();\r\n        await UIMgr.openUI(StatisticsUI);\r\n        UIMgr.closeUI(SettlementUI);\r\n    }\r\n\r\n    async onStatisticsClick() {\r\n        // 停止所有动画\r\n        if (this._scoreTween) this._scoreTween.stop();\r\n        if (this._expTween) this._expTween.stop();\r\n        await UIMgr.openUI(StatisticsUI);\r\n        UIMgr.closeUI(SettlementUI);\r\n    }\r\n\r\n    setNodeItem(node: Node, itemID: number, limit: number) {\r\n        let res = MyApp.lubanTables.TbResItem.get(itemID);\r\n        if (res === null) {\r\n            return;\r\n        }\r\n\r\n        let nodeQuaIcon = node!.getChildByName(\"ItemQuaIcon\");\r\n        let icon = nodeQuaIcon!.getChildByName(\"icon\");\r\n        let quaSprite = nodeQuaIcon!.getChildByName(\"QualityTypeSprite\");\r\n        let quality = res!.quality - 1;\r\n        UITools.modifyStateSprite(quaSprite!, quality);\r\n        let labelNum = node!.getChildByName(\"LabelNum\");\r\n        UITools.modifyNumber(labelNum!.getComponent(Label)!, 111);\r\n        let labelLimit = node!.getChildByName(\"LabelLimit\");\r\n        if (limit === 0) {\r\n            labelLimit!.getComponent(Label)!.string = \"\";\r\n        } else {\r\n            UITools.modifyNumber(labelLimit!.getComponent(Label)!, limit, 100);\r\n        }\r\n        const labelNumY = labelNum!.y;\r\n        const labelLimitY = labelLimit!.y;\r\n        const middleY = (labelNumY + labelLimitY) / 2;\r\n        if (labelLimit!.getComponent(Label)!.string.trim() === \"\") {\r\n            labelNum!.y = middleY;\r\n        }\r\n    }\r\n    async onShow(result: csproto.comm.IGameResult): Promise<void> {\r\n        if (result == null) return;\r\n        this.passScore!.string = result.max_levels?.toString() ?? \"0\";\r\n        this.lblScore!.string = result.total_score!.toString();\r\n        this.nodeBestWeek!.active = (result.is_week_bset == 1);\r\n        UITools.modifyNumber(this.scoreHigh!, result.history_bese_score!);\r\n        this.lblTimeCost!.string = \"用时 \" + UITools.formatTime(result.total_time_cost!);\r\n        result.reward_items?.forEach(item => {\r\n            this.setNodeItem(this.nodeItem1!, item.item_id!, item.expiration!);\r\n        });\r\n        this.game_stats = result.game_stats!;\r\n    }\r\n\r\n    async onHide(): Promise<void> {\r\n    }\r\n    async onClose(): Promise<void> {\r\n        // 停止所有动画\r\n        if (this._scoreTween) this._scoreTween.stop();\r\n        if (this._expTween) this._expTween.stop();\r\n    }\r\n}\r\n"]}
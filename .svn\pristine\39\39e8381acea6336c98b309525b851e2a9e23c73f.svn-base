[{"__type__": "cc.Prefab", "_name": "Bullet_211200202", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "Bullet_211200202", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}, {"__id__": 4}], "_prefab": {"__id__": 45}, "_lpos": {"__type__": "cc.Vec3", "x": -29.525, "y": 479.691, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "begKFdA65LzYkynT0HbkIl"}, {"__type__": "2564dArcRFKZKoo3odCQrHw", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 5}, "emitterName": "黑鸟战斗机-旋转齐射", "emitterData": {"__id__": 6}, "bulletData": {"__id__": 30}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "31k/54u8dPRZsJ/5MHRwps"}, {"__type__": "EmitterData", "isOnlyInScreen": true, "initialDelay": {"__id__": 7}, "emitDuration": {"__id__": 8}, "isPreWarm": false, "preWarmDuration": {"__id__": 9}, "preWarmEffect": null, "isLoop": true, "loopInterval": {"__id__": 10}, "emitInterval": {"__id__": 11}, "emitPower": {"__id__": 13}, "perEmitCount": {"__id__": 14}, "perEmitInterval": {"__id__": 16}, "perEmitOffsetX": {"__id__": 17}, "angle": {"__id__": 18}, "count": {"__id__": 19}, "arc": {"__id__": 20}, "radius": {"__id__": 21}, "emitEffect": null, "eventGroupData": [{"__id__": 22}]}, {"__type__": "ExpressionValue", "type": 2, "value": 2000, "expression": "2000", "values": [], "serializedProgram": null}, {"__type__": "ExpressionValue", "type": 2, "value": 7000, "expression": "7000", "values": [], "serializedProgram": null}, {"__type__": "ExpressionValue", "type": 2, "value": 0, "expression": "0", "values": [], "serializedProgram": null}, {"__type__": "ExpressionValue", "type": 2, "value": 8000, "expression": "8000", "values": [], "serializedProgram": null}, {"__type__": "ExpressionValue", "type": 2, "value": 3000, "expression": "3000", "values": [300, 300, 300, 200, 200], "serializedProgram": {"__id__": 12}}, {"__type__": "SerializableRPNProgram", "code": [0, 0, 0, 1, 19, 12, 2], "consts": [100, 200], "varNames": [], "propPaths": [], "strictVars": true}, {"__type__": "ExpressionValue", "type": 2, "value": 1, "expression": "1", "values": [], "serializedProgram": null}, {"__type__": "ExpressionValue", "type": 2, "value": 60, "expression": "60", "values": [], "serializedProgram": {"__id__": 15}}, {"__type__": "SerializableRPNProgram", "code": [0, 0, 0, 1, 19, 12, 2], "consts": [1, 3], "varNames": [], "propPaths": [], "strictVars": true}, {"__type__": "ExpressionValue", "type": 2, "value": 102, "expression": "102", "values": [], "serializedProgram": null}, {"__type__": "ExpressionValue", "type": 2, "value": 0, "expression": "0", "values": [], "serializedProgram": null}, {"__type__": "ExpressionValue", "type": 2, "value": 0, "expression": "0", "values": [], "serializedProgram": null}, {"__type__": "ExpressionValue", "type": 2, "value": 3, "expression": "3", "values": [], "serializedProgram": null}, {"__type__": "ExpressionValue", "type": 2, "value": 15, "expression": "15", "values": [], "serializedProgram": null}, {"__type__": "ExpressionValue", "type": 2, "value": 10, "expression": "10", "values": [], "serializedProgram": null}, {"__type__": "EventGroupData", "name": "", "triggerCount": -1, "conditions": [{"__id__": 23}], "actions": [{"__id__": 25}]}, {"__type__": "EventConditionData", "op": 0, "type": 13, "compareOp": 4, "targetValue": {"__id__": 24}}, {"__type__": "ExpressionValue", "type": 2, "value": 0, "expression": "0", "values": [], "serializedProgram": null}, {"__type__": "EventActionData", "name": "", "type": 13, "delay": {"__id__": 26}, "duration": {"__id__": 27}, "targetValueType": 2, "targetValue": {"__id__": 28}, "transitionDuration": {"__id__": 29}, "wrapMode": 1, "easing": 0}, {"__type__": "ExpressionValue", "type": 2, "value": 0, "expression": "0", "values": [], "serializedProgram": null}, {"__type__": "ExpressionValue", "type": 2, "value": 6000, "expression": "6000", "values": [], "serializedProgram": null}, {"__type__": "ExpressionValue", "type": 2, "value": -3600, "expression": "-3600", "values": [], "serializedProgram": null}, {"__type__": "ExpressionValue", "type": 2, "value": 6000, "expression": "6000", "values": [], "serializedProgram": null}, {"__type__": "BulletData", "prefab": {"__uuid__": "33ebf993-63d1-47cf-b113-21b161680e15", "__expectedType__": "cc.Prefab"}, "isFacingMoveDir": true, "isTrackingTarget": false, "isDestroyOutScreen": true, "isDestructive": false, "isDestructiveOnHit": false, "scale": {"__id__": 31}, "duration": {"__id__": 32}, "delayDestroy": {"__id__": 33}, "speed": {"__id__": 34}, "acceleration": {"__id__": 35}, "accelerationAngle": {"__id__": 36}, "eventGroupData": [{"__id__": 37}]}, {"__type__": "ExpressionValue", "type": 2, "value": 1, "expression": "1", "values": [], "serializedProgram": null}, {"__type__": "ExpressionValue", "type": 2, "value": 5000, "expression": "5000", "values": [], "serializedProgram": null}, {"__type__": "ExpressionValue", "type": 2, "value": 0, "expression": "0", "values": [], "serializedProgram": null}, {"__type__": "ExpressionValue", "type": 2, "value": 300, "expression": "300", "values": [], "serializedProgram": null}, {"__type__": "ExpressionValue", "type": 2, "value": 0, "expression": "0", "values": [], "serializedProgram": null}, {"__type__": "ExpressionValue", "type": 2, "value": 0, "expression": "0", "values": [], "serializedProgram": null}, {"__type__": "EventGroupData", "name": "", "triggerCount": 1, "conditions": [{"__id__": 38}], "actions": [{"__id__": 40}]}, {"__type__": "EventConditionData", "op": 0, "type": 101, "compareOp": 4, "targetValue": {"__id__": 39}}, {"__type__": "ExpressionValue", "type": 2, "value": 100, "expression": "100", "values": [], "serializedProgram": null}, {"__type__": "EventActionData", "name": "", "type": 105, "delay": {"__id__": 41}, "duration": {"__id__": 42}, "targetValueType": 1, "targetValue": {"__id__": 43}, "transitionDuration": {"__id__": 44}, "wrapMode": 0, "easing": 0}, {"__type__": "ExpressionValue", "type": 2, "value": 0, "expression": "0", "values": [], "serializedProgram": null}, {"__type__": "ExpressionValue", "type": 2, "value": 400, "expression": "400", "values": [], "serializedProgram": null}, {"__type__": "ExpressionValue", "type": 2, "value": 700, "expression": "700", "values": [], "serializedProgram": null}, {"__type__": "ExpressionValue", "type": 2, "value": 400, "expression": "400", "values": [], "serializedProgram": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f6gcZ/lXZAt4gDq6zMfn2j", "targetOverrides": null}]
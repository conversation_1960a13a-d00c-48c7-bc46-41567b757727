import { _decorator, Node } from 'cc';
import { SingletonBase } from 'db://assets/scripts/core/base/SingletonBase';
import GameMapRun from 'db://assets/bundles/common/script/game/ui/map/GameMapRun';
import { ResChapter, randStrategy } from 'db://assets/bundles/common/script/autogen/luban/schema';
import { MyApp } from 'db://assets/bundles/common/script/app/MyApp';
import { logInfo, logWarn } from 'db://assets/scripts/utils/Logger';
import { GameIns } from 'db://assets/bundles/common/script/game/GameIns';
import { GameEnum } from 'db://assets/bundles/common/script/game/const/GameEnum';

const { ccclass } = _decorator;

enum eRAND_STRATEGY {
    WEIGHT_PRUE = 1, // 纯权重随机
    WEIGHT_NO_REPEAT = 2, //权重随机，不重复
    ORDER = 3 // 按顺序
}

class MapObjectPoolManager {
    private _pools: Map<string, Map<string, Node[]>> = new Map();
    
    /**
     * 获取特定类型的节点
     * @param poolName 对象池名称（如"Backgrounds"）
     * @param prefabName 预制体名称（如"ForestBackground"）
     */
    public get(poolName: string, prefabName: string): Node | null {
        const typePool = this._pools.get(poolName);
        if (!typePool) return null;
        
        const pool = typePool.get(prefabName);
        if (pool && pool.length > 0) {
            const node = pool.pop()!;
            node.active = true;
            return node;
        }
        return null;
    }
    
    /**
     * 回收节点
     * @param poolName 对象池名称
     * @param node 要回收的节点
     */
    public put(poolName: string, node: Node): void {
        if (!node) return;
        
        const prefabName = node.name;
        
        node.active = false;
        node.removeFromParent();
        
        if (!this._pools.has(poolName)) {
            this._pools.set(poolName, new Map());
        }
        
        const typePool = this._pools.get(poolName)!;
        
        if (!typePool.has(prefabName)) {
            typePool.set(prefabName, []);
        }
        
        typePool.get(prefabName)!.push(node);
    }
    
    // 清空指定对象池
    public clearPool(poolName: string): void {
        if (this._pools.has(poolName)) {
            const typePool = this._pools.get(poolName)!;
            typePool.forEach(pool => {
                pool.forEach(node => node.destroy());
            });
            this._pools.delete(poolName);
        }
    }
    
    // 清空所有对象池
    public clearAll(): void {
        this._pools.forEach((typePool, poolName) => {
            typePool.forEach(pool => {
                pool.forEach(node => node.destroy());
            });
        });
        this._pools.clear();
    }
}

@ccclass('GameMapManager')
export class GameMapManager extends SingletonBase<GameMapManager> {

    _chapterID: number = 0;
    _chapterConfig: ResChapter | null = null;
    public get chapterConfig(): ResChapter | null { return this._chapterConfig; }
    private _levelList: number[] = []; // 随机出来的关卡列表（这个列表长度可能会大于章节表中的关卡数量）
    public get levelList(): number[] { return this._levelList; }

    private _objectPoolManager: MapObjectPoolManager = new MapObjectPoolManager();

    public get mapObjectPoolManager(): MapObjectPoolManager {
        return this._objectPoolManager;
    }

    constructor() {
        super();
    }

    start() {

    }

    async preLoad(chapterID: number) : Promise<void> {
        this._chapterID = chapterID;
        this._initLevelList();
        await GameMapRun.instance!.initData(this._levelList);
    }

    switchSectionState(state: GameEnum.eSECTION_STATE) {
        GameMapRun.instance!.switchSectionState(state);
    }

    /**
     * 更新游戏逻辑
     * @param {number} dt 每帧的时间间隔
     */
    updateGameLogic(dt: number) {
        GameMapRun.instance!.updateGameLogic(dt);
    }

    reset() {
        this._objectPoolManager.clearAll();
        GameMapRun.instance!.reset();
    }

    clear() {
        GameMapRun.instance!.clear();
    }

    async reInitLevelList() {
        this._initLevelList();
        await GameMapRun.instance!.initData(this._levelList);
        GameMapRun.instance!.switchSectionState(GameEnum.eSECTION_STATE.RESET);
    }

    // 根据策略随机出关卡列表
    private _initLevelList() {
        this._chapterConfig = MyApp.lubanTables.TbResChapter.get(this._chapterID)!;;
        if (this._chapterConfig == null) {
            logWarn("GameMapManager", `can not find chapter config by id ${this._chapterID}`);
            return;
        }

        // 随机出关卡组
        const levelGroupList = this._randomSelection(this._chapterConfig.strategyList, this._chapterConfig.levelGroupCount, this._chapterConfig.strategy);
        if (levelGroupList.length === 0) {
            logWarn('GameMapManager', " levelGroupList is null");
            return;
        }

        // 随机出关卡
        this._levelList = [];
        for (const levelGroupID of levelGroupList) {
            const levelGroupData = MyApp.lubanTables.TbResLevelGroup.get(levelGroupID);
            if (levelGroupData == null) {
                logWarn('GameMapManager', " levelGroupData is null");
                continue;
            }

            this._levelList.push(...this._randomSelection(levelGroupData.normSTList, levelGroupData.normLevelCount, levelGroupData.normLevelST));
            this._levelList.push(...this._randomSelection(levelGroupData.bossSTList, levelGroupData.bossLevelCount, levelGroupData.bossLevelST));
        }
        logInfo('GameMapManager', ` _levelList: ${this._levelList}`);
    }

    /**
     * 策略：
     * 1.严格按权重比例随机选择元素
     * 2.严格按权重比例随机选择元素，不重复
     * 3.按顺序选择元素
     * @param STList 带权重的元素数组
     * @param count 需要选择的元素数量
     * @returns 选中元素的ID数组
     */
    private _randomSelection(STList: randStrategy[], count: number, strategy: eRAND_STRATEGY): number[] {
        if (STList.length === 0 || count <= 0) return [];

        const results: number[] = [];
        if (strategy === eRAND_STRATEGY.WEIGHT_PRUE) {
            // 计算总权重
            const totalWeight = STList.reduce((sum, item) => sum + item.Weight, 0);

            // 如果所有权重都为0，则转为均匀随机
            if (totalWeight === 0) {
                for (let i = 0; i < count; i++) {
                    const randomIndex = Math.floor(GameIns.battleManager.random() * STList.length);
                    results.push(STList[randomIndex].ID);
                }
                return results;
            }

            // 严格按权重比例随机选择
            for (let i = 0; i < count; i++) {
                // 生成[0, totalWeight)区间的随机数
                const randomValue = GameIns.battleManager.random() * totalWeight;

                // 遍历查找随机数对应的元素
                let cumulativeWeight = 0;
                for (const item of STList) {
                    cumulativeWeight += item.Weight;
                    if (randomValue < cumulativeWeight) {
                        results.push(item.ID);
                        break;
                    }
                }
            }
        } else if (strategy === eRAND_STRATEGY.WEIGHT_NO_REPEAT) {
            // 计算总权重
            const totalWeight = STList.reduce((sum, item) => sum + item.Weight, 0);

            // 如果所有权重都为0，则转为均匀随机
            if (totalWeight === 0) {
                for (let i = 0; i < count; i++) {
                    let randomIndex = Math.floor(GameIns.battleManager.random() * STList.length);
                    // 避免重复选择相同的ID
                    if (i > 0 && STList[randomIndex].ID === results[i - 1]) {
                        // 如果与上一次选择的相同，选择下一个（循环）
                        randomIndex = (randomIndex + 1) % STList.length;
                    }
                    results.push(STList[randomIndex].ID);
                }
                return results;
            }

            // 创建副本以避免修改原始数据
            const tempList = [...STList];

            let lastSelectedId = -1;
            for (let i = 0; i <= count; i++) {
                // 如果上一次选择的ID存在，且它在当前列表中，调整其位置
                if (lastSelectedId !== -1) {
                    const lastSelectedIndex = tempList.findIndex(item => item.ID === lastSelectedId);
                    if (lastSelectedIndex !== -1 && lastSelectedIndex < tempList.length - 1) {
                        // 将上一次选择的ID与下一个元素交换位置
                        [tempList[lastSelectedIndex], tempList[lastSelectedIndex + 1]] =
                            [tempList[lastSelectedIndex + 1], tempList[lastSelectedIndex]];
                    }
                }

                // 生成[0, totalWeight)区间的随机数
                const randomValue = GameIns.battleManager.random() * totalWeight;

                // 遍历查找随机数对应的元素
                let cumulativeWeight = 0;
                let selectedIndex = -1;

                for (let j = 0; j < tempList.length; j++) {
                    cumulativeWeight += tempList[j].Weight;
                    if (randomValue < cumulativeWeight) {
                        selectedIndex = j;
                        break;
                    }
                }

                // 如果未找到有效索引，选择最后一个元素
                if (selectedIndex === -1) {
                    selectedIndex = tempList.length - 1;
                }

                // 获取选中的ID
                const selectedId = tempList[selectedIndex].ID;
                results.push(selectedId);

                // 更新上一次选择的ID
                lastSelectedId = selectedId;
            }
        } else if (strategy === eRAND_STRATEGY.ORDER) {
            // 按顺序选择元素，遇到ID为0时从数组开头重新开始
            let currentIndex = 0;

            for (let i = 0; i < count; i++) {
                // 如果当前元素的ID为0，则重置到数组开头
                if (STList[currentIndex].ID === 0) {
                    currentIndex = 0;

                    // 如果数组开头的元素ID也为0，则跳过所有ID为0的元素
                    while (currentIndex < STList.length && STList[currentIndex].ID === 0) {
                        currentIndex++;
                    }

                    // 如果所有元素ID都为0，则无法选择，跳出循环
                    if (currentIndex >= STList.length) {
                        break;
                    }
                }

                // 选择当前元素
                results.push(STList[currentIndex].ID);

                // 移动到下一个元素
                currentIndex++;

                // 如果到达数组末尾，回到开头
                if (currentIndex >= STList.length) {
                    currentIndex = 0;
                }
            }
        }

        return results;
    }
}



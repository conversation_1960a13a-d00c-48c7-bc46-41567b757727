import { _decorator, v3 } from 'cc';

import { Base<PERSON>, UILayer } from '../../../../../../scripts/core/base/UIMgr';
import { logDebug } from '../../../../../../scripts/utils/Logger';
import { GameIns } from '../../../game/GameIns';
import { DragButton } from '../../common/components/button/DragButton';
import { BundleName } from '../../../const/BundleConst';
const { ccclass, property } = _decorator;

@ccclass('MBoomUI')
export class MBoomUI extends BaseUI {
    public static getUrl(): string { return "prefab/MBoomUI"; }
    public static getLayer(): UILayer { return UILayer.Default }
    public static getBundleName(): string { return BundleName.GameFight }

    protected start(): void {
        this.node.position = v3(-230, -400, 0)
        this.getComponent(DragButton)!.addClick(this.onClick, this)
    }

    onClick() {
        logDebug("MBoom<PERSON>", "onClick", "aaaaaa")
        GameIns.mainPlaneManager.mainPlane?.CastSkill(1)
    }

    async onShow(): Promise<void> {
    }

    async onHide(...args: any[]): Promise<void> {
    }

    async onClose(...args: any[]): Promise<void> {
    }
}
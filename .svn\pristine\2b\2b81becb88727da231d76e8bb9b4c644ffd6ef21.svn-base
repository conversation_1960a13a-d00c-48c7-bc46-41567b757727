import { _decorator, Enum, CCFloat } from "cc";
const { ccclass, property } = _decorator;

import { eTargetValueType, EventActionBase, eWrapMode, IEventActionData } from "db://assets/bundles/common/script/game/eventgroup/IEventAction";
import { eEasing } from "db://assets/bundles/common/script/game/eventgroup/Easing";
import { ExpressionValue } from "db://assets/bundles/common/script/game/eventgroup/ExpressionValue";
import { EnemyEventGroupContext } from "../EventGroupCom";

export enum eEnemyAction {
    Speed,
    SpeedAngle,
    Acceleration,
    AccelerationAngle,
    Color_R,
    Color_G,
    Color_B,
    Scale_X,
    Scale_Y,
    TiltSpeed,
    TiltOffset,
    RotateSpeed,    // 自转速度(0-360度/秒)

    SelectLevel,    // 选中级别?
    ImmuneBulletDamage,
    ImmuneCollideDamage,
    IgnoreBullet,
    IgnoreCollide,
    ImmuneNuke,
    ImmuneActiveSkill,
    Invincible,
}

export enum eEnemyActionCn {
    速度 = eEnemyAction.Speed,
    速度角度 = eEnemyAction.SpeedAngle,
    加速度 = eEnemyAction.Acceleration,
    加速度角度 = eEnemyAction.AccelerationAngle,
    颜色R = eEnemyAction.Color_R,
    颜色G = eEnemyAction.Color_G,
    颜色B = eEnemyAction.Color_B,
    缩放_X = eEnemyAction.Scale_X,
    缩放_Y = eEnemyAction.Scale_Y,
    摆动速度 = eEnemyAction.TiltSpeed,
    摆动偏移 = eEnemyAction.TiltOffset,
    自转速度 = eEnemyAction.RotateSpeed,

    选中级别 = eEnemyAction.SelectLevel,
    免疫子弹伤害 = eEnemyAction.ImmuneBulletDamage,
    免疫撞击伤害 = eEnemyAction.ImmuneCollideDamage,
    无视子弹 = eEnemyAction.IgnoreBullet,
    无视撞击 = eEnemyAction.IgnoreCollide,
    免疫核弹 = eEnemyAction.ImmuneNuke,
    免疫主动技能 = eEnemyAction.ImmuneActiveSkill,
    无敌 = eEnemyAction.Invincible,
}

@ccclass('EnemyActionData')
export class EnemyActionData implements IEventActionData {
    @property({ displayName: '行为名称(编辑器下调试用)', editorOnly: true })
    public name: string = "";

    @property({visible:false})
    public type: eEnemyAction = eEnemyAction.Speed;
    @property({ type: Enum(eEnemyActionCn), displayName: '行为类型' })
    public get typeCn(): eEnemyActionCn { return this.type  as unknown as eEnemyActionCn; }
    public set typeCn(value: eEnemyActionCn) { this.type = value  as unknown as eEnemyAction; }

    @property({visible:false})
    public delay : ExpressionValue = new ExpressionValue('0');
    @property({ displayName: '延迟时间', tooltip: '事件触发后，延迟多少毫秒开始执行该行为' })
    public get delayStr(): number { return this.delay.value; }
    public set delayStr(value: number) { this.delay.value = value; }

    // 持续时间: 0表示立即执行
    @property({visible:false})
    public duration : ExpressionValue = new ExpressionValue('0');
    @property({ displayName: '持续时间', tooltip: '决定事件自己的生命周期' })
    public get durationStr(): number { return this.duration.value; }
    public set durationStr(value: number) { this.duration.value = value; }

    @property({ type: Enum(eTargetValueType), displayName: '目标值类型' })
    targetValueType: eTargetValueType = eTargetValueType.Absolute;

    @property({visible:false})
    public targetValue : ExpressionValue = new ExpressionValue('0');
    @property({displayName: '目标值'})
    public get targetValueStr(): string { return this.targetValue.raw; }
    public set targetValueStr(value: string) { this.targetValue.raw = value; }

    @property({visible:false})
    public transitionDuration : ExpressionValue = new ExpressionValue('1000');
    @property({ displayName: '变换到目标值所需时间' })
    public get transitionDurationStr(): number { return this.transitionDuration.value; }
    public set transitionDurationStr(value: number) { this.transitionDuration.value = value; }

    @property({ type: Enum(eWrapMode), displayName: '循环模式' })
    wrapMode: eWrapMode = eWrapMode.Once;

    @property({ type: Enum(eEasing), displayName: '缓动函数' })
    easing : eEasing = eEasing.Linear;
}

class EnemyActionBase extends EventActionBase<EnemyEventGroupContext> {
}

// TODO:
export class EnemyAction_Speed extends EnemyActionBase {
    protected resetStartValue(context: EnemyEventGroupContext): void {
        
    }

    protected onExecuteInternal(context: EnemyEventGroupContext, value: number): void {
        
    }
}
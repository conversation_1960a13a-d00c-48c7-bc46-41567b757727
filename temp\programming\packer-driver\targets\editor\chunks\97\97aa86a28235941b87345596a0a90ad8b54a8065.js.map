{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/game/WheelSpinnerUI.ts"], "names": ["_decorator", "Node", "tween", "v3", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "GameIns", "MyApp", "BundleName", "ccclass", "property", "WheelSpinnerUI", "REWARD_COUNT", "_isSpinning", "_currentRewardIndex", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getBundleName", "GameFight", "onLoad", "onShow", "resetWheel", "refreshSelectUI", "onHide", "args", "onClose", "rogueManager", "recycleRogueItems", "NodeSelect", "closeUI", "data", "getSeLectedHistory", "for<PERSON>ach", "value", "key", "config", "lubanTables", "TbResWordGroup", "get", "setRogueItem", "wordId", "NodeArrow", "angle", "onBtnStartClick", "startSpin", "targetRewardIndex", "Math", "floor", "random", "newIndex", "targetAngle", "extraRotations", "totalRotation", "to", "easing", "onComplete", "onSpinComplete", "start", "rewardIndex", "console", "log", "highlightSelectedReward", "giveReward", "NodeRewards", "rewardNodes", "children", "length", "scale"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,E,OAAAA,E;;AACzBC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;gCAGjBY,c,WADZF,OAAO,CAAC,gBAAD,C,UAGHC,QAAQ,CAACV,IAAD,C,UAERU,QAAQ,CAACV,IAAD,C,UAERU,QAAQ,CAACV,IAAD,C,2BAPb,MACaW,cADb;AAAA;AAAA,4BAC2C;AAAA;AAAA;;AAAA;;AAGN;AAHM;;AAKR;AALQ;;AASvC;AATuC,eAUtBC,YAVsB,GAUC,CAVD;AAUI;AAE3C;AAZuC,eAavCC,WAbuC,GAahB,KAbgB;AAaT;AAbS,eAcvCC,mBAduC,GAcT,CAdS;AAAA;;AAcN;AAEb,eAANC,MAAM,GAAW;AAAE,iBAAO,uBAAP;AAAiC;;AAC5C,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAyB;;AAClC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,SAAlB;AAA6B;;AAE3DC,QAAAA,MAAM,GAAS,CAExB;;AAEW,cAANC,MAAM,GAAkB;AAC1B;AACA,eAAKC,UAAL;AACA,eAAKC,eAAL;AACH;;AAEW,cAANC,MAAM,CAAC,GAAGC,IAAJ,EAAgC,CAAG;;AAElC,cAAPC,OAAO,CAAC,GAAGD,IAAJ,EAAgC;AACzC;AAAA;AAAA,kCAAQE,YAAR,CAAqBC,iBAArB,CAAuC,KAAKC,UAA5C;AACH;;AAEY,cAAPC,OAAO,GAAG;AACZ;AAAA;AAAA,8BAAMA,OAAN,CAAcnB,cAAd;AACH;;AAEDY,QAAAA,eAAe,GAAG;AACd,cAAIQ,IAAI,GAAG;AAAA;AAAA,kCAAQJ,YAAR,CAAqBK,kBAArB,EAAX;AACA;AAAA;AAAA,kCAAQL,YAAR,CAAqBC,iBAArB,CAAuC,KAAKC,UAA5C;AACAE,UAAAA,IAAI,CAACE,OAAL,CAAa,CAACC,KAAD,EAAQC,GAAR,KAAgB;AACzB,gBAAIC,MAAM,GAAG;AAAA;AAAA,gCAAMC,WAAN,CAAkBC,cAAlB,CAAiCC,GAAjC,CAAqCJ,GAArC,CAAb;AACA;AAAA;AAAA,oCAAQR,YAAR,CAAqBa,YAArB,CAAkC,KAAKX,UAAvC,EAAoDO,MAAM,CAAEK,MAA5D,EAAoEP,KAApE;AACH,WAHD;AAIH;AAED;AACJ;AACA;;;AACYZ,QAAAA,UAAU,GAAS;AACvB,cAAI,CAAC,KAAKoB,SAAV,EAAqB;AAErB,eAAK7B,WAAL,GAAmB,KAAnB;AACA,eAAK6B,SAAL,CAAeC,KAAf,GAAuB,CAAvB;AACA,eAAK7B,mBAAL,GAA2B,CAA3B;AACH;;AAED8B,QAAAA,eAAe,GAAS;AACpB,cAAI,KAAK/B,WAAT,EAAsB,OADF,CACU;;AAE9B,eAAKgC,SAAL;AACH;;AAEOA,QAAAA,SAAS,GAAS;AACtB,cAAI,CAAC,KAAKH,SAAV,EAAqB;AACrB,eAAK7B,WAAL,GAAmB,IAAnB;AAEA,gBAAMiC,iBAAiB,GAAGC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgB,KAAKrC,YAAhC,CAA1B;AACA,cAAIsC,QAAQ,GAAGJ,iBAAiB,GAAG,KAAKhC,mBAAxC;;AACA,cAAIoC,QAAQ,GAAG,CAAf,EAAiB;AACbA,YAAAA,QAAQ,IAAI,KAAKtC,YAAjB;AACH;;AACD,gBAAMuC,WAAW,GAAG,KAAKT,SAAL,CAAeC,KAAf,GAAqBO,QAAQ,GAAG,EAApD,CATsB,CAWtB;;AACA,gBAAME,cAAc,GAAG,CAAvB,CAZsB,CAYI;;AAC1B,gBAAMC,aAAa,GAAG,CAAED,cAAF,GAAmB,GAAnB,GAAyBD,WAA/C,CAbsB,CAetB;;AACAlD,UAAAA,KAAK,CAAC,KAAKyC,SAAN,CAAL,CACKY,EADL,CACQF,cAAc,GAAC,GADvB,EAC4B;AAAET,YAAAA,KAAK,EAAEU;AAAT,WAD5B,EACsD;AAC9CE,YAAAA,MAAM,EAAE,UADsC;AAC1B;AACpBC,YAAAA,UAAU,EAAE,MAAM;AACd;AACA,mBAAKC,cAAL,CAAoBX,iBAApB;AACH;AAL6C,WADtD,EAQKY,KARL;AASH;;AACOD,QAAAA,cAAc,CAACE,WAAD,EAA4B;AAC9C,eAAK9C,WAAL,GAAmB,KAAnB;AACA,eAAKC,mBAAL,GAA2B6C,WAA3B;AAEAC,UAAAA,OAAO,CAACC,GAAR,CAAa,kBAAiBF,WAAY,EAA1C,EAJ8C,CAM9C;;AACA,eAAKG,uBAAL,CAA6BH,WAA7B,EAP8C,CAS9C;;AACA,eAAKI,UAAL,CAAgBJ,WAAhB,EAV8C,CAY9C;;AACA,eAAKpC,eAAL;AACH;;AAEOuC,QAAAA,uBAAuB,CAACH,WAAD,EAA4B;AACvD,cAAI,CAAC,KAAKK,WAAV,EAAuB;AAEvB,gBAAMC,WAAW,GAAG,KAAKD,WAAL,CAAiBE,QAArC;;AACA,cAAID,WAAW,CAACE,MAAZ,GAAqBR,WAAzB,EAAsC;AAClC;AACA;AACA1D,YAAAA,KAAK,CAACgE,WAAW,CAACN,WAAD,CAAZ,CAAL,CACKL,EADL,CACQ,GADR,EACa;AAAEc,cAAAA,KAAK,EAAElE,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,CAAX;AAAX,aADb,EAEKoD,EAFL,CAEQ,GAFR,EAEa;AAAEc,cAAAA,KAAK,EAAElE,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP;AAAX,aAFb,EAGKwD,KAHL;AAIH;AACJ;;AAEOK,QAAAA,UAAU,CAACJ,WAAD,EAA4B;AAC1CC,UAAAA,OAAO,CAACC,GAAR,CAAa,SAAQF,WAAY,EAAjC;AACH;;AA5HsC,O;;;;;iBAGZ,I;;;;;;;iBAEF,I;;;;;;;iBAEC,I", "sourcesContent": ["import { _decorator, Node, tween, v3, Vec3, UIOpacity } from 'cc';\r\nimport { BaseUI, UILayer, UIMgr } from 'db://assets/scripts/core/base/UIMgr';\r\nimport { GameIns } from '../../../game/GameIns';\r\nimport { MyApp } from '../../../app/MyApp';\r\nimport { BundleName } from '../../../const/BundleConst';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass(\"WheelSpinnerUI\")\r\nexport class WheelSpinnerUI extends BaseUI {\r\n\r\n    @property(Node)\r\n    NodeRewards: Node | null = null; // 转盘奖励节点容器\r\n    @property(Node)\r\n    NodeArrow: Node | null = null; // 箭头节点，需要旋转这个\r\n    @property(Node)\r\n    NodeSelect: Node | null = null;\r\n\r\n    // 转盘配置\r\n    private readonly REWARD_COUNT: number = 6; // 奖励数量\r\n\r\n    // 转盘状态\r\n    _isSpinning: boolean = false; // 是否正在旋转\r\n    _currentRewardIndex: number = 0; // 当前奖励索引\r\n\r\n    public static getUrl(): string { return \"prefab/WheelSpinnerUI\"; };\r\n    public static getLayer(): UILayer { return UILayer.Default; }\r\n    public static getBundleName(): string { return BundleName.GameFight }\r\n\r\n    protected onLoad(): void {\r\n\r\n    }\r\n\r\n    async onShow(): Promise<void> {\r\n        // 初始化箭头位置\r\n        this.resetWheel();\r\n        this.refreshSelectUI();\r\n    }\r\n\r\n    async onHide(...args: any[]): Promise<void> { }\r\n\r\n    async onClose(...args: any[]): Promise<void> {\r\n        GameIns.rogueManager.recycleRogueItems(this.NodeSelect!);\r\n    }\r\n\r\n    async closeUI() {\r\n        UIMgr.closeUI(WheelSpinnerUI);\r\n    }\r\n\r\n    refreshSelectUI() {\r\n        let data = GameIns.rogueManager.getSeLectedHistory();\r\n        GameIns.rogueManager.recycleRogueItems(this.NodeSelect!);\r\n        data.forEach((value, key) => {\r\n            let config = MyApp.lubanTables.TbResWordGroup.get(key);\r\n            GameIns.rogueManager.setRogueItem(this.NodeSelect!, config!.wordId, value);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 初始化箭头位置\r\n     */\r\n    private resetWheel(): void {\r\n        if (!this.NodeArrow) return;\r\n        \r\n        this._isSpinning = false;\r\n        this.NodeArrow.angle = 3;\r\n        this._currentRewardIndex = 0;\r\n    }\r\n\r\n    onBtnStartClick(): void {\r\n        if (this._isSpinning) return; // 如果正在旋转，则忽略点击\r\n        \r\n        this.startSpin();\r\n    }\r\n\r\n    private startSpin(): void {\r\n        if (!this.NodeArrow) return;\r\n        this._isSpinning = true;\r\n\r\n        const targetRewardIndex = Math.floor(Math.random() * this.REWARD_COUNT);\r\n        let newIndex = targetRewardIndex - this._currentRewardIndex;\r\n        if (newIndex < 0){\r\n            newIndex += this.REWARD_COUNT;\r\n        }\r\n        const targetAngle = this.NodeArrow.angle-newIndex * 60;\r\n        \r\n        // 为了增加动画效果，让箭头多转几圈\r\n        const extraRotations = 3; // 额外旋转圈数\r\n        const totalRotation = - extraRotations * 360 + targetAngle;\r\n\r\n        // 使用缓动动画实现箭头旋转\r\n        tween(this.NodeArrow)\r\n            .to(extraRotations*0.8, { angle: totalRotation }, {\r\n                easing: \"cubicOut\", // 缓动函数，先快后慢\r\n                onComplete: () => {\r\n                    // 旋转完成\r\n                    this.onSpinComplete(targetRewardIndex);\r\n                }\r\n            })\r\n            .start();\r\n    }\r\n    private onSpinComplete(rewardIndex: number): void {\r\n        this._isSpinning = false;\r\n        this._currentRewardIndex = rewardIndex;\r\n\r\n        console.log(`转盘抽奖完成，选中奖励索引: ${rewardIndex}`);\r\n        \r\n        // 这里可以添加选中效果，比如高亮显示对应的奖励\r\n        this.highlightSelectedReward(rewardIndex);\r\n        \r\n        // 发放奖励\r\n        this.giveReward(rewardIndex);\r\n        \r\n        // 刷新选择UI\r\n        this.refreshSelectUI();\r\n    }\r\n\r\n    private highlightSelectedReward(rewardIndex: number): void {\r\n        if (!this.NodeRewards) return;\r\n        \r\n        const rewardNodes = this.NodeRewards.children;\r\n        if (rewardNodes.length > rewardIndex) {\r\n            // 这里可以添加高亮效果，比如改变颜色、缩放等\r\n            // 示例：简单的缩放效果\r\n            tween(rewardNodes[rewardIndex])\r\n                .to(0.2, { scale: v3(1.2, 1.2, 1) })\r\n                .to(0.2, { scale: v3(1, 1, 1) })\r\n                .start();\r\n        }\r\n    }\r\n\r\n    private giveReward(rewardIndex: number): void {\r\n        console.log(`发放奖励: ${rewardIndex}`);\r\n    }\r\n}"]}
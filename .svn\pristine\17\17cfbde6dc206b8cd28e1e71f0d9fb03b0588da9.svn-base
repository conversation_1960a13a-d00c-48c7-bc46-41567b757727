import { LevelDataEventCondtion, LevelDataEventCondtionComb, LevelDataEventCondtionType } from "./LevelDataEventCondtion";
import { LevelDataEventCondtionDelayDistance } from "./LevelDataEventCondtionDelayDistance";
import { LevelDataEventCondtionDelayTime } from "./LevelDataEventCondtionDelayTime";
import { LevelDataEventCondtionWave } from "./LevelDataEventCondtionWave";

export function newCondition(obj: {_type: LevelDataEventCondtionType, comb: LevelDataEventCondtionComb}): LevelDataEventCondtion {
        switch(obj._type) {
            case LevelDataEventCondtionType.DelayTime:
                return Object.assign(new LevelDataEventCondtionDelayTime(obj.comb), obj)
            case LevelDataEventCondtionType.DelayDistance:
                return Object.assign(new LevelDataEventCondtionDelayDistance(obj.comb), obj)
            case LevelDataEventCondtionType.Wave:
                return Object.assign(new LevelDataEventCondtionWave(obj.comb), obj)
            default:
                return Object.assign(new LevelDataEventCondtionDelayTime(obj.comb), obj)
        }
    }
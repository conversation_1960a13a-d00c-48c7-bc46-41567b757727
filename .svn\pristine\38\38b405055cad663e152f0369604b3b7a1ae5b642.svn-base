import { _decorator, CCBoolean, CCFloat, Sprite, Component, Node, CCObject, Vec2, assetManager, ImageAsset, SpriteFrame } from 'cc';
import { WaveData, FormationGroup, FormationPoint, eSpawnOrder, eWaveCompletion } from '../data/WaveData';
import { PathData } from '../data/PathData';
import { GameIns } from 'db://assets/bundles/common/script/game/GameIns';
import { WaveEventGroup, WaveEventGroupContext } from './WaveEventGroup';
import EnemyPlane from 'db://assets/bundles/common/script/game/ui/plane/enemy/EnemyPlane';
import PlaneBase from 'db://assets/bundles/common/script/game/ui/plane/PlaneBase';
import { PlaneEventType } from 'db://assets/bundles/common/script/game/ui/plane/event/PlaneEventType';
const { ccclass, property, executeInEditMode, menu } = _decorator;

@ccclass('Wave')
@menu("怪物/波次")
@executeInEditMode()
export class Wave extends Component {
    @property({displayName: '名称', editorOnly: true})
    waveName: string = '';                // 备注(策划用)
    @property({type: SpriteFrame, displayName: '显示图片(仅编辑器)', editorOnly: true})
    waveIcon: SpriteFrame|null = null;

    @property({type:WaveData})
    readonly waveData: WaveData = new WaveData();

    /*
     * 以下是新的wave逻辑, 旧的逻辑主要在WaveManager与EnemyWave
     */
    private _isSpawnCompleted: boolean = false;
    public get isSpawnCompleted() { return this._isSpawnCompleted; }
    private _isAllEnemyDead: boolean = false;
    public get isAllEnemyDead() { return this._isAllEnemyDead; }
    
    private _waveElapsedTime: number = 0;
    public get waveElapsedTime() { return this._waveElapsedTime; }
    private _nextSpawnTime: number = 0;
    private _totalWeight: number = 0;
    // 这个参数可能是时间，也可能是数量，取决于waveData.waveCompletion
    private _waveCompleteParam: number = 0;
    private _spawnIndex: number = 0;

    // 用在waveCompletion == SpawnCount时的队列
    private _spawnQueue: number[] = [];
    private _randomRepeatPlaneID: number = 0;
    // 当前wave的起点位置
    private _originX: number = 0;
    private _originY: number = 0;
    private _formationGroup: FormationGroup|null = null;
    public get formationGroup(): FormationGroup|null { return this._formationGroup; }
    private _path: PathData|null = null;
    public get path(): PathData|null { return this._path; }
    // 事件组
    private _eventGroups: WaveEventGroup[] = [];
    private _eventGroupContext: WaveEventGroupContext|null = null;
    // 怪物
    private _enemyCreated: EnemyPlane[] = [];

    onLoad() {
        if (this.waveData) {
            if (this.waveData.spawnOrder === eSpawnOrder.Random || this.waveData.spawnOrder === eSpawnOrder.RandomRepeat) {
                this._totalWeight = 0;
                // add up _totalWeight if is random
                this.waveData.spawnGroups.forEach((group) => {
                    this._totalWeight += group.weight;
                    group.selfWeight = this._totalWeight;
                });
            }
            
            if (this.waveData.eventGroupData) {
                if (!this._eventGroupContext) {
                    this._eventGroupContext = new WaveEventGroupContext();
                }
                this.waveData.eventGroupData.forEach((groupData) => {
                    const group = new WaveEventGroup(this._eventGroupContext!, groupData);
                    this._eventGroups.push(group);
                });
            }

            if (this.waveData.formationAsset) {
                this._formationGroup = new FormationGroup();
                Object.assign(this._formationGroup, this.waveData.formationAsset.json);
            }

            if (this.waveData.pathAsset) {
                this._path = PathData.fromJSON(this.waveData.pathAsset.json);
            }
        }
    }

    private reset() {
        this._isSpawnCompleted = false;
        this._isAllEnemyDead = false;
        this._waveElapsedTime = 0;
        this._nextSpawnTime = 0;
        this._spawnIndex = 0;
        this._randomRepeatPlaneID = 0;
        this._spawnQueue.length = 0;
        this._eventGroupContext && (this._eventGroupContext!.reset());
        
        this._eventGroups.forEach((group) => {
            group.reset();
        });

        if (this._enemyCreated.length > 0) {
            this._enemyCreated.length = 0;
        }
    }

    static random(): number {
        if (GameIns.battleManager) {
            return GameIns.battleManager.random();
        }

        return Math.random();
    }

    trigger(x: number, y: number) {
        this.reset();
        this._originX = x;
        this._originY = y;

        // console.log('wave triggered at x: ' + x + ', y: ' + y);

        // 对于固定数量的波次，可以预先生成队列，每次从里面取即可
        if (this.waveData) {
            this._waveCompleteParam = this.waveData.waveCompletionParam.eval();
            
            // 如果是RandomRepeat类型(随机一次,然后重复),我们先把这个飞机ID摇出来
            if (this.waveData.spawnOrder === eSpawnOrder.RandomRepeat) {
                const randomWeight = Wave.random() * this._totalWeight;
                for (const group of this.waveData.spawnGroups) {
                    if (randomWeight <= group.selfWeight) {
                        this._randomRepeatPlaneID = group.planeID;
                        break;
                    }
                }
            }

            if (this.waveData.waveCompletion === eWaveCompletion.SpawnCount) {
                if (this.waveData.spawnOrder === eSpawnOrder.Random) {
                    for (let i = 0; i < this._waveCompleteParam; i++) {
                        const randomWeight = Wave.random() * this._totalWeight;
                        for (const group of this.waveData.spawnGroups) {
                            if (randomWeight <= group.selfWeight) {
                                this._spawnQueue.push(group.planeID);
                                break;
                            }
                        }
                    }
                } 
                else if (this.waveData.spawnOrder === eSpawnOrder.Sequential) {
                    for (let i = 0; i < this._waveCompleteParam; i++) {
                        // 通过取余实现循环
                        this._spawnQueue.push(this.waveData.spawnGroups[i % this.waveData.spawnGroups.length].planeID);
                    }
                }
                else {
                    for (let i = 0; i < this._waveCompleteParam; i++) {
                        this._spawnQueue.push(this._randomRepeatPlaneID);
                    }
                }
            }

            // 编辑器下预览这里可能为空
            if (!this._formationGroup && this.waveData.formationAsset) {
                this._formationGroup = new FormationGroup();
                Object.assign(this._formationGroup, this.waveData.formationAsset.json);
            }

            if (!this._path && this.waveData.pathAsset) {
                this._path = PathData.fromJSON(this.waveData.pathAsset.json);
            }
        }

        this._eventGroupContext && (this._eventGroupContext!.wave = this);
        this._eventGroups.forEach((group) => {
            group.tryStart();
        });
    }

    // tick wave
    tick(dtInMiliseconds: number) {
        if (!this._isSpawnCompleted) {
            this.tickSpawn(dtInMiliseconds);
        }
    }
    
    private tickSpawn(dtInMiliseconds: number) {
        this._waveElapsedTime += dtInMiliseconds;
        if (this.waveData.waveCompletion === eWaveCompletion.SpawnCount) {
            // 产出固定数量的波次
            if (this._spawnIndex >= this._waveCompleteParam) {
                this._isSpawnCompleted = true;
            } else {
                if (this._waveElapsedTime >= this._nextSpawnTime) {
                    this.spawnFromQueue();
                }
            }
        } else {
            // 完全根据时间的波次
            if (this._waveElapsedTime >= this._waveCompleteParam) {
                this._isSpawnCompleted = true;
            } else {
                if (this._waveElapsedTime >= this._nextSpawnTime) {
                    this.spawnFromGroup();
                }
            }
        }

        if (this._eventGroups.length > 0) {
            for (let i = 0; i < this._eventGroups.length; i++) {
                this._eventGroups[i].tick(dtInMiliseconds);
            }
        }
    }

    private spawnFromQueue(): void {        
        this.spawnSingleFromQueue(this._spawnIndex % this._spawnQueue.length);
        this._nextSpawnTime = this._waveElapsedTime + this.waveData.spawnInterval.eval();
    }

    private spawnSingleFromQueue(index: number): void {
        if (index >= this._spawnQueue.length) {
            return;
        }

        let spawnOffset = this.waveData.spawnPosOffset;
        let spawnAngle = this.waveData.spawnAngle.eval();
        // let spawnSpeed = this.waveData.spawnSpeed.eval();

        this.createPlane(this._spawnQueue[index], spawnOffset, spawnAngle);
    }

    private async spawnFromGroup(): Promise<void> {
        this._nextSpawnTime = this._waveElapsedTime + this.waveData.spawnInterval.eval();

        let spawnOffset = this.waveData.spawnPosOffset;
        let spawnAngle = this.waveData.spawnAngle.eval();

        if (this.waveData.spawnOrder === eSpawnOrder.Random) {
            const randomWeight = Wave.random() * this._totalWeight;
            for (const group of this.waveData.spawnGroups) {
                if (randomWeight <= group.selfWeight) {
                    await this.createPlane(group.planeID, spawnOffset, spawnAngle);
                    break;
                }
            }
        } else if (this.waveData.spawnOrder === eSpawnOrder.RandomRepeat) {
            await this.createPlane(this._randomRepeatPlaneID, spawnOffset, spawnAngle);
        } else {
            await this.createPlane(this.waveData.spawnGroups[this._spawnIndex % this.waveData.spawnGroups.length].planeID, spawnOffset, spawnAngle);
        }
    }

    private async createPlane(planeId: number, offset: Vec2, angle: number) {
        let origin: Vec2 = new Vec2(this._originX, this._originY);
        if (this._path) {
            // 如果有路径的情况下, 选择路径起点作为原点
            origin = this._path.points[this._path.startIdx].position;
        }

        // 获取阵型数据
        if (this._formationGroup) {
            const point = this._formationGroup.points[this._spawnIndex % this._formationGroup.points.length];
            offset.x += point.x;
            offset.y += point.y;
        }

        origin.x += offset.x;
        origin.y += offset.y;

        this._spawnIndex++;
        if (this._createPlaneDelegate) {
            this._createPlaneDelegate(planeId, this._path ? offset : origin, angle);
            return;
        }

        // console.log(`createPlane: ${planeId}, ${offset.x}, ${offset.y}, ${angle}`);
        let enemy = await GameIns.enemyManager.addPlane(planeId);
        if (enemy) {
            if (this._path) {
                // 注意: Path已经包含了origin, 因此这里我们只传入origin
                enemy.initPath(offset.x, offset.y, this._path);
            } else {
                enemy.initMove(origin.x, origin.y, angle);
            }
            enemy.PlaneEventRegister(PlaneEventType.Die, this.onEnemyDie.bind(this));
            this._enemyCreated.push(enemy); // 记录下来，方便后面判断是否都死了
        }
    }

    private onEnemyDie(plane: EnemyPlane) {
        plane.PlaneEventUnRegister(PlaneEventType.Die, this.onEnemyDie.bind(this));
        const index = this._enemyCreated.indexOf(plane);
        if (index !== -1) {
            this._enemyCreated.splice(index, 1);

            if (this._enemyCreated.length === 0 && this._isSpawnCompleted) {
                this._isAllEnemyDead = true;
            }
        }
    }

    // 以下几个函数是为了给编辑器预览用
    private _createPlaneDelegate: ((planeId: number, pos: Vec2, angle: number) => void)|null = null;
    public setCreatePlaneDelegate(func: (planeId: number, pos: Vec2, angle: number) => void) {
        this._createPlaneDelegate = func;
    }

    public setupInEditor() {
        // create node and infos for preview
        let node = this.node.getChildByName('WavePreview');
        if (!node) {
            node = new Node('WavePreview');
            node.parent = this.node;
            node.addComponent('WavePreview');
            node.hideFlags = CCObject.Flags.AllHideMasks;
            node.setPosition(0, 0, 0);
        }
        // const thisPos = this.node.position;
        // node.setWorldPosition(750/2 + thisPos.x, 1334/2 + thisPos.y, 0);

        let spriteNode = this.node.getChildByName('Visual');
        if (!spriteNode) {
            spriteNode = new Node('Visual');
            spriteNode.parent = this.node;
            spriteNode.hideFlags = CCObject.Flags.AllHideMasks;
            
            if (this.waveData.pathAsset && !this._path) {
                this._path = PathData.fromJSON(this.waveData.pathAsset.json);
            }
            if (this._path) {
                const startPoint = this._path.getStartPoint();
                if (startPoint) {
                    spriteNode.setPosition(startPoint.x, startPoint.y, 0);
                }
            }
        }

        const sprite = spriteNode.getComponent(Sprite) || spriteNode.addComponent(Sprite);
        if (this.waveIcon) {
            sprite.spriteFrame = this.waveIcon;
        } else {
            // 写死的uuid, 编辑器用, 对应: db://assets/resources/game/level/background/Texture/128.png
            assetManager.loadAny<ImageAsset>('707afdf7-8acc-4d9b-9afb-ed259ef05bdc', 
                (err:Error|null, asset: ImageAsset) => {
                    if (err) {
                        console.warn(err);
                        return;
                    }

                    sprite.spriteFrame = SpriteFrame.createWithImage(asset);
                });
        }            
    }
}
import { _decorator } from 'cc';
import { BundleName } from 'db://assets/bundles/common/script/const/BundleConst';
import { BaseUI, UILayer, UIMgr, UIOpt } from 'db://assets/scripts/core/base/UIMgr';
import { ButtonPlus } from '../common/components/button/ButtonPlus';
const { ccclass, property } = _decorator;

@ccclass('PKReconnectUI')
export class PKReconnectUI extends BaseUI {

    @property(ButtonPlus)
    btnSettle: ButtonPlus | null = null;
    @property(ButtonPlus)
    btnContinue: ButtonPlus | null = null;

    public static getUrl(): string { return "prefab/ui/PKReconnectUI"; }
    public static getLayer(): UILayer { return UILayer.Default }
    public static getBundleName(): string { return BundleName.HomePK; }
    public static getUIOption(): UIOpt { return { isClickBgCloseUI: true } }
    protected onLoad(): void {
        this.btnSettle!.addClick(this.onSettleClick, this);
        this.btnContinue!.addClick(this.onContinueClick, this);
    }
    onSettleClick() {

    }
    onContinueClick() {

    }
    async closeUI() {
        UIMgr.closeUI(PKReconnectUI);
    }
    async onShow(): Promise<void> {

    }
    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {
    }
    protected onDestroy(): void {

    }
}



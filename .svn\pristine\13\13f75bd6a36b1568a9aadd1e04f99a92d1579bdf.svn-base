import { _decorator, AudioClip, AudioSource, clamp01, Node, resources } from "cc";
import { MyApp } from 'db://assets/bundles/common/script/app/MyApp';
import { RootPersist } from "db://assets/scripts/resupdate/RootPersist";
import { IMgr } from '../../../../scripts/core/base/IMgr';
const { ccclass, property } = _decorator;

export class audioManager extends IMgr {

    private static _instance: audioManager;

    static get instance() {
        if (this._instance) {
            return this._instance;
        }

        this._instance = new audioManager();
        return this._instance;
    }

    private _audioSource: AudioSource | null = null;
    private _soundSourcePool: AudioSource[] = [];

    // 音乐音量
    private _musicVolume: number = 1;
    // 音效音量(一般两者会区分开)
    private _soundVolume: number = 1;
    // 音效的index
    private _soundSourceIndex: number = 0;

    private static readonly musicRootPath: string = 'audio/music/';
    private static readonly soundRootPath: string = 'audio/sound/'

    /**管理器初始化*/
    init() {
        var audioNode = RootPersist.instance!.node;
        // TODO: 从本地序列化保存的数据里读取this._musicVolume
        this._musicVolume = 1;
        this._soundVolume = 1;
        this._audioSource = this.createAudioSource(audioNode, this._musicVolume);

        // 默认创建5个source来播放音效
        for (let i = 0; i < 5; i++) {
            this._soundSourcePool.push(this.createAudioSource(audioNode, this._soundVolume));
        }
    }

    private createAudioSource(node: Node, volume: number): AudioSource {
        const source = node.addComponent(AudioSource);
        source.loop = false;
        source.playOnAwake = false;
        source.volume = volume;
        return source;
    }

    /**
     * 播放音乐
     * @param {Boolean} loop 是否循环播放
     */
    playMusic(name: string, loop: boolean = true) {
        if (!this._audioSource) return;

        const clip_path = audioManager.musicRootPath + name;
        // MyApp.resMgr.load(MyApp.resMgr.defaultBundleName, clip_path, AudioClip,
        resources.load(clip_path, AudioClip,
            (error: any, clip: AudioClip) => {
                if (error) {
                    console.error("audioManager load AudioClip err", error);
                    return;
                }

                this._audioSource!.stop();
                this._audioSource!.clip = clip!;
                this._audioSource!.loop = loop;
                this._audioSource!.volume = this._musicVolume;
                this._audioSource!.play();
            });
    }

    pauseMusic() {
        if (this._audioSource && this._audioSource.playing) {
            this._audioSource.pause();
        }
    }

    resumeMusic() {
        if (this._audioSource && !this._audioSource.playing) {
            this._audioSource.play();
        }
    }

    /**
     * 播放音效
     * @param {audioName} audioClip 音效名称
     * @param {Number} volumeScale 播放音量倍数
     */
    playSound(audioName: string, volumeScale: number = 1) {
        const clip_path = audioManager.soundRootPath + audioName;
        MyApp.resMgr.load(MyApp.resMgr.defaultBundleName, clip_path, AudioClip,
            (error: any, clip: AudioClip) => {
                if (error) {
                    console.error("audioManager load AudioClip err", error);
                    return;
                }
                const source = this.getNextSoundSource();
                source.playOneShot(clip!, this._soundVolume * volumeScale);
            });
    }

    private getNextSoundSource(): AudioSource {
        const source = this._soundSourcePool[this._soundSourceIndex];
        this._soundSourceIndex = (this._soundSourceIndex + 1) % this._soundSourcePool.length;
        return source;
    }

    // 设置音乐音量
    public setMusicVolume(flag: number) {
        const audioSource = this._audioSource!;
        if (!audioSource) {
            return;
        }

        this._musicVolume = clamp01(flag);
        audioSource.volume = this._musicVolume;
    }

    public getMusicVolume(): number {
        return this._musicVolume;
    }

    // 设置音效音量
    public setSoundVolume(flag: number) {
        this._soundVolume = clamp01(flag);
        this._soundSourcePool.forEach((audioSource) => {
            audioSource.volume = this._soundVolume;
        });
    }

    public getSoundVolume(): number {
        return this._soundVolume;
    }
}
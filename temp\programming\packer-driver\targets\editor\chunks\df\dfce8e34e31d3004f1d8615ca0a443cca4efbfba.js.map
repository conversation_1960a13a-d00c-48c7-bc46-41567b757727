{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/weapon/Weapon.ts"], "names": ["_decorator", "instantiate", "Node", "Prefab", "Enum", "CCInteger", "eEmitterStatus", "Emitter", "Entity", "MyApp", "EmitterEnum", "ccclass", "property", "eWeaponUseCond", "Weapon", "type", "displayName", "visible", "useCondition", "DelayTime", "WeaponDestroyed", "m_emitterConfig", "undefined", "m_emitterPrefab", "m_state", "eWeaponState", "None", "m_stateElapsedTime", "m_target", "m_owner", "m_targetWeapons", "m_emitter", "init", "node", "getComponentsInChildren", "initEmitter", "loadEmitterByID", "changeStatus", "Prewarm", "emitterID", "GetInstance", "lubanMgr", "lubanTables", "TbResEmitter", "get", "resMgr", "load", "prefab", "error", "console", "createEmitter", "<PERSON><PERSON><PERSON><PERSON>", "setPosition", "getComponent", "warn", "setEntity", "setIsActive", "state", "value", "<PERSON><PERSON><PERSON><PERSON>", "owner", "<PERSON><PERSON><PERSON><PERSON>", "target", "Immediate", "activate", "updateGameLogic", "dt", "Aiming", "tickAiming", "Emitting", "tickNone", "aimingTime", "turn<PERSON>o<PERSON>arget", "tickEmitting", "weapon", "isDead", "delayTime", "ownerNode", "targetNode", "angle", "targetAngle", "Math", "atan2", "worldPosition", "y", "x", "PI", "deltaAngle", "max<PERSON><PERSON><PERSON>", "angleSpeed", "abs", "sign"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,W,OAAAA,W;AAAwBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,S,OAAAA,S;;AAGxDC,MAAAA,c,iBAAAA,c;AAAgBC,MAAAA,O,iBAAAA,O;;AAClBC,MAAAA,M;;AAEEC,MAAAA,K,iBAAAA,K;;AAEAC,MAAAA,W,iBAAAA,W;;;;;;;;;OAPH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBZ,U;;gCASlBa,c,0BAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;eAAAA,c;;;;;;;;AAcZ;AACA;AACA;;;wBAEaC,M,WADZH,OAAO,CAAC,QAAD,C,UAEHC,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAEV,SAAR;AAAmBW,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,UAERJ,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAEV,SAAR;AAAmBW,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,UAERJ,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAEX,IAAI,CAACS,cAAD,CAAZ;AAA8BG,QAAAA,WAAW,EAAE;AAA3C,OAAD,C,UAERJ,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAEV,SAAR;AAAmBW,QAAAA,WAAW,EAAE,YAAhC;;AACNC,QAAAA,OAAO,GAAG;AACN;AACA,iBAAO,KAAKC,YAAL,KAAsBL,cAAc,CAACM,SAA5C;AACH;;AAJK,OAAD,C,UASRP,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAE,CAACb,IAAD,CAAR;AAAgBc,QAAAA,WAAW,EAAE,MAA7B;;AACNC,QAAAA,OAAO,GAAG;AACN;AACA,iBAAO,KAAKC,YAAL,KAAsBL,cAAc,CAACO,eAA5C;AACH;;AAJK,OAAD,C,UAQRR,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAEX,IAAI;AAAA;AAAA,uCAAZ;AAA2BY,QAAAA,WAAW,EAAE;AAAxC,OAAD,C,2BAzBb,MACaF,MADb;AAAA;AAAA,4BACmC;AAAA;AAAA;;AAAA;;AAEU;AAFV;;AAIY;AAJZ;;AAAA;;AAaQ;AAEvC;AAf+B;;AAAA;;AAAA,eA2BvBO,eA3BuB,GA2BmBC,SA3BnB;AAAA,eA4BvBC,eA5BuB,GA4BU,IA5BV;AA4BgB;AA5BhB,eA8BvBC,OA9BuB,GA8BCC,YAAY,CAACC,IA9Bd;AAAA,eA+BvBC,kBA/BuB,GA+BM,CA/BN;AA+BS;AA/BT,eAgCvBC,QAhCuB,GAgCG,IAhCH;AAgCS;AAhCT,eAiCvBC,OAjCuB,GAiCE,IAjCF;AAiCS;AAjCT,eAkCvBC,eAlCuB,GAkCK,EAlCL;AAAA,eAmCvBC,SAnCuB,GAmCK,IAnCL;AAAA;;AAqC/BC,QAAAA,IAAI,GAAG;AACH,eAAKR,OAAL,GAAeC,YAAY,CAACC,IAA5B,CADG,CAEH;AACA;AACA;;AACA,cAAI,KAAKR,YAAL,KAAsBL,cAAc,CAACO,eAAzC,EAA0D;AACtD,iBAAKU,eAAL,GAAuB,KAAKG,IAAL,CAAUC,uBAAV,CAAkCpB,MAAlC,CAAvB;AACH;AACJ;;AAEOqB,QAAAA,WAAW,GAAG;AAClB,cAAI,CAAC,KAAKJ,SAAV,EAAqB;AACjB,iBAAKK,eAAL;AACH,WAFD,MAEO;AAAA;;AACH;AACA,oCAAKL,SAAL,6BAAgBM,YAAhB,CAA6B;AAAA;AAAA,kDAAeC,OAA5C;AACH;AACJ;;AAEOF,QAAAA,eAAe,GAAG;AACtB,cAAI,KAAKG,SAAL,GAAiB,CAAjB,IAAsB;AAAA;AAAA,8BAAMC,WAAN,EAAtB,IAA6C;AAAA;AAAA,8BAAMC,QAAvD,EAAiE;AAC7D,iBAAKpB,eAAL,GAAuB;AAAA;AAAA,gCAAMqB,WAAN,CAAkBC,YAAlB,CAA+BC,GAA/B,CAAmC,KAAKL,SAAxC,CAAvB;;AACA,gBAAI,KAAKlB,eAAT,EAA0B;AACtB;AAAA;AAAA,kCAAMwB,MAAN,CAAaC,IAAb,CAAkB,KAAKzB,eAAL,CAAqB0B,MAAvC,EAA+C5C,MAA/C,EAAuD,CAAC6C,KAAD,EAAaD,MAAb,KAAgC;AACnF,oBAAIC,KAAJ,EAAW;AACPC,kBAAAA,OAAO,CAACD,KAAR,CAAc,gCAAd,EAAgDA,KAAhD;AACA;AACH;;AAED,qBAAKzB,eAAL,GAAuBwB,MAAvB;AACA,qBAAKG,aAAL;AACH,eARD;AASH,aAVD,MAUO;AACHD,cAAAA,OAAO,CAACD,KAAR,CAAc,4CAAd;AACH;AACJ,WAfD,MAeO;AACHC,YAAAA,OAAO,CAACD,KAAR,CAAc,wCAAd;AACH;AACJ;;AAEOE,QAAAA,aAAa,GAAG;AACpB,cAAI,KAAK3B,eAAT,EAA0B;AACtB,kBAAMU,IAAI,GAAGhC,WAAW,CAAC,KAAKsB,eAAN,CAAxB;AACA,iBAAKU,IAAL,CAAUkB,QAAV,CAAmBlB,IAAnB;AACAA,YAAAA,IAAI,CAACmB,WAAL,CAAiB,CAAjB,EAAoB,CAApB;AACA,iBAAKrB,SAAL,GAAiBE,IAAI,CAACoB,YAAL;AAAA;AAAA,mCAAjB;;AACA,gBAAI,CAAC,KAAKtB,SAAV,EAAqB;AACjBkB,cAAAA,OAAO,CAACK,IAAR,CAAa,iDAAb;AACA;AACH;;AAED,iBAAKvB,SAAL,CAAewB,SAAf,CAAyB,KAAK1B,OAA9B;AACA,iBAAKE,SAAL,CAAeyB,WAAf,CAA2B,IAA3B;AACH;AACJ;;AAEe,YAALC,KAAK,GAAiB;AAC7B,iBAAO,KAAKjC,OAAZ;AACH;;AAEe,YAALiC,KAAK,CAACC,KAAD,EAAsB;AAClC,cAAI,KAAKlC,OAAL,KAAiBkC,KAArB,EAA4B;AACxB;AACH;;AAED,eAAKlC,OAAL,GAAekC,KAAf;AACA,eAAK/B,kBAAL,GAA0B,CAA1B;AACH;;AAEMgC,QAAAA,QAAQ,CAACC,KAAD,EAAwB;AACnC,eAAK/B,OAAL,GAAe+B,KAAf;AACA,iBAAO,IAAP;AACH;;AAEMC,QAAAA,SAAS,CAACC,MAAD,EAAgC;AAC5C,eAAKlC,QAAL,GAAgBkC,MAAhB;;AACA,cAAI,KAAK5C,YAAL,KAAsBL,cAAc,CAACkD,SAAzC,EAAoD;AAChD,iBAAKC,QAAL;AACH;;AAED,iBAAO,IAAP;AACH;;AAEMC,QAAAA,eAAe,CAACC,EAAD,EAAa;AAC/B,eAAKvC,kBAAL,IAA2BuC,EAA3B;;AACA,kBAAQ,KAAK1C,OAAb;AACI,iBAAKC,YAAY,CAAC0C,MAAlB;AACI,mBAAKC,UAAL,CAAgBF,EAAhB;AACA;;AACJ,iBAAKzC,YAAY,CAAC4C,QAAlB;AACI;AACA;;AACJ,iBAAK5C,YAAY,CAACC,IAAlB;AACI,mBAAK4C,QAAL,CAAcJ,EAAd;AACA;AATR;AAWH,SArI8B,CAuI/B;;;AACQF,QAAAA,QAAQ,GAAG;AACf,eAAKP,KAAL,GAAa,KAAKc,UAAL,GAAkB,CAAlB,GAAsB9C,YAAY,CAAC0C,MAAnC,GAA4C1C,YAAY,CAAC4C,QAAtE;AACA,eAAKlC,WAAL;AACH;;AAEOiC,QAAAA,UAAU,CAACF,EAAD,EAAa;AAC3B,cAAI,KAAKvC,kBAAL,IAA2B,KAAK4C,UAApC,EAAgD;AAC5C,iBAAKd,KAAL,GAAahC,YAAY,CAAC4C,QAA1B;AACH;;AAED,eAAKG,YAAL,CAAkBN,EAAlB;AACH;;AAEOO,QAAAA,YAAY,CAACP,EAAD,EAAa,CAC7B;AACA;AACA;AACH;;AAEOI,QAAAA,QAAQ,CAACJ,EAAD,EAAa;AACzB,cAAI,KAAKhD,YAAL,KAAsBL,cAAc,CAACO,eAAzC,EAA0D;AACtD,iBAAK,IAAIsD,MAAT,IAAmB,KAAK5C,eAAxB,EAAyC;AACrC,kBAAI,CAAC4C,MAAM,CAACC,MAAZ,EAAoB;AAChB;AACH;AACJ;;AACD,iBAAKX,QAAL;AACH,WAPD,MAOO,IAAI,KAAK9C,YAAL,KAAsBL,cAAc,CAACM,SAAzC,EAAoD;AACvD,gBAAI,KAAKQ,kBAAL,IAA2B,KAAKiD,SAApC,EAA+C;AAC3C,mBAAKZ,QAAL;AACH;AACJ;AACJ;;AAEOQ,QAAAA,YAAY,CAACN,EAAD,EAAa;AAC7B,cAAI,CAAC,KAAKtC,QAAN,IAAkB,CAAC,KAAKC,OAA5B,EAAqC;AACjC;AACH;;AACD,gBAAMgD,SAAS,GAAG,KAAKhD,OAAL,CAAaI,IAA/B;AACA,gBAAM6C,UAAU,GAAG,KAAKlD,QAAL,CAAcK,IAAjC;AACA,gBAAM8C,KAAK,GAAGF,SAAS,CAACE,KAAxB;AACA,gBAAMC,WAAW,GAAGC,IAAI,CAACC,KAAL,CAAWJ,UAAU,CAACK,aAAX,CAAyBC,CAAzB,GAA6BP,SAAS,CAACM,aAAV,CAAwBC,CAAhE,EAAmEN,UAAU,CAACK,aAAX,CAAyBE,CAAzB,GAA6BR,SAAS,CAACM,aAAV,CAAwBE,CAAxH,IAA6H,GAA7H,GAAmIJ,IAAI,CAACK,EAAxI,GAA6I,EAAjK;AACA,cAAIC,UAAU,GAAGP,WAAW,GAAGD,KAA/B;;AACA,cAAIQ,UAAU,GAAG,GAAjB,EAAsB;AAClBA,YAAAA,UAAU,IAAI,GAAd;AACH,WAFD,MAEO,IAAIA,UAAU,GAAG,CAAC,GAAlB,EAAuB;AAC1BA,YAAAA,UAAU,IAAI,GAAd;AACH;;AACD,gBAAMC,QAAQ,GAAG,KAAKC,UAAL,GAAkBvB,EAAlB,GAAuB,IAAxC;;AACA,cAAIe,IAAI,CAACS,GAAL,CAASH,UAAT,KAAwBC,QAA5B,EAAsC;AAClCX,YAAAA,SAAS,CAACE,KAAV,GAAkBC,WAAlB;AACH,WAFD,MAGK;AACDH,YAAAA,SAAS,CAACE,KAAV,IAAmBE,IAAI,CAACU,IAAL,CAAUJ,UAAV,IAAwBC,QAA3C;AACH;AACJ;;AA/L8B,O;;;;;iBAEM,E;;;;;;;iBAEA,I;;;;;;;iBAEU3E,cAAc,CAACkD,S;;;;;;;iBAO1B,C;;;;;;;iBASD,E;;;;;;;iBAGC,C", "sourcesContent": ["import { _decorator, instantiate, Component, Node, Prefab, Enum, CCInteger } from \"cc\";\r\nconst { ccclass, property } = _decorator;\r\n\r\nimport { eEmitterStatus, Emitter } from \"db://assets/bundles/common/script/game/bullet/Emitter\";\r\nimport Entity from \"db://assets/bundles/common/script/game/ui/base/Entity\";\r\nimport { ResEmitter } from \"db://assets/bundles/common/script/autogen/luban/schema\";\r\nimport { MyApp } from \"db://assets/bundles/common/script/app/MyApp\";\r\nimport PlaneBase from \"../PlaneBase\";\r\nimport { EmitterEnum } from \"db://assets/editor/enum-gen/EmitterEnum\";\r\n\r\nexport enum eWeaponUseCond {\r\n    Immediate,        // 立刻启用\r\n    DelayTime,        // 延迟一段时间启用\r\n    WeaponDestroyed,  // 前置武器被销毁\r\n    // TODO: TargetDistance, // 目标距离在范围内\r\n    // TODO: TargetAngle,    // 目标角度在范围内\r\n}\r\n\r\nexport const enum eWeaponState {\r\n    Aiming,     // 玩家飞机不需要这个状态, 只有敌机需要\r\n    Emitting,   // 发射中\r\n    None,       // 无任何状态:后面看需求是否增加Destroyed等状态\r\n}\r\n\r\n/**\r\n * 武器后续可能也需要继承自PlaneBase, 因为武器可能也需要血量，扣血等逻辑。\r\n */\r\n@ccclass('Weapon')\r\nexport class Weapon extends Entity {\r\n    @property({ type: CCInteger, displayName: \"转动速度(角度/秒)\" })\r\n    public readonly angleSpeed: number = 60; // 角速度\r\n    @property({ type: CCInteger, displayName: \"锁定目标时间(ms)\" })\r\n    public readonly aimingTime: number = 2000; // 锁定目标时间，单位ms\r\n    @property({ type: Enum(eWeaponUseCond), displayName: \"启用条件\"})\r\n    public readonly useCondition: eWeaponUseCond = eWeaponUseCond.Immediate;\r\n    @property({ type: CCInteger, displayName: \"延迟启用时间(ms)\", \r\n        visible() {\r\n            // @ts-ignore\r\n            return this.useCondition === eWeaponUseCond.DelayTime;\r\n        }\r\n    })\r\n    public readonly delayTime: number = 0; // 延迟时间，单位ms\r\n\r\n    // 注意这里的Node不能替换成Weapon，因为cocos在ccclass里不能引用自己。\r\n    @property({ type: [Node], displayName: \"目标武器\", \r\n        visible() { \r\n            // @ts-ignore\r\n            return this.useCondition === eWeaponUseCond.WeaponDestroyed \r\n        } \r\n    })\r\n    public targetWeaponNodes: Node[] = [];\r\n\r\n    @property({ type: Enum(EmitterEnum), displayName: \"发射器\" })\r\n    public readonly emitterID: number = 0;\r\n\r\n    private m_emitterConfig: ResEmitter | undefined = undefined;\r\n    private m_emitterPrefab: Prefab | null = null; // 武器发射器\r\n\r\n    private m_state: eWeaponState = eWeaponState.None;\r\n    private m_stateElapsedTime: number = 0; // 当前状态持续时间\r\n    private m_target: Entity | null = null; // 武器目标\r\n    private m_owner: Entity | null = null;  // 武器归属\r\n    private m_targetWeapons: Weapon[] = [];\r\n    private m_emitter: Emitter | null = null;\r\n\r\n    init() {\r\n        this.m_state = eWeaponState.None;\r\n        // if (this.emitter) {\r\n        //     this.emitter.changeStatus(eEmitterStatus.None);\r\n        // }\r\n        if (this.useCondition === eWeaponUseCond.WeaponDestroyed) {\r\n            this.m_targetWeapons = this.node.getComponentsInChildren(Weapon);\r\n        }\r\n    }\r\n\r\n    private initEmitter() {\r\n        if (!this.m_emitter) {\r\n            this.loadEmitterByID();\r\n        } else {\r\n            // 非首次启用了，后面看是直接切到预热还是要算启用延迟\r\n            this.m_emitter?.changeStatus(eEmitterStatus.Prewarm);\r\n        }\r\n    }\r\n\r\n    private loadEmitterByID() {\r\n        if (this.emitterID > 0 && MyApp.GetInstance() && MyApp.lubanMgr) {\r\n            this.m_emitterConfig = MyApp.lubanTables.TbResEmitter.get(this.emitterID);\r\n            if (this.m_emitterConfig) {\r\n                MyApp.resMgr.load(this.m_emitterConfig.prefab, Prefab, (error: any, prefab: Prefab) => {\r\n                    if (error) {\r\n                        console.error(\"Weapon load emitter prefab err\", error);\r\n                        return;\r\n                    }\r\n\r\n                    this.m_emitterPrefab = prefab;\r\n                    this.createEmitter();\r\n                });\r\n            } else {\r\n                console.error(\"Weapon load emitter err, no emitter config\");\r\n            }\r\n        } else {\r\n            console.error(\"Weapon load emitter err, no emitter id\");\r\n        }\r\n    }\r\n\r\n    private createEmitter() {\r\n        if (this.m_emitterPrefab) {\r\n            const node = instantiate(this.m_emitterPrefab);\r\n            this.node.addChild(node);\r\n            node.setPosition(0, 0);\r\n            this.m_emitter = node.getComponent(Emitter);\r\n            if (!this.m_emitter) {\r\n                console.warn(\"Weapon create emitter err, no emitter component\");\r\n                return;\r\n            }\r\n            \r\n            this.m_emitter.setEntity(this.m_owner as PlaneBase);\r\n            this.m_emitter.setIsActive(true);\r\n        }\r\n    }\r\n\r\n    public get state(): eWeaponState {\r\n        return this.m_state;\r\n    }\r\n\r\n    public set state(value: eWeaponState) {\r\n        if (this.m_state === value) {\r\n            return;\r\n        }\r\n\r\n        this.m_state = value;\r\n        this.m_stateElapsedTime = 0;\r\n    }\r\n\r\n    public setOwner(owner: Entity): Weapon {\r\n        this.m_owner = owner;\r\n        return this;\r\n    }\r\n\r\n    public setTarget(target: Entity | null): Weapon {\r\n        this.m_target = target;\r\n        if (this.useCondition === eWeaponUseCond.Immediate) {\r\n            this.activate();\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    public updateGameLogic(dt: number) {\r\n        this.m_stateElapsedTime += dt;\r\n        switch (this.m_state) {\r\n            case eWeaponState.Aiming:\r\n                this.tickAiming(dt);\r\n                break;\r\n            case eWeaponState.Emitting:\r\n                //this.tickEmitting(dt);\r\n                break;\r\n            case eWeaponState.None:\r\n                this.tickNone(dt);\r\n                break;\r\n        }\r\n    }\r\n\r\n    // 激活武器\r\n    private activate() {\r\n        this.state = this.aimingTime > 0 ? eWeaponState.Aiming : eWeaponState.Emitting;\r\n        this.initEmitter();\r\n    }\r\n\r\n    private tickAiming(dt: number) {\r\n        if (this.m_stateElapsedTime >= this.aimingTime) {\r\n            this.state = eWeaponState.Emitting;\r\n        }\r\n\r\n        this.turnToTarget(dt);\r\n    }\r\n\r\n    private tickEmitting(dt: number) {\r\n        //if (this.m_emitter && this.m_emitter.status !== eEmitterStatus.Emitting) {\r\n        //    this.m_emitter.changeStatus(eEmitterStatus.Emitting);\r\n        //}\r\n    }\r\n\r\n    private tickNone(dt: number) {\r\n        if (this.useCondition === eWeaponUseCond.WeaponDestroyed) {\r\n            for (let weapon of this.m_targetWeapons) {\r\n                if (!weapon.isDead) {\r\n                    return;\r\n                }\r\n            }\r\n            this.activate();\r\n        } else if (this.useCondition === eWeaponUseCond.DelayTime) {\r\n            if (this.m_stateElapsedTime >= this.delayTime) {\r\n                this.activate();\r\n            }\r\n        }\r\n    }\r\n\r\n    private turnToTarget(dt: number) {\r\n        if (!this.m_target || !this.m_owner) {\r\n            return;\r\n        }\r\n        const ownerNode = this.m_owner.node;\r\n        const targetNode = this.m_target.node;\r\n        const angle = ownerNode.angle;\r\n        const targetAngle = Math.atan2(targetNode.worldPosition.y - ownerNode.worldPosition.y, targetNode.worldPosition.x - ownerNode.worldPosition.x) * 180 / Math.PI - 90;\r\n        let deltaAngle = targetAngle - angle;\r\n        if (deltaAngle > 180) {\r\n            deltaAngle -= 360;\r\n        } else if (deltaAngle < -180) {\r\n            deltaAngle += 360;\r\n        }\r\n        const maxDelta = this.angleSpeed * dt / 1000;\r\n        if (Math.abs(deltaAngle) <= maxDelta) {\r\n            ownerNode.angle = targetAngle;\r\n        }\r\n        else {\r\n            ownerNode.angle += Math.sign(deltaAngle) * maxDelta;\r\n        }\r\n    }\r\n}"]}
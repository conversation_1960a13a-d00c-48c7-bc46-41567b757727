{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/gameui/game/GameReviveUI.ts"], "names": ["_decorator", "Label", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "GameIns", "BundleName", "ccclass", "property", "GameReviveUI", "_countdown", "_countdownInterval", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getUIOption", "isClickBgCloseUI", "getBundleName", "GameFight", "onLoad", "closeUI", "onShow", "onHide", "stopCountdown", "onClose", "onDestroy", "onBtnReviveClicked", "battleManager", "relifeBattle", "onBtnAdClicked", "onBtnCloseClicked", "quitBattle", "startCountdown", "updateCountdownLabel", "setInterval", "onCountdownFinished", "clearInterval", "labTime", "string"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;;AACZC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;;8BAGjBS,Y,WADZF,OAAO,CAAC,cAAD,C,UAGHC,QAAQ,CAACP,KAAD,C,2BAHb,MACaQ,YADb;AAAA;AAAA,4BACyC;AAAA;AAAA;;AAAA;;AAAA,eAK7BC,UAL6B,GAKR,CALQ;AAKL;AALK,eAM7BC,kBAN6B,GAM+B,IAN/B;AAAA;;AAMqC;AAEtD,eAANC,MAAM,GAAW;AAAE,iBAAO,qBAAP;AAA+B;;AAC1C,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAyB;;AACpC,eAAXC,WAAW,GAAU;AAAE,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AAAqC;;AAC/C,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,SAAlB;AAA6B;;AAE3DC,QAAAA,MAAM,GAAS,CAAE;;AAEd,cAAPC,OAAO,GAAG;AACZ;AAAA;AAAA,8BAAMA,OAAN,CAAcX,YAAd;AACH;;AAEW,cAANY,MAAM,GAAkB,CAC1B;AACH;;AAEW,cAANC,MAAM,GAAkB;AAC1B,eAAKC,aAAL,GAD0B,CACJ;AACzB;;AAEY,cAAPC,OAAO,GAAkB,CAE9B;;AAESC,QAAAA,SAAS,GAAS,CAE3B;;AAEDC,QAAAA,kBAAkB,GAAG;AACjB;AAAA;AAAA,kCAAQC,aAAR,CAAsBC,YAAtB;AACA,eAAKR,OAAL;AACH;;AAEDS,QAAAA,cAAc,GAAG;AACb,eAAKT,OAAL;AACA;AAAA;AAAA,kCAAQO,aAAR,CAAsBC,YAAtB;AACH;;AACDE,QAAAA,iBAAiB,GAAG;AAChB,eAAKC,UAAL;AACH,SA9CoC,CAgDrC;;;AACQC,QAAAA,cAAc,GAAS;AAC3B,eAAKtB,UAAL,GAAkB,CAAlB,CAD2B,CACN;;AACrB,eAAKuB,oBAAL,GAF2B,CAEE;;AAE7B,eAAKtB,kBAAL,GAA0BuB,WAAW,CAAC,MAAM;AACxC,iBAAKxB,UAAL;AACA,iBAAKuB,oBAAL;;AAEA,gBAAI,KAAKvB,UAAL,IAAmB,CAAvB,EAA0B;AACtB,mBAAKa,aAAL;AACA,mBAAKY,mBAAL;AACH;AACJ,WARoC,EAQlC,IARkC,CAArC,CAJ2B,CAYjB;AACb,SA9DoC,CAgErC;;;AACQZ,QAAAA,aAAa,GAAS;AAC1B,cAAI,KAAKZ,kBAAL,KAA4B,IAAhC,EAAsC;AAClCyB,YAAAA,aAAa,CAAC,KAAKzB,kBAAN,CAAb;AACA,iBAAKA,kBAAL,GAA0B,IAA1B;AACH;AACJ,SAtEoC,CAwErC;;;AACQsB,QAAAA,oBAAoB,GAAS;AACjC,cAAI,KAAKI,OAAT,EAAkB;AACd,iBAAKA,OAAL,CAAaC,MAAb,GAAuB,GAAE,KAAK5B,UAAW,GAAzC;AACH;AACJ,SA7EoC,CA+ErC;;;AACQyB,QAAAA,mBAAmB,GAAS;AAChC,eAAKJ,UAAL,GADgC,CACb;AAEtB;;AAEDA,QAAAA,UAAU,GAAG;AACT,eAAKX,OAAL;AACA;AAAA;AAAA,kCAAQO,aAAR,CAAsBI,UAAtB;AACH;;AAxFoC,O;;;;;iBAGb,I", "sourcesContent": ["import { _decorator, Label, Node } from 'cc';\r\nimport { BaseUI, UILayer, UIMgr, UIOpt } from 'db://assets/scripts/core/base/UIMgr';\r\nimport { GameIns } from '../../../game/GameIns';\r\nimport { BundleName } from '../../../const/BundleConst';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('GameReviveUI')\r\nexport class GameReviveUI extends BaseUI {\r\n\r\n    @property(Label)\r\n    labTime: Label | null = null;\r\n\r\n    private _countdown: number = 7; // 倒计时初始值\r\n    private _countdownInterval: ReturnType<typeof setInterval> | null = null; // 用于存储计时器 ID\r\n\r\n    public static getUrl(): string { return \"prefab/GameReviveUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Default; }\r\n    public static getUIOption(): UIOpt { return { isClickBgCloseUI: false }; }\r\n    public static getBundleName(): string { return BundleName.GameFight }\r\n\r\n    protected onLoad(): void {}\r\n\r\n    async closeUI() {\r\n        UIMgr.closeUI(GameReviveUI);\r\n    }\r\n\r\n    async onShow(): Promise<void> {\r\n        // this.startCountdown(); // 开始倒计时\r\n    }\r\n\r\n    async onHide(): Promise<void> {\r\n        this.stopCountdown(); // 停止倒计时\r\n    }\r\n\r\n    async onClose(): Promise<void> {\r\n\r\n    }\r\n\r\n    protected onDestroy(): void {\r\n\r\n    }\r\n\r\n    onBtnReviveClicked() {\r\n        GameIns.battleManager.relifeBattle();\r\n        this.closeUI();\r\n    }\r\n\r\n    onBtnAdClicked() {\r\n        this.closeUI();\r\n        GameIns.battleManager.relifeBattle();\r\n    }\r\n    onBtnCloseClicked() {\r\n        this.quitBattle();\r\n    }\r\n\r\n    // 开始倒计时\r\n    private startCountdown(): void {\r\n        this._countdown = 7; // 初始化倒计时\r\n        this.updateCountdownLabel(); // 更新初始显示\r\n\r\n        this._countdownInterval = setInterval(() => {\r\n            this._countdown--;\r\n            this.updateCountdownLabel();\r\n\r\n            if (this._countdown <= 0) {\r\n                this.stopCountdown();\r\n                this.onCountdownFinished();\r\n            }\r\n        }, 1000); // 每秒更新一次\r\n    }\r\n\r\n    // 停止倒计时\r\n    private stopCountdown(): void {\r\n        if (this._countdownInterval !== null) {\r\n            clearInterval(this._countdownInterval);\r\n            this._countdownInterval = null;\r\n        }\r\n    }\r\n\r\n    // 更新倒计时文本\r\n    private updateCountdownLabel(): void {\r\n        if (this.labTime) {\r\n            this.labTime.string = `${this._countdown}秒`;\r\n        }\r\n    }\r\n\r\n    // 倒计时结束时的逻辑\r\n    private onCountdownFinished(): void {\r\n        this.quitBattle(); // 关闭界面\r\n        \r\n    }\r\n\r\n    quitBattle() {\r\n        this.closeUI();\r\n        GameIns.battleManager.quitBattle();\r\n    }\r\n}"]}
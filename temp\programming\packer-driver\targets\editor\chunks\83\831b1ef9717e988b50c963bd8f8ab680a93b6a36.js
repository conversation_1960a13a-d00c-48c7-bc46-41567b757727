System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Button, Component, Label, Node, MyApp, csproto, DataEvent, EventMgr, UITools, AvatarIcon, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _dec10, _dec11, _dec12, _dec13, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _descriptor9, _descriptor10, _descriptor11, _descriptor12, _crd, ccclass, property, PKHistoryCellUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/bundles/common/script/app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "db://assets/bundles/common/script/autogen/pb/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataEvent(extras) {
    _reporterNs.report("DataEvent", "../../event/DataEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "../../event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUITools(extras) {
    _reporterNs.report("UITools", "../../game/utils/UITools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAvatarIcon(extras) {
    _reporterNs.report("AvatarIcon", "../common/components/base/AvatarIcon", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Button = _cc.Button;
      Component = _cc.Component;
      Label = _cc.Label;
      Node = _cc.Node;
    }, function (_unresolved_2) {
      MyApp = _unresolved_2.MyApp;
    }, function (_unresolved_3) {
      csproto = _unresolved_3.default;
    }, function (_unresolved_4) {
      DataEvent = _unresolved_4.DataEvent;
    }, function (_unresolved_5) {
      EventMgr = _unresolved_5.EventMgr;
    }, function (_unresolved_6) {
      UITools = _unresolved_6.UITools;
    }, function (_unresolved_7) {
      AvatarIcon = _unresolved_7.AvatarIcon;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "1cb83AOfjxBm6A+pLPkzln+", "PKHistoryCellUI", undefined);

      __checkObsolete__(['_decorator', 'Button', 'Component', 'Label', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("PKHistoryCellUI", PKHistoryCellUI = (_dec = ccclass('PKHistoryCellUI'), _dec2 = property(Button), _dec3 = property(Label), _dec4 = property(_crd && AvatarIcon === void 0 ? (_reportPossibleCrUseOfAvatarIcon({
        error: Error()
      }), AvatarIcon) : AvatarIcon), _dec5 = property(Label), _dec6 = property(Label), _dec7 = property(_crd && AvatarIcon === void 0 ? (_reportPossibleCrUseOfAvatarIcon({
        error: Error()
      }), AvatarIcon) : AvatarIcon), _dec8 = property(Label), _dec9 = property(Label), _dec10 = property(Label), _dec11 = property(Label), _dec12 = property(Node), _dec13 = property(Node), _dec(_class = (_class2 = class PKHistoryCellUI extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "btnClick", _descriptor, this);

          _initializerDefineProperty(this, "LabelType", _descriptor2, this);

          _initializerDefineProperty(this, "avatar1P", _descriptor3, this);

          _initializerDefineProperty(this, "name1P", _descriptor4, this);

          _initializerDefineProperty(this, "score1P", _descriptor5, this);

          _initializerDefineProperty(this, "avatar2P", _descriptor6, this);

          _initializerDefineProperty(this, "name2P", _descriptor7, this);

          _initializerDefineProperty(this, "score2P", _descriptor8, this);

          _initializerDefineProperty(this, "LabelTime", _descriptor9, this);

          _initializerDefineProperty(this, "labelMedal", _descriptor10, this);

          _initializerDefineProperty(this, "iconLeft", _descriptor11, this);

          _initializerDefineProperty(this, "iconRight", _descriptor12, this);

          this.guid = void 0;
        }

        onButtonClick() {
          if (!this.guid) {
            return;
          }

          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GAME_PVP_GET_REWARD, {
            game_pvp_get_reward: {
              guid: this.guid
            }
          });
        }

        start() {
          var _this$iconLeft, _this$iconRight;

          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).on((_crd && DataEvent === void 0 ? (_reportPossibleCrUseOfDataEvent({
            error: Error()
          }), DataEvent) : DataEvent).GamePvpGetAward, this.getAward, this);
          (_crd && UITools === void 0 ? (_reportPossibleCrUseOfUITools({
            error: Error()
          }), UITools) : UITools).modifyNumber(this.score1P, 123456789);
          (_crd && UITools === void 0 ? (_reportPossibleCrUseOfUITools({
            error: Error()
          }), UITools) : UITools).modifyNumber(this.score2P, 123456789);
          const timestamp = 1744731000; // 2025/04/15 20:30

          this.LabelTime.string = (_crd && UITools === void 0 ? (_reportPossibleCrUseOfUITools({
            error: Error()
          }), UITools) : UITools).formatDate(timestamp);
          (_this$iconLeft = this.iconLeft) == null || _this$iconLeft.children.forEach(node => {
            node.active = false;
          });
          (_this$iconRight = this.iconRight) == null || _this$iconRight.children.forEach(node => {
            node.active = false;
          });
        }

        getAward() {}

        update(deltaTime) {}

        onDestroy() {}

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "btnClick", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "LabelType", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "avatar1P", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "name1P", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "score1P", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "avatar2P", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "name2P", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor8 = _applyDecoratedDescriptor(_class2.prototype, "score2P", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor9 = _applyDecoratedDescriptor(_class2.prototype, "LabelTime", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor10 = _applyDecoratedDescriptor(_class2.prototype, "labelMedal", [_dec11], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor11 = _applyDecoratedDescriptor(_class2.prototype, "iconLeft", [_dec12], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor12 = _applyDecoratedDescriptor(_class2.prototype, "iconRight", [_dec13], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=831b1ef9717e988b50c963bd8f8ab680a93b6a36.js.map
import { _decorator, Enum, CCFloat, Component, JsonAsset, Node, Prefab, Slider, Vec3, ValueType, CCBoolean, CCString, Asset, resources, assetManager, AssetManager, Sprite, SpriteFrame, SpriteAtlas, math, instantiate, Vec2, CCInteger, AudioClip, BitMask, find } from 'cc';
const { ccclass, property, executeInEditMode } = _decorator;

import { LevelDataEvent, LevelDataLayer } from 'db://assets/bundles/common/script/leveldata/leveldata';
import { LevelDataEventTrigger, LevelDataEventTriggerType } from 'db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTrigger';
import { LevelDataEventTriggerLog } from 'db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerLog';
import { LevelDataEventTriggerAudio } from 'db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerAudio';
import { LevelDataEventTriggerWave, LevelDataEventWaveGroup } from 'db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerWave';
import { LevelDataEventTriggerSpecialEvent, eLevelSpecialEvent } from 'db://assets/bundles/common/script/leveldata/trigger/LevelDataEventTriggerSpecialEvent';
import { LevelDataEventCondtionType } from 'db://assets/bundles/common/script/leveldata/condition/LevelDataEventCondtion';
import { LevelDataEventCondtionWave } from 'db://assets/bundles/common/script/leveldata/condition/LevelDataEventCondtionWave';

import { LevelLayerElemLayer, LevelEditorElemUI } from './LevelEditorElemUI';
import { LevelEditorCondition } from './LevelEditorCondition';
import { LevelEditorEventTrigger, LevelEditorEventUI } from './LevelEditorEventUI'

/**
 * 这里原本设计是想做一个影子节点来编辑
 * 但后面为了简化，还是直接编辑：LevelEditorEventUI
 * 这个类就只做一些节点的管理了。
 */
@ccclass('LevelEditorEventShadowUI')
@executeInEditMode()
export class LevelEditorEventShadowUI extends Component {
    // 将配置同步到关卡的节点里去: _targetEventElem
    public static syncFromShadow(root: Node, dataLayers: LevelDataLayer[], layerName: string) {
        if (!root) return;

        let minLayerIndex = 0; 
        let maxLayerIndex = 0;
        switch (layerName) {
            case "background":
                minLayerIndex = LevelLayerElemLayer.Background;
                maxLayerIndex = LevelLayerElemLayer.Background;
                break;
            case "floor":
                minLayerIndex = LevelLayerElemLayer.Floor_0;
                maxLayerIndex = LevelLayerElemLayer.Sky_0 - 1;
                break;
            case "sky":
                minLayerIndex = LevelLayerElemLayer.Sky_0;
                maxLayerIndex = LevelLayerElemLayer.Sky_Max - 1;
                break;
            default: break;
        }
        
        dataLayers.forEach((layer) => {
            layer.events = [];
        });
        
        root.children.forEach((eventNode) => {
            const eventUIComp = eventNode.getComponent(LevelEditorEventUI);
            if (eventUIComp) {
                const layer = eventUIComp.layer;
                if (layer < minLayerIndex || layer > maxLayerIndex) {
                    console.warn(`LevelEditorEventShadowUI syncFromShadow skip event: ${eventUIComp.name}, layer: ${layerName}`);
                    return;
                }
                const layerIndex = layer - minLayerIndex;
                const layerData = dataLayers[layerIndex];
                if (!layerData) {
                    console.warn(`LevelEditorEventShadowUI syncFromShadow layerData == null, layerIndex: ${layerIndex}`);
                    return;
                }

                var event = new LevelDataEvent();
                eventUIComp.fillLevelData(event);
                layerData.events.push(event);
                // console.log(`LevelEditorEventShadowUI syncFromShadow event: ${event.name}, layerIndex: ${layerIndex}`);
            }
        })
    }

    public static syncToShadow(root: Node, dataLayers: LevelDataLayer[], layerName: string) {
        if (!root) return;

        let baseLayerIndex = LevelLayerElemLayer.Node;
        switch (layerName) {
            case "background":
                baseLayerIndex = LevelLayerElemLayer.Background;
                break;
            case "floor":
                baseLayerIndex = LevelLayerElemLayer.Floor_0;
                break;
            case "sky":
                baseLayerIndex = LevelLayerElemLayer.Sky_0;
                break;
            default: break;
        }

        dataLayers.forEach((layer, index) => {
            layer.events?.forEach((event) => {
                var node = new Node(layerName + "_" + index + "_" + event.name);
                var eventUIComp = node.addComponent(LevelEditorEventUI);
                root!.addChild(node);
                eventUIComp.layer = baseLayerIndex + index;
                eventUIComp.initByLevelData(event);
            });
        });
    }
}
let GameResourceList = {

    MainPlane: "prefabs/mainPlane/MainPlane",
    PrefabBoss: "prefabs/boss/BossPlane",
    Bullet: "prefabs/Bullet",
    EnemyPlane: "prefabs/enemy/EnemyPlane",
    HurtNum: "prefabs/effect/HurtNum",
    Hurt0: "prefabs/effect/Hurt",
    EmitterPrefabPath: "prefabs/emitter/",

    font_hurtNum: "font/hurtNum",
};

// Add "game/" prefix to all values
(() => {
    for (const key in GameResourceList) {
        GameResourceList[key as keyof typeof GameResourceList] = `game/${GameResourceList[key as keyof typeof GameResourceList]}`;
    }
})();

export default GameResourceList;
import { _decorator, instantiate, Node, Prefab, size, UIOpacity, Vec3, view } from "cc";
const { ccclass, property } = _decorator;

import { MyApp } from "db://assets/bundles/common/script/app/MyApp";

import { AttributeConst } from "db://assets/bundles/common/script/const/AttributeConst";
import { AttributeData } from "db://assets/bundles/common/script/data/base/AttributeData";
import { GameConst } from "../../../../../../../scripts/core/base/GameConst";

import { MainPlaneData } from "db://assets/bundles/common/script/data/plane/MainPlaneData";
import { Plane } from "db://assets/bundles/common/script/ui/Plane";

import { EDITOR } from "cc/env";
import PlaneBase from "db://assets/bundles/common/script/game/ui/plane/PlaneBase";
import { Bullet } from "../../../bullet/Bullet";
import { Emitter } from "../../../bullet/Emitter";
import FBoxCollider from "../../../collider-system/FBoxCollider";
import FCollider, { ColliderGroupType } from "../../../collider-system/FCollider";
import GameResourceList from "../../../const/GameResourceList";
import { GameIns } from "../../../GameIns";
import { eEntityTag } from "../../base/Entity";
import EffectLayer from "../../layer/EffectLayer";
import EnemyPlaneBase from "../enemy/EnemyPlaneBase";
import MainPlaneDebug from "./MainPlaneDebug";
import MainPlaneStat from "./MainPlaneStat";
import { GameFightUI } from "../../layer/GameFightUI";

@ccclass("MainPlane")
export class MainPlane extends PlaneBase {

    @property(Node)
    planeParent: Node | null = null;
    @property(Node)
    NodeEmitter: Node | null = null;
    @property(Node)
    InvincibleNode: Node | null = null;

    m_moveEnable = true; // 是否允许移动
    emitterComp: Emitter | null = null; // 发射器

    _hurtActTime = 0; // 受伤动画时间
    _hurtActDuration = 0.5; // 受伤动画持续时间

    _planeData: MainPlaneData | null = null;//飞机数据
    _plane: Plane | null = null;//飞机显示节点
    _fireEnable = true;//是否允许射击
    _unColliderTime: number = 0;//无敌时间
    statData: MainPlaneStat = new MainPlaneStat();

    private _maxMoveSpeedX: number = 2400; // 水平方向最大移动速度
    private _maxMoveSpeedY: number = 4000; // 垂直方向最大移动速度
    private _screenRatio: number = 1;
    private _battleWidth: number = 0; // 实际战斗宽度
    private _targetPosition: Vec3 = new Vec3(); // 目标位置
    private _lastPosition: Vec3 = new Vec3(); // 上一帧位置

    private hpRecoveryTime = 0;
    private _nuclearNum = 0;
    get nuclearNum() {
        return this._nuclearNum;
    }
    addNuclear(num: number) {
        this._nuclearNum += num;
    }
    addScore(num: number) {
        this.statData.score += num;
    }

    onLoad() {
        // 计算屏幕适配比例
        this._calculateScreenRatio();

        // 初始化位置
        this.node.getPosition(this._lastPosition);
        this._targetPosition.set(this._lastPosition);
    }

    // 纯表现层业务，请勿将逻辑代码写到这里
    update(dt: number) {
        dt = dt * GameIns.battleManager.animSpeed;
        this._hurtActTime += dt;

        if (this._unColliderTime > 0) {
            this._unColliderTime -= dt;
            if (this._unColliderTime <= 0) {
                this.cancelUncollide();
            }
        }

        this.smoothMoveToTarget(dt);
    }

    updateGameLogic(dt: number): void {
        super.updateGameLogic(dt)
        while (this.hpRecoveryTime <= GameIns.gameDataManager.gameTime) {
            this.hpRecoveryTime += 1;
            let hpRecovery = this.attribute.getHPRecovery()
            this.addHp(hpRecovery);
        }
    }

    async initPlane(planeData: MainPlaneData) {
        this._planeData = planeData;
        this.resetPlane();

        this._nuclearNum = planeData.getFinalAttributeByKey(AttributeConst.NuclearMax);
        //加载飞机显示
        const prefab = await MyApp.resMgr.loadAsync(planeData.recoursePrefab, Prefab);
        let plane = MyApp.planeMgr.getPlane(planeData, prefab);
        this._plane = plane.getComponent(Plane);
        this.planeParent!.addChild(plane);
        
        this.collideComp = this.getComponentInChildren(FBoxCollider)
        this.collideComp!.init(this, size(128, 128)); // 初始化碰撞组件
        this.collideComp!.groupType = ColliderGroupType.PLAYER;

        this.addTag(eEntityTag.Player);

        // 临时: 设置飞机发射组件
        // this.setEmitter();

        super.init();

        if (EDITOR) {
            this.node.addComponent(MainPlaneDebug)
        }
    }

    resetPlane() {
        this._plane?.reset();
        // 禁用射击
        this.setFireEnable(false);
        this.setMoveAble(false);
        this.colliderEnabled = false;
        this.isDead = false;
        this.curHp = this.maxHp;
        this.updateHpUI();

        this.hpRecoveryTime = 0;

        const targetY = -view.getVisibleSize().height / 2 * 0.3;
        this.node.setPosition(0, targetY);
        this._targetPosition.set(0, targetY, 0);
        this._lastPosition.set(0, targetY, 0);
    }

    updateHpUI(){
        GameFightUI.instance.updatePlayerUI();
    }

    setEmitter() {
        //后期根据飞机的数据，加载不同的发送组件预制体
        let path = GameResourceList.EmitterPrefabPath + "Emitter_main_01";
        MyApp.resMgr.loadAsync(path, Prefab).then((prefab) => {
            let node = instantiate(prefab);
            this.NodeEmitter?.addChild(node);
            node.setPosition(0, 0);

            this.emitterComp = node.getComponent(Emitter);
            this.emitterComp!.setEntity(this);
            this.emitterComp!.setIsActive(this._fireEnable);
            this.emitterComp!.emitterId = 1000001;
        });
    }

    /**
     * 主飞机入场动画
     */
    planeIn(): void {
        this.node.getComponent(UIOpacity)!.opacity = 0;
        this.scheduleOnce(() => {
            this.node.getComponent(UIOpacity)!.opacity = 255;
            this._plane?.onEnter(() => {
                GameIns.battleManager.onPlaneIn();
            });
        }, 0.7);
    }


    /**
     * 碰撞处理
     * @param {Object} collision 碰撞对象
     */
    onCollide(collision: FCollider) {
        let damage = 0;
        if (collision.entity instanceof Bullet) {
            if (this.attribute.getFinalAttributeByKey(AttributeConst.StatusImmuneBulletHurt) == 0) {
                damage = collision.entity.calcDamage(this);
            }
            if (damage > 0) {
                this.hurt(damage)
            }
        } else if (collision.entity instanceof EnemyPlaneBase) {
            this.collisionPlane(collision.entity);
        }
    }

    /**
     * 控制飞机移动
     * @param {number} localX
     * @param {number} touchY
     */
    onControl(localX: number, touchY: number) {
        if (!this.isDead && this.m_moveEnable) {
            // 将设计分辨率坐标转换为世界坐标
            const worldPos = this._convertTouchToWorld(localX, touchY);

            let isLeft = localX < this.node.position.x;
            this._plane?.onMoveCommand(isLeft);

            // 获取战斗边界
            const halfWidth = this._battleWidth / 2;
            const halfHeight = GameConst.ViewHeight / 2;

            // 限制飞机在战斗区域内
            let posX = Math.min(halfWidth, Math.max(-halfWidth, localX));
            let posY = Math.min(halfHeight, Math.max(-halfHeight, touchY));

            this._targetPosition.set(posX, posY, 0);
            //this.node.setPosition(posX, posY);

            // 调试日志
            //logInfo('MainPlane', ` 接收位置: (${localX},${touchY}) 限制后: (${posX},${posY}) 边界: X:±${halfWidth}, Y:±${halfHeight} `);
        }
    }

    /**
     * 平滑移动到目标位置
     * @param dt 帧时间（秒）
     */
    private smoothMoveToTarget(dt: number) {
        if (!this.m_moveEnable || this.isDead) return;

        // 获取当前位置
        this.node.getPosition(this._lastPosition);

        // 计算当前位置到目标位置的距离
        const dx = this._targetPosition.x - this._lastPosition.x;
        const dy = this._targetPosition.y - this._lastPosition.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        // 计算最大允许移动距离（分别应用X和Y方向的速度限制）
        const maxMoveX = this._maxMoveSpeedX * dt;
        const maxMoveY = this._maxMoveSpeedY * dt;

        // 应用移动速度限制（分别处理X和Y方向）
        let newX = this._lastPosition.x;
        let newY = this._lastPosition.y;

        // X方向移动限制
        if (Math.abs(dx) > maxMoveX) {
            newX += Math.sign(dx) * maxMoveX;
        } else {
            newX = this._targetPosition.x;
        }

        // Y方向移动限制
        if (Math.abs(dy) > maxMoveY) {
            newY += Math.sign(dy) * maxMoveY;
        } else {
            newY = this._targetPosition.y;
        }

        // 设置新位置
        this.node.setPosition(newX, newY);
    }


    begine(isContinue = false) {
        this.setFireEnable(true);
        this.setMoveAble(true);
        if (isContinue) {
            this.setUncollideByTime(2);
        } else {
            this.cancelUncollide();
        }
    }

    revive() {
        this.node.active = true;
        this.begine(true);
    }

    //实现父类的方法
    playHurtAnim() {
        if (this._hurtActTime > this._hurtActDuration) {
            this._hurtActTime = 0;
            // 显示红屏效果
            EffectLayer.instance.showRedScreen();
        }
    }

    toDie(): boolean {
        if (!super.toDie()) {
            return false;
        }
        this.node.active = false;
        this.resetPlane();
        GameIns.battleManager.setGameEnd(false);
        return true;
    }

    get collisionLevel() {
        return this._planeData!.config?.collideLevel || 0;
    }
    get collisionHurt() {
        return this._planeData!.config?.collideDamage || 0;
    }

    get attribute(): AttributeData {
        return this._planeData!;
    }

    getAttack(): number {
        return this._planeData!.getAttack();
    }

    setMoveAble(enable: boolean) {
        this.m_moveEnable = enable;
    }

    setFireEnable(enable: boolean) {
        this._fireEnable = enable;
        if (this.emitterComp) {
            this.emitterComp!.setIsActive(enable);
        }
    }
    //设置无敌状态
    setUncollideByTime(time: number) {
        this._unColliderTime = time;
        this.colliderEnabled = false;
        this.InvincibleNode!.active = true;
    }
    //取消无敌状态
    cancelUncollide() {
        this.colliderEnabled = true;
        this.InvincibleNode!.active = false;
    }

    setAnimSpeed(speed: number) {
        if (this._plane) {
            this._plane.setAnimSpeed(speed);
        }
    }
    get pickDiamondNum(): number {
        return this.statData.pickDiamond;
    }
    get killEnemyNum(): number {
        return this.statData.killEnemy;
    }
    get usedNuclearNum(): number {
        return this.statData.usedNuclear;
    }
    get usedSuperNum(): number {
        return this.statData.usedSuper;
    }

    /**
     * 计算屏幕适配比例
     */
    private _calculateScreenRatio() {
        const visibleSize = view.getVisibleSize();
        this._screenRatio = visibleSize.width / GameConst.designWidth;

        // 计算实际战斗宽度（考虑宽屏适配）
        this._battleWidth = GameConst.ViewBattleWidth * this._screenRatio;
        //logInfo('CameraMove', `屏幕比例: ${this._screenRatio}, 战斗宽度: ${this._battleWidth}`);
    }

    /**
     * 将触摸坐标转换为世界坐标
     * @param touchX 触摸点X坐标（0-750）
     * @param touchY 触摸点Y坐标（0-1334）
     * @returns 世界坐标
     */
    private _convertTouchToWorld(touchX: number, touchY: number): Vec3 {
        // 获取实际屏幕尺寸
        const visibleSize = view.getVisibleSize();

        // 计算设计分辨率到实际屏幕的比例
        const scaleX = visibleSize.width / GameConst.designWidth;
        const scaleY = visibleSize.height / GameConst.designHeight;

        // 将设计坐标转换为屏幕坐标
        const screenX = touchX * scaleX;
        const screenY = touchY * scaleY;

        // 将屏幕坐标转换为世界坐标
        // 世界坐标原点在屏幕中心，X范围：[-battleWidth/2, battleWidth/2]
        const worldX = screenX - visibleSize.width / 2;
        const worldY = screenY - visibleSize.height / 2;

        // 添加调试日志
        //logInfo('MainPlane', `转换: 设计(${touchX},${touchY}) -> 屏幕(${screenX},${screenY}) -> 世界(${worldX},${worldY})`);

        return new Vec3(worldX, worldY, 0);
    }
}
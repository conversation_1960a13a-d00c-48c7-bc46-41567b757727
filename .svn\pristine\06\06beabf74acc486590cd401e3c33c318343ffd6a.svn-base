"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.methods = void 0;
exports.load = load;
exports.unload = unload;
const path_1 = require("path");
// @ts-ignore
module.paths.push((0, path_1.join)(Editor.App.path, 'node_modules'));
// 当前版本需要在 module.paths 修改后才能正常使用 cc 模块
// 并且如果希望正常显示 cc 的定义，需要手动将 engine 文件夹里的 cc.d.ts 添加到插件的 tsconfig 里
// 当前版本的 cc 定义文件可以在当前项目的 temp/declarations/cc.d.ts 找到
const cc_1 = require("cc");
const { _utils } = cc_1.Prefab;
function load() { }
;
function unload() { }
;
exports.methods = {
    saveLevel() {
        console.log('saveLevel in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if (levelEditorUI) {
            levelEditorUI.save = true;
        }
    },
    playLevel() {
        console.log('playLevel in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if (levelEditorUI) {
            var play = levelEditorUI.play;
            levelEditorUI.play = !play;
        }
    },
    levelStart() {
        console.log('levelStart in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if (levelEditorUI) {
            levelEditorUI.progress = 0;
        }
    },
    levelEnd() {
        console.log('levelEnd in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if (levelEditorUI) {
            levelEditorUI.progress = 1;
        }
    },
    instantiatePrefab(component_uuid, prefabUuid) {
        // console.log('instantiatePrefab:', component_uuid, prefabUuid);
        var _a, _b, _c;
        let targetNode = (_a = cc_1.director.getScene()) === null || _a === void 0 ? void 0 : _a.getChildByUuid(component_uuid);
        if (!targetNode) {
            targetNode = (_b = cc_1.director.getScene()) === null || _b === void 0 ? void 0 : _b.getChildByName('Canvas');
        }
        if (targetNode) {
            // console.log("Canvas node found: ", targetNode.getComponent("EmitterEditor"));
            // @ts-ignore
            Editor.Message.request('scene', 'execute-component-method', {
                uuid: (_c = targetNode.getComponent("EmitterEditor")) === null || _c === void 0 ? void 0 : _c.uuid,
                name: 'instantiatePrefab',
                args: [prefabUuid]
            });
        }
    },
    // async saveToPrefab(component_uuid:string, nodeName: string, prefabUuid: string) {
    //     // console.log('saveToPrefab:', component_uuid, nodeName, prefabUuid);
    //     const scene = director.getScene();
    //     const target = scene!.getChildByPath(`Canvas/${nodeName}`);
    //     if (!target) {
    //         console.error("node not found:", nodeName);
    //         return;
    //     }
    //     cce.Prefab.createPrefabAssetFromNode(target.uuid, prefabUuid);
    // },
    async createBulletPrefab(sourceAssetUuid) {
        // @ts-ignore
        const sourceAssetInfo = await Editor.Message.request('asset-db', 'query-asset-info', sourceAssetUuid);
        if (!sourceAssetInfo)
            return;
        // 需要判断是spriteList还是单个sprite
        // TODO: 当sourceAssetUuid是一个目录的时候，下面这里的路径可能存在bug。还没有验证。
        // TODO: 这个目录后续可能调整到bundle目录
        const targetPrefabDir = 'db://assets/resources/game/prefabs/bullet/';
        if (sourceAssetInfo.importer === 'sprite-atlas') {
            cc_1.assetManager.loadAny({ uuid: sourceAssetUuid }, (err, asset) => {
                if (err) {
                    console.error(err);
                    return;
                }
                const spriteList = asset;
                for (let spriteFrame of spriteList.getSpriteFrames()) {
                    const targetPrefabName = spriteFrame.name;
                    const targetPrefabPath = targetPrefabDir + targetPrefabName + '.prefab';
                    createNewBullet(targetPrefabName, targetPrefabPath, spriteFrame, spriteList);
                }
            });
        }
        else if (sourceAssetInfo.importer === 'sprite-frame') {
            cc_1.assetManager.loadAny({ uuid: sourceAssetUuid }, (err, asset) => {
                if (err) {
                    console.error(err);
                    return;
                }
                const spriteFrame = asset;
                const targetPrefabName = spriteFrame.name;
                const targetPrefabPath = targetPrefabDir + targetPrefabName + '.prefab';
                createNewBullet(targetPrefabName, targetPrefabPath, spriteFrame, null);
            });
        }
        else {
            console.warn('Skipping unknown asset type:', sourceAssetInfo.importer);
        }
    },
    async createLevelPrefab(sourceAssetUuid) {
        // @ts-ignore
        const sourceAssetInfo = await Editor.Message.request('asset-db', 'query-asset-info', sourceAssetUuid);
        if (!sourceAssetInfo)
            return;
        // remove /x/x/*.* -> /x/x/
        let targetPrefabDir = sourceAssetInfo.path.replace('/Texture/', '/Prefab/');
        const targetPrefabPath = targetPrefabDir.substring(0, targetPrefabDir.lastIndexOf('/')) + '.prefab';
        const targetPrefabName = targetPrefabDir.substring(targetPrefabDir.lastIndexOf('/') + 1);
        if (sourceAssetInfo.importer === 'sprite-frame') {
            cc_1.assetManager.loadAny({ uuid: sourceAssetUuid }, (err, asset) => {
                if (err) {
                    console.error(err);
                    return;
                }
                // console.log(`Level Prefab will be created at path ${targetPrefabPath}`);
                const spriteFrame = asset;
                if (spriteFrame) {
                    createNewLevelObject(targetPrefabName, targetPrefabPath, spriteFrame); // 这个是创建prefab，不是prefab里面的节点，所以不需要parent
                }
                else {
                    console.warn('Asset is not a sprite-frame:', sourceAssetInfo);
                }
            });
        }
        else {
            console.warn('Skipping unknown asset type:', sourceAssetInfo.importer);
        }
    },
    async createMainPlanePrefab(sourceAssetUuid) {
        console.log(`createMainPlanePrefab uuid:[${sourceAssetUuid}]`);
        // @ts-ignore
        const sourceAssetInfo = await Editor.Message.request('asset-db', 'query-asset-info', sourceAssetUuid);
        if (!sourceAssetInfo) {
            console.error('query-asset-info failed:', sourceAssetUuid);
            return;
        }
        const planeId = cc_1.path.basename(sourceAssetInfo.path);
        const prefabPath = `db://assets/resources/game/prefabs/plane/mainplane/${planeId}.prefab`;
        const temp = new cc_1.Node("temp");
        cc_1.director.getScene().addChild(temp);
        const rootNode = new cc_1.Node(planeId);
        temp.addChild(rootNode);
        const spineNode = new cc_1.Node("spine");
        rootNode.addChild(spineNode);
        const spine = spineNode.addComponent(cc_1.sp.Skeleton);
        spine.skeletonData = await new Promise((resolve, reject) => {
            cc_1.assetManager.loadAny({ uuid: sourceAssetUuid }, (err, asset) => {
                if (err) {
                    console.error(err);
                    reject(err);
                }
                else {
                    console.log("createMainPlanePrefab load spine success");
                    resolve(asset);
                }
            });
        });
        const colliderNode = new cc_1.Node("collider");
        rootNode.addChild(colliderNode);
        const collider = colliderNode.addComponent(cc_1.BoxCollider2D);
        collider.size = new cc_1.Size(128, 128);
        try {
            await cce.Prefab.createPrefabAssetFromNode(rootNode.uuid, prefabPath);
            console.log(`main plane prefab created: ${prefabPath}`);
            temp.removeFromParent();
            Editor.Message.request('asset-db', 'refresh-asset', prefabPath);
        }
        catch (e) {
            console.error('Failed to create level prefab:', e, ', on asset: ', prefabPath);
        }
    },
    async createEnemyPlanePrefab(sourceAssetUuid) {
        console.log(`createEnemyPlanePrefab uuid:[${sourceAssetUuid}]`);
        // @ts-ignore
        const sourceAssetInfo = await Editor.Message.request('asset-db', 'query-asset-info', sourceAssetUuid);
        if (!sourceAssetInfo) {
            console.error('query-asset-info failed:', sourceAssetUuid);
            return;
        }
        const planeId = cc_1.path.basename(sourceAssetInfo.path);
        const prefabPath = `db://assets/resources/game/prefabs/plane/enemyplane/${planeId}.prefab`;
        const temp = new cc_1.Node("temp");
        cc_1.director.getScene().addChild(temp);
        const rootNode = new cc_1.Node(planeId);
        temp.addChild(rootNode);
        const spineNode = new cc_1.Node("spine");
        rootNode.addChild(spineNode);
        const spine = spineNode.addComponent(cc_1.sp.Skeleton);
        spine.skeletonData = await new Promise((resolve, reject) => {
            cc_1.assetManager.loadAny({ uuid: sourceAssetUuid }, (err, asset) => {
                if (err) {
                    console.error(err);
                    reject(err);
                }
                else {
                    resolve(asset);
                }
            });
        });
        const colliderNode = new cc_1.Node("collider");
        rootNode.addChild(colliderNode);
        const collider = colliderNode.addComponent(cc_1.BoxCollider2D);
        collider.size = spineNode.getComponent(cc_1.UITransform).contentSize;
        try {
            await cce.Prefab.createPrefabAssetFromNode(rootNode.uuid, prefabPath);
            console.log(`main plane prefab created: ${prefabPath}`);
            temp.removeFromParent();
            Editor.Message.request('asset-db', 'refresh-asset', prefabPath);
        }
        catch (e) {
            console.error('Failed to create level prefab:', e, ', on asset: ', prefabPath);
        }
    },
    // 阵型相关: addFormationPoint & saveFormationGroup
    addFormationPoint(formationGroupUuid) {
        const { director } = require('cc');
        let formationEditor = findComponentByUuid(director.getScene(), formationGroupUuid, 'FormationEditor');
        if (formationEditor) {
            // @ts-ignore
            formationEditor.addNewPoint(0, 0);
        }
        else {
            console.error('Formation group not found:', formationGroupUuid);
        }
    },
    async saveFormationGroup(formationGroupUuid) {
        const { director } = require('cc');
        let formationEditor = findComponentByUuid(director.getScene(), formationGroupUuid, 'FormationEditor');
        if (formationEditor) {
            // @ts-ignore
            const jsonData = formationEditor.formationData;
            // save this as JsonAsset
            if (jsonData === null) {
                // @ts-ignore
                let file = await Editor.Dialog.save({
                    path: Editor.Project.path + '/assets/resources/game/level/wave/formation/',
                    filters: [
                        { name: 'Json', extensions: ['json'] },
                    ],
                });
                if (file.canceled || !file.filePath) {
                    return;
                }
                // @ts-ignore
                Editor.Message.request('asset-db', 'create-asset', file.filePath, formationEditor.save()).then((res) => {
                    if (res) {
                        cc_1.assetManager.loadAny({ uuid: res.uuid }, (err, asset) => {
                            if (err) {
                                console.error(err);
                            }
                            else {
                                // @ts-ignore 
                                formationEditor.formationData = asset;
                            }
                        });
                    }
                });
            }
            else {
                // @ts-ignore
                Editor.Message.request('asset-db', 'save-asset', jsonData.uuid, formationEditor.save()).then((res) => {
                    if (res) {
                        cc_1.assetManager.loadAny({ uuid: res.uuid }, (err, asset) => {
                            if (err) {
                                console.error(err);
                            }
                            else {
                                // @ts-ignore 
                                formationEditor.formationData = asset;
                            }
                        });
                    }
                });
            }
        }
        else {
            console.error('Formation group not found:', formationGroupUuid);
        }
    },
    // 路径相关: addPathPoint & savePath & clearPath
    addPathPoint(pathEditorUuid) {
        const { director } = require('cc');
        let pathEditor = findComponentByUuid(director.getScene(), pathEditorUuid, 'PathEditor');
        if (pathEditor) {
            // @ts-ignore
            pathEditor.addNewPoint(0, 0);
        }
        else {
            console.error('Path editor not found:', pathEditorUuid);
        }
    },
    async savePath(pathEditorUuid) {
        const { director } = require('cc');
        let pathEditor = findComponentByUuid(director.getScene(), pathEditorUuid, 'PathEditor');
        if (pathEditor) {
            // @ts-ignore
            const jsonData = pathEditor.pathData;
            // save this as JsonAsset
            if (jsonData === null) {
                // @ts-ignore
                let file = await Editor.Dialog.save({
                    path: Editor.Project.path + '/assets/resources/game/level/wave/path/',
                    filters: [
                        { name: 'Json', extensions: ['json'] },
                    ],
                });
                if (file.canceled || !file.filePath) {
                    return;
                }
                // @ts-ignore
                Editor.Message.request('asset-db', 'create-asset', file.filePath, pathEditor.save()).then((res) => {
                    if (res) {
                        cc_1.assetManager.loadAny({ uuid: res.uuid }, (err, asset) => {
                            if (err) {
                                console.error(err);
                            }
                            else {
                                // @ts-ignore
                                pathEditor.pathData = asset;
                            }
                        });
                    }
                });
            }
            else {
                // @ts-ignore
                Editor.Message.request('asset-db', 'save-asset', jsonData.uuid, pathEditor.save()).then((res) => {
                    if (res) {
                        cc_1.assetManager.loadAny({ uuid: res.uuid }, (err, asset) => {
                            if (err) {
                                console.error(err);
                            }
                            else {
                                // @ts-ignore
                                pathEditor.pathData = asset;
                            }
                        });
                    }
                });
            }
        }
        else {
            console.error('Path editor not found:', pathEditorUuid);
        }
    },
    clearPath(pathEditorUuid) {
        const { director } = require('cc');
        let pathEditor = findComponentByUuid(director.getScene(), pathEditorUuid, 'PathEditor');
        if (pathEditor) {
            // @ts-ignore
            pathEditor.node.removeAllChildren();
            // @ts-ignore
            pathEditor.updateCurve();
        }
        else {
            console.error('Path editor not found:', pathEditorUuid);
        }
    },
    // 加载阵型数据到FormationEditor
    loadFormationData(assetUuid) {
        const { director } = require('cc');
        const scene = director.getScene();
        // 查找FormationEditor组件
        let formationEditor = scene === null || scene === void 0 ? void 0 : scene.getComponentInChildren('FormationEditor');
        if (formationEditor) {
            if (assetUuid && assetUuid != '') {
                // 加载资源并设置到FormationEditor
                cc_1.assetManager.loadAny({ uuid: assetUuid }, (err, asset) => {
                    if (err) {
                        console.error('Failed to load formation asset:', err);
                    }
                    else {
                        // @ts-ignore
                        formationEditor.formationData = asset;
                        console.log('Formation data loaded:', asset);
                    }
                });
            }
        }
        else {
            console.error('FormationEditor component not found in scene');
        }
    },
    // 加载路径数据到PathEditor
    loadPathData(assetUuid) {
        const { director } = require('cc');
        const scene = director.getScene();
        // 查找PathEditor组件
        let pathEditor = scene === null || scene === void 0 ? void 0 : scene.getComponentInChildren('PathEditor');
        if (pathEditor) {
            if (assetUuid && assetUuid != '') {
                // 加载资源并设置到PathEditor
                cc_1.assetManager.loadAny({ uuid: assetUuid }, (err, asset) => {
                    if (err) {
                        console.error('Failed to load path asset:', err);
                    }
                    else {
                        // @ts-ignore
                        pathEditor.pathData = asset;
                        console.log('Path data loaded:', asset);
                    }
                });
            }
        }
        else {
            console.error('PathEditor component not found in scene');
        }
    },
    //@region 关卡编辑器：事件节点相关的功能
    createEventNode() {
        const { director } = require('cc');
        const scene = director.getScene();
        if (scene == null)
            return;
        if (scene.name != "LevelEditor") {
            console.warn("createEventNode not in LevelEditor scene, current scene:", scene.name);
        }
        const root = (0, cc_1.find)("Canvas/EventNodeParent");
        if (root == null)
            return;
        let node = new cc_1.Node("event");
        node.parent = root;
        node.addComponent('LevelEditorEventUI');
        node.setPosition(0, 0, 0);
    },
    copyEventNode() {
        const root = getEventNodeParent();
        if (root == null)
            return;
        const selectedNodes = Editor.Selection.getSelected('node');
        if (selectedNodes.length == 0) {
            console.log("copyEventNode no node selected");
            return;
        }
        Editor.Selection.clear('node');
        selectedNodes.forEach((nodeUuid) => {
            let node = root.getChildByUuid(nodeUuid);
            if (node) {
                let newNode = (0, cc_1.instantiate)(node);
                newNode.parent = root;
                newNode.name = node.name + "_copy";
                newNode.setPosition(0, 0, 0);
                const eventSource = node.getComponent('LevelEditorEventUI');
                const eventTarget = newNode.getComponent('LevelEditorEventUI');
                // how to call LevelEditorEventUI.copyFrom? which is not part of this editor extension
                // @ts-ignore
                eventTarget.copyFrom(eventSource);
                Editor.Selection.select('node', [newNode.uuid]);
            }
        });
    },
    exportEventNode() {
        const selectedNodes = Editor.Selection.getSelected('node');
        if (selectedNodes.length == 0) {
            console.log("copyEventNode no node selected");
            return;
        }
        const root = getEventNodeParent();
        if (root == null)
            return;
        let events = [];
        selectedNodes.forEach((nodeUuid) => {
            let node = root.getChildByUuid(nodeUuid);
            if (node) {
                const eventSource = node.getComponent('LevelEditorEventUI');
                if (eventSource) {
                    events.push(eventSource);
                }
            }
        });
        if (events.length == 0) {
            console.log("exportEventNode no event found");
            return;
        }
    },
    matchEventNodeByWave() {
    },
    //@region 关卡编辑器：事件节点相关的功能
    createWavePrefab() {
        const scene = cc_1.director.getScene();
        if (!scene) {
            console.error("Scene not found");
            return;
        }
        let node = new cc_1.Node('wave_prefab_' + Math.floor(Math.random() * 1000000));
        node.parent = scene;
        node.setPosition(new cc_1.Vec3(0, 0, 0));
        node.addComponent('Wave');
        Editor.Selection.clear('node');
        Editor.Selection.select('node', [node.uuid]);
        cce.Prefab.createPrefabAssetFromNode(node.uuid, `db://assets/resources/game/prefabs/wave/${node.name}.prefab`);
    },
    // 路径编辑器相关
    flipPathHorizontal() {
        const { director } = require('cc');
        const scene = director.getScene();
        if (scene == null)
            return;
        if (scene.name != "PathEditor") {
            console.warn("flipPathHorizontal not in PathEditor scene, current scene:", scene.name);
        }
        let pathEditor = scene.getComponentInChildren('PathEditor');
        if (pathEditor == null) {
            console.error("PathEditor component not found in scene");
            return;
        }
        // @ts-ignore
        pathEditor.flipHorizontal();
    },
    flipPathVertical() {
        const { director } = require('cc');
        const scene = director.getScene();
        if (scene == null)
            return;
        if (scene.name != "PathEditor") {
            console.warn("flipPathVertical not in PathEditor scene, current scene:", scene.name);
        }
        let pathEditor = scene.getComponentInChildren('PathEditor');
        if (pathEditor == null) {
            console.error("PathEditor component not found in scene");
            return;
        }
        // @ts-ignore
        pathEditor.flipVertical();
    },
    fitPathToCircle() {
        const { director } = require('cc');
        const scene = director.getScene();
        if (scene == null)
            return;
        if (scene.name != "PathEditor") {
            console.warn("fitPathToCircle not in PathEditor scene, current scene:", scene.name);
        }
        let pathEditor = scene.getComponentInChildren('PathEditor');
        if (pathEditor == null) {
            console.error("PathEditor component not found in scene");
            return;
        }
        // @ts-ignore
        pathEditor.fitToCircle();
    },
};
function findComponentByUuid(node, uuid, className) {
    if (!node)
        return null;
    // 检查当前节点是否有指定的组件，如果有且UUID匹配则返回
    const component = node.getComponent(className);
    if (component) {
        // console.log(`${node.name} has component ${className}, uuid: ${component.uuid}, target: ${uuid}`);
        if (component.uuid === uuid) {
            return component;
        }
    }
    // 无论当前节点是否有组件，都要搜索子节点
    for (let i = 0; i < node.children.length; i++) {
        let found = findComponentByUuid(node.children[i], uuid, className);
        if (found) {
            return found;
        }
    }
    return null;
}
async function createNewBullet(prefabName, prefabPath, sourceSprite, sourceSpriteList) {
    const scene = cc_1.director.getScene();
    const target = scene.getChildByName('Canvas');
    if (!target) {
        console.error("Canvas node not found");
        return;
    }
    let node = new cc_1.Node(prefabName);
    node.parent = target;
    node.setPosition(new cc_1.Vec3(0, 0, 0));
    let uiTransform = node.addComponent(cc_1.UITransform); // Ensure it has a transform component
    node.addComponent('FBoxCollider');
    let movable = node.addComponent('DefaultMove');
    node.addComponent('Bullet');
    node.addComponent('cc.BoxCollider2D');
    let sprite = node.addComponent(cc_1.Sprite);
    if (sourceSpriteList) {
        sprite.spriteFrame = sourceSprite;
        sprite.spriteAtlas = sourceSpriteList;
    }
    if (sourceSprite) {
        uiTransform.contentSize = new cc_1.Size(sourceSprite.rect.width, sourceSprite.rect.height);
    }
    const nodeUuid = node.uuid;
    try {
        await cce.Prefab.createPrefabAssetFromNode(nodeUuid, prefabPath);
        console.log(`Bullet prefab created: ${prefabPath}`);
        Editor.Message.request('asset-db', 'refresh-asset', prefabPath);
    }
    catch (e) {
        console.error('Failed to create bullet prefab:', e);
    }
}
async function createNewLevelObject(prefabName, prefabPath, sourceSprite) {
    const scene = cc_1.director.getScene();
    let node = new cc_1.Node(prefabName);
    scene.addChild(node);
    let uiTransform = node.addComponent(cc_1.UITransform); // Ensure it has a transform component
    let sprite = node.addComponent(cc_1.Sprite);
    if (sourceSprite) {
        sprite.spriteFrame = sourceSprite;
        uiTransform.contentSize = new cc_1.Size(sourceSprite.rect.width, sourceSprite.rect.height);
    }
    const nodeUuid = node.uuid;
    try {
        await cce.Prefab.createPrefabAssetFromNode(nodeUuid, prefabPath);
        console.log(`Level prefab created: ${prefabPath}`);
        Editor.Message.request('asset-db', 'refresh-asset', prefabPath);
    }
    catch (e) {
        console.error('Failed to create level prefab:', e, ', on asset: ', prefabPath);
    }
}
function getEventNodeParent() {
    const scene = cc_1.director.getScene();
    if (!scene) {
        console.error("Scene not found");
        return null;
    }
    const node = (0, cc_1.find)("Canvas/EventNodeParent");
    if (!node) {
        console.error("EventNodeParent not found");
        return null;
    }
    return node;
}
//# sourceMappingURL=data:application/json;base64,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
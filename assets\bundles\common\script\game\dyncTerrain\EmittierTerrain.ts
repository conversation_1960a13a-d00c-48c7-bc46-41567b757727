import { _decorator, assetManager, Camera, CCBoolean, CC<PERSON>loat, CC<PERSON><PERSON>ger, Component, director, Enum, instantiate, Prefab, UITransform, Vec3, view } from 'cc';
import { EDITOR } from 'cc/env';
import { LayerEmittierStrategy, LayerEmittierType, LayerRandomRange } from '../../leveldata/leveldata';
import { GameIns } from '../GameIns';
import { logDebug } from 'db://assets/scripts/utils/Logger';
import { LevelNodeCheckOutScreen } from '../ui/map/LevelNodeCheckOutScreen';
const { ccclass, property, executeInEditMode, menu} = _decorator;

const EMIITER_POOL_NAME = "emittier_pool";

enum LayerEmittierTypeZh {
    无限 = LayerEmittierType.Infinite,
    持续时间 = LayerEmittierType.Duration,
    发射次数 = LayerEmittierType.Count,
    监听事件 = LayerEmittierType.Event
}

export enum EmittierStatus {
    inactive,
    active,
    pause,
    end
}

@ccclass('SerializableRandomRange')
export class SerializableRandomRange {
    @property({type: CCFloat, displayName: "最小值"})
    public min: number = 0;
    
    @property({type: CCFloat, displayName: "最大值"})
    public max: number = 0;
    
    // 提供转换方法
    toLayerRandomRange(): LayerRandomRange {
        return new LayerRandomRange(this.min, this.max);
    }
    
    fromLayerRandomRange(range: LayerRandomRange): void {
        this.min = range.min;
        this.max = range.max;
    }
}

export class EmittierElem extends Component {
    private _velocity: Vec3 = new Vec3(0, 0, 0);
    
    /**
     * 初始化元素运动参数
     * @param speed 运动速度
     * @param angle 运动角度（度）
     */
    public init(speed: number, angle: number): void {
        // 将角度转换为弧度
        const rad = angle * Math.PI / 180;
        
        // 计算速度分量
        this._velocity.x = Math.cos(rad) * speed;
        this._velocity.y = Math.sin(rad) * speed;
    }
    
    tick(dt: number): void {
        // 更新位置
        const pos = this.node.position.clone();
        pos.add(this._velocity.clone().multiplyScalar(dt));
        this.node.setPosition(pos);
    }
}

@ccclass('EmittierTerrain')
@executeInEditMode()
@menu('地形系统/地形发射器')
export class EmittierTerrain extends Component {

    @property({visible: false})
    private _bfollow: boolean = false;
    @property({type: CCBoolean, displayName: "是否跟随层级移动"})
    public set bfollow(value: boolean) { this._bfollow = value; }
    public get bfollow(): boolean { return this._bfollow; }
    @property({type: Prefab, displayName: "发射器"})
    public emittier: Prefab | null = null;
    @property({type: Enum(LayerEmittierStrategy), displayName: "发射策略"})
    public strategy = LayerEmittierStrategy.Random;
    @property({type: [Prefab], displayName: "发射元素组"})
    public emittierElements: Prefab[] = [];
    @property({type: Enum(LayerEmittierTypeZh), displayName:"发射器类型",})
    public get typeZh(): LayerEmittierTypeZh { return this.type as unknown as LayerEmittierTypeZh;}
    public set typeZh(value: LayerEmittierTypeZh) { this.type = value as unknown as LayerEmittierType;}
    @property({visible: false})
    public type = LayerEmittierType.Infinite;
    @property({type: CCFloat, displayName: "效果值", tooltip: "值对应类型:Infinite为无限,Duration为持续时间(ms),Count为发射次数,Event为监听事件"})
    public value: number = 0; // 根据type决定用途，Infinite为无限，Duration为持续时间，Count为发射次数，Event为监听事件
    @property({type: SerializableRandomRange, displayName: "效果值校正"})
    public valueModify: SerializableRandomRange = new SerializableRandomRange(); 
    @property({type: CCInteger, displayName: "初始延迟(ms)", min: 0})
    public initDelay: number = 0;
    @property({type: SerializableRandomRange, displayName: "延迟校正(ms)"})
    public delayModify: SerializableRandomRange = new SerializableRandomRange();
    @property({type: CCInteger, displayName: "发射间隔(ms)"})
    public interval: number = 0;
    @property({type: SerializableRandomRange, displayName: "间隔校正(ms)"})
    public intervalModify: SerializableRandomRange = new SerializableRandomRange();
    @property({type: CCInteger, displayName: "发射角度(0-360)",min: 0, max: 360})
    public angle: number = 0;
    @property({type: SerializableRandomRange, displayName: "角度校正(0-360)"})
    public angleModify: SerializableRandomRange = new SerializableRandomRange();     
    @property({type: CCFloat, displayName: "发射速度"})
    public speed: number = 0;
    @property({type: SerializableRandomRange, displayName: "速度校正"})
    public speedModify: SerializableRandomRange = new SerializableRandomRange();
    @property({type: SerializableRandomRange, displayName:"X偏移范围"})
    public offSetX: SerializableRandomRange = new SerializableRandomRange();

    private _status: EmittierStatus = EmittierStatus.inactive;
    private _activeElements: EmittierElem[] = [];

    private _curDelay: number = 0; // 当前延迟
    private _curValue: number = 0; // 当前效果值
    private _curEmiIndex: number = 0; // 当前发射器索引

    private _deltaTime = 0; // 发射器运行总时间
    private _lastEmitTime = 0; // 上次发射时间
    private _emitCount: number = 0; // 已发射元素计数
    private _nextInterval: number = 0; // 下一次发射的间隔时间
    private _initialDelayPassed: boolean = false; // 初始延迟是否已过

    private _poolName: string = '';

    public get status(): EmittierStatus {
        return this._status;
    }

    public get follow(): boolean {
        return this._bfollow;
    }

    protected onLoad(): void {
        this._resetData();

        if (EDITOR) {
            this.node.removeAllChildren();
            assetManager.loadAny({ uuid: this.emittier!.uuid }, (err, prefab: Prefab) => {
                if (err) {
                    return;
                } else {
                    
                    const emitterNode = instantiate(prefab);    
                    this.node.addChild(emitterNode);
                }
            });
        }
    }

    init(poolName: string) {
        this._poolName = poolName;
        this._status = EmittierStatus.inactive;
        this._resetData();
    }

    public startEmittier(): void {
        this._status = EmittierStatus.active;
        if (EDITOR) {
            this._curDelay = this.initDelay + this.delayModify!.min + Math.random() * (this.delayModify!.max - this.delayModify!.min);
            this._curValue = this.value + Math.random() * (this.valueModify!.max - this.valueModify!.min);
        } else {
            this._curDelay = this.initDelay + this.delayModify!.min + GameIns.battleManager.random() * (this.delayModify!.max - this.delayModify!.min);
            this._curValue = this.value + this.valueModify!.min + GameIns.battleManager.random() * (this.valueModify!.max - this.valueModify!.min);
        }
    }

    public tick(dt: number): void {
        if (this._status !== EmittierStatus.active) return;

        const dtMs = dt * 1000;
        // 运行发射器
        this._updateEmitter(dtMs);
        this._updateActiveElements(dt);
    }

    /**
     * 更新发射器自身状态
     * @param dt 增量时间（豪秒）
     */
    private _updateEmitter(dt: number): void {
        this._deltaTime += dt;
        
        if (!this._initialDelayPassed) {
            const delaySeconds = this._curDelay;
            if (this._deltaTime >= delaySeconds) {
                this._initialDelayPassed = true;
                this._deltaTime = 0; // 重置时间，开始发射循环
            }
            return;
        }
        
        // 运行发射器
        this._runEmittier();
    }

    /**
     * 更新所有已发射的元素
     * @param dt 增量时间（秒）
     */
    private _updateActiveElements(dt: number): void {
        // 遍历所有元素并更新
        for (let i = this._activeElements.length - 1; i >= 0; i--) {
            const elem = this._activeElements[i];
            
            // 检查元素是否已被销毁
            if (!elem.isValid) {
                // 从数组中移除已销毁的元素
                this._activeElements.splice(i, 1);
                continue;
            }
            
            // 更新元素状态
            elem.tick(dt);
        }
    }

    private _runEmittier(): void 
    {
        if (!this.emittier || this.emittierElements.length === 0) return;
        
        const currentTime = this._deltaTime;
        
        // 检查是否达到发射间隔
        if (currentTime - this._lastEmitTime < this._nextInterval) return;
        
        // 随机选择要发射的元素
        if (this.strategy === LayerEmittierStrategy.Random) { 
            if (EDITOR) {
                this._curEmiIndex = Math.floor(Math.random() * this.emittierElements.length);
            } else {
                this._curEmiIndex = Math.floor(GameIns.battleManager.random() * this.emittierElements.length);
            }
        } else if (this.strategy === LayerEmittierStrategy.Sequence) {
            this._curEmiIndex = this._emitCount % this.emittierElements.length;
        }
        const elemPrefab = this.emittierElements[this._curEmiIndex];

        let elemNode = GameIns.gameMapManager.mapObjectPoolManager.get(
            EMIITER_POOL_NAME, 
            elemPrefab.name
        );
        
        if (elemNode) {
            logDebug('EmittierTerrain',` 从对象池获取发射体 ${elemPrefab.name}`);
        } else {   
            elemNode = instantiate(elemPrefab);    
            const cheekOut = elemNode.addComponent(LevelNodeCheckOutScreen);
            cheekOut.init(EMIITER_POOL_NAME);
        }
        
        // 设置初始位置
        let offsetX = 0;
        if (EDITOR) {
            offsetX = this.offSetX!.min + Math.random() * (this.offSetX!.max - this.offSetX!.min);
            logDebug('EmittierTerrain',` 随机地形 x坐标偏移：${offsetX}`);
        } else {
            offsetX = GameIns.battleManager.random() * (this.offSetX!.max - this.offSetX!.min);
        }
        elemNode.setPosition(this.node.position.x + offsetX, 0, 0);
        
        // 设置初始角度
        let angle = 0;
        if (EDITOR) {
            angle = this.angle + this.angleModify!.min + Math.random() * (this.angleModify!.max - this.angleModify!.min);
        } else {
            angle = this.angle + this.angleModify!.min + GameIns.battleManager.random() * (this.angleModify!.max - this.angleModify!.min);
        }
        
        // 设置初始速度
        let speed = 0;
        if (EDITOR) {
            speed = this.speed + this.speedModify!.min + Math.random() * (this.speedModify!.max - this.speedModify!.min);
            logDebug('EmittierTerrain',` 随机地形 发射速度：${speed} `);
        } else {
            speed = this.speed + this.speedModify!.min + GameIns.battleManager.random() * (this.speedModify!.max - this.speedModify!.min);
        }
        
        // 获取或添加EmittierElem组件
        let elemComponent = elemNode.getComponent(EmittierElem);
        if (!elemComponent) {
            elemComponent = elemNode.addComponent(EmittierElem);
        }
        
        // 初始化元素运动
        elemComponent.init(speed, angle);
        
        // 添加到场景
        this.node.addChild(elemNode);

        // 添加到活动元素列表
        this._activeElements.push(elemComponent);
         
        // 更新发射计数和时间
        this._emitCount++;
        this._lastEmitTime = currentTime;
        
        // 计算下一次发射的间隔时间
        if (EDITOR) {
            this._nextInterval = this.interval + this.intervalModify!.min + Math.random() * (this.intervalModify!.max - this.intervalModify!.min);
            logDebug('EmittierTerrain',` 随机地形 下次发射间隔：${this._nextInterval}`);
        } else {
            this._nextInterval = this.interval + this.intervalModify!.min + GameIns.battleManager.random() * (this.intervalModify!.max - this.intervalModify!.min);            
        }
        
        // 检查发射器是否结束
        this._checkEmittierEnd();
    }

    private _checkEmittierEnd(): void {
        if (!this.emittier) return;
        
        switch (this.type) {
            case LayerEmittierType.Duration:
                // 持续时间结束
                if (this._deltaTime >= this._curValue) {
                    this._status = EmittierStatus.end;
                }
                break;
                
            case LayerEmittierType.Count:
                // 发射次数达到
                if (this._emitCount >= this._curValue) {
                    this._status = EmittierStatus.end;
                }
                break;
                
            case LayerEmittierType.Event:
                // 事件触发结束（需要外部实现）
                // 这里可以添加事件监听逻辑
                break;
                
            // 无限类型不需要结束检查
        }

        if (this._status === EmittierStatus.end){
            this._destroyEmitter();
        }
    }

    private _destroyEmitter(): void {
        this._destroyAllElements();

        if (!EDITOR) {
            this.node.destroy();
        } else {
            // 在编辑器中，我们只重置状态，不实际销毁节点
            this._resetData();
            this._recycleNode();
        }
    }

    private _resetData(): void {
        this._status = EmittierStatus.inactive;
        this._emitCount = 0;
        this._initialDelayPassed = false;
        this._activeElements = [];
        this._deltaTime = 0;
        this._lastEmitTime = 0;
        this._nextInterval = 0;
        this._curValue = 0;
        this._curDelay = 0;
        this._curEmiIndex = 0;
    }

    public play(bPlay: boolean): void {
        if (EDITOR) {
            if (bPlay) {
                this._status = EmittierStatus.active;
                this.startEmittier();
                logDebug('EmittierTerrain',"play emittier");
            } else {
                this._status = EmittierStatus.inactive;
                this._destroyEmitter();
            }
        }
    }

    /**
     * 销毁所有已发射的元素
     */
    private _destroyAllElements(): void {
        // 销毁所有元素节点
        for (const elem of this._activeElements) {
            if (elem.isValid) {
                elem.node.destroy();
            }
        }
        
        // 清空元素列表
        this._activeElements = [];
    }

    protected onDestroy(): void {
        this.node.removeAllChildren();
    }

    private _recycleNode(): void {
        if (this._poolName) {
            logDebug('EmittierTerrain',` 节点${this.node.name}已回收到对象池${this._poolName}`);
            GameIns.gameMapManager.mapObjectPoolManager.put(this._poolName, this.node);
        } else {
            // 没有指定对象池时直接销毁
            logDebug('EmittierTerrain', `节点${this.node.name}已销毁`);
            this.node.destroy();
        }
    }
}


export const enum AttributeConst {
    //基础属性
    MaxHPOutAdd = 1, //最大生命
    MaxHPOutPer = 2, //最大生命
    MaxHPInAdd  = 3, //最大生命
    MaxHPInPer  = 4, //最大生命

    AttackOutAdd = 5, //攻击
    AttackOutPer = 6, //攻击
    AttackInAdd  = 7, //攻击
    AttackInPer  = 8, //攻击

    BulletAttackOutAdd = 9,  //子弹攻击
    BulletAttackOutPer = 10, //子弹攻击
    BulletAttackInAdd  = 11, //子弹攻击
    BulletAttackInPer  = 12, //子弹攻击

    ExplosiveBulletAttackOutAdd = 13, //子弹攻击-炸弹
    ExplosiveBulletAttackOutPer = 14, //子弹攻击-炸弹
    ExplosiveBulletAttackInAdd  = 15, //子弹攻击-炸弹
    ExplosiveBulletAttackInPer  = 16, //子弹攻击-炸弹

    NormalBulletAttackOutAdd = 17, //子弹攻击-普通子弹
    NormalBulletAttackOutPer = 18, //子弹攻击-普通子弹
    NormalBulletAttackInAdd  = 19, //子弹攻击-普通子弹
    NormalBulletAttackInPer  = 20, //子弹攻击-普通子弹

    EnergeticBulletAttackOutAdd = 21, //子弹攻击-能量
    EnergeticBulletAttackOutPer = 22, //子弹攻击-能量
    EnergeticBulletAttackInAdd  = 23, //子弹攻击-能量
    EnergeticBulletAttackInPer  = 24, //子弹攻击-能量

    PhysicsBulletAttackOutAdd = 25, //子弹攻击-物理
    PhysicsBulletAttackOutPer = 26, //子弹攻击-物理
    PhysicsBulletAttackInAdd  = 27, //子弹攻击-物理
    PhysicsBulletAttackInPer  = 28, //子弹攻击-物理

    NuclearAttackOutAdd = 29, //核弹攻击
    NuclearAttackOutPer = 30, //核弹攻击
    NuclearAttackInAdd  = 31, //核弹攻击
    NuclearAttackInPer  = 32, //核弹攻击

    BulletHurtResistanceOutAdd = 33, // 子弹伤害抗性
    BulletHurtResistanceOutPer = 34, // 子弹伤害抗性
    BulletHurtResistanceInAdd  = 35, // 子弹伤害抗性
    BulletHurtResistanceInPer  = 36, // 子弹伤害抗性

    NuclearHurtResistanceOutAdd = 37, // 核弹伤害抗性
    NuclearHurtResistanceOutPer = 38, // 核弹伤害抗性
    NuclearHurtResistanceInAdd  = 39, // 核弹伤害抗性
    NuclearHurtResistanceInPer  = 40, // 核弹伤害抗性

    BulletHurtDerateOut = 41, // 子弹伤害减免
    BulletHurtDerateIn  = 42, // 子弹伤害减免

    NormalHurtBonusOut = 43, // 普通伤害加成
    NormalHurtBonusIn  = 44, // 普通伤害加成
    BossHurtBonusOut   = 45, // boss伤害加成
    BossHurtBonusIn    = 46, // boss伤害加成

    BulletHurtFixOut = 47, //子弹伤害修正
    BulletHurtFixIn  = 48, //子弹伤害修正

    ExplosiveBulletHurtFixOut = 49, //子弹伤害修正-炸弹
    ExplosiveBulletHurtFixIn  = 50, //子弹伤害修正-炸弹

    NormalBulletHurtFixOut = 51, //子弹伤害修正-普通子弹
    NormalBulletHurtFixIn  = 52, //子弹伤害修正-普通子弹

    EnergeticBulletHurtFixOut = 53, // 子弹伤害修正-能量
    EnergeticBulletHurtFixIn  = 54, // 子弹伤害修正-能量

    PhysicsBulletHurtFixOut = 55, // 子弹伤害修正-物理
    PhysicsBulletHurtFixIn  = 56, // 子弹伤害修正-物理

    NuclearHurtFixOut = 57, // 核弹伤害修正
    NuclearHurtFixIn  = 58, // 核弹伤害修正

    HPRecoveryOutAdd = 59, //气血恢复
    HPRecoveryOutPer = 60, //气血恢复
    HPRecoveryInAdd  = 61, //气血恢复
    HPRecoveryInPer  = 62, //气血恢复

    MaxHPRecoveryRateIn  = 63, //气血恢复
    MaxHPRecoveryRateOut = 64, //气血恢复

    FortunateOutAdd = 65, //幸运值
    FortunateOutPer = 66, //幸运值
    FortunateInAdd  = 67, //幸运值
    FortunateInPer  = 68, //幸运值

    MissRateOut = 69, //闪避率
    MissRateIn  = 70, //闪避率

    CollisionHurtResistanceOutAdd = 71, // 撞击伤害抗性
    CollisionHurtResistanceOutPer = 72, // 撞击伤害抗性
    CollisionHurtResistanceInAdd  = 73, // 撞击伤害抗性
    CollisionHurtResistanceInPer  = 74, // 撞击伤害抗性

    CollisionHurtDerateOut = 75, // 撞击伤害减免%
    CollisionHurtDerateIn = 76,  // 撞击伤害减免%

    FinalScoreRateOut = 77, // 结算得分
    FinalScoreRateIn  = 78, // 结算得分
    KillScoreRateOut  = 79,  // 击杀得分
    KillScoreRateIn   = 80,  // 击杀得分

    EnergyRecoveryOutPer = 81, // 能量恢复
    EnergyRecoveryOutAdd = 82, // 能量恢复
    EnergyRecoveryInPer  = 83, // 能量恢复
    EnergyRecoveryInAdd  = 84, // 能量恢复

    PickRadiusOutPer = 85, // 拾取范围
    PickRadiusOutAdd = 86, // 拾取范围
    PickRadiusInPer  = 87, // 拾取范围
    PickRadiusInAdd  = 88, // 拾取范围

    NuclearMax = 89, // 核弹携带上限

    MaxEnergyOutAdd = 90, //最大生命
    MaxEnergyOutPer = 91, //最大生命
    MaxEnergyInAdd  = 92, //最大生命
    MaxEnergyInPer  = 93, //最大生命

    StatusImmuneBulletHurt = 94, //免疫子弹伤害
    StatusImmuneCollisionHurt = 95, //免疫撞击伤害
    StatusIgnoreBullet = 96, //无视子弹
    StatusIgnoreCollision = 97, //无视撞击
    StatusImmuneNuclearHurt = 98, //免疫核弹伤害
    StatusImmuneActiveSkillHurt = 99, //免疫主动技能伤害
    StatusInvincible = 100, //无敌

    BulletPenetrationOut = 101, //子弹破甲伤害局外百分比
    BulletPenetrationIn = 102, //子弹破甲伤害局内百分比

    BulletPenetrationRate = 103, // 子弹破甲几率
    BulletPenetrationFlag = 104, // 子弹破甲标记
}


export const AttributeComeConst = {
    EQUIP: "Equip", //装备
    SKILL: "Skill", //技能
    BUFF: "Buff", //buff
}
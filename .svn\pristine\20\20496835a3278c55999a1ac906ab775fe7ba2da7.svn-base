import { _decorator, Component, Sprite, SpriteFrame } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('StateSprite')
export class StateSprite extends Component {

    @property(SpriteFrame)
    spriteFrame: SpriteFrame[] = [];

    public setState(sp: Sprite, state: number) {
        if (state < 0 || state >= this.spriteFrame.length) {
            return;
        }
        if (sp) {
            sp.spriteFrame = this.spriteFrame[state];
        }
    }

    start() {

    }

    update(deltaTime: number) {

    }
}



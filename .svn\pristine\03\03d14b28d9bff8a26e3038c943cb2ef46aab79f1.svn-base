import { _decorator, Label, Node } from 'cc';
import { BundleName } from 'db://assets/bundles/common/script/const/BundleConst';
import { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';
import { BaseUI, UILayer, UIMgr, UIOpt } from 'db://assets/scripts/core/base/UIMgr';
import { UITools } from '../../game/utils/UITools';
import List from '../common/components/list/List';
import { MatchCellUI } from './MatchCellUI';

const { ccclass, property } = _decorator;

@ccclass('MatchRankUI')
export class MatchRankUI extends BaseUI {
    @property(ButtonPlus)
    btnGo: ButtonPlus | null = null;

    @property(Label)
    lblTime: Label | null = null;

    leftTimes: number = 0;

    @property(List)
    list: List | null = null;

    @property(Label)
    nameArr: Label[] = [];

    @property(Label)
    scoreArr: Label[] = [];

    data: { rank: number, name: string, score: number }[] = []

    public static getUrl(): string { return "prefab/ui/MatchRankUI"; }
    public static getLayer(): UILayer { return UILayer.Default }
    public static getBundleName(): string { return BundleName.homeMatch; }
    public static getUIOption(): UIOpt { return { isClickBgCloseUI: true } }
    protected onLoad(): void {
        this.btnGo!.addClick(this.onGoClick, this);
        this.leftTimes = Math.floor(Date.now() / 1000) + 3600 * 20 + 50 * 60 + 10;

        for (let i = 0; i < 10; i++) {
            this.data.push({ rank: i, name: "小师妹", score: 10000 + i });
        }

        for (let i = 0; i < 3; i++) {
            this.nameArr[i].string = this.data[i].name;
            this.scoreArr[i].string = this.data[i].score.toString();
        }
        this.list!.numItems = this.data.length - 3;
    }

    async onGoClick() {
        UIMgr.closeUI(MatchRankUI);
    }
    async onShow(): Promise<void> {

    }
    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {
    }
    protected update(dt: number): void {
        let left = this.leftTimes - Math.floor(Date.now() / 1000);
        if (left < 0) {
            return;
        }
        this.lblTime!.string = "剩余时间：" + UITools.formatTime(left, true);
    }
    protected onDestroy(): void {
    }

    onListRender(listItem: Node, row: number) {
        let dataIndex = row + 3;
        if (dataIndex < this.data.length) {
            const cell = listItem.getComponent(MatchCellUI);
            if (cell !== null) {
                cell.setInfo(this.data[dataIndex].rank, this.data[dataIndex].name, this.data[dataIndex].score);
            }
        }
    }
}



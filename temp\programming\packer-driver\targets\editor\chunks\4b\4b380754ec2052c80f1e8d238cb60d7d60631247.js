System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, CCBoolean, CCFloat, CCInteger, Component, PlaneBase, AttributeConst, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _dec10, _dec11, _class, _class2, _descriptor, _crd, ccclass, property, disallowMultiple, PlaneBaseDebug;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _reportPossibleCrUseOfPlaneBase(extras) {
    _reporterNs.report("PlaneBase", "./PlaneBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAttributeConst(extras) {
    _reporterNs.report("AttributeConst", "../../../const/AttributeConst", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      CCBoolean = _cc.CCBoolean;
      CCFloat = _cc.CCFloat;
      CCInteger = _cc.CCInteger;
      Component = _cc.Component;
    }, function (_unresolved_2) {
      PlaneBase = _unresolved_2.default;
    }, function (_unresolved_3) {
      AttributeConst = _unresolved_3.AttributeConst;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "71bc5XPF+ZO/671oK/pGux5", "PlaneBaseDebug", undefined);

      __checkObsolete__(['_decorator', 'CCBoolean', 'CCFloat', 'CCInteger', 'Component']);

      ({
        ccclass,
        property,
        disallowMultiple
      } = _decorator);

      _export("default", PlaneBaseDebug = (_dec = ccclass("PlaneBaseDebug"), _dec2 = disallowMultiple(true), _dec3 = property(CCFloat), _dec4 = property(CCFloat), _dec5 = property(CCFloat), _dec6 = property(CCBoolean), _dec7 = property(CCFloat), _dec8 = property(CCInteger), _dec9 = property({
        type: [CCInteger]
      }), _dec10 = property(CCInteger), _dec11 = property(CCBoolean), _dec(_class = _dec2(_class = (_class2 = class PlaneBaseDebug extends Component {
        constructor(...args) {
          super(...args);
          this._planeBase = null;

          _initializerDefineProperty(this, "applyBuffID", _descriptor, this);
        }

        start() {
          this._planeBase = this.node.getComponent(_crd && PlaneBase === void 0 ? (_reportPossibleCrUseOfPlaneBase({
            error: Error()
          }), PlaneBase) : PlaneBase);
        }

        get maxHP() {
          var _this$_planeBase;

          return ((_this$_planeBase = this._planeBase) == null ? void 0 : _this$_planeBase.maxHp) || 0;
        }

        get hpRecovery() {
          var _this$_planeBase2;

          return ((_this$_planeBase2 = this._planeBase) == null ? void 0 : _this$_planeBase2.attribute.getHPRecovery()) || 0;
        }

        get curHP() {
          var _this$_planeBase3;

          return ((_this$_planeBase3 = this._planeBase) == null ? void 0 : _this$_planeBase3.curHp) || 0;
        }

        get isDead() {
          var _this$_planeBase4;

          return ((_this$_planeBase4 = this._planeBase) == null ? void 0 : _this$_planeBase4.isDead) || false;
        }

        get attack() {
          var _this$_planeBase5;

          return (_this$_planeBase5 = this._planeBase) == null ? void 0 : _this$_planeBase5.getAttack();
        }

        get bulletAttack() {
          var _this$_planeBase6;

          return (_this$_planeBase6 = this._planeBase) == null ? void 0 : _this$_planeBase6.attribute.getFinialAttributeByOutInKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).BulletAttackOutAdd, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).BulletAttackOutPer, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).BulletAttackInAdd, (_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).BulletAttackInPer);
        }

        get bufferList() {
          if (!this._planeBase || !this._planeBase.buffComp) {
            return [];
          }

          let list = [];

          this._planeBase.buffComp.buffs.forEach(buff => {
            list.push(buff.res.id);
          });

          return list;
        }

        get applyBuff() {
          return false;
        }

        set applyBuff(value) {
          var _this$_planeBase7;

          if (!value) {
            return;
          }

          (_this$_planeBase7 = this._planeBase) == null || _this$_planeBase7.buffComp.ApplyBuff(false, this.applyBuffID);
        }

      }, (_applyDecoratedDescriptor(_class2.prototype, "maxHP", [_dec3], Object.getOwnPropertyDescriptor(_class2.prototype, "maxHP"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "hpRecovery", [_dec4], Object.getOwnPropertyDescriptor(_class2.prototype, "hpRecovery"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "curHP", [_dec5], Object.getOwnPropertyDescriptor(_class2.prototype, "curHP"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "isDead", [_dec6], Object.getOwnPropertyDescriptor(_class2.prototype, "isDead"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "attack", [_dec7], Object.getOwnPropertyDescriptor(_class2.prototype, "attack"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "bulletAttack", [_dec8], Object.getOwnPropertyDescriptor(_class2.prototype, "bulletAttack"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "bufferList", [_dec9], Object.getOwnPropertyDescriptor(_class2.prototype, "bufferList"), _class2.prototype), _descriptor = _applyDecoratedDescriptor(_class2.prototype, "applyBuffID", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "applyBuff", [_dec11], Object.getOwnPropertyDescriptor(_class2.prototype, "applyBuff"), _class2.prototype)), _class2)) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=4b380754ec2052c80f1e8d238cb60d7d60631247.js.map
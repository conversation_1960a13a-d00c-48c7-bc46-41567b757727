System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Prefab, Enum, JsonAsset, EnemyEnum, PlaneBase, EnemyPlane, EnemyData, MyApp, _dec, _dec2, _dec3, _dec4, _class, _class2, _descriptor, _descriptor2, _descriptor3, _crd, ccclass, property, executeInEditMode, PlaneEditor;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfEnemyEnum(extras) {
    _reporterNs.report("EnemyEnum", "../enum-gen/EnemyEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneBase(extras) {
    _reporterNs.report("PlaneBase", "db://assets/bundles/common/script/game/ui/plane/PlaneBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyPlane(extras) {
    _reporterNs.report("EnemyPlane", "db://assets/bundles/common/script/game/ui/plane/enemy/EnemyPlane", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyData(extras) {
    _reporterNs.report("EnemyData", "db://assets/bundles/common/script/game/data/EnemyData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/bundles/common/script/app/MyApp", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Prefab = _cc.Prefab;
      Enum = _cc.Enum;
      JsonAsset = _cc.JsonAsset;
    }, function (_unresolved_2) {
      EnemyEnum = _unresolved_2.EnemyEnum;
    }, function (_unresolved_3) {
      PlaneBase = _unresolved_3.default;
    }, function (_unresolved_4) {
      EnemyPlane = _unresolved_4.default;
    }, function (_unresolved_5) {
      EnemyData = _unresolved_5.EnemyData;
    }, function (_unresolved_6) {
      MyApp = _unresolved_6.MyApp;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "c7cf7P2sahGCbIoqSJBccgx", "PlaneEditor", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'Prefab', 'Enum', 'JsonAsset']);

      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);

      _export("PlaneEditor", PlaneEditor = (_dec = ccclass('PlaneEditor'), _dec2 = property({
        type: Enum(_crd && EnemyEnum === void 0 ? (_reportPossibleCrUseOfEnemyEnum({
          error: Error()
        }), EnemyEnum) : EnemyEnum),
        displayName: "敌机ID"
      }), _dec3 = property({
        type: JsonAsset,
        displayName: "路径"
      }), _dec4 = property({
        type: _crd && PlaneBase === void 0 ? (_reportPossibleCrUseOfPlaneBase({
          error: Error()
        }), PlaneBase) : PlaneBase,
        displayName: "玩家飞机"
      }), _dec(_class = (_class2 = class PlaneEditor extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "enemyPlaneID", _descriptor, this);

          _initializerDefineProperty(this, "pathAsset", _descriptor2, this);

          _initializerDefineProperty(this, "playerPlane", _descriptor3, this);

          this._enemyPlane = null;
        }

        onLoad() {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).GetInstance().init();
          this.createEnemyPlane(this.enemyPlaneID);
        }

        update(deltaTime) {
          if (this._enemyPlane) {
            this._enemyPlane.updateGameLogic(deltaTime);
          }
        }

        async createEnemyPlane(id) {
          let enemyData = new (_crd && EnemyData === void 0 ? (_reportPossibleCrUseOfEnemyData({
            error: Error()
          }), EnemyData) : EnemyData)(id);
          const prefab = await (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadAsync(enemyData.recoursePrefab, Prefab);
          this._enemyPlane = this.node.getComponent(_crd && EnemyPlane === void 0 ? (_reportPossibleCrUseOfEnemyPlane({
            error: Error()
          }), EnemyPlane) : EnemyPlane);

          if (!this._enemyPlane) {
            console.log("no plane");
            return;
          }

          this._enemyPlane.initPlane(enemyData, prefab);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "enemyPlaneID", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "pathAsset", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "playerPlane", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=7f9ed24194562105bcb6d37ac2a847b7201a12a0.js.map
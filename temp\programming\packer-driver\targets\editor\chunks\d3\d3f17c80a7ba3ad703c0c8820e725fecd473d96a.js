System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, Node, Sprite, SpriteFrame, MyApp, QualityType, BundleName, StateSprite, UIToolMgr, _crd, UITools;

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfQualityType(extras) {
    _reporterNs.report("QualityType", "../../autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "../../const/BundleConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfStateSprite(extras) {
    _reporterNs.report("StateSprite", "../../ui/common/components/base/StateSprite", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Node = _cc.Node;
      Sprite = _cc.Sprite;
      SpriteFrame = _cc.SpriteFrame;
    }, function (_unresolved_2) {
      MyApp = _unresolved_2.MyApp;
    }, function (_unresolved_3) {
      QualityType = _unresolved_3.QualityType;
    }, function (_unresolved_4) {
      BundleName = _unresolved_4.BundleName;
    }, function (_unresolved_5) {
      StateSprite = _unresolved_5.StateSprite;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "99c161J6F5NM6S9meybVmGt", "UITools", undefined);

      __checkObsolete__(['Label', 'Node', 'RichText', 'Sprite', 'SpriteFrame']);

      UIToolMgr = class UIToolMgr {
        /**
         * 将时间戳格式化为 "时:分:秒" 或 "0时0分0秒" 的字符串
         * @param totalSeconds 时间戳（秒）
         * @param isChineseFormat 是否使用中文格式（默认 false）
         */
        formatTime(totalSeconds, isChineseFormat = false) {
          if (typeof totalSeconds !== 'number' || totalSeconds < 0) {
            return isChineseFormat ? '0时0分0秒' : '00:00:00';
          }

          const hours = Math.floor(totalSeconds / 3600);
          const minutes = Math.floor(totalSeconds % 3600 / 60);
          const seconds = totalSeconds % 60;

          const pad = num => num.toString().padStart(2, '0');

          if (isChineseFormat) {
            return `${hours}时${pad(minutes)}分${pad(seconds)}秒`;
          } else {
            return `${pad(hours)}:${pad(minutes)}:${pad(seconds)}`;
          }
        }
        /**
         * 将时间戳格式化为 "年/月/日 时:分" 的字符串
         * @param txt 目标 Label 组件
         * @param totalSeconds 时间戳（秒）
         */


        formatDate(totalSeconds) {
          const date = new Date(totalSeconds * 1000);
          const year = date.getFullYear();
          const month = (date.getMonth() + 1).toString().padStart(2, '0');
          const day = date.getDate().toString().padStart(2, '0');
          const hours = date.getHours().toString().padStart(2, '0');
          const minutes = date.getMinutes().toString().padStart(2, '0');
          return `${year}/${month}/${day} ${hours}:${minutes}`;
        }
        /**
         * 修改富文本原字符串中指定 <color> 标签内的数值
         * @param richText RichText
         * @param index 要修改的 <color> 标签的索引（从 0 开始）
         * @param newValue 新的数值
         */


        modifyColorTag(richText, index, options) {
          if (richText == null || richText.string == null) return;
          const originalString = richText.string;
          const parts = originalString.split(/(<color=[^>]+>)([^<]+)(<\/color>)/g);
          const filteredParts = parts.filter(part => part !== '');

          if (index < 0 || index >= filteredParts.length / 3) {
            throw new Error(`Invalid index: ${index}`);
          }

          const tagIndex = 3 * index;
          const valueIndex = tagIndex + 1;

          if (options.color) {
            filteredParts[tagIndex] = `<color=${options.color}>`;
          }

          if (options.value) {
            filteredParts[valueIndex] = options.value;
          }

          richText.string = filteredParts.join('');
        }
        /**
         * 修改 Label 文本中的数字部分
         * @param txt 目标 Label 组件
         * @param num 替换的数字（支持 number 或 string 类型）
         * @param num2 可选，第二个替换的数字（用于替换 "数字/数字" 格式）
         */


        modifyNumber(txt, num, num2) {
          if (!txt || num === null || num === undefined) return;

          if (num2 === null || num2 === undefined) {
            const replacement = num.toString();

            if (txt.string.match(/\d+/g)) {
              txt.string = txt.string.replace(/\d+/g, replacement);
            }
          } else {
            const replacement1 = num.toString();
            const replacement2 = num2.toString();
            const regex = /(\d+)\/(\d+)/g;

            if (txt.string.match(regex)) {
              txt.string = txt.string.replace(regex, `${replacement1}/${replacement2}`);
            }
          }
        }
        /**
         * 修改状态精灵
         * @param target 目标节点或精灵
         * @param idx 状态索引
         */


        modifyStateSprite(target, idx) {
          if (target == null) return;
          let stateSprite = null;
          let sprite = null;

          if (target instanceof Node) {
            stateSprite = target.getComponent(_crd && StateSprite === void 0 ? (_reportPossibleCrUseOfStateSprite({
              error: Error()
            }), StateSprite) : StateSprite);
            sprite = target.getComponent(Sprite);
          } else if (target instanceof Sprite) {
            stateSprite = target.getComponent(_crd && StateSprite === void 0 ? (_reportPossibleCrUseOfStateSprite({
              error: Error()
            }), StateSprite) : StateSprite);
            sprite = target;
          }

          if (stateSprite == null || sprite == null) return;
          stateSprite.setState(sprite, idx);
        }
        /**
         * 设置头像
         * @param sp 目标精灵
         * @param avatarbg 头像框
         */


        setAvatarBg(sp, avatarbg) {
          if (sp == null) return;
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadAsync((_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).Common, `texture/avatarbg/${avatarbg}/spriteFrame`, SpriteFrame).then(frame => {
            sp.spriteFrame = frame;
          });
        }
        /**
         * 设置物品品质
         * @param sp 目标精灵
         * @param quality 品质
         */


        setItemQuality(sp, quality) {
          if (sp == null) return;

          if (quality >= (_crd && QualityType === void 0 ? (_reportPossibleCrUseOfQualityType({
            error: Error()
          }), QualityType) : QualityType).COMMON && quality <= (_crd && QualityType === void 0 ? (_reportPossibleCrUseOfQualityType({
            error: Error()
          }), QualityType) : QualityType).MYTHIC) {
            let qualityPng = "";

            if (quality == (_crd && QualityType === void 0 ? (_reportPossibleCrUseOfQualityType({
              error: Error()
            }), QualityType) : QualityType).COMMON) {
              qualityPng = "grey_bg";
            } else if (quality == (_crd && QualityType === void 0 ? (_reportPossibleCrUseOfQualityType({
              error: Error()
            }), QualityType) : QualityType).UNCOMMON) {
              qualityPng = "green_bg";
            } else if (quality == (_crd && QualityType === void 0 ? (_reportPossibleCrUseOfQualityType({
              error: Error()
            }), QualityType) : QualityType).RACE) {
              qualityPng = "blue_bg";
            } else if (quality == (_crd && QualityType === void 0 ? (_reportPossibleCrUseOfQualityType({
              error: Error()
            }), QualityType) : QualityType).EPIC) {
              qualityPng = "purple_bg";
            } else if (quality == (_crd && QualityType === void 0 ? (_reportPossibleCrUseOfQualityType({
              error: Error()
            }), QualityType) : QualityType).LEGENDARY) {
              qualityPng = "golden_bg";
            } else if (quality == (_crd && QualityType === void 0 ? (_reportPossibleCrUseOfQualityType({
              error: Error()
            }), QualityType) : QualityType).MYTHIC) {
              qualityPng = "red_bg";
            }

            (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.loadAsync((_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
              error: Error()
            }), BundleName) : BundleName).Common, `texture/item/${qualityPng}/spriteFrame`, SpriteFrame).then(frame => {
              sp.spriteFrame = frame;
            });
          }
        }

      };

      _export("UITools", UITools = new UIToolMgr());

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=d3f17c80a7ba3ad703c0c8820e725fecd473d96a.js.map
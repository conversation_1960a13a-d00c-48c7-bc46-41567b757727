System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Label, Sprite, AvatarIcon, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _crd, ccclass, property, FriendRankCellUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfAvatarIcon(extras) {
    _reporterNs.report("AvatarIcon", "../common/components/base/AvatarIcon", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Label = _cc.Label;
      Sprite = _cc.Sprite;
    }, function (_unresolved_2) {
      AvatarIcon = _unresolved_2.AvatarIcon;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "9b3eds3faFMWrey42h/Txcx", "FriendRankCellUI", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Label', 'Sprite']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("FriendRankCellUI", FriendRankCellUI = (_dec = ccclass('FriendRankCellUI'), _dec2 = property(Label), _dec3 = property(_crd && AvatarIcon === void 0 ? (_reportPossibleCrUseOfAvatarIcon({
        error: Error()
      }), AvatarIcon) : AvatarIcon), _dec4 = property(Label), _dec5 = property(Label), _dec6 = property(Sprite), _dec(_class = (_class2 = class FriendRankCellUI extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "lblRank", _descriptor, this);

          _initializerDefineProperty(this, "avatarIcon", _descriptor2, this);

          _initializerDefineProperty(this, "lblName", _descriptor3, this);

          _initializerDefineProperty(this, "lblScore", _descriptor4, this);

          _initializerDefineProperty(this, "spPlane", _descriptor5, this);
        }

        setData(idx) {
          this.lblRank.string = idx.toString();
          this.lblName.string = "name";
          this.lblScore.string = "分数：";
          this.spPlane.spriteFrame = null;
        }

        start() {}

        update(deltaTime) {}

        onDestroy() {}

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "lblRank", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "avatarIcon", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "lblName", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "lblScore", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "spPlane", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=370ed3ac7753dc32e619564bd191d5f9d3c41051.js.map
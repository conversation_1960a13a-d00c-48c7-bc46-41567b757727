import { _decorator, find, Label, Node, Tween, tween, Vec3 } from 'cc';
import { BaseUI, UILayer, UIMgr } from 'db://assets/scripts/core/base/UIMgr';
import { BundleName } from '../../../const/BundleConst';
import { DataEvent } from '../../../event/DataEvent';
import { EventMgr } from '../../../event/EventManager';
import { ButtonPlus } from '../../common/components/button/ButtonPlus';


const { ccclass, property } = _decorator;

@ccclass("RogueUI")
export class RogueUI extends BaseUI {

    @property(Node)
    nodeFresh: Node | null = null;
    @property(Node)
    nodeExclude: Node | null = null;
    @property(Node)
    nodeAbility: Node | null = null;
    @property([Node])
    rogueSelectNodes: Node[] = [];
    @property(Label)
    freshTimes: Label | null = null;
    @property(Label)
    excludeTimes: Label | null = null;

    private activeTweens: Tween<Node>[] = [];
    private activeTimeouts: ReturnType<typeof setTimeout>[] = [];
    _callFunc: Function | null = null;

    public static getUrl(): string { return "prefab/ui/fight/RogueUI"; };
    public static getLayer(): UILayer { return UILayer.Default }
    public static getBundleName(): string { return BundleName.Common }
    protected onLoad(): void {
        this.nodeFresh!.getComponentInChildren(ButtonPlus)!.addClick(this.onFresh, this);
        this.nodeExclude!.getComponentInChildren(ButtonPlus)!.addClick(this.onCancel, this);

        this.showNodesSequentiallyWithScale();
    }

    private showNodesSequentiallyWithScale(): void {
        const children = this.nodeAbility!.children;
        if (!children || children.length === 0) return;

        // 初始隐藏所有子节点
        children.forEach((child) => {
            child.active = false;
        });

        // 显示第一个节点并启动动画
        this.showNodeWithHalfScale(children, 0);
    }
    private showNodeWithHalfScale(children: Node[], index: number): void {
        if (index >= children.length) return;

        const child = children[index];
        child.active = true;
        child.setScale(new Vec3(0, 0, 1));

        // 前半部分动画：缩放到一半
        const halfScaleTween = tween(child)
            .to(0.05, { scale: new Vec3(0.5, 0.5, 1) }, { easing: 'quadOut' })
            .call(() => {
                // 缩放到一半时，触发下一个节点
                this.showNodeWithHalfScale(children, index + 1);
            })
            .start();

        // 后半部分动画：从一半缩放到完整
        const fullScaleTween = tween(child)
            .to(0.05, { scale: new Vec3(1, 1, 1) }, { easing: 'quadOut' })
            .start();

        this.activeTweens.push(halfScaleTween, fullScaleTween);
    }

    private showNodesSequentially(): void {
        const children = this.nodeAbility!.children;
        if (!children || children.length === 0) return;

        // 初始隐藏所有子节点
        children.forEach((child) => {
            child.active = false;
        });

        // 逐个显示子节点
        let delay = 0;
        const interval = 0.1; // 每个节点显示的间隔时间（秒）
        children.forEach((child, index) => {
            const timeoutId = setTimeout(() => {
                child.active = true;
                // 初始缩放为 0（使用 Vec3 类型）
                child.setScale(new Vec3(0, 0, 1));
                // 缩放动画（使用 Vec3 类型）
                const t = tween(child)
                    .to(0.1, { scale: new Vec3(1, 1, 1) }, { easing: 'quadOut' })
                    .start();
                this.activeTweens.push(t);
            }, delay * 1000);
            this.activeTimeouts.push(timeoutId);
            delay += interval;
        });
    }

    onFresh() {
        // this.rogueSelectIcons.forEach(element => {
        //     element.updateActive(0);
        // });
    }
    onCancel() {
        // this.rogueSelectIcons.forEach(element => {
        //     element.updateActive(0);
        // });
        // let btn = this.nodeExclude!.getComponentInChildren(ButtonPlus);
        // if (btn!.getComponentInChildren(Label)!.string == "排除") {
        //     btn!.getComponentInChildren(Label)!.string = "取消";
        //     this.rogueSelectIcons.forEach(element => {
        //         element.updateStatus(2);
        //     });
        // } else {
        //     btn!.getComponentInChildren(Label)!.string = "排除";
        //     this.rogueSelectIcons.forEach(element => {
        //         element.updateStatus(1);
        //     });
        // }
    }

    async closeUI() {
        UIMgr.closeUI(RogueUI)
        this._callFunc?.();
    }
    protected onDestroy(): void {
        EventMgr.targetOff(this)
    }

    async onShow(callFunc:Function | null = null): Promise<void> {
        this._callFunc = callFunc;
        this.refreshUI();
    }

    refreshUI(){
        let len = 3;
        for(let i= 0;i<len; i++){
            let item = this.rogueSelectNodes[i];

            let iconBg = find("iconBg",item);
            let rogueIcon = find("rogueIcon",item);
            let rogueName = find("rogueName",item);
            let rogueDesc = find("rogueDesc",item);
            let rogueType = find("rogueType",item);
            item.getComponent(ButtonPlus)!.addClick(()=>{
                this.closeUI()
            },this)
        }
    }

    async onHide(...args: any[]): Promise<void> { }
    async onClose(...args: any[]): Promise<void> {
        this.stopAllEffects(); // 停止所有动画
        // 可选：重置节点状态
        const children = this.nodeAbility!.children;
        if (children) {
            children.forEach((child) => {
                child.active = false;
                child.setScale(new Vec3(1, 1, 1)); // 恢复默认缩放
            });
        }
    }
    private stopAllEffects(): void {
        // 停止所有 tween 动画
        this.activeTweens.forEach((tween) => {
            tween.stop();
        });
        this.activeTweens = [];

        // 清除所有 setTimeout
        this.activeTimeouts.forEach((timeoutId) => {
            clearTimeout(timeoutId as unknown as number);
        });
        this.activeTimeouts = [];
    }
}

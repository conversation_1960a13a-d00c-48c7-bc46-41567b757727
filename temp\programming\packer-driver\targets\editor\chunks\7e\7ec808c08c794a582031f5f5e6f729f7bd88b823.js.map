{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/data/plane/PlaneCacheInfo.ts"], "names": ["PlaneCacheInfoElem", "PlaneCacheInfo", "planeId", "_isInit", "curPlaneId", "_planeDataDic", "init", "onNetAllPlaneInfo", "update", "getPlaneInfoById", "id", "getCurPlaneInfo"], "mappings": ";;;8BAEaA,kB,EAIAC,c;;;;;;;;;;;;;;;;;;;;;;oCAJAD,kB,GAAN,MAAMA,kBAAN,CAAyB;AAAA;AAAA,eAC5BE,OAD4B,GACV,CADU;AAAA;;AAAA,O;;gCAInBD,c,GAAN,MAAMA,cAAN,CAAsC;AAAA;AAAA,eACzCE,OADyC,GAC/B,KAD+B;AAAA,eAEzCC,UAFyC,GAEpB,CAFoB;AAAA,eAGzCC,aAHyC,GAGS,EAHT;AAAA;;AAGa;AAG/CC,QAAAA,IAAI,GAAS;AAChB,eAAKH,OAAL,GAAe,IAAf;AACA,eAAKI,iBAAL;AACH;;AAEDC,QAAAA,MAAM,GAAS,CACd;;AAEDD,QAAAA,iBAAiB,GAAE;AACf,eAAKH,UAAL,GAAkB,QAAlB,CADe,CACY;;AAC3B,eAAKC,aAAL,GAAqB,EAArB;AACA,eAAKA,aAAL,CAAmB,KAAKD,UAAxB,IAAsC;AAClCF,YAAAA,OAAO,EAAC,KAAKE;AADqB,WAAtC;AAGH;;AAEDK,QAAAA,gBAAgB,CAACC,EAAD,EAAW;AACvB,cAAI,CAAC,KAAKP,OAAV,EAAkB;AACd,iBAAKG,IAAL;AACH;;AACD,iBAAO,KAAKD,aAAL,CAAmBK,EAAnB,CAAP;AACH;;AACDC,QAAAA,eAAe,GAAG;AACd,cAAI,CAAC,KAAKR,OAAV,EAAkB;AACd,iBAAKG,IAAL;AACH;;AACD,iBAAO,KAAKD,aAAL,CAAmB,KAAKD,UAAxB,CAAP;AACH;;AAjCwC,O", "sourcesContent": ["import { IData } from \"db://assets/bundles/common/script/data/DataManager\";\n\nexport class PlaneCacheInfoElem {\n    planeId: number = 0;\n}\n\nexport class PlaneCacheInfo implements IData {\n    _isInit = false;\n    curPlaneId: number = 0;\n    _planeDataDic:{[key:number]:PlaneCacheInfoElem} = {}; //飞机数据\n\n\n    public init(): void {\n        this._isInit = true;\n        this.onNetAllPlaneInfo()\n    }\n\n    update(): void {\n    }\n\n    onNetAllPlaneInfo(){\n        this.curPlaneId = 10100001;//10003101;\n        this._planeDataDic = {}\n        this._planeDataDic[this.curPlaneId] = {\n            planeId:this.curPlaneId,\n        }\n    }\n\n    getPlaneInfoById(id:number){\n        if (!this._isInit){\n            this.init();\n        }\n        return this._planeDataDic[id]\n    }\n    getCurPlaneInfo() {\n        if (!this._isInit){\n            this.init();\n        }\n        return this._planeDataDic[this.curPlaneId]\n    }\n}\n"]}
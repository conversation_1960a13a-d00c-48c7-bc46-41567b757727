import { _decorator } from 'cc';
import { BundleName } from 'db://assets/bundles/common/script/const/BundleConst';
import { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';
import { HomeUI } from 'db://assets/bundles/common/script/ui/home/<USER>';
import { BaseUI, UILayer, UIMgr, UIOpt } from 'db://assets/scripts/core/base/UIMgr';

const { ccclass, property } = _decorator;

@ccclass('MatchResultUI')
export class MatchResultUI extends BaseUI {

    @property(ButtonPlus)
    btnExchange: ButtonPlus | null = null;
    @property(ButtonPlus)
    btnSurpass: ButtonPlus | null = null;
    @property(ButtonPlus)
    btnSuspend: ButtonPlus | null = null;
    @property(ButtonPlus)
    btnFightBack: ButtonPlus | null = null;
    @property(ButtonPlus)
    btnGetReward: ButtonPlus | null = null;

    public static getUrl(): string { return "prefab/ui/MatchResultUI"; }
    public static getLayer(): UILayer { return UILayer.Default }
    public static getBundleName(): string { return BundleName.homeMatch; }
    public static getUIOption(): UIOpt { return { isClickBgCloseUI: true } }
    protected onLoad(): void {
        this.btnExchange!.addClick(this.onExchangeClick, this);
        this.btnSurpass!.addClick(this.onSurpassClick, this);
        this.btnSuspend!.addClick(this.onSuspendClick, this);
        this.btnFightBack!.addClick(this.onFightBackClick, this);
        this.btnGetReward!.addClick(this.onGetRewardClick, this);
        this.switchBtn(1);
    }

    switchBtn(type: number) {
        this.btnExchange!.node.active = type == 1;
        this.btnSurpass!.node.active = type == 1;
        this.btnSuspend!.node.active = type == 2;
        this.btnFightBack!.node.active = type == 2;
        this.btnGetReward!.node.active = type == 3;
    }


    async onExchangeClick() {
        UIMgr.closeUI(MatchResultUI);
        await UIMgr.openUI(HomeUI)
    }
    async onSurpassClick() {
        UIMgr.closeUI(MatchResultUI);
        await UIMgr.openUI(HomeUI)
    }
    async onSuspendClick() {
        UIMgr.closeUI(MatchResultUI);
        await UIMgr.openUI(HomeUI)
    }
    async onFightBackClick() {
        UIMgr.closeUI(MatchResultUI);
        await UIMgr.openUI(HomeUI)
    }
    async onGetRewardClick() {
        UIMgr.closeUI(MatchResultUI);
        await UIMgr.openUI(HomeUI)
    }

    async onShow(): Promise<void> {

    }
    async onHide(): Promise<void> {
    }
    async onClose(): Promise<void> {
    }
    protected onDestroy(): void {
    }
}



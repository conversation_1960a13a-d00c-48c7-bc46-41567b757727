"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.methods = void 0;
exports.load = load;
exports.unload = unload;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
function genEnum(jsonFileName, enumName) {
    try {
        // @ts-ignore
        const projectPath = Editor.Project.path;
        const jsonPath = path.join(projectPath, 'assets', 'bundles', 'luban', jsonFileName + '.json');
        const outputPath = path.join(projectPath, 'assets', 'editor', 'enum-gen', enumName + '.ts');
        // 检查输入文件是否存在
        if (!fs.existsSync(jsonPath)) {
            console.warn('json file not found:', jsonPath);
            return;
        }
        // 读取JSON文件
        const jsonContent = fs.readFileSync(jsonPath, 'utf8');
        const bulletData = JSON.parse(jsonContent);
        if (!Array.isArray(bulletData)) {
            console.warn('json is not an array');
            return;
        }
        // 生成枚举内容
        let enumContent = `export enum ${enumName} {\n`;
        bulletData.forEach((item) => {
            if (item.id) {
                // remove '-'
                item.name = item.name.replace('-', '');
                if (item.name && item.name.trim() !== '') {
                    enumContent += `    ${item.name} = ${item.id},\n`;
                }
                else {
                    enumContent += `    ${item.id} = ${item.id},\n`;
                }
            }
        });
        enumContent += '}\n';
        // 确保输出目录存在
        const outputDir = path.dirname(outputPath);
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }
        // 写入文件
        fs.writeFileSync(outputPath, enumContent, 'utf8');
        console.log(`${enumName}.ts generated successfully at:`, outputPath);
        // 刷新资源数据库
        // @ts-ignore
        Editor.Message.request('asset-db', 'refresh-asset', `db://assets/editor/enum-gen/${enumName}.ts`);
    }
    catch (error) {
        console.error(`Error creating ${enumName}:`, error);
    }
}
/**
 * @en Registration method for the main process of Extension
 * @zh 为扩展的主进程的注册方法
 */
exports.methods = {
    /**
     * @en A method that can be triggered by message
     * @zh 通过 message 触发的方法
     */
    movePlayerUp() {
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'emitter-editor',
            method: 'movePlayerUp',
            args: []
        });
    },
    movePlayerDown() {
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'emitter-editor',
            method: 'movePlayerDown',
            args: []
        });
    },
    movePlayerLeft() {
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'emitter-editor',
            method: 'movePlayerLeft',
            args: []
        });
    },
    movePlayerRight() {
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'emitter-editor',
            method: 'movePlayerRight',
            args: []
        });
    },
    /**
     * 监听asset-db:asset-changed消息
     */
    onAssetChanged(_uuid, info) {
        if (info && info.path) {
            if (info.path.includes('tbresemitter.json')) {
                console.log('tbresemitter.json file changed, regenerating EmitterEnum...');
                exports.methods.createEmitterEnum();
            }
            else if (info.path.includes('tbresenemy.json')) {
                console.log('tbresenemy.json file changed, regenerating EnemyEnum...');
                exports.methods.createEnemyEnum();
            }
        }
    },
    /**
     * 创建EmitterEnum枚举文件
     */
    createEmitterEnum() {
        genEnum('tbresemitter', 'EmitterEnum');
    },
    createEnemyEnum() {
        genEnum('tbresenemy', 'EnemyEnum');
    }
};
/**
 * @en Method Triggered on Extension Startup
 * @zh 扩展启动时触发的方法
 */
function load() {
    console.log('emitter-editor extension loaded');
    console.log('Available methods:', Object.keys(exports.methods));
    // 初始化时生成一次EmitterEnum
    exports.methods.createEmitterEnum();
    exports.methods.createEnemyEnum();
}
/**
 * @en Method triggered when uninstalling the extension
 * @zh 卸载扩展时触发的方法
 */
function unload() { }
//# sourceMappingURL=data:application/json;base64,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
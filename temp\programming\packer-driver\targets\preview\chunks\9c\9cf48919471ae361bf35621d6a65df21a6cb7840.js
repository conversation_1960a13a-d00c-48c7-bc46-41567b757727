System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Prefab, instantiate, UITransform, LevelUtils, MyApp, GameConst, GameIns, LevelNodeCheckOutScreen, LevelLayer, _dec, _class, _crd, ccclass, property, BackgroundsNodeName, BACKGROUND_POOL_NAME, LevelBackgroundLayerUI;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfLevelDataBackgroundLayer(extras) {
    _reporterNs.report("LevelDataBackgroundLayer", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelUtils(extras) {
    _reporterNs.report("LevelUtils", "./LevelUtils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/bundles/common/script/app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "db://assets/scripts/core/base/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelNodeCheckOutScreen(extras) {
    _reporterNs.report("LevelNodeCheckOutScreen", "./LevelNodeCheckOutScreen", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelLayer(extras) {
    _reporterNs.report("LevelLayer", "./LevelLayer", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Prefab = _cc.Prefab;
      instantiate = _cc.instantiate;
      UITransform = _cc.UITransform;
    }, function (_unresolved_2) {
      LevelUtils = _unresolved_2.LevelUtils;
    }, function (_unresolved_3) {
      MyApp = _unresolved_3.MyApp;
    }, function (_unresolved_4) {
      GameConst = _unresolved_4.GameConst;
    }, function (_unresolved_5) {
      GameIns = _unresolved_5.GameIns;
    }, function (_unresolved_6) {
      LevelNodeCheckOutScreen = _unresolved_6.LevelNodeCheckOutScreen;
    }, function (_unresolved_7) {
      LevelLayer = _unresolved_7.LevelLayer;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "9356e983HBL44+bF3u3oqGt", "LevelBackgroundLayerUI", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Prefab', 'Node', 'instantiate', 'UITransform']);

      ({
        ccclass,
        property
      } = _decorator);
      BackgroundsNodeName = "backgrounds";
      BACKGROUND_POOL_NAME = "background_pool";

      _export("LevelBackgroundLayerUI", LevelBackgroundLayerUI = (_dec = ccclass('LevelBackgroundLayerUI'), _dec(_class = class LevelBackgroundLayerUI extends (_crd && LevelLayer === void 0 ? (_reportPossibleCrUseOfLevelLayer({
        error: Error()
      }), LevelLayer) : LevelLayer) {
        constructor() {
          super(...arguments);
          this._backgrounds = [];
          this._offSetY = 0;
          // 当前关卡的偏移量
          this._backgroundsNode = null;
        }

        onLoad() {}

        initByLevelData(data, offSetY, bFirstLoad) {
          var _this = this;

          return _asyncToGenerator(function* () {
            _this._offSetY = offSetY;

            _this.node.setPosition(0, offSetY, 0);

            _this._backgroundsNode = (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
              error: Error()
            }), LevelUtils) : LevelUtils).getOrAddNode(_this.node, BackgroundsNodeName);
            _this._backgrounds = [];
            yield _this._initBackgrounds(data, bFirstLoad);
          })();
        }

        _initBackgrounds(data, bFirstLoad) {
          var _this2 = this;

          return _asyncToGenerator(function* () {
            for (var i = 0; i < data.backgrounds.length; i++) {
              var path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                error: Error()
              }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                error: Error()
              }), MyApp) : MyApp).resMgr.defaultBundleName, data.backgrounds[i]);
              var prefab = yield (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                error: Error()
              }), MyApp) : MyApp).resMgr.loadAsync(path, Prefab);

              _this2._backgrounds.push(prefab);
            }

            if (bFirstLoad) {
              var offSetY = 0;
              var halfHeight = 0;

              while (_this2._backgrounds.length > 0 && offSetY - halfHeight < (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                error: Error()
              }), GameConst) : GameConst).VIEWPORT_LOAD_POS) {
                var node = _this2._addBackground(offSetY);

                var nodeHeight = node.getComponent(UITransform).height;
                offSetY += nodeHeight;
                halfHeight = nodeHeight / 2;
              }
            }
          })();
        }

        tick(deltaTime) {
          var posY = this.node.position.y;
          posY -= deltaTime * this.speed;
          this.node.setPosition(0, posY, 0);

          this._checkBackgrounds();
        }

        _checkBackgrounds() {
          if (!this._backgroundsNode || this._backgrounds.length === 0) {
            return;
          } // 获取最后一个背景节点


          var lastChild = this._backgroundsNode.children[this._backgroundsNode.children.length - 1];
          var lastChildTransform = lastChild.getComponent(UITransform);

          if (!lastChildTransform) {
            return;
          } // 计算最后一个背景节点的顶部位置（世界坐标） 


          var lastBackgroundHeight = lastChildTransform.height;
          var lastChildTop = this.node.position.y + lastChild.position.y + lastBackgroundHeight / 2; // 如果最后一个背景的顶部位置小于加载阈值，添加新背景

          if (lastChildTop < (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).VIEWPORT_LOAD_POS) {
            var newY = lastChild.position.y + lastBackgroundHeight;

            var newNode = this._addBackground(newY);
          }
        }

        _addBackground(yPos) {
          var index = this._backgroundsNode.children.length % this._backgrounds.length;
          var prefab = this._backgrounds[index];
          var prefabName = prefab.name;
          var node = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameMapManager.mapObjectPoolManager.get(BACKGROUND_POOL_NAME, prefabName);

          if (!node) {
            node = instantiate(this._backgrounds[index]);
            var checkOut = node.addComponent(_crd && LevelNodeCheckOutScreen === void 0 ? (_reportPossibleCrUseOfLevelNodeCheckOutScreen({
              error: Error()
            }), LevelNodeCheckOutScreen) : LevelNodeCheckOutScreen);
            checkOut.init(BACKGROUND_POOL_NAME);
          }

          this._backgroundsNode.addChild(node);

          node.setPosition(0, yPos, 0);
          return node;
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=9cf48919471ae361bf35621d6a65df21a6cb7840.js.map
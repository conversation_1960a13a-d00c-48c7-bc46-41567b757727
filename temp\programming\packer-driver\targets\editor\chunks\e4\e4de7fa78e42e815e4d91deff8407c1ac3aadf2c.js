System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10", "__unresolved_11"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Node, GameEnum, PlaneBase, Bullet, Plane, EnemyPrefab, eMoveEvent, eOrientationType, PathMove, GameIns, MyApp, EventGroupComp, AttributeConst, _dec, _dec2, _class, _class2, _descriptor, _crd, ccclass, property, EnemyPlaneBase;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../../../const/GameEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneBase(extras) {
    _reporterNs.report("PlaneBase", "db://assets/bundles/common/script/game/ui/plane/PlaneBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFCollider(extras) {
    _reporterNs.report("FCollider", "../../../collider-system/FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBullet(extras) {
    _reporterNs.report("Bullet", "../../../bullet/Bullet", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlane(extras) {
    _reporterNs.report("Plane", "db://assets/bundles/common/script/ui/Plane", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyData(extras) {
    _reporterNs.report("EnemyData", "../../../data/EnemyData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyPrefab(extras) {
    _reporterNs.report("EnemyPrefab", "../../../data/EnemyData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeMoveEvent(extras) {
    _reporterNs.report("eMoveEvent", "db://assets/bundles/common/script/game/move/IMovable", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeOrientationType(extras) {
    _reporterNs.report("eOrientationType", "db://assets/bundles/common/script/game/move/IMovable", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathMove(extras) {
    _reporterNs.report("PathMove", "db://assets/bundles/common/script/game/move/PathMove", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathData(extras) {
    _reporterNs.report("PathData", "db://assets/bundles/common/script/game/data/PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../../../app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventGroupComp(extras) {
    _reporterNs.report("EventGroupComp", "../event/EventGroupCom", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAttributeConst(extras) {
    _reporterNs.report("AttributeConst", "db://assets/bundles/common/script/const/AttributeConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAttributeData(extras) {
    _reporterNs.report("AttributeData", "../../../../data/base/AttributeData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResEnemy(extras) {
    _reporterNs.report("ResEnemy", "db://assets/bundles/common/script/autogen/luban/schema", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Node = _cc.Node;
    }, function (_unresolved_2) {
      GameEnum = _unresolved_2.GameEnum;
    }, function (_unresolved_3) {
      PlaneBase = _unresolved_3.default;
    }, function (_unresolved_4) {
      Bullet = _unresolved_4.Bullet;
    }, function (_unresolved_5) {
      Plane = _unresolved_5.Plane;
    }, function (_unresolved_6) {
      EnemyPrefab = _unresolved_6.EnemyPrefab;
    }, function (_unresolved_7) {
      eMoveEvent = _unresolved_7.eMoveEvent;
      eOrientationType = _unresolved_7.eOrientationType;
    }, function (_unresolved_8) {
      PathMove = _unresolved_8.PathMove;
    }, function (_unresolved_9) {
      GameIns = _unresolved_9.GameIns;
    }, function (_unresolved_10) {
      MyApp = _unresolved_10.MyApp;
    }, function (_unresolved_11) {
      EventGroupComp = _unresolved_11.EventGroupComp;
    }, function (_unresolved_12) {
      AttributeConst = _unresolved_12.AttributeConst;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "d7856c6OStISpoCTCG+UjsB", "EnemyPlaneBase", undefined);

      __checkObsolete__(['_decorator', 'Node', 'Prefab']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", EnemyPlaneBase = (_dec = ccclass('EnemyPlaneBase'), _dec2 = property(Node), _dec(_class = (_class2 = class EnemyPlaneBase extends (_crd && PlaneBase === void 0 ? (_reportPossibleCrUseOfPlaneBase({
        error: Error()
      }), PlaneBase) : PlaneBase) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "planeParent", _descriptor, this);

          this._plane = null;
          this._enemyData = null;
          this._enemyPrefabData = null;
          this._moveCom = null;
          this._eventGroupComp = null;
          this.removeAble = false;
          this.bullets = [];
        }

        get resConfig() {
          var _this$_enemyData;

          return ((_this$_enemyData = this._enemyData) == null ? void 0 : _this$_enemyData.config) || null;
        }

        get enemyPrefabData() {
          return this._enemyPrefabData;
        }

        get moveCom() {
          return this._moveCom;
        }

        get eventGroupComp() {
          return this._eventGroupComp;
        }

        reset() {
          var _this$_plane, _this$_moveCom;

          // 继承自BaseComp的组件在reset里会执行reset
          super.reset(); // 继承自Component的组件这里自行reset

          (_this$_plane = this._plane) == null || _this$_plane.reset();
          (_this$_moveCom = this._moveCom) == null || _this$_moveCom.reset();
        }

        initPlane(data, prefab) {
          this.reset();
          this.enemy = true;
          this._enemyData = data;
          this.removeAble = false; //加载飞机显示

          if (!this._plane) {
            let plane = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).planeMgr.getPlane(data, prefab);
            this._plane = plane.getComponent(_crd && Plane === void 0 ? (_reportPossibleCrUseOfPlane({
              error: Error()
            }), Plane) : Plane);
            this.planeParent.addChild(plane);
            this._enemyPrefabData = plane.getComponentInChildren(_crd && EnemyPrefab === void 0 ? (_reportPossibleCrUseOfEnemyPrefab({
              error: Error()
            }), EnemyPrefab) : EnemyPrefab);

            if (this._enemyPrefabData && this._enemyPrefabData.eventGroups) {
              this._eventGroupComp = this.getComp('eventGroup');

              if (!this._eventGroupComp) {
                this._eventGroupComp = new (_crd && EventGroupComp === void 0 ? (_reportPossibleCrUseOfEventGroupComp({
                  error: Error()
                }), EventGroupComp) : EventGroupComp)();
                this.addComp('eventGroup', this._eventGroupComp);
              }
            }
          }

          super.init();
          this._moveCom = this.getComponent(_crd && PathMove === void 0 ? (_reportPossibleCrUseOfPathMove({
            error: Error()
          }), PathMove) : PathMove) || this.addComponent(_crd && PathMove === void 0 ? (_reportPossibleCrUseOfPathMove({
            error: Error()
          }), PathMove) : PathMove);

          this._moveCom.removeAllListeners();

          this._moveCom.on((_crd && eMoveEvent === void 0 ? (_reportPossibleCrUseOfeMoveEvent({
            error: Error()
          }), eMoveEvent) : eMoveEvent).onBecomeInvisible, () => this.onBecameInvisible());

          this._moveCom.on((_crd && eMoveEvent === void 0 ? (_reportPossibleCrUseOfeMoveEvent({
            error: Error()
          }), eMoveEvent) : eMoveEvent).onBecomeVisible, () => this.onBecameVisible());

          this.refreshAttributes();
        }

        initMove(x, y, angle) {
          this.setPos(x, y); // 兼容编辑器

          if (!this._moveCom) {
            this._moveCom = this.getComponent(_crd && PathMove === void 0 ? (_reportPossibleCrUseOfPathMove({
              error: Error()
            }), PathMove) : PathMove) || this.addComponent(_crd && PathMove === void 0 ? (_reportPossibleCrUseOfPathMove({
              error: Error()
            }), PathMove) : PathMove);
          }

          this.refreshMoveParam();
          this._moveCom.speedAngle = angle;

          this._moveCom.setMovable(true);
        }

        initPath(x, y, pathData) {
          if (!this._moveCom) {
            return;
          }

          this.setPos(x, y);
          this.refreshMoveParam();

          this._moveCom.setOffset(x, y).setPath(pathData).setMovable(true);
        }

        refreshMoveParam() {
          var _this$_enemyData2, _this$_enemyData3, _this$_enemyPrefabDat, _this$_enemyPrefabDat2, _this$_enemyData4, _this$_enemyData5;

          if (!this._moveCom) {
            return;
          }

          this._moveCom.speed = ((_this$_enemyData2 = this._enemyData) == null ? void 0 : _this$_enemyData2.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).MoveSpeed)) || 100;
          this._moveCom.turnSpeed = ((_this$_enemyData3 = this._enemyData) == null ? void 0 : _this$_enemyData3.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).TurnSpeed)) || 0;
          this._moveCom.acceleration = 0;
          this._moveCom.accelerationAngle = 0;
          this._moveCom.tiltSpeed = ((_this$_enemyPrefabDat = this._enemyPrefabData) == null ? void 0 : _this$_enemyPrefabDat.tiltSpeed) || 0;
          this._moveCom.tiltOffset = ((_this$_enemyPrefabDat2 = this._enemyPrefabData) == null ? void 0 : _this$_enemyPrefabDat2.tiltOffset) || 0;
          let orientationType = ((_this$_enemyData4 = this._enemyData) == null || (_this$_enemyData4 = _this$_enemyData4.config) == null ? void 0 : _this$_enemyData4.orientation) || (_crd && eOrientationType === void 0 ? (_reportPossibleCrUseOfeOrientationType({
            error: Error()
          }), eOrientationType) : eOrientationType).Path;
          let orientationParam = ((_this$_enemyData5 = this._enemyData) == null || (_this$_enemyData5 = _this$_enemyData5.config) == null ? void 0 : _this$_enemyData5.orientationParam) || 0;

          this._moveCom.setOrientation(orientationType, orientationParam);
        }

        dieWhenOffScreen() {
          this.toDie((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).EnemyDestroyType.Leave);
        }

        onBecameInvisible() {
          // TODO: 从表格里增加延时销毁的配置
          this.dieWhenOffScreen(); // this.scheduleOnce(this.dieWhenOffScreen, delayDestroy);
        }

        onBecameVisible() {// this.unschedule(this.dieWhenOffScreen);
        }

        refreshAttributes() {
          var _this$_enemyData6;

          const config = (_this$_enemyData6 = this._enemyData) == null ? void 0 : _this$_enemyData6.config;

          if (!config) {
            return;
          }

          this.attribute.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).MaxHPOutAdd, config.baseHp);
          this.attribute.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).AttackOutAdd, config.baseAtk);
          this.attribute.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).StatusImmuneBulletHurt, config.immuneBulletDamage ? 1 : 0);
          this.attribute.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).StatusImmuneCollisionHurt, config.immuneCollideDamage ? 1 : 0);
          this.attribute.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).StatusIgnoreBullet, config.ignoreBullet ? 1 : 0);
          this.attribute.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).StatusIgnoreCollision, config.ignoreCollide ? 1 : 0);
          this.attribute.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).StatusImmuneNuclearHurt, config.immuneNuke ? 1 : 0);
          this.attribute.addBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).StatusImmuneActiveSkillHurt, config.immuneActiveSkill ? 1 : 0);
          this.curHp = this.maxHp;
        }

        toDie(destroyType = (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
          error: Error()
        }), GameEnum) : GameEnum).EnemyDestroyType.Die) {
          if (!super.toDie(destroyType)) {
            return false;
          }

          this.colliderEnabled = false;
          this.onDie(destroyType);
          return true;
        }

        onDie(destroyType) {
          this.willRemove();

          switch (destroyType) {
            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyDestroyType.Die:
              this.playDieAnim(() => {
                this.removeAble = true;
              });
              break;

            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyDestroyType.Leave:
            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyDestroyType.TrackOver:
            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyDestroyType.TimeOver:
              this.removeAble = true;
              break;
          }
        }

        playDieAnim(callBack) {
          // if (this.plane) {
          //     this.plane.playDieAnim(callBack);   
          // }
          callBack == null || callBack();
        }

        get attribute() {
          return this._enemyData;
        } // 表现相关的'属性'接口


        get speed() {
          return this.attribute.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).MoveSpeed);
        }

        set speed(value) {
          this.attribute.setBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).MoveSpeed, value);
          this.moveCom.speed = value;
        }

        get turnSpeed() {
          return this.attribute.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).TurnSpeed);
        }

        set turnSpeed(value) {
          this.attribute.setBaseAttribute((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).TurnSpeed, value);
          this.moveCom.turnSpeed = value;
        }

        get speedAngle() {
          return this.moveCom.speedAngle;
        }

        set speedAngle(value) {
          this.moveCom.speedAngle = value; // 一旦通过speedAngle设置角度，就关闭路径

          this.moveCom.setOnPath(false);
        }

        get acceleration() {
          return this.moveCom.acceleration;
        }

        set acceleration(value) {
          this.moveCom.acceleration = value;
        }

        get accelerationAngle() {
          return this.moveCom.accelerationAngle;
        }

        set accelerationAngle(value) {
          this.moveCom.accelerationAngle = value; // 一旦通过accelerationAngle设置角度，就关闭路径

          this.moveCom.setOnPath(false);
        }

        get tiltSpeed() {
          return this.moveCom.tiltSpeed;
        }

        set tiltSpeed(value) {
          this.moveCom.tiltSpeed = value;
        }

        get tiltOffset() {
          return this.moveCom.tiltOffset;
        }

        set tiltOffset(value) {
          this.moveCom.tiltOffset = value;
        }

        get rotateSpeed() {
          if (this.moveCom.orientationType === (_crd && eOrientationType === void 0 ? (_reportPossibleCrUseOfeOrientationType({
            error: Error()
          }), eOrientationType) : eOrientationType).Rotate) {
            return this.moveCom.orientationParam;
          }

          return 0;
        }

        set rotateSpeed(value) {
          this.moveCom.setOrientation((_crd && eOrientationType === void 0 ? (_reportPossibleCrUseOfeOrientationType({
            error: Error()
          }), eOrientationType) : eOrientationType).Rotate, value);
        } // 常用的属性接口


        get collisionLevel() {
          var _config;

          return ((_config = this._enemyData.config) == null ? void 0 : _config.collideLevel) || 0;
        }

        get collisionHurt() {
          var _config2;

          return ((_config2 = this._enemyData.config) == null ? void 0 : _config2.collideDamage) || 0;
        }

        get immuneBulletDamage() {
          return this.attribute.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).StatusImmuneBulletHurt) == 1;
        }

        get immuneCollideDamage() {
          return this.attribute.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).StatusImmuneCollisionHurt) == 1;
        }

        get ignoreBullet() {
          return this.attribute.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).StatusIgnoreBullet) == 1;
        }

        get ignoreCollide() {
          return this.attribute.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).StatusIgnoreCollision) == 1;
        }

        get immuneNuke() {
          return this.attribute.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).StatusImmuneNuclearHurt) == 1;
        }

        get immuneActiveSkill() {
          return this.attribute.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).StatusImmuneActiveSkillHurt) == 1;
        }

        get invincible() {
          return this.attribute.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).StatusInvincible) == 1;
        }

        get target() {
          var _mainPlaneManager;

          return (_mainPlaneManager = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager) == null ? void 0 : _mainPlaneManager.mainPlane;
        }

        getAttack() {
          return this._enemyData.getAttack();
        }

        onCollide(collider) {
          if (this.isDead) {
            return;
          }

          if (collider.entity instanceof (_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
            error: Error()
          }), Bullet) : Bullet)) {
            if (this.attribute.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
              error: Error()
            }), AttributeConst) : AttributeConst).StatusImmuneBulletHurt) == 0) {
              const damage = collider.entity.calcDamage(this);
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).hurtEffectManager.createHurtNumByType(collider.entity.node.getPosition(), damage);
              this.hurt(damage); // 计算破甲概率

              const attacker = collider.entity.emitter.getEntity();
              const penetrationRate = (attacker == null ? void 0 : attacker.attribute.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
                error: Error()
              }), AttributeConst) : AttributeConst).BulletPenetrationRate)) || 0;

              if ((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.random() < penetrationRate / 10000) {
                this.buffComp.ApplyBuff(false, (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                  error: Error()
                }), MyApp) : MyApp).lubanTables.TbGlobalAttr.BulletPenetrationFlagBuffID);
              }
            }
          } else if (collider.entity instanceof (_crd && PlaneBase === void 0 ? (_reportPossibleCrUseOfPlaneBase({
            error: Error()
          }), PlaneBase) : PlaneBase) && !collider.entity.enemy) {
            this.collisionPlane(collider.entity);
          }
        }
        /**
         * 准备移除敌机
         */


        willRemove() {}

        addBullet(bullet) {
          if (this.bullets) {
            this.bullets.push(bullet);
          }
        }
        /**
         * 从敌人移除子弹
         * @param {Bullet} bullet 子弹对象
         */


        removeBullet(bullet) {
          if (this.bullets) {
            const index = this.bullets.indexOf(bullet);

            if (index >= 0) {
              this.bullets.splice(index, 1);
            }
          }
        }

        setPos(x, y) {
          this.node.setPosition(x, y);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "planeParent", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=e4de7fa78e42e815e4d91deff8407c1ac3aadf2c.js.map
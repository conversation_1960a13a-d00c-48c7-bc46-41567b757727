System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10", "__unresolved_11"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, instantiate, JsonAsset, Node, Prefab, Sprite, SpriteFrame, UITransform, v2, MyApp, LayerSplicingMode, LevelDataTerrain, LevelEventRun, Tools, GameIns, logDebug, logError, logInfo, GameConst, LevelNodeCheckOutScreen, LevelUtils, EmittierStatus, EmittierTerrain, LevelLayer, _dec, _class, _crd, ccclass, SCROLL_POOL_NAME, TERRAIN_POOL_NAME, EMIITER_POOL_NAME, TerrainsNodeName, DynamicNodeName, ScrollsNodeName, EmittiersNodeName, jsonRootPath, LevelLayerUI;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/bundles/common/script/app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLayerSplicingMode(extras) {
    _reporterNs.report("LayerSplicingMode", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEvent(extras) {
    _reporterNs.report("LevelDataEvent", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataLayer(extras) {
    _reporterNs.report("LevelDataLayer", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataScroll(extras) {
    _reporterNs.report("LevelDataScroll", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataTerrain(extras) {
    _reporterNs.report("LevelDataTerrain", "db://assets/bundles/common/script/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelEventRun(extras) {
    _reporterNs.report("LevelEventRun", "./LevelEventRun", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "db://assets/bundles/common/script/game/utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "db://assets/bundles/common/script/game/GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogDebug(extras) {
    _reporterNs.report("logDebug", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogError(extras) {
    _reporterNs.report("logError", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogInfo(extras) {
    _reporterNs.report("logInfo", "db://assets/scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "db://assets/scripts/core/base/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelNodeCheckOutScreen(extras) {
    _reporterNs.report("LevelNodeCheckOutScreen", "./LevelNodeCheckOutScreen", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelUtils(extras) {
    _reporterNs.report("LevelUtils", "./LevelUtils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmittierStatus(extras) {
    _reporterNs.report("EmittierStatus", "db://assets/bundles/common/script/game/dyncTerrain/EmittierTerrain", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmittierTerrain(extras) {
    _reporterNs.report("EmittierTerrain", "db://assets/bundles/common/script/game/dyncTerrain/EmittierTerrain", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelLayer(extras) {
    _reporterNs.report("LevelLayer", "./LevelLayer", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      instantiate = _cc.instantiate;
      JsonAsset = _cc.JsonAsset;
      Node = _cc.Node;
      Prefab = _cc.Prefab;
      Sprite = _cc.Sprite;
      SpriteFrame = _cc.SpriteFrame;
      UITransform = _cc.UITransform;
      v2 = _cc.v2;
    }, function (_unresolved_2) {
      MyApp = _unresolved_2.MyApp;
    }, function (_unresolved_3) {
      LayerSplicingMode = _unresolved_3.LayerSplicingMode;
      LevelDataTerrain = _unresolved_3.LevelDataTerrain;
    }, function (_unresolved_4) {
      LevelEventRun = _unresolved_4.LevelEventRun;
    }, function (_unresolved_5) {
      Tools = _unresolved_5.Tools;
    }, function (_unresolved_6) {
      GameIns = _unresolved_6.GameIns;
    }, function (_unresolved_7) {
      logDebug = _unresolved_7.logDebug;
      logError = _unresolved_7.logError;
      logInfo = _unresolved_7.logInfo;
    }, function (_unresolved_8) {
      GameConst = _unresolved_8.GameConst;
    }, function (_unresolved_9) {
      LevelNodeCheckOutScreen = _unresolved_9.LevelNodeCheckOutScreen;
    }, function (_unresolved_10) {
      LevelUtils = _unresolved_10.LevelUtils;
    }, function (_unresolved_11) {
      EmittierStatus = _unresolved_11.EmittierStatus;
      EmittierTerrain = _unresolved_11.EmittierTerrain;
    }, function (_unresolved_12) {
      LevelLayer = _unresolved_12.LevelLayer;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "d139a2/Z9NEzIGUXE+qqxb8", "LevelLayerUI", undefined);

      __checkObsolete__(['_decorator', 'Component', 'instantiate', 'js', 'JsonAsset', 'log', 'Node', 'Prefab', 'Sprite', 'SpriteFrame', 'UITransform', 'v2', 'Vec2', 'view']);

      ({
        ccclass
      } = _decorator);
      SCROLL_POOL_NAME = "scroll_pool";
      TERRAIN_POOL_NAME = "terrain_pool";
      EMIITER_POOL_NAME = "emittier_pool";
      TerrainsNodeName = "terrains";
      DynamicNodeName = "dynamic";
      ScrollsNodeName = "scrolls";
      EmittiersNodeName = "emittiers";
      jsonRootPath = 'game/level/background/Prefab/Config/';

      _export("LevelLayerUI", LevelLayerUI = (_dec = ccclass('LevelLayerUI'), _dec(_class = class LevelLayerUI extends (_crd && LevelLayer === void 0 ? (_reportPossibleCrUseOfLevelLayer({
        error: Error()
      }), LevelLayer) : LevelLayer) {
        constructor() {
          super(...arguments);
          this._offSetY = 0;
          // 当前关卡的偏移量
          this.terrainsNode = null;
          this.dynamicNode = null;
          this.scrollsNode = null;
          this.emittiersNode = null;
          this._scorllData = null;
          this._scorllPrefabs = [];
          // 已经加载的
          this._lastScrollNodeHeight = 0;
          this._emittierInfo = [];
          this._inactiveEmittierNodes = [];
          // 未激活的发射器节点列表
          this._insEmittierLock = false;
          // 当前关卡的地形信息，用于动态实例化
          this._terrainsInfo = [];
          // 地形基础元素，单个terrain根据Y坐标排序（背景、滚动层、发射器单独处理）
          this._insTerrainLock = false;
          this.events = [];
          this.eventRunners = [];
        }

        onLoad() {}

        initByLevelData(data, offSetY, bFirstLoad) {
          var _this = this;

          return _asyncToGenerator(function* () {
            _this._offSetY = offSetY;

            _this.node.setPosition(0, offSetY, 0);

            _this.terrainsNode = (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
              error: Error()
            }), LevelUtils) : LevelUtils).getOrAddNode(_this.node, TerrainsNodeName);
            _this.dynamicNode = (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
              error: Error()
            }), LevelUtils) : LevelUtils).getOrAddNode(_this.node, DynamicNodeName);
            _this.scrollsNode = (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
              error: Error()
            }), LevelUtils) : LevelUtils).getOrAddNode(_this.node, ScrollsNodeName);
            _this.emittiersNode = (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
              error: Error()
            }), LevelUtils) : LevelUtils).getOrAddNode(_this.node, EmittiersNodeName);
            _this._scorllPrefabs = [];
            _this._terrainsInfo = [];
            _this._emittierInfo = [];
            _this._inactiveEmittierNodes = [];
            _this._insTerrainLock = false;
            _this._insEmittierLock = false; // 首关基础地形只加载一屏半的资源，剩余的动态加载
            // ------------------------------------------

            yield _this._initTerrainsByLevelData(data, bFirstLoad);
            yield _this._initDynamicsByLevelData(data, bFirstLoad);
            yield _this._initEmittierLevelData(data, bFirstLoad); // ------------------------------------------

            yield _this._initScorllsByLevelData(data, bFirstLoad);

            _this._sortTerrainsInfo();

            _this.events = [...data.events];

            _this.events.sort((a, b) => a.position.y - b.position.y);

            _this.events.forEach(event => {
              _this.eventRunners.push(new (_crd && LevelEventRun === void 0 ? (_reportPossibleCrUseOfLevelEventRun({
                error: Error()
              }), LevelEventRun) : LevelEventRun)(event, _this));
            });
          })();
        }

        _initTerrainsByLevelData(data, bFirstLoad) {
          var _this2 = this;

          return _asyncToGenerator(function* () {
            var _this2$node$parent;

            if (!data || _this2.terrainsNode === null || data.terrains.length === 0) {
              return;
            }

            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this2$node$parent = _this2.node.parent) == null || (_this2$node$parent = _this2$node$parent.parent) == null ? void 0 : _this2$node$parent.name) + ":" + _this2.node.name + " \u56FA\u5B9A\u5730\u5F62\u521D\u59CB\u5316\uFF1A" + data.remark);
            var zIndex = 0;

            for (var terrain of data.terrains) {
              if (bFirstLoad) {
                if (terrain.type.length > 0) {
                  if (terrain.type.endsWith('.json')) {
                    var _this2$node$parent2;

                    var json_path = jsonRootPath + terrain.type.replace('.json', '');
                    (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                      error: Error()
                    }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this2$node$parent2 = _this2.node.parent) == null || (_this2$node$parent2 = _this2$node$parent2.parent) == null ? void 0 : _this2$node$parent2.name) + ":" + _this2.node.name + "] jsonData : " + (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
                      error: Error()
                    }), LevelUtils) : LevelUtils).extractPathPart(json_path));
                    var jsonAsset = yield (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                      error: Error()
                    }), MyApp) : MyApp).resMgr.loadAsync(json_path, JsonAsset);
                    var jsonData = jsonAsset.json;

                    if (jsonData.terrains && Array.isArray(jsonData.terrains)) {
                      var _this2$node$parent3;

                      var subZIndex = 0;
                      var subParentNode = (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
                        error: Error()
                      }), LevelUtils) : LevelUtils).getOrAddNode(_this2.terrainsNode, "terr_" + zIndex);
                      (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                        error: Error()
                      }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this2$node$parent3 = _this2.node.parent) == null || (_this2$node$parent3 = _this2$node$parent3.parent) == null ? void 0 : _this2$node$parent3.name) + ":" + _this2.node.name + "] init terrain \u521B\u5EFA\u8282\u70B9 subParentNode: " + subParentNode.name + " \u7236\u8282\u70B9\uFF1A" + subParentNode.parent.name);
                      subParentNode.setSiblingIndex(zIndex);

                      for (var t of jsonData.terrains) {
                        var terrainData = Object.assign(new (_crd && LevelDataTerrain === void 0 ? (_reportPossibleCrUseOfLevelDataTerrain({
                          error: Error()
                        }), LevelDataTerrain) : LevelDataTerrain)(), t);
                        var terrainPosY = terrain.position.y + terrainData.position.y;

                        if (terrainPosY - terrainData.height / 2 + _this2.node.position.y <= (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                          error: Error()
                        }), GameConst) : GameConst).VIEWPORT_LOAD_POS) {
                          if (terrainData.type.endsWith('.png')) {
                            var _this2$node$parent4;

                            var png_path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                              error: Error()
                            }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                              error: Error()
                            }), MyApp) : MyApp).resMgr.defaultBundleName, terrainData.type.replace('.png', ''));
                            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                              error: Error()
                            }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this2$node$parent4 = _this2.node.parent) == null || (_this2$node$parent4 = _this2$node$parent4.parent) == null ? void 0 : _this2$node$parent4.name) + ":" + _this2.node.name + "] \u9759\u6001\u5143\u7D20(\u901A\u8FC7json\u914D\u7F6E) png : " + (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
                              error: Error()
                            }), LevelUtils) : LevelUtils).extractPathPart(png_path));
                            yield _this2._createPngNode(png_path, v2(terrain.position.x + terrainData.position.x, terrainPosY), v2(terrainData.scale.x, terrainData.scale.y), terrainData.rotation, subParentNode, subZIndex);
                          } else {
                            var _this2$node$parent5;

                            var path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                              error: Error()
                            }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                              error: Error()
                            }), MyApp) : MyApp).resMgr.defaultBundleName, terrainData.uuid);
                            var prefabNode = yield _this2._createTerrainPrefabNode(path, v2(terrain.position.x + terrainData.position.x, terrainPosY), v2(terrainData.scale.x, terrainData.scale.y), terrainData.rotation, subParentNode, subZIndex);
                            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                              error: Error()
                            }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this2$node$parent5 = _this2.node.parent) == null || (_this2$node$parent5 = _this2$node$parent5.parent) == null ? void 0 : _this2$node$parent5.name) + ":" + _this2.node.name + "] \u9759\u6001\u5143\u7D20(\u901A\u8FC7json\u914D\u7F6E) prefab : " + prefabNode.name);
                          }
                        } else {
                          terrainData.position.y = terrainPosY;
                          terrainData.position.x += terrain.position.x;
                          terrainData.subParentNode = subParentNode;

                          _this2._addTerrainInfo(terrainData, subParentNode, subZIndex);
                        }

                        subZIndex++;
                      }
                    } else {
                      (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
                        error: Error()
                      }), logError) : logError)("LevelLayerUI", "JSON data has no terrains array");
                    }
                  } else if (terrain.type.endsWith('.png')) {
                    if (terrain.position.y - terrain.height / 2 + _this2.node.position.y <= (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                      error: Error()
                    }), GameConst) : GameConst).VIEWPORT_LOAD_POS) {
                      var _this2$node$parent6;

                      var _png_path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                        error: Error()
                      }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                        error: Error()
                      }), MyApp) : MyApp).resMgr.defaultBundleName, terrain.type.replace('.png', ''));

                      yield _this2._createPngNode(_png_path, v2(terrain.position.x, terrain.position.y), v2(terrain.scale.x, terrain.scale.y), terrain.rotation, _this2.terrainsNode, zIndex);
                      (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                        error: Error()
                      }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this2$node$parent6 = _this2.node.parent) == null || (_this2$node$parent6 = _this2$node$parent6.parent) == null ? void 0 : _this2$node$parent6.name) + ":" + _this2.node.name + "] \u9759\u6001\u5143\u7D20 png : " + (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
                        error: Error()
                      }), LevelUtils) : LevelUtils).extractPathPart(_png_path));
                    } else {
                      _this2._addTerrainInfo(terrain, _this2.terrainsNode, zIndex);
                    }
                  }
                } else {
                  if (terrain.position.y - terrain.height / 2 + _this2.node.position.y <= (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                    error: Error()
                  }), GameConst) : GameConst).VIEWPORT_LOAD_POS) {
                    var _this2$node$parent7;

                    var _path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                      error: Error()
                    }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                      error: Error()
                    }), MyApp) : MyApp).resMgr.defaultBundleName, terrain.uuid);

                    var _prefabNode = yield _this2._createTerrainPrefabNode(_path, v2(terrain.position.x, terrain.position.y), v2(terrain.scale.x, terrain.scale.y), terrain.rotation, _this2.terrainsNode, zIndex);

                    (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                      error: Error()
                    }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this2$node$parent7 = _this2.node.parent) == null || (_this2$node$parent7 = _this2$node$parent7.parent) == null ? void 0 : _this2$node$parent7.name) + ":" + _this2.node.name + "] \u9759\u6001\u5143\u7D20 prefab Node: " + _prefabNode.name);
                  } else {
                    _this2._addTerrainInfo(terrain, _this2.terrainsNode, zIndex);
                  }
                }
              } else {
                _this2._addTerrainInfo(terrain, _this2.terrainsNode, zIndex);
              }

              zIndex++;
            }
          })();
        }

        _initDynamicsByLevelData(data, bFirstLoad) {
          var _this3 = this;

          return _asyncToGenerator(function* () {
            var _this3$node$parent;

            if (!data || _this3.dynamicNode === null || data.dynamics.length === 0) {
              return;
            }

            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this3$node$parent = _this3.node.parent) == null || (_this3$node$parent = _this3$node$parent.parent) == null ? void 0 : _this3$node$parent.name) + ":" + _this3.node.name + " \u968F\u673A\u5730\u5F62\u521D\u59CB\u5316\uFF1A" + data.remark);
            var zIndex = 0;

            var _loop = function* _loop() {
              var weights = [];
              dynamics.group.forEach(dynamic => {
                weights.push(dynamic.weight);
              });
              var dynaIndex = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).getRandomIndexByWeights(weights, (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.random());
              var dynamic = dynamics.group[dynaIndex];
              weights = [];
              dynamic.terrains.forEach(terrain => {
                weights.push(terrain.weight);
              });
              var terrainIndex = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).getRandomIndexByWeights(weights, (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.random());
              var terrain = dynamic.terrains[terrainIndex];
              var randomOffsetX = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.random() * (terrain.offSetX.max - terrain.offSetX.min) + terrain.offSetX.min;
              var randomOffsetY = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.random() * (terrain.offSetY.max - terrain.offSetY.min) + terrain.offSetY.min;

              if (!_this3.node.parent || !_this3.node.parent.parent) {
                console.log("LevelLayerUI", "this.node.parent || !this.node.parent.parent");
                return {
                  v: void 0
                };
              }

              if (bFirstLoad) {
                if (terrain.type.length > 0) {
                  var _this3$node$parent2;

                  (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                    error: Error()
                  }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this3$node$parent2 = _this3.node.parent) == null || (_this3$node$parent2 = _this3$node$parent2.parent) == null ? void 0 : _this3$node$parent2.name) + ":" + _this3.node.name + "] \u968F\u673A\u51FA\u7684\u5730\u5F62 : " + (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
                    error: Error()
                  }), LevelUtils) : LevelUtils).extractPathPart((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                    error: Error()
                  }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                    error: Error()
                  }), MyApp) : MyApp).resMgr.defaultBundleName, terrain.uuid)));

                  if (terrain.type.endsWith('.json')) {
                    var _this3$node$parent3;

                    var json_path = jsonRootPath + terrain.type.replace('.json', '');
                    (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                      error: Error()
                    }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this3$node$parent3 = _this3.node.parent) == null || (_this3$node$parent3 = _this3$node$parent3.parent) == null ? void 0 : _this3$node$parent3.name) + ":" + _this3.node.name + "] jsonData : " + (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
                      error: Error()
                    }), LevelUtils) : LevelUtils).extractPathPart(json_path));
                    var jsonAsset = yield (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                      error: Error()
                    }), MyApp) : MyApp).resMgr.loadAsync(json_path, JsonAsset);
                    var jsonData = jsonAsset.json;

                    if (jsonData.terrains && Array.isArray(jsonData.terrains)) {
                      var _this3$node$parent4;

                      var subZIndex = 0;
                      var subParentNode = (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
                        error: Error()
                      }), LevelUtils) : LevelUtils).getOrAddNode(_this3.dynamicNode, "dyna_" + zIndex);
                      (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                        error: Error()
                      }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this3$node$parent4 = _this3.node.parent) == null || (_this3$node$parent4 = _this3$node$parent4.parent) == null ? void 0 : _this3$node$parent4.name) + ":" + _this3.node.name + "] init dyna \u521B\u5EFA\u8282\u70B9 subParentNode: " + subParentNode.name + " \u7236\u8282\u70B9\uFF1A" + subParentNode.parent.name);
                      subParentNode.setSiblingIndex(zIndex);

                      for (var t of jsonData.terrains) {
                        var _this3$node$parent5;

                        var terrainData = Object.assign(new (_crd && LevelDataTerrain === void 0 ? (_reportPossibleCrUseOfLevelDataTerrain({
                          error: Error()
                        }), LevelDataTerrain) : LevelDataTerrain)(), t); //logDebug("LevelLayerUI", `jsonData.terrains: ${JSON.stringify(terrainData)}`);

                        var terrainPosY = dynamic.position.y + randomOffsetY + terrainData.position.y;
                        var curOffPosY = terrainPosY - terrainData.height / 2;
                        var curPosY = curOffPosY + _this3.node.position.y;
                        (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                          error: Error()
                        }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this3$node$parent5 = _this3.node.parent) == null || (_this3$node$parent5 = _this3$node$parent5.parent) == null ? void 0 : _this3$node$parent5.name) + ":" + _this3.node.name + "] \u7236\u8282\u70B9\u5750\u6807\uFF1A" + _this3.node.position.y + " \u5B50\u8282\u504F\u79FB\u5750\u6807\uFF1A" + curOffPosY);

                        if (curPosY <= (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                          error: Error()
                        }), GameConst) : GameConst).VIEWPORT_LOAD_POS) {
                          if (terrainData.type.endsWith('.png')) {
                            var _this3$node$parent6;

                            var png_path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                              error: Error()
                            }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                              error: Error()
                            }), MyApp) : MyApp).resMgr.defaultBundleName, terrainData.type.replace('.png', ''));
                            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                              error: Error()
                            }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this3$node$parent6 = _this3.node.parent) == null || (_this3$node$parent6 = _this3$node$parent6.parent) == null ? void 0 : _this3$node$parent6.name) + ":" + _this3.node.name + "] \u968F\u673A\u5143\u7D20(\u901A\u8FC7json\u914D\u7F6E) png : " + (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
                              error: Error()
                            }), LevelUtils) : LevelUtils).extractPathPart(png_path));
                            yield _this3._createPngNode(png_path, v2(dynamic.position.x + randomOffsetX + terrainData.position.x, terrainPosY), v2(terrainData.scale.x, terrainData.scale.y), terrainData.rotation, subParentNode, subZIndex);
                          } else {
                            var _this3$node$parent7;

                            var path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                              error: Error()
                            }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                              error: Error()
                            }), MyApp) : MyApp).resMgr.defaultBundleName, terrainData.uuid);
                            var prefabNode = yield _this3._createTerrainPrefabNode(path, v2(dynamic.position.x + randomOffsetX + terrainData.position.x, terrainPosY), v2(terrainData.scale.x, terrainData.scale.y), terrainData.rotation, subParentNode, subZIndex);
                            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                              error: Error()
                            }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this3$node$parent7 = _this3.node.parent) == null || (_this3$node$parent7 = _this3$node$parent7.parent) == null ? void 0 : _this3$node$parent7.name) + ":" + _this3.node.name + "] \u968F\u673A\u5143\u7D20(\u901A\u8FC7json\u914D\u7F6E) prefab : " + prefabNode.name);
                          }
                        } else {
                          _this3._addTerrainInfo({
                            uuid: terrainData.uuid,
                            type: terrainData.type,
                            height: terrainData.height,
                            position: v2(dynamic.position.x + randomOffsetX + terrainData.position.x, terrainPosY),
                            scale: v2(terrainData.scale.x, terrainData.scale.y),
                            rotation: terrainData.rotation
                          }, subParentNode, subZIndex);
                        }

                        subZIndex++;
                      }
                    } else {
                      (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
                        error: Error()
                      }), logError) : logError)("LevelLayerUI", "JSON data has no terrains array");
                    }
                  } else if (terrain.type.endsWith('.png')) {
                    if (dynamic.position.y - terrain.height / 2 + randomOffsetY + _this3.node.position.y <= (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                      error: Error()
                    }), GameConst) : GameConst).VIEWPORT_LOAD_POS) {
                      var _this3$node$parent8;

                      var _png_path2 = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                        error: Error()
                      }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                        error: Error()
                      }), MyApp) : MyApp).resMgr.defaultBundleName, terrain.type.replace('.png', ''));

                      (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                        error: Error()
                      }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this3$node$parent8 = _this3.node.parent) == null || (_this3$node$parent8 = _this3$node$parent8.parent) == null ? void 0 : _this3$node$parent8.name) + ":" + _this3.node.name + "] \u968F\u673A\u5143\u7D20 png : " + (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
                        error: Error()
                      }), LevelUtils) : LevelUtils).extractPathPart(_png_path2));
                      yield _this3._createPngNode(_png_path2, v2(dynamic.position.x + randomOffsetX, dynamic.position.y + randomOffsetY), v2(dynamic.scale.x, dynamic.scale.y), dynamic.rotation, _this3.dynamicNode, zIndex);
                    } else {
                      _this3._addTerrainInfo({
                        uuid: terrain.uuid,
                        type: terrain.type,
                        height: terrain.height,
                        position: v2(dynamic.position.x + randomOffsetX, dynamic.position.y + randomOffsetY),
                        scale: v2(dynamic.scale.x, dynamic.scale.y),
                        rotation: dynamic.rotation
                      }, _this3.dynamicNode, zIndex);
                    }
                  }
                } else {
                  if (dynamic.position.y + randomOffsetX - terrain.height / 2 + _this3.node.position.y <= (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                    error: Error()
                  }), GameConst) : GameConst).VIEWPORT_LOAD_POS) {
                    var _this3$node$parent9;

                    var _path2 = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                      error: Error()
                    }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                      error: Error()
                    }), MyApp) : MyApp).resMgr.defaultBundleName, terrain.uuid);

                    var _prefabNode2 = yield _this3._createTerrainPrefabNode(_path2, v2(dynamic.position.x + randomOffsetX, dynamic.position.y + randomOffsetY), v2(dynamic.scale.x, dynamic.scale.y), dynamic.rotation, _this3.dynamicNode, zIndex);

                    (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                      error: Error()
                    }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this3$node$parent9 = _this3.node.parent) == null || (_this3$node$parent9 = _this3$node$parent9.parent) == null ? void 0 : _this3$node$parent9.name) + ":" + _this3.node.name + "] \u968F\u673A\u5143\u7D20 prefab : " + _prefabNode2.name);
                  } else {
                    _this3._addTerrainInfo({
                      uuid: terrain.uuid,
                      type: '',
                      height: terrain.height,
                      position: v2(dynamic.position.x + randomOffsetX, dynamic.position.y + randomOffsetY),
                      scale: v2(dynamic.scale.x, dynamic.scale.y),
                      rotation: dynamic.rotation
                    }, _this3.dynamicNode, zIndex);
                  }
                }
              } else {
                _this3._addTerrainInfo({
                  uuid: terrain.uuid,
                  type: terrain.type,
                  height: terrain.height,
                  position: v2(dynamic.position.x + randomOffsetX, dynamic.position.y + randomOffsetY),
                  scale: v2(dynamic.scale.x, dynamic.scale.y),
                  rotation: dynamic.rotation
                }, _this3.dynamicNode, zIndex);
              }

              zIndex++;
            },
                _ret;

            for (var dynamics of data.dynamics) {
              _ret = yield* _loop();
              if (_ret) return _ret.v;
            }
          })();
        }

        _initEmittierLevelData(data, bFristLevel) {
          var _this4 = this;

          return _asyncToGenerator(function* () {
            if (!data || _this4.emittiersNode === null || data.emittiers.length === 0) {
              return;
            }

            var index = 0;

            for (var emittier of data.emittiers) {
              if (bFristLevel) {
                if (emittier.position.y - emittier.height / 2 + _this4.node.position.y < (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                  error: Error()
                }), GameConst) : GameConst).VIEWPORT_LOAD_POS) {
                  var _this4$node$parent;

                  var path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                    error: Error()
                  }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                    error: Error()
                  }), MyApp) : MyApp).resMgr.defaultBundleName, emittier.uuid);
                  var prefab = yield _this4._createEmittierNode(path, v2(emittier.position.x, emittier.position.y), v2(emittier.scale.x, emittier.scale.y), emittier.rotation, index);
                  (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                    error: Error()
                  }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this4$node$parent = _this4.node.parent) == null || (_this4$node$parent = _this4$node$parent.parent) == null ? void 0 : _this4$node$parent.name) + ":" + _this4.node.name + "] \u53D1\u5C04\u5668 prefab : " + prefab.name);
                } else {
                  _this4._addEmittierInfo(emittier);
                }
              } else {
                _this4._addEmittierInfo(emittier);
              }

              index++;
            }

            _this4._emittierInfo.sort((a, b) => {
              return a.position.y - b.position.y;
            });
          })();
        }

        _initScorllsByLevelData(data, bFristLevel) {
          var _this5 = this;

          return _asyncToGenerator(function* () {
            if (!data || _this5.scrollsNode === null || data.scrolls.length === 0) {
              return;
            } // 根据权重随机出一个滚动组


            var weights = [];
            data.scrolls.forEach(element => {
              weights.push(element.weight);
            });
            var srocllIndex = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).getRandomIndexByWeights(weights, (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.random());
            _this5._scorllData = data.scrolls[srocllIndex];

            for (var i = 0; i < _this5._scorllData.uuids.length; i++) {
              var path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                error: Error()
              }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                error: Error()
              }), MyApp) : MyApp).resMgr.defaultBundleName, _this5._scorllData.uuids[i]);
              var prefab = yield (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                error: Error()
              }), MyApp) : MyApp).resMgr.loadAsync(path, Prefab);

              _this5._scorllPrefabs.push(prefab);
            }

            var offSetY = 0;
            var halfHeight = 0;

            while (_this5._scorllPrefabs.length > 0 && offSetY - halfHeight < (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
              error: Error()
            }), GameConst) : GameConst).VIEWPORT_LOAD_POS) {
              var randomOffsetX = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.random() * (_this5._scorllData.offSetX.max - _this5._scorllData.offSetX.min) + _this5._scorllData.offSetX.min;

              var node = _this5._addScrollNode(randomOffsetX, offSetY);

              var nodeHeight = 0;

              if (_this5._scorllData.splicingMode === (_crd && LayerSplicingMode === void 0 ? (_reportPossibleCrUseOfLayerSplicingMode({
                error: Error()
              }), LayerSplicingMode) : LayerSplicingMode).node_height) {
                nodeHeight = node.getComponent(UITransform).contentSize.height;
              } else if (_this5._scorllData.splicingMode === (_crd && LayerSplicingMode === void 0 ? (_reportPossibleCrUseOfLayerSplicingMode({
                error: Error()
              }), LayerSplicingMode) : LayerSplicingMode).fix_height) {
                nodeHeight = 1334;
              } else if (_this5._scorllData.splicingMode === (_crd && LayerSplicingMode === void 0 ? (_reportPossibleCrUseOfLayerSplicingMode({
                error: Error()
              }), LayerSplicingMode) : LayerSplicingMode).random_height) {
                nodeHeight = Math.max(_this5._scorllData.offSetY.min, _this5._scorllData.offSetY.max) + node.getComponent(UITransform).contentSize.height;
              }

              offSetY += nodeHeight;
              halfHeight = nodeHeight / 2;
              _this5._lastScrollNodeHeight = nodeHeight;
            }
          })();
        }

        tick(deltaTime) {
          var posY = this.node.position.y;
          posY -= deltaTime * this.speed;
          this.node.setPosition(0, posY, 0); // 说明: event的激活，是从进入世界范围开始。
          // 又：因为游戏是从上往下滚动，因为我们用一个固定大小变量定义上边界，一旦进入这个范围，就激活。

          var scrollY = -posY + (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).VIEWPORT_TOP; // console.log('LevelLayerUI tick scrollY', scrollY, -posY, this._offSetY, GameConst.VIEWPORT_TOP);

          for (var i = 0; i < this.eventRunners.length; i++) {
            var eventRunner = this.eventRunners[i];
            eventRunner.tick(scrollY);

            if (eventRunner.isTriggered) {
              // 条件已触发
              this.eventRunners.splice(i, 1);
              i--;
            }
          }

          if (!this._insTerrainLock) {
            this._checkDynaTerrain(posY);
          }

          this._checkScroll();

          if (this._insEmittierLock) {
            this._checkEmittier(posY);
          }
        }

        _sortTerrainsInfo() {
          this._terrainsInfo.sort((a, b) => {
            return a.data.position.y - b.data.position.y;
          });

          (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
            error: Error()
          }), logInfo) : logInfo)("LevelLayerUI", this.node.name + " \u5730\u5F62\u5143\u7D20\u6570\u91CF: " + this._terrainsInfo.length);
          /*this._terrainsInfo.forEach((terrain, index) => {
              logInfo("LevelLayerUI", `[${index}] Y坐标: ${terrain.data.position.y} 元素类型：${terrain.parentNode} uuid:${terrain.data.uuid} json:${terrain.data.type}`);
          });*/
        }

        _addTerrainInfo(terrainData, parentNode, zIndex) {
          var _this$node$parent;

          var attr = '';
          var elemName = '';

          if (terrainData.type.length > 0) {
            attr = '图片';
            elemName = (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
              error: Error()
            }), LevelUtils) : LevelUtils).extractPathPart((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.defaultBundleName, terrainData.type.replace('.png', '')));
          } else {
            attr = '预制体';
            elemName = (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
              error: Error()
            }), LevelUtils) : LevelUtils).extractPathPart((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.defaultBundleName, terrainData.uuid), '');
          }

          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
            error: Error()
          }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this$node$parent = this.node.parent) == null || (_this$node$parent = _this$node$parent.parent) == null ? void 0 : _this$node$parent.name) + ":" + this.node.name + "] \u6DFB\u52A0\u5230\u9884\u52A0\u8F7D\u5730\u5F62\u7EC4 \u7C7B\u578B: " + attr + " \u5143\u7D20: " + elemName + "  \u7236\u8282\u70B9\uFF1A" + parentNode.name + "  zIndex: " + zIndex);

          this._terrainsInfo.push({
            data: terrainData,
            parentNode: parentNode,
            zIndex: zIndex
          });
        }

        _addEmittierInfo(terrainData) {
          var _this$node$parent2;

          this._emittierInfo.push(terrainData);

          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
            error: Error()
          }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this$node$parent2 = this.node.parent) == null || (_this$node$parent2 = _this$node$parent2.parent) == null ? void 0 : _this$node$parent2.name) + ":" + this.node.name + "] \u6DFB\u52A0\u5230\u9884\u52A0\u8F7D\u53D1\u5C04\u5668\u7EC4 \u5143\u7D20: " + (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
            error: Error()
          }), LevelUtils) : LevelUtils).extractPathPart((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.defaultBundleName, terrainData.uuid)));
        } // 动态实例化场景元素，当元素的位置在一个屏幕以上位置时，就实例化


        _checkDynaTerrain(curPosY) {
          var _this6 = this;

          return _asyncToGenerator(function* () {
            if (!_this6.node.parent || !_this6.node.parent.parent) return;
            if (_this6._insTerrainLock) return;
            _this6._insTerrainLock = true;

            try {
              var indicesToRemove = [];
              var newTerrainsInfo = [];

              for (var i = 0; i < _this6._terrainsInfo.length; i++) {
                var terrainInfo = _this6._terrainsInfo[i]; // 因为列表排过序，先简单判断一次只要保存的元素第一个不在预判屏幕内，就跳出循环，因为后续的元素也都不会在预判屏幕内
                // 逻辑后面会根据每个节点的高度再仔细判断一次

                if (terrainInfo.data.position.y - terrainInfo.data.height / 2 + curPosY > (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                  error: Error()
                }), GameConst) : GameConst).VIEWPORT_LOAD_POS) {
                  break;
                }

                if (terrainInfo.data.type.length > 0) {
                  if (terrainInfo.data.type.endsWith('.json')) {
                    var json_path = jsonRootPath + terrainInfo.data.type.replace('.json', '');
                    var jsonAsset = yield (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                      error: Error()
                    }), MyApp) : MyApp).resMgr.loadAsync(json_path, JsonAsset);
                    var jsonData = jsonAsset.json;

                    if (jsonData.terrains && Array.isArray(jsonData.terrains)) {
                      var _this6$node$parent;

                      var subZIndex = 0;
                      var subParentNode = (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
                        error: Error()
                      }), LevelUtils) : LevelUtils).getOrAddNode(terrainInfo.parentNode, "dyna_" + terrainInfo.zIndex);
                      (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                        error: Error()
                      }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this6$node$parent = _this6.node.parent) == null || (_this6$node$parent = _this6$node$parent.parent) == null ? void 0 : _this6$node$parent.name) + ":" + _this6.node.name + "] _instantiate \u521B\u5EFA\u8282\u70B9 subParentNode: " + subParentNode.name + " \u7236\u8282\u70B9\uFF1A" + subParentNode.parent.name);
                      subParentNode.setSiblingIndex(terrainInfo.zIndex);

                      for (var t of jsonData.terrains) {
                        var terrainData = Object.assign(new (_crd && LevelDataTerrain === void 0 ? (_reportPossibleCrUseOfLevelDataTerrain({
                          error: Error()
                        }), LevelDataTerrain) : LevelDataTerrain)(), t);

                        if (terrainInfo.data.position.y + terrainData.position.y - terrainData.height / 2 + curPosY <= (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                          error: Error()
                        }), GameConst) : GameConst).VIEWPORT_LOAD_POS) {
                          if (terrainData.type.endsWith('.png')) {
                            var _this6$node$parent2;

                            var png_path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                              error: Error()
                            }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                              error: Error()
                            }), MyApp) : MyApp).resMgr.defaultBundleName, terrainData.type.replace('.png', ''));
                            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                              error: Error()
                            }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this6$node$parent2 = _this6.node.parent) == null || (_this6$node$parent2 = _this6$node$parent2.parent) == null ? void 0 : _this6$node$parent2.name) + ":" + _this6.node.name + "] \u968F\u673A\u5143\u7D20 png \u8DEF\u5F84: " + png_path + " index: " + i);
                            yield _this6._createPngNode(png_path, v2(terrainInfo.data.position.x + terrainData.position.x, terrainInfo.data.position.y + terrainData.position.y), v2(terrainData.scale.x, terrainData.scale.y), terrainData.rotation, subParentNode, subZIndex);
                          } else {
                            var _this6$node$parent3;

                            var path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                              error: Error()
                            }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                              error: Error()
                            }), MyApp) : MyApp).resMgr.defaultBundleName, terrainData.uuid);
                            var prefabNode = yield _this6._createTerrainPrefabNode(path, v2(terrainInfo.data.position.x + terrainData.position.x, terrainInfo.data.position.y + terrainData.position.y), v2(terrainData.scale.x, terrainData.scale.y), terrainData.rotation, subParentNode, subZIndex);
                            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                              error: Error()
                            }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this6$node$parent3 = _this6.node.parent) == null || (_this6$node$parent3 = _this6$node$parent3.parent) == null ? void 0 : _this6$node$parent3.name) + ":" + _this6.node.name + "] \u5B9E\u65F6\u521B\u5EFA\u5143\u7D20(\u901A\u8FC7json\u914D\u7F6E) prefab Node: " + prefabNode.name + " index: " + i);
                          }
                        } else {
                          var _this6$node$parent4;

                          var _name = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                            error: Error()
                          }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                            error: Error()
                          }), MyApp) : MyApp).resMgr.defaultBundleName, terrainData.uuid);

                          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                            error: Error()
                          }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this6$node$parent4 = _this6.node.parent) == null || (_this6$node$parent4 = _this6$node$parent4.parent) == null ? void 0 : _this6$node$parent4.name) + ":" + _this6.node.name + "] push TerrainsInfo name: " + _name + " parentName: " + subParentNode.name + " index: " + i + " subZIndex: " + subZIndex);
                          newTerrainsInfo.push({
                            data: {
                              uuid: terrainData.uuid,
                              type: terrainData.type,
                              height: terrainData.height,
                              position: v2(terrainInfo.data.position.x + terrainData.position.x, terrainInfo.data.position.y + terrainData.position.y),
                              scale: v2(terrainData.scale.x, terrainData.scale.y),
                              rotation: terrainData.rotation
                            },
                            parentNode: subParentNode,
                            zIndex: subZIndex
                          });
                        }

                        subZIndex++;
                      }

                      indicesToRemove.push(i);
                    }
                  } else if (terrainInfo.data.type.endsWith('.png')) {
                    if (terrainInfo.data.position.y - terrainInfo.data.height / 2 + curPosY <= (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                      error: Error()
                    }), GameConst) : GameConst).VIEWPORT_LOAD_POS) {
                      var _png_path3 = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                        error: Error()
                      }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                        error: Error()
                      }), MyApp) : MyApp).resMgr.defaultBundleName, terrainInfo.data.type.replace('.png', ''));

                      yield _this6._createPngNode(_png_path3, v2(terrainInfo.data.position.x, terrainInfo.data.position.y), v2(terrainInfo.data.scale.x, terrainInfo.data.scale.y), terrainInfo.data.rotation, terrainInfo.parentNode, terrainInfo.zIndex);
                      indicesToRemove.push(i);
                    }
                  }
                } else {
                  if (terrainInfo.data.position.y - terrainInfo.data.height / 2 + curPosY <= (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                    error: Error()
                  }), GameConst) : GameConst).VIEWPORT_LOAD_POS) {
                    var _this6$node$parent5;

                    var _path3 = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                      error: Error()
                    }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                      error: Error()
                    }), MyApp) : MyApp).resMgr.defaultBundleName, terrainInfo.data.uuid);

                    var _prefabNode3 = yield _this6._createTerrainPrefabNode(_path3, v2(terrainInfo.data.position.x, terrainInfo.data.position.y), v2(terrainInfo.data.scale.x, terrainInfo.data.scale.y), terrainInfo.data.rotation, terrainInfo.parentNode, terrainInfo.zIndex);

                    (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                      error: Error()
                    }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this6$node$parent5 = _this6.node.parent) == null || (_this6$node$parent5 = _this6$node$parent5.parent) == null ? void 0 : _this6$node$parent5.name) + ":" + _this6.node.name + "] \u5B9E\u65F6\u521B\u5EFA\u5143\u7D20 prefab Node: " + _prefabNode3.name + " \u7236\u8282\u70B9\uFF1A" + terrainInfo.parentNode.name + " \u8282\u70B9\u987A\u5E8F\uFF1A" + terrainInfo.zIndex);
                    indicesToRemove.push(i);
                  }
                }
              }

              for (var _i = indicesToRemove.length - 1; _i >= 0; _i--) {
                _this6._terrainsInfo.splice(indicesToRemove[_i], 1);
              }

              if (newTerrainsInfo.length > 0) {
                _this6._terrainsInfo.push(...newTerrainsInfo);

                _this6._sortTerrainsInfo();
              }
            } catch (error) {
              (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
                error: Error()
              }), logError) : logError)("LevelLayerUI", "_instantiate Terrain error: " + error);
            } finally {
              _this6._insTerrainLock = false;
            }
          })();
        }

        _checkScroll() {
          if (!this.scrollsNode || this._scorllPrefabs.length === 0) return;
          var lastChild = this.scrollsNode.children[this.scrollsNode.children.length - 1];
          var lastChildTransform = lastChild.getComponent(UITransform);
          if (!lastChildTransform) return;
          var lastChildTop = this.node.position.y + lastChild.position.y + this._lastScrollNodeHeight / 2;

          if (lastChildTop < (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).VIEWPORT_TOP) {
            var newY = lastChild.position.y + this._lastScrollNodeHeight;

            var randomOffsetX = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.random() * (this._scorllData.offSetX.max - this._scorllData.offSetX.min) + this._scorllData.offSetX.min;

            var newNode = this._addScrollNode(randomOffsetX, newY);

            var nodeHeight = 0;

            if (this._scorllData.splicingMode === (_crd && LayerSplicingMode === void 0 ? (_reportPossibleCrUseOfLayerSplicingMode({
              error: Error()
            }), LayerSplicingMode) : LayerSplicingMode).node_height) {
              nodeHeight = newNode.getComponent(UITransform).contentSize.height;
            } else if (this._scorllData.splicingMode === (_crd && LayerSplicingMode === void 0 ? (_reportPossibleCrUseOfLayerSplicingMode({
              error: Error()
            }), LayerSplicingMode) : LayerSplicingMode).fix_height) {
              nodeHeight = 1334;
            } else if (this._scorllData.splicingMode === (_crd && LayerSplicingMode === void 0 ? (_reportPossibleCrUseOfLayerSplicingMode({
              error: Error()
            }), LayerSplicingMode) : LayerSplicingMode).random_height) {
              nodeHeight = Math.max(this._scorllData.offSetY.min, this._scorllData.offSetY.max) + newNode.getComponent(UITransform).contentSize.height;
            }

            this._lastScrollNodeHeight = nodeHeight;
          }
        }

        _checkEmittier(posY) {
          var _this7 = this;

          return _asyncToGenerator(function* () {
            if (!_this7.emittiersNode || _this7._emittierInfo.length === 0) return;
            if (!_this7._insEmittierLock) return;
            _this7._insEmittierLock = true;

            try {
              var indicesToRemove = [];

              for (var i = 0; i < _this7._emittierInfo.length; i++) {
                var emittierInfo = _this7._emittierInfo[i];

                if (emittierInfo.position.y - emittierInfo.height / 2 + posY > (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                  error: Error()
                }), GameConst) : GameConst).VIEWPORT_LOAD_POS) {
                  break;
                } else {
                  var _this7$node$parent;

                  var path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                    error: Error()
                  }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                    error: Error()
                  }), MyApp) : MyApp).resMgr.defaultBundleName, emittierInfo.uuid);
                  var emittierNode = yield _this7._createEmittierNode(path, v2(emittierInfo.position.x, emittierInfo.position.y), v2(emittierInfo.scale.x, emittierInfo.scale.y), emittierInfo.rotation, i);
                  (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                    error: Error()
                  }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this7$node$parent = _this7.node.parent) == null || (_this7$node$parent = _this7$node$parent.parent) == null ? void 0 : _this7$node$parent.name) + ":" + _this7.node.name + "] \u5B9E\u65F6\u521B\u5EFA\u53D1\u5C04\u5668 prefab Node: " + emittierNode.name + " \u7236\u8282\u70B9\uFF1A" + _this7.emittiersNode.name + " \u8282\u70B9\u987A\u5E8F\uFF1A" + i);
                  indicesToRemove.push(i);
                }
              }

              for (var _i2 = indicesToRemove.length - 1; _i2 >= 0; _i2--) {
                _this7._emittierInfo.splice(indicesToRemove[_i2], 1);
              }
            } catch (error) {
              (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
                error: Error()
              }), logError) : logError)("LevelLayerUI", "_checkEmittier error: " + error);
            } finally {
              _this7._insEmittierLock = false;
            }

            for (var _i3 = _this7._inactiveEmittierNodes.length - 1; _i3 >= 0; _i3--) {
              var node = _this7._inactiveEmittierNodes[_i3];

              if (!node.isValid) {
                _this7._inactiveEmittierNodes.splice(_i3, 1);

                continue;
              }

              var comp = node.getComponent(_crd && EmittierTerrain === void 0 ? (_reportPossibleCrUseOfEmittierTerrain({
                error: Error()
              }), EmittierTerrain) : EmittierTerrain);

              if (!comp) {
                _this7._inactiveEmittierNodes.splice(_i3, 1);

                continue;
              }

              if (comp.status === (_crd && EmittierStatus === void 0 ? (_reportPossibleCrUseOfEmittierStatus({
                error: Error()
              }), EmittierStatus) : EmittierStatus).inactive && posY + node.position.y <= (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                error: Error()
              }), GameConst) : GameConst).VIEWPORT_TOP) {
                comp.startEmittier();

                if (comp.follow === false) {
                  _this7.speed = 0;
                }

                _this7._inactiveEmittierNodes.splice(_i3, 1);
              }
            }
          })();
        }
        /**
         * 创建并配置 PNG 类型的地形节点
         * @param spriteFramePath - SpriteFrame 资源路径
         * @param position - 节点位置
         * @param scale - 节点缩放
         * @param rotation - 节点旋转角度
         * @param parentNode - 父节点
         * @param zIndex - 在父节点中的层级顺序
         * @returns 返回创建好的地形节点
         */


        _createPngNode(spriteFramePath, position, scale, rotation, parentNode, zIndex) {
          var _this8 = this;

          return _asyncToGenerator(function* () {
            if (!spriteFramePath.endsWith('/spriteFrame')) {
              var _this8$node$parent;

              (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
                error: Error()
              }), logError) : logError)("LevelLayerUI", "[" + ((_this8$node$parent = _this8.node.parent) == null || (_this8$node$parent = _this8$node$parent.parent) == null ? void 0 : _this8$node$parent.name) + ":" + _this8.node.name + "] \u975E\u6807\u51C6 png \u8DEF\u5F84: " + spriteFramePath);
              return null;
            }

            var name = (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
              error: Error()
            }), LevelUtils) : LevelUtils).extractPathPart(spriteFramePath);
            var terrainNode = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gameMapManager.mapObjectPoolManager.get(TERRAIN_POOL_NAME, name);

            if (terrainNode) {
              var _this8$node$parent2;

              // 2. 如果从对象池获取到节点，直接配置属性
              (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                error: Error()
              }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this8$node$parent2 = _this8.node.parent) == null || (_this8$node$parent2 = _this8$node$parent2.parent) == null ? void 0 : _this8$node$parent2.name) + ":" + _this8.node.name + "] \u4ECE\u5BF9\u8C61\u6C60" + TERRAIN_POOL_NAME + "\u83B7\u53D6 PNG \u5143\u7D20\u8282\u70B9: " + name);
            } else {
              var _this8$node$parent3;

              var spriteFrame = yield (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                error: Error()
              }), MyApp) : MyApp).resMgr.loadAsync(spriteFramePath, SpriteFrame);
              (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                error: Error()
              }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this8$node$parent3 = _this8.node.parent) == null || (_this8$node$parent3 = _this8$node$parent3.parent) == null ? void 0 : _this8$node$parent3.name) + ":" + _this8.node.name + "] \u521B\u5EFA PNG \u5143\u7D20\u8282\u70B9: " + name + " \u7236\u8282\u70B9\uFF1A" + parentNode.name + " \u8282\u70B9\u987A\u5E8F\uFF1A" + zIndex);
              terrainNode = new Node();
              terrainNode.name = name;
              var terrainSprite = terrainNode.addComponent(Sprite);
              terrainSprite.spriteFrame = spriteFrame;
              var uiTransform = terrainNode.getComponent(UITransform);

              if (uiTransform && spriteFrame) {
                var size = spriteFrame.originalSize;
                uiTransform.setContentSize(size.width, size.height); //logDebug("LevelLayerUI", `PNG 尺寸: ${size.width}x${size.height}`);
              }

              var checkOut = terrainNode.addComponent(_crd && LevelNodeCheckOutScreen === void 0 ? (_reportPossibleCrUseOfLevelNodeCheckOutScreen({
                error: Error()
              }), LevelNodeCheckOutScreen) : LevelNodeCheckOutScreen);
              checkOut.init(TERRAIN_POOL_NAME);
            }

            terrainNode.setPosition(position.x, position.y, 0);
            terrainNode.setScale(scale.x, scale.y);
            terrainNode.setRotationFromEuler(0, 0, rotation);
            parentNode.addChild(terrainNode);
            terrainNode.setSiblingIndex(zIndex);
            return terrainNode;
          })();
        }
        /**
         * 创建并配置 Prefab 类型的地形节点
         * @param prefabPath - Prefab 资源路径
         * @param position - 节点位置
         * @param scale - 节点缩放
         * @param rotation - 节点旋转角度
         * @param parentNode - 父节点
         * @param zIndex - 在父节点中的层级顺序
         * @returns 返回创建好的地形节点
         */


        _createTerrainPrefabNode(prefabPath, position, scale, rotation, parentNode, zIndex) {
          var _this9 = this;

          return _asyncToGenerator(function* () {
            var name = (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
              error: Error()
            }), LevelUtils) : LevelUtils).extractPathPart(prefabPath, '');
            var terrainNode = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gameMapManager.mapObjectPoolManager.get(TERRAIN_POOL_NAME, name);

            if (!terrainNode) {
              var _this9$node$parent;

              var prefab = yield (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                error: Error()
              }), MyApp) : MyApp).resMgr.loadAsync(prefabPath, Prefab);
              terrainNode = instantiate(prefab);
              var checkOut = terrainNode.addComponent(_crd && LevelNodeCheckOutScreen === void 0 ? (_reportPossibleCrUseOfLevelNodeCheckOutScreen({
                error: Error()
              }), LevelNodeCheckOutScreen) : LevelNodeCheckOutScreen);
              checkOut.init(TERRAIN_POOL_NAME);
              (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                error: Error()
              }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this9$node$parent = _this9.node.parent) == null || (_this9$node$parent = _this9$node$parent.parent) == null ? void 0 : _this9$node$parent.name) + ":" + _this9.node.name + "] \u521B\u5EFA Prefab \u5143\u7D20\u8282\u70B9: " + name + " \u7236\u8282\u70B9\uFF1A" + parentNode.name + " \u8282\u70B9\u987A\u5E8F\uFF1A" + zIndex);
            } else {
              var _this9$node$parent2;

              (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                error: Error()
              }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this9$node$parent2 = _this9.node.parent) == null || (_this9$node$parent2 = _this9$node$parent2.parent) == null ? void 0 : _this9$node$parent2.name) + ":" + _this9.node.name + "] \u4ECE\u5BF9\u8C61\u6C60" + TERRAIN_POOL_NAME + "\u83B7\u53D6 Prefab \u5143\u7D20\u8282\u70B9: " + name);
            }

            terrainNode.setPosition(position.x, position.y, 0);
            terrainNode.setScale(scale.x, scale.y);
            terrainNode.setRotationFromEuler(0, 0, rotation);
            parentNode.addChild(terrainNode);
            terrainNode.setSiblingIndex(zIndex);
            return terrainNode;
          })();
        }
        /**
         * 创建并配置 Prefab 类型的地形节点
         * @param prefabPath - Prefab 资源路径
         * @param position - 节点位置
         * @param scale - 节点缩放
         * @param rotation - 节点旋转角度
         * @param zIndex - 在父节点中的层级顺序
         * @returns 返回创建好的地形节点
         */


        _createEmittierNode(prefabPath, position, scale, rotation, zIndex) {
          var _this10 = this;

          return _asyncToGenerator(function* () {
            var name = (_crd && LevelUtils === void 0 ? (_reportPossibleCrUseOfLevelUtils({
              error: Error()
            }), LevelUtils) : LevelUtils).extractPathPart(prefabPath, '');
            var emittierNode = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).gameMapManager.mapObjectPoolManager.get(EMIITER_POOL_NAME, name);

            if (!emittierNode) {
              var _this10$node$parent;

              var prefab = yield (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                error: Error()
              }), MyApp) : MyApp).resMgr.loadAsync(prefabPath, Prefab);
              emittierNode = instantiate(prefab);
              var emittierTerrain = emittierNode.addComponent(_crd && EmittierTerrain === void 0 ? (_reportPossibleCrUseOfEmittierTerrain({
                error: Error()
              }), EmittierTerrain) : EmittierTerrain);
              emittierTerrain.init(EMIITER_POOL_NAME);
              (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                error: Error()
              }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this10$node$parent = _this10.node.parent) == null || (_this10$node$parent = _this10$node$parent.parent) == null ? void 0 : _this10$node$parent.name) + ":" + _this10.node.name + "] \u521B\u5EFA\u53D1\u5C04\u5668\u8282\u70B9: " + name + " \u7236\u8282\u70B9\uFF1A" + _this10.emittiersNode.name + " \u8282\u70B9\u987A\u5E8F\uFF1A" + zIndex);
            } else {
              var _this10$node$parent2;

              (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                error: Error()
              }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this10$node$parent2 = _this10.node.parent) == null || (_this10$node$parent2 = _this10$node$parent2.parent) == null ? void 0 : _this10$node$parent2.name) + ":" + _this10.node.name + "] \u4ECE\u5BF9\u8C61\u6C60" + EMIITER_POOL_NAME + "\u83B7\u53D6\u53D1\u5C04\u5668\u8282\u70B9: " + name);
            }

            emittierNode.setPosition(position.x, position.y, 0);
            emittierNode.setScale(scale.x, scale.y);
            emittierNode.setRotationFromEuler(0, 0, rotation);

            _this10.emittiersNode.addChild(emittierNode);

            emittierNode.setSiblingIndex(zIndex);
            return emittierNode;
          })();
        }

        _addScrollNode(xPos, yPos) {
          var index = this.scrollsNode.children.length % this._scorllPrefabs.length;
          var prefab = this._scorllPrefabs[index];
          var prefabName = prefab.name;
          var node = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameMapManager.mapObjectPoolManager.get(SCROLL_POOL_NAME, prefabName);

          if (!node) {
            node = instantiate(this._scorllPrefabs[index]);
            var checkOut = node.addComponent(_crd && LevelNodeCheckOutScreen === void 0 ? (_reportPossibleCrUseOfLevelNodeCheckOutScreen({
              error: Error()
            }), LevelNodeCheckOutScreen) : LevelNodeCheckOutScreen);
            checkOut.init(SCROLL_POOL_NAME);
          } else {
            var _this$node$parent3;

            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)("LevelLayerUI", "[" + ((_this$node$parent3 = this.node.parent) == null || (_this$node$parent3 = _this$node$parent3.parent) == null ? void 0 : _this$node$parent3.name) + ":" + this.node.name + "] \u4ECE\u5BF9\u8C61\u6C60" + SCROLL_POOL_NAME + "\u83B7\u53D6 Prefab \u5143\u7D20\u8282\u70B9: " + name);
          }

          this.scrollsNode.addChild(node);
          node.setPosition(xPos, yPos, 0);
          return node;
        }

        getEventByElemID(elemID) {
          for (var event of this.events) {
            if (event.elemID == elemID) {
              return event;
            }
          }

          return null;
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=928ab71a9883ce6ff25bdd946042179d19325cc0.js.map
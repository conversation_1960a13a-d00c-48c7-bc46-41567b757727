import { _decorator, Label, Node } from 'cc';
import { BaseUI, UILayer, UIMgr, UIOpt } from 'db://assets/scripts/core/base/UIMgr';
import { GameIns } from '../../../game/GameIns';
import { BundleName } from '../../../const/BundleConst';

const { ccclass, property } = _decorator;

@ccclass('GameReviveUI')
export class GameReviveUI extends BaseUI {

    @property(Label)
    labTime: Label | null = null;

    private _countdown: number = 7; // 倒计时初始值
    private _countdownInterval: ReturnType<typeof setInterval> | null = null; // 用于存储计时器 ID

    public static getUrl(): string { return "prefab/GameReviveUI"; }
    public static getLayer(): UILayer { return UILayer.Default; }
    public static getUIOption(): UIOpt { return { isClickBgCloseUI: false }; }
    public static getBundleName(): string { return BundleName.GameFight }

    protected onLoad(): void {}

    async closeUI() {
        UIMgr.closeUI(GameReviveUI);
    }

    async onShow(): Promise<void> {
        // this.startCountdown(); // 开始倒计时
    }

    async onHide(): Promise<void> {
        this.stopCountdown(); // 停止倒计时
    }

    async onClose(): Promise<void> {

    }

    protected onDestroy(): void {

    }

    onBtnReviveClicked() {
        GameIns.battleManager.relifeBattle();
        this.closeUI();
    }

    onBtnAdClicked() {
        this.closeUI();
        GameIns.battleManager.relifeBattle();
    }
    onBtnCloseClicked() {
        this.quitBattle();
    }

    // 开始倒计时
    private startCountdown(): void {
        this._countdown = 7; // 初始化倒计时
        this.updateCountdownLabel(); // 更新初始显示

        this._countdownInterval = setInterval(() => {
            this._countdown--;
            this.updateCountdownLabel();

            if (this._countdown <= 0) {
                this.stopCountdown();
                this.onCountdownFinished();
            }
        }, 1000); // 每秒更新一次
    }

    // 停止倒计时
    private stopCountdown(): void {
        if (this._countdownInterval !== null) {
            clearInterval(this._countdownInterval);
            this._countdownInterval = null;
        }
    }

    // 更新倒计时文本
    private updateCountdownLabel(): void {
        if (this.labTime) {
            this.labTime.string = `${this._countdown}秒`;
        }
    }

    // 倒计时结束时的逻辑
    private onCountdownFinished(): void {
        this.quitBattle(); // 关闭界面
        
    }

    quitBattle() {
        this.closeUI();
        GameIns.battleManager.quitBattle();
    }
}
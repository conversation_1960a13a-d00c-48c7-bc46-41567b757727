{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/plane/event/EventGroupCom.ts"], "names": ["EnemyEventGroupContext", "EnemyEventInstance", "EnemyConditionFactory", "EnemyActionFactory", "EventGroupComp", "_decorator", "BaseComp", "EventGroupBase", "Condition<PERSON><PERSON><PERSON>", "EnemyConditionData", "eEnemyCondition", "EnemyActionData", "eEnemyAction", "enemy_cond", "enemy_act", "GameIns", "ccclass", "property", "<PERSON><PERSON><PERSON>", "plane", "reset", "EnemyEventGroupData", "displayName", "tooltip", "type", "buildConditions", "chain", "data", "conditions", "for<PERSON>ach", "condData", "index", "condition", "create", "onLoad", "context", "push", "buildActions", "actions", "map", "actionData", "action", "ElapsedTime", "EnemyCondition_ElapsedTime", "Pos_X", "EnemyCondition_Pos_X", "Pos_Y", "EnemyCondition_Pos_Y", "CurHP", "EnemyCondition_CurHP", "CurHPPercent", "EnemyCondition_CurHPPercent", "Speed", "EnemyCondition_Speed", "SpeedAngle", "EnemyCondition_SpeedAngle", "Acceleration", "EnemyCondition_Acceleration", "AccelerationAngle", "EnemyCondition_AccelerationAngle", "DistanceToPlayer", "EnemyCondition_DistanceToPlayer", "AngleToPlayer", "EnemyCondition_AngleToPlayer", "Player_Pos_X", "EnemyCondition_Player_Pos_X", "Player_Pos_Y", "EnemyCondition_Player_Pos_Y", "Player_CurHPPercent", "EnemyCondition_Player_CurHPPercent", "Level_ElapsedTime", "EnemyCondition_Level_ElapsedTime", "Level_InfLevel", "EnemyCondition_Level_InfLevel", "Level_ChallengeLevelSection", "EnemyCondition_Level_ChallengeLevelSection", "SelectLevel", "EnemyCondition_SelectLevel", "ImmuneBulletDamage", "EnemyCondition_ImmuneBulletDamage", "ImmuneCollideDamage", "EnemyCondition_ImmuneCollideDamage", "IgnoreBullet", "EnemyCondition_IgnoreBullet", "IgnoreCollide", "EnemyCondition_IgnoreCollide", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "EnemyCondition_ImmuneNuke", "ImmuneActiveSkill", "EnemyCondition_ImmuneActiveSkill", "Invincible", "EnemyCondition_Invincible", "Error", "EnemyAction_Speed", "EnemyAction_SpeedAngle", "EnemyAction_Acceleration", "EnemyAction_AccelerationAngle", "Color_R", "EnemyAction_Color_R", "Color_G", "EnemyAction_Color_G", "Color_B", "EnemyAction_Color_B", "Scale_X", "EnemyAction_Scale_X", "Scale_Y", "EnemyAction_Scale_Y", "TiltSpeed", "EnemyAction_TiltSpeed", "TiltOffset", "EnemyAction_TiltOffset", "RotateSpeed", "EnemyAction_RotateSpeed", "EnemyAction_SelectLevel", "EnemyAction_ImmuneBulletDamage", "EnemyAction_ImmuneCollideDamage", "EnemyAction_IgnoreBullet", "EnemyAction_IgnoreCollide", "EnemyAction_ImmuneNuke", "EnemyAction_ImmuneActiveSkill", "EnemyAction_Invincible", "_context", "_instances", "init", "entity", "onInit", "mainPlaneManager", "mainPlane", "enemyPrefabData", "eventGroups", "groupData", "instance", "tryStart", "onReset", "tryStop", "updateGameLogic", "dt", "length", "tick"], "mappings": ";;;yOAkCaA,sB,EAyBPC,kB,EAqBAC,qB,EA4DAC,kB,EAkDOC,c;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA9LJC,MAAAA,U,OAAAA,U;;AAIFC,MAAAA,Q;;AAGmBC,MAAAA,c,iBAAAA,c;;AACAC,MAAAA,c,iBAAAA,c;;AAIjBC,MAAAA,kB,iBAAAA,kB;AAAoBC,MAAAA,e,iBAAAA,e;;AACpBC,MAAAA,e,iBAAAA,e;AAAiBC,MAAAA,Y,iBAAAA,Y;;AAEdC,MAAAA,U;;AACAC,MAAAA,S;;AACHC,MAAAA,O,iBAAAA,O;;;;;;;;;OAhBH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBZ,U;;AAkB9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;wCAEaL,sB,GAAN,MAAMA,sBAAN,CAA2D;AAAA;AAAA,eAC9DkB,WAD8D,GAChC,IADgC;AAAA,eAE9DC,KAF8D,GAEjC,IAFiC;AAAA;;AAI9DC,QAAAA,KAAK,GAAG;AACJ,eAAKF,WAAL,GAAmB,IAAnB;AACA,eAAKC,KAAL,GAAa,IAAb;AACH;;AAP6D,O;;qCAWrDE,mB,WADZL,OAAO,CAAC,qBAAD,C,UAEHC,QAAQ,CAAC;AAAEK,QAAAA,WAAW,EAAE;AAAf,OAAD,C,UAGRL,QAAQ,CAAC;AAAEK,QAAAA,WAAW,EAAE,MAAf;AAAuBC,QAAAA,OAAO,EAAE;AAAhC,OAAD,C,UAGRN,QAAQ,CAAC;AAAEO,QAAAA,IAAI,EAAE;AAAA;AAAA,qDAAR;AAA8BF,QAAAA,WAAW,EAAE;AAA3C,OAAD,C,UAGRL,QAAQ,CAAC;AAAEO,QAAAA,IAAI,EAAE;AAAA;AAAA,+CAAR;AAA2BF,QAAAA,WAAW,EAAE;AAAxC,OAAD,C,4BAXb,MACaD,mBADb,CAC4D;AAAA;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAElC,E;;;;;;;iBAGQ,C;;;;;;;iBAGY,E;;;;;;;iBAGN,E;;;;AAGlCpB,MAAAA,kB,GAAN,MAAMA,kBAAN;AAAA;AAAA,4CAA6F;AAC/EwB,QAAAA,eAAe,GAA2C;AAChE,gBAAMC,KAAK,GAAG;AAAA;AAAA,iDAAd;AACA,eAAKC,IAAL,CAAUC,UAAV,CAAqBC,OAArB,CAA6B,CAACC,QAAD,EAAWC,KAAX,KAAqB;AAC9C,kBAAMC,SAAS,GAAG9B,qBAAqB,CAAC+B,MAAtB,CAA6BH,QAA7B,CAAlB;;AACA,gBAAIE,SAAJ,EAAe;AACXA,cAAAA,SAAS,CAACE,MAAV,CAAiB,KAAKC,OAAtB;AACAT,cAAAA,KAAK,CAACE,UAAN,CAAiBQ,IAAjB,CAAsBJ,SAAtB;AACH;AACJ,WAND;AAOA,iBAAON,KAAP;AACH;;AAESW,QAAAA,YAAY,GAA2C;AAC7D,iBAAO,KAAKV,IAAL,CAAUW,OAAV,CAAkBC,GAAlB,CAAsBC,UAAU,IAAI;AACvC,gBAAIC,MAAM,GAAGtC,kBAAkB,CAAC8B,MAAnB,CAA0BO,UAA1B,CAAb;AACA,mBAAOC,MAAP;AACH,WAHM,CAAP;AAIH;;AAlBwF,O;AAqBvFvC,MAAAA,qB,GAAN,MAAMA,qBAAN,CAA4B;AACX,eAAN+B,MAAM,CAACN,IAAD,EAAoE;AAC7E,kBAAQA,IAAI,CAACH,IAAb;AACI,iBAAK;AAAA;AAAA,oDAAgBkB,WAArB;AACI,qBAAO,IAAI7B,UAAU,CAAC8B,0BAAf,CAA0ChB,IAA1C,CAAP;;AACJ,iBAAK;AAAA;AAAA,oDAAgBiB,KAArB;AACI,qBAAO,IAAI/B,UAAU,CAACgC,oBAAf,CAAoClB,IAApC,CAAP;;AACJ,iBAAK;AAAA;AAAA,oDAAgBmB,KAArB;AACI,qBAAO,IAAIjC,UAAU,CAACkC,oBAAf,CAAoCpB,IAApC,CAAP;;AACJ,iBAAK;AAAA;AAAA,oDAAgBqB,KAArB;AACI,qBAAO,IAAInC,UAAU,CAACoC,oBAAf,CAAoCtB,IAApC,CAAP;;AACJ,iBAAK;AAAA;AAAA,oDAAgBuB,YAArB;AACI,qBAAO,IAAIrC,UAAU,CAACsC,2BAAf,CAA2CxB,IAA3C,CAAP;;AACJ,iBAAK;AAAA;AAAA,oDAAgByB,KAArB;AACI,qBAAO,IAAIvC,UAAU,CAACwC,oBAAf,CAAoC1B,IAApC,CAAP;;AACJ,iBAAK;AAAA;AAAA,oDAAgB2B,UAArB;AACI,qBAAO,IAAIzC,UAAU,CAAC0C,yBAAf,CAAyC5B,IAAzC,CAAP;;AACJ,iBAAK;AAAA;AAAA,oDAAgB6B,YAArB;AACI,qBAAO,IAAI3C,UAAU,CAAC4C,2BAAf,CAA2C9B,IAA3C,CAAP;;AACJ,iBAAK;AAAA;AAAA,oDAAgB+B,iBAArB;AACI,qBAAO,IAAI7C,UAAU,CAAC8C,gCAAf,CAAgDhC,IAAhD,CAAP;;AACJ,iBAAK;AAAA;AAAA,oDAAgBiC,gBAArB;AACI,qBAAO,IAAI/C,UAAU,CAACgD,+BAAf,CAA+ClC,IAA/C,CAAP;;AACJ,iBAAK;AAAA;AAAA,oDAAgBmC,aAArB;AACI,qBAAO,IAAIjD,UAAU,CAACkD,4BAAf,CAA4CpC,IAA5C,CAAP;;AACJ,iBAAK;AAAA;AAAA,oDAAgBqC,YAArB;AACI,qBAAO,IAAInD,UAAU,CAACoD,2BAAf,CAA2CtC,IAA3C,CAAP;;AACJ,iBAAK;AAAA;AAAA,oDAAgBuC,YAArB;AACI,qBAAO,IAAIrD,UAAU,CAACsD,2BAAf,CAA2CxC,IAA3C,CAAP;;AACJ,iBAAK;AAAA;AAAA,oDAAgByC,mBAArB;AACI,qBAAO,IAAIvD,UAAU,CAACwD,kCAAf,CAAkD1C,IAAlD,CAAP;;AACJ,iBAAK;AAAA;AAAA,oDAAgB2C,iBAArB;AACI,qBAAO,IAAIzD,UAAU,CAAC0D,gCAAf,CAAgD5C,IAAhD,CAAP;;AACJ,iBAAK;AAAA;AAAA,oDAAgB6C,cAArB;AACI,qBAAO,IAAI3D,UAAU,CAAC4D,6BAAf,CAA6C9C,IAA7C,CAAP;;AACJ,iBAAK;AAAA;AAAA,oDAAgB+C,2BAArB;AACI,qBAAO,IAAI7D,UAAU,CAAC8D,0CAAf,CAA0DhD,IAA1D,CAAP;;AACJ,iBAAK;AAAA;AAAA,oDAAgBiD,WAArB;AACI,qBAAO,IAAI/D,UAAU,CAACgE,0BAAf,CAA0ClD,IAA1C,CAAP;;AACJ,iBAAK;AAAA;AAAA,oDAAgBmD,kBAArB;AACI,qBAAO,IAAIjE,UAAU,CAACkE,iCAAf,CAAiDpD,IAAjD,CAAP;;AACJ,iBAAK;AAAA;AAAA,oDAAgBqD,mBAArB;AACI,qBAAO,IAAInE,UAAU,CAACoE,kCAAf,CAAkDtD,IAAlD,CAAP;;AACJ,iBAAK;AAAA;AAAA,oDAAgBuD,YAArB;AACI,qBAAO,IAAIrE,UAAU,CAACsE,2BAAf,CAA2CxD,IAA3C,CAAP;;AACJ,iBAAK;AAAA;AAAA,oDAAgByD,aAArB;AACI,qBAAO,IAAIvE,UAAU,CAACwE,4BAAf,CAA4C1D,IAA5C,CAAP;;AACJ,iBAAK;AAAA;AAAA,oDAAgB2D,UAArB;AACI,qBAAO,IAAIzE,UAAU,CAAC0E,yBAAf,CAAyC5D,IAAzC,CAAP;;AACJ,iBAAK;AAAA;AAAA,oDAAgB6D,iBAArB;AACI,qBAAO,IAAI3E,UAAU,CAAC4E,gCAAf,CAAgD9D,IAAhD,CAAP;;AACJ,iBAAK;AAAA;AAAA,oDAAgB+D,UAArB;AACI,qBAAO,IAAI7E,UAAU,CAAC8E,yBAAf,CAAyChE,IAAzC,CAAP;;AAEJ;AACI,oBAAM,IAAIiE,KAAJ,CAAW,2BAA0BjE,IAAI,CAACH,IAAK,EAA/C,CAAN;AArDR;AAuDH;;AAzDuB,O;AA4DtBrB,MAAAA,kB,GAAN,MAAMA,kBAAN,CAAyB;AACR,eAAN8B,MAAM,CAACN,IAAD,EAA8D;AACvE,kBAAQA,IAAI,CAACH,IAAb;AACI,iBAAK;AAAA;AAAA,8CAAa4B,KAAlB;AACI,qBAAO,IAAItC,SAAS,CAAC+E,iBAAd,CAAgClE,IAAhC,CAAP;;AACJ,iBAAK;AAAA;AAAA,8CAAa2B,UAAlB;AACI,qBAAO,IAAIxC,SAAS,CAACgF,sBAAd,CAAqCnE,IAArC,CAAP;;AACJ,iBAAK;AAAA;AAAA,8CAAa6B,YAAlB;AACI,qBAAO,IAAI1C,SAAS,CAACiF,wBAAd,CAAuCpE,IAAvC,CAAP;;AACJ,iBAAK;AAAA;AAAA,8CAAa+B,iBAAlB;AACI,qBAAO,IAAI5C,SAAS,CAACkF,6BAAd,CAA4CrE,IAA5C,CAAP;;AACJ,iBAAK;AAAA;AAAA,8CAAasE,OAAlB;AACI,qBAAO,IAAInF,SAAS,CAACoF,mBAAd,CAAkCvE,IAAlC,CAAP;;AACJ,iBAAK;AAAA;AAAA,8CAAawE,OAAlB;AACI,qBAAO,IAAIrF,SAAS,CAACsF,mBAAd,CAAkCzE,IAAlC,CAAP;;AACJ,iBAAK;AAAA;AAAA,8CAAa0E,OAAlB;AACI,qBAAO,IAAIvF,SAAS,CAACwF,mBAAd,CAAkC3E,IAAlC,CAAP;;AACJ,iBAAK;AAAA;AAAA,8CAAa4E,OAAlB;AACI,qBAAO,IAAIzF,SAAS,CAAC0F,mBAAd,CAAkC7E,IAAlC,CAAP;;AACJ,iBAAK;AAAA;AAAA,8CAAa8E,OAAlB;AACI,qBAAO,IAAI3F,SAAS,CAAC4F,mBAAd,CAAkC/E,IAAlC,CAAP;;AACJ,iBAAK;AAAA;AAAA,8CAAagF,SAAlB;AACI,qBAAO,IAAI7F,SAAS,CAAC8F,qBAAd,CAAoCjF,IAApC,CAAP;;AACJ,iBAAK;AAAA;AAAA,8CAAakF,UAAlB;AACI,qBAAO,IAAI/F,SAAS,CAACgG,sBAAd,CAAqCnF,IAArC,CAAP;;AACJ,iBAAK;AAAA;AAAA,8CAAaoF,WAAlB;AACI,qBAAO,IAAIjG,SAAS,CAACkG,uBAAd,CAAsCrF,IAAtC,CAAP;;AACJ,iBAAK;AAAA;AAAA,8CAAaiD,WAAlB;AACI,qBAAO,IAAI9D,SAAS,CAACmG,uBAAd,CAAsCtF,IAAtC,CAAP;;AACJ,iBAAK;AAAA;AAAA,8CAAamD,kBAAlB;AACI,qBAAO,IAAIhE,SAAS,CAACoG,8BAAd,CAA6CvF,IAA7C,CAAP;;AACJ,iBAAK;AAAA;AAAA,8CAAaqD,mBAAlB;AACI,qBAAO,IAAIlE,SAAS,CAACqG,+BAAd,CAA8CxF,IAA9C,CAAP;;AACJ,iBAAK;AAAA;AAAA,8CAAauD,YAAlB;AACI,qBAAO,IAAIpE,SAAS,CAACsG,wBAAd,CAAuCzF,IAAvC,CAAP;;AACJ,iBAAK;AAAA;AAAA,8CAAayD,aAAlB;AACI,qBAAO,IAAItE,SAAS,CAACuG,yBAAd,CAAwC1F,IAAxC,CAAP;;AACJ,iBAAK;AAAA;AAAA,8CAAa2D,UAAlB;AACI,qBAAO,IAAIxE,SAAS,CAACwG,sBAAd,CAAqC3F,IAArC,CAAP;;AACJ,iBAAK;AAAA;AAAA,8CAAa6D,iBAAlB;AACI,qBAAO,IAAI1E,SAAS,CAACyG,6BAAd,CAA4C5F,IAA5C,CAAP;;AACJ,iBAAK;AAAA;AAAA,8CAAa+D,UAAlB;AACI,qBAAO,IAAI5E,SAAS,CAAC0G,sBAAd,CAAqC7F,IAArC,CAAP;;AACJ;AACI,oBAAM,IAAIiE,KAAJ,CAAW,wBAAuBjE,IAAI,CAACH,IAAK,EAA5C,CAAN;AA1CR;AA4CH;;AA9CoB,O,EAiDzB;;gCACapB,c,GAAN,MAAMA,cAAN;AAAA;AAAA,gCAAsC;AAAA;AAAA;AAAA,eACzCe,KADyC,GACZ,IADY;AAAA,eAGjCsG,QAHiC,GAGE,IAAIzH,sBAAJ,EAHF;AAAA,eAIjC0H,UAJiC,GAIE,EAJF;AAAA;;AAMzCC,QAAAA,IAAI,CAACC,MAAD,EAAiB;AACjB,gBAAMD,IAAN,CAAWC,MAAX;AACA,eAAKzG,KAAL,GAAayG,MAAb;AACH;;AAEDC,QAAAA,MAAM,GAAG;AAAA;;AACL,eAAKJ,QAAL,CAAcvG,WAAd,wBAA4B;AAAA;AAAA,kCAAQ4G,gBAApC,qBAA4B,kBAA0BC,SAAtD;AACA,eAAKN,QAAL,CAActG,KAAd,GAAsB,KAAKA,KAA3B;;AACA,cAAI,KAAKA,KAAL,IAAc,KAAKA,KAAL,CAAW6G,eAA7B,EAA8C;AAC1C,iBAAK7G,KAAL,CAAW6G,eAAX,CAA2BC,WAA3B,CAAuCpG,OAAvC,CAA+CqG,SAAS,IAAI;AACxD,kBAAIC,QAAQ,GAAG,IAAIlI,kBAAJ,CAAuB,KAAKwH,QAA5B,EAAsCS,SAAtC,CAAf;AACAC,cAAAA,QAAQ,CAACC,QAAT;;AACA,mBAAKV,UAAL,CAAgBtF,IAAhB,CAAqB+F,QAArB;AACH,aAJD;AAKH;AACJ;;AAEDE,QAAAA,OAAO,GAAG;AACN,eAAKX,UAAL,CAAgB7F,OAAhB,CAAwBsG,QAAQ,IAAIA,QAAQ,CAACG,OAAT,EAApC;;AACA,eAAKZ,UAAL,GAAkB,EAAlB;;AACA,eAAKD,QAAL,CAAcrG,KAAd;AACH;;AAEDmH,QAAAA,eAAe,CAACC,EAAD,EAAa;AACxB,cAAI,KAAKd,UAAL,CAAgBe,MAAhB,GAAyB,CAA7B,EAAgC;AAC5B,iBAAKf,UAAL,CAAgB7F,OAAhB,CAAwBsG,QAAQ,IAAI;AAChCA,cAAAA,QAAQ,CAACO,IAAT,CAAcF,EAAd;AACH,aAFD;AAGH;AACJ;;AAnCwC,O", "sourcesContent": ["import { _decorator, Enum } from \"cc\";\r\nconst { ccclass, property } = _decorator;\r\n\r\nimport Entity from \"db://assets/bundles/common/script/game/ui/base/Entity\";\r\nimport BaseComp from \"db://assets/bundles/common/script/game/ui/base/BaseComp\";\r\nimport EnemyPlaneBase from \"db://assets/bundles/common/script/game/ui/plane/enemy/EnemyPlaneBase\";\r\nimport { IEventGroupContext } from \"db://assets/bundles/common/script/game/eventgroup/IEventGroupContext\";\r\nimport { IEventGroupData, EventGroupBase } from \"db://assets/bundles/common/script/game/eventgroup/IEventGroup\";\r\nimport { IEventCondition, ConditionChain } from \"db://assets/bundles/common/script/game/eventgroup/IEventCondition\";\r\nimport { IEventAction } from \"db://assets/bundles/common/script/game/eventgroup/IEventAction\";\r\nimport PlaneBase from \"db://assets/bundles/common/script/game/ui/plane/PlaneBase\";\r\n\r\nimport { EnemyConditionData, eEnemyCondition } from './conditions/EnemyEventConditionData';\r\nimport { EnemyActionData, eEnemyAction } from './actions/EnemyEventActionData';\r\n\r\nimport * as enemy_cond from './conditions/EnemyEventCondition';\r\nimport * as enemy_act from './actions/EnemyEventAction';\r\nimport { GameIns } from \"db://assets/bundles/common/script/game/GameIns\";\r\n\r\n/**\r\n * 这个文件主要用于实现敌机的一些简单的 条件->效果 机制，主要适用于敌机表现相关的，如：移动速度，摆动等等。\r\n * 核心接口：\r\n *   编辑器配置：\r\n *   - IEventGroupData: 事件组数据\r\n *   - IEventConditionData: 事件条件数据\r\n *   - IEventActionData: 事件效果数据\r\n *   运行时：\r\n *   - IEventGroupContext: 事件执行上下文\r\n *   - IEventCondition: 事件条件\r\n *   - IEventAction: 事件效果\r\n *   - EventInstance: 事件运行时的实例\r\n * 原则上，扩展可以通过扩展条件或者事件的枚举，并提供具体的实现即可。\r\n */\r\n\r\nexport class EnemyEventGroupContext implements IEventGroupContext {\r\n    playerPlane: PlaneBase|null = null;\r\n    plane: EnemyPlaneBase|null = null;\r\n\r\n    reset() {\r\n        this.playerPlane = null;\r\n        this.plane = null;\r\n    }\r\n}\r\n\r\n@ccclass('EnemyEventGroupData')\r\nexport class EnemyEventGroupData implements IEventGroupData {\r\n    @property({ displayName: '事件组名称' })\r\n    public name: string = \"\";\r\n\r\n    @property({ displayName: '触发次数', tooltip: '触发次数(默认1,只能触发一次; -1表示循环触发)' })\r\n    public triggerCount: number = 1;\r\n\r\n    @property({ type: [EnemyConditionData], displayName: '条件列表' })\r\n    public conditions: EnemyConditionData[] = [];\r\n\r\n    @property({ type: [EnemyActionData], displayName: '行为列表' })\r\n    public actions: EnemyActionData[] = [];\r\n}\r\n\r\nclass EnemyEventInstance extends EventGroupBase<EnemyEventGroupContext, EnemyEventGroupData> {\r\n    protected buildConditions(): ConditionChain<EnemyEventGroupContext> {\r\n        const chain = new ConditionChain<EnemyEventGroupContext>();\r\n        this.data.conditions.forEach((condData, index) => {\r\n            const condition = EnemyConditionFactory.create(condData);\r\n            if (condition) {\r\n                condition.onLoad(this.context);\r\n                chain.conditions.push(condition);\r\n            }\r\n        });\r\n        return chain;\r\n    }\r\n\r\n    protected buildActions(): IEventAction<EnemyEventGroupContext>[] {\r\n        return this.data.actions.map(actionData => {\r\n            let action = EnemyActionFactory.create(actionData);\r\n            return action;\r\n        });\r\n    }\r\n}\r\n\r\nclass EnemyConditionFactory {\r\n    static create(data: EnemyConditionData): IEventCondition<EnemyEventGroupContext> {\r\n        switch (data.type) {\r\n            case eEnemyCondition.ElapsedTime:\r\n                return new enemy_cond.EnemyCondition_ElapsedTime(data);\r\n            case eEnemyCondition.Pos_X:\r\n                return new enemy_cond.EnemyCondition_Pos_X(data);\r\n            case eEnemyCondition.Pos_Y:\r\n                return new enemy_cond.EnemyCondition_Pos_Y(data);\r\n            case eEnemyCondition.CurHP:\r\n                return new enemy_cond.EnemyCondition_CurHP(data);\r\n            case eEnemyCondition.CurHPPercent:\r\n                return new enemy_cond.EnemyCondition_CurHPPercent(data);\r\n            case eEnemyCondition.Speed:\r\n                return new enemy_cond.EnemyCondition_Speed(data);\r\n            case eEnemyCondition.SpeedAngle:\r\n                return new enemy_cond.EnemyCondition_SpeedAngle(data);\r\n            case eEnemyCondition.Acceleration:\r\n                return new enemy_cond.EnemyCondition_Acceleration(data);\r\n            case eEnemyCondition.AccelerationAngle:\r\n                return new enemy_cond.EnemyCondition_AccelerationAngle(data);\r\n            case eEnemyCondition.DistanceToPlayer:\r\n                return new enemy_cond.EnemyCondition_DistanceToPlayer(data);\r\n            case eEnemyCondition.AngleToPlayer:\r\n                return new enemy_cond.EnemyCondition_AngleToPlayer(data);\r\n            case eEnemyCondition.Player_Pos_X:\r\n                return new enemy_cond.EnemyCondition_Player_Pos_X(data);\r\n            case eEnemyCondition.Player_Pos_Y:\r\n                return new enemy_cond.EnemyCondition_Player_Pos_Y(data);\r\n            case eEnemyCondition.Player_CurHPPercent:\r\n                return new enemy_cond.EnemyCondition_Player_CurHPPercent(data);\r\n            case eEnemyCondition.Level_ElapsedTime:\r\n                return new enemy_cond.EnemyCondition_Level_ElapsedTime(data);\r\n            case eEnemyCondition.Level_InfLevel:\r\n                return new enemy_cond.EnemyCondition_Level_InfLevel(data);\r\n            case eEnemyCondition.Level_ChallengeLevelSection:\r\n                return new enemy_cond.EnemyCondition_Level_ChallengeLevelSection(data);\r\n            case eEnemyCondition.SelectLevel:\r\n                return new enemy_cond.EnemyCondition_SelectLevel(data);\r\n            case eEnemyCondition.ImmuneBulletDamage:\r\n                return new enemy_cond.EnemyCondition_ImmuneBulletDamage(data);\r\n            case eEnemyCondition.ImmuneCollideDamage:\r\n                return new enemy_cond.EnemyCondition_ImmuneCollideDamage(data);\r\n            case eEnemyCondition.IgnoreBullet:\r\n                return new enemy_cond.EnemyCondition_IgnoreBullet(data);\r\n            case eEnemyCondition.IgnoreCollide:\r\n                return new enemy_cond.EnemyCondition_IgnoreCollide(data);\r\n            case eEnemyCondition.ImmuneNuke:\r\n                return new enemy_cond.EnemyCondition_ImmuneNuke(data);\r\n            case eEnemyCondition.ImmuneActiveSkill:\r\n                return new enemy_cond.EnemyCondition_ImmuneActiveSkill(data);\r\n            case eEnemyCondition.Invincible:\r\n                return new enemy_cond.EnemyCondition_Invincible(data);\r\n            \r\n            default: \r\n                throw new Error(`Unknown condition type: ${data.type}`);\r\n        }\r\n    }\r\n}\r\n\r\nclass EnemyActionFactory {\r\n    static create(data: EnemyActionData): IEventAction<EnemyEventGroupContext> {\r\n        switch (data.type) {\r\n            case eEnemyAction.Speed:\r\n                return new enemy_act.EnemyAction_Speed(data);\r\n            case eEnemyAction.SpeedAngle:\r\n                return new enemy_act.EnemyAction_SpeedAngle(data);\r\n            case eEnemyAction.Acceleration:\r\n                return new enemy_act.EnemyAction_Acceleration(data);\r\n            case eEnemyAction.AccelerationAngle:\r\n                return new enemy_act.EnemyAction_AccelerationAngle(data);\r\n            case eEnemyAction.Color_R:\r\n                return new enemy_act.EnemyAction_Color_R(data);\r\n            case eEnemyAction.Color_G:\r\n                return new enemy_act.EnemyAction_Color_G(data);\r\n            case eEnemyAction.Color_B:\r\n                return new enemy_act.EnemyAction_Color_B(data);\r\n            case eEnemyAction.Scale_X:\r\n                return new enemy_act.EnemyAction_Scale_X(data);\r\n            case eEnemyAction.Scale_Y:\r\n                return new enemy_act.EnemyAction_Scale_Y(data);\r\n            case eEnemyAction.TiltSpeed:\r\n                return new enemy_act.EnemyAction_TiltSpeed(data);\r\n            case eEnemyAction.TiltOffset:\r\n                return new enemy_act.EnemyAction_TiltOffset(data);\r\n            case eEnemyAction.RotateSpeed:\r\n                return new enemy_act.EnemyAction_RotateSpeed(data);\r\n            case eEnemyAction.SelectLevel:\r\n                return new enemy_act.EnemyAction_SelectLevel(data);\r\n            case eEnemyAction.ImmuneBulletDamage:\r\n                return new enemy_act.EnemyAction_ImmuneBulletDamage(data);\r\n            case eEnemyAction.ImmuneCollideDamage:\r\n                return new enemy_act.EnemyAction_ImmuneCollideDamage(data);\r\n            case eEnemyAction.IgnoreBullet:\r\n                return new enemy_act.EnemyAction_IgnoreBullet(data);\r\n            case eEnemyAction.IgnoreCollide:\r\n                return new enemy_act.EnemyAction_IgnoreCollide(data);\r\n            case eEnemyAction.ImmuneNuke:\r\n                return new enemy_act.EnemyAction_ImmuneNuke(data);\r\n            case eEnemyAction.ImmuneActiveSkill:\r\n                return new enemy_act.EnemyAction_ImmuneActiveSkill(data);\r\n            case eEnemyAction.Invincible:\r\n                return new enemy_act.EnemyAction_Invincible(data);\r\n            default:\r\n                throw new Error(`Unknown action type: ${data.type}`);\r\n        }\r\n    }\r\n}\r\n\r\n// 挂在Entity身上，用来执行事件组的组件\r\nexport class EventGroupComp extends BaseComp {\r\n    plane: EnemyPlaneBase|null = null;\r\n\r\n    private _context: EnemyEventGroupContext = new EnemyEventGroupContext();\r\n    private _instances: EnemyEventInstance[] = [];\r\n    \r\n    init(entity: Entity) {\r\n        super.init(entity);\r\n        this.plane = entity as EnemyPlaneBase;\r\n    }\r\n\r\n    onInit() {\r\n        this._context.playerPlane = GameIns.mainPlaneManager?.mainPlane;\r\n        this._context.plane = this.plane;\r\n        if (this.plane && this.plane.enemyPrefabData) {\r\n            this.plane.enemyPrefabData.eventGroups.forEach(groupData => {\r\n                let instance = new EnemyEventInstance(this._context, groupData);\r\n                instance.tryStart()\r\n                this._instances.push(instance);\r\n            });\r\n        }\r\n    }\r\n\r\n    onReset() {\r\n        this._instances.forEach(instance => instance.tryStop());\r\n        this._instances = [];\r\n        this._context.reset();\r\n    }\r\n\r\n    updateGameLogic(dt: number) {\r\n        if (this._instances.length > 0) {\r\n            this._instances.forEach(instance => {\r\n                instance.tick(dt);\r\n            });\r\n        }\r\n    }\r\n}"]}
import { _decorator, CCFloat, Component, Node, CCString, Prefab, assetManager, instantiate, UITransform, view, Graphics, Color, Rect, Label, Vec3, Quat } from 'cc';
const { ccclass, property, executeInEditMode } = _decorator;
import { LayerRandomRange, LayerSplicingMode, LayerType, LevelData, LevelDataBackgroundLayer, LevelDataLayer, LevelDataRandTerrain, LevelDataRandTerrains, LevelDataRandTerrainsGroup, LevelDataScroll } from 'db://assets/bundles/common/script/leveldata/leveldata';
import { LevelEditorLayerUI } from './LevelEditorLayerUI';
import { LayerEditorRandomRange, LevelBackgroundLayer, LevelEditorUtils, LevelLayer, LevelRandTerrainsLayersUI, LevelRandTerrainsLayerUI, LevelRandTerrainUI } from './utils';
import { RandTerrain } from 'db://assets/bundles/common/script/game/dyncTerrain/RandTerrain';
import { EmittierTerrain } from '../../bundles/common/script/game/dyncTerrain/EmittierTerrain';
import { LevelDataEvent } from 'db://assets/bundles/common/script/leveldata/leveldata';
import { LevelEditorEventShadowUI } from './LevelEditorEventShadowUI';

const BackgroundsNodeName = "backgrounds";

@ccclass('LevelEditorBaseUI')
@executeInEditMode()
export class LevelEditorBaseUI extends Component {
    @property(CCString)
    public levelname: string = "";
    @property({type:CCFloat, displayName:"关卡时长(ms)",min:2000})
    public totalTime: number = 2000;
    private _totalHeight: number = 0;

    @property({type:LevelBackgroundLayer, displayName:"背景"})
    public backgroundLayer: LevelBackgroundLayer = new LevelBackgroundLayer();
    @property({type:[LevelLayer], displayName:"地面"})
    public floorLayers: LevelLayer[] = [];
    @property({type:[LevelLayer], displayName:"天空"})
    public skyLayers: LevelLayer[] = [];

    private backgroundLayerNode:Node|null = null;
    private floorLayersNode:Node|null = null;
    private skyLayersNode:Node|null = null;
    private eventParentNode:Node|null = null;

    private _isLoadingScrollNodes: boolean = false;
    private _play: boolean = false;
    private _drawNode: Node | null = null;

    onLoad():void {
        console.log(`LevelEditorBaseUI start.`);
        this.backgroundLayerNode = LevelEditorUtils.getOrAddNode(this.node, "BackgroundLayer");
        this.floorLayersNode = LevelEditorUtils.getOrAddNode(this.node, "FloorLayers");
        this.skyLayersNode = LevelEditorUtils.getOrAddNode(this.node, "SkyLayers");
        this._drawNode = LevelEditorUtils.getOrAddNode(this.node, "DrawNode");
        this.eventParentNode = LevelEditorUtils.getOrAddNode(this.node, "EventNodeParent");

        console.log(`LevelEditorBaseUI start ${this.floorLayersNode?.uuid}`);
    }
    update(dt:number):void {
        this.checkLayerNode(this.floorLayersNode!, this.floorLayers,dt);
        this.checkLayerNode(this.skyLayersNode!, this.skyLayers,dt);
    }
    private setBackgroundNodePosition(node:Node, yOff:number):number {
        const height = node.getComponent(UITransform)!.contentSize.height;
        node.setPosition(0, yOff-view.getVisibleSize().height / 2 + height / 2);
        return height;

    }
    public tick(progress: number):void {
        let yOff = 0
        for (let i = 0; i < this.backgroundLayer.backgroundsNode!.children.length; i++) {
            var bg = this.backgroundLayer.backgroundsNode!.children[i]
            yOff += this.setBackgroundNodePosition(bg, yOff)
        }
        while(this.backgroundLayer.backgrounds.length > 0 && yOff < this._totalHeight) {
            let bg:Node|null = null;
            let bgIndex = this.backgroundLayer.backgroundsNode!.children.length % this.backgroundLayer.backgrounds.length;
            const prefab = this.backgroundLayer.backgrounds[bgIndex]
            if (prefab != null) {
                bg = instantiate(prefab)
            } 
            if (bg == null) {
                bg = new Node("empty");
                bg.addComponent(UITransform).height = 1024;
            }
            this.backgroundLayer.backgroundsNode!.addChild(bg);
            yOff += this.setBackgroundNodePosition(bg, yOff)
        }
        for (let i = this.backgroundLayer.backgroundsNode!.children.length - 1; i >= 0; i--) {
            const bg = this.backgroundLayer.backgroundsNode!.children[i]
            if (bg.position.y - bg.getComponent(UITransform)!.height/2 > this._totalHeight) {
                bg.removeFromParent()
            } else {
                break;
            }
        }

        this.backgroundLayer!.node!.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!.tick(
            progress, this.totalTime, this.backgroundLayer.speed);
        this.floorLayers.forEach((layer) => {
            if (layer.node && layer.node.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)) {
                layer.node!.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!.tick(progress, this.totalTime, layer.speed);
            }
        });
        this.skyLayers.forEach((layer) => {
            if (layer.node && layer.node.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)) {
                layer.node!.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!.tick(progress, this.totalTime, layer.speed);
            }
        });
    }

    private static addLayer(parentNode: Node, name: string): LevelEditorLayerUI {
        var layerNode = new Node(name);
        var layerCom = layerNode.addComponent<LevelEditorLayerUI>(LevelEditorLayerUI);
        parentNode.addChild(layerNode);
        return layerCom;
    }

    private checkLayerNode(parentNode: Node, layers: LevelLayer[], dt: number):void {
        var removeLayerNodes: Node[] = []
        parentNode.children.forEach(node => {
            var layerCom = node.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI);
            if (layerCom == null) {
                console.log(`Level checkLayerNode remove ${node.name} because layerCom == null"`);
                removeLayerNodes.push(node)
                return;
            }
            if (layers.find((layer) => layer.node == node) == null) {
                console.log(`Level checkLayerNode remove ${node.name} because not in layers"`);
                removeLayerNodes.push(node)
                return;
            }
        });
        removeLayerNodes.forEach(element => {
            element.removeFromParent();    
        });
        layers.forEach((layer, i) => {
            if (layer.node == null || layer.node.isValid == false) {
                console.log(`Level checkLayerNode add because layer == null`);
                layer.node = LevelEditorBaseUI.addLayer(parentNode, `layer_${i}`).node; 
            }

            if (layer.type === LayerType.Scroll) {
                this._checkScrollNode(layer, layer!.node);
            } else if (layer.type === LayerType.Random) {
                this._checkRandTerrainNode(layer, layer!.node);
            } else if (layer.type === LayerType.Emittier) {
                this._checkEmittierNode(layer, layer!.node);   
                this._updateEmittierNode(dt, layer!.node);
            }
        });
    }

    private async _checkScrollNode(data: LevelLayer, parentNode: Node):Promise<void> {
        const scrollsNode = LevelEditorUtils.getOrAddNode(parentNode, "scrolls");
        if (data.type != LayerType.Scroll) {
            scrollsNode.removeAllChildren();
            return;
        }

        if (this._isLoadingScrollNodes) {
            //console.log(`LevelEditorBaseUI _checkScrollNode _isLoadingScrollNodes ${this._isLoadingScrollNodes} scrollsNode.children.length ${scrollsNode.children.length}`);
            return;
        }

        const isCountMatch = scrollsNode.children.length === data.scrollLayers.length;
        //console.log(`LevelEditorBaseUI _checkScrollNode children.length ${scrollsNode.children.length} data.scrollLayers.length ${data.scrollLayers.length}`); 
        if (!isCountMatch) {
            const loadPromises: Promise<void>[] = [];
            scrollsNode.removeAllChildren();
            this._isLoadingScrollNodes = true; // 标记为加载中

            data.scrollLayers.forEach((scroll, index) => {
                if (scroll.scrollPrefabs.length <= 0) return;

                    scroll.scrollPrefabs.forEach(prefab => {
                        const loadPromise = new Promise<void>((resolve) => {
                            assetManager.loadAny({ uuid: prefab!.uuid }, (err: Error, loadedPrefab: Prefab) => {
                                if (err) {
                                    console.error("Failed to load prefab:", err);
                                    resolve();
                                } else {
                                    resolve();
                                }
                            });
                    });
                    
                    loadPromises.push(loadPromise);
                });
            });

            await Promise.all(loadPromises);
            //console.log(`LevelEditorBaseUI _checkScrollNode data.scrollLayers ${data.scrollLayers.length}`);
            data.scrollLayers.forEach((scroll, index) => {
                const scrollNode = LevelEditorUtils.getOrAddNode(scrollsNode, `scroll_${index}`);
                const totalHeight = data.speed * this.totalTime / 1000; 
                console.log(`LevelEditorBaseUI _checkScrollNode totalHeight ${totalHeight}`);
                let posOffsetY = 0;
                let height = 0;
                let prefabIndex = 0;

                while (height < totalHeight) {
                    const curPrefab = scroll.scrollPrefabs[prefabIndex];
                    const child = instantiate(curPrefab);
                    const randomOffsetX = Math.random() * (scroll.splicingOffsetX!.max - scroll.splicingOffsetX!.min) + scroll.splicingOffsetX!.min;
                    child.setPosition(randomOffsetX, posOffsetY, 0);

                    let offY = 0;
                    if (scroll.splicingMode === LayerSplicingMode.node_height) {
                        offY = child.getComponent(UITransform)!.contentSize.height;
                    } else if (scroll.splicingMode === LayerSplicingMode.fix_height) {
                        offY = 1334;
                    } else if (scroll.splicingMode === LayerSplicingMode.random_height) {
                        offY = Math.max(scroll.splicingOffsetY!.min, scroll.splicingOffsetY!.max) + child.getComponent(UITransform)!.contentSize.height;
                    }

                    scrollNode.addChild(child);
                    posOffsetY += offY;
                    height += offY;
                    prefabIndex = (prefabIndex + 1) % scroll.scrollPrefabs.length;
                }
            });
            this._isLoadingScrollNodes = false; 
        }
    }

    private _checkRandTerrainNode(data: LevelLayer, parentNode: Node):void {
        const dynamicNode = LevelEditorUtils.getOrAddNode(parentNode, "dynamic");

        // 删除所有多余的dyna节点
        const currentDynaNodes = dynamicNode.children;
        for (let i = currentDynaNodes.length - 1; i >= 0; i--) {
            const node = currentDynaNodes[i];
            const match = node.name.match(/^dyna_(\d+)$/);
            if (match) {
                const index = parseInt(match[1]);
                if (index >= data.randomLayers.length) {
                    node.removeFromParent();
                }
            }
        }

        if (data.type != LayerType.Random || data.randomLayers.length === 0) {
            dynamicNode.removeAllChildren();
            return;
        }

        let needRebuild = false;
        const rebuildList: number[] = [];
        
        for (let i = 0; i < data.randomLayers.length; i++) {
            const randTerrains = data.randomLayers[i];
            const dynaNode = LevelEditorUtils.getOrAddNode(dynamicNode, `dyna_${i}`);
            
            // 计算该dyna节点应有的总地形元素数量
            let expectedChildCount = 0;
            for (const terrains of randTerrains.dynamicTerrains) {
                expectedChildCount += terrains.dynamicTerrain.length;
            }
            
            // 检查子节点数量是否匹配
            if (dynaNode.children.length !== expectedChildCount) {
                needRebuild = true;
                rebuildList.push(i);
                continue;
            }
            
            // 检查每个子节点对应的预制体UUID是否匹配
            let childIndex = 0;
            let isUUIDMatch = true;
            
            for (const terrains of randTerrains.dynamicTerrains) {
                for (const terrain of terrains.dynamicTerrain) {
                    const childNode = dynaNode.children[childIndex];
                    // @ts-ignore
                    const childPrefabUUID = childNode._prefab?.asset?._uuid;
                    const terrainUUID = terrain?.terrainElement?.uuid;
                    
                    if (childPrefabUUID !== terrainUUID) {
                        isUUIDMatch = false;
                        break;
                    }
                    childIndex++;
                }
                if (!isUUIDMatch) break;
            }
            
            if (!isUUIDMatch) {
                needRebuild = true;
                rebuildList.push(i);
            }
        }

        if (needRebuild) {
            //console.log("LevelEditorBaseUI _checkRandTerrainNode need rebuild");

            for (const index of rebuildList) {
                const dynaNode = LevelEditorUtils.getOrAddNode(dynamicNode, `dyna_${index}`);
                dynaNode.removeAllChildren();
                const randTerrains = data.randomLayers[index];
                // 遍历所有地形组
                for (let j = 0; j < randTerrains.dynamicTerrains.length; j++) {
                    const terrains = randTerrains.dynamicTerrains[j];
                    
                    // 遍历地形组中的每个地形元素
                    for (let k = 0; k < terrains.dynamicTerrain.length; k++) {
                        const terrain = terrains.dynamicTerrain[k];
                        assetManager.loadAny({ uuid: terrain?.terrainElement?.uuid }, (err, prefab: Prefab) => { 
                            if (err) {
                                //console.error(`加载地形元素失败: ${terrain?.terrainElements?.uuid}`, err);
                                return;
                            }

                            const node = instantiate(prefab);
                            node.name = `rand_${j}_${k}`;
                            dynaNode.addChild(node);
                            const randomOffsetX = Math.random() * (terrain.offSetX!.max - terrain.offSetX!.min) + terrain.offSetX!.min;   
                            const randomOffsetY = Math.random() * (terrain.offSetY!.max - terrain.offSetY!.min) + terrain.offSetY!.min;
                            dynamicNode.setPosition(randomOffsetX, randomOffsetY, 0);
                        });
                    }
                }
            }
        }
    }

    private _checkEmittierNode(data: LevelLayer, parentNode: Node):void {
        const emittierNode = LevelEditorUtils.getOrAddNode(parentNode, "emittiers");
        if (data.type != LayerType.Emittier) {
            emittierNode.removeAllChildren();
            return;
        }

        // 1. 按索引遍历数据层
        for (let i = 0; i < data.emittierLayers.length; i++) {
            const emitterData = data.emittierLayers[i];
            
            // 安全获取节点 - 检查索引是否有效
            const existingNode = i < emittierNode.children.length ? 
                                emittierNode.children[i] : 
                                null;
            
            // 检查是否需要重新加载节点
            let needReload = false;
            
            if (!existingNode || emitterData === null) {
                // 节点不存在，需要创建新节点
                needReload = true;
            } else {
                // 检查UUID是否匹配
                // @ts-ignore
                if (existingNode._prefab?.asset?.uuid !== emitterData.uuid ) {
                    needReload = true; 
                }
            }
            
            // 重新加载节点
            if (needReload) {
                // 保存原有位置和缩放信息（如果节点存在）
                const oldPosition = existingNode ? existingNode.position.clone() : new Vec3(0, 0, 0);
                const oldRotation = existingNode ? existingNode.rotation.clone() : new Quat();
                const oldScale = existingNode ? existingNode.scale.clone() : new Vec3(1, 1, 1);
                
                // 移除旧节点（如果存在）
                if (existingNode) {
                    existingNode.removeFromParent();
                }
                
                if (emitterData && emitterData.uuid) {
                    assetManager.loadAny({ uuid: emitterData.uuid }, (err, prefab: Prefab) => {
                        if (err) {
                            return;
                        } else {
                            
                            const emitterNode = instantiate(prefab);
                            emitterNode.name = `emittier_${i}`;
                            emitterNode.position = oldPosition;
                            emitterNode.rotation = oldRotation;
                            emitterNode.scale = oldScale;
                            
                            emittierNode.addChild(emitterNode);
                            emitterNode.setSiblingIndex(i);
                        }
                    });
                } else {
                    return;
                }
            }
        }
        
        // 2. 删除多余的节点
        while (emittierNode.children.length > data.emittierLayers.length) {
            const lastIndex = emittierNode.children.length - 1;
            emittierNode.children[lastIndex].removeFromParent();
        }
    }

    private _playEmittierNode(value: boolean, parentNode: Node):void {
        parentNode.children.forEach(node => {
            const emittierNode = LevelEditorUtils.getOrAddNode(node, "emittiers");
            console.log("play emittier   ---------- 0");
            if (emittierNode === null || emittierNode.children.length === 0) {
                return;
            }
            console.log("play emittier   ---------- 0 <USER> <GROUP>");

            for (let i = 0; i < emittierNode.children.length; i++) {
                const emitterNode = emittierNode.children[i];
                const emittier = emitterNode.getComponent(EmittierTerrain);
                console.log("play emittier", value);
                if (emittier) {
                    console.log("play emittier ------ 1", value);
                    emittier.play(value);
                }
            }
        });
    }

    private _updateEmittierNode(dt: number, parentNode: Node):void {
        const emittierNode = LevelEditorUtils.getOrAddNode(parentNode, "emittiers");
        if (emittierNode === null || emittierNode.children.length === 0) {
            return;
        }

        for (let i = 0; i < emittierNode.children.length; i++) {
            const emitterNode = emittierNode.children[i];
            const emittier = emitterNode.getComponent(EmittierTerrain);
            if (emittier) {
                emittier.tick(dt);
            }
        }
    }

    public initByLevelData(data: LevelData):void {
        this.levelname = data.name;
        this.totalTime = data.totalTime;

        // 同步事件的shadow节点
        this.eventParentNode!.removeAllChildren();

        this.backgroundLayerNode!.removeAllChildren()
        this.backgroundLayer = new LevelBackgroundLayer();
        this.backgroundLayer.backgrounds = [];
        data.backgroundLayer?.backgrounds?.forEach((background) => {
            assetManager.loadAny({uuid:background}, (err: Error, prefab:Prefab) => {
                if (err) {
                    console.error("LevelEditorBaseUI initByLevelData load background prefab err", err);
                    return
                } 
                this.backgroundLayer.backgrounds.push(prefab);
            });
        });
        this.backgroundLayer.speed = data.backgroundLayer?.speed;
        this.backgroundLayer.remark = data.backgroundLayer?.remark;
        this._totalHeight = this.backgroundLayer.speed * this.totalTime / 1000;
        this.backgroundLayer.node = LevelEditorBaseUI.addLayer(this.backgroundLayerNode!, "layer").node;
        this.backgroundLayer.backgroundsNode = LevelEditorUtils.getOrAddNode(this.backgroundLayer.node, BackgroundsNodeName);
        this.backgroundLayer.backgroundsNode.setSiblingIndex(0); 
        this.backgroundLayer.node.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!.initByLevelData(data.backgroundLayer);
    
        this.floorLayers = []
        this.skyLayers = []
        LevelEditorBaseUI.initLayers(this.floorLayersNode!, this.floorLayers, data.floorLayers);
        LevelEditorBaseUI.initLayers(this.skyLayersNode!, this.skyLayers, data.skyLayers);

        LevelEditorEventShadowUI.syncToShadow(this.eventParentNode!, [data.backgroundLayer!], "background");
        LevelEditorEventShadowUI.syncToShadow(this.eventParentNode!, data.floorLayers, "floor");
        LevelEditorEventShadowUI.syncToShadow(this.eventParentNode!, data.skyLayers, "sky");

        // // 同步shadow
        // const _syncEventShadowNode = (event: LevelDataEvent, index: number, layerName: string) => {
        //     const eventNode = new Node(layerName + "_" + index + "_" + event.name);
        //     const shadowComp = eventNode.addComponent(LevelEditorEventShadowUI);
        //     eventNode.setPosition(event.position.x, event.position.y, 0);
        //     this.eventShadowNode!.addChild(eventNode);
        //     shadowComp.syncFromTargetElem(event, index, layerName);
        // };
        // data.backgroundLayer?.events?.forEach((event, index) => {
        //     _syncEventShadowNode(event, index, "background");
        // });
        // data.floorLayers.forEach((layer, layerIndex) => {
        //     layer.events.forEach((event) => {
        //         _syncEventShadowNode(event, layerIndex, `floor`);
        //     });
        // });
        // data.skyLayers.forEach((layer, layerIndex) => {
        //     layer.events.forEach((event) => {
        //         _syncEventShadowNode(event, layerIndex, `sky`);
        //     });
        // });

        this._drawNodeGraphics();
    }

    private static initLayers(parentNode: Node, layers: LevelLayer[], dataLayers: LevelDataLayer[]):void {
        parentNode.removeAllChildren()
        dataLayers.forEach((layer, i) => {
            var levelLayer = new LevelLayer();
            levelLayer.speed = layer.speed;
            levelLayer.type = layer.type;
            levelLayer.remark = layer.remark;
            levelLayer.zIndex = layer.zIndex;
            levelLayer.node = LevelEditorBaseUI.addLayer(parentNode, `layer_${i}`).node;
            levelLayer.node.setSiblingIndex(layer.zIndex);
            const levelEditorLayerUI = levelLayer.node.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!;
            if (layer.type === LayerType.Scroll) {
                console.log("initScorllsByLevelData levelLayer.length ", levelLayer.scrollLayers.length);
                levelEditorLayerUI.initScorllsByLevelData(levelLayer, layer); 
            } else if (layer.type === LayerType.Random) {
                levelLayer.randomLayers = [];
                layer.dynamics.forEach((dynamic) => {   
                    var randomLayers = new LevelRandTerrainsLayersUI();
                    dynamic.group.forEach((randTerrains) => {    
                        console.log("initRandom ByLevelData randTerrains ", randTerrains.scale.x, randTerrains.scale.y);  
                        var randomLayer = new LevelRandTerrainsLayerUI();
                        randomLayer.dynamicTerrain = [];
                        randomLayer.weight = randTerrains.weight;
                        randTerrains.terrains.forEach((terrain) => {
                            var dynamicTerrain = new LevelRandTerrainUI();
                            dynamicTerrain.weight = terrain.weight;
                            assetManager.loadAny({uuid: terrain.uuid}, (err: Error, prefab:Prefab) => {
                                if (err) {
                                    return;
                                } 
                                dynamicTerrain.terrainElement = prefab;
                                dynamicTerrain.offSetX = new LayerEditorRandomRange();
                                dynamicTerrain.offSetX.min = terrain.offSetX!.min;
                                dynamicTerrain.offSetX.max = terrain.offSetX!.max;
                                
                                dynamicTerrain.offSetY = new LayerEditorRandomRange();
                                dynamicTerrain.offSetY.min = terrain.offSetY!.min;
                                dynamicTerrain.offSetY.max = terrain.offSetY!.max;
                                randomLayer.dynamicTerrain.push(dynamicTerrain);
                            });
                        });
                        randomLayers.dynamicTerrains.push(randomLayer);  
                    });   
                    levelLayer.randomLayers.push(randomLayers);
                }); 
            } else if (layer.type === LayerType.Emittier) {
                levelEditorLayerUI.initEmittierLevelData(levelLayer, layer); 
            }
            levelEditorLayerUI.initByLevelData(layer);
            layers.push(levelLayer);
        });
    }

    private _fillLevelLayerData(layer: LevelLayer, dataLayer: LevelDataLayer):void {
        dataLayer.speed = layer.speed;
        dataLayer.type = layer.type;
        dataLayer.remark = layer.remark;
        dataLayer.zIndex = layer.zIndex;
        dataLayer.totalTime = this.totalTime;

        if (layer.type === LayerType.Scroll) {   
            dataLayer.scrolls = [];
            layer.scrollLayers.forEach((scrollLayer) => {  
                var dataScroll = new LevelDataScroll();     
                scrollLayer.scrollPrefabs.forEach((scrollPrefab) => {
                    dataScroll.uuids.push(scrollPrefab?.uuid!);
                });
                dataScroll.weight = scrollLayer.weight;
                
                dataScroll.splicingMode = scrollLayer.splicingMode;
                dataScroll.offSetY = new LayerRandomRange();
                dataScroll.offSetY.min = scrollLayer.splicingOffsetY!.min;
                dataScroll.offSetY.max = scrollLayer.splicingOffsetY!.max;

                dataScroll.offSetX = new LayerRandomRange();
                dataScroll.offSetX.min = scrollLayer.splicingOffsetX!.min;
                dataScroll.offSetX.max = scrollLayer.splicingOffsetX!.max;
                dataLayer.scrolls.push(dataScroll);
                console.log("LevelEditorBaseUI fill scrollLayersData", dataLayer);
            });
        } else if (layer.type === LayerType.Random) {
            dataLayer.dynamics = [];
            layer.randomLayers.forEach((randomLayer) => {   
                var datas = new LevelDataRandTerrainsGroup();
                randomLayer.dynamicTerrains.forEach((terrains) => {
                    var data = new LevelDataRandTerrains();
                    data.terrains = [];
                    data.weight = terrains.weight;
                    terrains.dynamicTerrain.forEach((terrainElement) => {  
                        var terrainData = new LevelDataRandTerrain();
                        terrainData.weight = terrainElement.weight;
                        terrainData.uuid = terrainElement.terrainElement?.uuid!;
                        terrainData.offSetX = new LayerRandomRange(terrainElement.offSetX?.min, terrainElement.offSetX?.max);
                        terrainData.offSetY = new LayerRandomRange(terrainElement.offSetY?.min, terrainElement.offSetY?.max);
                        data.terrains.push(terrainData);
                    });
                    datas.group.push(data);
                }); 
                dataLayer.dynamics.push(datas);
            });
        } else if (layer.type === LayerType.Emittier) {
            
        }
        layer.node!.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!.fillLevelData(dataLayer);
    }

    private _fillLevelLayersData(layers: LevelLayer[], dataLayers: LevelDataLayer[]):void {
        layers.sort((a, b) => a.zIndex - b.zIndex);
        layers.forEach((layer) => {
            var levelLayer = new LevelDataLayer();
            this._fillLevelLayerData(layer, levelLayer);
            dataLayers.push(levelLayer);
        });
    }
    
    public fillLevelData(data: LevelData):void {
        data.name = this.levelname;
        data.totalTime = this.totalTime;

        data.backgroundLayer = new LevelDataBackgroundLayer();
        for (let i = 0; i < this.backgroundLayer.backgrounds.length; i++) {
            const prefab = this.backgroundLayer.backgrounds[i];
            if (prefab == null) {
                continue;
            }
            data.backgroundLayer.backgrounds.push(prefab.uuid);
        }
        this._fillLevelLayerData(this.backgroundLayer, data.backgroundLayer);

        data.floorLayers = []
        data.skyLayers = []
        this._fillLevelLayersData(this.floorLayers, data.floorLayers);
        this._fillLevelLayersData(this.skyLayers, data.skyLayers);

        LevelEditorEventShadowUI.syncFromShadow(this.eventParentNode!, [data.backgroundLayer], "background");
        LevelEditorEventShadowUI.syncFromShadow(this.eventParentNode!, data.floorLayers, "floor");
        LevelEditorEventShadowUI.syncFromShadow(this.eventParentNode!, data.skyLayers, "sky");
    }

    public playLevel(bPlay: boolean, progress: number) {
        this._setTimeNode(progress);

        this._playLayer([this.backgroundLayer], bPlay, progress);
        this._playLayer(this.floorLayers, bPlay, progress);
        this._playLayer(this.skyLayers, bPlay, progress);

        if (this._play === bPlay) {
            return;
        }

        this._play = bPlay; 
        if (bPlay) {
            this._drawMask();
        } else {
            this._drawMaskClear();
        }
        this._playEmittierNode(this._play,this.floorLayersNode!);
        this._playEmittierNode(this._play,this.skyLayersNode!);
        this._randLayerActive(this.floorLayers, this.floorLayersNode!, this._play);
        this._randLayerActive(this.skyLayers, this.skyLayersNode!, this._play);
    }

    private _playLayer(layers: LevelLayer[], bPlay: boolean, progress: number): void {
        layers.forEach((layer) => {
            layer.node!.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!.play(bPlay, progress * this.totalTime * layer.speed / 1000);
        });
    }

    private _randLayerActive(layers: LevelLayer[], parentNode: Node, bPlay: boolean):void {
        layers.forEach((layer, index) => {
            if (layer.type === LayerType.Random) {
                const layerNode = parentNode.getChildByName(`layer_${index}`);
                const dynamicNode = layerNode!.getChildByName("dynamic")!; 
            
                if (bPlay) {
                    if (dynamicNode.children.length <= 0 ) return; 
                    // 先激活所有dyna_x节点
                    dynamicNode.children.forEach(dynaNode => dynaNode.active = true);
                    
                    // 遍历所有dyna_x节点（每个对应一个地形策略组）
                    dynamicNode.children.forEach((dynaNode, groupIndex) => {
                        let length = 0; 
                        for (let i = 0; i < layer.randomLayers[groupIndex].dynamicTerrains.length; i++) {
                            length += layer.randomLayers[groupIndex].dynamicTerrains[i].dynamicTerrain.length;
                        }
                        if (dynaNode.children.length != length) {
                            return;
                        }
                        
                        let layerRandIndex = -1;
                        let groupRandIndex = -1; 
                        // 策略的总权重
                        let layersTotalWeight = 0;
                        for (const randomTerrain of layer.randomLayers[groupIndex].dynamicTerrains) {  
                            layersTotalWeight += randomTerrain.weight;
                        }
                        let layersRandWeight = Math.random() * layersTotalWeight;
                        let layersAccWeight = 0;
                        
                        if (layer.randomLayers[groupIndex].dynamicTerrains.length === 1) {
                            layerRandIndex = 0;
                        } else {
                            for (let j = 0; j < layer.randomLayers[groupIndex].dynamicTerrains.length; j++) {
                                layersAccWeight += layer.randomLayers[groupIndex].dynamicTerrains[j].weight;
                                if (layersRandWeight <= layersAccWeight) {
                                    layerRandIndex = j;
                                    console.log("LevelEditorBaseUI _rand random layerRandIndex", layerRandIndex);
                                    break;
                                }
                            }
                        }

                        // 组的总权重
                        let groupTotalWeight = 0;
                        if (layer.randomLayers[groupIndex] === null || layer.randomLayers[groupIndex].dynamicTerrains.length === 0 || layer.randomLayers[groupIndex].dynamicTerrains[layerRandIndex] === null) {
                            console.error("策划佬地形策略组没有配东西!!!!!! 在layer:",dynaNode.parent!.parent!.name);
                            return;
                        }
                        layer.randomLayers[groupIndex].dynamicTerrains[layerRandIndex].dynamicTerrain.forEach(terrains => { 
                            groupTotalWeight += terrains.weight;
                        });
                        let groupRandWeight = Math.random() * groupTotalWeight;
                        let groupAccWeight = 0;
                        
                        if (layer.randomLayers[groupIndex].dynamicTerrains[layerRandIndex].dynamicTerrain.length === 1) {
                            groupRandIndex = 0;
                        } else {
                            console.log("LevelEditorBaseUI _rand random dynamicTerrains length", layer.randomLayers[groupIndex].dynamicTerrains[layerRandIndex].dynamicTerrain.length);
                            for (let j = 0; j < layer.randomLayers[groupIndex].dynamicTerrains[layerRandIndex].dynamicTerrain.length; j++) {
                                groupAccWeight += layer.randomLayers[groupIndex].dynamicTerrains[layerRandIndex].dynamicTerrain[j].weight;
                                if (groupRandWeight <= groupAccWeight) {
                                    groupRandIndex = j;
                                    console.log("LevelEditorBaseUI _rand random groupRandIndex", groupRandIndex);
                                    break;
                                }
                            }
                        }

                        // 显示选中的策略
                        dynaNode.children.forEach((terrainNode) => {
                            const match = terrainNode.name.match(/^rand_(\d+)_(\d+)$/);
                            let active = false;
                            if (match) {
                                const index = parseInt(match[1]);
                                const itemIndex = parseInt(match[2]);
                                if (index === layerRandIndex && itemIndex === groupRandIndex) {
                                    active = true;
                                }
                                terrainNode.active = active;
                                const terrain = terrainNode.getComponent(RandTerrain)!;
                                if (terrain) {
                                    terrain.play(active);
                                }
                            } else {
                                terrainNode.active = active;
                            }
                            
                        });
                    });
                } else {
                    dynamicNode.children.forEach(dynaNode => {
                        dynaNode.active = true;
                        dynaNode.children.forEach(terrainNode => {
                            terrainNode.active = true;
                            const terrain = terrainNode.getComponent(RandTerrain)!;
                            if (terrain) {
                                terrain.play(false);
                            }
                        });
                    });
                } 
            } else if (layer.type === LayerType.Scroll) {
                parentNode.children.forEach(layerNode => {
                    // 滚动层逻辑
                    const scrollsNode = layerNode.getChildByName("scrolls")!;
                    if (scrollsNode.children.length !== layer.scrollLayers.length) {
                        return;
                    }
                    scrollsNode.children.forEach(layerNode => layerNode.active = true);
                    
                    if (bPlay) {
                        // 计算总权重
                        let totalWeight = 0;
                        for (const scrollLayer of layer.scrollLayers) {
                            totalWeight += scrollLayer.weight;
                        }
                        
                        // 随机选择要显示的滚动体
                        let randomWeight = Math.random() * totalWeight;
                        let selectedIndex = -1;
                        for (let i = 0; i < layer.scrollLayers.length; i++) {
                            randomWeight -= layer.scrollLayers[i].weight;
                            if (randomWeight <= 0) {
                                selectedIndex = i;
                                console.log("LevelEditorBase selectedIndex", selectedIndex);
                                break;
                            }
                        }
                        
                        scrollsNode.children.forEach((child, index) => {
                            const active = (index === selectedIndex);
                            child.active = active;
                            if (active) {
                                child.children.forEach(children => {
                                    if (children.getComponent(RandTerrain)) {
                                        children.getComponent(RandTerrain)!.play(true);
                                    }
                                });
                            }
                            console.log("LevelEditorBase scrollsNode name", child.name,index,"selectedIndex",selectedIndex,"active",child.active);
                        });
                    } else {
                        scrollsNode.children.forEach(child => {
                            child.active = true;
                            child.children.forEach(children => {
                                if (children.getComponent(RandTerrain)) {
                                    children.getComponent(RandTerrain)!.play(false);
                                }
                            });
                        });
                    }
                })
            }
        });
    }

    private _drawNodeGraphics(): void {
        const graphics = this._drawNode!.getComponent(Graphics);
        if (!graphics) return; 

        const drawTransform = this._drawNode!.getComponent(UITransform)!;

        const drawport = new Rect(
            this._drawNode!.getPosition().x - drawTransform.contentSize.width / 2,
            this._drawNode!.getPosition().y - drawTransform.contentSize.height / 2,
            drawTransform.contentSize.width,
            this._totalHeight
        );
        
        // Draw drawport rectangle
        graphics.strokeColor = Color.BLUE;
        graphics.lineWidth = 10;
        graphics.rect(drawport.x, drawport.y, drawport.width, drawport.height);
        graphics.stroke()

        const graphicsView = this._drawNode!.getChildByName("drawView")!.getComponent(Graphics);
        if (!graphicsView) return;

        const drawview = new Rect(
            -750 / 2,
            -1334 / 2,
            750,
            this._totalHeight
        );

        graphicsView.strokeColor = Color.RED;
        graphicsView.lineWidth = 10;
        graphicsView.rect(drawview.x, drawview.y, drawview.width, drawview.height);
        graphicsView.stroke()
    }

    private _drawMask(): void {
        if (!this._play) return;

        const maskGraphics = this._drawNode!.getChildByName("drawMask")!.getComponent(Graphics);
        if (!maskGraphics) return;

        const maskWidth = 10000;
        const maskHeight = 1334;
        maskGraphics.fillColor = Color.BLACK;
        
        maskGraphics.fillRect(
            -maskWidth / 2,
            this._drawNode!.getPosition().y + maskHeight - maskHeight / 2, 
            maskWidth, 
            maskHeight
        );
        
        maskGraphics.fillRect(
            -maskWidth / 2,
            this._drawNode!.getPosition().y - maskHeight - maskHeight / 2,  
            maskWidth, 
            maskHeight
        );
        
        maskGraphics.fillRect(
            -maskWidth - 750 / 2, 
            this._drawNode!.getPosition().y - maskHeight / 2, 
            maskWidth, 
            maskHeight
        );
        
        maskGraphics.fillRect(
            750 / 2, 
            this._drawNode!.getPosition().y - maskHeight / 2, 
            maskWidth, 
            maskHeight
        );
    }

    private _setTimeNode (progress: number) {
        if (!this._play) return;

        const timeNode = this._drawNode!.getChildByName("time")!;
        const progressNode = this._drawNode!.getChildByName("progress")!;
        if (timeNode && progressNode) {
            timeNode.active = true;
            progressNode.active = true;
            timeNode.getComponent(Label)!.string = `时间：${(this.totalTime * progress / 1000).toFixed(2)}`;
            progressNode.getComponent(Label)!.string = `进度：${progress.toFixed(2)}`;
        }
    }

    private _drawMaskClear(): void {
        const timeNode = this._drawNode!.getChildByName("time")!;
        const progressNode = this._drawNode!.getChildByName("progress")!;
        if (timeNode && progressNode) {
            timeNode.active = false;
            progressNode.active = false;
        }
        const maskGraphics = this._drawNode!.getChildByName("drawMask")!.getComponent(Graphics);
        if (!maskGraphics) return;

        maskGraphics.clear();
    }
}
import { _decorator, Component, Node, sp, CCString, CCFloat, CCBoolean, Button, Graphics } from 'cc';
import { LevelEditorUtils } from '../level/utils';
const { ccclass, property, executeInEditMode } = _decorator;

@ccclass('PlaneView')
@executeInEditMode(true)
export class PlaneView extends Component {

    // ========== Spine相关属性 ==========
    @property({
        type: sp.SkeletonData,
        displayName: "Spine骨骼数据",
        tooltip: "拖拽Spine骨骼数据到这里"
    })
    get skeletonData() {
        return this._skeletonData;
    }
    set skeletonData(value: sp.SkeletonData | null) {
        this._skeletonData = value;
        if (this.spine) {
            this.spine.skeletonData = value;
        }
    }
    _skeletonData: sp.SkeletonData | null = null;

    private _animSpeed: number = 1.0;
    private set animSpeed(value: number) {
        this._animSpeed = value;
        if (this.spine) {
            this.spine.timeScale = value;
        }
    }

    // ========== 私有属性 ==========
    private spine: sp.Skeleton | null = null;
    private currentAnimIndex: number = 0;
    private cycle: boolean = true;
    private planeNode: Node | null = null;

    onLoad() {
        this.planeNode = this.node.getChildByPath("plane");
        this.initSpine();
    }

    start() {
        let playNode = this.node.getChildByPath("ui/play")
        playNode!.getComponentsInChildren(Button).forEach(button => {
            button.node.on(Button.EventType.CLICK, ()=>{
                this.playAnimation(button.node.name)
            });
        });
        const g = this.planeNode!.getChildByName("g")!.getComponent(Graphics)!;
        g.moveTo(-64, 0);
        g.lineTo(64, 0);
        g.lineTo(64, 128);
        g.lineTo(-64, 128);
        g.close();
        g.stroke();
    }

    /**
     * 初始化Spine组件
     */
    private initSpine() {
        // 获取或创建Spine组件
        this.spine = LevelEditorUtils.getOrAddComp(this.planeNode!, sp.Skeleton);

        // 设置骨骼数据
        if (this.skeletonData) {
            this.spine.skeletonData = this.skeletonData;
        }

        // 设置播放速度
        this.spine.timeScale = this._animSpeed;

        // 设置动画完成监听
        this.spine.setCompleteListener((trackEntry) => {
            console.log(`动画播放完成: ${trackEntry.animation?.name}`);
        });
    }

    /**
     * 播放指定动画
     * @param animName 动画名称
     * @param loop 是否循环
     */
    playAnimation(animName: string) {
        if (!this.spine) {
            console.warn("Spine组件未初始化");
            return;
        }

        if (!animName) {
            console.warn("动画名称为空");
            return;
        }

        this._animSpeed = 1.0;
        try {
            this.spine.setAnimation(0, animName, this.cycle);
            console.log(`播放动画: ${animName}, 循环: ${this.cycle}`);
        } catch (error) {
            console.error(`播放动画失败: ${animName}`, error);
        }
    }

    /**
     * 停止当前动画
     */
    stopAnimation() {
        if (this.spine) {
            this.spine.clearTracks();
            console.log("停止动画");
        }
    }

    /**
     * 暂停/恢复动画
     */
    pauseResumeAnimation() {
        if (this.spine) {
            this.spine.paused = !this.spine.paused;
            console.log(this.spine.paused ? "暂停动画" : "恢复动画");
        }
    }

    setAnimationCycle() {
        this.cycle = !this.cycle;
        if (this.spine) {
            this.spine.setAnimation(0, this.spine.getCurrent(0)?.animation?.name || "", this.cycle);
        }
    }

    downAnimationSpeed() {
        this.animSpeed = Math.max(this._animSpeed / 2, 0.125)
    }
    upAnimationSpeed() {
        this.animSpeed = Math.min(this._animSpeed * 2, 8)
    }
    
    /**
     * 获取所有可用的动画名称
     */
    getAvailableAnimations(): string[] {
        if (!this.spine || !this.spine.skeletonData) {
            return [];
        }

        const animations: string[] = [];
        const skeletonData = this.spine.skeletonData.getRuntimeData();
        if (skeletonData && skeletonData.animations) {
            for (let i = 0; i < skeletonData.animations.length; i++) {
                animations.push(skeletonData.animations[i].name);
            }
        }
        return animations;
    }
}



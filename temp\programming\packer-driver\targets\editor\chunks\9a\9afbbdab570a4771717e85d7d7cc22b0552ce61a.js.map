{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/ui/map/LevelNodeCheckOutScreen.ts"], "names": ["_decorator", "Component", "UITransform", "Vec3", "GameConst", "logDebug", "logError", "GameIns", "ccclass", "property", "LevelNodeCheckOutScreen", "_remove_thresHoldTop", "_remove_thresHoldBottom", "_height", "_worldPos", "_hasEnteredScreen", "_frameCounter", "_isRecycling", "_poolName", "init", "poolName", "uiTransform", "node", "getComponent", "height", "name", "BATTLE_VIEW_BOTTOM", "BATTLE_VIEW_TOP", "update", "deltaTime", "<PERSON><PERSON><PERSON><PERSON>", "active", "_checkScreenBoundary", "getWorldPosition", "topPosition", "y", "bottomPosition", "isOutOfScreenTop", "isOutOfScreenBottom", "_recycleNode", "length", "gameMapManager", "mapObjectPoolManager", "put", "destroy"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;;AAC1CC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,Q,iBAAAA,Q;;AACVC,MAAAA,O,iBAAAA,O;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;yCAGjBU,uB,WADZF,OAAO,CAAC,yBAAD,C,gBAAR,MACaE,uBADb,SAC6CT,SAD7C,CACuD;AAAA;AAAA;AAAA,eAC3CU,oBAD2C,GACZ,CADY;AAAA,eAE3CC,uBAF2C,GAET,CAFS;AAAA,eAG3CC,OAH2C,GAGzB,CAHyB;AAAA,eAI3CC,SAJ2C,GAIzB,IAAIX,IAAJ,EAJyB;AAAA,eAK3CY,iBAL2C,GAKd,KALc;AAAA,eAM3CC,aAN2C,GAMnB,CANmB;AAAA,eAO3CC,YAP2C,GAOnB,KAPmB;AAAA,eAS3CC,SAT2C,GASvB,EATuB;AAAA;;AAW5CC,QAAAA,IAAI,CAACC,QAAD,EAAyB;AAChC,eAAKF,SAAL,GAAiBE,QAAjB;AACA,eAAKL,iBAAL,GAAyB,KAAzB;AACA,eAAKE,YAAL,GAAoB,KAApB;AAEA,gBAAMI,WAAW,GAAG,KAAKC,IAAL,CAAUC,YAAV,CAAuBrB,WAAvB,CAApB;;AACA,cAAImB,WAAJ,EAAiB;AACb,iBAAKR,OAAL,GAAeQ,WAAW,CAACG,MAA3B;AACH,WAFD,MAEO;AACH;AAAA;AAAA,sCAAS,yBAAT,EAAoC,KAAI,KAAKF,IAAL,CAAUG,IAAK,iBAAvD;AACH;;AAED,eAAKb,uBAAL,GAA+B;AAAA;AAAA,sCAAUc,kBAAzC;AACA,eAAKf,oBAAL,GAA4B;AAAA;AAAA,sCAAUgB,eAAtC;AACH;;AAEDC,QAAAA,MAAM,CAACC,SAAD,EAAoB;AACtB,cAAI,KAAKP,IAAL,CAAUQ,OAAV,KAAsB,KAAtB,IAA+B,KAAKR,IAAL,CAAUS,MAAV,KAAqB,KAAxD,EAA+D;AAC/D,cAAI,KAAKlB,OAAL,KAAiB,CAArB,EAAwB;AAExB,eAAKG,aAAL,GAJsB,CAMtB;;AACA,cAAI,KAAKA,aAAL,IAAsB,EAA1B,EAA8B;AAC1B,iBAAKA,aAAL,GAAqB,CAArB;;AACA,iBAAKgB,oBAAL;AACH;AACD;AACR;AACA;AACA;AACA;AACA;;AAEK;;AAEOA,QAAAA,oBAAoB,GAAS;AACjC,cAAI,CAAC,KAAKV,IAAL,CAAUQ,OAAX,IAAsB,KAAKR,IAAL,CAAUS,MAAV,KAAqB,KAA/C,EAAsD;AACtD,cAAI,KAAKlB,OAAL,IAAgB,CAApB,EAAuB;AACvB,cAAI,KAAKI,YAAT,EAAuB;AAEvB,eAAKK,IAAL,CAAUW,gBAAV,CAA2B,KAAKnB,SAAhC;AAEA,gBAAMoB,WAAW,GAAG,KAAKpB,SAAL,CAAeqB,CAAf,GAAmB,KAAKtB,OAAL,GAAe,CAAtD;AACA,gBAAMuB,cAAc,GAAG,KAAKtB,SAAL,CAAeqB,CAAf,GAAmB,KAAKtB,OAAL,GAAe,CAAzD,CARiC,CAUjC;;AACA,cAAI,CAAC,KAAKE,iBAAV,EAA6B;AACzB;AACA,gBAAIqB,cAAc,GAAG,KAAKzB,oBAAtB,IAA8CuB,WAAW,GAAG,KAAKtB,uBAArE,EAA8F;AAC1F,mBAAKG,iBAAL,GAAyB,IAAzB;AACH;;AACD;AACH,WAjBgC,CAmBjC;;;AACA,gBAAMsB,gBAAgB,GAAGH,WAAW,GAAG,KAAKtB,uBAA5C;AACA,gBAAM0B,mBAAmB,GAAGF,cAAc,GAAG,KAAKzB,oBAAlD;;AAEA,cAAI0B,gBAAgB,IAAIC,mBAAxB,EAA6C;AACzC,iBAAKC,YAAL;AACH;AACJ;;AAEOA,QAAAA,YAAY,GAAS;AACzB,cAAI,KAAKtB,YAAT,EAAuB;AACvB,eAAKA,YAAL,GAAoB,IAApB;;AAEA,cAAI,KAAKC,SAAL,CAAesB,MAAf,GAAwB,CAA5B,EAA+B;AAC3B;AAAA;AAAA,sCAAS,yBAAT,EAAoC,MAAK,KAAKlB,IAAL,CAAUG,IAAK,UAAS,KAAKP,SAAU,EAAhF;AACA;AAAA;AAAA,oCAAQuB,cAAR,CAAuBC,oBAAvB,CAA4CC,GAA5C,CAAgD,KAAKzB,SAArD,EAAgE,KAAKI,IAArE;AACH,WAHD,MAGO;AACH;AACA;AAAA;AAAA,sCAAS,yBAAT,EAAqC,KAAI,KAAKA,IAAL,CAAUG,IAAK,KAAxD;AACA,iBAAKH,IAAL,CAAUsB,OAAV;AACH;AACJ;;AAvFkD,O", "sourcesContent": ["import { _decorator, Component, Node, UITransform, Vec3 } from 'cc';\r\nimport { GameConst } from 'db://assets/scripts/core/base/GameConst';\r\nimport { logDebug, logError } from 'db://assets/scripts/utils/Logger';\r\nimport { GameIns } from 'db://assets/bundles/common/script/game/GameIns';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('LevelNodeCheckOutScreen')\r\nexport class LevelNodeCheckOutScreen extends Component {\r\n    private _remove_thresHoldTop: number = 0;\r\n    private _remove_thresHoldBottom: number = 0;\r\n    private _height: number = 0;\r\n    private _worldPos: Vec3 = new Vec3();\r\n    private _hasEnteredScreen: boolean = false;\r\n    private _frameCounter: number = 0;\r\n    private _isRecycling: boolean = false;\r\n\r\n    private _poolName: string = '';\r\n\r\n    public init(poolName: string): void {\r\n        this._poolName = poolName;\r\n        this._hasEnteredScreen = false;\r\n        this._isRecycling = false;\r\n\r\n        const uiTransform = this.node.getComponent(UITransform);\r\n        if (uiTransform) {\r\n            this._height = uiTransform.height;\r\n        } else {\r\n            logError('LevelNodeCheckOutScreen',`节点${this.node.name}缺少UITransform组件`);\r\n        }\r\n\r\n        this._remove_thresHoldBottom = GameConst.BATTLE_VIEW_BOTTOM;\r\n        this._remove_thresHoldTop = GameConst.BATTLE_VIEW_TOP;\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n        if (this.node.isValid === false || this.node.active === false) return;\r\n        if (this._height === 0) return;\r\n\r\n        this._frameCounter++;\r\n        \r\n        // 每30帧检测一次\r\n        if (this._frameCounter >= 30) {\r\n            this._frameCounter = 0;\r\n            this._checkScreenBoundary();\r\n        }\r\n        /*this.node.getWorldPosition(this._worldPos);\r\n        \r\n        const topPosition = this._worldPos.y + this._height / 2;\r\n\r\n        if (topPosition < this._remove_thresHold) {\r\n            this._recycleNode();\r\n        }*/\r\n    }\r\n\r\n    private _checkScreenBoundary(): void {\r\n        if (!this.node.isValid || this.node.active === false) return;\r\n        if (this._height <= 0) return;\r\n        if (this._isRecycling) return;\r\n        \r\n        this.node.getWorldPosition(this._worldPos);\r\n    \r\n        const topPosition = this._worldPos.y + this._height / 2;\r\n        const bottomPosition = this._worldPos.y - this._height / 2;\r\n        \r\n        // 检查是否进入过屏幕\r\n        if (!this._hasEnteredScreen) {\r\n            // 如果节点任何部分在屏幕内，则标记为已进入\r\n            if (bottomPosition < this._remove_thresHoldTop && topPosition > this._remove_thresHoldBottom) {\r\n                this._hasEnteredScreen = true;\r\n            }\r\n            return;\r\n        }\r\n        \r\n        // 检查是否移出屏幕（顶部或底部）\r\n        const isOutOfScreenTop = topPosition < this._remove_thresHoldBottom;\r\n        const isOutOfScreenBottom = bottomPosition > this._remove_thresHoldTop;\r\n        \r\n        if (isOutOfScreenTop || isOutOfScreenBottom) {\r\n            this._recycleNode();\r\n        }\r\n    }\r\n\r\n    private _recycleNode(): void {\r\n        if (this._isRecycling) return;\r\n        this._isRecycling = true;\r\n\r\n        if (this._poolName.length > 0) {\r\n            logDebug('LevelNodeCheckOutScreen',` 节点${this.node.name}已回收到对象池${this._poolName}`);\r\n            GameIns.gameMapManager.mapObjectPoolManager.put(this._poolName, this.node);\r\n        } else {\r\n            // 没有指定对象池时直接销毁\r\n            logDebug('LevelNodeCheckOutScreen', `节点${this.node.name}已销毁`);\r\n            this.node.destroy();\r\n        }\r\n    }\r\n}\r\n\r\n\r\n"]}
System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, v3, BaseUI, UILayer, logDebug, GameIns, DragButton, BundleName, _dec, _class, _crd, ccclass, property, MBoomUI;

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "../../../../../../scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "../../../../../../scripts/core/base/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogDebug(extras) {
    _reporterNs.report("logDebug", "../../../../../../scripts/utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../../game/GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDragButton(extras) {
    _reporterNs.report("DragButton", "../../common/components/button/DragButton", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "../../../const/BundleConst", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      v3 = _cc.v3;
    }, function (_unresolved_2) {
      BaseUI = _unresolved_2.BaseUI;
      UILayer = _unresolved_2.UILayer;
    }, function (_unresolved_3) {
      logDebug = _unresolved_3.logDebug;
    }, function (_unresolved_4) {
      GameIns = _unresolved_4.GameIns;
    }, function (_unresolved_5) {
      DragButton = _unresolved_5.DragButton;
    }, function (_unresolved_6) {
      BundleName = _unresolved_6.BundleName;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "5242f36HohPJ4HIOwEWC5Mq", "MBoomUI", undefined);

      __checkObsolete__(['_decorator', 'v3']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("MBoomUI", MBoomUI = (_dec = ccclass('MBoomUI'), _dec(_class = class MBoomUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        static getUrl() {
          return "prefab/MBoomUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Default;
        }

        static getBundleName() {
          return (_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).GameFight;
        }

        start() {
          this.node.position = v3(-230, -400, 0);
          this.getComponent(_crd && DragButton === void 0 ? (_reportPossibleCrUseOfDragButton({
            error: Error()
          }), DragButton) : DragButton).addClick(this.onClick, this);
        }

        onClick() {
          var _mainPlaneManager$mai;

          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
            error: Error()
          }), logDebug) : logDebug)("MBoomUI", "onClick", "aaaaaa");
          (_mainPlaneManager$mai = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.mainPlane) == null || _mainPlaneManager$mai.CastSkill(1);
        }

        async onShow() {}

        async onHide(...args) {}

        async onClose(...args) {}

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=6a1e8f2712cde486f3cd00c2d3d5926bf07ec48c.js.map
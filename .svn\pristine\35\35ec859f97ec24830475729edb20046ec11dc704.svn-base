/**
 * 路径编辑器
 */
'use strict';
Object.defineProperty(exports, "__esModule", { value: true });
exports.$ = exports.template = void 0;
exports.update = update;
exports.ready = ready;
const { updatePropByDump, disconnectGroup } = require('./../../prop');
exports.template = `
<div class="component-container"></div>
<ui-prop>
    <ui-button class="btn-add">添加路径点</ui-button>
    <ui-button class="btn-save">保存</ui-button>
    <ui-button class="btn-clear">清空路径</ui-button>
</ui-prop>
`;
exports.$ = {
    componentContainer: '.component-container',
    btnAdd: '.btn-add',
    btnSave: '.btn-save',
    btnClear: '.btn-clear',
};
function update(dump) {
    updatePropByDump(this, dump);
    this.dump = dump;
}
async function ready() {
    disconnectGroup(this);
    this.$.btnAdd.addEventListener('confirm', async () => {
        var _a;
        // console.log('add path point', this.dump);
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'level-editor',
            method: 'addPathPoint',
            args: [(_a = this.dump) === null || _a === void 0 ? void 0 : _a.value.uuid.value]
        });
    });
    this.$.btnSave.addEventListener('confirm', async () => {
        var _a;
        // console.log('save path', this.dump);
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'level-editor',
            method: 'savePath',
            args: [(_a = this.dump) === null || _a === void 0 ? void 0 : _a.value.uuid.value]
        });
    });
    this.$.btnClear.addEventListener('confirm', async () => {
        var _a;
        // console.log('clear path', this.dump);
        // @ts-ignore
        Editor.Message.request('scene', 'execute-scene-script', {
            name: 'level-editor',
            method: 'clearPath',
            args: [(_a = this.dump) === null || _a === void 0 ? void 0 : _a.value.uuid.value]
        });
    });
}
//# sourceMappingURL=data:application/json;base64,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
{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/data/PathData.ts"], "names": ["_decorator", "Vec2", "CCFloat", "CCInteger", "Enum", "eOrientationType", "ExpressionValue", "ccclass", "property", "ePathType", "PathPoint", "type", "displayName", "range", "slide", "tooltip", "visible", "formerlySerializedAs", "orientationType", "Fixed", "Rotate", "speedStr", "speedExpr", "raw", "value", "isSubdivided", "_isSubdivided", "constructor", "x", "y", "position", "speed", "eval", "toString", "fromJSON", "data", "smoothness", "stayDuration", "orientationParam", "PathData", "editor<PERSON><PERSON><PERSON>", "pathType", "Circle", "Custom", "_cachedSubdividedPoints", "toJSON", "name", "radius", "centerX", "centerY", "startAngle", "startIdx", "endIdx", "points", "closed", "map", "p", "point", "pathData", "catmullRomPoint", "t", "p0", "p1", "p2", "p3", "lerp", "t2", "t3", "catmullRom", "linear", "getStartPoint", "length", "getSubdividedPoints", "regen", "generateSubdividedPointsInternal", "effectivePoints", "getPoints", "subdivided", "pointCount", "firstPoint", "push", "segmentCount", "i", "getControlPoint", "pointNext", "startSmoothness", "endSmoothness", "segmentPoints", "adaptiveSubdivision", "lastPoint", "distance", "pop", "startIndex", "Math", "max", "min", "endIndex", "slice", "point1", "point2", "max<PERSON><PERSON><PERSON>", "avgSmoothness", "subdivisionPoints", "subdivideRecursive", "t1", "depth", "createCurveInterpolatedPoint", "tMid", "startPos", "midPos", "endPos", "linearMid", "error", "curvature", "calculateCurvature", "baseThreshold", "curvatureT<PERSON>eshold", "leftPoints", "rightPoints", "v1", "subtract", "v2", "len1", "len2", "normalize", "dot", "clampedDot", "angle", "acos", "PI", "pos", "newPoint", "index", "wrappedIndex", "add"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;AACtCC,MAAAA,gB,iBAAAA,gB;;AACAC,MAAAA,e,iBAAAA,e;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;;2BAElBS,S,0BAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;eAAAA,S;;AAKZ;AACA;AACA;;;2BAEaC,S,WADZH,OAAO,CAAC,WAAD,C,UAEHC,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAET,OAAR;AAAiBU,QAAAA,WAAW,EAAE;AAA9B,OAAD,C,UAGRJ,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAET,OAAR;AAAiBU,QAAAA,WAAW,EAAE;AAA9B,OAAD,C,UAGRJ,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAET,OAAR;AAAiBU,QAAAA,WAAW,EAAE,MAA9B;AAAsCC,QAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,CAAJ,CAA7C;AAAqDC,QAAAA,KAAK,EAAE,IAA5D;AAAkEC,QAAAA,OAAO,EAAE;AAA3E,OAAD,C,UAGRP,QAAQ,CAAC;AAACQ,QAAAA,OAAO,EAAC,KAAT;AAAgBC,QAAAA,oBAAoB,EAAE;AAAtC,OAAD,C,UAERT,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAER,SAAR;AAAmBS,QAAAA,WAAW,EAAE,IAAhC;AAAsCG,QAAAA,OAAO,EAAE;AAA/C,OAAD,C,UAIRP,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAER,SAAR;AAAmBS,QAAAA,WAAW,EAAE,MAAhC;AAAwCG,QAAAA,OAAO,EAAE;AAAjD,OAAD,C,UAGRP,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAEP,IAAI;AAAA;AAAA,iDAAZ;AAAgCQ,QAAAA,WAAW,EAAE,MAA7C;AAAqDG,QAAAA,OAAO,EAAE;AAA9D,OAAD,C,UAGRP,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAER,SAAR;AAAmBS,QAAAA,WAAW,EAAE,MAAhC;AAAwCG,QAAAA,OAAO,EAAE,aAAjD;;AACNC,QAAAA,OAAO,GAAG;AACN;AACA,iBAAO,KAAKE,eAAL,KAAyB;AAAA;AAAA,oDAAiBC,KAA1C,IAAmD,KAAKD,eAAL,KAAyB;AAAA;AAAA,oDAAiBE,MAApG;AACH;;AAJK,OAAD,C,2BAvBb,MACaV,SADb,CACuB;AAaP,YAARW,QAAQ,GAAW;AAAE,iBAAO,KAAKC,SAAL,CAAeC,GAAtB;AAA4B;;AACzC,YAARF,QAAQ,CAACG,KAAD,EAAgB;AAAE,eAAKF,SAAL,CAAeC,GAAf,GAAqBC,KAArB;AAA6B;;AAkBpC,YAAZC,YAAY,GAAY;AAC/B,iBAAO,KAAKC,aAAZ;AACH;;AACsB,YAAZD,YAAY,CAACD,KAAD,EAAiB;AACpC,eAAKE,aAAL,GAAqBF,KAArB;AACH;;AAEDG,QAAAA,WAAW,CAACC,CAAD,EAAgBC,CAAhB,EAA+B;AAAA,cAA9BD,CAA8B;AAA9BA,YAAAA,CAA8B,GAAlB,CAAkB;AAAA;;AAAA,cAAfC,CAAe;AAAfA,YAAAA,CAAe,GAAH,CAAG;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAT1C;AAS0C,eARlCH,aAQkC,GART,KAQS;AACtC,eAAKE,CAAL,GAASA,CAAT;AACA,eAAKC,CAAL,GAASA,CAAT;AACH;;AAEkB,YAARC,QAAQ,GAAS;AACxB,iBAAO,IAAI7B,IAAJ,CAAS,KAAK2B,CAAd,EAAiB,KAAKC,CAAtB,CAAP;AACH;;AAEkB,YAARC,QAAQ,CAACN,KAAD,EAAc;AAC7B,eAAKI,CAAL,GAASJ,KAAK,CAACI,CAAf;AACA,eAAKC,CAAL,GAASL,KAAK,CAACK,CAAf;AACH;;AAEe,YAALE,KAAK,GAAW;AACvB,iBAAO,KAAKT,SAAL,CAAeU,IAAf,EAAP;AACH;;AAEe,YAALD,KAAK,CAACP,KAAD,EAAgB;AAC5B,eAAKF,SAAL,CAAeC,GAAf,GAAqBC,KAAK,CAACS,QAAN,EAArB;AACH;;AAEMC,QAAAA,QAAQ,CAACC,IAAD,EAAkB;AAC7B,eAAKP,CAAL,GAASO,IAAI,CAACP,CAAL,IAAU,CAAnB;AACA,eAAKC,CAAL,GAASM,IAAI,CAACN,CAAL,IAAU,CAAnB;AACA,eAAKO,UAAL,GAAkBD,IAAI,CAACC,UAAL,IAAmB,CAArC;AACA,eAAKL,KAAL,GAAaI,IAAI,CAACJ,KAAL,IAAc,GAA3B;AACA,eAAKM,YAAL,GAAoBF,IAAI,CAACE,YAAL,IAAqB,CAAzC;AACA,eAAKnB,eAAL,GAAuBiB,IAAI,CAACjB,eAAL,IAAwB,CAA/C;AACA,eAAKoB,gBAAL,GAAwBH,IAAI,CAACG,gBAAL,IAAyB,CAAjD;AACH;;AArEkB,O;;;;;iBAEA,C;;;;;;;iBAGA,C;;;;;;;iBAGS,G;;;;;;;iBAGQ;AAAA;AAAA,kDAAoB,KAApB,C;;;;;;;iBAMN,C;;;;;;;iBAGa,C;;;;;;;iBAQT,C;;;AA4CtC;AACA;AACA;;;0BAEaC,Q,aADZhC,OAAO,CAAC,UAAD,C,WAEHC,QAAQ,CAAC;AAAEI,QAAAA,WAAW,EAAE,MAAf;AAAuB4B,QAAAA,UAAU,EAAE;AAAnC,OAAD,C,WAGRhC,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAEP,IAAI,CAACK,SAAD,CAAZ;AAAyBG,QAAAA,WAAW,EAAE;AAAtC,OAAD,C,WAGRJ,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAET,OAAR;AAAiBU,QAAAA,WAAW,EAAE,IAA9B;;AAAoCI,QAAAA,OAAO,GAAG;AACpD;AACA,iBAAO,KAAKyB,QAAL,KAAkBhC,SAAS,CAACiC,MAAnC;AACH;;AAHS,OAAD,C,WAKRlC,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAET,OAAR;AAAiBU,QAAAA,WAAW,EAAE,KAA9B;;AAAqCI,QAAAA,OAAO,GAAG;AACrD;AACA,iBAAO,KAAKyB,QAAL,KAAkBhC,SAAS,CAACiC,MAAnC;AACH;;AAHS,OAAD,C,WAKRlC,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAET,OAAR;AAAiBU,QAAAA,WAAW,EAAE,KAA9B;;AAAqCI,QAAAA,OAAO,GAAG;AACrD;AACA,iBAAO,KAAKyB,QAAL,KAAkBhC,SAAS,CAACiC,MAAnC;AACH;;AAHS,OAAD,C,WAKRlC,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAER,SAAR;AAAmBS,QAAAA,WAAW,EAAE,MAAhC;;AAAwCI,QAAAA,OAAO,GAAG;AACxD;AACA,iBAAO,KAAKyB,QAAL,KAAkBhC,SAAS,CAACiC,MAAnC;AACH;;AAHS,OAAD,C,WAMRlC,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAER,SAAR;AAAmBS,QAAAA,WAAW,EAAE,UAAhC;;AAA4CI,QAAAA,OAAO,GAAG;AAC5D;AACA,iBAAO,KAAKyB,QAAL,KAAkBhC,SAAS,CAACkC,MAAnC;AACH;;AAHS,OAAD,C,WAMRnC,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAER,SAAR;AAAmBS,QAAAA,WAAW,EAAE,iBAAhC;;AAAmDI,QAAAA,OAAO,GAAG;AACnE;AACA,iBAAO,KAAKyB,QAAL,KAAkBhC,SAAS,CAACkC,MAAnC;AACH;;AAHS,OAAD,C,WAMRnC,QAAQ,CAAC;AAAEG,QAAAA,IAAI,EAAE,CAACD,SAAD,CAAR;AAAqBE,QAAAA,WAAW,EAAE,KAAlC;;AAAyCI,QAAAA,OAAO,GAAG;AACzD;AACA,iBAAO,KAAKyB,QAAL,KAAkBhC,SAAS,CAACkC,MAAnC;AACH;;AAHS,OAAD,C,WAMRnC,QAAQ,CAAC;AAAEI,QAAAA,WAAW,EAAE,QAAf;AAAyBG,QAAAA,OAAO,EAAE,UAAlC;;AAA8CC,QAAAA,OAAO,GAAG;AAC9D;AACA,iBAAO,KAAKyB,QAAL,KAAkBhC,SAAS,CAACkC,MAAnC;AACH;;AAHS,OAAD,C,8BA/Cb,MACaJ,QADb,CACsB;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAoDlB;AApDkB,eAqDVK,uBArDU,GAqDoC,IArDpC;AAAA;;AAuDlB;AACJ;AACA;AACWC,QAAAA,MAAM,GAAQ;AACjB,cAAI,KAAKJ,QAAL,KAAkBhC,SAAS,CAACiC,MAAhC,EAAwC;AACpC,mBAAO;AACHI,cAAAA,IAAI,EAAE,KAAKA,IADR;AAEHL,cAAAA,QAAQ,EAAE,KAAKA,QAFZ;AAGHM,cAAAA,MAAM,EAAE,KAAKA,MAHV;AAIHC,cAAAA,OAAO,EAAE,KAAKA,OAJX;AAKHC,cAAAA,OAAO,EAAE,KAAKA,OALX;AAMHC,cAAAA,UAAU,EAAE,KAAKA;AANd,aAAP;AAQH;;AAED,iBAAO;AACHJ,YAAAA,IAAI,EAAE,KAAKA,IADR;AAEHL,YAAAA,QAAQ,EAAE,KAAKA,QAFZ;AAGHU,YAAAA,QAAQ,EAAE,KAAKA,QAHZ;AAIHC,YAAAA,MAAM,EAAE,KAAKA,MAJV;AAKHC,YAAAA,MAAM,EAAE,KAAKA,MALV;AAMHC,YAAAA,MAAM,EAAE,KAAKA;AANV,WAAP;AAQH;AAED;AACJ;AACA;;;AACWpB,QAAAA,QAAQ,CAACC,IAAD,EAAkB;AAC7B,cAAI,CAACA,IAAL,EAAW;AACX,eAAKW,IAAL,GAAYX,IAAI,CAACW,IAAL,IAAa,EAAzB;AACA,eAAKL,QAAL,GAAgBN,IAAI,CAACM,QAAL,IAAiBhC,SAAS,CAACkC,MAA3C,CAH6B,CAI7B;;AACA,eAAKC,uBAAL,GAA+B,IAA/B;;AACA,cAAI,KAAKH,QAAL,KAAkBhC,SAAS,CAACiC,MAAhC,EAAwC;AACpC,iBAAKK,MAAL,GAAcZ,IAAI,CAACY,MAAL,IAAe,GAA7B;AACA,iBAAKC,OAAL,GAAeb,IAAI,CAACa,OAAL,IAAgB,CAA/B;AACA,iBAAKC,OAAL,GAAed,IAAI,CAACc,OAAL,IAAgB,CAA/B;AACA,iBAAKC,UAAL,GAAkBf,IAAI,CAACe,UAAL,IAAmB,CAArC;AACA;AACH;;AAED,eAAKC,QAAL,GAAgBhB,IAAI,CAACgB,QAAL,IAAiB,CAAjC;AACA,eAAKC,MAAL,GAAcjB,IAAI,CAACiB,MAAL,IAAe,CAAC,CAA9B;AACA,eAAKC,MAAL,GAAclB,IAAI,CAACkB,MAAL,GAAclB,IAAI,CAACkB,MAAL,CAAYE,GAAZ,CAAiBC,CAAD,IAAY;AACpD,gBAAMC,KAAK,GAAG,IAAI/C,SAAJ,EAAd;AACA+C,YAAAA,KAAK,CAACvB,QAAN,CAAesB,CAAf;AACA,mBAAOC,KAAP;AACH,WAJ2B,CAAd,GAIT,EAJL;AAKA,eAAKH,MAAL,GAAcnB,IAAI,CAACmB,MAAL,IAAe,KAA7B;AACH;AAED;AACJ;AACA;;;AAC0B,eAARpB,QAAQ,CAACC,IAAD,EAAsB;AACxC,cAAMuB,QAAQ,GAAG,IAAInB,QAAJ,EAAjB;AACAmB,UAAAA,QAAQ,CAACxB,QAAT,CAAkBC,IAAlB;AACA,iBAAOuB,QAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACiC,eAAfC,eAAe,CAACC,CAAD,EAAYC,EAAZ,EAAsBC,EAAtB,EAAgCC,EAAhC,EAA0CC,EAA1C,EAAoD5B,UAApD,EAAoF;AAAA,cAAhCA,UAAgC;AAAhCA,YAAAA,UAAgC,GAAX,GAAW;AAAA;;AAC7G;AACA,cAAIA,UAAU,KAAK,CAAnB,EAAsB;AAClB,mBAAOnC,IAAI,CAACgE,IAAL,CAAU,IAAIhE,IAAJ,EAAV,EAAsB6D,EAAtB,EAA0BC,EAA1B,EAA8BH,CAA9B,CAAP;AACH;;AAED,cAAMM,EAAE,GAAGN,CAAC,GAAGA,CAAf;AACA,cAAMO,EAAE,GAAGD,EAAE,GAAGN,CAAhB,CAP6G,CAS7G;;AACA,cAAMQ,UAAU,GAAG,IAAInE,IAAJ,EAAnB;AACAmE,UAAAA,UAAU,CAACxC,CAAX,GAAe,OACV,IAAIkC,EAAE,CAAClC,CAAR,GACA,CAAC,CAACiC,EAAE,CAACjC,CAAJ,GAAQmC,EAAE,CAACnC,CAAZ,IAAiBgC,CADjB,GAEA,CAAC,IAAIC,EAAE,CAACjC,CAAP,GAAW,IAAIkC,EAAE,CAAClC,CAAlB,GAAsB,IAAImC,EAAE,CAACnC,CAA7B,GAAiCoC,EAAE,CAACpC,CAArC,IAA0CsC,EAF1C,GAGA,CAAC,CAACL,EAAE,CAACjC,CAAJ,GAAQ,IAAIkC,EAAE,CAAClC,CAAf,GAAmB,IAAImC,EAAE,CAACnC,CAA1B,GAA8BoC,EAAE,CAACpC,CAAlC,IAAuCuC,EAJ5B,CAAf;AAOAC,UAAAA,UAAU,CAACvC,CAAX,GAAe,OACV,IAAIiC,EAAE,CAACjC,CAAR,GACA,CAAC,CAACgC,EAAE,CAAChC,CAAJ,GAAQkC,EAAE,CAAClC,CAAZ,IAAiB+B,CADjB,GAEA,CAAC,IAAIC,EAAE,CAAChC,CAAP,GAAW,IAAIiC,EAAE,CAACjC,CAAlB,GAAsB,IAAIkC,EAAE,CAAClC,CAA7B,GAAiCmC,EAAE,CAACnC,CAArC,IAA0CqC,EAF1C,GAGA,CAAC,CAACL,EAAE,CAAChC,CAAJ,GAAQ,IAAIiC,EAAE,CAACjC,CAAf,GAAmB,IAAIkC,EAAE,CAAClC,CAA1B,GAA8BmC,EAAE,CAACnC,CAAlC,IAAuCsC,EAJ5B,CAAf,CAlB6G,CAyB7G;;AACA,cAAI/B,UAAU,GAAG,CAAjB,EAAoB;AAChB,gBAAMiC,MAAM,GAAGpE,IAAI,CAACgE,IAAL,CAAU,IAAIhE,IAAJ,EAAV,EAAsB6D,EAAtB,EAA0BC,EAA1B,EAA8BH,CAA9B,CAAf;AACA,mBAAO3D,IAAI,CAACgE,IAAL,CAAU,IAAIhE,IAAJ,EAAV,EAAsBoE,MAAtB,EAA8BD,UAA9B,EAA0ChC,UAA1C,CAAP;AACH;;AAED,iBAAOgC,UAAP;AACH;;AAEME,QAAAA,aAAa,GAAmB;AACnC,cAAI,KAAKnB,QAAL,GAAgB,CAAhB,IAAqB,KAAKA,QAAL,IAAiB,KAAKE,MAAL,CAAYkB,MAAtD,EAA8D;AAC1D,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAKlB,MAAL,CAAY,KAAKF,QAAjB,CAAP;AACH;AAED;AACJ;AACA;AACA;;;AACWqB,QAAAA,mBAAmB,CAACC,KAAD,EAAsC;AAAA,cAArCA,KAAqC;AAArCA,YAAAA,KAAqC,GAApB,KAAoB;AAAA;;AAC5D,cAAK,CAAC,KAAK7B,uBAAN,IAAiC,KAAKA,uBAAL,CAA6B2B,MAA7B,KAAwC,CAA1E,IAAgFE,KAApF,EAA2F;AACvF,iBAAK7B,uBAAL,GAA+B,KAAK8B,gCAAL,EAA/B;AACH;;AAED,iBAAO,KAAK9B,uBAAZ;AACH;AAED;AACJ;AACA;;;AACY8B,QAAAA,gCAAgC,GAAgB;AACpD,cAAMC,eAAe,GAAG,KAAKC,SAAL,EAAxB;;AAEA,cAAID,eAAe,CAACJ,MAAhB,GAAyB,CAA7B,EAAgC;AAC5B,mBAAOI,eAAP;AACH;;AAED,cAAME,UAAuB,GAAG,EAAhC;AACA,cAAMC,UAAU,GAAGH,eAAe,CAACJ,MAAnC,CARoD,CAUpD;;AACA,cAAMQ,UAAU,GAAGJ,eAAe,CAAC,CAAD,CAAlC;AACAE,UAAAA,UAAU,CAACG,IAAX,CAAgBD,UAAhB,EAZoD,CAcpD;;AACA,cAAME,YAAY,GAAG,KAAK3B,MAAL,GAAcwB,UAAd,GAA2BA,UAAU,GAAG,CAA7D,CAfoD,CAgBpD;;AACA,eAAK,IAAII,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,YAApB,EAAkCC,CAAC,EAAnC,EAAuC;AACnC,gBAAMrB,EAAE,GAAG,KAAKsB,eAAL,CAAqBR,eAArB,EAAsCO,CAAC,GAAG,CAA1C,CAAX;AACA,gBAAMpB,EAAE,GAAGa,eAAe,CAACO,CAAD,CAAf,CAAmBpD,QAA9B;AACA,gBAAMiC,EAAE,GAAG,KAAKoB,eAAL,CAAqBR,eAArB,EAAsCO,CAAC,GAAG,CAA1C,CAAX;AACA,gBAAMlB,EAAE,GAAG,KAAKmB,eAAL,CAAqBR,eAArB,EAAsCO,CAAC,GAAG,CAA1C,CAAX;AAEA,gBAAMzB,KAAK,GAAGkB,eAAe,CAACO,CAAD,CAA7B;AACA,gBAAME,SAAS,GAAGT,eAAe,CAAC,CAACO,CAAC,GAAG,CAAL,IAAUJ,UAAX,CAAjC;AAEA,gBAAMO,eAAe,GAAG5B,KAAK,CAACrB,UAA9B;AACA,gBAAMkD,aAAa,GAAGF,SAAS,CAAChD,UAAhC,CAVmC,CAYnC;;AACA,gBAAIiD,eAAe,KAAK,CAApB,IAAyBC,aAAa,KAAK,CAA/C,EAAkD;AAC9C;AACAT,cAAAA,UAAU,CAACG,IAAX,CAAgBI,SAAhB;AACH,aAHD,MAGO;AACH;AACA,kBAAMG,aAAa,GAAG,KAAKC,mBAAL,CAAyB3B,EAAzB,EAA6BC,EAA7B,EAAiCC,EAAjC,EAAqCC,EAArC,EAAyCP,KAAzC,EAAgD2B,SAAhD,CAAtB;AACAP,cAAAA,UAAU,CAACG,IAAX,CAAgB,GAAGO,aAAnB;AACH;AACJ,WAtCmD,CAwCpD;;;AACA,cAAI,KAAKjC,MAAL,IAAeuB,UAAU,CAACN,MAAX,GAAoB,CAAvC,EAA0C;AACtC,gBAAMQ,WAAU,GAAGF,UAAU,CAAC,CAAD,CAA7B;AACA,gBAAMY,SAAS,GAAGZ,UAAU,CAACA,UAAU,CAACN,MAAX,GAAoB,CAArB,CAA5B;AACA,gBAAMmB,QAAQ,GAAGzF,IAAI,CAACyF,QAAL,CAAcX,WAAU,CAACjD,QAAzB,EAAmC2D,SAAS,CAAC3D,QAA7C,CAAjB;;AAEA,gBAAI4D,QAAQ,GAAG,GAAf,EAAoB;AAChBb,cAAAA,UAAU,CAACc,GAAX;AACH;AACJ;;AAED,iBAAOd,UAAP;AACH;AAED;AACJ;AACA;;;AACYD,QAAAA,SAAS,GAAgB;AAC7B,cAAI,KAAKvB,MAAL,CAAYkB,MAAZ,KAAuB,CAA3B,EAA8B,OAAO,EAAP;AAE9B,cAAMqB,UAAU,GAAGC,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYD,IAAI,CAACE,GAAL,CAAS,KAAK5C,QAAd,EAAwB,KAAKE,MAAL,CAAYkB,MAAZ,GAAqB,CAA7C,CAAZ,CAAnB;AACA,cAAMyB,QAAQ,GAAG,KAAK5C,MAAL,KAAgB,CAAC,CAAjB,GAAqB,KAAKC,MAAL,CAAYkB,MAAZ,GAAqB,CAA1C,GAA8CsB,IAAI,CAACC,GAAL,CAASF,UAAT,EAAqBC,IAAI,CAACE,GAAL,CAAS,KAAK3C,MAAd,EAAsB,KAAKC,MAAL,CAAYkB,MAAZ,GAAqB,CAA3C,CAArB,CAA/D;AAEA,iBAAO,KAAKlB,MAAL,CAAY4C,KAAZ,CAAkBL,UAAlB,EAA8BI,QAAQ,GAAG,CAAzC,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACYR,QAAAA,mBAAmB,CACvB3B,EADuB,EACbC,EADa,EACHC,EADG,EACOC,EADP,EAEvBkC,MAFuB,EAEJC,MAFI,EAGvBC,QAHuB,EAIZ;AAAA,cADXA,QACW;AADXA,YAAAA,QACW,GADQ,CACR;AAAA;;AACX,cAAMC,aAAa,GAAG,CAACH,MAAM,CAAC9D,UAAP,GAAoB+D,MAAM,CAAC/D,UAA5B,IAA0C,CAAhE,CADW,CAGX;;AACA,cAAIiE,aAAa,KAAK,CAAtB,EAAyB;AACrB,mBAAO,CAACF,MAAD,CAAP;AACH,WANU,CAQX;;;AACA,cAAMG,iBAAiB,GAAG/D,QAAQ,CAACgE,kBAAT,CAA4B1C,EAA5B,EAAgCC,EAAhC,EAAoCC,EAApC,EAAwCC,EAAxC,EAA4CkC,MAA5C,EAAoDC,MAApD,EAA4D,CAA5D,EAA+D,QAA/D,EAAyE,CAAzE,EAA4EC,QAA5E,EAAsFC,aAAtF,CAA1B,CATW,CAWX;;AACAC,UAAAA,iBAAiB,CAACtB,IAAlB,CAAuBmB,MAAvB;AAEA,iBAAOG,iBAAP;AACH;AAED;AACJ;AACA;;;AAC6B,eAAlBC,kBAAkB,CACrB1C,EADqB,EACXC,EADW,EACDC,EADC,EACSC,EADT,EAErBkC,MAFqB,EAEFC,MAFE,EAGrBK,EAHqB,EAGTtC,EAHS,EAGGuC,KAHH,EAGkBL,QAHlB,EAGoChE,UAHpC,EAIV;AACX;AACA,cAAIqE,KAAK,IAAIL,QAAb,EAAuB;AACnB,mBAAO,CAAC7D,QAAQ,CAACmE,4BAAT,CAAsC7C,EAAtC,EAA0CC,EAA1C,EAA8CC,EAA9C,EAAkDC,EAAlD,EAAsDkC,MAAtD,EAA8DC,MAA9D,EAAsEjC,EAAtE,EAA0E9B,UAA1E,CAAD,CAAP;AACH;;AAED,cAAMuE,IAAI,GAAG,CAACH,EAAE,GAAGtC,EAAN,IAAY,CAAzB,CANW,CAQX;;AACA,cAAM0C,QAAQ,GAAGrE,QAAQ,CAACoB,eAAT,CAAyB6C,EAAzB,EAA6B3C,EAA7B,EAAiCC,EAAjC,EAAqCC,EAArC,EAAyCC,EAAzC,EAA6C5B,UAA7C,CAAjB;AACA,cAAMyE,MAAM,GAAGtE,QAAQ,CAACoB,eAAT,CAAyBgD,IAAzB,EAA+B9C,EAA/B,EAAmCC,EAAnC,EAAuCC,EAAvC,EAA2CC,EAA3C,EAA+C5B,UAA/C,CAAf;AACA,cAAM0E,MAAM,GAAGvE,QAAQ,CAACoB,eAAT,CAAyBO,EAAzB,EAA6BL,EAA7B,EAAiCC,EAAjC,EAAqCC,EAArC,EAAyCC,EAAzC,EAA6C5B,UAA7C,CAAf,CAXW,CAaX;;AACA,cAAM2E,SAAS,GAAG9G,IAAI,CAACgE,IAAL,CAAU,IAAIhE,IAAJ,EAAV,EAAsB2G,QAAtB,EAAgCE,MAAhC,EAAwC,GAAxC,CAAlB,CAdW,CAgBX;;AACA,cAAME,KAAK,GAAG/G,IAAI,CAACyF,QAAL,CAAcmB,MAAd,EAAsBE,SAAtB,CAAd,CAjBW,CAmBX;;AACA,cAAME,SAAS,GAAG1E,QAAQ,CAAC2E,kBAAT,CAA4BN,QAA5B,EAAsCC,MAAtC,EAA8CC,MAA9C,CAAlB,CApBW,CAsBX;;AACA,cAAMpB,QAAQ,GAAGzF,IAAI,CAACyF,QAAL,CAAckB,QAAd,EAAwBE,MAAxB,CAAjB;AACA,cAAMK,aAAa,GAAGtB,IAAI,CAACC,GAAL,CAAS,GAAT,EAAcJ,QAAQ,GAAG,IAAzB,CAAtB,CAxBW,CAwB2C;;AACtD,cAAM0B,kBAAkB,GAAGD,aAAa,IAAI,IAAIF,SAAS,GAAG,EAApB,CAAxC,CAzBW,CAyBsD;AACjE;AACA;;AACA,cAAID,KAAK,GAAGI,kBAAZ,EAAgC;AAC5B,mBAAO,CAAC7E,QAAQ,CAACmE,4BAAT,CAAsC7C,EAAtC,EAA0CC,EAA1C,EAA8CC,EAA9C,EAAkDC,EAAlD,EAAsDkC,MAAtD,EAA8DC,MAA9D,EAAsEjC,EAAtE,EAA0E9B,UAA1E,CAAD,CAAP;AACH,WA9BU,CAgCX;;;AACA,cAAMiF,UAAU,GAAG9E,QAAQ,CAACgE,kBAAT,CAA4B1C,EAA5B,EAAgCC,EAAhC,EAAoCC,EAApC,EAAwCC,EAAxC,EAA4CkC,MAA5C,EAAoDC,MAApD,EAA4DK,EAA5D,EAAgEG,IAAhE,EAAsEF,KAAK,GAAG,CAA9E,EAAiFL,QAAjF,EAA2FhE,UAA3F,CAAnB;AACA,cAAMkF,WAAW,GAAG/E,QAAQ,CAACgE,kBAAT,CAA4B1C,EAA5B,EAAgCC,EAAhC,EAAoCC,EAApC,EAAwCC,EAAxC,EAA4CkC,MAA5C,EAAoDC,MAApD,EAA4DQ,IAA5D,EAAkEzC,EAAlE,EAAsEuC,KAAK,GAAG,CAA9E,EAAiFL,QAAjF,EAA2FhE,UAA3F,CAApB;AAEA,iBAAO,CAAC,GAAGiF,UAAJ,EAAgB,GAAGC,WAAnB,CAAP;AACH;AAED;AACJ;AACA;;;AAC6B,eAAlBJ,kBAAkB,CAACpD,EAAD,EAAWC,EAAX,EAAqBC,EAArB,EAAuC;AAC5D,cAAMuD,EAAE,GAAGtH,IAAI,CAACuH,QAAL,CAAc,IAAIvH,IAAJ,EAAd,EAA0B8D,EAA1B,EAA8BD,EAA9B,CAAX;AACA,cAAM2D,EAAE,GAAGxH,IAAI,CAACuH,QAAL,CAAc,IAAIvH,IAAJ,EAAd,EAA0B+D,EAA1B,EAA8BD,EAA9B,CAAX,CAF4D,CAI5D;;AACA,cAAM2D,IAAI,GAAGH,EAAE,CAAChD,MAAH,EAAb;AACA,cAAMoD,IAAI,GAAGF,EAAE,CAAClD,MAAH,EAAb;AACA,cAAImD,IAAI,GAAG,KAAP,IAAgBC,IAAI,GAAG,KAA3B,EAAkC,OAAO,CAAP;AAElCJ,UAAAA,EAAE,CAACK,SAAH;AACAH,UAAAA,EAAE,CAACG,SAAH,GAV4D,CAY5D;;AACA,cAAMC,GAAG,GAAG5H,IAAI,CAAC4H,GAAL,CAASN,EAAT,EAAaE,EAAb,CAAZ;AACA,cAAMK,UAAU,GAAGjC,IAAI,CAACC,GAAL,CAAS,CAAC,CAAV,EAAaD,IAAI,CAACE,GAAL,CAAS,CAAT,EAAY8B,GAAZ,CAAb,CAAnB;AACA,cAAME,KAAK,GAAGlC,IAAI,CAACmC,IAAL,CAAUF,UAAV,CAAd,CAf4D,CAiB5D;;AACA,iBAAOC,KAAK,GAAGlC,IAAI,CAACoC,EAApB;AACH;AAED;AACJ;AACA;;;AACuC,eAA5BvB,4BAA4B,CAC/B7C,EAD+B,EACrBC,EADqB,EACXC,EADW,EACDC,EADC,EAE/BkC,MAF+B,EAEZC,MAFY,EAG/BvC,CAH+B,EAGpBxB,UAHoB,EAItB;AACT;AACA,cAAM8F,GAAG,GAAG3F,QAAQ,CAACoB,eAAT,CAAyBC,CAAzB,EAA4BC,EAA5B,EAAgCC,EAAhC,EAAoCC,EAApC,EAAwCC,EAAxC,EAA4C5B,UAA5C,CAAZ;AACA,cAAM+F,QAAQ,GAAG,IAAIzH,SAAJ,CAAcwH,GAAG,CAACtG,CAAlB,EAAqBsG,GAAG,CAACrG,CAAzB,CAAjB,CAHS,CAKT;;AACAsG,UAAAA,QAAQ,CAACpG,KAAT,GAAiBmE,MAAM,CAACnE,KAAP,GAAe,CAACoE,MAAM,CAACpE,KAAP,GAAemE,MAAM,CAACnE,KAAvB,IAAgC6B,CAAhE;AACAuE,UAAAA,QAAQ,CAAC/F,UAAT,GAAsB8D,MAAM,CAAC9D,UAAP,GAAoB,CAAC+D,MAAM,CAAC/D,UAAP,GAAoB8D,MAAM,CAAC9D,UAA5B,IAA0CwB,CAApF;AACAuE,UAAAA,QAAQ,CAACjH,eAAT,GAA2BgF,MAAM,CAAChF,eAAlC;AACAiH,UAAAA,QAAQ,CAAC7F,gBAAT,GAA4B4D,MAAM,CAAC5D,gBAAP,GAA0B,CAAC6D,MAAM,CAAC7D,gBAAP,GAA0B4D,MAAM,CAAC5D,gBAAlC,IAAsDsB,CAA5G;AACAuE,UAAAA,QAAQ,CAAC1G,YAAT,GAAwB,IAAxB;AAEA,iBAAO0G,QAAP;AACH;AAED;AACJ;AACA;;;AACYhD,QAAAA,eAAe,CAACR,eAAD,EAA+ByD,KAA/B,EAAoD;AACvE,cAAMtD,UAAU,GAAGH,eAAe,CAACJ,MAAnC;;AAEA,cAAI,KAAKjB,MAAT,EAAiB;AACb;AACA,gBAAM+E,YAAY,GAAG,CAAED,KAAK,GAAGtD,UAAT,GAAuBA,UAAxB,IAAsCA,UAA3D;AACA,mBAAOH,eAAe,CAAC0D,YAAD,CAAf,CAA8BvG,QAArC;AACH,WAJD,MAIO;AACH;AACA,gBAAIsG,KAAK,GAAG,CAAZ,EAAe;AACX;AACA,kBAAMvE,EAAE,GAAGc,eAAe,CAAC,CAAD,CAAf,CAAmB7C,QAA9B;AACA,kBAAMgC,EAAE,GAAGa,eAAe,CAAC,CAAD,CAAf,CAAmB7C,QAA9B;AACA,qBAAO7B,IAAI,CAACuH,QAAL,CAAc,IAAIvH,IAAJ,EAAd,EAA0B4D,EAA1B,EAA8B5D,IAAI,CAACuH,QAAL,CAAc,IAAIvH,IAAJ,EAAd,EAA0B6D,EAA1B,EAA8BD,EAA9B,CAA9B,CAAP;AACH,aALD,MAKO,IAAIuE,KAAK,IAAItD,UAAb,EAAyB;AAC5B;AACA,kBAAMjB,EAAE,GAAGc,eAAe,CAACG,UAAU,GAAG,CAAd,CAAf,CAAgChD,QAA3C;AACA,kBAAMgC,GAAE,GAAGa,eAAe,CAACG,UAAU,GAAG,CAAd,CAAf,CAAgChD,QAA3C;AACA,qBAAO7B,IAAI,CAACqI,GAAL,CAAS,IAAIrI,IAAJ,EAAT,EAAqB6D,GAArB,EAAyB7D,IAAI,CAACuH,QAAL,CAAc,IAAIvH,IAAJ,EAAd,EAA0B6D,GAA1B,EAA8BD,EAA9B,CAAzB,CAAP;AACH,aALM,MAKA;AACH,qBAAOc,eAAe,CAACyD,KAAD,CAAf,CAAuBtG,QAA9B;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACI;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAvakB,O;;;;;iBAEI,E;;;;;;;iBAGOrB,SAAS,CAACkC,M;;;;;;;iBAMf,G;;;;;;;iBAKC,C;;;;;;;iBAKA,C;;;;;;;iBAKG,C;;;;;;;iBAMF,C;;;;;;;iBAMF,CAAC,C;;;;;;;iBAMI,E;;;;;;;iBAMJ,K", "sourcesContent": ["import { _decorator, Vec2, CCFloat, CCInteger, Enum } from 'cc';\r\nimport { eOrientationType } from 'db://assets/bundles/common/script/game/move/IMovable';\r\nimport { ExpressionValue } from '../eventgroup/ExpressionValue';\r\nconst { ccclass, property } = _decorator;\r\n\r\nexport enum ePathType {\r\n    Custom,  // 自定义\r\n    Circle,  // 圆形(圆形不需要让策划再去编辑点了，程序来生成)\r\n}\r\n\r\n/**\r\n * 路径点数据\r\n */\r\n@ccclass(\"PathPoint\")\r\nexport class PathPoint {\r\n    @property({ type: CCFloat, displayName: \"X坐标\" })\r\n    public x: number = 0;\r\n\r\n    @property({ type: CCFloat, displayName: \"Y坐标\" })\r\n    public y: number = 0;\r\n\r\n    @property({ type: CCFloat, displayName: \"平滑程度\", range: [0, 1], slide: true, tooltip: \"0=直线连接, 1=最大平滑曲线\" })\r\n    public smoothness: number = 0.5;\r\n\r\n    @property({visible:false, formerlySerializedAs: 'speed'})\r\n    public speedExpr: ExpressionValue = new ExpressionValue('500');\r\n    @property({ type: CCInteger, displayName: \"速度\", tooltip: \"飞机在此点的速度\" })\r\n    get speedStr(): string { return this.speedExpr.raw; }\r\n    set speedStr(value: string) { this.speedExpr.raw = value; }\r\n    \r\n    @property({ type: CCInteger, displayName: \"停留时间\", tooltip: \"飞机到达此点后停留时间（毫秒）\" })\r\n    public stayDuration: number = 0;\r\n\r\n    @property({ type: Enum(eOrientationType), displayName: \"朝向类型\", tooltip: \"飞机在此点的朝向\" })\r\n    public orientationType: eOrientationType = 0;\r\n\r\n    @property({ type: CCInteger, displayName: \"朝向参数\", tooltip: \"根据朝向类型不同而不同\",\r\n        visible() {\r\n            // @ts-ignore\r\n            return this.orientationType === eOrientationType.Fixed || this.orientationType === eOrientationType.Rotate;\r\n        }\r\n    })\r\n    public orientationParam: number = 0;\r\n\r\n    // 标记是否是插值的点（非原始点）\r\n    private _isSubdivided: boolean = false;\r\n    public get isSubdivided(): boolean {\r\n        return this._isSubdivided;\r\n    }\r\n    public set isSubdivided(value: boolean) {\r\n        this._isSubdivided = value;\r\n    }\r\n\r\n    constructor(x: number = 0, y: number = 0) {\r\n        this.x = x;\r\n        this.y = y;\r\n    }\r\n\r\n    public get position(): Vec2 {\r\n        return new Vec2(this.x, this.y);\r\n    }\r\n\r\n    public set position(value: Vec2) {\r\n        this.x = value.x;\r\n        this.y = value.y;\r\n    }\r\n\r\n    public get speed(): number {\r\n        return this.speedExpr.eval();\r\n    }\r\n\r\n    public set speed(value: number) {\r\n        this.speedExpr.raw = value.toString();\r\n    }\r\n\r\n    public fromJSON(data: any): void {\r\n        this.x = data.x || 0;\r\n        this.y = data.y || 0;\r\n        this.smoothness = data.smoothness || 1;\r\n        this.speed = data.speed || 500;\r\n        this.stayDuration = data.stayDuration || 0;\r\n        this.orientationType = data.orientationType || 0;\r\n        this.orientationParam = data.orientationParam || 0;\r\n    }\r\n}\r\n\r\n/**\r\n * 路径数据\r\n */\r\n@ccclass(\"PathData\")\r\nexport class PathData {\r\n    @property({ displayName: '路径名称', editorOnly: true })\r\n    public name: string = \"\";\r\n\r\n    @property({ type: Enum(ePathType), displayName: \"路径类型\"})\r\n    public pathType: ePathType = ePathType.Custom;\r\n\r\n    @property({ type: CCFloat, displayName: \"半径\", visible() {\r\n        // @ts-ignore\r\n        return this.pathType === ePathType.Circle;\r\n    } })\r\n    public radius: number = 100;\r\n    @property({ type: CCFloat, displayName: \"中心X\", visible() {\r\n        // @ts-ignore\r\n        return this.pathType === ePathType.Circle;\r\n    } })\r\n    public centerX: number = 0;\r\n    @property({ type: CCFloat, displayName: \"中心Y\", visible() {\r\n        // @ts-ignore\r\n        return this.pathType === ePathType.Circle;\r\n    } })\r\n    public centerY: number = 0;\r\n    @property({ type: CCInteger, displayName: \"起始角度\", visible() {\r\n        // @ts-ignore\r\n        return this.pathType === ePathType.Circle;\r\n    } })\r\n    public startAngle: number = 0;\r\n\r\n    @property({ type: CCInteger, displayName: '起始点(默认0)', visible() {\r\n        // @ts-ignore\r\n        return this.pathType === ePathType.Custom;\r\n    } })\r\n    public startIdx: number = 0;\r\n\r\n    @property({ type: CCInteger, displayName: '结束点(-1代表使用路径终点)', visible() {\r\n        // @ts-ignore\r\n        return this.pathType === ePathType.Custom;\r\n    } })\r\n    public endIdx: number = -1;\r\n\r\n    @property({ type: [PathPoint], displayName: '路径点', visible() {\r\n        // @ts-ignore\r\n        return this.pathType === ePathType.Custom;\r\n    } })\r\n    public points: PathPoint[] = [];\r\n\r\n    @property({ displayName: \"是否闭合路径\", tooltip: \"路径是否形成闭环\", visible() {\r\n        // @ts-ignore\r\n        return this.pathType === ePathType.Custom;\r\n    } })\r\n    public closed: boolean = false;\r\n\r\n    // 缓存的路径数据（不参与序列化）\r\n    private _cachedSubdividedPoints: PathPoint[] | null = null;\r\n\r\n    /**\r\n     * 自定义序列化 - 排除缓存数据\r\n     */\r\n    public toJSON(): any {\r\n        if (this.pathType === ePathType.Circle) {\r\n            return {\r\n                name: this.name,\r\n                pathType: this.pathType,\r\n                radius: this.radius,\r\n                centerX: this.centerX,\r\n                centerY: this.centerY,\r\n                startAngle: this.startAngle,\r\n            };\r\n        }\r\n\r\n        return {\r\n            name: this.name,\r\n            pathType: this.pathType,\r\n            startIdx: this.startIdx,\r\n            endIdx: this.endIdx,\r\n            points: this.points,\r\n            closed: this.closed\r\n        };\r\n    }\r\n\r\n    /**\r\n     * 自定义反序列化 - 清除缓存确保重新计算\r\n     */\r\n    public fromJSON(data: any): void {\r\n        if (!data) return;\r\n        this.name = data.name || \"\";\r\n        this.pathType = data.pathType || ePathType.Custom;\r\n        // 清除缓存，确保使用新数据重新计算\r\n        this._cachedSubdividedPoints = null;\r\n        if (this.pathType === ePathType.Circle) {\r\n            this.radius = data.radius || 100;\r\n            this.centerX = data.centerX || 0;\r\n            this.centerY = data.centerY || 0;\r\n            this.startAngle = data.startAngle || 0;\r\n            return;\r\n        }\r\n\r\n        this.startIdx = data.startIdx || 0;\r\n        this.endIdx = data.endIdx || -1;\r\n        this.points = data.points ? data.points.map((p: any) => {\r\n            const point = new PathPoint();\r\n            point.fromJSON(p);\r\n            return point;\r\n        }) : [];\r\n        this.closed = data.closed || false;\r\n    }\r\n\r\n    /**\r\n     * 静态工厂方法 - 从JSON创建PathData实例\r\n     */\r\n    public static fromJSON(data: any): PathData {\r\n        const pathData = new PathData();\r\n        pathData.fromJSON(data);\r\n        return pathData;\r\n    }\r\n    /**\r\n     * 获取Catmull-Rom曲线上的点\r\n     * @param t 参数值 [0, 1]\r\n     * @param p0 前一个控制点（用于计算切线）\r\n     * @param p1 起始点（曲线经过此点）\r\n     * @param p2 结束点（曲线经过此点）\r\n     * @param p3 后一个控制点（用于计算切线）\r\n     * @param smoothness 平滑程度 [0, 1]，0=直线，1=最平滑曲线\r\n     */\r\n    public static catmullRomPoint(t: number, p0: Vec2, p1: Vec2, p2: Vec2, p3: Vec2, smoothness: number = 0.5): Vec2 {\r\n        // 当smoothness为0时，直接返回线性插值（直线）\r\n        if (smoothness === 0) {\r\n            return Vec2.lerp(new Vec2(), p1, p2, t);\r\n        }\r\n\r\n        const t2 = t * t;\r\n        const t3 = t2 * t;\r\n\r\n        // 标准Catmull-Rom插值公式\r\n        const catmullRom = new Vec2();\r\n        catmullRom.x = 0.5 * (\r\n            (2 * p1.x) +\r\n            (-p0.x + p2.x) * t +\r\n            (2 * p0.x - 5 * p1.x + 4 * p2.x - p3.x) * t2 +\r\n            (-p0.x + 3 * p1.x - 3 * p2.x + p3.x) * t3\r\n        );\r\n\r\n        catmullRom.y = 0.5 * (\r\n            (2 * p1.y) +\r\n            (-p0.y + p2.y) * t +\r\n            (2 * p0.y - 5 * p1.y + 4 * p2.y - p3.y) * t2 +\r\n            (-p0.y + 3 * p1.y - 3 * p2.y + p3.y) * t3\r\n        );\r\n\r\n        // 当smoothness不为1时，在线性插值和Catmull-Rom之间混合\r\n        if (smoothness < 1) {\r\n            const linear = Vec2.lerp(new Vec2(), p1, p2, t);\r\n            return Vec2.lerp(new Vec2(), linear, catmullRom, smoothness);\r\n        }\r\n\r\n        return catmullRom;\r\n    }\r\n\r\n    public getStartPoint(): PathPoint|null {\r\n        if (this.startIdx < 0 || this.startIdx >= this.points.length) {\r\n            return null;\r\n        }\r\n        return this.points[this.startIdx];\r\n    }\r\n\r\n    /**\r\n     * 获取细分后的路径点（包含完整的PathPoint信息）\r\n     * 这是推荐的新方法，替代generateCurvePoints + 重新采样的方式\r\n     */\r\n    public getSubdividedPoints(regen: boolean = false): PathPoint[] {\r\n        if ((!this._cachedSubdividedPoints || this._cachedSubdividedPoints.length === 0) || regen) {\r\n            this._cachedSubdividedPoints = this.generateSubdividedPointsInternal();\r\n        }\r\n\r\n        return this._cachedSubdividedPoints!;\r\n    }\r\n\r\n    /**\r\n     * 内部方法：生成细分后的PathPoint数组\r\n     */\r\n    private generateSubdividedPointsInternal(): PathPoint[] {\r\n        const effectivePoints = this.getPoints();\r\n\r\n        if (effectivePoints.length < 2) {\r\n            return effectivePoints;\r\n        }\r\n\r\n        const subdivided: PathPoint[] = [];\r\n        const pointCount = effectivePoints.length;\r\n\r\n        // 添加第一个点\r\n        const firstPoint = effectivePoints[0];\r\n        subdivided.push(firstPoint);\r\n\r\n        // 计算需要处理的段数\r\n        const segmentCount = this.closed ? pointCount : pointCount - 1;\r\n        // 为每一段生成细分点\r\n        for (let i = 0; i < segmentCount; i++) {\r\n            const p0 = this.getControlPoint(effectivePoints, i - 1);\r\n            const p1 = effectivePoints[i].position;\r\n            const p2 = this.getControlPoint(effectivePoints, i + 1);\r\n            const p3 = this.getControlPoint(effectivePoints, i + 2);\r\n\r\n            const point = effectivePoints[i];\r\n            const pointNext = effectivePoints[(i + 1) % pointCount];\r\n            \r\n            const startSmoothness = point.smoothness;\r\n            const endSmoothness = pointNext.smoothness;\r\n\r\n            // 如果任一端点的smoothness为0，则整段使用直线\r\n            if (startSmoothness === 0 || endSmoothness === 0) {\r\n                // 直线连接：只需要添加终点\r\n                subdivided.push(pointNext);\r\n            } else {\r\n                // 使用自适应细分算法\r\n                const segmentPoints = this.adaptiveSubdivision(p0, p1, p2, p3, point, pointNext);\r\n                subdivided.push(...segmentPoints);\r\n            }\r\n        }\r\n\r\n        // 处理闭合路径的重复点\r\n        if (this.closed && subdivided.length > 1) {\r\n            const firstPoint = subdivided[0];\r\n            const lastPoint = subdivided[subdivided.length - 1];\r\n            const distance = Vec2.distance(firstPoint.position, lastPoint.position);\r\n\r\n            if (distance < 0.1) {\r\n                subdivided.pop();\r\n            }\r\n        }\r\n\r\n        return subdivided;\r\n    }\r\n\r\n    /**\r\n     * 获取有效的路径点范围（考虑startIdx和endIdx）\r\n     */\r\n    private getPoints(): PathPoint[] {\r\n        if (this.points.length === 0) return [];\r\n\r\n        const startIndex = Math.max(0, Math.min(this.startIdx, this.points.length - 1));\r\n        const endIndex = this.endIdx === -1 ? this.points.length - 1 : Math.max(startIndex, Math.min(this.endIdx, this.points.length - 1));\r\n\r\n        return this.points.slice(startIndex, endIndex + 1);\r\n    }\r\n\r\n    /**\r\n     * 自适应细分算法 - 基于曲率和误差的智能细分\r\n     * @param p0 前一个控制点\r\n     * @param p1 起始点\r\n     * @param p2 结束点\r\n     * @param p3 后一个控制点\r\n     * @param point1 起始PathPoint\r\n     * @param point2 结束PathPoint\r\n     * @returns 细分后的PathPoint数组\r\n     */\r\n    private adaptiveSubdivision(\r\n        p0: Vec2, p1: Vec2, p2: Vec2, p3: Vec2,\r\n        point1: PathPoint, point2: PathPoint,\r\n        maxDepth: number = 6\r\n    ): PathPoint[] {\r\n        const avgSmoothness = (point1.smoothness + point2.smoothness) / 2;\r\n\r\n        // 如果平滑度为0，直接返回终点\r\n        if (avgSmoothness === 0) {\r\n            return [point2];\r\n        }\r\n\r\n        // 递归细分（从深度0开始），但不包括t=1的终点\r\n        const subdivisionPoints = PathData.subdivideRecursive(p0, p1, p2, p3, point1, point2, 0, 0.999999, 0, maxDepth, avgSmoothness);\r\n\r\n        // 最后添加原始的终点，确保保留所有原始属性（包括stayDuration等）\r\n        subdivisionPoints.push(point2);\r\n\r\n        return subdivisionPoints;\r\n    }\r\n\r\n    /**\r\n     * 递归细分方法\r\n     */\r\n    static subdivideRecursive(\r\n        p0: Vec2, p1: Vec2, p2: Vec2, p3: Vec2,\r\n        point1: PathPoint, point2: PathPoint,\r\n        t1: number, t2: number, depth: number, maxDepth: number, smoothness: number\r\n    ): PathPoint[] {\r\n        // 达到最大深度，停止细分\r\n        if (depth >= maxDepth) {\r\n            return [PathData.createCurveInterpolatedPoint(p0, p1, p2, p3, point1, point2, t2, smoothness)];\r\n        }\r\n\r\n        const tMid = (t1 + t2) / 2;\r\n\r\n        // 计算三个点：起点、中点、终点\r\n        const startPos = PathData.catmullRomPoint(t1, p0, p1, p2, p3, smoothness);\r\n        const midPos = PathData.catmullRomPoint(tMid, p0, p1, p2, p3, smoothness);\r\n        const endPos = PathData.catmullRomPoint(t2, p0, p1, p2, p3, smoothness);\r\n\r\n        // 计算线性插值的中点\r\n        const linearMid = Vec2.lerp(new Vec2(), startPos, endPos, 0.5);\r\n\r\n        // 计算误差（曲线中点与线性中点的距离）\r\n        const error = Vec2.distance(midPos, linearMid);\r\n\r\n        // 计算曲率（使用三点法）\r\n        const curvature = PathData.calculateCurvature(startPos, midPos, endPos);\r\n\r\n        // 动态误差阈值：考虑距离和曲率\r\n        const distance = Vec2.distance(startPos, endPos);\r\n        const baseThreshold = Math.max(0.5, distance * 0.01); // 基础阈值\r\n        const curvatureThreshold = baseThreshold * (1 + curvature * 10); // 曲率调整\r\n        // console.log('error:', error, 'curvatureThreshold:', curvatureThreshold);\r\n        // 如果误差小于阈值，不需要进一步细分\r\n        if (error < curvatureThreshold) {\r\n            return [PathData.createCurveInterpolatedPoint(p0, p1, p2, p3, point1, point2, t2, smoothness)];\r\n        }\r\n\r\n        // 需要细分：递归处理两个子段\r\n        const leftPoints = PathData.subdivideRecursive(p0, p1, p2, p3, point1, point2, t1, tMid, depth + 1, maxDepth, smoothness);\r\n        const rightPoints = PathData.subdivideRecursive(p0, p1, p2, p3, point1, point2, tMid, t2, depth + 1, maxDepth, smoothness);\r\n\r\n        return [...leftPoints, ...rightPoints];\r\n    }\r\n\r\n    /**\r\n     * 计算三点的曲率\r\n     */\r\n    static calculateCurvature(p1: Vec2, p2: Vec2, p3: Vec2): number {\r\n        const v1 = Vec2.subtract(new Vec2(), p2, p1);\r\n        const v2 = Vec2.subtract(new Vec2(), p3, p2);\r\n\r\n        // 避免除零\r\n        const len1 = v1.length();\r\n        const len2 = v2.length();\r\n        if (len1 < 0.001 || len2 < 0.001) return 0;\r\n\r\n        v1.normalize();\r\n        v2.normalize();\r\n\r\n        // 计算角度变化\r\n        const dot = Vec2.dot(v1, v2);\r\n        const clampedDot = Math.max(-1, Math.min(1, dot));\r\n        const angle = Math.acos(clampedDot);\r\n\r\n        // 归一化曲率值\r\n        return angle / Math.PI;\r\n    }\r\n\r\n    /**\r\n     * 创建曲线插值的PathPoint（使用Catmull-Rom曲线）\r\n     */\r\n    static createCurveInterpolatedPoint(\r\n        p0: Vec2, p1: Vec2, p2: Vec2, p3: Vec2,\r\n        point1: PathPoint, point2: PathPoint,\r\n        t: number, smoothness: number\r\n    ): PathPoint {\r\n        // 使用Catmull-Rom曲线计算位置\r\n        const pos = PathData.catmullRomPoint(t, p0, p1, p2, p3, smoothness);\r\n        const newPoint = new PathPoint(pos.x, pos.y);\r\n\r\n        // 插值其他属性\r\n        newPoint.speed = point1.speed + (point2.speed - point1.speed) * t;\r\n        newPoint.smoothness = point1.smoothness + (point2.smoothness - point1.smoothness) * t;\r\n        newPoint.orientationType = point1.orientationType;\r\n        newPoint.orientationParam = point1.orientationParam + (point2.orientationParam - point1.orientationParam) * t;\r\n        newPoint.isSubdivided = true;\r\n\r\n        return newPoint;\r\n    }\r\n\r\n    /**\r\n     * 获取有效路径点的控制点（处理边界情况）\r\n     */\r\n    private getControlPoint(effectivePoints: PathPoint[], index: number): Vec2 {\r\n        const pointCount = effectivePoints.length;\r\n\r\n        if (this.closed) {\r\n            // 闭合路径，使用循环索引\r\n            const wrappedIndex = ((index % pointCount) + pointCount) % pointCount;\r\n            return effectivePoints[wrappedIndex].position;\r\n        } else {\r\n            // 开放路径，边界处理\r\n            if (index < 0) {\r\n                // 延伸第一个点\r\n                const p0 = effectivePoints[0].position;\r\n                const p1 = effectivePoints[1].position;\r\n                return Vec2.subtract(new Vec2(), p0, Vec2.subtract(new Vec2(), p1, p0));\r\n            } else if (index >= pointCount) {\r\n                // 延伸最后一个点\r\n                const p0 = effectivePoints[pointCount - 2].position;\r\n                const p1 = effectivePoints[pointCount - 1].position;\r\n                return Vec2.add(new Vec2(), p1, Vec2.subtract(new Vec2(), p1, p0));\r\n            } else {\r\n                return effectivePoints[index].position;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 获取控制点（处理边界情况）- 保留用于兼容性\r\n     */\r\n    // private getControlPoint(index: number): Vec2 {\r\n    //     const pointCount = this.points.length;\r\n\r\n    //     if (this.closed) {\r\n    //         // 闭合路径，使用循环索引\r\n    //         const wrappedIndex = ((index % pointCount) + pointCount) % pointCount;\r\n    //         return this.points[wrappedIndex].position;\r\n    //     } else {\r\n    //         // 开放路径，边界处理\r\n    //         if (index < 0) {\r\n    //             // 延伸第一个点\r\n    //             const p0 = this.points[0].position;\r\n    //             const p1 = this.points[1].position;\r\n    //             return Vec2.subtract(new Vec2(), p0, Vec2.subtract(new Vec2(), p1, p0));\r\n    //         } else if (index >= pointCount) {\r\n    //             // 延伸最后一个点\r\n    //             const p0 = this.points[pointCount - 2].position;\r\n    //             const p1 = this.points[pointCount - 1].position;\r\n    //             return Vec2.add(new Vec2(), p1, Vec2.subtract(new Vec2(), p1, p0));\r\n    //         } else {\r\n    //             return this.points[index].position;\r\n    //         }\r\n    //     }\r\n    // }\r\n}"]}
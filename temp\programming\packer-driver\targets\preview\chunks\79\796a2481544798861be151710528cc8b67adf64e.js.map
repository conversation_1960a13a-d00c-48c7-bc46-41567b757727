{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/pk/FriendFusionUI.ts"], "names": ["_decorator", "BundleName", "ButtonPlus", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "DataEvent", "EventMgr", "List", "FriendFusionCellUI", "ccclass", "property", "FriendFusionUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getBundleName", "HomePK", "getUIOption", "isClickBgCloseUI", "onLoad", "btnClose", "addClick", "onCloseClick", "btnBattle", "onBattleClick", "list", "numItems", "on", "FusionCellClick", "onFusionCellClick", "closeUI", "index", "content", "children", "for<PERSON>ach", "element", "getComponent", "updateSelect", "onShow", "onHide", "onClose", "onDestroy", "off", "onList<PERSON>ender", "listItem", "row", "cell", "setData"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AAEjBC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,Q,iBAAAA,Q;;AACFC,MAAAA,I;;AACEC,MAAAA,kB,iBAAAA,kB;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;gCAGjBY,c,WADZF,OAAO,CAAC,gBAAD,C,UAEHC,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,uB,2BANb,MACaC,cADb;AAAA;AAAA,4BAC2C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAQnB,eAANC,MAAM,GAAW;AAAE,iBAAO,0BAAP;AAAoC;;AAC/C,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAwB;;AACjC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,MAAlB;AAA2B;;AAC1C,eAAXC,WAAW,GAAU;AAAE,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AAAmC;;AAC9DC,QAAAA,MAAM,GAAS;AACrB,eAAKC,QAAL,CAAeC,QAAf,CAAwB,KAAKC,YAA7B,EAA2C,IAA3C;AACA,eAAKC,SAAL,CAAgBF,QAAhB,CAAyB,KAAKG,aAA9B,EAA6C,IAA7C;AACA,eAAKC,IAAL,CAAWC,QAAX,GAAsB,EAAtB;AACA;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,sCAAUC,eAAtB,EAAuC,KAAKC,iBAA5C,EAA+D,IAA/D;AACH;;AAEKP,QAAAA,YAAY,GAAG;AAAA;AACjB;AAAA;AAAA,gCAAMQ,OAAN,CAAcnB,cAAd;AADiB;AAEpB;;AACKa,QAAAA,aAAa,GAAG;AAAA;AAErB;;AACOK,QAAAA,iBAAiB,CAACE,KAAD,EAAgB;AACrC,eAAKN,IAAL,CAAWO,OAAX,CAAmBC,QAAnB,CAA4BC,OAA5B,CAAoCC,OAAO,IAAI;AAC3CA,YAAAA,OAAO,CAACC,YAAR;AAAA;AAAA,0DAA0CC,YAA1C;AACH,WAFD;AAGH;;AACKC,QAAAA,MAAM,GAAkB;AAAA;AAE7B;;AACKC,QAAAA,MAAM,GAAkB;AAAA;AAC7B;;AACKC,QAAAA,OAAO,GAAkB;AAAA;AAC9B;;AACSC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,GAAT,CAAa;AAAA;AAAA,sCAAUd,eAAvB,EAAwC,KAAKC,iBAA7C,EAAgE,IAAhE;AACH;;AAEDc,QAAAA,YAAY,CAACC,QAAD,EAAiBC,GAAjB,EAA8B;AACtC,cAAMC,IAAI,GAAGF,QAAQ,CAACR,YAAT;AAAA;AAAA,uDAAb;;AACA,cAAIU,IAAI,KAAK,IAAb,EAAmB;AACfA,YAAAA,IAAI,CAACC,OAAL,CAAaF,GAAG,GAAG,CAAnB,EAAsBA,GAAG,GAAG,CAA5B;AACH;AACJ;;AA9CsC,O;;;;;iBAET,I;;;;;;;iBAEC,I;;;;;;;iBAEX,I", "sourcesContent": ["import { _decorator, Node } from 'cc';\r\nimport { BundleName } from 'db://assets/bundles/common/script/const/BundleConst';\r\nimport { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';\r\nimport { BaseUI, UILayer, UIMgr, UIOpt, } from 'db://assets/scripts/core/base/UIMgr';\r\nimport { DataMgr } from '../../data/DataManager';\r\nimport { DataEvent } from '../../event/DataEvent';\r\nimport { EventMgr } from '../../event/EventManager';\r\nimport List from '../common/components/list/List';\r\nimport { FriendFusionCellUI } from './FriendFusionCellUI';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('FriendFusionUI')\r\nexport class FriendFusionUI extends BaseUI {\r\n    @property(ButtonPlus)\r\n    btnClose: ButtonPlus | null = null;\r\n    @property(ButtonPlus)\r\n    btnBattle: ButtonPlus | null = null;\r\n    @property(List)\r\n    list: List | null = null;\r\n\r\n    public static getUrl(): string { return \"prefab/ui/FriendFusionUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Default }\r\n    public static getBundleName(): string { return BundleName.HomePK; }\r\n    public static getUIOption(): UIOpt { return { isClickBgCloseUI: true } }\r\n    protected onLoad(): void {\r\n        this.btnClose!.addClick(this.onCloseClick, this);\r\n        this.btnBattle!.addClick(this.onBattleClick, this);\r\n        this.list!.numItems = 10;\r\n        EventMgr.on(DataEvent.FusionCellClick, this.onFusionCellClick, this);\r\n    }\r\n\r\n    async onCloseClick() {\r\n        UIMgr.closeUI(FriendFusionUI);\r\n    }\r\n    async onBattleClick() {\r\n\r\n    }\r\n    private onFusionCellClick(index: number) {\r\n        this.list!.content.children.forEach(element => {\r\n            element.getComponent(FriendFusionCellUI)!.updateSelect();\r\n        });\r\n    }\r\n    async onShow(): Promise<void> {\r\n\r\n    }\r\n    async onHide(): Promise<void> {\r\n    }\r\n    async onClose(): Promise<void> {\r\n    }\r\n    protected onDestroy(): void {\r\n        EventMgr.off(DataEvent.FusionCellClick, this.onFusionCellClick, this);\r\n    }\r\n\r\n    onListRender(listItem: Node, row: number) {\r\n        const cell = listItem.getComponent(FriendFusionCellUI);\r\n        if (cell !== null) {\r\n            cell.setData(row + 1, row + 1);\r\n        }\r\n    }\r\n}\r\n\r\n\r\n"]}
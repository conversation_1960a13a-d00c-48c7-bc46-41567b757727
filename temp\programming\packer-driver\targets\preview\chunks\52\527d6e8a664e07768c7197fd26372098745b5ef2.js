System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, SingletonBase, MyApp, instantiate, Prefab, NodePool, RogueItem, BundleName, RogueManager, _crd;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfSingletonBase(extras) {
    _reporterNs.report("SingletonBase", "db://assets/scripts/core/base/SingletonBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResWorldGroup(extras) {
    _reporterNs.report("ResWorldGroup", "../../autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../app/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRogueItem(extras) {
    _reporterNs.report("RogueItem", "../../ui/gameui/game/RogueItem", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "../../const/BundleConst", _context.meta, extras);
  }

  _export("RogueManager", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      instantiate = _cc.instantiate;
      Prefab = _cc.Prefab;
      NodePool = _cc.NodePool;
    }, function (_unresolved_2) {
      SingletonBase = _unresolved_2.SingletonBase;
    }, function (_unresolved_3) {
      MyApp = _unresolved_3.MyApp;
    }, function (_unresolved_4) {
      RogueItem = _unresolved_4.RogueItem;
    }, function (_unresolved_5) {
      BundleName = _unresolved_5.BundleName;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "2bf32sCEIlL0JxsMNiflSa1", "RogueManager", undefined);
      /**
       * 肉鸽数据管理
       */


      __checkObsolete__(['instantiate', 'Prefab', 'Node', 'NodePool']);

      _export("RogueManager", RogueManager = class RogueManager extends (_crd && SingletonBase === void 0 ? (_reportPossibleCrUseOfSingletonBase({
        error: Error()
      }), SingletonBase) : SingletonBase) {
        constructor() {
          super();
          this._rougeGroupConfig = {};
          this._selectedHistory = new Map();
          // 词条ID -> 选取次数
          this._typeSelectedCount = new Map();
          // 词条类型 -> 选取次数
          this._rogueItemPool = new NodePool();
          this.initRougeGroupConfig();
        }

        initRougeGroupConfig() {
          var list = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbResWordGroup.getDataList();

          for (var v of list) {
            if (!this._rougeGroupConfig[v.GroupID]) {
              this._rougeGroupConfig[v.GroupID] = [];
            }

            this._rougeGroupConfig[v.GroupID].push(v);
          }
        }

        reset() {
          this._selectedHistory.clear();

          this._typeSelectedCount.clear();
        }

        clear() {
          this.reset();

          this._rogueItemPool.clear();
        }

        getSeLectedHistory() {
          return this._selectedHistory;
        }

        setRogueItem(parent, wordId, lv) {
          var _this = this;

          return _asyncToGenerator(function* () {
            if (lv === void 0) {
              lv = 0;
            }

            var rogue; // Try to get from pool first

            if (_this._rogueItemPool.size() > 0) {
              var node = _this._rogueItemPool.get();

              rogue = node.getComponent(_crd && RogueItem === void 0 ? (_reportPossibleCrUseOfRogueItem({
                error: Error()
              }), RogueItem) : RogueItem);
            } else {
              // Create new if pool is empty
              rogue = yield _this.getRogueItem();
            }

            rogue.node.parent = parent;
            rogue.initRogueById(wordId, lv);
          })();
        }

        getRogueItem() {
          return _asyncToGenerator(function* () {
            var rogueItem = yield (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.loadAsync((_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
              error: Error()
            }), BundleName) : BundleName).GameFight, "prefab/RogueItem", Prefab);
            var node = instantiate(rogueItem);
            return node.getComponent(_crd && RogueItem === void 0 ? (_reportPossibleCrUseOfRogueItem({
              error: Error()
            }), RogueItem) : RogueItem);
          })();
        }

        recycleRogueItems(parentNode) {
          if (!parentNode) return;

          for (var i = parentNode.children.length - 1; i >= 0; i--) {
            var childNode = parentNode.children[i];
            var rogueItem = childNode.getComponent(_crd && RogueItem === void 0 ? (_reportPossibleCrUseOfRogueItem({
              error: Error()
            }), RogueItem) : RogueItem);

            if (rogueItem) {
              rogueItem.reset == null || rogueItem.reset();

              this._rogueItemPool.put(childNode);
            }
          }
        }

        clearRogueItemPool() {
          this._rogueItemPool.clear();
        }
        /**
         * 随机获取词条
         * @param groupId 词条组ID
         * @param count 随机数量，默认3个
         * @returns 随机到的词条列表
         */


        randomWords(groupId, count) {
          if (count === void 0) {
            count = 3;
          }

          var list = this._rougeGroupConfig[groupId];

          if (!list || list.length === 0) {
            console.warn("\u8BCD\u6761\u7EC4 " + groupId + " \u4E0D\u5B58\u5728\u6216\u4E3A\u7A7A");
            return [];
          } // 按优先级分组


          var priorityGroups = this.groupByPriority(list);
          var result = [];
          var remainingCount = count; // 按优先级从低到高依次随机

          var priorities = Object.keys(priorityGroups).map(Number).sort((a, b) => a - b);

          for (var _priority of priorities) {
            if (remainingCount <= 0) break;
            var availableWords = this.getAvailableWords(priorityGroups[_priority]);
            if (availableWords.length === 0) continue; // 逐个随机，而不是一次性返回所有

            var wordsForThisPriority = this.randomFromPriorityGroupOneByOne(availableWords, Math.min(remainingCount, availableWords.length));
            result.push(...wordsForThisPriority);
            remainingCount -= wordsForThisPriority.length;
          }

          return result;
        }
        /**
         * 按优先级分组
         */


        groupByPriority(words) {
          var groups = {};

          for (var word of words) {
            if (!groups[word.priority]) {
              groups[word.priority] = [];
            }

            groups[word.priority].push(word);
          }

          return groups;
        }
        /**
         * 获取可用的词条（考虑次数限制）
         */


        getAvailableWords(words) {
          return words.filter(word => {
            // 检查次数限制
            if (word.times === -1) return true; // -1表示无限次

            if (word.times === 0) return false; // 0表示不可选

            var selectedCount = this._selectedHistory.get(word.ID) || 0;
            return selectedCount < word.times;
          });
        }
        /**
         * 从指定优先级组中逐个随机选择词条（每次随机后重新计算权重）
         */


        randomFromPriorityGroupOneByOne(words, count) {
          var _this2 = this;

          if (words.length <= count) {
            // 如果数量不够，返回所有可用的，但需要打乱顺序
            return this.shuffleArray([...words]);
          }

          var selected = [];
          var availableWords = [...words];

          var _loop = function _loop() {
            // 每次随机都重新计算权重（考虑类型修正）
            var weightedWords = availableWords.map(word => {
              var weight = word.weight; // 如果之前选取过该类型，加上类型权重修正

              if (word.type && word.typWeightFix) {
                var typeSelectedCount = _this2._typeSelectedCount.get(word.type) || 0;

                if (typeSelectedCount > 0) {
                  weight += word.typWeightFix;
                }
              }

              return {
                word,
                weight
              };
            }); // 权重随机选择一个词条

            var selectedWord = _this2.selectByWeight(weightedWords);

            if (selectedWord) {
              selected.push(selectedWord); // 从可用列表中移除已选中的词条

              var index = availableWords.findIndex(w => w.ID === selectedWord.ID);

              if (index !== -1) {
                availableWords.splice(index, 1);
              }
            }
          };

          for (var i = 0; i < count && availableWords.length > 0; i++) {
            _loop();
          }

          return selected;
        }
        /**
         * 根据权重随机选择一个词条
         */


        selectByWeight(weightedWords) {
          if (weightedWords.length === 0) return null;
          var totalWeight = weightedWords.reduce((sum, item) => sum + item.weight, 0);
          var random = Math.random() * totalWeight;

          for (var item of weightedWords) {
            random -= item.weight;

            if (random <= 0) {
              return item.word;
            }
          } // 如果由于浮点数精度问题没有选中，返回最后一个


          return weightedWords[weightedWords.length - 1].word;
        }
        /**
         * 打乱数组顺序
         */


        shuffleArray(array) {
          var shuffled = [...array];

          for (var i = shuffled.length - 1; i > 0; i--) {
            var j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
          }

          return shuffled;
        }

        selectRogue(selectedWords) {
          // 更新选取记录
          this.updateSelectionHistory(selectedWords);
        }
        /**
         * 更新选取记录
         */


        updateSelectionHistory(selectedWords) {
          for (var word of selectedWords) {
            // 更新词条选取次数
            var currentCount = this._selectedHistory.get(word.ID) || 0;

            this._selectedHistory.set(word.ID, currentCount + 1); // 更新类型选取次数


            if (word.type) {
              var typeCount = this._typeSelectedCount.get(word.type) || 0;

              this._typeSelectedCount.set(word.type, typeCount + 1);
            }
          }
        }
        /**
         * 获取词条选取次数
         */


        getWordSelectionCount(wordId) {
          return this._selectedHistory.get(wordId) || 0;
        }
        /**
         * 获取类型选取次数
         */


        getTypeSelectionCount(wordType) {
          return this._typeSelectedCount.get(wordType) || 0;
        }

        coverToBuffIds(wordGroupId) {
          var buffIDs = [];
          wordGroupId.forEach(key => {
            var config = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).lubanTables.TbResWordGroup.get(key);
            var wordConfig = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).lubanTables.TbResWord.get(config.wordId);
            buffIDs.push(wordConfig.buffID);
          });
          return buffIDs;
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=527d6e8a664e07768c7197fd26372098745b5ef2.js.map
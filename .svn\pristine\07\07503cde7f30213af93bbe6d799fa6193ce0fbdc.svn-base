import { IEventGroupContext } from "./IEventGroupContext";
import { ExpressionValue } from "./ExpressionValue";

export enum eConditionOp {
    And, Or
}

export enum eCompareOp {
    Equal, // 等于
    NotEqual, // 不等于
    Greater, // 大于
    Less, // 小于
    GreaterEqual, // 大于等于
    LessEqual, // 小于等于
}

// 数据基类(不含具体的type类型)
export interface IEventConditionData {
    op: eConditionOp;
    compareOp: eCompareOp;
    targetValue: ExpressionValue;
}

// Base interfaces
export interface IEventCondition<CtxType extends IEventGroupContext> {
    readonly data: IEventConditionData;

    onLoad(context: CtxType): void;
    evaluate(context: CtxType): boolean;
}

export class EventConditionBase<CtxType extends IEventGroupContext> implements IEventCondition<CtxType> {
    readonly data: IEventConditionData;

    protected _targetValue: number = 0;

    constructor(data: IEventConditionData) {
        this.data = data;
    }

    onLoad(context: CtxType): void {
        this._targetValue = this.data.targetValue.eval();
    }

    evaluate(context: CtxType): boolean {
        // Default implementation (can be overridden)
        return true;
    }
}

// 提供一个静态函数帮助比较value
export class Comparer {
    static compare(a: number, b: number, op: eCompareOp): boolean {
        switch (op) {
            case eCompareOp.Equal:
                return a === b;
            case eCompareOp.NotEqual:
                return a !== b;
            case eCompareOp.Greater:
                return a > b;
            case eCompareOp.GreaterEqual:
                return a >= b;
            case eCompareOp.Less:
                return a < b;
            case eCompareOp.LessEqual:
                return a <= b;
            default:
                throw new Error(`Unknown compare operator: ${op}`);
        }
    }
}

// Condition chain with operators
export class ConditionChain<CtxType extends IEventGroupContext> {
    conditions: Array<IEventCondition<CtxType>> = [];

    evaluate(context: CtxType): boolean {
        if (this.conditions.length === 0) return true;
        let result = this.conditions[0].evaluate(context);
        
        for (let i = 1; i < this.conditions.length; i++) {
            const condition = this.conditions[i];
            const conditionResult = condition.evaluate(context);
            
            if (condition.data.op === eConditionOp.And) {
                result = result && conditionResult;
            } else if (condition.data.op === eConditionOp.Or) {
                result = result || conditionResult;
            }
        }
        
        return result;
    }
}

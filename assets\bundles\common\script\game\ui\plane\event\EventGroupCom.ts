import { _decorator, Enum } from "cc";
const { ccclass, property } = _decorator;

import Entity from "db://assets/bundles/common/script/game/ui/base/Entity";
import BaseComp from "db://assets/bundles/common/script/game/ui/base/BaseComp";
import EnemyPlaneBase from "db://assets/bundles/common/script/game/ui/plane/enemy/EnemyPlaneBase";
import { IEventGroupContext } from "db://assets/bundles/common/script/game/eventgroup/IEventGroupContext";
import { IEventGroupData, EventGroupBase } from "db://assets/bundles/common/script/game/eventgroup/IEventGroup";
import { IEventCondition, ConditionChain } from "db://assets/bundles/common/script/game/eventgroup/IEventCondition";
import { IEventAction } from "db://assets/bundles/common/script/game/eventgroup/IEventAction";
import PlaneBase from "db://assets/bundles/common/script/game/ui/plane/PlaneBase";

import { EnemyConditionData, eEnemyCondition } from './conditions/EnemyEventConditionData';
import { EnemyActionData, eEnemyAction } from './actions/EnemyEventActionData';

import * as enemy_cond from './conditions/EnemyEventCondition';
import * as enemy_act from './actions/EnemyEventAction';
import { GameIns } from "db://assets/bundles/common/script/game/GameIns";

/**
 * 这个文件主要用于实现敌机的一些简单的 条件->效果 机制，主要适用于敌机表现相关的，如：移动速度，摆动等等。
 * 核心接口：
 *   编辑器配置：
 *   - IEventGroupData: 事件组数据
 *   - IEventConditionData: 事件条件数据
 *   - IEventActionData: 事件效果数据
 *   运行时：
 *   - IEventGroupContext: 事件执行上下文
 *   - IEventCondition: 事件条件
 *   - IEventAction: 事件效果
 *   - EventInstance: 事件运行时的实例
 * 原则上，扩展可以通过扩展条件或者事件的枚举，并提供具体的实现即可。
 */

export class EnemyEventGroupContext implements IEventGroupContext {
    playerPlane: PlaneBase|null = null;
    plane: EnemyPlaneBase|null = null;

    reset() {
        this.playerPlane = null;
        this.plane = null;
    }
}

@ccclass('EnemyEventGroupData')
export class EnemyEventGroupData implements IEventGroupData {
    @property({ displayName: '事件组名称' })
    public name: string = "";

    @property({ displayName: '触发次数', tooltip: '触发次数(默认1,只能触发一次; -1表示循环触发)' })
    public triggerCount: number = 1;

    @property({ type: [EnemyConditionData], displayName: '条件列表' })
    public conditions: EnemyConditionData[] = [];

    @property({ type: [EnemyActionData], displayName: '行为列表' })
    public actions: EnemyActionData[] = [];
}

class EnemyEventInstance extends EventGroupBase<EnemyEventGroupContext, EnemyEventGroupData> {
    protected buildConditions(): ConditionChain<EnemyEventGroupContext> {
        const chain = new ConditionChain<EnemyEventGroupContext>();
        this.data.conditions.forEach((condData, index) => {
            const condition = EnemyConditionFactory.create(condData);
            if (condition) {
                condition.onLoad(this.context);
                chain.conditions.push(condition);
            }
        });
        return chain;
    }

    protected buildActions(): IEventAction<EnemyEventGroupContext>[] {
        return this.data.actions.map(actionData => {
            let action = EnemyActionFactory.create(actionData);
            return action;
        });
    }
}

class EnemyConditionFactory {
    static create(data: EnemyConditionData): IEventCondition<EnemyEventGroupContext> {
        switch (data.type) {
            case eEnemyCondition.ElapsedTime:
                return new enemy_cond.EnemyCondition_ElapsedTime(data);
            case eEnemyCondition.Pos_X:
                return new enemy_cond.EnemyCondition_Pos_X(data);
            case eEnemyCondition.Pos_Y:
                return new enemy_cond.EnemyCondition_Pos_Y(data);
            case eEnemyCondition.CurHP:
                return new enemy_cond.EnemyCondition_CurHP(data);
            case eEnemyCondition.CurHPPercent:
                return new enemy_cond.EnemyCondition_CurHPPercent(data);
            case eEnemyCondition.Speed:
                return new enemy_cond.EnemyCondition_Speed(data);
            case eEnemyCondition.SpeedAngle:
                return new enemy_cond.EnemyCondition_SpeedAngle(data);
            case eEnemyCondition.Acceleration:
                return new enemy_cond.EnemyCondition_Acceleration(data);
            case eEnemyCondition.AccelerationAngle:
                return new enemy_cond.EnemyCondition_AccelerationAngle(data);
            case eEnemyCondition.DistanceToPlayer:
                return new enemy_cond.EnemyCondition_DistanceToPlayer(data);
            case eEnemyCondition.AngleToPlayer:
                return new enemy_cond.EnemyCondition_AngleToPlayer(data);
            case eEnemyCondition.Player_Pos_X:
                return new enemy_cond.EnemyCondition_Player_Pos_X(data);
            case eEnemyCondition.Player_Pos_Y:
                return new enemy_cond.EnemyCondition_Player_Pos_Y(data);
            case eEnemyCondition.Player_CurHPPercent:
                return new enemy_cond.EnemyCondition_Player_CurHPPercent(data);
            case eEnemyCondition.Level_ElapsedTime:
                return new enemy_cond.EnemyCondition_Level_ElapsedTime(data);
            case eEnemyCondition.Level_InfLevel:
                return new enemy_cond.EnemyCondition_Level_InfLevel(data);
            case eEnemyCondition.Level_ChallengeLevelSection:
                return new enemy_cond.EnemyCondition_Level_ChallengeLevelSection(data);
            case eEnemyCondition.SelectLevel:
                return new enemy_cond.EnemyCondition_SelectLevel(data);
            case eEnemyCondition.ImmuneBulletDamage:
                return new enemy_cond.EnemyCondition_ImmuneBulletDamage(data);
            case eEnemyCondition.ImmuneCollideDamage:
                return new enemy_cond.EnemyCondition_ImmuneCollideDamage(data);
            case eEnemyCondition.IgnoreBullet:
                return new enemy_cond.EnemyCondition_IgnoreBullet(data);
            case eEnemyCondition.IgnoreCollide:
                return new enemy_cond.EnemyCondition_IgnoreCollide(data);
            case eEnemyCondition.ImmuneNuke:
                return new enemy_cond.EnemyCondition_ImmuneNuke(data);
            case eEnemyCondition.ImmuneActiveSkill:
                return new enemy_cond.EnemyCondition_ImmuneActiveSkill(data);
            case eEnemyCondition.Invincible:
                return new enemy_cond.EnemyCondition_Invincible(data);
            
            default: 
                throw new Error(`Unknown condition type: ${data.type}`);
        }
    }
}

class EnemyActionFactory {
    static create(data: EnemyActionData): IEventAction<EnemyEventGroupContext> {
        switch (data.type) {
            case eEnemyAction.Speed:
                return new enemy_act.EnemyAction_Speed(data);
            case eEnemyAction.SpeedAngle:
                return new enemy_act.EnemyAction_SpeedAngle(data);
            case eEnemyAction.Acceleration:
                return new enemy_act.EnemyAction_Acceleration(data);
            case eEnemyAction.AccelerationAngle:
                return new enemy_act.EnemyAction_AccelerationAngle(data);
            case eEnemyAction.Color_R:
                return new enemy_act.EnemyAction_Color_R(data);
            case eEnemyAction.Color_G:
                return new enemy_act.EnemyAction_Color_G(data);
            case eEnemyAction.Color_B:
                return new enemy_act.EnemyAction_Color_B(data);
            case eEnemyAction.Scale_X:
                return new enemy_act.EnemyAction_Scale_X(data);
            case eEnemyAction.Scale_Y:
                return new enemy_act.EnemyAction_Scale_Y(data);
            case eEnemyAction.TiltSpeed:
                return new enemy_act.EnemyAction_TiltSpeed(data);
            case eEnemyAction.TiltOffset:
                return new enemy_act.EnemyAction_TiltOffset(data);
            case eEnemyAction.RotateSpeed:
                return new enemy_act.EnemyAction_RotateSpeed(data);
            case eEnemyAction.SelectLevel:
                return new enemy_act.EnemyAction_SelectLevel(data);
            case eEnemyAction.ImmuneBulletDamage:
                return new enemy_act.EnemyAction_ImmuneBulletDamage(data);
            case eEnemyAction.ImmuneCollideDamage:
                return new enemy_act.EnemyAction_ImmuneCollideDamage(data);
            case eEnemyAction.IgnoreBullet:
                return new enemy_act.EnemyAction_IgnoreBullet(data);
            case eEnemyAction.IgnoreCollide:
                return new enemy_act.EnemyAction_IgnoreCollide(data);
            case eEnemyAction.ImmuneNuke:
                return new enemy_act.EnemyAction_ImmuneNuke(data);
            case eEnemyAction.ImmuneActiveSkill:
                return new enemy_act.EnemyAction_ImmuneActiveSkill(data);
            case eEnemyAction.Invincible:
                return new enemy_act.EnemyAction_Invincible(data);
            default:
                throw new Error(`Unknown action type: ${data.type}`);
        }
    }
}

// 挂在Entity身上，用来执行事件组的组件
export class EventGroupComp extends BaseComp {
    plane: EnemyPlaneBase|null = null;

    private _context: EnemyEventGroupContext = new EnemyEventGroupContext();
    private _instances: EnemyEventInstance[] = [];
    
    init(entity: Entity) {
        super.init(entity);
        this.plane = entity as EnemyPlaneBase;
    }

    onInit() {
        this._context.playerPlane = GameIns.mainPlaneManager?.mainPlane;
        this._context.plane = this.plane;
        if (this.plane && this.plane.enemyPrefabData) {
            this.plane.enemyPrefabData.eventGroups.forEach(groupData => {
                let instance = new EnemyEventInstance(this._context, groupData);
                instance.tryStart()
                this._instances.push(instance);
            });
        }
    }

    onReset() {
        this._instances.forEach(instance => instance.tryStop());
        this._instances = [];
        this._context.reset();
    }

    updateGameLogic(dt: number) {
        if (this._instances.length > 0) {
            this._instances.forEach(instance => {
                instance.tick(dt);
            });
        }
    }
}
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.methods = void 0;
exports.load = load;
exports.unload = unload;
const path_1 = require("path");
// @ts-ignore
module.paths.push((0, path_1.join)(Editor.App.path, 'node_modules'));
// 当前版本需要在 module.paths 修改后才能正常使用 cc 模块
// 并且如果希望正常显示 cc 的定义，需要手动将 engine 文件夹里的 cc.d.ts 添加到插件的 tsconfig 里
// 当前版本的 cc 定义文件可以在当前项目的 temp/declarations/cc.d.ts 找到
const cc_1 = require("cc");
const { _utils } = cc_1.Prefab;
function load() { }
;
function unload() { }
;
exports.methods = {
    saveLevel() {
        console.log('saveLevel in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if (levelEditorUI) {
            levelEditorUI.save = true;
        }
    },
    playLevel() {
        console.log('playLevel in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if (levelEditorUI) {
            var play = levelEditorUI.play;
            levelEditorUI.play = !play;
        }
    },
    levelStart() {
        console.log('levelStart in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if (levelEditorUI) {
            levelEditorUI.progress = 0;
        }
    },
    levelEnd() {
        console.log('levelEnd in scene');
        const { director } = require('cc');
        let levelEditorUI = director.getScene().getComponentInChildren("LevelEditorUI");
        if (levelEditorUI) {
            levelEditorUI.progress = 1;
        }
    },
    instantiatePrefab(component_uuid, prefabUuid) {
        // console.log('instantiatePrefab:', component_uuid, prefabUuid);
        var _a, _b, _c;
        let targetNode = (_a = cc_1.director.getScene()) === null || _a === void 0 ? void 0 : _a.getChildByUuid(component_uuid);
        if (!targetNode) {
            targetNode = (_b = cc_1.director.getScene()) === null || _b === void 0 ? void 0 : _b.getChildByName('Canvas');
        }
        if (targetNode) {
            // console.log("Canvas node found: ", targetNode.getComponent("EmitterEditor"));
            // @ts-ignore
            Editor.Message.request('scene', 'execute-component-method', {
                uuid: (_c = targetNode.getComponent("EmitterEditor")) === null || _c === void 0 ? void 0 : _c.uuid,
                name: 'instantiatePrefab',
                args: [prefabUuid]
            });
        }
    },
    // async saveToPrefab(component_uuid:string, nodeName: string, prefabUuid: string) {
    //     // console.log('saveToPrefab:', component_uuid, nodeName, prefabUuid);
    //     const scene = director.getScene();
    //     const target = scene!.getChildByPath(`Canvas/${nodeName}`);
    //     if (!target) {
    //         console.error("node not found:", nodeName);
    //         return;
    //     }
    //     cce.Prefab.createPrefabAssetFromNode(target.uuid, prefabUuid);
    // },
    async createBulletPrefab(sourceAssetUuid) {
        // @ts-ignore
        const sourceAssetInfo = await Editor.Message.request('asset-db', 'query-asset-info', sourceAssetUuid);
        if (!sourceAssetInfo)
            return;
        // 需要判断是spriteList还是单个sprite
        // TODO: 当sourceAssetUuid是一个目录的时候，下面这里的路径可能存在bug。还没有验证。
        // TODO: 这个目录后续可能调整到bundle目录
        const targetPrefabDir = 'db://assets/resources/game/prefabs/bullet/';
        if (sourceAssetInfo.importer === 'sprite-atlas') {
            cc_1.assetManager.loadAny({ uuid: sourceAssetUuid }, (err, asset) => {
                if (err) {
                    console.error(err);
                    return;
                }
                const spriteList = asset;
                for (let spriteFrame of spriteList.getSpriteFrames()) {
                    const targetPrefabName = spriteFrame.name;
                    const targetPrefabPath = targetPrefabDir + targetPrefabName + '.prefab';
                    createNewBullet(targetPrefabName, targetPrefabPath, spriteFrame, spriteList);
                }
            });
        }
        else if (sourceAssetInfo.importer === 'sprite-frame') {
            cc_1.assetManager.loadAny({ uuid: sourceAssetUuid }, (err, asset) => {
                if (err) {
                    console.error(err);
                    return;
                }
                const spriteFrame = asset;
                const targetPrefabName = spriteFrame.name;
                const targetPrefabPath = targetPrefabDir + targetPrefabName + '.prefab';
                createNewBullet(targetPrefabName, targetPrefabPath, spriteFrame, null);
            });
        }
        else {
            console.warn('Skipping unknown asset type:', sourceAssetInfo.importer);
        }
    },
    async createLevelPrefab(sourceAssetUuid) {
        // @ts-ignore
        const sourceAssetInfo = await Editor.Message.request('asset-db', 'query-asset-info', sourceAssetUuid);
        if (!sourceAssetInfo)
            return;
        // remove /x/x/*.* -> /x/x/
        let targetPrefabDir = sourceAssetInfo.path.replace('/Texture/', '/Prefab/');
        const targetPrefabPath = targetPrefabDir.substring(0, targetPrefabDir.lastIndexOf('/')) + '.prefab';
        const targetPrefabName = targetPrefabDir.substring(targetPrefabDir.lastIndexOf('/') + 1);
        if (sourceAssetInfo.importer === 'sprite-frame') {
            cc_1.assetManager.loadAny({ uuid: sourceAssetUuid }, (err, asset) => {
                if (err) {
                    console.error(err);
                    return;
                }
                // console.log(`Level Prefab will be created at path ${targetPrefabPath}`);
                const spriteFrame = asset;
                if (spriteFrame) {
                    createNewLevelObject(targetPrefabName, targetPrefabPath, spriteFrame); // 这个是创建prefab，不是prefab里面的节点，所以不需要parent
                }
                else {
                    console.warn('Asset is not a sprite-frame:', sourceAssetInfo);
                }
            });
        }
        else {
            console.warn('Skipping unknown asset type:', sourceAssetInfo.importer);
        }
    },
    async createMainPlanePrefab(sourceAssetUuid) {
        console.log(`createMainPlanePrefab uuid:[${sourceAssetUuid}]`);
        // @ts-ignore
        const sourceAssetInfo = await Editor.Message.request('asset-db', 'query-asset-info', sourceAssetUuid);
        if (!sourceAssetInfo) {
            console.error('query-asset-info failed:', sourceAssetUuid);
            return;
        }
        const planeId = cc_1.path.basename(sourceAssetInfo.path);
        const prefabPath = `db://assets/resources/game/prefabs/plane/mainplane/${planeId}.prefab`;
        const temp = new cc_1.Node("temp");
        cc_1.director.getScene().addChild(temp);
        const rootNode = new cc_1.Node(planeId);
        temp.addChild(rootNode);
        const spineNode = new cc_1.Node("spine");
        rootNode.addChild(spineNode);
        const spine = spineNode.addComponent(cc_1.sp.Skeleton);
        spine.skeletonData = await new Promise((resolve, reject) => {
            cc_1.assetManager.loadAny({ uuid: sourceAssetUuid }, (err, asset) => {
                if (err) {
                    console.error(err);
                    reject(err);
                }
                else {
                    console.log("createMainPlanePrefab load spine success");
                    resolve(asset);
                }
            });
        });
        const colliderNode = new cc_1.Node("collider");
        rootNode.addChild(colliderNode);
        const collider = colliderNode.addComponent(cc_1.BoxCollider2D);
        collider.size = new cc_1.Size(128, 128);
        try {
            await cce.Prefab.createPrefabAssetFromNode(rootNode.uuid, prefabPath);
            console.log(`main plane prefab created: ${prefabPath}`);
            temp.removeFromParent();
            Editor.Message.request('asset-db', 'refresh-asset', prefabPath);
        }
        catch (e) {
            console.error('Failed to create level prefab:', e, ', on asset: ', prefabPath);
        }
    },
    async createEnemyPlanePrefab(sourceAssetUuid) {
        console.log(`createEnemyPlanePrefab uuid:[${sourceAssetUuid}]`);
        // @ts-ignore
        const sourceAssetInfo = await Editor.Message.request('asset-db', 'query-asset-info', sourceAssetUuid);
        if (!sourceAssetInfo) {
            console.error('query-asset-info failed:', sourceAssetUuid);
            return;
        }
        const planeId = cc_1.path.basename(sourceAssetInfo.path);
        const prefabPath = `db://assets/resources/game/prefabs/plane/enemyplane/${planeId}.prefab`;
        const temp = new cc_1.Node("temp");
        cc_1.director.getScene().addChild(temp);
        const rootNode = new cc_1.Node(planeId);
        temp.addChild(rootNode);
        const spineNode = new cc_1.Node("spine");
        rootNode.addChild(spineNode);
        const spine = spineNode.addComponent(cc_1.sp.Skeleton);
        spine.skeletonData = await new Promise((resolve, reject) => {
            cc_1.assetManager.loadAny({ uuid: sourceAssetUuid }, (err, asset) => {
                if (err) {
                    console.error(err);
                    reject(err);
                }
                else {
                    resolve(asset);
                }
            });
        });
        const colliderNode = new cc_1.Node("collider");
        rootNode.addChild(colliderNode);
        const collider = colliderNode.addComponent(cc_1.BoxCollider2D);
        collider.size = spineNode.getComponent(cc_1.UITransform).contentSize;
        try {
            await cce.Prefab.createPrefabAssetFromNode(rootNode.uuid, prefabPath);
            console.log(`main plane prefab created: ${prefabPath}`);
            temp.removeFromParent();
            Editor.Message.request('asset-db', 'refresh-asset', prefabPath);
        }
        catch (e) {
            console.error('Failed to create level prefab:', e, ', on asset: ', prefabPath);
        }
    },
    // 阵型相关: addFormationPoint & saveFormationGroup
    addFormationPoint(formationGroupUuid) {
        const { director } = require('cc');
        let formationEditor = findComponentByUuid(director.getScene(), formationGroupUuid, 'FormationEditor');
        if (formationEditor) {
            // @ts-ignore
            formationEditor.addNewPoint(0, 0);
        }
        else {
            console.error('Formation group not found:', formationGroupUuid);
        }
    },
    async saveFormationGroup(formationGroupUuid) {
        const { director } = require('cc');
        let formationEditor = findComponentByUuid(director.getScene(), formationGroupUuid, 'FormationEditor');
        if (formationEditor) {
            // @ts-ignore
            const jsonData = formationEditor.formationData;
            // save this as JsonAsset
            if (jsonData === null) {
                // @ts-ignore
                let file = await Editor.Dialog.save({
                    path: Editor.Project.path + '/assets/resources/game/level/wave/formation/',
                    filters: [
                        { name: 'Json', extensions: ['json'] },
                    ],
                });
                if (file.canceled || !file.filePath) {
                    return;
                }
                // @ts-ignore
                Editor.Message.request('asset-db', 'create-asset', file.filePath, formationEditor.save()).then((res) => {
                    if (res) {
                        cc_1.assetManager.loadAny({ uuid: res.uuid }, (err, asset) => {
                            if (err) {
                                console.error(err);
                            }
                            else {
                                // @ts-ignore 
                                formationEditor.formationData = asset;
                            }
                        });
                    }
                });
            }
            else {
                // @ts-ignore
                Editor.Message.request('asset-db', 'save-asset', jsonData.uuid, formationEditor.save()).then((res) => {
                    if (res) {
                        cc_1.assetManager.loadAny({ uuid: res.uuid }, (err, asset) => {
                            if (err) {
                                console.error(err);
                            }
                            else {
                                // @ts-ignore 
                                formationEditor.formationData = asset;
                            }
                        });
                    }
                });
            }
        }
        else {
            console.error('Formation group not found:', formationGroupUuid);
        }
    },
    // 路径相关: addPathPoint & savePath & clearPath
    addPathPoint(pathEditorUuid) {
        const { director } = require('cc');
        let pathEditor = findComponentByUuid(director.getScene(), pathEditorUuid, 'PathEditor');
        if (pathEditor) {
            // @ts-ignore
            pathEditor.addNewPoint(0, 0);
        }
        else {
            console.error('Path editor not found:', pathEditorUuid);
        }
    },
    async savePath(pathEditorUuid) {
        const { director } = require('cc');
        let pathEditor = findComponentByUuid(director.getScene(), pathEditorUuid, 'PathEditor');
        if (pathEditor) {
            // @ts-ignore
            const jsonData = pathEditor.pathData;
            // save this as JsonAsset
            if (jsonData === null) {
                // @ts-ignore
                let file = await Editor.Dialog.save({
                    path: Editor.Project.path + '/assets/resources/game/level/wave/path/',
                    filters: [
                        { name: 'Json', extensions: ['json'] },
                    ],
                });
                if (file.canceled || !file.filePath) {
                    return;
                }
                // @ts-ignore
                Editor.Message.request('asset-db', 'create-asset', file.filePath, pathEditor.save()).then((res) => {
                    if (res) {
                        cc_1.assetManager.loadAny({ uuid: res.uuid }, (err, asset) => {
                            if (err) {
                                console.error(err);
                            }
                            else {
                                // @ts-ignore
                                pathEditor.pathData = asset;
                            }
                        });
                    }
                });
            }
            else {
                // @ts-ignore
                Editor.Message.request('asset-db', 'save-asset', jsonData.uuid, pathEditor.save()).then((res) => {
                    if (res) {
                        cc_1.assetManager.loadAny({ uuid: res.uuid }, (err, asset) => {
                            if (err) {
                                console.error(err);
                            }
                            else {
                                // @ts-ignore
                                pathEditor.pathData = asset;
                            }
                        });
                    }
                });
            }
        }
        else {
            console.error('Path editor not found:', pathEditorUuid);
        }
    },
    clearPath(pathEditorUuid) {
        const { director } = require('cc');
        let pathEditor = findComponentByUuid(director.getScene(), pathEditorUuid, 'PathEditor');
        if (pathEditor) {
            // @ts-ignore
            pathEditor.node.removeAllChildren();
            // @ts-ignore
            pathEditor.updateCurve();
        }
        else {
            console.error('Path editor not found:', pathEditorUuid);
        }
    },
    // 加载阵型数据到FormationEditor
    loadFormationData(assetUuid) {
        const { director } = require('cc');
        const scene = director.getScene();
        // 查找FormationEditor组件
        let formationEditor = scene === null || scene === void 0 ? void 0 : scene.getComponentInChildren('FormationEditor');
        if (formationEditor) {
            // 加载资源并设置到FormationEditor
            cc_1.assetManager.loadAny({ uuid: assetUuid }, (err, asset) => {
                if (err) {
                    console.error('Failed to load formation asset:', err);
                }
                else {
                    // @ts-ignore
                    formationEditor.formationData = asset;
                    console.log('Formation data loaded:', asset);
                }
            });
        }
        else {
            console.error('FormationEditor component not found in scene');
        }
    },
    // 加载路径数据到PathEditor
    loadPathData(assetUuid) {
        const { director } = require('cc');
        const scene = director.getScene();
        // 查找PathEditor组件
        let pathEditor = scene === null || scene === void 0 ? void 0 : scene.getComponentInChildren('PathEditor');
        if (pathEditor) {
            // 加载资源并设置到PathEditor
            cc_1.assetManager.loadAny({ uuid: assetUuid }, (err, asset) => {
                if (err) {
                    console.error('Failed to load path asset:', err);
                }
                else {
                    // @ts-ignore
                    pathEditor.pathData = asset;
                    console.log('Path data loaded:', asset);
                }
            });
        }
        else {
            console.error('PathEditor component not found in scene');
        }
    },
    // 关卡编辑器：节点创建
    createEventNode() {
        const { director } = require('cc');
        const scene = director.getScene();
        if (scene == null)
            return;
        if (scene.name != "LevelEditor") {
            console.warn("createEventNode not in LevelEditor scene, current scene:", scene.name);
        }
        const root = (0, cc_1.find)("Canvas/EventNodeParent");
        if (root == null)
            return;
        let node = new cc_1.Node("event");
        node.parent = root;
        node.addComponent('LevelEditorEventUI');
        node.setPosition(0, 0, 0);
    }
};
function findComponentByUuid(node, uuid, className) {
    if (!node)
        return null;
    // 检查当前节点是否有指定的组件，如果有且UUID匹配则返回
    const component = node.getComponent(className);
    if (component) {
        // console.log(`${node.name} has component ${className}, uuid: ${component.uuid}, target: ${uuid}`);
        if (component.uuid === uuid) {
            return component;
        }
    }
    // 无论当前节点是否有组件，都要搜索子节点
    for (let i = 0; i < node.children.length; i++) {
        let found = findComponentByUuid(node.children[i], uuid, className);
        if (found) {
            return found;
        }
    }
    return null;
}
async function createNewBullet(prefabName, prefabPath, sourceSprite, sourceSpriteList) {
    const scene = cc_1.director.getScene();
    const target = scene.getChildByName('Canvas');
    if (!target) {
        console.error("Canvas node not found");
        return;
    }
    let node = new cc_1.Node(prefabName);
    node.parent = target;
    node.setPosition(new cc_1.Vec3(0, 0, 0));
    let uiTransform = node.addComponent(cc_1.UITransform); // Ensure it has a transform component
    node.addComponent('FBoxCollider');
    let movable = node.addComponent('DefaultMove');
    node.addComponent('Bullet');
    node.addComponent('cc.BoxCollider2D');
    let sprite = node.addComponent(cc_1.Sprite);
    if (sourceSpriteList) {
        sprite.spriteFrame = sourceSprite;
        sprite.spriteAtlas = sourceSpriteList;
    }
    if (sourceSprite) {
        uiTransform.contentSize = new cc_1.Size(sourceSprite.rect.width, sourceSprite.rect.height);
    }
    const nodeUuid = node.uuid;
    try {
        await cce.Prefab.createPrefabAssetFromNode(nodeUuid, prefabPath);
        console.log(`Bullet prefab created: ${prefabPath}`);
        Editor.Message.request('asset-db', 'refresh-asset', prefabPath);
    }
    catch (e) {
        console.error('Failed to create bullet prefab:', e);
    }
}
async function createNewLevelObject(prefabName, prefabPath, sourceSprite) {
    const scene = cc_1.director.getScene();
    let node = new cc_1.Node(prefabName);
    scene.addChild(node);
    let uiTransform = node.addComponent(cc_1.UITransform); // Ensure it has a transform component
    let sprite = node.addComponent(cc_1.Sprite);
    if (sourceSprite) {
        sprite.spriteFrame = sourceSprite;
        uiTransform.contentSize = new cc_1.Size(sourceSprite.rect.width, sourceSprite.rect.height);
    }
    const nodeUuid = node.uuid;
    try {
        await cce.Prefab.createPrefabAssetFromNode(nodeUuid, prefabPath);
        console.log(`Level prefab created: ${prefabPath}`);
        Editor.Message.request('asset-db', 'refresh-asset', prefabPath);
    }
    catch (e) {
        console.error('Failed to create level prefab:', e, ', on asset: ', prefabPath);
    }
}
//# sourceMappingURL=data:application/json;base64,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
{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/dyncTerrain/RandTerrain.ts"], "names": ["_decorator", "assetManager", "CCInteger", "Component", "instantiate", "Prefab", "v2", "Vec2", "EDITOR", "Tools", "MyApp", "GameIns", "logDebug", "logError", "log<PERSON>arn", "LevelUtils", "ccclass", "property", "executeInEditMode", "menu", "TerrainElem", "displayName", "RandTerrain", "type", "onLoad", "_loadElems", "update", "terrain", "length", "isCountMatch", "node", "children", "isUUIDMatch", "i", "Math", "min", "terrainElem", "elem", "nodeUUID", "_prefab", "asset", "_uuid", "elemUUID", "uuid", "for<PERSON>ach", "child", "index", "offSet", "position", "x", "y", "play", "bPlay", "weights", "push", "weight", "selectedIndex", "getRandomIndexByWeights", "random", "active", "onDestroy", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadAny", "err", "prefab", "setPosition", "<PERSON><PERSON><PERSON><PERSON>", "battleManager", "path", "resMgr", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultBundleName", "load", "extractPathPart"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,Y,OAAAA,Y;AAAcC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,I,OAAAA,I;;AACzEC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,O,iBAAAA,O;;AACpBC,MAAAA,U,iBAAAA,U;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA,iBAArB;AAAwCC,QAAAA;AAAxC,O,GAAiDnB,U;;6BAG1CoB,W,WADZJ,OAAO,CAAC,aAAD,C,UAEHC,QAAQ,CAACf,SAAD,C,UAERe,QAAQ,CAACZ,MAAD,C,UAERY,QAAQ,CAAC;AAACI,QAAAA,WAAW,EAAE;AAAd,OAAD,C,2BANb,MACaD,WADb,CACyB;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAEG,C;;;;;;;iBAEK,I;;;;;;;iBAEP,IAAIb,IAAJ,CAAS,CAAT,EAAY,CAAZ,C;;;;6BAMbe,W,YAHZN,OAAO,CAAC,aAAD,C,UACPE,iBAAiB,E,UACjBC,IAAI,CAAC,YAAD,C,UAEAF,QAAQ,CAAC;AAACM,QAAAA,IAAI,EAAE,CAACH,WAAD;AAAP,OAAD,C,6DAJb,MAGaE,WAHb,SAGiCnB,SAHjC,CAG2C;AAAA;AAAA;;AAAA;AAAA;;AAI7BqB,QAAAA,MAAM,GAAS;AACrB,eAAKC,UAAL;AACH,SANsC,CAQvC;;;AACUC,QAAAA,MAAM,GAAS;AACrB,cAAIlB,MAAJ,EAAY;AACR,gBAAI,KAAKmB,OAAL,KAAiB,IAAjB,IAAyB,KAAKA,OAAL,CAAaC,MAAb,IAAuB,CAApD,EAAuD;AACnD;AACH;;AAED,kBAAMC,YAAY,GAAG,KAAKC,IAAL,CAAUC,QAAV,CAAmBH,MAAnB,KAA8B,KAAKD,OAAL,CAAaC,MAAhE;AACA,gBAAII,WAAW,GAAG,IAAlB;;AAEA,iBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGC,IAAI,CAACC,GAAL,CAAS,KAAKL,IAAL,CAAUC,QAAV,CAAmBH,MAA5B,EAAoC,KAAKD,OAAL,CAAaC,MAAjD,CAApB,EAA8EK,CAAC,EAA/E,EAAmF;AAAA;;AAC/E,oBAAMG,WAAW,GAAG,KAAKT,OAAL,CAAaM,CAAb,CAApB,CAD+E,CAG/E;;AACA,kBAAI,CAACG,WAAD,IAAgB,CAACA,WAAW,CAACC,IAAjC,EAAuC;AACnC;AAAA;AAAA,wCAAQ,aAAR,EAAuB,yBAAwBJ,CAAE,aAAjD;AACA,yBAFmC,CAEzB;AACb;;AAED,oBAAMH,IAAI,GAAG,KAAKA,IAAL,CAAUC,QAAV,CAAmBE,CAAnB,CAAb,CAT+E,CAU/E;;AACA,oBAAMK,QAAQ,oBAAGR,IAAI,CAACS,OAAR,8BAAG,cAAcC,KAAjB,qBAAG,cAAqBC,KAAtC,CAX+E,CAWlC;;AAC7C,oBAAMC,QAAQ,GAAGN,WAAW,CAACC,IAAZ,CAAiBM,IAAlC;;AAEA,kBAAIL,QAAQ,KAAKI,QAAjB,EAA2B;AACvBV,gBAAAA,WAAW,GAAG,KAAd;AACA;AACH;AACJ;;AAED,gBAAI,CAACH,YAAD,IAAiB,CAACG,WAAtB,EAAmC;AAC/B,mBAAKP,UAAL;AACH,aAFD,MAEO;AACH,mBAAKK,IAAL,CAAUC,QAAV,CAAmBa,OAAnB,CAA2B,CAACC,KAAD,EAAQC,KAAR,KAAkB;AACzC,qBAAKnB,OAAL,CAAamB,KAAb,EAAoBC,MAApB,GAA6BzC,EAAE,CAACuC,KAAK,CAACG,QAAN,CAAeC,CAAhB,EAAmBJ,KAAK,CAACG,QAAN,CAAeE,CAAlC,CAA/B;AACH,eAFD;AAGH;AACJ;AACJ,SA9CsC,CAgDvC;;;AACOC,QAAAA,IAAI,CAACC,KAAD,EAAuB;AAC9B,cAAI5C,MAAJ,EAAY;AACR,gBAAI4C,KAAJ,EAAW;AACP,kBAAIC,OAAiB,GAAG,EAAxB;;AACA,mBAAK,IAAIpB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKN,OAAL,CAAaC,MAAjC,EAAyCK,CAAC,EAA1C,EAA8C;AAC1CoB,gBAAAA,OAAO,CAACC,IAAR,CAAa,KAAK3B,OAAL,CAAaM,CAAb,EAAgBsB,MAA7B;AACH;;AAED,oBAAMC,aAAa,GAAG;AAAA;AAAA,kCAAMC,uBAAN,CAA8BJ,OAA9B,EAAuCnB,IAAI,CAACwB,MAAL,EAAvC,CAAtB,CANO,CAQP;;AACA,mBAAK,IAAIzB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKH,IAAL,CAAUC,QAAV,CAAmBH,MAAvC,EAA+CK,CAAC,EAAhD,EAAoD;AAChD,qBAAKH,IAAL,CAAUC,QAAV,CAAmBE,CAAnB,EAAsB0B,MAAtB,GAAgC1B,CAAC,KAAKuB,aAAtC;AACH;AACJ,aAZD,MAYO;AACH,mBAAK,IAAIvB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKH,IAAL,CAAUC,QAAV,CAAmBH,MAAvC,EAA+CK,CAAC,EAAhD,EAAoD;AAChD,qBAAKH,IAAL,CAAUC,QAAV,CAAmBE,CAAnB,EAAsB0B,MAAtB,GAA+B,IAA/B;AACH;AACJ;AACJ;AACJ;;AAESC,QAAAA,SAAS,GAAS;AACxB,eAAK9B,IAAL,CAAU+B,iBAAV;AACH;;AAEOpC,QAAAA,UAAU,GAAS;AACvB,eAAKE,OAAL,CAAaiB,OAAb,CAAsBP,IAAD,IAAU;AAC3B,gBAAI,CAACA,IAAD,IAASA,IAAI,CAACA,IAAL,IAAa,IAA1B,EAAgC;AAC5B;AACH;;AAED,iBAAKP,IAAL,CAAU+B,iBAAV;;AACA,gBAAIrD,MAAJ,EAAY;AAAA;;AACRP,cAAAA,YAAY,CAAC6D,OAAb,CAAqB;AAACnB,gBAAAA,IAAI,gBAACN,IAAI,CAACA,IAAN,qBAAC,WAAWM;AAAjB,eAArB,EAA6C,CAACoB,GAAD,EAAMC,MAAN,KAAwB;AACjE,oBAAID,GAAJ,EAAS;AACL;AAAA;AAAA,4CAAS,aAAT,EAAwB,gCAA+BA,GAAI,EAA3D;AACA;AACH;;AAED,oBAAIjC,IAAI,GAAG1B,WAAW,CAAC4D,MAAD,CAAtB;AACAlC,gBAAAA,IAAI,CAACmC,WAAL,CAAiB5B,IAAI,CAACU,MAAL,CAAYE,CAA7B,EAAgCZ,IAAI,CAACU,MAAL,CAAYG,CAA5C,EAA+C,CAA/C;AACA,qBAAKpB,IAAL,CAAWoC,QAAX,CAAoBpC,IAApB;AACH,eATD;AAUH,aAXD,MAWO;AACH,kBAAIuB,OAAiB,GAAG,EAAxB;;AACA,mBAAK,IAAIpB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKN,OAAL,CAAaC,MAAjC,EAAyCK,CAAC,EAA1C,EAA8C;AAC1CoB,gBAAAA,OAAO,CAACC,IAAR,CAAa,KAAK3B,OAAL,CAAaM,CAAb,EAAgBsB,MAA7B;AACH;;AAED,oBAAMC,aAAa,GAAG;AAAA;AAAA,kCAAMC,uBAAN,CAA8BJ,OAA9B,EAAuC;AAAA;AAAA,sCAAQc,aAAR,CAAsBT,MAAtB,EAAvC,CAAtB;AACA,oBAAMrB,IAAI,GAAG,KAAKV,OAAL,CAAa6B,aAAb,CAAb;AACA,oBAAMY,IAAI,GAAG;AAAA;AAAA,kCAAMC,MAAN,CAAaC,YAAb,CAA0B;AAAA;AAAA,kCAAMD,MAAN,CAAaE,iBAAvC,EAA0DlC,IAAI,CAACA,IAAL,CAAWM,IAArE,CAAb;AACA;AAAA;AAAA,kCAAM0B,MAAN,CAAaG,IAAb,CAAkBJ,IAAlB,EAAwB,CAACL,GAAD,EAAoBC,MAApB,KAAuC;AAC3D,oBAAID,GAAJ,EAAS;AACL;AACH;;AAED,oBAAIjC,IAAI,GAAG1B,WAAW,CAAC4D,MAAD,CAAtB;AACAlC,gBAAAA,IAAI,CAACmC,WAAL,CAAiB5B,IAAI,CAACU,MAAL,CAAYE,CAA7B,EAAgCZ,IAAI,CAACU,MAAL,CAAYG,CAA5C,EAA+C,CAA/C;AACA,qBAAKpB,IAAL,CAAWoC,QAAX,CAAoBpC,IAApB;AACA;AAAA;AAAA,0CAAS,aAAT,EAAwB,4BAA2B;AAAA;AAAA,8CAAW2C,eAAX,CAA2BL,IAA3B,EAAgC,EAAhC,CAAoC,EAAvF;AACH,eATD;AAUH;AACJ,WArCD;AAsCH;;AAlHsC,O;;;;;iBAEP,E", "sourcesContent": ["import { _decorator, assetManager, CCInteger, Component, instantiate, Prefab, v2, Vec2 } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { Tools } from 'db://assets/bundles/common/script/game/utils/Tools';\r\nimport { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\r\nimport { GameIns } from 'db://assets/bundles/common/script/game/GameIns';\r\nimport { logDebug, logError, logWarn } from 'db://assets/scripts/utils/Logger';\r\nimport { LevelUtils } from 'db://assets/bundles/common/script/game/ui/map/LevelUtils';\r\nconst { ccclass, property, executeInEditMode, menu } = _decorator;\r\n\r\n@ccclass('TerrainElem')\r\nexport class TerrainElem {\r\n    @property(CCInteger)\r\n    public weight: number = 0;\r\n    @property(Prefab)\r\n    public elem: Prefab | null = null;\r\n    @property({displayName: \"坐标偏移\"})\r\n    public offSet: Vec2 = new Vec2(0, 0);\r\n}\r\n\r\n@ccclass('RandTerrain')\r\n@executeInEditMode()\r\n@menu('地形系统/随机地形组')\r\nexport class RandTerrain extends Component {\r\n    @property({type: [TerrainElem]})\r\n    public terrain: TerrainElem[] = [];\r\n\r\n    protected onLoad(): void {\r\n        this._loadElems();\r\n    }\r\n\r\n    // 纯表现层业务，请勿将逻辑代码写到这里\r\n    protected update(): void {\r\n        if (EDITOR) {\r\n            if (this.terrain === null || this.terrain.length == 0) {\r\n                return;\r\n            }\r\n\r\n            const isCountMatch = this.node.children.length === this.terrain.length;\r\n            let isUUIDMatch = true;\r\n\r\n            for (let i = 0; i < Math.min(this.node.children.length, this.terrain.length); i++) {\r\n                const terrainElem = this.terrain[i];\r\n                \r\n                // 增加空值检查\r\n                if (!terrainElem || !terrainElem.elem) {\r\n                    logWarn('RandTerrain',` TerrainElem at index ${i} is invalid`);\r\n                    continue; // 跳过无效元素\r\n                }\r\n\r\n                const node = this.node.children[i];\r\n                // @ts-ignore\r\n                const nodeUUID = node._prefab?.asset?._uuid; // 使用可选链\r\n                const elemUUID = terrainElem.elem.uuid;\r\n\r\n                if (nodeUUID !== elemUUID) {\r\n                    isUUIDMatch = false;\r\n                    break;\r\n                }\r\n            }\r\n\r\n            if (!isCountMatch || !isUUIDMatch) {\r\n                this._loadElems();\r\n            } else {\r\n                this.node.children.forEach((child, index) => {\r\n                    this.terrain[index].offSet = v2(child.position.x, child.position.y);\r\n                });\r\n            }\r\n        }\r\n    }\r\n\r\n    // 只在编辑器调用\r\n    public play(bPlay: boolean): void {\r\n        if (EDITOR) {\r\n            if (bPlay) {\r\n                let weights: number[] = [];\r\n                for (let i = 0; i < this.terrain.length; i++) {\r\n                    weights.push(this.terrain[i].weight);\r\n                }\r\n\r\n                const selectedIndex = Tools.getRandomIndexByWeights(weights, Math.random());\r\n\r\n                // 设置所有节点的active状态\r\n                for (let i = 0; i < this.node.children.length; i++) {\r\n                    this.node.children[i].active = (i === selectedIndex);\r\n                }\r\n            } else {\r\n                for (let i = 0; i < this.node.children.length; i++) {\r\n                    this.node.children[i].active = true;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    protected onDestroy(): void {\r\n        this.node.removeAllChildren();\r\n    }\r\n\r\n    private _loadElems(): void {\r\n        this.terrain.forEach((elem) => {\r\n            if (!elem || elem.elem == null) {\r\n                return;\r\n            }\r\n\r\n            this.node.removeAllChildren();\r\n            if (EDITOR) {\r\n                assetManager.loadAny({uuid:elem.elem?.uuid}, (err, prefab:Prefab) => { \r\n                    if (err) {\r\n                        logError('RandTerrain',` load TerrainElem prefab err ${err}`);\r\n                        return;\r\n                    }\r\n\r\n                    var node = instantiate(prefab);\r\n                    node.setPosition(elem.offSet.x, elem.offSet.y, 0);\r\n                    this.node!.addChild(node);\r\n                });\r\n            } else {\r\n                let weights: number[] = [];\r\n                for (let i = 0; i < this.terrain.length; i++) {\r\n                    weights.push(this.terrain[i].weight);\r\n                }\r\n\r\n                const selectedIndex = Tools.getRandomIndexByWeights(weights, GameIns.battleManager.random());\r\n                const elem = this.terrain[selectedIndex];\r\n                const path = MyApp.resMgr.getAssetPath(MyApp.resMgr.defaultBundleName, elem.elem!.uuid);\r\n                MyApp.resMgr.load(path, (err: Error | null, prefab: Prefab) => {\r\n                    if (err) {\r\n                        return;\r\n                    }         \r\n\r\n                    var node = instantiate(prefab);\r\n                    node.setPosition(elem.offSet.x, elem.offSet.y, 0);\r\n                    this.node!.addChild(node);\r\n                    logDebug('RandTerrain',` load TerrainElem prefab ${LevelUtils.extractPathPart(path,'')}`);\r\n                });\r\n            }\r\n        })\r\n    }\r\n}\r\n\r\n"]}
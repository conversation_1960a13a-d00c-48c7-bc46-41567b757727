import { _decorator, Component, misc, Enum, Node, UITransform, Vec2, Vec3, JsonAsset } from 'cc';
import { BulletSystem } from '../bullet/BulletSystem';
import { MoveBase, eMoveEvent, eOrientationType } from './IMovable';
import Entity from '../ui/base/Entity';
import { PathData, PathPoint } from '../data/PathData';
import { DefaultMove } from './DefaultMove';

const { degreesToRadians, radiansToDegrees } = misc;
const { ccclass, property, executeInEditMode } = _decorator;

@ccclass('PathMove')
@executeInEditMode()
export class PathMove extends DefaultMove {
    public _pathAsset: JsonAsset | null = null;
    @property({ type: JsonAsset, displayName: "路径数据(预览用)" })
    public get pathAsset(): JsonAsset | null {
        return this._pathAsset;
    }
    public set pathAsset(value: JsonAsset) {
        this._pathAsset = value;
        if (value) {
            this.setPath(PathData.fromJSON(value.json));
        }
    }

    @property({ displayName: "循环移动" })
    public loop: boolean = false;

    @property({ displayName: "反向移动" })
    public reverse: boolean = false;

    @property({ type: Enum(eOrientationType), displayName:"朝向类型" })
    public get editor_orientationType(): eOrientationType {
        return this.orientationType;
    }
    public set editor_orientationType(value: eOrientationType) {
        this.setOrientation(value, this.editor_orientationParam);
    }
    @property({ displayName: "朝向参数" })
    public get editor_orientationParam(): number {
        return this.orientationParam;
    }
    public set editor_orientationParam(value: number) {
        this.setOrientation(this.orientationType, value);
    }

    // 路径相关数据
    private _pathData: PathData | null = null;
    private _subdivided: PathPoint[] = []; // 细分后的路径点（包含完整信息）
    // 路径偏移
    private _offsetX: number = 0;
    private _offsetY: number = 0;
    // 平滑过渡参数（1-25)
    private static readonly kLerpFactor = 16;

    // 移动状态
    private _currentPointIndex: number = 0; // 当前所在的细分点索引
    private _nextPointIndex: number = 0;
    private _remainDistance: number = 0;    // 距离下一个点的剩余距离

    // 停留状态
    private _stayTimer: number = 0; // 停留计时器（秒）

    // private _updateInEditor: boolean = false;
    // public onFocusInEditor() {
    //     this._updateInEditor = true;
    //     this._isMovable = true;
    // }
    // public onLostFocusInEditor() {
    //     this._updateInEditor = false;
    //     this._isMovable = false;
    //     this.resetToStart();
    // }
    // public update(dt: number) {
    //     if (this._updateInEditor) {
    //         this.tick(dt);
    //     }
    // }

    // 注意调用顺序,先调用setOffset,再调用setPath
    public setOffset(x: number, y: number): PathMove {
        this._offsetX = x;
        this._offsetY = y;
        return this;
    }
    public setPath(pathData: PathData): PathMove {
        this._pathData = pathData;
        // 使用新的细分点方法，直接获取包含完整信息的PathPoint数组
        this._subdivided = this._pathData.getSubdividedPoints();
        this.resetToStart();

        return this;
    }

    /**
     * 主要的移动更新逻辑
     */
    public tick(dt: number): void {
        if (!this._isMovable) return;
        if (!this._pathData) {
            super.tick(dt);
            return;
        }
        
        // 处理停留逻辑
        if (this._stayTimer > 0) {
            this._stayTimer -= dt;
            if (this._stayTimer <= 0) {
                this._stayTimer = 0;
                // 停留结束，继续移动到下一个点
                this.moveToNextPoint();
            }
        } else if (this._nextPointIndex !== this._currentPointIndex) {
            this.tickMovement(dt);
        }
        this.updateTilting(this.speedAngle, dt, this._position);
        
        if (Math.abs(this.speed) > 0.001 || Math.abs(this.tiltSpeed) > 0.001) {
            // 设置节点位置
            this.node.setPosition(this._position);
            this.checkVisibility();

            // 更新朝向
            this.updateOrientation(dt);
        }
    }

    private tickMovement(dt: number) {
        // 使用匀加速直线运动更新位置
        const v0 = this.speed;
        // s = v0*t + 0.5*a*t^2
        const s = v0 * dt + 0.5 * this.acceleration * dt * dt;
        this.speed += this.acceleration * dt;

        // console.log(`v0: ${v0.toFixed(2)}, a: ${this.acceleration.toFixed(2)}, s: ${s.toFixed(2)}`);

        // 计算移动向量
        const angleRad = this.speedAngle;
        const deltaX = Math.cos(angleRad) * s;
        const deltaY = Math.sin(angleRad) * s;

        // 更新位置
        this._position.x += deltaX;
        this._position.y += deltaY;

        // 检查是否到达目标点
        if (this._remainDistance > 0) {
            this._remainDistance -= s;
            if (this._remainDistance <= 0) {
                this.onReachPoint(this._nextPointIndex);
            }
        }
    }

    protected getDesiredOrientation(dt: number): number {
        if (this._pathData && this.orientationType === eOrientationType.Path) {
            // PathMove这里speedAngle是radians，需要转换为degrees进行角度lerp
            const targetAngleDegrees = radiansToDegrees(this.speedAngle);
            return PathMove.lerpAngle(this.orientation, targetAngleDegrees, PathMove.kLerpFactor, dt);
        }
        return super.getDesiredOrientation(dt);
    }

    private onReachPoint(pointIndex: number) {
        // 更新当前点索引
        this._currentPointIndex = pointIndex;
        // 检查是否需要停留
        const currentPoint = this.getPathPoint(pointIndex);
        if (currentPoint) { 
            this._basePosition.x = currentPoint.x + this._offsetX;
            this._basePosition.y = currentPoint.y + this._offsetY;
            this._position.set(this._basePosition);
            this.node.setPosition(this._position);
            // 设置速度
            this.speed = currentPoint.speed;
            // 设置朝向
            this.orientationType = currentPoint.orientationType;
            this.orientationParam = currentPoint.orientationParam;
            // 这里要考虑的问题是: 第一个点的初始朝向,希望是立刻生效,而不是lerp
            if (pointIndex === 0) {
                const nextPoint = this.getPathPoint(pointIndex + 1);
                if (nextPoint) {
                    const dirX = nextPoint.x - currentPoint.x;
                    const dirY = nextPoint.y - currentPoint.y;
                    this.speedAngle = Math.atan2(dirY, dirX);
                    if (this.orientationType === eOrientationType.Path) {
                        this.orientation = radiansToDegrees(this.speedAngle);
                    }
                }
                this.updateOrientation(0);
            }

            // console.log(`到达点 ${pointIndex}, 停留时间: ${currentPoint.stayDuration}ms`);
            if (currentPoint.stayDuration > 0) {
                this._stayTimer = currentPoint.stayDuration / 1000.0;
                return;
            }
        } else {
            this.speed = 0;
        }

        // 继续移动到下一个点
        this.moveToNextPoint();
    }

    private moveToNextPoint() {
        const nextIndex = this._currentPointIndex + 1;
        if (nextIndex >= this._subdivided.length) {
            // 到达路径终点
            if (this.loop) {
                // 循环模式，回到起点
                this.setNext(0);
                this.emit(eMoveEvent.onPathLoop);
            } else {
                // 停止移动
                this._nextPointIndex = this._currentPointIndex;
                this.emit(eMoveEvent.onPathEnd);
            }
        } else {
            // 移动到下一个点
            this.setNext(nextIndex);
        }
    }

    private setNext(pathPointIndex: number) {
        this._nextPointIndex = pathPointIndex;

        const currentPoint = this.getPathPoint(this._currentPointIndex);
        const nextPoint = this.getPathPoint(this._nextPointIndex);
        if (currentPoint && nextPoint) {
            const dirX = nextPoint.x - currentPoint.x;
            const dirY = nextPoint.y - currentPoint.y;
            this._remainDistance = Math.sqrt(dirX * dirX + dirY * dirY);

            if (this._remainDistance > 1) {
                // 计算目标移动角度
                this.speedAngle = Math.atan2(dirY, dirX);
                
                // 计算加速度：使用匀加速直线运动公式 v1^2 = v0^2 + 2*a*x
                // 解出 a = (v1^2 - v0^2) / (2*x)
                const v0 = currentPoint.speed;
                const v1 = nextPoint.speed;
                this.acceleration = (v1 * v1 - v0 * v0) / (2 * this._remainDistance);
                // console.log(`设置下一段: ${this._currentPointIndex} -> ${this._nextPointIndex}, 距离: ${this._remainDistance.toFixed(2)}, 初速度: ${v0}, 末速度: ${v1}, 加速度: ${this.acceleration.toFixed(2)}, 角度: ${radiansToDegrees(targetAngle).toFixed(2)}`);
            }
        }
    }

    private getPathPoint(pathPointIndex: number): PathPoint | null {
        if (pathPointIndex < 0 || pathPointIndex >= this._subdivided.length) {
            return null;
        }
        return this._subdivided[pathPointIndex];
    }

    private resetToStart() {
        this._currentPointIndex = 0;
        this._nextPointIndex = 0;
        this._stayTimer = 0;
        this._tiltTime = 0;
        this._remainDistance = 0;
        this.speed = 0;
        this.acceleration = 0;
        this.tiltSpeed = 0;
        this.tiltOffset = 0;

        this.onReachPoint(0);
    }

    public isStaying(): boolean {
        return this._stayTimer > 0;
    }

    public getRemainingStayTime(): number {
        return this._stayTimer;
    }

    // static lerp(a: number, b: number, decay: number, dt: number): number {
    //     return (a - b) * Math.exp(-decay * dt) + b;
    // }

    /**
     * 角度插值，正确处理角度环绕问题
     * @param from 起始角度（度）
     * @param to 目标角度（度）
     * @param decay 衰减系数
     * @param dt 时间增量
     * @returns 插值后的角度（度）
     */
    static lerpAngle(from: number, to: number, decay: number, dt: number): number {
        // 将角度标准化到[-180, 180]范围
        const normalizeAngle = (angle: number): number => {
            while (angle > 180) angle -= 360;
            while (angle < -180) angle += 360;
            return angle;
        };

        // 标准化输入角度
        from = normalizeAngle(from);
        to = normalizeAngle(to);

        // 计算角度差，选择最短路径
        let diff = to - from;
        if (diff > 180) diff -= 360;
        if (diff < -180) diff += 360;

        // 使用指数衰减插值
        const lerpedDiff = diff * (1 - Math.exp(-decay * dt));
        return normalizeAngle(from + lerpedDiff);
    }
}

System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Enum, Node, Color, Component, UITransform, JsonAsset, CCInteger, Graphics, Vec2, Vec3, ePathType, PathData, PathPoint, PathPointEditor, eOrientationType, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _dec10, _dec11, _dec12, _dec13, _class, _class2, _descriptor, _descriptor2, _crd, ccclass, executeInEditMode, property, disallowMultiple, menu, requireComponent, PathEditor;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _reportPossibleCrUseOfePathType(extras) {
    _reporterNs.report("ePathType", "db://assets/bundles/common/script/game/data/PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathData(extras) {
    _reporterNs.report("PathData", "db://assets/bundles/common/script/game/data/PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathPoint(extras) {
    _reporterNs.report("PathPoint", "db://assets/bundles/common/script/game/data/PathData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathPointEditor(extras) {
    _reporterNs.report("PathPointEditor", "./PathPointEditor", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeOrientationType(extras) {
    _reporterNs.report("eOrientationType", "db://assets/bundles/common/script/game/move/IMovable", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Enum = _cc.Enum;
      Node = _cc.Node;
      Color = _cc.Color;
      Component = _cc.Component;
      UITransform = _cc.UITransform;
      JsonAsset = _cc.JsonAsset;
      CCInteger = _cc.CCInteger;
      Graphics = _cc.Graphics;
      Vec2 = _cc.Vec2;
      Vec3 = _cc.Vec3;
    }, function (_unresolved_2) {
      ePathType = _unresolved_2.ePathType;
      PathData = _unresolved_2.PathData;
      PathPoint = _unresolved_2.PathPoint;
    }, function (_unresolved_3) {
      PathPointEditor = _unresolved_3.PathPointEditor;
    }, function (_unresolved_4) {
      eOrientationType = _unresolved_4.eOrientationType;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "3b164ESPc1DLJK8SqXjxSO0", "PathEditor", undefined);

      __checkObsolete__(['_decorator', 'Enum', 'Node', 'Color', 'Component', 'UITransform', 'JsonAsset', 'CCInteger', 'Graphics', 'Vec2', 'Vec3']);

      ({
        ccclass,
        executeInEditMode,
        property,
        disallowMultiple,
        menu,
        requireComponent
      } = _decorator);

      _export("PathEditor", PathEditor = (_dec = ccclass('PathEditor'), _dec2 = menu("怪物/编辑器/路径编辑"), _dec3 = requireComponent(Graphics), _dec4 = executeInEditMode(true), _dec5 = disallowMultiple(true), _dec6 = property({
        type: JsonAsset,
        displayName: "路径数据"
      }), _dec7 = property({
        displayName: "路径名称"
      }), _dec8 = property({
        type: Enum(_crd && ePathType === void 0 ? (_reportPossibleCrUseOfePathType({
          error: Error()
        }), ePathType) : ePathType),
        displayName: "路径类型"
      }), _dec9 = property({
        type: CCInteger,
        displayName: '起始点'
      }), _dec10 = property({
        type: CCInteger,
        displayName: '结束点(-1代表默认最后个点)'
      }), _dec11 = property({
        displayName: "是否闭合",

        visible() {
          // @ts-ignore
          return this._pathDataObj.points.length >= 3;
        }

      }), _dec12 = property({
        displayName: "曲线颜色"
      }), _dec13 = property({
        displayName: "显示细分线段"
      }), _dec(_class = _dec2(_class = _dec3(_class = _dec4(_class = _dec5(_class = (_class2 = class PathEditor extends Component {
        constructor() {
          super(...arguments);
          this._graphics = null;

          _initializerDefineProperty(this, "curveColor", _descriptor, this);

          _initializerDefineProperty(this, "showSegments", _descriptor2, this);

          // 是否使用不同颜色来绘制不同的细分线段
          this._showDirectionArrow = true;
          this._pathData = null;
          this._pathDataObj = new (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData)();
          this._cachedChildrenCount = 0;
        }

        get graphics() {
          if (!this._graphics) {
            this._graphics = this.node.getComponent(Graphics) || this.node.addComponent(Graphics);
          }

          return this._graphics;
        }

        set pathData(value) {
          this._pathData = value;
          this.reload();
        }

        get pathData() {
          return this._pathData;
        }

        get pathName() {
          return this._pathDataObj.name;
        }

        set pathName(value) {
          this._pathDataObj.name = value;
        }

        get pathType() {
          return this._pathDataObj.pathType;
        }

        set pathType(value) {
          this._pathDataObj.pathType = value;
        }

        get startIdx() {
          return this._pathDataObj.startIdx;
        }

        set startIdx(value) {
          this._pathDataObj.startIdx = value;
        }

        get endIdx() {
          return this._pathDataObj.endIdx;
        }

        set endIdx(value) {
          this._pathDataObj.endIdx = value;
        }

        get isClosed() {
          return this._pathDataObj.closed;
        }

        set isClosed(value) {
          this._pathDataObj.closed = value;
        }

        onLoad() {
          var uiTrans = this.getComponent(UITransform);
          uiTrans.setContentSize(1, 1); // 让这个点不好选中
        }

        reload() {
          if (!this._pathData) return;
          var pathData = (_crd && PathData === void 0 ? (_reportPossibleCrUseOfPathData({
            error: Error()
          }), PathData) : PathData).fromJSON(this._pathData.json);
          this._pathDataObj = pathData;
          this.node.removeAllChildren();

          if (this._pathDataObj && this._pathDataObj.points.length > 0) {
            this._pathDataObj.points.forEach(point => {
              this.addPoint(point);
            });
          }
        }

        save() {
          // 收集所有路径点数据
          var pointEditors = this.getComponentsInChildren(_crd && PathPointEditor === void 0 ? (_reportPossibleCrUseOfPathPointEditor({
            error: Error()
          }), PathPointEditor) : PathPointEditor);
          this._pathDataObj.points = pointEditors.map(editor => editor.pathPoint);
          return JSON.stringify(this._pathDataObj, null, 2);
        }

        addPoint(point) {
          var pointNode = new Node();
          pointNode.parent = this.node;
          pointNode.setPosition(point.x, point.y, 0);
          var pointEditor = pointNode.addComponent(_crd && PathPointEditor === void 0 ? (_reportPossibleCrUseOfPathPointEditor({
            error: Error()
          }), PathPointEditor) : PathPointEditor);
          pointEditor.pathPoint = point;
          return pointNode;
        }

        addNewPoint(x, y) {
          var point = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(x, y);
          var node = this.addPoint(point);
          this.updateCurve();
          return node;
        }

        flipHorizontal() {
          var points = this.getSelectedPoints(); // 计算选中点的中心点，然后反转

          var center = this.calculateCenter(points);
          points.forEach(pointEditor => {
            var pos = pointEditor.node.position.toVec2();
            var x = center.x * 2 - pos.x;
            pointEditor.node.setPosition(x, pos.y, 0);
          });
        }

        flipVertical() {
          var points = this.getSelectedPoints(); // 计算选中点的中心点，然后反转

          var center = this.calculateCenter(points);
          points.forEach(pointEditor => {
            var pos = pointEditor.node.position.toVec2();
            var y = center.y * 2 - pos.y;
            pointEditor.node.setPosition(pos.x, y, 0);
          });
        }

        fitToCircle() {
          // 将选中点拟合到一个圆上
          var points = this.getSelectedPoints();
          if (points.length < 3) return;
          var center = this.calculateCenter(points);
          var radius = this.calculateRadius(points, center); // 第一个点位置保持不变，以第一个点到圆心的方向作为起始角度

          var firstPoint = points[0];
          var firstPos = firstPoint.node.getPosition(); // 计算第一个点相对于圆心的角度作为起始角度

          var startAngle = Math.atan2(firstPos.y - center.y, firstPos.x - center.x); // 计算角度间隔（平分圆周）

          var angleStep = Math.PI * 2 / points.length;

          for (var i = 0; i < points.length; i++) {
            var angle = startAngle + angleStep * i;
            var x = center.x + Math.cos(angle) * radius;
            var y = center.y + Math.sin(angle) * radius;
            points[i].node.setPosition(x, y, 0);
          }
        }

        getSelectedPoints() {
          var selected = Editor.Selection.getSelected('node');

          if (!selected || selected.length === 0) {
            console.log("no node selected");
            return [];
          }

          var points = [];
          selected.forEach(uuid => {
            var node = this.node.getChildByUuid(uuid);

            if (node) {
              var pointEditor = node.getComponent(_crd && PathPointEditor === void 0 ? (_reportPossibleCrUseOfPathPointEditor({
                error: Error()
              }), PathPointEditor) : PathPointEditor);

              if (pointEditor) {
                points.push(pointEditor);
              }
            }
          });
          points.sort((a, b) => a.node.getSiblingIndex() - b.node.getSiblingIndex());
          return points;
        }

        calculateCenter(points) {
          var totalX = 0;
          var totalY = 0;
          var totalCount = 0;
          points.forEach(pointEditor => {
            totalCount++;
            var pos = pointEditor.node.position.toVec2();
            totalX += pos.x;
            totalY += pos.y;
          });
          return new Vec2(totalX / totalCount, totalY / totalCount);
        }

        calculateRadius(points, center) {
          var totalDistance = 0;
          points.forEach(pointEditor => {
            var pos = pointEditor.node.position.toVec2();
            totalDistance += Vec2.distance(pos, center);
          });
          return totalDistance / points.length;
        }

        updateCurve() {
          // 收集当前所有点的数据
          var pointEditors = this.getComponentsInChildren(_crd && PathPointEditor === void 0 ? (_reportPossibleCrUseOfPathPointEditor({
            error: Error()
          }), PathPointEditor) : PathPointEditor);
          this._pathDataObj.points = pointEditors.map(editor => editor.pathPoint);
        }

        update(_dt) {
          var childrenCount = this.node.children.length;

          if (childrenCount !== this._cachedChildrenCount) {
            this._cachedChildrenCount = childrenCount;
          } // 把自己的位置锁定在原点，避免出现绘制偏移


          this.node.position = Vec3.ZERO;
          this.updateCurve();
          this.drawPath();
        }

        drawPath() {
          var graphics = this.graphics;
          graphics.clear();

          if (this._pathDataObj.pathType === (_crd && ePathType === void 0 ? (_reportPossibleCrUseOfePathType({
            error: Error()
          }), ePathType) : ePathType).Custom) {
            this.drawCustomPath();
          } else {
            this.drawCirclePath();
          }
        }

        drawCirclePath() {
          var graphics = this.graphics;
          var pathData = this._pathDataObj;
          var radius = pathData.radius;
          var centerX = pathData.centerX;
          var centerY = pathData.centerY;
          var startAngle = pathData.startAngle; // draw using graphics

          graphics.strokeColor = this.curveColor;
          graphics.lineWidth = 8;
          graphics.arc(centerX, centerY, radius, startAngle, startAngle + Math.PI * 2, false);
          graphics.stroke();
        }

        drawCustomPath() {
          // console.log(`drawPath points length: `, this._pathDataObj.points.length);
          if (this._pathDataObj.points.length < 2) return;
          var graphics = this.graphics;

          var subdivided = this._pathDataObj.getSubdividedPoints(true);

          if (subdivided.length > 1) {
            if (this.showSegments) {
              PathEditor.drawSegmentedPath(graphics, subdivided, this._pathDataObj.closed, 8);
            } else {
              PathEditor.drawUniformPath(graphics, subdivided, this.curveColor, this._pathDataObj.closed, 8);
            } // 绘制路径终点的方向箭头（仅对非闭合路径）


            if (this._showDirectionArrow && !this._pathDataObj.closed) {
              var endPoint = subdivided[subdivided.length - 1];
              var prevPoint = subdivided[subdivided.length - 2];

              if (subdivided.length >= 5) {
                prevPoint = subdivided[subdivided.length - 5];
              }

              var direction = Math.atan2(endPoint.y - prevPoint.y, endPoint.x - prevPoint.x);
              PathEditor.drawPathDirectionArrow(graphics, endPoint.position, direction, this._pathDataObj.closed);
            }
          }

          if (this.showSegments) {
            console.log('subdivided points length: ', subdivided.length);
          }
        }
        /**
         * 绘制统一颜色的路径
         */


        static drawUniformPath(graphics, subdivided, color, closed, width) {
          if (width === void 0) {
            width = 5;
          }

          graphics.strokeColor = color;
          graphics.lineWidth = width;
          graphics.moveTo(subdivided[0].x, subdivided[0].y);

          for (var i = 1; i < subdivided.length; i++) {
            graphics.lineTo(subdivided[i].x, subdivided[i].y);
          } // 如果是闭合路径，连接回起点


          if (closed) {
            graphics.lineTo(subdivided[0].x, subdivided[0].y);
          }

          graphics.stroke();
        }
        /**
         * 绘制分段着色的路径 - 每个细分段用不同颜色
         */


        static drawSegmentedPath(graphics, subdivided, closed, width) {
          if (width === void 0) {
            width = 5;
          }

          graphics.lineWidth = width;
          if (subdivided.length < 2) return; // 为每个细分段绘制不同的颜色

          for (var i = 0; i < subdivided.length - 1; i++) {
            var t = i / (subdivided.length - 1); // 计算当前位置的归一化参数 [0,1]
            // 从绿色到红色的颜色插值

            var color = this.interpolateColor(Color.GREEN, Color.RED, t);
            graphics.strokeColor = color; // 绘制当前段

            graphics.moveTo(subdivided[i].x, subdivided[i].y);
            graphics.lineTo(subdivided[i + 1].x, subdivided[i + 1].y);
            graphics.stroke(); // draw arrow head

            var direction = Math.atan2(subdivided[i + 1].y - subdivided[i].y, subdivided[i + 1].x - subdivided[i].x);
            PathEditor.drawPathDirectionArrow(graphics, subdivided[i + 1].position, direction, closed);
          } // 如果是闭合路径，绘制最后一段回到起点


          if (closed && subdivided.length > 2) {
            var lastColor = this.interpolateColor(Color.GREEN, Color.RED, 1.0);
            graphics.strokeColor = lastColor;
            graphics.moveTo(subdivided[subdivided.length - 1].x, subdivided[subdivided.length - 1].y);
            graphics.lineTo(subdivided[0].x, subdivided[0].y);
            graphics.stroke();
          }
        }
        /**
         * 颜色插值函数
         * @param color1 起始颜色
         * @param color2 结束颜色
         * @param t 插值参数 [0,1]
         */


        static interpolateColor(color1, color2, t) {
          t = Math.max(0, Math.min(1, t)); // 确保t在[0,1]范围内

          var r = color1.r + (color2.r - color1.r) * t;
          var g = color1.g + (color2.g - color1.g) * t;
          var b = color1.b + (color2.b - color1.b) * t;
          var a = color1.a + (color2.a - color1.a) * t;
          return new Color(r, g, b, a);
        }
        /**
         * 绘制路径方向箭头
         */


        static drawPathDirectionArrow(graphics, position, direction, closed, length, width) {
          if (length === void 0) {
            length = 30;
          }

          if (width === void 0) {
            width = 5;
          }

          // 如果是闭合路径，不绘制箭头（因为没有明确的终点）
          if (closed) return; // 箭头参数

          var arrowHeadLength = length;
          var arrowHeadAngle = Math.PI / 6; // 30度，适中的角度
          // 设置箭头样式

          graphics.strokeColor = Color.RED;
          graphics.lineWidth = width; // 计算箭头起点（从路径终点开始）

          var arrowStartX = position.x;
          var arrowStartY = position.y; // 计算箭头两条线段的端点

          var leftX = arrowStartX - Math.cos(direction - arrowHeadAngle) * arrowHeadLength;
          var leftY = arrowStartY - Math.sin(direction - arrowHeadAngle) * arrowHeadLength;
          var rightX = arrowStartX - Math.cos(direction + arrowHeadAngle) * arrowHeadLength;
          var rightY = arrowStartY - Math.sin(direction + arrowHeadAngle) * arrowHeadLength; // 绘制箭头的两条线段（">"形状）
          // 第一条线段：从箭头尖端到左侧

          graphics.moveTo(arrowStartX, arrowStartY);
          graphics.lineTo(leftX, leftY);
          graphics.stroke(); // 第二条线段：从箭头尖端到右侧

          graphics.moveTo(arrowStartX, arrowStartY);
          graphics.lineTo(rightX, rightY);
          graphics.stroke();
        }
        /**
         * 绘制路径点（在节点本地坐标系中，用于 PathPointEditor）
         */


        static drawPathPoint(graphics, pathPoint, selected, pointSize, index, totalCount, startIdx, endIdx, siblings, clearGraphics) {
          if (pointSize === void 0) {
            pointSize = 20;
          }

          if (startIdx === void 0) {
            startIdx = 0;
          }

          if (endIdx === void 0) {
            endIdx = -1;
          }

          if (clearGraphics === void 0) {
            clearGraphics = true;
          }

          if (clearGraphics) {
            graphics.clear();
          } // 绘制点（在本地坐标系中心）


          var color = selected ? Color.YELLOW : this.getDefaultColorByIndex(index, totalCount, startIdx, endIdx);
          graphics.fillColor = color;
          graphics.strokeColor = Color.BLACK;
          graphics.lineWidth = 5;
          graphics.circle(0, 0, pointSize);
          graphics.fill();
          graphics.stroke(); // 绘制平滑程度指示器

          if (pathPoint.smoothness > 0) {
            graphics.strokeColor = Color.GREEN;
            graphics.lineWidth = 5;
            var radius = pointSize + 5 + pathPoint.smoothness * 10;
            graphics.circle(0, 0, radius);
            graphics.stroke();
          } // 绘制朝向指示器（箭头）


          if (pathPoint.speed > 0) {
            this.drawOrientationArrow(graphics, pathPoint, siblings || null, index);
          }
        }
        /**
         * 在指定位置绘制路径点（用于 WavePreview 等需要在统一 Graphics 上绘制多个点的场景）
         */


        static drawPathPointAtPosition(graphics, pathPoint, x, y, selected, pointSize, index, totalCount, startIdx, endIdx, drawSmoothness, drawOrientation) {
          if (pointSize === void 0) {
            pointSize = 20;
          }

          if (startIdx === void 0) {
            startIdx = 0;
          }

          if (endIdx === void 0) {
            endIdx = -1;
          }

          if (drawSmoothness === void 0) {
            drawSmoothness = true;
          }

          if (drawOrientation === void 0) {
            drawOrientation = true;
          }

          // 绘制点
          var color = selected ? Color.YELLOW : this.getDefaultColorByIndex(index, totalCount, startIdx, endIdx);
          graphics.fillColor = color;
          graphics.strokeColor = Color.BLACK;
          graphics.lineWidth = 5;
          graphics.circle(x, y, pointSize);
          graphics.fill();
          graphics.stroke(); // 绘制平滑程度指示器

          if (pathPoint.smoothness > 0 && drawSmoothness) {
            graphics.strokeColor = Color.GREEN;
            graphics.lineWidth = 5;
            var radius = pointSize + 5 + pathPoint.smoothness * 10;
            graphics.circle(x, y, radius);
            graphics.stroke();
          } // 绘制朝向指示器（箭头）


          if (pathPoint.speed > 0 && drawOrientation) {
            this.drawOrientationArrowAtPosition(graphics, pathPoint, null, index, x, y);
          }
        }
        /**
         * 在指定位置绘制朝向箭头
         */


        static drawOrientationArrowAtPosition(graphics, pathPoint, siblings, currentIndex, x, y) {
          var arrowLength = Math.min(pathPoint.speed / 10, 100);
          var arrowAngle = this.calculateArrowAngle(pathPoint, siblings, currentIndex); // 根据朝向类型设置不同颜色

          graphics.strokeColor = this.getArrowColorByOrientationType(pathPoint.orientationType);
          graphics.lineWidth = 3; // 计算箭头终点

          var endX = x + Math.cos(arrowAngle) * arrowLength;
          var endY = y + Math.sin(arrowAngle) * arrowLength; // 绘制箭头主线

          graphics.moveTo(x, y);
          graphics.lineTo(endX, endY); // 绘制箭头头部

          var arrowHeadLength = 8;
          var arrowHeadAngle = Math.PI / 6; // 30度

          var leftX = endX - Math.cos(arrowAngle - arrowHeadAngle) * arrowHeadLength;
          var leftY = endY - Math.sin(arrowAngle - arrowHeadAngle) * arrowHeadLength;
          var rightX = endX - Math.cos(arrowAngle + arrowHeadAngle) * arrowHeadLength;
          var rightY = endY - Math.sin(arrowAngle + arrowHeadAngle) * arrowHeadLength;
          graphics.moveTo(leftX, leftY);
          graphics.lineTo(endX, endY);
          graphics.lineTo(rightX, rightY);
          graphics.stroke();
        }
        /**
         * 根据索引获取默认颜色
         */


        static getDefaultColorByIndex(index, count, startIdx, endIdx) {
          if (startIdx === void 0) {
            startIdx = 0;
          }

          if (endIdx === void 0) {
            endIdx = -1;
          }

          if (endIdx === -1) endIdx = count - 1; // 起点

          if (index === startIdx) return Color.GREEN; // 终点
          else if (index === endIdx) return Color.RED; // 中间点
          else return Color.WHITE;
        }
        /**
         * 绘制朝向箭头
         */


        static drawOrientationArrow(graphics, pathPoint, siblings, currentIndex) {
          var arrowLength = Math.min(pathPoint.speed / 10, 100);
          var arrowAngle = this.calculateArrowAngle(pathPoint, siblings, currentIndex); // 根据朝向类型设置不同颜色

          graphics.strokeColor = this.getArrowColorByOrientationType(pathPoint.orientationType);
          graphics.lineWidth = 3; // 计算箭头终点

          var endX = Math.cos(arrowAngle) * arrowLength;
          var endY = Math.sin(arrowAngle) * arrowLength; // 绘制箭头主线

          graphics.moveTo(0, 0);
          graphics.lineTo(endX, endY); // 绘制箭头头部

          var arrowHeadLength = 8;
          var arrowHeadAngle = Math.PI / 6; // 30度

          var leftX = endX - Math.cos(arrowAngle - arrowHeadAngle) * arrowHeadLength;
          var leftY = endY - Math.sin(arrowAngle - arrowHeadAngle) * arrowHeadLength;
          var rightX = endX - Math.cos(arrowAngle + arrowHeadAngle) * arrowHeadLength;
          var rightY = endY - Math.sin(arrowAngle + arrowHeadAngle) * arrowHeadLength;
          graphics.moveTo(leftX, leftY);
          graphics.lineTo(endX, endY);
          graphics.lineTo(rightX, rightY);
          graphics.stroke();
        }
        /**
         * 根据朝向类型计算箭头角度
         */


        static calculateArrowAngle(pathPoint, siblings, currentIndex) {
          switch (pathPoint.orientationType) {
            case (_crd && eOrientationType === void 0 ? (_reportPossibleCrUseOfeOrientationType({
              error: Error()
            }), eOrientationType) : eOrientationType).Path:
              return this.calculateMovementDirection(pathPoint, siblings, currentIndex);

            case (_crd && eOrientationType === void 0 ? (_reportPossibleCrUseOfeOrientationType({
              error: Error()
            }), eOrientationType) : eOrientationType).Target:
              return this.calculatePlayerDirection(pathPoint);

            case (_crd && eOrientationType === void 0 ? (_reportPossibleCrUseOfeOrientationType({
              error: Error()
            }), eOrientationType) : eOrientationType).Fixed:
              // orientationParam作为固定角度（度）
              return pathPoint.orientationParam * Math.PI / 180;

            default:
              return 0;
            // 默认向右
          }
        }
        /**
         * 计算移动方向
         */


        static calculateMovementDirection(pathPoint, siblings, currentIndex) {
          if (!siblings || siblings.length <= 1) return 0; // 如果没有提供currentIndex，尝试找到当前点在siblings中的索引

          if (currentIndex === undefined) {
            currentIndex = -1;

            for (var i = 0; i < siblings.length; i++) {
              var pointEditor = siblings[i].getComponent(_crd && PathPointEditor === void 0 ? (_reportPossibleCrUseOfPathPointEditor({
                error: Error()
              }), PathPointEditor) : PathPointEditor);

              if (pointEditor && pointEditor.pathPoint === pathPoint) {
                currentIndex = i;
                break;
              }
            }
          }

          if (currentIndex === -1 || currentIndex === undefined) return 0; // 如果是第一个点，使用到下一个点的方向

          if (currentIndex === 0 && siblings.length > 1) {
            var nextPoint = siblings[1].position;
            var currentPoint = siblings[currentIndex].position;
            return Math.atan2(nextPoint.y - currentPoint.y, nextPoint.x - currentPoint.x);
          } // 如果是最后一个点，使用从上一个点的方向
          else if (currentIndex === siblings.length - 1 && siblings.length > 1) {
            var prevPoint = siblings[currentIndex - 1].position;
            var _currentPoint = siblings[currentIndex].position;
            return Math.atan2(_currentPoint.y - prevPoint.y, _currentPoint.x - prevPoint.x);
          } // 中间点，使用前后两点的平均方向
          else if (currentIndex > 0 && currentIndex < siblings.length - 1) {
            var _prevPoint = siblings[currentIndex - 1].position;
            var _nextPoint = siblings[currentIndex + 1].position;
            return Math.atan2(_nextPoint.y - _prevPoint.y, _nextPoint.x - _prevPoint.x);
          }

          return 0; // 默认向右
        }
        /**
         * 计算朝向玩家的方向
         */


        static calculatePlayerDirection(pathPoint) {
          // 假设玩家在屏幕底部中央 (0, -400)
          // 在实际游戏中，这应该从游戏状态获取玩家位置
          var playerX = 0;
          var playerY = -400;
          return Math.atan2(playerY - pathPoint.y, playerX - pathPoint.x);
        }
        /**
         * 根据朝向类型获取箭头颜色
         */


        static getArrowColorByOrientationType(orientationType) {
          switch (orientationType) {
            case (_crd && eOrientationType === void 0 ? (_reportPossibleCrUseOfeOrientationType({
              error: Error()
            }), eOrientationType) : eOrientationType).Path:
              return Color.BLUE;
            // 蓝色：跟随移动方向

            case (_crd && eOrientationType === void 0 ? (_reportPossibleCrUseOfeOrientationType({
              error: Error()
            }), eOrientationType) : eOrientationType).Target:
              return Color.MAGENTA;
            // 紫色：朝向玩家

            case (_crd && eOrientationType === void 0 ? (_reportPossibleCrUseOfeOrientationType({
              error: Error()
            }), eOrientationType) : eOrientationType).Fixed:
              return new Color(255, 165, 0, 255);
            // 橙色：固定朝向

            default:
              return Color.BLUE;
            // 默认蓝色
          }
        }

      }, (_applyDecoratedDescriptor(_class2.prototype, "pathData", [_dec6], Object.getOwnPropertyDescriptor(_class2.prototype, "pathData"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "pathName", [_dec7], Object.getOwnPropertyDescriptor(_class2.prototype, "pathName"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "pathType", [_dec8], Object.getOwnPropertyDescriptor(_class2.prototype, "pathType"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "startIdx", [_dec9], Object.getOwnPropertyDescriptor(_class2.prototype, "startIdx"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "endIdx", [_dec10], Object.getOwnPropertyDescriptor(_class2.prototype, "endIdx"), _class2.prototype), _applyDecoratedDescriptor(_class2.prototype, "isClosed", [_dec11], Object.getOwnPropertyDescriptor(_class2.prototype, "isClosed"), _class2.prototype), _descriptor = _applyDecoratedDescriptor(_class2.prototype, "curveColor", [_dec12], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return Color.WHITE;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "showSegments", [_dec13], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return false;
        }
      })), _class2)) || _class) || _class) || _class) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b09cd97fe2cb43a7b5c53aaa1e3a7c89d31e63fc.js.map